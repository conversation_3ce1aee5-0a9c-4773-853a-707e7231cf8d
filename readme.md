### Overview
ArchKnow is an AI-powered platform that automatically analyzes Git repositories to extract, categorize, and track architectural decisions from pull requests, commits, and design documents. It helps development teams maintain architectural knowledge and make informed decisions by providing:

Automated extraction of architectural decisions from code changes
Vector-based similarity search for relevant decisions
Design document generation wizards
Root cause analysis capabilities
VSCode extension for in-editor assistance

### Key Features
1. Automated Decision Extraction
Analyzes PR content, code changes, and comments
Uses sophisticated LLM prompts to identify architectural significance
Categorizes decisions by type (architecture, technology, data, etc.)
Tracks confidence scores and related files
2. Semantic Search
Vector-based similarity search using OpenAI embeddings
Context-aware retrieval for relevant decisions
Multi-modal search (code, text, concepts)
3. Knowledge Graph
Relationship analysis between decisions
Chronological decision tracking
Impact and dependency mapping
4. Design Document Automation
Multi-phase generation process
Integration with existing architectural knowledge
Implementation planning with AI agent protocols
5. Developer Tools
VSCode extension for in-editor assistance
Command-line tools for batch processing
GitHub webhook integration for real-time updates

### Core Technologies
Frontend: Next.js 14 with React, TypeScript, and Tailwind CSS
Backend: Next.js API routes (serverless functions)
Database: Supabase (PostgreSQL) with Row Level Security
Vector Database: Pinecone for semantic search
AI/ML: Anthropic Claude and OpenAI models
Authentication: GitHub OAuth via Supabase Auth
Deployment: Vercel with GitHub App integration

## Key Workflows

1. Repository Analysis Workflow

Repository Setup: User selects a GitHub repository (public or private)
PR Discovery: System fetches historical merged PRs
Content Analysis: processMergedPR analyzes each PR using LLMs
Decision Extraction: Identifies architectural decisions using structured prompts
Vector Storage: Stores decisions in Pinecone for semantic search
Relationship Analysis: Builds knowledge graph of decision relationships

2. Design Document Generation

Task Analysis: LLM analyzes the development task
Decision Discovery: Searches existing decisions for relevant context
Phase-based Generation:
Phase 1: Goals and decision points
Phase 2: Context analysis and alternatives
Phase 3: Full design document with implementation plan
Implementation Protocol: Generates AI agent instructions for development

3. VSCode Integration

File Context Analysis: Extension analyzes current file for relevant decisions
Semantic Search: Queries Pinecone for related architectural knowledge
In-editor Assistance: Provides contextual suggestions and documentation
Code Review: Analyzes staged changes against existing decisions

GitHub Repository → PR/Commit Fetch → LLM Analysis → Decision Extraction → Vector Embedding → Storage (PostgreSQL + Pinecone)

User Opens a file in VSCode → Vector Search (Pinecone) → Decision Filtering (Supabase) → Ranked Results → Take decisions appropriately


### API Routes (src/app/api/):

analyze-repository/route.ts - Main analysis endpoint that processes PRs and extracts decisions
decisions/route.ts - CRUD operations for architectural decisions
architecture-analysis/route.ts - Knowledge graph and relationship analysis
design-doc-wizard/ - Multi-step design document generation
extension/ - VSCode extension backend endpoints
github/ - GitHub API proxy and webhook handling

### Utility libraries and service integrations (src/lib/):
supabase-server.ts & supabase-client.ts - Database clients
github.ts & github-auth.ts - GitHub API integration
pineconeUtils.ts - Vector database operations
llmUtils.ts - LLM service abstractions
designDocProcessor.ts - Design document generation logic

### Main orchestrator (src/orchestrator.js):
The core analysis engine that:

Processes merged PRs and extracts architectural decisions
Uses LLM prompts to identify significant architectural choices
Stores decisions in both PostgreSQL and Pinecone
Handles relationship analysis between decisions
Supports design document analysis and milestone tracking

### VSCode Extension (extension/)
src/extension.ts - Main extension entry point
src/webview.ts - Webview panel management (3000+ lines)
src/aiAgentProtocol.ts - AI agent communication protocol
src/services/ - Business logic services
src/commands/ - VSCode command implementations

### Command Line Interface (bin/cli.js)
A comprehensive CLI tool for:

Batch processing repositories
Extracting architectural decisions from PR history
Local analysis without web interface
Development and debugging workflows


### Database Schema (sql/)
Core Tables:

repository_loading_jobs - Track analysis job status
repository_pr_analysis_status - PR processing status
design_doc_sessions - Design document wizard state
deployment_constitutions - Repository deployment configurations
rca_analysis_sessions - Root cause analysis data



## Dataflow

1. Multi-tenant Architecture

Pinecone Namespaces: Each repository gets isolated namespace (installationId_owner/repo)
Row Level Security: Supabase RLS ensures data isolation
GitHub App Integration: Secure access to private repositories


## Technical Considerations

1. Scaling Challenges
LLM API rate limits and costs
Pinecone vector database scaling
Vercel function timeout limits
GitHub API rate limiting

2. Security
GitHub App permissions model
Row Level Security (RLS) for multi-tenancy
API key management for external services
Webhook signature verification

3. Performance Optimizations
Batch processing for large repositories
Incremental analysis for new changes
Caching strategies for expensive operations
Lazy loading for UI components
