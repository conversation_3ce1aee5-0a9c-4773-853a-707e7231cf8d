const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

async function debugSession() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  console.log('Environment check:');
  console.log('URL exists:', !!supabaseUrl);
  console.log('Service role key exists:', !!supabaseServiceRoleKey);
  
  if (!supabaseUrl || !supabaseServiceRoleKey) {
    console.error('Missing environment variables');
    return;
  }

  // Create admin client
  const supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false
    }
  });

  const sessionId = 'cc3421b5-bc76-4951-a2a5-57d8b799f70e';
  
  console.log('\n=== Testing session query ===');
  
  try {
    const { data, error } = await supabase
      .from('design_doc_sessions')
      .select('wizard_state, title, github_issue_url, created_at, updated_at, repository_slug, installation_id, user_id')
      .eq('id', sessionId)
      .single();

    console.log('Query result:');
    console.log('Error:', error);
    console.log('Data exists:', !!data);
    
    if (data) {
      console.log('Title:', data.title);
      console.log('Repository:', data.repository_slug);
      console.log('User ID:', data.user_id);
      console.log('Wizard state keys:', data.wizard_state ? Object.keys(data.wizard_state) : 'null');
      console.log('Current step:', data.wizard_state?.currentStep);
      console.log('Task details:', data.wizard_state?.taskDetails ? Object.keys(data.wizard_state.taskDetails) : 'null');
      
      // Show full wizard state for debugging
      console.log('\nFull wizard state:');
      console.log(JSON.stringify(data.wizard_state, null, 2));
    }
  } catch (err) {
    console.error('Exception:', err);
  }
  
  // Also try without RLS bypass
  console.log('\n=== Testing with anon key (should fail) ===');
  const anonClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
  
  try {
    const { data, error } = await anonClient
      .from('design_doc_sessions')
      .select('wizard_state, title')
      .eq('id', sessionId)
      .single();

    console.log('Anon query result:');
    console.log('Error:', error);
    console.log('Data exists:', !!data);
  } catch (err) {
    console.error('Anon exception:', err);
  }
}

debugSession().catch(console.error); 