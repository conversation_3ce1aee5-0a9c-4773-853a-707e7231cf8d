import * as vscode from 'vscode';
import * as path from 'path';
import { marked } from 'marked';
import { Decision, Risk } from '../services/apiService';
import { escapeHtml, getNonce } from '../utils';
import { DesignDocMetadata, AiReviewComment } from '../types';

export function getPromptGeneratorWebviewContent(extensionUri: vscode.Uri, webview: vscode.Webview): string {
    const nonce = getNonce();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArchKnow Context Generator</title>
    <style>
        body { font-family: sans-serif; padding: 1em; background-color: var(--vscode-editor-background); color: var(--vscode-editor-foreground);}
        .container { display: flex; flex-direction: column; gap: 1em; }
        .options { border: 1px solid var(--vscode-sideBarSectionHeader-border); padding: 1em; border-radius: 5px; background-color: var(--vscode-sideBar-background); }
        
        .concepts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5em;
        }
        .concepts-title {
            margin: 0;
            font-size: 0.9em;
            font-weight: normal;
            color: var(--vscode-descriptionForeground);
        }
        .concepts-refresh {
            font-size: 0.8em;
            padding: 2px 6px;
            background-color: transparent;
            border: 1px solid var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border-radius: 3px;
            cursor: pointer;
            margin: 0;
        }
        .concepts-refresh:hover {
            background-color: var(--vscode-button-secondaryBackground);
        }
        
        .concepts-container { 
            margin-top: 0.5em; 
            max-height: 200px; 
            overflow-y: auto; 
            padding: 0.5em;
            border: 1px solid var(--vscode-input-border);
            border-radius: 3px;
            background-color: var(--vscode-input-background);
        }
        
        .concepts-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 50px;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }
        
        .concept-tag {
            display: inline-block;
            padding: 0.3em 0.7em;
            margin: 0.2em;
            border-radius: 15px;
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: 1px solid var(--vscode-button-secondaryBackground);
            cursor: pointer;
            transition: background-color 0.2s ease, border-color 0.2s ease;
            font-size: 0.9em;
            user-select: none;
        }
        .concept-tag:hover {
             background-color: var(--vscode-button-secondaryHoverBackground);
        }
        .concept-tag.selected {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: 1px solid var(--vscode-button-border, var(--vscode-focusBorder));
            font-weight: bold;
        }
        .concept-tag .count {
            font-size: 0.8em;
            margin-left: 5px;
            opacity: 0.7;
        }
         .concepts-container[disabled] {
             border-color: transparent !important;
         }
         .concepts-container[disabled] .concept-tag {
             opacity: 0.5;
             pointer-events: none;
             cursor: default;
             background-color: var(--vscode-disabledForeground);
             color: var(--vscode-editor-background);
             border-color: var(--vscode-disabledForeground);
         }
         .concepts-container:focus-within {
              border: 1px solid var(--vscode-focusBorder);
         }

         .file-selection { display: flex; align-items: center; gap: 0.5em; margin-top: 0.5em; }
         #selected-file-path {
             flex-grow: 1;
             padding: 0.3em 0.5em;
             background-color: var(--vscode-input-background);
             color: var(--vscode-input-foreground);
             border: 1px solid var(--vscode-input-border);
             border-radius: 3px;
             font-size: 0.9em;
             overflow: hidden;
             text-overflow: ellipsis;
             white-space: nowrap;
             min-height: 1.5em;
             line-height: 1.5em;
         }
         #select-file-button { margin-top: 0; padding: 0.3em 0.8em; }
 
         textarea { width: 95%; min-height: 200px; margin-top: 1em; font-family: monospace; background-color: var(--vscode-input-background); color: var(--vscode-input-foreground); border: 1px solid var(--vscode-input-border); border-radius: 3px; padding: 0.5em; }
         textarea#user-prompt-input { min-height: 50px; width: 90%; margin-left: 1em; }
         #prompt-output-container {
             background-color: var(--vscode-sideBar-background);
             border: 1px solid var(--vscode-textSeparator-foreground);
             border-radius: 5px;
             padding: 15px;
             margin-top: 1em;
             overflow: auto;
             min-height: 200px;
             max-height: 600px;
         }
         .markdown-body {
             line-height: 1.6;
         }
         .markdown-body h1, .markdown-body h2, .markdown-body h3, .markdown-body h4 {
             color: var(--vscode-textLink-foreground);
             margin-top: 1.5em;
             margin-bottom: 0.5em;
             padding-bottom: 5px;
         }
         .markdown-body h1 { font-size: 1.8em; }
         .markdown-body h2 { font-size: 1.5em; }
         .markdown-body h3 { font-size: 1.3em; }
         .markdown-body h4 { font-size: 1.1em; }
         .markdown-body p, .markdown-body ul, .markdown-body ol {
             margin-bottom: 1em;
         }
         .markdown-body ul, .markdown-body ol {
             padding-left: 2em;
         }
         .markdown-body li {
             margin-bottom: 0.5em;
         }
         .markdown-body blockquote {
             border-left: 3px solid var(--vscode-textLink-foreground);
             padding-left: 1em;
             margin-left: 0;
             color: var(--vscode-descriptionForeground);
         }
         .markdown-body code {
             background-color: var(--vscode-textCodeBlock-background);
             padding: 0.2em 0.4em;
             border-radius: 3px;
             font-family: var(--vscode-editor-font-family);
             font-size: 0.9em;
         }
         .markdown-body pre code {
             display: block;
             padding: 1em;
             overflow-x: auto;
             line-height: 1.45;
         }
         .markdown-body table {
             border-collapse: collapse;
             width: 100%;
             margin-bottom: 1em;
         }
         .markdown-body th, .markdown-body td {
             border: 1px solid var(--vscode-textSeparator-foreground);
             padding: 0.5em;
             text-align: left;
         }
         .markdown-body th {
             background-color: var(--vscode-textBlockQuote-background);
         }
         .markdown-body a {
             color: var(--vscode-textLink-foreground);
             text-decoration: none;
         }
         .markdown-body a:hover {
             text-decoration: underline;
         }
         button { padding: 0.5em 1em; cursor: pointer; border: 1px solid var(--vscode-button-border, var(--vscode-contrastBorder)); background-color: var(--vscode-button-background); color: var(--vscode-button-foreground); border-radius: 3px; margin-top: 0.5em; }
         button:hover:not(:disabled) { background-color: var(--vscode-button-hoverBackground); }
         #error-message { color: var(--vscode-errorForeground); margin-top: 1em; min-height: 1.2em; }
         .options label { margin-left: 0.3em; display: inline-block; margin-bottom: 0.2em; }
         .options > div { margin-bottom: 0.8em; }

          button:disabled {
             opacity: 0.5;
             cursor: default;
          }
          textarea[disabled],
          #file-selection-ui[disabled] #select-file-button,
          #file-selection-ui[disabled] #selected-file-path {
              opacity: 0.5;
              pointer-events: none;
              background-color: var(--vscode-disabledForeground);
              border-color: var(--vscode-disabledForeground);
              color: var(--vscode-editor-background);
          }
          #file-selection-ui[disabled] #selected-file-path {
               color: var(--vscode-editorHint-foreground);
          }
          input[type="radio"]:disabled + label {
               opacity: 0.5;
               cursor: default;
          }
    </style>
</head>
<body>
    <div class="container">
        <h1>ArchKnow Context Generator</h1>

        <div class="options">
            <h2>Select Context Source:</h2>
            <div>
                <input type="radio" id="context-file" name="context-source" value="file" checked>
                <label for="context-file">Use Specific File:</label>
                <div class="file-selection" id="file-selection-ui">
                    <span id="selected-file-path" title="No file selected">No file selected</span>
                    <button id="select-file-button">Select File...</button>
                </div>
            </div>
            <div>
                <input type="radio" id="context-concepts" name="context-source" value="concepts">
                <label for="context-concepts">Use Domain Concepts:</label>
                <div class="concepts-header">
                    <span class="concepts-title">Select one or more concepts</span>
                    <button class="concepts-refresh" id="refresh-concepts-button" title="Refresh domain concepts from server">Refresh</button>
                </div>
                <div id="concepts-container" class="concepts-container" tabindex="0" aria-labelledby="context-concepts">
                    <div class="concepts-loading">Loading domain concepts...</div>
                </div>
            </div>
            <div>
                <input type="radio" id="context-prompt" name="context-source" value="prompt">
                <label for="context-prompt">Use User Prompt:</label>
                <textarea id="user-prompt-input" placeholder="Enter your question or task description..."></textarea>
            </div>
        </div>

        <button id="fetch-button">Fetch ArchKnow Context</button>

        <div id="error-message"></div>

        <h2>Architectural Context:</h2>
        <div id="prompt-output-container">
            <div id="prompt-output" class="markdown-body"></div>
        </div>
        <button id="copy-button">Copy Prompt to Clipboard</button>
        <div style="margin-top: 0.5em; font-size: 0.9em; color: var(--vscode-descriptionForeground);">
            Note: For each decision we surface, cursor:// links are included so details can be viewed or shared with another developer.
        </div>
    </div>
    
    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();
        const state = {
            selectedConcepts: []
        };
        
        const fetchButton = document.getElementById('fetch-button');
        const copyButton = document.getElementById('copy-button');
        const promptOutputContainer = document.getElementById('prompt-output-container');
        const promptOutput = document.getElementById('prompt-output');
        const conceptsContainerDiv = document.getElementById('concepts-container');
        const userPromptInput = document.getElementById('user-prompt-input');
        const contextFileRadio = document.getElementById('context-file');
        const contextConceptsRadio = document.getElementById('context-concepts');
        const contextPromptRadio = document.getElementById('context-prompt');
        const errorMessageDiv = document.getElementById('error-message');
        const fileSelectionUI = document.getElementById('file-selection-ui');
        const selectFileButton = document.getElementById('select-file-button');
        const selectedFilePathSpan = document.getElementById('selected-file-path');
        const refreshConceptsButton = document.getElementById('refresh-concepts-button');

        let currentSelectedFilePath = '';
        let rawMarkdownContent = '';
        
        function updateSelectedConcepts() {
            const selectedConcepts = Array.from(conceptsContainerDiv.querySelectorAll('.concept-tag.selected'))
                .map(tag => tag.dataset.concept);
            
            state.selectedConcepts = selectedConcepts;
            
            vscode.postMessage({ 
                command: 'conceptsSelected',
                concepts: selectedConcepts
            });
        }

        function markdownToHtml(markdown) {
            if (!markdown) return '';
            
            markdown = markdown.replace(/\`\`\`([\\w]*)(\\n[\\s\\S]*?)\`\`\`/g, function(match, language, code) {
                return '<pre><code class="language-' + language + '">' + 
                    escapeHtml(code.trim()) + 
                    '</code></pre>';
            });
            
            markdown = markdown.replace(/\`([^\`]+)\`/g, '<code>$1</code>');
            markdown = markdown.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
            markdown = markdown.replace(/^### (.*$)/gm, '<h3>$1</h3>');
            markdown = markdown.replace(/^## (.*$)/gm, '<h2>$1</h2>');
            markdown = markdown.replace(/^# (.*$)/gm, '<h1>$1</h1>');
            markdown = markdown.replace(/\\*\\*([^\\*]+)\\*\\*/g, '<strong>$1</strong>');
            markdown = markdown.replace(/\\*([^\\*]+)\\*/g, '<em>$1</em>');
            markdown = markdown.replace(/^\\* (.*)$/gm, '<ul><li>$1</li></ul>');
            markdown = markdown.replace(/^\\d+\\. (.*)$/gm, '<ol><li>$1</li></ol>');
            markdown = markdown.replace(/<\\/ul>\\s*<ul>/g, '');
            markdown = markdown.replace(/<\\/ol>\\s*<ol>/g, '');
            markdown = markdown.replace(/^> (.*)$/gm, '<blockquote>$1</blockquote>');
            markdown = markdown.replace(/<\\/blockquote>\\s*<blockquote>/g, '<br>');
            markdown = markdown.replace(/\\[([^\\]]+)\\]\\(([^\\)]+)\\)/g, '<a href="$2">$1</a>');
            markdown = markdown.replace(/^(?!<[h|u|o|b|p])(.+)$/gm, '<p>$1</p>');
            markdown = markdown.replace(/<p>\\s*<\\/p>/g, '');
            
            return markdown;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function clearError() {
            errorMessageDiv.textContent = '';
        }

        function handleContextSourceChange() {
            const useFile = contextFileRadio.checked;
            const useConcepts = contextConceptsRadio.checked;
            const usePrompt = contextPromptRadio.checked;

            if (useFile) {
                fileSelectionUI.removeAttribute('disabled');
                selectFileButton.removeAttribute('disabled');
                selectedFilePathSpan.style.backgroundColor = 'var(--vscode-input-background)';
                selectedFilePathSpan.style.borderColor = 'var(--vscode-input-border)';
                selectedFilePathSpan.style.color = currentSelectedFilePath ? 'var(--vscode-input-foreground)' : 'var(--vscode-editorHint-foreground)';
            } else {
                fileSelectionUI.setAttribute('disabled', 'true');
                selectFileButton.setAttribute('disabled', 'true');
                selectedFilePathSpan.style.backgroundColor = 'var(--vscode-disabledForeground)';
                selectedFilePathSpan.style.borderColor = 'var(--vscode-disabledForeground)';
                selectedFilePathSpan.style.color = 'var(--vscode-editorHint-foreground)';
            }

            if (useConcepts) {
                conceptsContainerDiv.removeAttribute('disabled');
                conceptsContainerDiv.style.borderColor = 'transparent';
                refreshConceptsButton.removeAttribute('disabled');
            } else {
                conceptsContainerDiv.setAttribute('disabled', 'true');
                refreshConceptsButton.setAttribute('disabled', 'true');
            }

            if (usePrompt) {
                userPromptInput.removeAttribute('disabled');
                userPromptInput.style.backgroundColor = 'var(--vscode-input-background)';
                userPromptInput.style.borderColor = 'var(--vscode-input-border)';
                userPromptInput.style.color = 'var(--vscode-input-foreground)';
            } else {
                userPromptInput.setAttribute('disabled', 'true');
                userPromptInput.style.backgroundColor = 'var(--vscode-disabledForeground)';
                userPromptInput.style.borderColor = 'var(--vscode-disabledForeground)';
                userPromptInput.style.color = 'var(--vscode-editorHint-foreground)';
            }

            clearError();
            promptOutput.innerHTML = '';
            promptOutputContainer.style.display = 'none';
        }
        
        // Event listeners and remaining JavaScript code...
        refreshConceptsButton.addEventListener('click', () => {
            if (refreshConceptsButton.hasAttribute('disabled')) return;
            conceptsContainerDiv.innerHTML = '<div class="concepts-loading">Refreshing domain concepts...</div>';
            vscode.postMessage({ command: 'refreshDomainConcepts' });
        });

        contextFileRadio.addEventListener('change', handleContextSourceChange);
        contextConceptsRadio.addEventListener('change', handleContextSourceChange);
        contextPromptRadio.addEventListener('change', handleContextSourceChange);

        selectFileButton.addEventListener('click', () => {
            if (selectFileButton.hasAttribute('disabled')) return;
            clearError();
            vscode.postMessage({ command: 'requestFileSelection' });
        });

        // Add remaining event listeners and message handlers...
        
        fetchButton.addEventListener('click', () => {
            if (fetchButton.hasAttribute('disabled')) return;
            clearError();
            promptOutput.innerHTML = '<p>Fetching...</p>';
            promptOutputContainer.style.display = 'block';
            fetchButton.disabled = true;

            let requestData = null;

            if (contextFileRadio.checked) {
                if (!currentSelectedFilePath) {
                    errorMessageDiv.textContent = 'Please select a file first.';
                    promptOutput.innerHTML = '';
                    promptOutputContainer.style.display = 'none';
                    fetchButton.disabled = false;
                    return;
                }
                requestData = { command: 'fetchContext', type: 'file', value: currentSelectedFilePath };
            } else if (contextConceptsRadio.checked) {
                const selectedConcepts = [];
                const selectedTags = conceptsContainerDiv.querySelectorAll('.concept-tag.selected');
                selectedTags.forEach(tag => {
                    selectedConcepts.push(tag.dataset.concept);
                });

                if (selectedConcepts.length === 0) {
                    errorMessageDiv.textContent = 'Please select at least one domain concept.';
                    promptOutput.innerHTML = '';
                    promptOutputContainer.style.display = 'none';
                    fetchButton.disabled = false;
                    return;
                }
                requestData = { command: 'fetchContext', type: 'concepts', value: selectedConcepts };
            } else if (contextPromptRadio.checked) {
                const userPromptText = userPromptInput.value.trim();
                if (userPromptText === '') {
                    errorMessageDiv.textContent = 'Please enter a user prompt.';
                    promptOutput.innerHTML = '';
                    promptOutputContainer.style.display = 'none';
                    fetchButton.disabled = false;
                    return;
                }
                requestData = { command: 'fetchContext', type: 'prompt', value: userPromptText };
            }

            if(requestData) {
                vscode.postMessage(requestData);
            } else {
                errorMessageDiv.textContent = 'Please select a valid context source.';
                promptOutput.innerHTML = '';
                promptOutputContainer.style.display = 'none';
                fetchButton.disabled = false;
            }
        });

        copyButton.addEventListener('click', () => {
            if (copyButton.hasAttribute('disabled')) return;
            clearError();
            
            if (rawMarkdownContent && rawMarkdownContent !== 'Fetching...') {
                navigator.clipboard.writeText(rawMarkdownContent).then(() => {
                    vscode.postMessage({ command: 'showInfo', text: 'Prompt copied to clipboard!' });
                    copyButton.textContent = 'Copied!';
                    copyButton.disabled = true;
                    setTimeout(() => {
                        copyButton.textContent = 'Copy Prompt to Clipboard';
                        copyButton.disabled = false;
                    }, 1500);
                }).catch(err => {
                    errorMessageDiv.textContent = 'Failed to copy: ' + err;
                    console.error('Copy failed:', err);
                });
            } else if (promptOutput.innerHTML.includes('Fetching...')) {
                errorMessageDiv.textContent = 'Please wait for context to load.';
            }
            else {
                errorMessageDiv.textContent = 'Nothing to copy.';
            }
        });

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'fileSelected':
                    currentSelectedFilePath = message.filePath || '';
                    selectedFilePathSpan.textContent = message.filePath || 'No file selected';
                    selectedFilePathSpan.title = message.filePath || '';
                    selectedFilePathSpan.style.color = currentSelectedFilePath ? 'var(--vscode-input-foreground)' : 'var(--vscode-editorHint-foreground)';
                    if (!contextFileRadio.checked) {
                        contextFileRadio.checked = true;
                        handleContextSourceChange();
                    }
                    promptOutput.innerHTML = '';
                    promptOutputContainer.style.display = 'none';
                    break;
                case 'loadConcepts':
                    conceptsContainerDiv.innerHTML = '';
                    if (message.concepts && message.concepts.length > 0) {
                        message.concepts.forEach(item => {
                            const tag = document.createElement('span');
                            tag.classList.add('concept-tag');
                            tag.dataset.concept = item.concept;
                            tag.setAttribute('role', 'checkbox');
                            tag.setAttribute('aria-checked', 'false');
                            tag.tabIndex = -1;
                            tag.textContent = item.concept;

                            const countSpan = document.createElement('span');
                            countSpan.classList.add('count');
                            countSpan.textContent = \`(\${item.count})\`;
                            tag.appendChild(countSpan);

                            tag.addEventListener('click', () => {
                                if (!conceptsContainerDiv.hasAttribute('disabled')) {
                                    const isSelected = tag.classList.toggle('selected');
                                    tag.setAttribute('aria-checked', isSelected.toString());
                                    clearError();
                                    promptOutput.innerHTML = '';
                                    promptOutputContainer.style.display = 'none';
                                    updateSelectedConcepts();
                                }
                            });
                            
                            if (message.selectedConcepts && 
                                Array.isArray(message.selectedConcepts) && 
                                message.selectedConcepts.includes(item.concept)) {
                                tag.classList.add('selected');
                                tag.setAttribute('aria-checked', 'true');
                            }
                            
                            conceptsContainerDiv.appendChild(tag);
                        });
                        
                        if (message.selectedConcepts && Array.isArray(message.selectedConcepts)) {
                            state.selectedConcepts = message.selectedConcepts;
                        }
                    } else {
                        conceptsContainerDiv.innerHTML = '<p>No domain concepts found in .archknow/domain-concepts.csv. Run the sync command if needed.</p>';
                    }
                    handleContextSourceChange();
                    
                    if (state.selectedConcepts && state.selectedConcepts.length > 0 && !contextConceptsRadio.checked) {
                        contextConceptsRadio.checked = true;
                        handleContextSourceChange();
                    }
                    break;
                case 'displayPrompt':
                    rawMarkdownContent = message.content;
                    promptOutput.innerHTML = markdownToHtml(message.content);
                    promptOutputContainer.style.display = 'block';
                    fetchButton.disabled = false;
                    break;
                case 'showError':
                    errorMessageDiv.textContent = message.text;
                    promptOutput.innerHTML = '';
                    promptOutputContainer.style.display = 'none';
                    fetchButton.disabled = false;
                    break;
            }
        });

        // Add keyboard navigation for concept tags
        conceptsContainerDiv.addEventListener('keydown', (event) => {
            if (conceptsContainerDiv.hasAttribute('disabled')) return;

            const currentTag = event.target.closest('.concept-tag');
            const allTags = Array.from(conceptsContainerDiv.querySelectorAll('.concept-tag:not([disabled])'));
            let currentIdx = currentTag ? allTags.indexOf(currentTag) : -1;

            switch (event.key) {
                case 'ArrowRight':
                case 'ArrowDown':
                    event.preventDefault();
                    currentIdx = (currentIdx + 1) % allTags.length;
                    allTags[currentIdx]?.focus();
                    break;
                case 'ArrowLeft':
                case 'ArrowUp':
                    event.preventDefault();
                    currentIdx = (currentIdx - 1 + allTags.length) % allTags.length;
                    allTags[currentIdx]?.focus();
                    break;
                case ' ':
                case 'Enter':
                    if (currentTag) {
                        event.preventDefault();
                        const isSelected = currentTag.classList.toggle('selected');
                        currentTag.setAttribute('aria-checked', isSelected.toString());
                        clearError();
                        promptOutput.innerHTML = '';
                        promptOutputContainer.style.display = 'none';
                        updateSelectedConcepts();
                    }
                    break;
            }
        });
        
        handleContextSourceChange();
        promptOutputContainer.style.display = 'none';
    </script>
</body>
</html>`;
}

export function getDecisionsWebviewContent(decisions: Decision[], apiUrl: string): string {
    const styles = `
        body { padding: 20px; font-family: sans-serif; background-color: var(--vscode-editor-background); color: var(--vscode-editor-foreground); }
        h1 { border-bottom: 1px solid var(--vscode-separator-foreground); padding-bottom: 10px; margin-bottom: 20px; }
        .decision { border: 1px solid var(--vscode-sideBarSectionHeader-border); background-color: var(--vscode-sideBar-background); padding: 15px; margin-bottom: 15px; border-radius: 5px; }
        .decision h3 { margin-top: 0; border-bottom: 1px solid var(--vscode-separator-foreground); padding-bottom: 5px; margin-bottom: 10px;}
        .decision h4 { margin-top: 15px; margin-bottom: 5px; font-size: 1.1em; color: var(--vscode-textLink-foreground); }
        .decision p { margin-top: 5px; margin-bottom: 10px; line-height: 1.5; }
        .decision-link { display: inline-block; margin-top: 10px; font-size: 0.9em; text-decoration: none; color: var(--vscode-textLink-foreground); background-color: var(--vscode-button-secondaryBackground); padding: 5px 10px; border-radius: 3px; }
        .decision-link:hover { background-color: var(--vscode-button-secondaryHoverBackground); }
        .no-decisions { color: var(--vscode-editorHint-foreground); }
        .risks-list { list-style: none; padding-left: 0; }
        .risk-item { border-left: 3px solid; padding: 8px 12px; margin-bottom: 8px; background-color: var(--vscode-editorWidget-background); border-radius: 3px; }
        .risk-item strong { display: block; margin-bottom: 3px; }
        .risk-high { border-color: var(--vscode-errorForeground); }
        .risk-medium { border-color: var(--vscode-editorWarning-foreground); }
        code { background-color: var(--vscode-textCodeBlock-background); padding: 0.2em 0.4em; border-radius: 3px; font-family: var(--vscode-editor-font-family); }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        .related-files-list { list-style: none; padding: 0; margin-top: 5px; }
        .related-files-list li { margin-bottom: 3px; font-size: 0.95em; }
        .file-link { cursor: pointer; color: var(--vscode-textLink-foreground); text-decoration: none; }
        .file-link:hover { text-decoration: underline; }
        .referenced-decisions-list { list-style: none; padding: 0; margin-top: 5px; }
        .referenced-decisions-list li { margin-bottom: 3px; font-size: 0.95em; }
        .decision-meta { font-size: 0.85em; color: var(--vscode-descriptionForeground); margin-bottom: 10px; }
        .concept-summary { margin-bottom: 20px; padding: 10px; background-color: var(--vscode-sideBar-background); border: 1px solid var(--vscode-sideBarSectionHeader-border); border-radius: 5px; }
        .concept-summary h4 { margin-top: 0; margin-bottom: 8px; font-size: 1em; }
        .concept-summary span { display: inline-block; background-color: var(--vscode-badge-background); color: var(--vscode-badge-foreground); padding: 3px 8px; border-radius: 10px; margin-right: 5px; margin-bottom: 5px; font-size: 0.9em; }
        .concept-summary span .count { font-weight: bold; margin-left: 3px; }
        .dev-prompt { margin-top: 15px; padding: 10px; background-color: var(--vscode-textBlockQuote-background); border-left: 3px solid var(--vscode-textLink-foreground); border-radius: 3px; }
        .dev-prompt h4 { margin: 0 0 5px 0; font-size: 0.9em; color: var(--vscode-descriptionForeground); }
        .dev-prompt p { margin: 0; font-style: italic; font-size: 0.95em; }
        .dev-prompt-summary { margin-bottom: 20px; padding: 15px; background-color: var(--vscode-editorWidget-background); border: 1px solid var(--vscode-textLink-activeForeground); border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .dev-prompt-summary h3 { margin-top: 0; margin-bottom: 10px; font-size: 1.2em; color: var(--vscode-textLink-activeForeground); }
        .dev-prompt-summary ul { list-style: disc; padding-left: 20px; margin: 0; }
        .dev-prompt-summary li { margin-bottom: 8px; font-size: 1em; line-height: 1.4; }
        .risks-summary { margin-bottom: 20px; padding: 15px; background-color: var(--vscode-editorWidget-background); border-left: 5px solid var(--vscode-errorForeground); border-radius: 3px; }
        .risks-summary h3 { margin-top: 0; margin-bottom: 10px; color: var(--vscode-errorForeground); }
        .risks-summary .risks-list { margin-top: 0; }
        .patterns-summary { margin-bottom: 20px; padding: 15px; background-color: var(--vscode-editorWidget-background); border: 1px solid var(--vscode-infoForeground); border-radius: 5px; }
        .patterns-summary h3 { margin-top: 0; margin-bottom: 10px; color: var(--vscode-infoForeground); }
        .patterns-summary ul { list-style: disc; padding-left: 20px; margin: 0; }
        .patterns-summary li { margin-bottom: 8px; font-size: 1em; line-height: 1.4; }
        #copy-md-button { 
            position: fixed; 
            top: 10px; 
            right: 15px; 
            padding: 5px 12px; 
            cursor: pointer; 
            border: 1px solid var(--vscode-button-border, var(--vscode-contrastBorder)); 
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border-radius: 3px;
            font-size: 0.9em;
        }
        #copy-md-button:hover { background-color: var(--vscode-button-hoverBackground); }
    `;

    // Generate aggregated content
    const conceptCounts = new Map<string, number>();
    const uniqueDevPrompts = new Set<string>();
    const uniquePatternReasons = new Set<string>(); // For follows_standard_practice_reason
    const highRisksAcrossDecisions: Array<{description: string; migration?: string; decisionTitle: string}> = [];
    const mediumRisksAcrossDecisions: Array<{description: string; migration?: string; decisionTitle: string}> = [];

    if (decisions && decisions.length > 0) {
        decisions.forEach(decision => {
            if (!decision || !decision.metadata) {
                return;
            }

            // Aggregate concepts
            if (Array.isArray(decision.metadata.domain_concepts)) {
                decision.metadata.domain_concepts.forEach(concept => {
                    if (typeof concept === 'string' && concept.trim() !== '') {
                        conceptCounts.set(concept, (conceptCounts.get(concept) || 0) + 1);
                    }
                });
            }

            // Aggregate dev prompts
            if (decision.metadata.dev_prompt && typeof decision.metadata.dev_prompt === 'string') {
                const trimmedPrompt = decision.metadata.dev_prompt.trim();
                if (trimmedPrompt !== '') {
                    uniqueDevPrompts.add(trimmedPrompt);
                }
            }

            // Aggregate risks
            try {
                if (decision.metadata.risks_extracted && typeof decision.metadata.risks_extracted === 'string') {
                    const risks = JSON.parse(decision.metadata.risks_extracted);
                    if (Array.isArray(risks)) {
                        const decisionTitle = decision.metadata.title || 'Untitled Decision';
                        
                        risks.filter(r => r && r.severity && r.severity.toLowerCase() === 'high')
                            .forEach(risk => {
                                highRisksAcrossDecisions.push({
                                    description: risk.description,
                                    migration: risk.migration,
                                    decisionTitle
                                });
                            });
                            
                        risks.filter(r => r && r.severity && r.severity.toLowerCase() === 'medium')
                            .forEach(risk => {
                                mediumRisksAcrossDecisions.push({
                                    description: risk.description,
                                    migration: risk.migration,
                                    decisionTitle
                                });
                            });
                    }
                }
            } catch (e) {
                console.error("ArchKnow: Error parsing risks for aggregation", e);
            }

            // Aggregate best practice reasons (patterns)
            if (decision.metadata.follows_standard_practice_reason && 
                typeof decision.metadata.follows_standard_practice_reason === 'string' && 
                decision.metadata.follows_standard_practice_reason.trim() !== '') {
                uniquePatternReasons.add(decision.metadata.follows_standard_practice_reason.trim());
            }
        });
    }

    const sortedConcepts = Array.from(conceptCounts.entries())
        .map(([concept, count]) => ({ concept, count }))
        .sort((a, b) => b.count - a.count);

    let conceptsSummaryHtml = '';
    if (sortedConcepts.length > 0) {
        conceptsSummaryHtml = `<div class="concept-summary"><h4>Top Domain Concepts:</h4>`;
        // Modify how concepts are displayed: path format, no count
        sortedConcepts.forEach(item => {
            const conceptPath = escapeHtml(item.concept).split(' > ').join(' &rarr; ');
            conceptsSummaryHtml += `<span>${conceptPath}</span>`;
        });
        conceptsSummaryHtml += `</div>`;
    }

    let devPromptsSummaryHtml = '';
    if (uniqueDevPrompts.size > 0) {
        devPromptsSummaryHtml = `<div class="dev-prompt-summary"><h3>Developer Guidance</h3><ul>`;
        uniqueDevPrompts.forEach(prompt => {
            devPromptsSummaryHtml += `<li>${escapeHtml(prompt)}</li>`;
        });
        devPromptsSummaryHtml += `</ul></div>`;
    }

    let patternsSummaryHtml = '';
    if (uniquePatternReasons.size > 0) {
        patternsSummaryHtml = `<div class="patterns-summary"><h3>Patterns</h3><ul>`;
        uniquePatternReasons.forEach(reason => {
            patternsSummaryHtml += `<li>${escapeHtml(reason)}</li>`;
        });
        patternsSummaryHtml += `</ul></div>`;
    }

    let risksSummaryHtml = '';
    if (highRisksAcrossDecisions.length > 0 || mediumRisksAcrossDecisions.length > 0) {
        risksSummaryHtml = `<div class="risks-summary"><h3>Important Risks To Consider</h3><ul class="risks-list">`;
        
        highRisksAcrossDecisions.forEach(risk => {
            risksSummaryHtml += `
                <li class="risk-item risk-high">
                    <strong>HIGH: ${escapeHtml(risk.description)}</strong>
                    ${risk.migration ? `<span>Mitigation: ${escapeHtml(risk.migration)}</span>` : ''}
                    <div><small>From: ${escapeHtml(risk.decisionTitle)}</small></div>
                </li>`;
        });
        
        mediumRisksAcrossDecisions.forEach(risk => {
            risksSummaryHtml += `
                <li class="risk-item risk-medium">
                    <strong>MEDIUM: ${escapeHtml(risk.description)}</strong>
                    ${risk.migration ? `<span>Mitigation: ${escapeHtml(risk.migration)}</span>` : ''}
                    <div><small>From: ${escapeHtml(risk.decisionTitle)}</small></div>
                </li>`;
        });
        
        risksSummaryHtml += `</ul></div>`;
    }

    // Generate markdown for copying
    const markdownToCopy = generateMarkdownForDecisions(decisions, apiUrl);

    // Generate decision HTML
    let decisionsHtml = '';
    if (decisions && decisions.length > 0) {
        decisionsHtml += `<h2>Technical Decisions</h2>`;
        decisions.forEach(decision => {
            if (!decision || !decision.metadata) {
                return;
            }

            decisionsHtml += `
                <div class="decision">
                    <h3><a href="cursor://archknow.archknow/decision/${decision.id}" title="Open Decision Details: ${escapeHtml(decision.id)}">${escapeHtml(decision.metadata.title || 'Untitled Decision')}</a></h3>`;

            // Keep date if available
            if (decision.metadata.pr_merged_at && typeof decision.metadata.pr_merged_at === 'number' && decision.metadata.pr_merged_at > 0) {
                try {
                    const date = new Date(decision.metadata.pr_merged_at);
                    const formattedDate = date.toISOString().split('T')[0];
                    decisionsHtml += `<div class="decision-meta">Merged: ${formattedDate}</div>`;
                } catch (e) { /* ignore date formatting errors */ }
            }

            // Keep relevance reason if available
            if (decision.metadata.relevance_reason) {
                decisionsHtml += `
                    <div class="dev-prompt">
                        <h4>Why This Decision Is Relevant</h4>
                        <p>${escapeHtml(decision.metadata.relevance_reason)}</p>
                    </div>`;
            }

            if (decision.metadata.rationale) {
                decisionsHtml += `
                    <h4>Rationale</h4>
                    <pre>${escapeHtml(decision.metadata.rationale)}</pre>`;
            }

            if (decision.metadata.implications) {
                decisionsHtml += `
                    <h4>Implications</h4>
                    <pre>${escapeHtml(decision.metadata.implications)}</pre>`;
            }

            // Add Risks
            try {
                if (decision.metadata.risks_extracted && typeof decision.metadata.risks_extracted === 'string') {
                    const risks = JSON.parse(decision.metadata.risks_extracted);
                    if (Array.isArray(risks) && risks.length > 0) {
                        const highRisks = risks.filter(r => r && r.severity && r.severity.toLowerCase() === 'high');
                        const mediumRisks = risks.filter(r => r && r.severity && r.severity.toLowerCase() === 'medium');

                        if (highRisks.length > 0 || mediumRisks.length > 0) {
                            decisionsHtml += `<h4>Risks</h4><ul class="risks-list">`;
                            
                            highRisks.forEach(risk => {
                                decisionsHtml += `
                                    <li class="risk-item risk-high">
                                        <strong>HIGH: ${escapeHtml(risk.description)}</strong>
                                        ${risk.migration ? `<span>Mitigation: ${escapeHtml(risk.migration)}</span>` : ''}
                                    </li>`;
                            });
                            
                            mediumRisks.forEach(risk => {
                                decisionsHtml += `
                                    <li class="risk-item risk-medium">
                                        <strong>MEDIUM: ${escapeHtml(risk.description)}</strong>
                                        ${risk.migration ? `<span>Mitigation: ${escapeHtml(risk.migration)}</span>` : ''}
                                    </li>`;
                            });
                            
                            decisionsHtml += `</ul>`;
                        }
                    }
                }
            } catch (e) {
                console.error("ArchKnow: Error parsing risks for decision", e);
            }

            if (Array.isArray(decision.metadata.related_files) && decision.metadata.related_files.length > 0) {
                decisionsHtml += `
                    <h4>Related Files</h4>
                    <ul class="related-files-list">`;
                
                decision.metadata.related_files.forEach(file => {
                    decisionsHtml += `<li><code><a class="file-link" data-path="${escapeHtml(file)}">${escapeHtml(file)}</a></code></li>`;
                });
                
                decisionsHtml += `</ul>`;
            }

            if (Array.isArray(decision.referenced_decisions) && decision.referenced_decisions.length > 0) {
                decisionsHtml += `
                    <h4>Referenced Decisions</h4>
                    <ul class="referenced-decisions-list">`;
                
                decision.referenced_decisions.forEach(referencedDecision => {
                    let refTitle = 'Unknown Decision';
                    if (referencedDecision && referencedDecision.metadata && referencedDecision.metadata.title) {
                        refTitle = referencedDecision.metadata.title;
                    } else if (referencedDecision && referencedDecision.title) {
                        refTitle = referencedDecision.title;
                    }
                    
                    const refId = referencedDecision && referencedDecision.id ? referencedDecision.id : 'unknown';
                    decisionsHtml += `<li>${escapeHtml(refTitle)} (ID: ${escapeHtml(refId)})</li>`;
                });
                
                decisionsHtml += `</ul>`;
            }

            decisionsHtml += `</div>`;
        });
    } else {
        decisionsHtml = `<p class="no-decisions">No relevant architectural decisions found for the current file.</p>`;
    }

    const nonce = getNonce();

    return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline' ${vscode.Uri.parse(apiUrl).scheme === 'https' ? apiUrl : ''}; script-src 'nonce-${nonce}'; img-src data: https:;">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ArchKnow Context</title>
        <style>${styles}</style>
    </head>
    <body>
        <button id="copy-md-button" nonce="${nonce}">Copy as Markdown</button>
        
        <div id="markdown-data" data-markdown="${escapeHtml(markdownToCopy)}" style="display: none;"></div>

        ${patternsSummaryHtml}
        ${devPromptsSummaryHtml}
        ${risksSummaryHtml}
        ${conceptsSummaryHtml}
        ${decisionsHtml}

        <script nonce="${nonce}">
            (function() {
                const copyButton = document.getElementById('copy-md-button');
                const markdownContainer = document.getElementById('markdown-data');
                
                if (copyButton && markdownContainer) {
                    const markdown = markdownContainer.getAttribute('data-markdown');
                    copyButton.addEventListener('click', () => {
                        if (!markdown) {
                            copyButton.textContent = 'Error!';
                            setTimeout(() => { copyButton.textContent = 'Copy as Markdown'; }, 2000);
                            return;
                        }
                        navigator.clipboard.writeText(markdown).then(() => {
                            copyButton.textContent = 'Copied!';
                            setTimeout(() => { copyButton.textContent = 'Copy as Markdown'; }, 1500);
                        }, (err) => {
                            console.error('Failed to copy markdown: ', err);
                            copyButton.textContent = 'Failed!';
                            setTimeout(() => { copyButton.textContent = 'Copy as Markdown'; }, 2000);
                        });
                    });
                }
                 
                document.querySelectorAll('.file-link').forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const filePath = link.getAttribute('data-path');
                        if (filePath) {
                            const vscode = acquireVsCodeApi();
                            vscode.postMessage({
                                command: 'openFile',
                                filePath: filePath
                            });
                        }
                    });
                });
            })();
        </script>
    </body>
    </html>`;
}

function generateMarkdownForDecisions(decisions: Decision[], apiUrl: string): string {
    let markdown = '';

    const conceptCounts = new Map<string, number>();
    const uniquePatternReasons = new Set<string>(); // For follows_standard_practice_reason

    if (decisions && decisions.length > 0) {
        decisions.forEach(decision => {
            if (!decision || !decision.metadata) {
                console.error("ArchKnow: Encountered an undefined decision or decision metadata in generateMarkdownForDecisions. Skipping concept aggregation.");
                return;
            }

            const concepts = decision.metadata.domain_concepts;
            if (Array.isArray(concepts)) {
                concepts.forEach(concept => {
                    if (typeof concept === 'string' && concept.trim() !== '') {
                        conceptCounts.set(concept, (conceptCounts.get(concept) || 0) + 1);
                    }
                });
            }
        });
    }
    
    const sortedConcepts = Array.from(conceptCounts.entries())
        .map(([concept, count]) => ({ concept, count }))
        .sort((a, b) => b.count - a.count);

    if (sortedConcepts.length > 0) {
        markdown += `**Top Domain Concepts:**\n`;
        sortedConcepts.forEach(item => {
            markdown += `* ${escapeHtml(item.concept)} (${item.count})\n`;
        });
        markdown += `\n---\n\n`;
    }

    const uniqueDevPrompts = new Set<string>();
    if (decisions && decisions.length > 0) {
        decisions.forEach(decision => {
            if (!decision || !decision.metadata) {
                console.warn("ArchKnow: Skipping decision in dev prompt aggregation (markdown) due to missing metadata.");
                return;
            }
            if (decision.metadata.dev_prompt && typeof decision.metadata.dev_prompt === 'string') {
                const trimmedPrompt = decision.metadata.dev_prompt.trim();
                if (trimmedPrompt !== '') {
                    uniqueDevPrompts.add(trimmedPrompt);
                }
            }
        });
    }

    if (uniqueDevPrompts.size > 0) {
        markdown += `**Developer Guidance:**\n`;
        uniqueDevPrompts.forEach(prompt => {
            markdown += `* ${escapeHtml(prompt)}\n`;
        });
        markdown += `\n---\n\n`;
    }

    if (decisions && decisions.length > 0) {
        decisions.forEach(decision => {
            if (!decision || !decision.metadata) {
                console.error("ArchKnow: Encountered an undefined decision or decision metadata in generateMarkdownForDecisions. Skipping.");
                return;
            }

            markdown += `### ${escapeHtml(decision.metadata.title || 'Untitled Decision')}\n`;
            const decisionLinkUrl = `cursor://archknow.archknow/viewDecision?id=${encodeURIComponent(decision.id)}`;
            markdown += `[${decisionLinkUrl}](${decisionLinkUrl})\n\n`;

            if (decision.metadata.pr_merged_at && typeof decision.metadata.pr_merged_at === 'number' && decision.metadata.pr_merged_at > 0) {
                try {
                    const date = new Date(decision.metadata.pr_merged_at);
                    const formattedDate = date.toISOString().split('T')[0];
                    markdown += `*Merged: ${formattedDate}*\n\n`;
                } catch (e) { }
            }

            if (decision.metadata.rationale) {
                markdown += `**Rationale:**\n\`\`\`\n${escapeHtml(decision.metadata.rationale)}\n\`\`\`\n\n`;
            }
            if (decision.metadata.implications) {
                markdown += `**Implications:**\n\`\`\`\n${escapeHtml(decision.metadata.implications)}\n\`\`\`\n\n`;
            }

            try {
                if (decision.metadata.risks_extracted && typeof decision.metadata.risks_extracted === 'string') {
                    const risks: Risk[] = JSON.parse(decision.metadata.risks_extracted);
                    if (Array.isArray(risks)) {
                        const filteredRisks = risks.filter(r => r && r.severity && (r.severity.toLowerCase() === 'high' || r.severity.toLowerCase() === 'medium'));
                        if (filteredRisks.length > 0) {
                            const highRisks = filteredRisks.filter(r => r.severity.toLowerCase() === 'high');
                            const mediumRisks = filteredRisks.filter(r => r.severity.toLowerCase() === 'medium');

                            if (highRisks.length > 0) {
                                markdown += `**High Severity Risks:**\n`;
                                highRisks.forEach(risk => {
                                    markdown += `* ${escapeHtml(risk.description)}\n`;
                                    if (risk.migration) {
                                        markdown += `  *Mitigation:* ${escapeHtml(risk.migration)}\n`;
                                    }
                                });
                                markdown += `\n`;
                            }
                            if (mediumRisks.length > 0) {
                                markdown += `**Medium Severity Risks:**\n`;
                                mediumRisks.forEach(risk => {
                                    markdown += `* ${escapeHtml(risk.description)}\n`;
                                    if (risk.migration) {
                                        markdown += `  *Mitigation:* ${escapeHtml(risk.migration)}\n`;
                                    }
                                });
                                markdown += `\n`;
                            }
                        }
                    }
                }
            } catch (e) { }

            if (Array.isArray(decision.metadata.related_files) && decision.metadata.related_files.length > 0) {
                markdown += `**Related Files:**\n`;
                decision.metadata.related_files.forEach((file: string) => {
                    markdown += `* \`${escapeHtml(file)}\`\n`;
                });
                markdown += `\n`;
            }

            if (Array.isArray(decision.referenced_decisions) && decision.referenced_decisions.length > 0) {
                markdown += `**Referenced Decisions:**\n`;
                decision.referenced_decisions.forEach((referencedDecision: any) => {
                    let refTitle = 'Unknown Decision';
                    if (referencedDecision && referencedDecision.metadata && referencedDecision.metadata.title) {
                        refTitle = referencedDecision.metadata.title;
                    } else if (referencedDecision && referencedDecision.title) {
                        refTitle = referencedDecision.title;
                    }
                    
                    const refId = referencedDecision && referencedDecision.id ? referencedDecision.id : 'unknown';
                    markdown += `* ${escapeHtml(refTitle)} (ID: ${escapeHtml(refId)})\n`;
                });
                markdown += `\n`;
            }

            // Aggregate best practice reasons for Markdown output
            if (decision.metadata.follows_standard_practice_reason && 
                typeof decision.metadata.follows_standard_practice_reason === 'string' && 
                decision.metadata.follows_standard_practice_reason.trim() !== '') {
                uniquePatternReasons.add(decision.metadata.follows_standard_practice_reason.trim());
            }

            markdown += `\n---\n\n`;
        });
    } else {
        markdown += `_No relevant active architectural decisions found for the current file._\n`;
    }

    if (uniquePatternReasons.size > 0) {
        markdown += `**Patterns Observed:**\n`;
        uniquePatternReasons.forEach(reason => {
            markdown += `* ${escapeHtml(reason)}\n`;
        });
        markdown += `\n---\n\n`;
    }

    return markdown;
}

export function getFeedbackWebviewContent(feedback: string): string {
    const feedbackHtml = marked.parse(feedback);

    let agentInstructions = '';
    const agentInstructionsMatch = feedback.match(/\n## 🤖 Agent Instructions \(for actionable changes\)([^#]*)/);
    if (agentInstructionsMatch && agentInstructionsMatch[1]) {
        agentInstructions = agentInstructionsMatch[1].trim();
    }
    const agentInstructionsHtml = marked.parse(agentInstructions.replace(/\*\*File:\*\*/g, '\n**File:**').replace(/\*\*Issue:\*\*/g, '\n**Issue:**').replace(/\*\*Action:\*\*/g, '\n**Action:**'));

    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Feedback</title>
            <style>
                body {
                    font-family: var(--vscode-editor-font-family, Segoe WPC,Segoe UI,sans-serif);
                    color: var(--vscode-editor-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                    line-height: 1.6;
                }
                h1, h2, h3, h4, h5, h6 {
                    font-weight: bold;
                    margin-top: 1.5em;
                    margin-bottom: 0.5em;
                }
                h1 { color: var(--vscode-textLink-foreground); }
                h2 { 
                    border-bottom: 1px solid var(--vscode-editorWidget-border, #454545);
                    padding-bottom: 0.3em;
                }
                ul, ol {
                    margin-left: 20px;
                }
                li {
                    margin-bottom: 0.5em;
                }
                code {
                    font-family: var(--vscode-editor-font-family, Menlo, Monaco, Consolas, "Courier New", monospace);
                    background-color: var(--vscode-textCodeBlock-background, rgba(0, 0, 0, 0.1));
                    padding: 0.2em 0.4em;
                    border-radius: 3px;
                    font-size: 0.9em;
                }
                pre code {
                    display: block;
                    padding: 0.5em;
                    overflow-x: auto;
                }
                blockquote {
                    border-left: 3px solid var(--vscode-textSeparator-foreground);
                    padding-left: 10px;
                    margin-left: 0;
                    color: var(--vscode-textSeparator-foreground);
                }
                strong { font-weight: bold; }
                em { font-style: italic; }
                table {
                    border-collapse: collapse;
                    width: 100%;
                    margin-bottom: 1em;
                }
                th, td {
                    border: 1px solid var(--vscode-editorWidget-border, #454545);
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: var(--vscode-editorWidget-background, #252526);
                }
                .copy-button {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: 1px solid var(--vscode-button-border, transparent);
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                    margin-left: 10px;
                    font-size: 0.9em;
                }
                .copy-button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                .feedback-content {
                    margin-bottom: 30px;
                }
                .agent-instructions-section {
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 2px solid var(--vscode-editorWidget-border, #454545);
                }
            </style>
        </head>
        <body>
            <div class="feedback-content">
                ${feedbackHtml}
            </div>

            ${agentInstructions ? `
            <div class="agent-instructions-section">
                <h2>🤖 Agent Instructions (for actionable changes) <button class="copy-button" onclick="copyAgentInstructions()">Copy Instructions</button></h2>
                <div id="agent-instructions-text">
                    ${agentInstructionsHtml}
                </div>
            </div>
            ` : ''}

            <script>
                const vscode = acquireVsCodeApi();

                function copyAgentInstructions() {
                    const agentInstructionsText = document.getElementById('agent-instructions-text').innerText;
                    navigator.clipboard.writeText(agentInstructionsText).then(() => {
                        vscode.postMessage({
                            command: 'showInformationMessage',
                            text: 'Agent instructions copied to clipboard!'
                        });
                    }).catch(err => {
                        console.error('Failed to copy agent instructions: ', err);
                        vscode.postMessage({
                            command: 'showErrorMessage',
                            text: 'Failed to copy agent instructions: ' + err.message
                        });
                    });
                }
            </script>
        </body>
        </html>
    `;
}

export function getDesignDocGeneratorHtml(
    options: { taskTitle?: string; taskDescription?: string } = {}
): string {
    const { taskTitle = '', taskDescription = '' } = options;

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Design Document</title>
    <style>
        /* Same styles as before */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
            padding: 20px;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: var(--vscode-editor-foreground);
            border-bottom: 1px solid var(--vscode-separator-foreground);
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--vscode-descriptionForeground);
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-size: 1em;
            box-sizing: border-box; /* Important for width */
        }
        textarea {
            min-height: 150px;
            resize: vertical;
        }
        button {
            padding: 10px 18px;
            border: none;
            border-radius: 4px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: background-color 0.2s ease;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        #error-message {
            color: var(--vscode-errorForeground);
            margin-top: 15px;
            display: none; /* Hidden by default */
        }
        .loader {
            border: 4px solid var(--vscode-descriptionForeground);
            border-radius: 50%;
            border-top: 4px solid var(--vscode-button-background);
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: none; /* Hidden by default */
            margin-left: 10px;
        }
        .button-container {
            display: flex;
            align-items: center;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Generate Technical Design Document</h1>
        <p>Fill in the details below to generate the technical design for your task</p>
        
        <div class="form-group">
            <label for="task-title">Task Title *</label>
            <input type="text" id="task-title" placeholder="e.g., Implement OAuth 2.0 for API Authentication" value="${escapeHtml(taskTitle)}">
        </div>
        
        <div class="form-group">
            <label for="task-description">Task Description *</label>
            <textarea id="task-description" placeholder="Provide a detailed description of the task, including goals, requirements, and user stories.">${escapeHtml(taskDescription)}</textarea>
        </div>
        
        <div class="form-group">
            <label for="initial-approach">Initial Approach or Ideas (Optional)</label>
            <textarea id="initial-approach" placeholder="If you have any initial thoughts on how to approach this, describe them here. This can include potential technologies, architecture patterns, or known constraints."></textarea>
        </div>
        
        <div class="button-container">
            <button id="generate-button">Generate</button>
            <div id="loader" class="loader"></div>
        </div>
        <div id="error-message"></div>
        <div id="job-info" style="margin-top: 15px; display: none;"></div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        const generateButton = document.getElementById('generate-button');
        const titleInput = document.getElementById('task-title');
        const descriptionInput = document.getElementById('task-description');
        const approachInput = document.getElementById('initial-approach');
        const errorMessageDiv = document.getElementById('error-message');
        const loader = document.getElementById('loader');
        const jobInfoDiv = document.getElementById('job-info');

        generateButton.addEventListener('click', () => {
            const taskTitle = titleInput.value.trim();
            const taskDescription = descriptionInput.value.trim();
            const initialApproachIdeas = approachInput.value.trim();

            if (!taskTitle || !taskDescription) {
                errorMessageDiv.textContent = 'Task Title and Task Description are required.';
                errorMessageDiv.style.display = 'block';
                return;
            }

            errorMessageDiv.style.display = 'none';
            generateButton.disabled = true;
            loader.style.display = 'inline-block';

            vscode.postMessage({
                command: 'generateDesignDoc',
                taskTitle,
                taskDescription,
                initialApproachIdeas
            });
        });

        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'error':
                    errorMessageDiv.textContent = 'Error: ' + message.message;
                    errorMessageDiv.style.display = 'block';
                    generateButton.disabled = false;
                    loader.style.display = 'none';
                    break;
                case 'jobSubmitted':
                    jobInfoDiv.style.display = 'block';
                    jobInfoDiv.innerHTML = \`Job <strong>\${message.jobId}</strong> submitted successfully. <br>\${message.message}\`;
                    // The panel will be closed by the extension, but we can also hide the form
                    document.querySelector('.container').innerHTML = '<h1>Job Submitted</h1>' + jobInfoDiv.innerHTML;
                    break;
            }
        });
    </script>
</body>
</html>`;
}

function generateWebviewContent(title: string, body: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-editor-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .markdown-body {
            line-height: 1.6;
        }
        .markdown-body h1, .markdown-body h2, .markdown-body h3, .markdown-body h4 {
            color: var(--vscode-textLink-foreground);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            padding-bottom: 5px;
        }
        .markdown-body h1 { font-size: 1.8em; }
        .markdown-body h2 { font-size: 1.5em; }
        .markdown-body h3 { font-size: 1.3em; }
        .markdown-body h4 { font-size: 1.1em; }
        .markdown-body p, .markdown-body ul, .markdown-body ol {
            margin-bottom: 1em;
        }
        .markdown-body ul, .markdown-body ol {
            padding-left: 2em;
        }
        .markdown-body li {
            margin-bottom: 0.5em;
        }
        .markdown-body blockquote {
            border-left: 3px solid var(--vscode-textLink-foreground);
            padding-left: 1em;
            margin-left: 0;
            color: var(--vscode-descriptionForeground);
        }
        .markdown-body code {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: var(--vscode-editor-font-family);
            font-size: 0.9em;
        }
        .markdown-body pre code {
            display: block;
            padding: 1em;
            overflow-x: auto;
            line-height: 1.45;
        }
        .markdown-body table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 1em;
        }
        .markdown-body th, .markdown-body td {
            border: 1px solid var(--vscode-textSeparator-foreground);
            padding: 0.5em;
            text-align: left;
        }
        .markdown-body th {
            background-color: var(--vscode-textBlockQuote-background);
        }
        .markdown-body a {
            color: var(--vscode-textLink-foreground);
            text-decoration: none;
        }
        .markdown-body a:hover {
            text-decoration: underline;
        }
        button { padding: 0.5em 1em; cursor: pointer; border: 1px solid var(--vscode-button-border, var(--vscode-contrastBorder)); background-color: var(--vscode-button-background); color: var(--vscode-button-foreground); border-radius: 3px; margin-top: 0.5em; }
        button:hover:not(:disabled) { background-color: var(--vscode-button-hoverBackground); }
        #error-message { color: var(--vscode-errorForeground); margin-top: 1em; min-height: 1.2em; }
        .options label { margin-left: 0.3em; display: inline-block; margin-bottom: 0.2em; }
        .options > div { margin-bottom: 0.8em; }

         button:disabled {
            opacity: 0.5;
            cursor: default;
         }
         textarea[disabled],
         #file-selection-ui[disabled] #select-file-button,
         #file-selection-ui[disabled] #selected-file-path {
             opacity: 0.5;
             pointer-events: none;
             background-color: var(--vscode-disabledForeground);
             border-color: var(--vscode-disabledForeground);
             color: var(--vscode-editor-background);
         }
         #file-selection-ui[disabled] #selected-file-path {
              color: var(--vscode-editorHint-foreground);
         }
         input[type="radio"]:disabled + label {
              opacity: 0.5;
              cursor: default;
         }
    </style>
</head>
<body>
    <div class="container">
        <h1>${title}</h1>
        <div class="markdown-body">
            ${body}
        </div>
    </div>
</body>
</html>`;
}

// AI Review Panel Styles
const aiReviewStyles = `
    .ai-review-panel {
        margin-top: 1em;
        padding: 1em;
        background-color: var(--vscode-sideBar-background);
        border: 1px solid var(--vscode-sideBarSectionHeader-border);
        border-radius: 5px;
    }
    
    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1em;
    }
    
    .comments-section {
        margin-top: 1em;
    }
    
    .ai-comment {
        margin: 0.5em 0;
        padding: 1em;
        border: 1px solid var(--vscode-textSeparator-foreground);
        border-radius: 3px;
        background-color: var(--vscode-editor-background);
    }
    
    .comment-header {
        display: flex;
        align-items: center;
        gap: 0.5em;
        margin-bottom: 0.5em;
    }
    
    .comment-type {
        padding: 0.2em 0.5em;
        border-radius: 3px;
        font-size: 0.8em;
        font-weight: bold;
    }
    
    .comment-type-blocking {
        background-color: var(--vscode-errorForeground);
        color: var(--vscode-editor-background);
    }
    
    .comment-type-non-blocking {
        background-color: var(--vscode-warningForeground);
        color: var(--vscode-editor-background);
    }
    
    .comment-type-nit {
        background-color: var(--vscode-textPreformat-foreground);
        color: var(--vscode-editor-background);
    }
    
    .comment-status {
        font-size: 0.8em;
        color: var(--vscode-descriptionForeground);
    }
    
    .comment-content {
        margin: 0.5em 0;
    }
    
    .comment-section {
        margin-top: 0.5em;
        font-size: 0.9em;
        color: var(--vscode-descriptionForeground);
    }
    
    .comment-actions {
        display: flex;
        gap: 0.5em;
        margin-top: 0.5em;
    }
    
    .action-button {
        font-size: 0.8em;
        padding: 0.3em 0.6em;
    }
    
    .action-button.secondary {
        background-color: transparent;
        border: 1px solid var(--vscode-button-secondaryBackground);
        color: var(--vscode-button-secondaryForeground);
    }
    
    .comment-resolution {
        margin-top: 0.5em;
        padding: 0.5em;
        background-color: var(--vscode-textBlockQuote-background);
        border-left: 3px solid var(--vscode-textLink-foreground);
        font-size: 0.9em;
    }
    
    .resolution-meta {
        margin-top: 0.3em;
        font-size: 0.8em;
        color: var(--vscode-descriptionForeground);
    }
    
    .comment-select {
        margin-left: auto;
        font-size: 0.8em;
        color: var(--vscode-descriptionForeground);
    }
`;

function generateAiCommentHtml(comment: AiReviewComment): string {
    const commentTypeClass = `comment-type-${comment.type}`;
    const commentStatusClass = `comment-status-${comment.status}`;
    const isAddressable = comment.status === 'open' || comment.status === 'pending-ai-addressal';
    
    return `
    <div class="ai-comment ${commentTypeClass} ${commentStatusClass}" data-comment-id="${comment.id}">
        <div class="comment-header">
            <span class="comment-type">${comment.type}</span>
            <span class="comment-status">${comment.status}</span>
            ${isAddressable ? `
            <label class="comment-select">
                <input type="checkbox" class="comment-checkbox" onchange="updateSelectedComments()">
                Select for AI addressing
            </label>
            ` : ''}
        </div>
        
        <div class="comment-content">
            <div class="comment-text">${escapeHtml(comment.text)}</div>
            ${comment.suggestedSection ? `
            <div class="comment-section">
                <strong>Suggested Section:</strong> ${escapeHtml(comment.suggestedSection)}
            </div>
            ` : ''}
        </div>
        
        ${comment.status === 'open' ? `
        <div class="comment-actions">
            <button onclick="markCommentStatus('${comment.id}', 'addressed')" class="action-button">
                Mark as Addressed
            </button>
            <button onclick="markCommentStatus('${comment.id}', 'wont-fix')" class="action-button secondary">
                Won't Fix
            </button>
            <button onclick="markCommentStatus('${comment.id}', 'pending-ai-addressal')" class="action-button">
                Let AI Address
            </button>
        </div>
        ` : ''}
        
        ${(comment.status === 'addressed' || comment.status === 'wont-fix') && comment.resolutionText ? `
        <div class="comment-resolution">
            <strong>Resolution:</strong> ${escapeHtml(comment.resolutionText)}
            <div class="resolution-meta">
                by ${comment.resolutionAuthor} on ${new Date(comment.resolutionTimestamp!).toLocaleDateString()}
            </div>
        </div>
        ` : ''}
    </div>
    `;
}

function generateAiReviewPanel(metadata: DesignDocMetadata): string {
    const aiComments = metadata.aiComments || [];
    const openComments = aiComments.filter((c: AiReviewComment) => c.status === 'open' || c.status === 'pending-ai-addressal');
    const addressedComments = aiComments.filter((c: AiReviewComment) => c.status === 'addressed' || c.status === 'wont-fix');

    return `
    <div class="ai-review-panel">
        <div class="panel-header">
            <h3>AI Review</h3>
            ${metadata.status === 'pending' ? `
            <button id="request-ai-review-panel" onclick="requestAiReview()" class="primary-button">
                Request AI Review
            </button>` : ''}
        </div>
        
        ${openComments.length > 0 ? `
        <div class="comments-section open-comments">
            <h4>Open Comments (${openComments.length})</h4>
            <div class="comments-list">
                ${openComments.map(comment => generateAiCommentHtml(comment)).join('')}
            </div>
            ${openComments.length > 0 ? `
            <button id="address-selected-comments" onclick="addressSelectedComments()" class="secondary">
                Let AI Address Selected Comments
            </button>
            ` : ''}
        </div>
        ` : ''}
        
        ${addressedComments.length > 0 ? `
        <div class="comments-section addressed-comments">
            <h4>Addressed Comments (${addressedComments.length})</h4>
            <div class="comments-list">
                ${addressedComments.map(comment => generateAiCommentHtml(comment)).join('')}
            </div>
        </div>
        ` : ''}
    </div>
    `;
}

// Update the existing getDesignDocWebviewContent function to include AI review functionality
export function getDesignDocWebviewContent(extensionUri: vscode.Uri, webview: vscode.Webview, metadata: DesignDocMetadata): string {
    const nonce = getNonce();
    
    // Combine the existing styles with AI review styles
    const combinedStyles = `
        ${getBaseStyles()} /* Your existing base styles function */
        ${aiReviewStyles}
    `;

    return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Design Document</title>
        <style>
            ${combinedStyles}
        </style>
    </head>
    <body>
        <div class="container">
            ${generateMetadataSection(metadata)}
            ${generateAiReviewPanel(metadata)}
            <div id="content" class="markdown-body">
                <!-- Content will be inserted here -->
            </div>
        </div>
        <script nonce="${nonce}">
            const vscode = acquireVsCodeApi();
            let currentDocUri = undefined;
            
            // Existing message handler
            window.addEventListener('message', event => {
                const message = event.data;
                switch (message.type) {
                    case 'setContent':
                        document.getElementById('content').innerHTML = message.content;
                        break;
                    case 'setDocUri':
                        currentDocUri = message.uri;
                        break;
                }
            });
            
            // AI Review Functions
            function requestAiReview() {
                if (!currentDocUri) return;
                const metadataButton = document.getElementById('request-ai-review-metadata');
                const panelButton = document.getElementById('request-ai-review-panel');
                
                if (metadataButton) {
                    metadataButton.disabled = true;
                    metadataButton.textContent = 'Requesting AI Review...';
                }
                if (panelButton) {
                    panelButton.disabled = true;
                    panelButton.textContent = 'Requesting AI Review...';
                }
                
                vscode.postMessage({
                    command: 'requestAiReview',
                    docUriString: currentDocUri
                });
            }
            
            function updateSelectedComments() {
                const selectedComments = Array.from(document.querySelectorAll('.comment-checkbox:checked'))
                    .map(cb => cb.closest('.ai-comment').dataset.commentId);
                
                const addressButton = document.getElementById('address-selected-comments');
                if (addressButton) {
                    addressButton.disabled = selectedComments.length === 0;
                }
            }
            
            function addressSelectedComments() {
                if (!currentDocUri) return;
                const selectedComments = Array.from(document.querySelectorAll('.comment-checkbox:checked'))
                    .map(cb => cb.closest('.ai-comment').dataset.commentId);
                
                if (selectedComments.length > 0) {
                    vscode.postMessage({
                        command: 'addressAiComment',
                        docUriString: currentDocUri,
                        commentIds: selectedComments
                    });
                }
            }
            
            function markCommentStatus(commentId, newStatus) {
                if (!currentDocUri) return;
                let resolutionText;
                if (newStatus === 'wont-fix') {
                    resolutionText = prompt('Please provide a reason why this comment will not be addressed:');
                    if (!resolutionText) return; // User cancelled
                } else if (newStatus === 'addressed') {
                    resolutionText = prompt('Please describe how you addressed this comment:');
                    if (!resolutionText) return; // User cancelled
                }
                
                vscode.postMessage({
                    command: 'updateCommentStatus',
                    docUriString: currentDocUri,
                    commentId,
                    newStatus,
                    resolutionText
                });
            }
        </script>
    </body>
    </html>`;
}

// Helper function to get base styles (replace with your actual base styles)
function getBaseStyles(): string {
    return `
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-editor-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 1em;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        /* Add other base styles as needed */
    `;
}

// Helper function to generate metadata section (replace with your actual implementation)
function generateMetadataSection(metadata: DesignDocMetadata): string {
    return `
        <div class="metadata-section">
            <h1>${escapeHtml(metadata.title || 'Untitled Design Document')}</h1>
            <div class="metadata-details">
                <p>Status: ${metadata.status}</p>
                ${metadata.author ? `<p>Author: ${escapeHtml(metadata.author)}</p>` : ''}
                ${metadata.lastUpdated ? `<p>Last Updated: ${new Date(metadata.lastUpdated).toLocaleDateString()}</p>` : ''}
                ${metadata.status === 'pending' ? `
                <button id="request-ai-review-metadata" onclick="requestAiReview()" class="primary-button">
                    Request AI Review
                </button>` : ''}
            </div>
        </div>
    `;
}

export function getDesignDocJobsStatusHtml(): string {
    return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Design Doc Jobs Status</title>
        <style>
            .job-card {
                border: 1px solid var(--vscode-panel-border);
                border-radius: 6px;
                margin: 10px 0;
                padding: 15px;
                background-color: var(--vscode-editor-background);
            }
            
            .job-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }
            
            .job-title {
                font-size: 1.1em;
                font-weight: bold;
                color: var(--vscode-editor-foreground);
            }
            
            .job-status {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.9em;
            }
            
            .status-pending {
                background-color: var(--vscode-statusBarItem-warningBackground);
                color: var(--vscode-statusBarItem-warningForeground);
            }
            
            .status-processing {
                background-color: var(--vscode-progressBar-background);
                color: var(--vscode-statusBarItem-remoteForeground);
            }
            
            .status-completed {
                background-color: var(--vscode-testing-iconPassed);
                color: var(--vscode-statusBarItem-remoteForeground);
            }
            
            .status-error {
                background-color: var(--vscode-statusBarItem-errorBackground);
                color: var(--vscode-statusBarItem-errorForeground);
            }
            
            .progress-bar {
                width: 100%;
                height: 6px;
                background-color: var(--vscode-progressBar-background);
                border-radius: 3px;
                margin: 10px 0;
                overflow: hidden;
                opacity: 0.3;
            }
            
            .progress-fill {
                height: 100%;
                background-color: var(--vscode-progressBar-background);
                transition: width 0.3s ease;
                opacity: 1;
            }
            
            .job-details {
                margin-top: 10px;
                font-size: 0.9em;
                color: var(--vscode-descriptionForeground);
            }

            .job-timestamp {
                margin-top: 5px;
                font-size: 0.8em;
                color: var(--vscode-descriptionForeground);
                opacity: 0.8;
            }
            
            .job-actions {
                margin-top: 10px;
                display: flex;
                gap: 10px;
            }
            
            button {
                padding: 6px 12px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
            }
            
            button:hover {
                background-color: var(--vscode-button-hoverBackground);
            }
            
            button:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }
            
            .no-jobs {
                text-align: center;
                padding: 20px;
                color: var(--vscode-descriptionForeground);
            }

            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding: 10px;
                background-color: var(--vscode-editor-background);
            }

            .header h2 {
                margin: 0;
                color: var(--vscode-editor-foreground);
            }

            .refresh-button {
                background-color: transparent;
                border: 1px solid var(--vscode-button-background);
                padding: 4px 8px;
                font-size: 0.9em;
            }

            .refresh-button:hover {
                background-color: var(--vscode-button-background);
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>Design Document Jobs</h2>
            <button class="refresh-button" onclick="refreshJobs()">Refresh</button>
        </div>
        <div id="jobs-container">
            <div class="no-jobs">No active design doc generation jobs.</div>
        </div>
        
        <script>
            const vscode = acquireVsCodeApi();
            
            // Initialize with any existing jobs
            window.addEventListener('message', event => {
                const message = event.data;
                switch (message.command) {
                    case 'updateJobs':
                        updateJobsDisplay(message.jobs);
                        break;
                    case 'updateJob':
                        updateSingleJob(message.jobId, message.status);
                        break;
                }
            });
            
            function updateJobsDisplay(jobsList) {
                const container = document.getElementById('jobs-container');
                if (!jobsList || jobsList.length === 0) {
                    container.innerHTML = '<div class="no-jobs">No active design doc generation jobs.</div>';
                    return;
                }
                
                container.innerHTML = jobsList.map(job => createJobCard(job)).join('');
            }

            function updateSingleJob(jobId, status) {
                const existingCard = document.getElementById(\`job-\${jobId}\`);
                if (existingCard) {
                    // Update the existing card
                    existingCard.outerHTML = createJobCard(status);
                } else {
                    // If the card doesn't exist, refresh all jobs
                    refreshJobs();
                }
            }
            
            function createJobCard(job) {
                const createdAt = new Date(job.created_at).toLocaleString();
                const updatedAt = new Date(job.updated_at).toLocaleString();
                
                return \`
                    <div class="job-card" id="job-\${job.jobId}">
                        <div class="job-header">
                            <span class="job-title">\${job.taskTitle}</span>
                            <span class="job-status status-\${job.status}">\${job.status.toUpperCase()}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: \${job.progressPercentage || 0}%"></div>
                        </div>
                        <div class="job-details">\${job.message || job.phaseProgress || ''}</div>
                        <div class="job-timestamp">
                            Created: \${createdAt}<br>
                            Last Updated: \${updatedAt}
                        </div>
                        <div class="job-actions">
                            \${job.status === 'completed' && job.output_file_path ? 
                                \`<button onclick="openDocument('\${job.jobId}')">Open Document</button>\` : 
                                ''}
                        </div>
                    </div>
                \`;
            }
            
            function openDocument(jobId) {
                vscode.postMessage({
                    command: 'openDocument',
                    jobId: jobId
                });
            }

            function refreshJobs() {
                vscode.postMessage({
                    command: 'refreshJobs'
                });
            }
        </script>
    </body>
    </html>`;
}

function generateSingleDecisionMarkdown(decision: Decision): string {
    let markdown = `# ${escapeHtml(decision.metadata.title)}\\n\\n`;

    if (decision.metadata.pr_url) {
        markdown += `**Source PR:** [${escapeHtml(decision.metadata.pr_url)}](${escapeHtml(decision.metadata.pr_url)})\\n`;
    }
    if (decision.metadata.pr_merged_at) {
        const mergedDate = new Date(decision.metadata.pr_merged_at * 1000);
        markdown += `**Merged At:** ${escapeHtml(mergedDate.toLocaleDateString())}\\n`;
    }
    if (decision.id) {
        markdown += `**Decision ID:** ${escapeHtml(decision.id)}\\n`;
    }
    markdown += `\\n`; // Ensure separation

    const addSection = (title: string, content?: string | null, isPreformatted = false) => {
        if (content && content.trim()) {
            markdown += `## ${title}\\n\\n`;
            if (isPreformatted) {
                markdown += `\`\`\`\\n${escapeHtml(content.trim())}\\n\`\`\`\\n\\n`;
            } else {
                markdown += `${escapeHtml(content.trim())}\\n\\n`;
            }
        }
    };

    addSection("Rationale", decision.metadata.rationale);
    addSection("Implications", decision.metadata.implications);
    // Treat risks_extracted as preformatted if it's typically JSON or structured text
    addSection("Risks", decision.metadata.risks_extracted, true);
    addSection("Developer Prompt Used", decision.metadata.dev_prompt, true);
    addSection("Best Practice Adherence", decision.metadata.follows_standard_practice_reason);
    // Treat data_model_changes as preformatted if it's typically JSON or structured text
    addSection("Data Model Changes", decision.metadata.data_model_changes, true);

    if (decision.metadata.related_files && decision.metadata.related_files.length > 0) {
        markdown += `## Related Files\\n\\n`;
        decision.metadata.related_files.forEach(file => {
            markdown += `* <a href=\"#\" data-action=\"open-file\" data-filepath=\"${escapeHtml(file)}\">${escapeHtml(file)}</a>\\n`;
        });
        markdown += `\\n`; // Double newline after list
    }
    
    if (decision.metadata.domain_concepts && decision.metadata.domain_concepts.length > 0) {
        markdown += `## Domain Concepts\\n\\n`;
        markdown += decision.metadata.domain_concepts.map(concept => `\`${escapeHtml(concept)}\``).join(', ');
        markdown += `\\n\\n`;
    }

    if (decision.referenced_decisions && decision.referenced_decisions.length > 0) {
        markdown += `## Referenced Decisions\\n\\n`;
        decision.referenced_decisions.forEach(ref => {
            const refTitle = ref.metadata?.title || ref.id;
            markdown += `* **${escapeHtml(ref.relationship_type || 'Related to')}:** <a href=\"#\" data-action=\"view-decision\" data-decision-id=\"${escapeHtml(ref.id)}\">${escapeHtml(refTitle)}</a> (ID: ${escapeHtml(ref.id)})\\n`;
        });
        markdown += `\\n`; // Double newline after list
    }
    return markdown;
}

export function getSingleDecisionWebviewContent(decision: Decision, apiUrl: string, panelTitle: string): string {
    const nonce = getNonce();
    const decisionMarkdown = generateSingleDecisionMarkdown(decision);
    const htmlContent = marked.parse(decisionMarkdown, { breaks: true });

    const cspSource = vscode.workspace.getConfiguration().get('workbench.experimental.webview.cspSource') || 'https://*.vscode-cdn.net';

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; img-src data: https:;">
    <title>${escapeHtml(panelTitle)}</title>
    <style>
        body {\n            font-family: var(--vscode-font-family);\n            color: var(--vscode-editor-foreground);\n            background-color: var(--vscode-editor-background);\n            padding: 20px;\n            line-height: 1.6;\n        }\n        .decision-card {\n            background-color: var(--vscode-sideBar-background, var(--vscode-editorWidget-background));\n            border: 1px solid var(--vscode-sideBar-border, var(--vscode-editorWidget-border, var(--vscode-contrastBorder)));\n            border-radius: 5px;\n            padding: 15px;\n            margin-bottom: 20px;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n        }\n        .decision-header {\n            margin-bottom: 10px;\n        }\n        .decision-header .decision-title { /* More specific selector for the main title */\n            font-size: 1.8em; /* Ensure main title is large */\n            font-weight: bold;\n            margin: 0 0 10px 0;\n            color: var(--vscode-editor-foreground); /* Standard foreground for title */\n            border-bottom: none; /* No border for the main card title itself */\n        }\n         .meta-info {\n            font-size: 0.9em;\n            color: var(--vscode-descriptionForeground);\n            margin-bottom: 15px;\n        }\n        .meta-info span { margin-right: 15px; display: inline-block; margin-bottom: 5px; }\n        \n        /* Styles for content generated from Markdown */\n        .markdown-body-content {\n            /* This div wraps the HTML from marked.parse() */\n        }\n        .markdown-body-content h1,\n        .markdown-body-content h2,\n        .markdown-body-content h3,\n        .markdown-body-content h4,\n        .markdown-body-content h5,\n        .markdown-body-content h6 {\n            color: var(--vscode-textLink-foreground);\n            border-bottom: 1px solid var(--vscode-editorWidget-border, var(--vscode-contrastBorder));\n            padding-bottom: 0.3em;\n            margin-top: 1.5em;\n            margin-bottom: 1em;\n            font-weight: bold;\n            line-height: 1.3; /* Specific line height for headings */\n        }\n        .markdown-body-content h1 { font-size: 1.6em; } /* Markdown H1 (if any) */\n        .markdown-body-content h2 { font-size: 1.4em; } /* Markdown H2 */\n        .markdown-body-content h3 { font-size: 1.2em; } /* Markdown H3 */\n        .markdown-body-content p {\n            margin-bottom: 1em;\n            white-space: normal; /* Crucial for wrapping and respecting <br> */\n            font-size: 1em; /* Ensure normal font size */\n            line-height: 1.6; /* Inherit from body or set explicitly */\n            color: var(--vscode-editor-foreground); /* Ensure normal text color */\n        }\n        .markdown-body-content ul,\n        .markdown-body-content ol {\n            margin-left: 20px;\n            margin-bottom: 1em;\n            white-space: normal;\n        }\n        .markdown-body-content li {\n            margin-bottom: 0.5em;\n            white-space: normal;\n        }\n        .markdown-body-content pre {\n            background-color: var(--vscode-textCodeBlock-background);\n            padding: 1em;\n            overflow-x: auto;\n            border-radius: 4px;\n            border: 1px solid var(--vscode-editorWidget-border, var(--vscode-contrastBorder));\n            white-space: pre-wrap;\n            word-wrap: break-word;\n            color: var(--vscode-editor-foreground); /* Ensure code text is visible */\n        }\n        .markdown-body-content code { /* For inline code */\n            background-color: var(--vscode-textCodeBlock-background);\n            padding: 0.2em 0.4em;\n            border-radius: 3px;\n            font-family: var(--vscode-editor-font-family);\n            font-size: 0.9em;\n            white-space: normal;\n            color: var(--vscode-editor-foreground); /* Ensure code text is visible */\n        }\n        .markdown-body-content pre code { /* For code inside pre, reset some styles */\n            padding: 0;\n            font-size: inherit;\n            background-color: transparent;\n            border-radius: 0;\n            border: none;\n            white-space: pre; \n        }\n         .markdown-body-content a {\n            color: var(--vscode-textLink-activeForeground);\n            text-decoration: none;\n        }\n        .markdown-body-content a:hover {\n            text-decoration: underline;\n        }\n        .decision-id-display {\n            font-size: 0.85em;\n            color: var(--vscode-disabledForeground);\n            margin-top: 20px;\n            padding-top: 10px;\n            border-top: 1px solid var(--vscode-editorWidget-border, var(--vscode-contrastBorder));\n            text-align: right;\n        }\n    </style>
</head>
<body>
    <div class="decision-card">
        <div class="decision-header">
             <h1 class="decision-title">${escapeHtml(decision.metadata.title)}</h1>
             <div class="meta-info">
                ${decision.metadata.pr_url ? `<span><a href="${escapeHtml(decision.metadata.pr_url)}" target="_blank">View PR</a></span>` : ''}
                ${decision.metadata.pr_merged_at ? `<span>Merged: ${new Date(decision.metadata.pr_merged_at * 1000).toLocaleDateString()}</span>` : ''}
             </div>
        </div>
        <div class="markdown-body-content">
            ${htmlContent}
        </div>
         <div class="decision-id-display">ID: ${escapeHtml(decision.id)}</div>
    </div>

    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();
        document.addEventListener('click', event => {
            const target = event.target.closest('a');
            if (target && target.dataset.action) {
                event.preventDefault();
                const action = target.dataset.action;
                const filePath = target.dataset.filepath;
                const decisionId = target.dataset.decisionId;

                if (action === 'open-file' && filePath) {
                    vscode.postMessage({
                        command: 'openFile',
                        filePath: filePath
                    });
                } else if (action === 'view-decision' && decisionId) {
                     vscode.postMessage({
                        command: 'viewDecision',
                        decisionId: decisionId
                    });
                }
            }
        });
    </script>
</body>
</html>`;
}
