import * as vscode from 'vscode';
import { UI_CONFIG } from '../config/constants';

export class StatusBarManager {
    private statusBarItem: vscode.StatusBarItem;

    constructor() {
        this.statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right, 
            UI_CONFIG.STATUS_BAR_PRIORITY
        );
        this.statusBarItem.command = 'archknow.getContextForFile';
        this.updateStatusBarItem(vscode.window.activeTextEditor);
    }

    updateStatusBarItem(editor: vscode.TextEditor | undefined): void {
        if (editor) {
            this.statusBarItem.text = `$(book) ArchKnow Context`;
            this.statusBarItem.tooltip = 'Get ArchKnow Context for Current File';
            this.statusBarItem.command = 'archknow.getContextForFile';
            this.statusBarItem.show();
        } else {
            this.statusBarItem.text = `$(book) ArchKnow Context`;
            this.statusBarItem.tooltip = 'No active file';
            this.statusBarItem.command = 'archknow.getContextForFile';
            this.statusBarItem.show();
        }
    }

    registerEventListeners(context: vscode.ExtensionContext): void {
        context.subscriptions.push(
            vscode.window.onDidChangeActiveTextEditor(editor => this.updateStatusBarItem(editor))
        );
        
        context.subscriptions.push(
            vscode.window.onDidChangeTextEditorSelection(event => this.updateStatusBarItem(event.textEditor))
        );
        
        context.subscriptions.push(
            vscode.workspace.onDidSaveTextDocument(() => {
                this.updateStatusBarItem(vscode.window.activeTextEditor);
            })
        );

        context.subscriptions.push(this.statusBarItem);
    }

    dispose(): void {
        this.statusBarItem.dispose();
    }
}