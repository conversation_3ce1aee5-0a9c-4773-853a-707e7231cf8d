const vscode = acquireVsCodeApi();
let fullTreeData = []; // To store the original tree data for filtering

window.addEventListener('message', event => {
    const message = event.data; // The JSON data our extension sent
    if (message.command === 'loadData') {
        fullTreeData = message.data;
        renderTree(fullTreeData, document.getElementById('tree-container'));
    }
});

function renderTree(nodes, parentElement, initialSearchTerm = '') {
    parentElement.innerHTML = ''; // Clear previous tree
    const ul = document.createElement('ul');
    nodes.forEach(node => {
        const li = createNodeElement(node, initialSearchTerm);
        ul.appendChild(li);
    });
    parentElement.appendChild(ul);
}

function createNodeElement(node, searchTerm = '') {
    const li = document.createElement('li');
    li.classList.add('tree-node');

    const nodeContentDiv = document.createElement('div');
    nodeContentDiv.classList.add('node-content');

    const toggle = document.createElement('span');
    toggle.classList.add('toggle');
    if (node.children && node.children.length > 0) {
        toggle.textContent = '▶'; // Collapsed by default
    } else {
        toggle.classList.add('empty'); // No children, make toggle invisible but take space
    }
    
    const nameSpan = document.createElement('span');
    nameSpan.classList.add('node-name');
    if (searchTerm && node.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        highlightSearchTerm(nameSpan, node.name, searchTerm);
    } else {
        nameSpan.textContent = node.name;
    }

    const countSpan = document.createElement('span');
    countSpan.classList.add('node-count');
    countSpan.textContent = `(${node.count})`;

    nodeContentDiv.appendChild(toggle);
    nodeContentDiv.appendChild(nameSpan);
    nodeContentDiv.appendChild(countSpan);
    li.appendChild(nodeContentDiv);

    if (node.children && node.children.length > 0) {
        const childrenUl = document.createElement('ul');
        node.children.forEach(child => {
            childrenUl.appendChild(createNodeElement(child, searchTerm));
        });
        li.appendChild(childrenUl);

        // Expand if this node or any child matches the search term
        if (searchTerm && matchesSearch(node, searchTerm.toLowerCase())) {
            li.classList.add('expanded');
            toggle.textContent = '▼';
        }
    }

    nodeContentDiv.addEventListener('click', () => {
        if (node.children && node.children.length > 0) {
            li.classList.toggle('expanded');
            toggle.textContent = li.classList.contains('expanded') ? '▼' : '▶';
        }
    });

    return li;
}

function highlightSearchTerm(element, text, term) {
    const lowerText = text.toLowerCase();
    const lowerTerm = term.toLowerCase();
    let startIndex = 0;
    while (startIndex < text.length) {
        const termIndex = lowerText.indexOf(lowerTerm, startIndex);
        if (termIndex === -1) {
            element.appendChild(document.createTextNode(text.substring(startIndex)));
            break;
        }
        // Text before match
        element.appendChild(document.createTextNode(text.substring(startIndex, termIndex)));
        // Matched text (highlighted)
        const matchSpan = document.createElement('span');
        matchSpan.classList.add('highlight');
        matchSpan.textContent = text.substring(termIndex, termIndex + term.length);
        element.appendChild(matchSpan);
        startIndex = termIndex + term.length;
    }
}

function matchesSearch(node, searchTermLower) {
    if (node.name.toLowerCase().includes(searchTermLower)) {
        return true;
    }
    if (node.children) {
        return node.children.some(child => matchesSearch(child, searchTermLower));
    }
    return false;
}

// Search/Filter functionality
const searchBox = document.getElementById('search-box');
searchBox.addEventListener('input', (e) => {
    const searchTerm = e.target.value;
    if (!searchTerm || searchTerm.trim() === '') {
        renderTree(fullTreeData, document.getElementById('tree-container'));
    } else {
        const filteredData = filterTreeData(fullTreeData, searchTerm.toLowerCase());
        renderTree(filteredData, document.getElementById('tree-container'), searchTerm);
         // Automatically expand nodes that contain search results after filtering
        document.querySelectorAll('.tree-node').forEach(liNode => {
            const nodeNameSpan = liNode.querySelector('.node-name');
            const nodeText = nodeNameSpan ? nodeNameSpan.textContent.toLowerCase() : '';
            let hasMatchingChild = false;
            const childLis = liNode.querySelectorAll(':scope > ul > .tree-node'); // Direct children li
            childLis.forEach(childLi => {
                if (childLi.style.display !== 'none') { // if child is visible after filtering
                     // Check if child itself or any of its descendants match
                    if (nodeContainsSearchTermRecursive(childLi, searchTerm.toLowerCase())) {
                        hasMatchingChild = true;
                    }
                }
            });

            if (nodeText.includes(searchTerm.toLowerCase()) || hasMatchingChild) {
                // Expand if the node itself matches or if any of its visible children match
                if (liNode.querySelector(':scope > .node-content > .toggle:not(.empty)')) {
                     if (!liNode.classList.contains('expanded')) {
                        liNode.classList.add('expanded');
                        liNode.querySelector(':scope > .node-content > .toggle').textContent = '▼';
                    }
                }
            }
        });
    }
});

function nodeContainsSearchTermRecursive(liElement, searchTermLower) {
    // Check the current node's name
    const nameSpan = liElement.querySelector(':scope > .node-content > .node-name');
    if (nameSpan && nameSpan.textContent.toLowerCase().includes(searchTermLower)) {
        return true;
    }
    // Check children recursively
    const childrenUl = liElement.querySelector(':scope > ul');
    if (childrenUl) {
        const childLiElements = childrenUl.querySelectorAll(':scope > .tree-node');
        for (const childLi of childLiElements) {
            if (nodeContainsSearchTermRecursive(childLi, searchTermLower)) {
                return true;
            }
        }
    }
    return false;
}

function filterTreeData(nodes, searchTermLower) {
    return nodes.map(node => {
        let children = [];
        if (node.children && node.children.length > 0) {
            children = filterTreeData(node.children, searchTermLower);
        }

        if (node.name.toLowerCase().includes(searchTermLower) || children.length > 0) {
            return { ...node, children: children }; 
        }
        return null; 
    }).filter(node => node !== null);
}

// Initial load request
vscode.postMessage({ command: 'requestInitialData' }); 