<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Concepts Tree</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-editor-foreground);
            background-color: var(--vscode-editor-background);
            padding: 10px;
        }
        ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .tree-node {
            cursor: pointer;
        }
        .tree-node .node-content {
            display: flex;
            align-items: center;
        }
        .tree-node .toggle {
            margin-right: 8px;
            width: 16px; /* Fixed width for alignment */
            display: inline-block; /* For alignment */
            text-align: center;
        }
        .tree-node .toggle.empty {
            /* Make empty toggles take up space but be invisible */
            visibility: hidden; 
        }
        .tree-node ul {
            display: none; /* Hidden by default */
        }
        .tree-node.expanded > ul {
            display: block;
        }
        .node-name {
            flex-grow: 1;
        }
        .node-count {
            margin-left: 10px;
            font-size: 0.9em;
            color: var(--vscode-descriptionForeground);
        }
         /* Style for the search box */
        #search-box {
            width: 95%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid var(--vscode-input-border, #ccc);
            background-color: var(--vscode-input-background, #fff);
            color: var(--vscode-input-foreground, #000);
            border-radius: 3px;
        }
        #search-box:focus {
            outline: 1px solid var(--vscode-focusBorder);
        }
        .highlight {
            background-color: var(--vscode-list-activeSelectionBackground);
            color: var(--vscode-list-activeSelectionForeground);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <input type="text" id="search-box" placeholder="Search concepts...">
    <div id="tree-container"></div>
    <script src="./domainConceptsTree.js"></script>
</body>
</html> 