import * as vscode from 'vscode';
import * as path from 'path';
import { UI_CONFIG } from '../config/constants';

export interface WebviewPanels {
    decisionsPanel?: vscode.WebviewPanel;
    singleDecisionPanel?: vscode.WebviewPanel;
    promptGeneratorPanel?: vscode.WebviewPanel;
    feedbackPanel?: vscode.WebviewPanel;
    designDocWorkflowPanel?: vscode.WebviewPanel;
    domainConceptsTreePanel?: vscode.WebviewPanel;
}

export class WebviewManager {
    private panels: WebviewPanels = {};
    private designDocJobsStatusPanel: vscode.WebviewPanel | undefined;

    constructor(private context: vscode.ExtensionContext) {}

    createOrShowDecisionsPanel(htmlContent: string, title: string): vscode.WebviewPanel {
        const column = vscode.window.activeTextEditor
            ? vscode.ViewColumn.Beside
            : vscode.ViewColumn.One;

        if (this.panels.decisionsPanel) {
            this.panels.decisionsPanel.title = title;
            this.panels.decisionsPanel.reveal(column);
            this.panels.decisionsPanel.webview.html = htmlContent;
        } else {
            this.panels.decisionsPanel = vscode.window.createWebviewPanel(
                'archknowDecisions',
                title,
                column,
                {
                    enableScripts: true,
                    localResourceRoots: [],
                    retainContextWhenHidden: UI_CONFIG.WEBVIEW_RETAIN_CONTEXT
                }
            );
            
            this.panels.decisionsPanel.webview.html = htmlContent;
            
            this.panels.decisionsPanel.onDidDispose(
                () => {
                    this.panels.decisionsPanel = undefined;
                },
                null,
                this.context.subscriptions
            );
        }

        return this.panels.decisionsPanel;
    }

    createOrShowSingleDecisionPanel(htmlContent: string, title: string): vscode.WebviewPanel {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : vscode.ViewColumn.Beside;

        if (this.panels.singleDecisionPanel) {
            this.panels.singleDecisionPanel.title = title;
            this.panels.singleDecisionPanel.webview.html = htmlContent;
            this.panels.singleDecisionPanel.reveal(column);
        } else {
            this.panels.singleDecisionPanel = vscode.window.createWebviewPanel(
                'archknowSingleDecision',
                title,
                column || vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    retainContextWhenHidden: UI_CONFIG.WEBVIEW_RETAIN_CONTEXT,
                    localResourceRoots: [vscode.Uri.joinPath(this.context.extensionUri, 'media')]
                }
            );
            
            this.panels.singleDecisionPanel.webview.html = htmlContent;
            
            this.panels.singleDecisionPanel.onDidDispose(
                () => { this.panels.singleDecisionPanel = undefined; },
                null,
                this.context.subscriptions
            );
        }

        return this.panels.singleDecisionPanel;
    }

    createOrShowPromptGeneratorPanel(htmlContent: string): vscode.WebviewPanel {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (this.panels.promptGeneratorPanel) {
            this.panels.promptGeneratorPanel.reveal(column);
            // Update the HTML content if provided
            if (htmlContent) {
                this.panels.promptGeneratorPanel.webview.html = htmlContent;
            }
            return this.panels.promptGeneratorPanel;
        }

        this.panels.promptGeneratorPanel = vscode.window.createWebviewPanel(
            'archknowPromptGenerator',
            'ArchKnow Context Generator',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: UI_CONFIG.WEBVIEW_RETAIN_CONTEXT,
            }
        );

        if (htmlContent) {
            this.panels.promptGeneratorPanel.webview.html = htmlContent;
        }

        this.panels.promptGeneratorPanel.onDidDispose(
            () => {
                this.panels.promptGeneratorPanel = undefined;
            },
            null,
            this.context.subscriptions
        );

        return this.panels.promptGeneratorPanel;
    }

    createOrShowFeedbackPanel(htmlContent: string, title: string): vscode.WebviewPanel {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (this.panels.feedbackPanel && !this.panels.feedbackPanel.visible) {
            this.panels.feedbackPanel = undefined;
        }

        if (this.panels.feedbackPanel) {
            this.panels.feedbackPanel.title = title;
            this.panels.feedbackPanel.reveal(column);
            this.panels.feedbackPanel.webview.html = htmlContent;
        } else {
            this.panels.feedbackPanel = vscode.window.createWebviewPanel(
                'archknowFeedback',
                title,
                column || vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    localResourceRoots: []
                }
            );
            
            this.panels.feedbackPanel.webview.html = htmlContent;
            
            this.panels.feedbackPanel.onDidDispose(
                () => { this.panels.feedbackPanel = undefined; },
                null,
                this.context.subscriptions
            );
        }

        return this.panels.feedbackPanel;
    }

    createOrShowStreamlinedFeedbackPanel(
        feedbackData: {
            scope_status: 'COMPLIANT' | 'MAJOR_DEVIATION';
            scope_assessment?: {
                deliverables_status?: Array<{
                    deliverable: string;
                    status: 'IMPLEMENTED' | 'PARTIAL' | 'MISSING' | 'NOT_APPLICABLE';
                    evidence: string;
                }>;
                additional_scope?: string;
                scope_gap?: string;
                data_model_analysis?: Array<{
                    change_description: string;
                    change_type: "FEATURE_CREEP" | "REGRESSION" | "UNEXPECTED_CHANGE";
                    file: string;
                    reasoning: string;
                    id?: string;
                    status?: 'OPEN' | 'ACCEPTED';
                }>;
                significant_regressions?: Array<{
                    regression_description: string;
                    file: string;
                    impact: string;
                    id?: string;
                    status?: 'OPEN' | 'ACCEPTED';
                }>;
            };
            critical_security_risks: Array<{
                risk: string;
                severity: 'CRITICAL';
                impact: string;
                fix: string;
                id?: string;
                status?: 'OPEN' | 'ACCEPTED';
            }>;
            critical_performance_risks: Array<{
                risk: string;
                severity: 'CRITICAL';
                impact: string;
                file: string;
                fix: string;
                id?: string;
                status?: 'OPEN' | 'ACCEPTED';
            }>;
            critical_ux_risks: Array<{
                risk: string;
                severity: 'CRITICAL';
                impact: string;
                fix: string;
                id?: string;
                status?: 'OPEN' | 'ACCEPTED';
            }>;
            scope_deviations: Array<{
                id: string;
                deviation: string;
                impact: string;
                action: string;
                status: 'NEEDS_ACTION' | 'VERIFIED';
            }>;
            integration_blocked: boolean;
            summary: string;
            pr_summary?: string;
            pr_ready_summary?: {
                title: string;
                summary: string;
                deliverables_completed: string[];
                scope_status: string;
            };
            task_context?: {
                task_title: string;
                task_description: string;
                design_id: string;
            };
            milestone_context?: {
                milestone_id: string;
                title: string;
                description: string;
                priority: string;
                planned_deliverables: string[];
                verification_criteria: string[];
            };
            metadata?: {
                total_lines_changed: number;
                files_modified: number;
                integration_ready: boolean;
            };
            issue_url?: string;
            review_metadata?: {
                security_posture?: {
                    level: string;
                    rationale: string;
                };
                review_iteration?: number;
                review_mode?: string;
            };
        },
        title: string,
        jobId?: string,
        milestoneId?: string
    ): vscode.WebviewPanel {
        const column = vscode.window.activeTextEditor?.viewColumn || vscode.ViewColumn.One;

        // Augment risks with id and status if they don't have them
        const allRiskTypes = ['critical_security_risks', 'critical_performance_risks', 'critical_ux_risks'];
        allRiskTypes.forEach(riskType => {
            if ((feedbackData as any)[riskType]) {
                (feedbackData as any)[riskType].forEach((risk: any, index: number) => {
                    if (!risk.id) {
                        const riskString = JSON.stringify(risk);
                        let hash = 0;
                        for (let i = 0; i < riskString.length; i++) {
                            const char = riskString.charCodeAt(i);
                            hash = ((hash << 5) - hash) + char;
                            hash = hash | 0; // Convert to 32bit integer
                        }
                        risk.id = `${riskType}-${index}-${hash}`;
                    }
                    if (!risk.status) {
                        risk.status = 'OPEN'; // Default status
                    }
                });
            }
        });

        // Augment data model analysis and regressions with id and status
        if (feedbackData.scope_assessment?.data_model_analysis) {
            feedbackData.scope_assessment.data_model_analysis.forEach((item: any, index: number) => {
                if (!item.id) {
                    const itemString = JSON.stringify(item);
                    let hash = 0;
                    for (let i = 0; i < itemString.length; i++) {
                        const char = itemString.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash | 0;
                    }
                    item.id = `data-model-${index}-${hash}`;
                }
                if (!item.status) {
                    item.status = 'OPEN';
                }
            });
        }

        if (feedbackData.scope_assessment?.significant_regressions) {
            feedbackData.scope_assessment.significant_regressions.forEach((item: any, index: number) => {
                if (!item.id) {
                    const itemString = JSON.stringify(item);
                    let hash = 0;
                    for (let i = 0; i < itemString.length; i++) {
                        const char = itemString.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash | 0;
                    }
                    item.id = `regression-${index}-${hash}`;
                }
                if (!item.status) {
                    item.status = 'OPEN';
                }
            });
        }

        // Generate HTML for streamlined feedback
        const htmlContent = this.generateStreamlinedFeedbackHtml(feedbackData, jobId, milestoneId);

        if (this.panels.feedbackPanel) {
            this.panels.feedbackPanel.reveal(column);
            this.panels.feedbackPanel.webview.html = this.generateStreamlinedFeedbackHtml(feedbackData, jobId, milestoneId);
        } else {
            this.panels.feedbackPanel = vscode.window.createWebviewPanel(
                'streamlinedFeedback',
                title,
                column,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true
                }
            );

            this.panels.feedbackPanel.webview.html = this.generateStreamlinedFeedbackHtml(feedbackData, jobId, milestoneId);

            // Handle messages from the webview
            this.panels.feedbackPanel.webview.onDidReceiveMessage(
                async (message) => {
                    console.log('Received message from webview:', message);
                    switch (message.type) {
                        case 'generatePR':
                            await this.handleGeneratePR(feedbackData, jobId, milestoneId);
                            break;
                        case 'copyFeedback':
                            if (message.payload) {
                                await vscode.env.clipboard.writeText(message.payload);
                                vscode.window.showInformationMessage('Filtered feedback JSON copied to clipboard!');
                            }
                            break;
                        case 'showInformationMessage':
                            if (message.text) {
                                vscode.window.showInformationMessage(message.text);
                            }
                            break;
                        case 'markRiskAsAccepted':
                            console.log('Processing markRiskAsAccepted for riskId:', message.riskId);
                            if (message.riskId) {
                                let riskFound = false;
                                for (const riskType of allRiskTypes) {
                                    if ((feedbackData as any)[riskType]) {
                                        const risk = (feedbackData as any)[riskType].find((r: any) => r.id === message.riskId);
                                        if (risk) {
                                            risk.status = 'ACCEPTED';
                                            riskFound = true;
                                            break;
                                        }
                                    }
                                }

                                // Check data model analysis and regressions
                                if (!riskFound && feedbackData.scope_assessment?.data_model_analysis) {
                                    const item = feedbackData.scope_assessment.data_model_analysis.find((r: any) => r.id === message.riskId);
                                    if (item) {
                                        item.status = 'ACCEPTED';
                                        riskFound = true;
                                    }
                                }

                                if (!riskFound && feedbackData.scope_assessment?.significant_regressions) {
                                    const item = feedbackData.scope_assessment.significant_regressions.find((r: any) => r.id === message.riskId);
                                    if (item) {
                                        item.status = 'ACCEPTED';
                                        riskFound = true;
                                    }
                                }

                                if (riskFound) {
                                    console.log('Risk found and marked as accepted, re-evaluating integration status');
                                    // Re-evaluate integration_blocked status
                                    const hasOpenRisks = allRiskTypes.some(riskType =>
                                        (feedbackData as any)[riskType]?.some((r: any) => r.status === 'OPEN')
                                    );
                                    const hasOpenDataModelIssues = feedbackData.scope_assessment?.data_model_analysis?.some((r: any) => r.status === 'OPEN') || false;
                                    const hasOpenRegressions = feedbackData.scope_assessment?.significant_regressions?.some((r: any) => r.status === 'OPEN') || false;
                                    const hasUnverifiedDeviations = feedbackData.scope_deviations.some((d: any) => d.status === 'NEEDS_ACTION');
                                    const isScopeNonCompliant = feedbackData.scope_status === 'MAJOR_DEVIATION' && hasUnverifiedDeviations;
                                    
                                    feedbackData.integration_blocked = hasUnverifiedDeviations || hasOpenRisks || hasOpenDataModelIssues || hasOpenRegressions || isScopeNonCompliant;
                                    console.log('New integration_blocked status:', feedbackData.integration_blocked);

                                    // Re-render the webview with updated state
                                    if (this.panels.feedbackPanel) {
                                        this.panels.feedbackPanel.webview.html = this.generateStreamlinedFeedbackHtml(feedbackData, jobId, milestoneId);
                                    }
                                } else {
                                    console.log('Risk not found with ID:', message.riskId);
                                }
                            }
                            break;
                        case 'markDeviationAsVerified':
                            if (message.deviationId) {
                                const deviation = feedbackData.scope_deviations.find(d => d.id === message.deviationId);
                                if (deviation) {
                                    deviation.status = 'VERIFIED';
                                }
                                // Re-evaluate integration_blocked status
                                const hasUnverifiedDeviations = feedbackData.scope_deviations.some(d => d.status === 'NEEDS_ACTION');

                                if (!hasUnverifiedDeviations) {
                                    feedbackData.scope_status = 'COMPLIANT';
                                }
                                
                                const hasOpenRisks = allRiskTypes.some(riskType =>
                                    (feedbackData as any)[riskType]?.some((r: any) => r.status === 'OPEN')
                                );
                                const hasRisks = (feedbackData.critical_security_risks.length > 0 || feedbackData.critical_performance_risks.length > 0 || feedbackData.critical_ux_risks.length > 0);
                                feedbackData.integration_blocked = hasUnverifiedDeviations || hasOpenRisks;
                                const hasOpenDataModelIssues = feedbackData.scope_assessment?.data_model_analysis?.some((r: any) => r.status === 'OPEN') || false;
                                const hasOpenRegressions = feedbackData.scope_assessment?.significant_regressions?.some((r: any) => r.status === 'OPEN') || false;
                                const isScopeNonCompliant = feedbackData.scope_status === 'MAJOR_DEVIATION' && hasUnverifiedDeviations;
                                
                                feedbackData.integration_blocked = hasUnverifiedDeviations || hasOpenRisks || hasOpenDataModelIssues || hasOpenRegressions || isScopeNonCompliant;

                                // Re-render the webview with updated state
                                if (this.panels.feedbackPanel) {
                                    this.panels.feedbackPanel.webview.html = this.generateStreamlinedFeedbackHtml(feedbackData, jobId, milestoneId);
                                }
                            }
                            break;
                    }
                },
                undefined,
                this.context.subscriptions
            );

            this.panels.feedbackPanel.onDidDispose(() => {
                this.panels.feedbackPanel = undefined;
            });
        }

        return this.panels.feedbackPanel;
    }

    private generateStreamlinedFeedbackHtml(feedbackData: any, jobId?: string, milestoneId?: string): string {
        const getScopeStatusColor = (status: string) => {
            switch (status) {
                case 'COMPLIANT': return '#28a745';
                case 'MAJOR_DEVIATION': return '#dc3545';
                default: return '#6c757d';
            }
        };

        const getStatusColor = (status: string) => {
            switch (status) {
                case 'IMPLEMENTED': return '#28a745';
                case 'PARTIAL': return '#ffc107';
                case 'MISSING': return '#dc3545';
                case 'NOT_APPLICABLE': return '#6c757d';
                default: return '#6c757d';
            }
        };

        const getStatusIcon = (status: string) => {
            switch (status) {
                case 'IMPLEMENTED': return '✅';
                case 'PARTIAL': return '⚠️';
                case 'MISSING': return '❌';
                case 'NOT_APPLICABLE': return '➖';
                default: return '❓';
            }
        };

        // Filter risks
        const openSecurityRisks = (feedbackData.critical_security_risks || []).filter((r: any) => r.status !== 'ACCEPTED');
        const acceptedSecurityRisks = (feedbackData.critical_security_risks || []).filter((r: any) => r.status === 'ACCEPTED');
        const openPerformanceRisks = (feedbackData.critical_performance_risks || []).filter((r: any) => r.status !== 'ACCEPTED');
        const acceptedPerformanceRisks = (feedbackData.critical_performance_risks || []).filter((r: any) => r.status === 'ACCEPTED');
        const openUxRisks = (feedbackData.critical_ux_risks || []).filter((r: any) => r.status !== 'ACCEPTED');
        const acceptedUxRisks = (feedbackData.critical_ux_risks || []).filter((r: any) => r.status === 'ACCEPTED');
        const allAcceptedRisks = [...acceptedSecurityRisks, ...acceptedPerformanceRisks, ...acceptedUxRisks];

        // Filter data model analysis and regressions
        const openDataModelIssues = (feedbackData.scope_assessment?.data_model_analysis || []).filter((r: any) => r.status !== 'ACCEPTED');
        const acceptedDataModelIssues = (feedbackData.scope_assessment?.data_model_analysis || []).filter((r: any) => r.status === 'ACCEPTED');
        const openRegressions = (feedbackData.scope_assessment?.significant_regressions || []).filter((r: any) => r.status !== 'ACCEPTED');
        const acceptedRegressions = (feedbackData.scope_assessment?.significant_regressions || []).filter((r: any) => r.status === 'ACCEPTED');
        const allAcceptedItems = [...allAcceptedRisks, ...acceptedDataModelIssues, ...acceptedRegressions];

        // Extract milestone context if available
        const milestoneContext = feedbackData.milestone_context;
        const taskContext = feedbackData.task_context;
        const taskTitle = taskContext?.task_title;
        const milestoneTitle = milestoneContext?.title;
        const milestoneDescription = milestoneContext?.description || 'Comprehensive assessment of implementation progress';
        const milestoneDisplayId = milestoneContext?.milestone_id || milestoneId;
        
        // Create a comprehensive title that shows both task and milestone when different
        let displayTitle;
        if (taskTitle && milestoneTitle && taskTitle !== milestoneTitle) {
            displayTitle = `${taskTitle} - ${milestoneTitle}`;
        } else {
            displayTitle = taskTitle || milestoneTitle;
        }

        return `
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                line-height: 1.6;
                color: #2c3e50;
                background-color: #f8f9fa;
                margin: 0;
                padding: 20px;
            }
            
            .milestone-feedback-container {
                max-width: 1000px;
                margin: 0 auto;
                background: #ffffff;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                overflow: hidden;
            }
            
            .milestone-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }
            
            .milestone-title {
                font-size: 28px;
                font-weight: 700;
                margin: 0 0 10px 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
            
            .milestone-subtitle {
                font-size: 16px;
                opacity: 0.9;
                margin: 0;
                font-weight: 400;
            }

            .milestone-task-title {
                font-size: 18px;
                font-weight: 500;
                margin: 8px 0 0 0;
                opacity: 0.9;
            }
            
            .status-banner {
                padding: 25px 30px;
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 15px;
                border-bottom: 1px solid #e9ecef;
            }
            
            .status-banner.compliant {
                background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                color: #155724;
                border-left: 5px solid #28a745;
            }
            
            .status-banner.major-deviation {
                background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
                color: #721c24;
                border-left: 5px solid #dc3545;
            }
            
            .status-icon {
                font-size: 24px;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            }
            
            .content-section {
                padding: 30px;
                border-bottom: 1px solid #e9ecef;
            }
            
            .content-section:last-child {
                border-bottom: none;
            }
            
            .section-title {
                font-size: 22px;
                font-weight: 700;
                color: #2c3e50;
                margin: 0 0 20px 0;
                padding-bottom: 10px;
                border-bottom: 2px solid #e9ecef;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .section-icon {
                font-size: 20px;
            }
            
            .assessment-text {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 20px;
                margin: 15px 0;
                font-size: 15px;
                line-height: 1.7;
                color: #495057;
            }
            
            .deliverables-grid {
                display: grid;
                gap: 20px;
                margin-top: 20px;
            }
            
            .deliverable-card {
                background: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }
            
            .deliverable-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            }
            
            .deliverable-header {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                margin-bottom: 15px;
            }
            
            .deliverable-status {
                padding: 6px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                color: white;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                gap: 5px;
            }
            
            .deliverable-title {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
                margin: 0;
                flex: 1;
                line-height: 1.4;
            }
            
            .deliverable-evidence {
                background: #f1f3f4;
                border: 1px solid #dadce0;
                border-radius: 6px;
                padding: 12px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 13px;
                color: #5f6368;
                margin-top: 10px;
                word-break: break-all;
            }
            
            .risk-card {
                background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
                border: 1px solid #feb2b2;
                border-left: 4px solid #e53e3e;
                border-radius: 8px;
                padding: 20px;
                margin: 15px 0;
            }
            
            .risk-header {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 12px;
            }
            
            .risk-severity {
                background: #e53e3e;
                color: white;
                padding: 4px 10px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            
            .risk-title {
                font-size: 16px;
                font-weight: 600;
                color: #742a2a;
                margin: 0;
            }
            
            .risk-description {
                color: #742a2a;
                margin: 8px 0;
                line-height: 1.6;
            }
            
            .risk-fix {
                background: rgba(255, 255, 255, 0.7);
                border-radius: 6px;
                padding: 12px;
                margin-top: 12px;
                font-weight: 500;
                color: #2d3748;
            }
            
            .risk-fix::before {
                content: "🔧 Fix: ";
                font-weight: 700;
            }
            
            .risk-card.accepted-risk {
                background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
                border-color: #9ae6b4;
                border-left-color: #38a169;
            }

            .risk-card.accepted-risk .risk-title,
            .risk-card.accepted-risk .risk-description {
                color: #22543d;
            }
            
            .deviation-card {
                background: linear-gradient(135deg, #fffaf0 0%, #feebc8 100%);
                border: 1px solid #f6ad55;
                border-left: 4px solid #ed8936;
                border-radius: 8px;
                padding: 20px;
                margin: 15px 0;
                position: relative;
            }

            .deviation-card.verified {
                background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
                border-color: #9ae6b4;
                border-left-color: #38a169;
            }
            
            .deviation-title {
                font-size: 16px;
                font-weight: 600;
                color: #744210;
                margin: 0 0 8px 0;
            }
            
            .deviation-impact {
                color: #744210;
                margin: 8px 0;
                line-height: 1.6;
            }
            
            .deviation-action {
                background: rgba(255, 255, 255, 0.7);
                border-radius: 6px;
                padding: 12px;
                margin-top: 12px;
                font-weight: 500;
                color: #2d3748;
            }
            
            .deviation-action::before {
                content: "⚡ Action: ";
                font-weight: 700;
            }
            
            .summary-section {
                background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
                border-radius: 10px;
                padding: 25px;
                margin: 20px 0;
                border: 1px solid #e2e8f0;
            }
            
            .summary-text {
                font-size: 16px;
                line-height: 1.7;
                color: #2d3748;
                margin: 0;
                font-weight: 500;
            }
            
            .integration-status {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 20px;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                margin: 20px 0;
            }
            
            .integration-ready {
                background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
                color: #22543d;
                border: 1px solid #9ae6b4;
            }
            
            .integration-blocked {
                background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
                color: #742a2a;
                border: 1px solid #feb2b2;
            }
            
            .generate-pr-button {
                background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                margin-top: 15px;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
            }
            
            .generate-pr-button:hover {
                background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
            }
            
            .generate-pr-button:active {
                transform: translateY(0);
            }
            
            .copy-feedback-button {
                background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                margin-top: 15px;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
            }

            .copy-feedback-button:hover {
                background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(237, 137, 54, 0.4);
            }
            
            .accept-risk-button {
                background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 600;
                cursor: pointer;
                margin-top: 12px;
                transition: all 0.2s ease;
                box-shadow: 0 2px 6px rgba(56, 161, 105, 0.3);
            }

            .accept-risk-button:hover {
                background: linear-gradient(135deg, #2f855a 0%, #276749 100%);
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(56, 161, 105, 0.4);
            }

            .accept-risk-button:active {
                transform: translateY(0);
            }
            
            .verify-button {
                background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 600;
                cursor: pointer;
                margin-top: 12px;
                transition: all 0.2s ease;
                box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
            }

            .verify-button:hover {
                background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(66, 153, 225, 0.4);
            }

            .verify-button:active {
                transform: translateY(0);
            }
            
            .no-items {
                text-align: center;
                color: #718096;
                font-style: italic;
                padding: 30px;
                background: #f7fafc;
                border-radius: 8px;
                border: 1px dashed #cbd5e0;
            }
            
            .design-doc-section {
                background: linear-gradient(135deg, #e8f4f8 0%, #d6edf5 100%);
                border-radius: 8px;
                padding: 20px;
                border: 1px solid #bee3f8;
            }
            
            .design-doc-text {
                color: #2c3e50;
                margin: 0 0 15px 0;
                font-size: 16px;
                font-weight: 500;
            }
            
            .design-doc-link {
                display: inline-block;
                background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
                color: white;
                text-decoration: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 16px;
                transition: all 0.2s ease;
                box-shadow: 0 2px 6px rgba(66, 153, 225, 0.3);
                margin: 10px 0;
            }
            
            .design-doc-link:hover {
                background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
                text-decoration: none;
                color: white;
            }
            
            .design-doc-link:active {
                transform: translateY(0);
            }
            
            .design-doc-description {
                color: #4a5568;
                font-size: 14px;
                margin: 10px 0 0 0;
                font-style: italic;
                line-height: 1.5;
            }
            
            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }
                
                .milestone-header {
                    padding: 20px;
                }
                
                .milestone-title {
                    font-size: 24px;
                }
                
                .content-section {
                    padding: 20px;
                }
                
                .section-title {
                    font-size: 20px;
                }
            }
        </style>
        
        <div class="milestone-feedback-container">
            <div class="milestone-header">
                <h1 class="milestone-title">${taskTitle || milestoneTitle || 'Milestone Review Feedback'}</h1>
                ${taskTitle && milestoneTitle && taskTitle !== milestoneTitle ? `<p class="milestone-task-title">${milestoneTitle}</p>` : ''}
                <p class="milestone-subtitle">${milestoneDescription}</p>
            </div>
            
            <div class="status-banner ${!feedbackData.integration_blocked ? 'compliant' : 'major-deviation'}">
                <span class="status-icon">${!feedbackData.integration_blocked ? '✅' : '⚠️'}</span>
                <span>Integration: ${!feedbackData.integration_blocked ? 'READY' : 'NEEDS ATTENTION'}</span>
            </div>
            
            ${taskContext || milestoneDisplayId ? `
            <div style="padding: 20px 30px; background: #f8f9fa; border-bottom: 1px solid #e9ecef;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; font-size: 14px;">
                    ${taskContext?.design_id ? `
                    <div>
                        <strong style="color: #495057;">Design ID:</strong><br>
                        <code style="background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-size: 12px;">${taskContext.design_id}</code>
                    </div>
                    ` : ''}
                    ${taskTitle ? `
                    <div>
                        <strong style="color: #495057;">Task:</strong><br>
                        <span style="color: #2c3e50;">${taskTitle}</span>
                    </div>
                    ` : ''}
                    ${milestoneTitle ? `
                    <div>
                        <strong style="color: #495057;">Milestone:</strong><br>
                        <span style="color: #2c3e50;">${milestoneTitle}</span>
                    </div>
                    ` : ''}
                    ${milestoneDisplayId ? `
                    <div>
                        <strong style="color: #495057;">Milestone ID:</strong><br>
                        <code style="background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-size: 12px;">${milestoneDisplayId}</code>
                    </div>
                    ` : ''}
                    ${feedbackData.review_metadata?.security_posture ? `
                    <div>
                        <strong style="color: #495057;">Security Review Level:</strong><br>
                        <span style="color: #2c3e50; font-weight: 600;">${feedbackData.review_metadata.security_posture.level}</span>
                        <div style="font-size: 11px; color: #6c757d; margin-top: 4px; line-height: 1.3;">
                            ${feedbackData.review_metadata.security_posture.rationale}
                        </div>
                    </div>
                    ` : ''}
                    ${feedbackData.review_metadata?.review_iteration && feedbackData.review_metadata.review_iteration > 1 ? `
                    <div>
                        <strong style="color: #495057;">Review Iteration:</strong><br>
                        <span style="color: #2c3e50;">${feedbackData.review_metadata.review_iteration} (${feedbackData.review_metadata.review_mode})</span>
                    </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}
            
            ${feedbackData.scope_assessment ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">📋</span>
                    Scope Assessment
                </h2>               
                
                ${feedbackData.scope_assessment.deliverables_status && feedbackData.scope_assessment.deliverables_status.length > 0 ? `
                <h3 style="margin: 25px 0 15px 0; color: #2c3e50; font-size: 18px;">📦 Deliverables Status</h3>
                <div class="deliverables-grid">
                    ${feedbackData.scope_assessment.deliverables_status.map((deliverable: any) => `
                    <div class="deliverable-card">
                        <div class="deliverable-header">
                            <div class="deliverable-status" style="background-color: ${getStatusColor(deliverable.status)};">
                                ${getStatusIcon(deliverable.status)} ${deliverable.status}
                            </div>
                        </div>
                        <h4 class="deliverable-title">${deliverable.deliverable}</h4>
                        ${deliverable.evidence ? `
                        <div class="deliverable-evidence">
                            <strong>Evidence:</strong> ${deliverable.evidence}
                        </div>
                        ` : ''}
                    </div>
                    `).join('')}
                </div>
                ` : ''}
                
                ${feedbackData.scope_assessment.additional_scope ? `
                <h3 style="margin: 25px 0 15px 0; color: #2c3e50; font-size: 18px;">➕ Additional Scope</h3>
                <div class="assessment-text">
                    ${feedbackData.scope_assessment.additional_scope}
                </div>
                ` : ''}
                
                ${feedbackData.scope_assessment.scope_gap ? `
                <h3 style="margin: 25px 0 15px 0; color: #2c3e50; font-size: 18px;">🔍 Scope Gap Analysis</h3>
                <div class="assessment-text">
                    ${feedbackData.scope_assessment.scope_gap}
                </div>
                ` : ''}
            </div>
            ` : ''}

            ${openDataModelIssues.length > 0 ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">💾</span>
                    Data Model Analysis
                </h2>
                ${openDataModelIssues.map((change: any) => `
                <div class="deviation-card">
                    <h3 class="deviation-title">${change.change_type.replace(/_/g, ' ')}</h3>
                    <div class="deviation-impact"><strong>Change:</strong> ${change.change_description}</div>
                    <div class="deviation-impact"><strong>File:</strong> <code>${change.file}</code></div>
                    <div class="deviation-impact"><strong>Reasoning:</strong> ${change.reasoning}</div>
                    <button class="accept-risk-button" onclick="markRiskAsAccepted('${change.id}')">
                        Accept Risk
                    </button>
                </div>
                `).join('')}
            </div>
            ` : ''}

            ${openRegressions.length > 0 ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">📉</span>
                    Significant Regressions
                </h2>
                ${openRegressions.map((regression: any) => `
                <div class="risk-card">
                    <h3 class="risk-title">Regression Detected</h3>
                    <div class="risk-description">${regression.regression_description}</div>
                    <div class="risk-description"><strong>File:</strong> <code>${regression.file}</code></div>
                    <div class="risk-description"><strong>Impact:</strong> ${regression.impact}</div>
                    <button class="accept-risk-button" onclick="markRiskAsAccepted('${regression.id}')">
                        Accept Risk
                    </button>
                </div>
                `).join('')}
            </div>
            ` : ''}
            
            ${openSecurityRisks.length > 0 ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">🔒</span>
                    Critical Security Risks
                </h2>
                ${openSecurityRisks.map((risk: any) => `
                <div class="risk-card">
                    <div class="risk-header">
                        <span class="risk-severity">${risk.severity}</span>
                        <h3 class="risk-title">Security Risk</h3>
                    </div>
                    <div class="risk-description">${risk.risk}</div>
                    <div class="risk-description"><strong>Impact:</strong> ${risk.impact}</div>
                    ${risk.file ? `<div class="risk-description"><strong>File:</strong> <code>${risk.file}</code></div>` : ''}
                    <div class="risk-fix">${risk.fix}</div>
                    <button class="accept-risk-button" onclick="markRiskAsAccepted('${risk.id}')">
                        Accept Risk
                    </button>
                </div>
                `).join('')}
            </div>
            ` : ''}
            
            ${openPerformanceRisks.length > 0 ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">⚡️</span>
                    Critical Performance Risks
                </h2>
                ${openPerformanceRisks.map((risk: any) => `
                <div class="risk-card">
                    <div class="risk-header">
                        <span class="risk-severity">${risk.severity}</span>
                        <h3 class="risk-title">Performance Risk</h3>
                    </div>
                    <div class="risk-description">${risk.risk}</div>
                    <div class="risk-description"><strong>Impact:</strong> ${risk.impact}</div>
                    ${risk.file ? `<div class="risk-description"><strong>File:</strong> <code>${risk.file}</code></div>` : ''}
                    <div class="risk-fix">${risk.fix}</div>
                    <button class="accept-risk-button" onclick="markRiskAsAccepted('${risk.id}')">
                        Accept Risk
                    </button>
                </div>
                `).join('')}
            </div>
            ` : ''}
            
            ${openUxRisks.length > 0 ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">👤</span>
                    Critical UX Risks
                </h2>
                ${openUxRisks.map((risk: any) => `
                <div class="risk-card">
                    <div class="risk-header">
                        <span class="risk-severity">${risk.severity}</span>
                        <h3 class="risk-title">UX Risk</h3>
                    </div>
                    <div class="risk-description">${risk.risk}</div>
                    <div class="risk-description"><strong>Impact:</strong> ${risk.impact}</div>
                    <div class="risk-fix">${risk.fix}</div>
                     <button class="accept-risk-button" onclick="markRiskAsAccepted('${risk.id}')">
                        Accept Risk
                    </button>
                </div>
                `).join('')}
            </div>
            ` : ''}
            
            ${feedbackData.scope_deviations && feedbackData.scope_deviations.length > 0 ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">📐</span>
                    Scope Deviations
                </h2>
                ${feedbackData.scope_deviations.map((deviation: any) => `
                <div class="deviation-card ${deviation.status === 'VERIFIED' ? 'verified' : ''}">
                    ${deviation.status === 'VERIFIED' ? '<span class="verified-badge">✓ Verified</span>' : ''}
                    <h3 class="deviation-title">Deviation Identified</h3>
                    <div class="deviation-impact"><strong>Description:</strong> ${deviation.deviation}</div>
                    <div class="deviation-impact"><strong>Impact:</strong> ${deviation.impact}</div>
                    <div class="deviation-action">${deviation.action}</div>
                    ${deviation.status !== 'VERIFIED' ? `
                    <button class="verify-button" onclick="markAsVerified('${deviation.id}')">
                        Mark as Verified
                    </button>
                    ` : ''}
                </div>
                `).join('')}
            </div>
            ` : ''}
            
            ${allAcceptedItems.length > 0 ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">✅</span>
                    Accepted Risks
                </h2>
                ${allAcceptedRisks.map((risk: any) => `
                <div class="accepted-risk-card">
                    <div class="risk-header">
                        <span class="risk-severity">${risk.severity || 'ACCEPTED'}</span>
                        <h3 class="risk-title">${
                            risk.id?.includes('security') ? 'Security Risk Accepted' :
                            risk.id?.includes('performance') ? 'Performance Risk Accepted' :
                            'UX Risk Accepted'
                        }</h3>
                    </div>
                    <div class="risk-description">${risk.risk}</div>
                    <div class="risk-description"><strong>Impact:</strong> ${risk.impact}</div>
                </div>
                `).join('')}
                ${acceptedDataModelIssues.map((item: any) => `
                <div class="accepted-risk-card">
                    <div class="risk-header">
                        <span class="risk-severity">ACCEPTED</span>
                        <h3 class="risk-title">Data Model Issue Accepted</h3>
                    </div>
                    <div class="risk-description">${item.change_description}</div>
                    <div class="risk-description"><strong>Type:</strong> ${item.change_type.replace(/_/g, ' ')}</div>
                    <div class="risk-description"><strong>File:</strong> <code>${item.file}</code></div>
                    <div class="risk-description"><strong>Reasoning:</strong> ${item.reasoning}</div>
                </div>
                `).join('')}
                ${acceptedRegressions.map((item: any) => `
                <div class="accepted-risk-card">
                    <div class="risk-header">
                        <span class="risk-severity">ACCEPTED</span>
                        <h3 class="risk-title">Regression Accepted</h3>
                    </div>
                    <div class="risk-description">${item.regression_description}</div>
                    <div class="risk-description"><strong>File:</strong> <code>${item.file}</code></div>
                    <div class="risk-description"><strong>Impact:</strong> ${item.impact}</div>
                </div>
                `).join('')}
            </div>
            ` : ''}
            
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">🔗</span>
                    Integration Status
                </h2>
                <div class="integration-status ${feedbackData.integration_blocked ? 'integration-blocked' : 'integration-ready'}">
                    <span>${feedbackData.integration_blocked ? '🚫' : '✅'}</span>
                    <span>${feedbackData.integration_blocked ? 'Integration Blocked' : 'Ready for Integration'}</span>
                </div>
                ${!feedbackData.integration_blocked ? `
                <button class="generate-pr-button" onclick="generatePR()">
                    🚀 Generate PR
                </button>
                ` : `
                <button class="copy-feedback-button" onclick="copyFeedback()">
                    📋 Copy Feedback JSON
                </button>
                `}
            </div>
            
            ${feedbackData.summary ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">📝</span>
                    Summary
                </h2>
                <div class="summary-section">
                    <p class="summary-text">${feedbackData.summary}</p>
                </div>
            </div>
            ` : ''}
            
            ${feedbackData.task_context?.design_id && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(feedbackData.task_context.design_id) ? `
            <div class="content-section">
                <h2 class="section-title">
                    <span class="section-icon">📋</span>
                    Design Document
                </h2>
                <div class="design-doc-section">
                    <p class="design-doc-text">
                        View the complete design document that guided this implementation:
                    </p>
                    <a href="https://archknow.vercel.app/design-doc-wizard/view/${feedbackData.task_context.design_id}" 
                       target="_blank" 
                       class="design-doc-link">
                        🔗 View Complete Design
                    </a>
                    <p class="design-doc-description">
                        This link provides access to the full design process including task analysis, technical decisions, and implementation details.
                    </p>
                </div>
            </div>
            ` : ''}
        </div>
        
        <script>
            const vscode = acquireVsCodeApi();
            const feedbackData = ${JSON.stringify(feedbackData)};

            function generatePR() {
                vscode.postMessage({
                    type: 'generatePR'
                });
            }

            function copyFeedback() {
                // Filter feedback data to only include items that need attention
                const filteredFeedback = {
                    // Only include scope status if there are issues
                    ...(feedbackData.scope_status === 'MAJOR_DEVIATION' && { scope_status: feedbackData.scope_status }),
                    
                    // Only include scope assessment if there are issues
                    ...(feedbackData.scope_assessment && {
                        scope_assessment: {
                            // Only include deliverables that are not fully implemented
                            ...(feedbackData.scope_assessment.deliverables_status?.filter(d => d.status !== 'IMPLEMENTED').length > 0 && {
                                deliverables_status: feedbackData.scope_assessment.deliverables_status.filter(d => d.status !== 'IMPLEMENTED')
                            }),
                            // Include additional scope and gaps if they exist
                            ...(feedbackData.scope_assessment.additional_scope && { additional_scope: feedbackData.scope_assessment.additional_scope }),
                            ...(feedbackData.scope_assessment.scope_gap && { scope_gap: feedbackData.scope_assessment.scope_gap }),
                            // Only include data model analysis that is not accepted
                            ...(feedbackData.scope_assessment.data_model_analysis?.filter(d => d.status !== 'ACCEPTED').length > 0 && {
                                data_model_analysis: feedbackData.scope_assessment.data_model_analysis.filter(d => d.status !== 'ACCEPTED')
                            }),
                            // Only include regressions that are not accepted
                            ...(feedbackData.scope_assessment.significant_regressions?.filter(r => r.status !== 'ACCEPTED').length > 0 && {
                                significant_regressions: feedbackData.scope_assessment.significant_regressions.filter(r => r.status !== 'ACCEPTED')
                            })
                        }
                    }),
                    
                    // Only include risks that are still open
                    ...(feedbackData.critical_security_risks?.filter(r => r.status !== 'ACCEPTED').length > 0 && {
                        critical_security_risks: feedbackData.critical_security_risks.filter(r => r.status !== 'ACCEPTED')
                    }),
                    ...(feedbackData.critical_performance_risks?.filter(r => r.status !== 'ACCEPTED').length > 0 && {
                        critical_performance_risks: feedbackData.critical_performance_risks.filter(r => r.status !== 'ACCEPTED')
                    }),
                    ...(feedbackData.critical_ux_risks?.filter(r => r.status !== 'ACCEPTED').length > 0 && {
                        critical_ux_risks: feedbackData.critical_ux_risks.filter(r => r.status !== 'ACCEPTED')
                    }),
                    
                    // Only include scope deviations that need action
                    ...(feedbackData.scope_deviations?.filter(d => d.status === 'NEEDS_ACTION').length > 0 && {
                        scope_deviations: feedbackData.scope_deviations.filter(d => d.status === 'NEEDS_ACTION')
                    }),
                    
                    // Include integration status if blocked
                    ...(feedbackData.integration_blocked && { integration_blocked: feedbackData.integration_blocked }),
                    
                    // Include summary if there are issues
                    ...(feedbackData.summary && { summary: feedbackData.summary }),
                    
                    // Include context information
                    ...(feedbackData.task_context && { task_context: feedbackData.task_context }),
                    ...(feedbackData.milestone_context && { milestone_context: feedbackData.milestone_context }),
                    ...(feedbackData.issue_url && { issue_url: feedbackData.issue_url })
                };
                
                // Only copy if there are actual issues to report
                const hasIssues = Object.keys(filteredFeedback).length > 0;
                
                if (hasIssues) {
                    vscode.postMessage({
                        type: 'copyFeedback',
                        payload: JSON.stringify(filteredFeedback, null, 2)
                    });
                } else {
                    // Show message that everything looks good
                    vscode.postMessage({
                        type: 'showInformationMessage',
                        text: 'All items are implemented or accepted. No issues to copy!'
                    });
                }
            }

            function markAsVerified(deviationId) {
                console.log('markAsVerified called with:', deviationId);
                vscode.postMessage({
                    type: 'markDeviationAsVerified',
                    deviationId: deviationId
                });
            }

            function markRiskAsAccepted(riskId) {
                console.log('markRiskAsAccepted called with:', riskId);
                vscode.postMessage({
                    type: 'markRiskAsAccepted',
                    riskId: riskId
                });
            }
        </script>
        `;
    }

    createOrShowDesignDocWorkflowPanel(htmlContent: string, title: string, workspaceRoot: vscode.Uri): vscode.WebviewPanel {
        console.log('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: Creating/showing panel with title:', title);
        console.log('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: Received HTML content length:', htmlContent?.length);
        
        if (htmlContent && htmlContent.length < 500) { // Log small content fully
            console.log('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: Full HTML content:', htmlContent);
        } else if (htmlContent) {
            console.log('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: HTML content snippet (first 250 chars):', htmlContent.substring(0, 250));
        } else {
            console.error('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: CRITICAL - Received null or empty htmlContent!');
        }

        const column = vscode.window.activeTextEditor 
            ? vscode.window.activeTextEditor.viewColumn 
            : vscode.ViewColumn.Beside;

        if (this.panels.designDocWorkflowPanel) {
            console.log('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: Revealing existing panel.');
            this.panels.designDocWorkflowPanel.reveal(column);
            this.panels.designDocWorkflowPanel.title = title;
            this.panels.designDocWorkflowPanel.webview.html = htmlContent;
        } else {
            console.log('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: Creating new panel.');
            this.panels.designDocWorkflowPanel = vscode.window.createWebviewPanel(
                'archknowDesignDocWorkflow',
                title,
                column || vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    retainContextWhenHidden: UI_CONFIG.WEBVIEW_RETAIN_CONTEXT,
                    localResourceRoots: [vscode.Uri.joinPath(this.context.extensionUri, 'media'), workspaceRoot]
                }
            );
            
            this.panels.designDocWorkflowPanel.webview.html = htmlContent;
            
            this.panels.designDocWorkflowPanel.onDidDispose(
                () => { 
                    console.log('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: Panel disposed.');
                    this.panels.designDocWorkflowPanel = undefined; 
                }, 
                null, 
                this.context.subscriptions
            );
        }
        console.log('ArchKnow [WebviewManager.createOrShowDesignDocWorkflowPanel]: Panel setup complete.');
        return this.panels.designDocWorkflowPanel;
    }

    createDesignDocGeneratorPanel(htmlContent: string): vscode.WebviewPanel {
        const panel = vscode.window.createWebviewPanel(
            'archknowDesignDocGenerator',
            'Generate Design Document',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: UI_CONFIG.WEBVIEW_RETAIN_CONTEXT
            }
        );

        panel.webview.html = htmlContent;
        return panel;
    }

    createDesignDocReviewPanel(htmlContent: string, title: string): vscode.WebviewPanel {
        const panel = vscode.window.createWebviewPanel(
            'designDocReview',
            title,
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                localResourceRoots: [vscode.Uri.joinPath(this.context.extensionUri, 'media')]
            }
        );

        panel.webview.html = htmlContent;
        return panel;
    }

    createOrShowDesignDocJobsStatusPanel(htmlContent: string): vscode.WebviewPanel {
        if (this.designDocJobsStatusPanel) {
            this.designDocJobsStatusPanel.reveal();
            if (htmlContent) {
                this.designDocJobsStatusPanel.webview.html = htmlContent;
            }
            return this.designDocJobsStatusPanel;
        }

        this.designDocJobsStatusPanel = vscode.window.createWebviewPanel(
            'archknowDesignDocJobsStatus',
            'Design Doc Jobs Status',
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        if (htmlContent) {
            this.designDocJobsStatusPanel.webview.html = htmlContent;
        }

        this.designDocJobsStatusPanel.onDidDispose(() => {
            this.designDocJobsStatusPanel = undefined;
        });

        return this.designDocJobsStatusPanel;
    }

    public createOrShowDomainConceptsTreePanel(treeData: any[]): vscode.WebviewPanel {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : vscode.ViewColumn.One;
        const panelId = 'archknowDomainConceptsTree';
        const title = 'Domain Concepts Hierarchy';

        if (this.panels.domainConceptsTreePanel) {
            this.panels.domainConceptsTreePanel.title = title;
            this.panels.domainConceptsTreePanel.reveal(column);
            // Send updated data to the existing webview
            this.panels.domainConceptsTreePanel.webview.postMessage({ command: 'loadData', data: treeData });
        } else {
            this.panels.domainConceptsTreePanel = vscode.window.createWebviewPanel(
                panelId,
                title,
                column || vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    retainContextWhenHidden: UI_CONFIG.WEBVIEW_RETAIN_CONTEXT,
                    localResourceRoots: [vscode.Uri.joinPath(this.context.extensionUri, 'src', 'ui', 'webviews')]
                }
            );

            this.panels.domainConceptsTreePanel.webview.html = this.getHtmlForDomainConceptsTreeWebview(this.panels.domainConceptsTreePanel.webview);

            // Handle messages from the webview
            this.panels.domainConceptsTreePanel.webview.onDidReceiveMessage(
                message => {
                    switch (message.command) {
                        case 'requestInitialData':
                            // Webview is ready and requesting data
                            if (this.panels.domainConceptsTreePanel) { // Check if panel still exists
                                this.panels.domainConceptsTreePanel.webview.postMessage({ command: 'loadData', data: treeData });
                            }
                            return;
                        // Add other message handlers if needed
                    }
                },
                undefined,
                this.context.subscriptions
            );
            
            this.panels.domainConceptsTreePanel.onDidDispose(
                () => {
                    this.panels.domainConceptsTreePanel = undefined;
                },
                null,
                this.context.subscriptions
            );
             // Send initial data once the panel is created and HTML is set
            // The webview script will request it again via 'requestInitialData' if it loads later
            this.panels.domainConceptsTreePanel.webview.postMessage({ command: 'loadData', data: treeData });
        }
        return this.panels.domainConceptsTreePanel;
    }

    private getHtmlForDomainConceptsTreeWebview(webview: vscode.Webview): string {
        const scriptPathOnDisk = vscode.Uri.joinPath(this.context.extensionUri, 'src', 'ui', 'webviews', 'domainConceptsTree.js');
        const scriptUri = webview.asWebviewUri(scriptPathOnDisk);

        // Using a direct path for the HTML file as it's simpler and localResourceRoots is set
        const htmlPath = vscode.Uri.joinPath(this.context.extensionUri, 'src', 'ui', 'webviews', 'domainConceptsTree.html');
        
        // Read the HTML file content
        // Note: In a real extension, consider async file reading or bundling this HTML
        // For simplicity here, we'll assume htmlContent is read and URI is replaced
        // This is a placeholder for actual file reading logic if it were more complex
        let htmlContent = `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src ${webview.cspSource}; img-src ${webview.cspSource} data:;">
                <title>Domain Concepts Hierarchy</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        color: var(--vscode-editor-foreground);
                        background-color: var(--vscode-editor-background);
                        padding: 10px;
                    }
                    ul {
                        list-style-type: none;
                        padding-left: 20px;
                    }
                    .tree-node {
                        cursor: pointer;
                    }
                    .tree-node .node-content {
                        display: flex;
                        align-items: center;
                    }
                    .tree-node .toggle {
                        margin-right: 8px;
                        width: 16px; 
                        display: inline-block; 
                        text-align: center;
                    }
                    .tree-node .toggle.empty {
                         visibility: hidden; 
                    }
                    .tree-node ul {
                        display: none; 
                    }
                    .tree-node.expanded > ul {
                        display: block;
                    }
                    .node-name {
                        flex-grow: 1;
                    }
                    .node-count {
                        margin-left: 10px;
                        font-size: 0.9em;
                        color: var(--vscode-descriptionForeground);
                    }
                    #search-box {
                        width: 95%;
                        padding: 8px;
                        margin-bottom: 15px;
                        border: 1px solid var(--vscode-input-border, #ccc);
                        background-color: var(--vscode-input-background, #fff);
                        color: var(--vscode-input-foreground, #000);
                        border-radius: 3px;
                    }
                    #search-box:focus {
                        outline: 1px solid var(--vscode-focusBorder);
                    }
                    .highlight {
                        background-color: var(--vscode-list-activeSelectionBackground);
                        color: var(--vscode-list-activeSelectionForeground);
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                <input type="text" id="search-box" placeholder="Search concepts...">
                <div id="tree-container"></div>
                <script src="${scriptUri}"></script>
            </body>
            </html>
        `; 
        // In a real extension, you would read the htmlPath file content here. 
        // For example, using fs.readFileSync if synchronous is acceptable at this point, or make this method async.
        // For simplicity here, we'll assume htmlContent is read and URI is replaced
        // This is a placeholder for actual file reading logic if it were more complex
        try {
            const actualHtmlContent = require('fs').readFileSync(htmlPath.fsPath, 'utf8');
            htmlContent = actualHtmlContent.replace('./domainConceptsTree.js', scriptUri.toString());
        } catch (e) {
            console.warn('ArchKnow: Could not read domainConceptsTree.html directly, using embedded fallback. Error:', e);
            // Fallback to the already constructed HTML with scriptUri if fs fails (e.g. in restricted environment)
        }
        
        return htmlContent;
    }

    updateDesignDocJobStatus(jobId: string, status: any): void {
        if (this.designDocJobsStatusPanel) {
            // Validate that we have meaningful status data before updating
            if (!status || (!status.phaseProgress && status.progressPercentage === undefined && !status.currentPhase)) {
                console.log(`ArchKnow [WebviewManager]: Skipping status update for job ${jobId} due to incomplete status data`);
                return;
            }

            // Ensure we don't update with a phase0_pending state unless it's truly a new job
            if (status.currentPhase === 'phase0_pending' && status.progressPercentage === 0) {
                console.log(`ArchKnow [WebviewManager]: Skipping potential stale phase0_pending update for job ${jobId}`);
                return;
            }

            this.designDocJobsStatusPanel.webview.postMessage({
                command: 'updateJob',
                jobId: jobId,
                status: status
            });
        }
    }

    updateDesignDocJobs(jobs: any[]): void {
        if (this.designDocJobsStatusPanel) {
            // Sort jobs by updated_at in descending order (most recent first)
            const sortedJobs = [...jobs].sort((a, b) => {
                const dateA = new Date(a.updated_at || 0);
                const dateB = new Date(b.updated_at || 0);
                return dateB.getTime() - dateA.getTime();
            });

            this.designDocJobsStatusPanel.webview.postMessage({
                command: 'updateJobs',
                jobs: sortedJobs
            });
        }
    }

    setupFileOpenHandler(panel: vscode.WebviewPanel, repoRoot: string): void {
        panel.webview.onDidReceiveMessage(
            async (message) => {
                if (message.command === 'openFile') {
                    try {
                        if (!message.filePath) {
                            throw new Error("No file path provided");
                        }

                        const workspaceFolders = vscode.workspace.workspaceFolders;
                        if (!workspaceFolders || workspaceFolders.length === 0) {
                            throw new Error("No workspace folder is open");
                        }

                        const fileBaseDir = repoRoot || workspaceFolders[0].uri.fsPath;
                        const fileUri = vscode.Uri.file(path.join(fileBaseDir, message.filePath));

                        try {
                            await vscode.workspace.fs.stat(fileUri);
                        } catch (err) {
                            throw new Error(`File not found: ${message.filePath}`);
                        }

                        const document = await vscode.workspace.openTextDocument(fileUri);
                        await vscode.window.showTextDocument(document, { preview: false });
                    } catch (error: any) {
                        vscode.window.showErrorMessage(`ArchKnow: Error opening file: ${error.message}`);
                    }
                }
            },
            undefined,
            this.context.subscriptions
        );
    }

    dispose(): void {
        Object.values(this.panels).forEach(panel => {
            if (panel) {
                panel.dispose();
            }
        });
        this.panels = {};
    }

    private async handleGeneratePR(feedbackData: any, jobId?: string, milestoneId?: string): Promise<void> {
        try {
            // Generate PR description with milestone feedback
            const prTitle = this.generatePRTitle(feedbackData, milestoneId);
            const prDescription = this.generatePRDescription(feedbackData, jobId, milestoneId);
            const encodedDescription = encodeURIComponent(prDescription);
            const encodedTitle = encodeURIComponent(prTitle);
            
            // Get the Git extension and repository
            const gitExtension = vscode.extensions.getExtension('vscode.git')?.exports;
            if (!gitExtension) {
                throw new Error('Git extension not available');
            }
            
            const git = gitExtension.getAPI(1);
            if (git.repositories.length === 0) {
                throw new Error('No Git repository found');
            }
            
            const repo = git.repositories[0];
            const remotes = repo.state.remotes;
            
            // Look for origin remote first, then any remote
            const originRemote = remotes.find((remote: any) => remote.name === 'origin');
            const remote = originRemote || remotes[0];
            
            if (!remote || !remote.fetchUrl) {
                throw new Error('No Git remote found');
            }
            
            // Check repository status
            const workingTreeChanges = repo.state.workingTreeChanges;
            const indexChanges = repo.state.indexChanges;
            const hasUncommittedChanges = workingTreeChanges.length > 0 || indexChanges.length > 0;
            
            // Get current branch
            const currentBranch = repo.state.HEAD?.name;
            if (!currentBranch) {
                throw new Error('Could not determine current branch');
            }
            
            // Check if we're on main/master branch
            const isMainBranch = currentBranch === 'main' || currentBranch === 'master';
            if (isMainBranch && hasUncommittedChanges) {
                const createBranch = await vscode.window.showWarningMessage(
                    'You are on the main branch with uncommitted changes. Would you like to create a feature branch for the PR?',
                    'Create Branch',
                    'Cancel'
                );
                
                if (createBranch === 'Create Branch') {
                    const branchName = await vscode.window.showInputBox({
                        prompt: 'Enter branch name for the PR',
                        value: `milestone-${milestoneId || 'implementation'}`,
                        validateInput: (value) => {
                            if (!value || value.trim().length === 0) {
                                return 'Branch name cannot be empty';
                            }
                            if (!/^[a-zA-Z0-9\-_\/]+$/.test(value)) {
                                return 'Branch name contains invalid characters';
                            }
                            return null;
                        }
                    });
                    
                    if (!branchName) {
                        vscode.window.showInformationMessage('PR generation cancelled.');
                        return;
                    }
                    
                    // Create and checkout new branch
                    await repo.createBranch(branchName, true);
                    vscode.window.showInformationMessage(`Created and switched to branch: ${branchName}`);
                } else {
                    vscode.window.showInformationMessage('PR generation cancelled.');
                    return;
                }
            }
            
            // Handle uncommitted changes
            if (hasUncommittedChanges) {
                const action = await vscode.window.showInformationMessage(
                    'You have uncommitted changes. What would you like to do?',
                    'Commit & Push',
                    'Cancel'
                );
                
                if (action === 'Commit & Push') {
                    // Stage all changes if there are working tree changes
                    if (workingTreeChanges.length > 0) {
                        vscode.window.showInformationMessage('Staging all changes...');
                        await repo.add([]);
                    }
                    
                    // Create commit message using PR summary if available
                    let defaultCommitMessage;
                    const taskTitle = feedbackData.task_context?.task_title || feedbackData.milestone_context?.title || 'Milestone Implementation';
                    const milestoneTitle = feedbackData.milestone_context?.title;
                    const milestoneDisplay = milestoneId || feedbackData.milestone_context?.milestone_id || '';
                    
                    console.log('ArchKnow [generateCommitMessage]: Debug values:', {
                        taskTitle,
                        milestoneTitle,
                        milestoneDisplay,
                        hasTaskContext: !!feedbackData.task_context,
                        taskContextTaskTitle: feedbackData.task_context?.task_title,
                        milestoneContextTitle: feedbackData.milestone_context?.title,
                        areEqual: taskTitle === milestoneTitle
                    });
                    
                    // Create a descriptive commit title that includes both task and milestone info
                    let commitTitle;
                    if (feedbackData.task_context?.task_title && milestoneTitle && milestoneTitle !== taskTitle) {
                        // We have both task title and different milestone title
                        commitTitle = milestoneDisplay ? 
                            `${taskTitle} - ${milestoneTitle} (${milestoneDisplay})` : 
                            `${taskTitle} - ${milestoneTitle}`;
                        console.log('ArchKnow [generateCommitMessage]: Using combined format:', commitTitle);
                    } else {
                        // Use the existing format when they're the same or we only have one
                        commitTitle = milestoneDisplay ? `${taskTitle} ${milestoneDisplay}` : taskTitle;
                        console.log('ArchKnow [generateCommitMessage]: Using simple format:', commitTitle);
                    }
                    
                    let commitBody = 'Milestone implementation complete.';

                    // Extract implemented deliverables from multiple sources
                    let deliverables: string[] = [];
                    
                    // First, try to get from scope assessment status (detailed status)
                    const scopeDeliverables = feedbackData.scope_assessment?.deliverables_status
                        ?.filter((d: any) => d.status === 'IMPLEMENTED' || d.status === 'PARTIAL')
                        .map((d: any) => `${d.deliverable} (${d.status.toLowerCase()})`);
                    
                    if (scopeDeliverables && scopeDeliverables.length > 0) {
                        deliverables = scopeDeliverables;
                        console.log('ArchKnow [generateCommitMessage]: Using scope assessment deliverables:', scopeDeliverables);
                    } 
                    // Fallback: use all planned deliverables from milestone context
                    else if (feedbackData.milestone_context?.planned_deliverables) {
                        deliverables = feedbackData.milestone_context.planned_deliverables.map((d: string) => d);
                        console.log('ArchKnow [generateCommitMessage]: Using all planned deliverables as fallback:', deliverables);
                    }

                    if (deliverables.length > 0) {
                        const deliverablesList = deliverables.map(d => `- ${d}`).join('\n');
                        commitBody += `\n\nImplemented Deliverables:\n${deliverablesList}`;
                        console.log('ArchKnow [generateCommitMessage]: Added deliverables to commit body');
                    } else {
                        console.log('ArchKnow [generateCommitMessage]: No deliverables found to include in commit');
                    }

                    if (jobId) {
                        commitBody += `\n\nDesign ID: ${jobId}`;
                    }

                    // Add GitHub issue URL if available
                    if (feedbackData.issue_url) {
                        commitBody += `\n\nRelates to: ${feedbackData.issue_url}`;
                        if (milestoneTitle || milestoneDisplay) {
                            commitBody += `\n\n[Milestone implementation]`;
                        }
                    }

                    defaultCommitMessage = `${commitTitle}\n\n${commitBody}`;
                    
                    const commitMessage = await this.showCommitMessageDialog(defaultCommitMessage);
                    
                    if (!commitMessage) {
                        vscode.window.showInformationMessage('PR generation cancelled.');
                        return;
                    }
                    
                    // Commit changes
                    vscode.window.showInformationMessage('Committing changes...');
                    await repo.commit(commitMessage);
                    
                } else {
                    vscode.window.showInformationMessage('PR generation cancelled.');
                    return;
                }
            }
            
            // Check if current branch exists on remote
            const currentBranchName = repo.state.HEAD?.name;
            if (!currentBranchName) {
                throw new Error('Could not determine current branch name');
            }
            
            // Push to remote
            vscode.window.showInformationMessage('Pushing changes to remote...');
            try {
                await repo.push();
            } catch (pushError) {
                // If push fails, try to set upstream and push
                try {
                    await repo.push(remote.name, currentBranchName, true);
                } catch (upstreamError) {
                    throw new Error(`Failed to push changes: ${upstreamError}`);
                }
            }
            
            vscode.window.showInformationMessage('Changes pushed successfully!');
            
            // Convert Git URL to GitHub URL
            let githubUrl = remote.fetchUrl;
            
            // Handle SSH URLs (**************:user/repo.git)
            if (githubUrl.startsWith('**************:')) {
                githubUrl = githubUrl.replace('**************:', 'https://github.com/');
            }
            
            // Remove .git suffix
            if (githubUrl.endsWith('.git')) {
                githubUrl = githubUrl.slice(0, -4);
            }
            
            // Ensure it's a GitHub URL
            if (!githubUrl.includes('github.com')) {
                throw new Error('Repository is not hosted on GitHub');
            }
            
            // Create PR URL with current branch and title
            const prUrl = `${githubUrl}/compare/${currentBranchName}?expand=1&title=${encodedTitle}&body=${encodedDescription}`;
            
            await vscode.env.openExternal(vscode.Uri.parse(prUrl));
            vscode.window.showInformationMessage('Opening GitHub PR creation page with milestone feedback...');
            
        } catch (error) {
            console.error('Error generating PR:', error);
            
            // Fallback: copy description to clipboard
            try {
                const prDescription = this.generatePRDescription(feedbackData, jobId, milestoneId);
                await vscode.env.clipboard.writeText(prDescription);
                vscode.window.showWarningMessage(`Could not complete PR flow: ${(error as Error).message}. PR description copied to clipboard - please create the PR manually on GitHub.`);
            } catch (clipboardError) {
                console.error('Error copying to clipboard:', clipboardError);
                vscode.window.showErrorMessage(`Failed to generate PR: ${(error as Error).message}. Please commit and push your changes manually, then create the PR on GitHub.`);
            }
        }
    }

    private generatePRDescription(feedbackData: any, jobId?: string, milestoneId?: string): string {
        const statusEmoji = feedbackData.scope_status === 'COMPLIANT' ? '✅' : '⚠️';
        const integrationStatus = feedbackData.integration_blocked ? 'Blocked' : 'Ready';
        
        let description = `<h1>${statusEmoji} Milestone Implementation Report</h1>\n\n`;
        
        // Include Design ID and Task info at the top
        if (jobId) {
            description += `**Design ID:** \`${jobId}\`\n`;
        }
        const taskTitle = feedbackData.task_context?.task_title || feedbackData.milestone_context?.title || 'Milestone Implementation';
        if (taskTitle) {
            description += `**Task:** ${taskTitle}\n`;
        }
        const milestoneTitle = feedbackData.milestone_context?.title;
        if (milestoneTitle && milestoneTitle !== taskTitle) {
            description += `**Milestone:** ${milestoneTitle}\n`;
        }
        if (milestoneId || feedbackData.milestone_context?.milestone_id) {
            const displayMilestoneId = milestoneId || feedbackData.milestone_context?.milestone_id;
            description += `**Milestone ID:** \`${displayMilestoneId}\`\n`;
        }
        
        // Add GitHub issue URL if available
        if (feedbackData.issue_url) {
            description += `**Related Issue:** ${feedbackData.issue_url}\n`;
        }
        
        description += `**Integration Status:** ${integrationStatus}\n\n`;
        
        // Add scope assessment overview
        description += `<h3>Scope Assessment</h3>\n`;
        description += `**Status:** ${feedbackData.scope_status}\n\n`;
        
        // Use pr_summary if available, otherwise pr_ready_summary, otherwise fallback summary
        const summaryText = feedbackData.pr_summary || 
                           feedbackData.pr_ready_summary?.summary || 
                           feedbackData.summary || 
                           'Implementation completed with all deliverables.';
                                   
        // Add deliverables status if available
        if (feedbackData.scope_assessment?.deliverables_status?.length > 0) {
            description += `<h3>Deliverables Status</h3>\n\n`;
            feedbackData.scope_assessment.deliverables_status.forEach((deliverable: any) => {
                const statusIcon = this.getDeliverableStatusIcon(deliverable.status);
                description += `- ${statusIcon} **${deliverable.deliverable}**\n`;
                description += `  - **Status:** ${deliverable.status}\n`;
                if (deliverable.evidence) {
                    description += `  - **Evidence:** \`${deliverable.evidence}\`\n`;
                }
            });
            description += '\n';
        }
        
        // Add additional scope if implemented beyond requirements
        if (feedbackData.scope_assessment?.additional_scope) {
            description += `<h3>Additional Features Implemented</h3>\n${feedbackData.scope_assessment.additional_scope}\n\n`;
        }
        
        // Add data model analysis if any
        if (feedbackData.scope_assessment?.data_model_analysis?.length > 0) {
            description += `<h3>💾 Data Model Analysis</h3>\n`;
            feedbackData.scope_assessment.data_model_analysis.forEach((change: any) => {
                description += `- **${change.change_type.replace(/_/g, ' ')}:** ${change.change_description}\n`;
                description += `  - File: \`${change.file}\`\n`;
                description += `  - Reasoning: ${change.reasoning}\n`;
            });
            description += '\n';
        }

        // Add significant regressions if any
        if (feedbackData.scope_assessment?.significant_regressions?.length > 0) {
            description += `<h3>📉 Significant Regressions</h3>\n`;
            feedbackData.scope_assessment.significant_regressions.forEach((regression: any) => {
                description += `- **Regression:** ${regression.regression_description}\n`;
                description += `  - File: \`${regression.file}\`\n`;
                description += `  - Impact: ${regression.impact}\n`;
            });
            description += '\n';
        }

        // Add risks if any
        const openRisks = [
            ...(feedbackData.critical_security_risks?.filter((r: any) => r.status !== 'ACCEPTED') || []),
            ...(feedbackData.critical_performance_risks?.filter((r: any) => r.status !== 'ACCEPTED') || []),
            ...(feedbackData.critical_ux_risks?.filter((r: any) => r.status !== 'ACCEPTED') || [])
        ];

        if (openRisks.length > 0) {
            description += `<h3>🚨 Open Critical Issues</h3>\n`;
            
            (feedbackData.critical_security_risks?.filter((r: any) => r.status !== 'ACCEPTED') || []).forEach((risk: any) => {
                description += `- 🔒 **Security Risk:** ${risk.risk}\n`;
                description += `  - Impact: ${risk.impact}\n`;
                description += `  - Fix: ${risk.fix}\n`;
            });
            
            (feedbackData.critical_performance_risks?.filter((r: any) => r.status !== 'ACCEPTED') || []).forEach((risk: any) => {
                description += `- ⚡️ **Performance Risk:** ${risk.risk}\n`;
                description += `  - Impact: ${risk.impact}\n`;
                description += `  - File: ${risk.file}\n`;
                description += `  - Fix: ${risk.fix}\n`;
            });
            
            (feedbackData.critical_ux_risks?.filter((r: any) => r.status !== 'ACCEPTED') || []).forEach((risk: any) => {
                description += `- 👤 **UX Risk:** ${risk.risk}\n`;
                description += `  - Impact: ${risk.impact}\n`;
                description += `  - Fix: ${risk.fix}\n`;
            });
            description += '\n';
        }
        
        // Add scope deviations if any
        const openDeviations = feedbackData.scope_deviations?.filter((d: any) => d.status !== 'VERIFIED');
        if (openDeviations?.length > 0) {
            description += `<h3>Scope Deviations</h3>\n`;
            openDeviations.forEach((deviation: any) => {
                description += `- ⚠️ **${deviation.deviation}**\n`;
                description += `  - Impact: ${deviation.impact}\n`;
                description += `  - Action: ${deviation.action}\n`;
            });
            description += '\n';
        }
        
        // Add accepted risks if any
        const acceptedRisks = [
            ...(feedbackData.critical_security_risks?.filter((r: any) => r.status === 'ACCEPTED') || []),
            ...(feedbackData.critical_performance_risks?.filter((r: any) => r.status === 'ACCEPTED') || []),
            ...(feedbackData.critical_ux_risks?.filter((r: any) => r.status === 'ACCEPTED') || [])
        ];

        if (acceptedRisks.length > 0) {
            description += `<h3>✅ Accepted Risks</h3>\n\n`;
            description += `The following critical risks were identified but have been accepted and will not be addressed at this time:\n\n`;
            acceptedRisks.forEach((risk: any) => {
                let riskType = 'Risk';
                if (risk.id?.includes('security')) riskType = 'Security Risk';
                if (risk.id?.includes('performance')) riskType = 'Performance Risk';
                if (risk.id?.includes('ux')) riskType = 'UX Risk';
                description += `- **${riskType}:** ${risk.risk}\n`;
                if (risk.file) {
                    description += `  - File: \`${risk.file}\`\n`;
                }
                description += `  - Impact: ${risk.impact}\n\n`;
            });
        }

        // Add accepted data model issues if any
        const acceptedDataModelIssues = feedbackData.scope_assessment?.data_model_analysis?.filter((r: any) => r.status === 'ACCEPTED') || [];
        if (acceptedDataModelIssues.length > 0) {
            description += `<h3>✅ Accepted Data Model Issues</h3>\n\n`;
            description += `The following data model issues were identified but have been accepted:\n\n`;
            acceptedDataModelIssues.forEach((issue: any) => {
                description += `- **${issue.change_type.replace(/_/g, ' ')}:** ${issue.change_description}\n`;
                description += `  - File: \`${issue.file}\`\n`;
                description += `  - Reasoning: ${issue.reasoning}\n\n`;
            });
        }

        // Add accepted regressions if any
        const acceptedRegressions = feedbackData.scope_assessment?.significant_regressions?.filter((r: any) => r.status === 'ACCEPTED') || [];
        if (acceptedRegressions.length > 0) {
            description += `<h3>✅ Accepted Regressions</h3>\n\n`;
            description += `The following regressions were identified but have been accepted:\n\n`;
            acceptedRegressions.forEach((regression: any) => {
                description += `- **Regression:** ${regression.regression_description}\n`;
                description += `  - File: \`${regression.file}\`\n`;
                description += `  - Impact: ${regression.impact}\n\n`;
            });
        }
        
        // Add verification criteria if available
        if (feedbackData.milestone_context?.verification_criteria?.length > 0) {
            description += `<h3>Verification Criteria</h3>\n`;
            feedbackData.milestone_context.verification_criteria.forEach((criteria: string) => {
                description += `- ✅ ${criteria}\n`;
            });
            description += '\n';
        }
        
        // Add public design doc link if design_id is a valid UUID (actual session ID)
        if (feedbackData.task_context?.design_id) {
            const designId = feedbackData.task_context.design_id;
            // Validate that it's a UUID format (not placeholder like 'mcp-generated' or 'implementation-plan')
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
            if (uuidRegex.test(designId)) {
                description += `---\n\n`;
                description += `**📋 View Complete Design Document**: [https://archknow.vercel.app/design-doc-wizard/view/${designId}](https://archknow.vercel.app/design-doc-wizard/view/${designId})\n\n`;
                description += `*This link provides access to the full design process including task analysis, technical decisions, and implementation details.*\n\n`;
            }
        }
        
        description += `---\n\n[![Generated by Nuvineer](https://img.shields.io/badge/Generated%20by-Nuvineer-4a90e2?style=flat-square&logo=data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KUGhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K)](https://nuvineer.ai)`;
        
        return description;
    }

    private generatePRTitle(feedbackData: any, milestoneId?: string): string {
        const taskTitle = feedbackData.task_context?.task_title || feedbackData.milestone_context?.title || 'Milestone Implementation';
        const milestoneTitle = feedbackData.milestone_context?.title;
        const milestoneDisplay = milestoneId || feedbackData.milestone_context?.milestone_id || '';
        
        console.log('ArchKnow [generatePRTitle]: Debug values:', {
            taskTitle,
            milestoneTitle,
            milestoneDisplay,
            hasTaskContext: !!feedbackData.task_context,
            taskContextTaskTitle: feedbackData.task_context?.task_title,
            milestoneContextTitle: feedbackData.milestone_context?.title,
            areEqual: taskTitle === milestoneTitle
        });
        
        // Create a descriptive PR title that includes both task and milestone info
        if (feedbackData.task_context?.task_title && milestoneTitle && milestoneTitle !== taskTitle) {
            // We have both task title and different milestone title
            const result = milestoneDisplay ? 
                `${taskTitle} - ${milestoneTitle} (${milestoneDisplay})` : 
                `${taskTitle} - ${milestoneTitle}`;
            console.log('ArchKnow [generatePRTitle]: Using combined format:', result);
            return result;
        } else {
            // Use the existing format when they're the same or we only have one
            const result = milestoneDisplay ? `${taskTitle} ${milestoneDisplay}` : taskTitle;
            console.log('ArchKnow [generatePRTitle]: Using simple format:', result);
            return result;
        }
    }

    private getDeliverableStatusIcon(status: string): string {
        switch (status) {
            case 'IMPLEMENTED': return '✅';
            case 'PARTIAL': return '⚠️';
            case 'MISSING': return '❌';
            case 'NOT_APPLICABLE': return '➖';
            default: return '❓';
        }
    }

    private async showCommitMessageDialog(message: string): Promise<string | undefined> {
        return new Promise((resolve) => {
            const panel = vscode.window.createWebviewPanel(
                'commitMessage',
                'Edit Commit Message',
                vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    retainContextWhenHidden: false
                }
            );

            panel.webview.html = this.getCommitMessageDialogHtml(message);

            panel.webview.onDidReceiveMessage(
                (message) => {
                    switch (message.type) {
                        case 'commit':
                            resolve(message.message);
                            panel.dispose();
                            break;
                        case 'cancel':
                            resolve(undefined);
                            panel.dispose();
                            break;
                    }
                },
                undefined,
                this.context.subscriptions
            );

            panel.onDidDispose(() => {
                resolve(undefined);
            });
        });
    }

    private getCommitMessageDialogHtml(message: string): string {
        const escapedMessage = message.replace(/"/g, '&quot;').replace(/\n/g, '\\n');
        
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Commit Message</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 10px 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
        }
        
        .content {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 12px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }
        
        textarea {
            width: 100%;
            min-height: 220px;
            padding: 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background-color: #ffffff;
            color: #2c3e50;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            box-sizing: border-box;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        
        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }
        
        .primary-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .primary-button:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .primary-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }
        
        .secondary-button {
            background-color: #6c757d;
            color: white;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
        }
        
        .secondary-button:hover {
            background-color: #5a6268;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
        
        .help-text {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin-top: 12px;
            font-size: 14px;
            color: #495057;
            line-height: 1.5;
        }
        
        .help-text .tip-icon {
            font-size: 16px;
            margin-right: 8px;
        }
        
        .char-count {
            font-size: 13px;
            color: #6c757d;
            text-align: right;
            margin-top: 8px;
            font-weight: 500;
        }
        
        .commit-preview {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-size: 13px;
            color: #495057;
        }
        
        .commit-preview-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        
        .archknow-badge {
            text-align: center;
            margin-top: 20px;
            padding: 10px;
            border-top: 1px solid #e9ecef;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .title {
                font-size: 24px;
            }
            
            .content {
                padding: 20px;
            }
            
            .button-group {
                flex-direction: column;
                gap: 10px;
            }
            
            button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Commit Message</h1>
            <p class="subtitle">Review and finalize your milestone implementation commit</p>
        </div>
        
        <div class="content">
            <div class="form-group">
                <label for="commitMessage">📝 Commit Message:</label>
                <textarea id="commitMessage" placeholder="Enter your commit message...">${escapedMessage.replace(/\\n/g, '\n')}</textarea>
                <div class="char-count">
                    <span id="charCount">0</span> characters
                </div>
                <div class="help-text">
                    <span class="tip-icon">💡</span><strong>Best Practices:</strong> 
                    Use a clear, descriptive message that explains what was implemented. 
                    The first line should be a brief summary (50 chars or less), followed by a blank line and more detailed description if needed.
                </div>
            </div>
            
            <div class="button-group">
                <button type="button" class="secondary-button" onclick="cancel()">Cancel</button>
                <button type="button" class="primary-button" id="commitButton" onclick="commit()">🚀 Commit & Continue</button>
            </div>
        </div>
        
        <div class="nuvineer-badge">
            <a href="https://nuvineer.ai" target="_blank" style="text-decoration: none;">
                <img src="https://img.shields.io/badge/Powered%20by-Nuvineer-667eea?style=flat-square&logo=data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Powered by Nuvineer" />
            </a>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        const textarea = document.getElementById('commitMessage');
        const charCount = document.getElementById('charCount');
        const commitButton = document.getElementById('commitButton');
        
        function updateCharCount() {
            const count = textarea.value.length;
            charCount.textContent = count;
            
            // Update button state
            commitButton.disabled = textarea.value.trim().length === 0;
        }
        
        function commit() {
            const message = textarea.value.trim();
            if (message) {
                vscode.postMessage({
                    type: 'commit',
                    message: message
                });
            }
        }
        
        function cancel() {
            vscode.postMessage({
                type: 'cancel'
            });
        }
        
        // Event listeners
        textarea.addEventListener('input', updateCharCount);
        textarea.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                commit();
            }
            if (e.key === 'Escape') {
                cancel();
            }
        });
        
        // Initialize
        updateCharCount();
        textarea.focus();
        
        // Select all text for easy editing
        textarea.select();
    </script>
</body>
</html>`;
    }
}