export interface DesignDocMetadata {
    title: string;
    author?: string;
    status: DesignDocStatus;
    dateCreated: string;
    lastUpdated: string;
    approver?: string;
    reviewers?: string[];
    feedback?: DesignDocFeedbackItem[];
    feedback_requested_at?: string;
    approved_at?: string;
    implemented_at?: string;
    implementation_plan_generated?: boolean;
    implementation_plan_uri?: string;
    aiComments?: AiReviewComment[];
}

export interface DesignDocFeedbackItem {
    user: string;
    comment: string;
    timestamp: string;
    type: 'comment' | 'change_request' | 'approval_comment' | 'system_action';
    category?: 'blocking' | 'non-blocking' | 'nit';
    selection?: {
        startLine?: number;
        startOffset?: number;
        endLine?: number;
        endOffset?: number;
        selectedText: string;
    };
    id?: string;
    status?: 'open' | 'addressed' | 'wont-fix';
    resolutionText?: string;
    resolutionAuthor?: string;
    resolutionTimestamp?: string;
}

export interface TextSelection {
    selectedText: string;
    startLine?: number;
    endLine?: number;
    startColumn?: number;
    endColumn?: number;
}

export type DesignDocStatus = 'pending' | 'under_review' | 'ready_for_implementation' | 'implemented' | 'changes-requested';

export interface DesignDocFrontmatter {
    status: 'pending' | 'under_review' | 'ready_for_implementation' | 'implemented' | 'changes-requested';
    title?: string;
    author?: string;
    approver?: string;          
    reviewers?: string[];   
    feedback_requested_at?: string;
    approved_at?: string;
    implemented_at?: string;
    feedback?: DesignDocFeedbackItem[];
    implementation_plan_generated?: boolean;
    implementation_plan_uri?: string;
    lastUpdated?: string;
    aiComments?: AiReviewComment[];
    jobId?: string;
}

// Interface for converting between DesignDocFrontmatter and DesignDocMetadata
export function convertFrontmatterToMetadata(frontmatter: DesignDocFrontmatter): DesignDocMetadata {
    return {
        title: frontmatter.title || 'Untitled Design Document',
        author: frontmatter.author,
        status: frontmatter.status as DesignDocStatus,
        dateCreated: frontmatter.feedback_requested_at || frontmatter.lastUpdated || new Date().toISOString(),
        lastUpdated: frontmatter.lastUpdated || new Date().toISOString(),
        approver: frontmatter.approver,
        reviewers: frontmatter.reviewers,
        feedback: frontmatter.feedback,
        feedback_requested_at: frontmatter.feedback_requested_at,
        approved_at: frontmatter.approved_at,
        implemented_at: frontmatter.implemented_at,
        implementation_plan_generated: frontmatter.implementation_plan_generated,
        implementation_plan_uri: frontmatter.implementation_plan_uri,
        aiComments: frontmatter.aiComments
    };
}

// Additional types for better organization
export interface WebviewMessage {
    command: string;
    [key: string]: any;
}

export interface FileSelectionMessage extends WebviewMessage {
    command: 'requestFileSelection';
}

export interface FetchContextMessage extends WebviewMessage {
    command: 'fetchContext';
    type: 'file' | 'concepts' | 'prompt';
    value: string | string[];
}

export interface ConceptsSelectedMessage extends WebviewMessage {
    command: 'conceptsSelected';
    concepts: string[];
}

export interface DesignDocWorkflowMessage extends WebviewMessage {
    docUriString: string;
}

export interface RequestFeedbackMessage extends DesignDocWorkflowMessage {
    command: 'requestFeedback';
    approver: string;
    reviewers: string[];
}

export interface SetGitHubHandleMessage extends WebviewMessage {
    command: 'setGitHubHandle';
    handle: string;
    docUriString?: string;
}

export interface SaveDocumentMessage extends DesignDocWorkflowMessage {
    command: 'saveDocument';
    title: string;
    author: string;
    content: string;
}

export interface AddCommentMessage extends DesignDocWorkflowMessage {
    command: 'addComment';
    comment: DesignDocFeedbackItem;
}

// API-related types that might be used across modules
export interface ApiError {
    status: number;
    message: string;
    details?: string;
}

export interface ApiSuccessResponse<T = any> {
    success: true;
    data?: T;
}

export interface ApiErrorResponse {
    success: false;
    error: string;
    details?: string;
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

// Configuration types
export interface ExtensionConfiguration {
    apiKey?: string;
    apiUrl: string;
    autoRefreshConcepts?: boolean;
    maxConceptsToShow?: number;
}

// Webview state management types
export interface WebviewState {
    selectedConcepts?: string[];
    selectedFilePath?: string;
    currentPrompt?: string;
}

// Progress reporting types
export interface ProgressStep {
    increment: number;
    message: string;
}

export interface ProgressReporter {
    report(step: ProgressStep): void;
}

// Updated types.ts - Add new message types for edit/save functionality

export interface SaveMetadataMessage extends DesignDocWorkflowMessage {
    command: 'saveMetadata';
    updatedMetadata: Partial<DesignDocFrontmatter>;
}

export interface ApproveDocumentMessage extends DesignDocWorkflowMessage {
    command: 'approveDocument';
}

export interface RequestChangesDocumentMessage extends DesignDocWorkflowMessage {
    command: 'requestChangesDocument';
}

// New types for AI Review and Feedback
export interface AiReviewComment {
    id: string;
    type: 'blocking' | 'non-blocking' | 'nit';
    status: 'open' | 'addressed' | 'wont-fix' | 'pending-ai-addressal';
    author: 'ai-reviewer' | string;
    timestamp: string;
    text: string;
    suggestedSection?: string;
    resolutionAuthor?: string;
    resolutionTimestamp?: string;
    resolutionText?: string;
}

export interface RequestAiReviewMessage extends DesignDocWorkflowMessage {
    command: 'requestAiReview';
}

export interface AddressAiCommentMessage extends DesignDocWorkflowMessage {
    command: 'addressAiComment';
    commentIds: string[];
}

export interface UpdateCommentStatusMessage extends DesignDocWorkflowMessage {
    command: 'updateCommentStatus';
    commentId: string;
    newStatus: 'addressed' | 'wont-fix' | 'pending-ai-addressal';
    resolutionText?: string;
}

// Update the DesignDocMessage union type to include new message types
export type DesignDocMessage = 
    | RequestFeedbackMessage
    | SetGitHubHandleMessage
    | SaveDocumentMessage
    | SaveMetadataMessage
    | ApproveDocumentMessage
    | RequestChangesDocumentMessage
    | AddCommentMessage
    | RequestAiReviewMessage
    | AddressAiCommentMessage
    | UpdateCommentStatusMessage;

export interface DeveloperNotes {
    referenced_decisions?: string[];
    implementation_guidance?: string;
}

export interface HighLevelDesign {
    core_architectural_choices: CoreArchitecturalChoice[];
    key_components_and_responsibilities: KeyComponent[];
    data_model_changes: string;
    infrastructure_and_ops: InfrastructureAndOps;
    developer_notes: DeveloperNotes;
    process_flow_diagram_mermaid: string;
    overall_system_overview: string;
    technicalDecisions?: {
        criticalChoices?: Array<{
            decision: string;
            rationale: string;
            implications: string;
        }>;
        systemConstraints?: Array<{
            constraint: string;
            enforcement: string;
            userCommunication: string;
        }>;
    };
    errorHandling?: {
        scenarios?: Array<{
            scenario: string;
            detection: string;
            userMessage: string;
            recovery: string;
        }>;
        retryStrategy?: string;
        fallbackBehavior?: string;
    };
    coreFlow?: {
        overview?: string;
        components?: Array<{
            name: string;
            responsibility: string;
        }>;
        dataModelChanges?: string;
    };
    processFlow?: string;
    infrastructure?: {
        deployment?: string;
        monitoring?: string;
        security?: string;
        rollback?: string;
    };
    developerNotes?: {
        referencedDecisions?: string[];
        implementationGuidance?: string;
    };
}

export interface CoreArchitecturalChoice {
    decision_point_id: string;
    decision_point_title: string;
    justification_and_context: string;
    recommended_approach_description: string;
    new_constraints_introduced: string;
}

export interface ErrorHandlingAndRecovery {
    overall_strategy?: string;
    critical_error_scenarios?: string[];
}

export interface KeyComponent {
    name: string;
    responsibility: string;
}

export interface InfrastructureAndOps {
    deployment_strategy?: string;
    monitoring_approach?: string;
    security_considerations?: string;
    rollback_plan?: string;
}

export interface AlternativesAnalysis {
    evaluatedApproaches: EvaluatedApproach[];
    recommendation?: string;
}

export interface EvaluatedApproach {
    approachName: string;
    description: string;
    pros?: string[];
    cons?: string[];
    alignmentWithContext?: string;
    referencedDecisionIds?: string[];
    recommendation_justification?: string;
}

export interface GeneratedDesignDocFromApi {
    title?: string;
    goals?: string[];
    non_goals?: string[];
    high_level_design?: HighLevelDesign;
    alternatives_analysis?: AlternativesAnalysis;
    process_flow_diagram_mermaid?: string;
    milestones_sketch?: string;
    success_metrics?: string[];
    referenced_decisions?: ReferencedDecisionForDoc[];
}

export interface ReferencedDecisionForDoc {
    id: string;
    values: any;
    summary_of_relevance_in_this_design?: string;
    metadata: {
    title: string;
    related_files?: string[];
    dev_prompt?: string;
    best_practice_reason?: string;
    risks_extracted?: {
        high?: string[];
        medium?: string[];
    };
    impact?: string;
}
}

export interface Milestone {
    name: string;
    description: string;
    priority: 'High' | 'Medium' | 'Low';
    complexity: 'High' | 'Medium' | 'Low';
    owner?: string;
    dependencies?: string[];
    successIndicators?: string[];
}