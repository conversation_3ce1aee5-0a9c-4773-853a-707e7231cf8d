import * as vscode from 'vscode';

export class ArchKnowUriHandler implements vscode.UriHandler {
    async handleUri(uri: vscode.Uri): Promise<void> {
        console.log(`ArchKnow: ArchKnowUriHandler.handleUri INVOKED with: ${uri.toString()}`);

        const scheme = uri.scheme.toLowerCase();
        const path = uri.path;
        const authority = uri.authority;

        if (scheme === 'cursor' && authority && authority.toLowerCase() === 'archknow.archknow') {
            console.log(`ArchKnow: Detected 'cursor' scheme for this extension: ${uri.toString()}`);
            
            if (path === '/viewDecision') {
                console.log(`ArchKnow: Handling 'cursor://archknow.archknow/viewDecision' URI: ${uri.toString()}`);
                try {
                    const params = new URLSearchParams(uri.query);
                    const decisionId = params.get('id');
                    if (decisionId) {
                        console.log(`ArchKnow: Triggering showDecisionById for ID: ${decisionId} from cursor:// URI`);
                        vscode.commands.executeCommand('archknow.showDecisionById', decisionId);
                    } else {
                        vscode.window.showErrorMessage('ArchKnow: Decision ID missing in cursor:// URI.');
                        console.warn('ArchKnow: Decision ID missing in cursor://.../viewDecision URI query:', uri.query);
                    }
                } catch (error: any) {
                    console.error(`ArchKnow: Error parsing cursor://.../viewDecision URI: ${error}`);
                    vscode.window.showErrorMessage(`ArchKnow: Invalid cursor:// decision URI format: ${error.message}`);
                }
            } else if (path && path.startsWith('/file/')) {
                const filePathToOpen = path.substring('/file'.length);
                try {
                    if (!vscode.workspace.isTrusted) {
                        vscode.window.showErrorMessage('ArchKnow: Workspace must be trusted to open files via cursor:// URI.');
                        return;
                    }
                    const fileUri = vscode.Uri.file(filePathToOpen);
                    console.log(`ArchKnow: Attempting to open file from cursor:// URI: ${fileUri.fsPath}`);
                    const document = await vscode.workspace.openTextDocument(fileUri);
                    
                    await vscode.window.showTextDocument(document, { preview: false });
                    console.log(`ArchKnow: Successfully opened ${fileUri.fsPath}. Triggering getContextForFile command.`);
                    setTimeout(() => {
                        vscode.commands.executeCommand('archknow.getContextForFile');
                    }, 500);
                } catch (error: any) {
                    console.error(`ArchKnow: Error handling cursor://.../file URI: ${error}`);
                    vscode.window.showErrorMessage(`ArchKnow: Failed to open file from cursor:// URI: ${error.message}`);
                }
            } else {
                vscode.window.showWarningMessage(`ArchKnow: Received unhandled cursor://archknow.archknow URI path: ${path}`);
            }
        } else {
            vscode.window.showWarningMessage(`ArchKnow: ArchKnowUriHandler received URI with unhandled scheme or authority: ${uri.toString()}`);
        }
    }
}