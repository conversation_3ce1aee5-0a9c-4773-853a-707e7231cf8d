export const AI_AGENT_PROTOCOL_FILE_NAME = 'AI_AGENT_IMPLEMENTATION_PROTOCOL.md';

export const AI_AGENT_PROTOCOL_CONTENT = `# AI Agent Implementation Protocol

**Preamble:**
These instructions guide the AI agent in implementing features and changes outlined in an accompanying design document with rigorous quality control, risk assessment, and scope validation. The primary objectives are:
1. All development work occurs on a dedicated feature branch with milestone-driven progression
2. Changes are broken down into small, logical, and review-friendly atomic commits
3. Each milestone undergoes thorough scope validation and risk assessment before integration
4. Each defined milestone can be independently and safely integrated into the primary development branch upon completion
5. All deviations from planned scope are explicitly tracked and justified
6. Critical risks (security, performance, UX) are identified and categorized as new vs. existing

---

## Phase 0: Preparation & Milestone Definition

1. **Analyze Design Document:** Thoroughly review the provided design document to understand its goals, scope, technical details, and architectural decisions.
2. **Identify/Define Milestones:**
   * If the design document explicitly defines clear, sequential milestones with success criteria, proceed with those.
   * If milestones are not clearly defined or are too broad, the first task is to propose a logical breakdown of the work into smaller, distinct, implementable, and independently testable milestones. Each proposed milestone must have clear success criteria.
   * **Action:** If milestones were self-defined, seek confirmation/approval for this breakdown before proceeding with implementation.
3. **Establish Baseline Risk Profile:** Document the current system's risk profile in areas that will be modified (security, performance, UX patterns).

---

## Phase 1: Project Setup

1. **1.1. Identify Primary Branch:** Determine the project's main development line (eg'main, master). All integrations will ultimately target this branch.
2. **1.2. Create Main Feature Branch:**
   * Create a new branch from the latest state of the primary branch.
   * **Naming Convention:** 'feature/<design-doc-identifier-or-short-title>' (e.g., 'feature/adyen-fallback-provider'). This branch will accumulate all milestone work for this design document.

---

## Phase 2: Milestone-Driven Implementation Cycle (Repeat for each milestone)

### 2.1. Pre-Implementation Planning
1. **Create Milestone Branch:**
   * Ensure your main feature branch ('feature/<design-doc-identifier>') is up-to-date with its remote counterpart.
   * Create a new branch *from the current state of your main feature branch*.
   * **Naming Convention:** '<main-feature-branch-name>/milestone-<N>-<short-milestone-description>'

2. **Milestone Scope Validation:**
   * Review the specific deliverables, success criteria, and verification requirements for the current milestone
   * Identify all files that will be modified or created
   * Map each deliverable to specific implementation tasks
   * Document expected integration points with existing system components

### 2.2. Implementation Phase
1. **Focus on Milestone Requirements:**
   * Switch to the milestone branch and focus exclusively on implementing the requirements, tasks, and success criteria defined for the current milestone in the design document.
   * Pay close attention to any "Developer Guidance," "Related Files," or specific technical instructions mentioned in the design document relevant to this milestone.

2. **Develop with Atomic Commits:**
   * Break down the implementation work for the milestone into the smallest possible logical, atomic commits. Each commit should represent a single, complete unit of change.
   * **Commit Message Convention:** Use clear, concise, and descriptive commit messages (e.g., follow Conventional Commits format: \`feat: implement payment abstraction interface\`, \`fix: resolve issue in Stripe webhook handler\`, \`test: add unit tests for Adyen client\`).

3. **Maintain Code Quality Standards:**
   * Write comprehensive unit tests for all new or modified functionality. Ensure all existing and new tests pass.
   * Update or create any necessary documentation (e.g., code comments, README updates, API documentation).
   * Adhere strictly to the project's existing coding standards, style guides, and linting/formatting rules.
   * Follow established architectural patterns and decisions referenced in the design document.

### 2.3. Pre-Integration Validation (CRITICAL STEP)

**Before proceeding to integration, MANDATORY evaluation of staged changes:**

1. **Stage All Changes:** Use 'git add .' to stage all milestone changes for evaluation.

2. **Generate Milestone Completion Report:**
   * **Scope Compliance Assessment:**
     * Compare implemented deliverables against planned milestone requirements
     * Identify any planned features that were not implemented
     * Identify any additional features that were implemented beyond scope
     * Rate scope compliance: COMPLETE | PARTIAL | EXCEEDED | DEVIATED
   
   * **Critical Risk Analysis:**
     * **Security Risks:** Authentication bypasses, authorization issues, data exposure, input validation gaps, injection vulnerabilities
     * **Performance Risks:** Memory leaks, inefficient queries, blocking operations, resource exhaustion, scalability bottlenecks  
     * **UX Risks:** Accessibility issues, confusing workflows, error handling gaps, loading states, responsive design problems
     * **Integration Risks:** Breaking changes, API compatibility, dependency conflicts, data migration issues
     * **Categorize each risk:** NEW RISK (introduced by this milestone) | EXISTING RISK (already present in codebase) | MITIGATED RISK (existing risk reduced)

   * **Deviation Documentation:**
     * List all deviations from the original milestone plan with justifications
     * Document any architectural decisions that differ from the design document
     * Note any implementation shortcuts or technical debt introduced

   * **Integration Readiness Checklist:**
     * [ ] All milestone deliverables implemented
     * [ ] All tests pass (existing + new)
     * [ ] No critical security risks introduced
     * [ ] Performance benchmarks maintained
     * [ ] Breaking changes documented
     * [ ] Database migrations tested
     * [ ] Feature flags properly implemented
     * [ ] Error handling comprehensive

3. **Milestone Independence Verification:**
   * Critically assess whether the changes for this milestone, once merged, would leave the application in a stable, functional, and deployable state for the functionality covered by this milestone.
   * The milestone should not have hard dependencies on *future, un-merged* milestones.
   * Verify that feature gating properly isolates incomplete functionality.

---

## Phase 3: Milestone Review and Integration Cycle (Repeat for each milestone)

### 3.1. Quality Assurance Review
1. **Code Review (Simulated or Actual):**
   * The changes should be reviewed against the design document, milestone requirements, coding standards, and test coverage.
   * Validate that all identified risks have appropriate mitigations or are acceptable for integration.

2. **Risk Mitigation Planning:**
   * For any NEW RISKS identified, document mitigation strategies
   * For EXISTING RISKS that remain, note if they are acceptable or require future addressing
   * Ensure no critical security vulnerabilities are being introduced

### 3.2. Pull Request and Integration
1. **Pull Request (PR) to Main Feature Branch:**
   * Once the milestone implementation is complete and validated on its branch, create a Pull Request (PR) from the milestone branch to the main feature branch ('feature/<design-doc-identifier>').
   * **PR Title:** Clearly indicate the milestone number and its purpose (e.g., "Feat(Milestone 1): Implement Payment Provider Abstraction Layer").
   * **PR Description Must Include:**
     * Concise summary of the changes made for this milestone
     * Link to the relevant section(s) of the design document
     * Instructions on how to test or verify the changes
     * **MANDATORY Milestone Metadata:**
       * Implementation Plan Path: <path/to/your_implementation_plan.md>
       * Design Document Path: <path/to/your_design_document.md>
       * Scope Compliance: [COMPLETE|PARTIAL|EXCEEDED|DEVIATED] with details
       * Milestone Deviations Summary: [List any deviations from the design doc or implementation plan for this milestone and concise reasons, or state "No deviations."]
       * Critical Risks Introduced: [List NEW RISKS with severity levels, or state "No new critical risks."]
       * Existing Risks Status: [List any EXISTING RISKS that were modified/mitigated, or state "No existing risks affected."]
       * Integration Dependencies: [List any dependencies on other milestones or external systems]

2. **Merge to Main Feature Branch:**
   * After the PR is approved (or self-validated), merge the milestone branch into the main feature branch.
   * Delete the (now merged) milestone branch (e.g., 'feature/adyen-fallback-provider/milestone-1-abstraction-layer').

3. **Integrate Completed Milestone into Primary Branch:**
   * The objective is to integrate each milestone into the primary development branch ('main' or 'master') as it's completed and stabilized on the main feature branch.
   * **Steps for Integration:**
     1. **Synchronize:** Ensure your local main feature branch is fully synchronized with the latest state of the *remote primary development branch*.
     2. **Create PR to Primary:** Create a new Pull Request from your (now updated) main feature branch to the primary development branch.
     3. **PR Details:** Include the same milestone metadata as above, plus integration impact assessment.
     4. **Review and Merge:** After this PR is reviewed and merged into the primary development branch by the project maintainers, the milestone is officially integrated.
     5. **Post-Merge Sync:** After the merge to primary is complete, ensure your local main feature branch is again updated to reflect the state of the primary branch.

---

## Phase 4: Finalization

1. **4.1. All Milestones Complete:** Repeat Phase 2 and Phase 3 for all defined milestones.
2. **4.2. Final Integration Assessment:**
   * Conduct end-to-end testing of the complete feature
   * Validate that all milestones work together cohesively
   * Confirm that no critical risks were introduced during the full implementation
   * Document any technical debt or future improvement opportunities
3. **4.3. Cleanup:** Once the main feature branch has been fully merged into the primary development branch and all changes are live/verified, the main feature branch (feature/<design-doc-identifier>) can be deleted.

---

## Critical Success Factors

1. **Never compromise on the pre-integration validation step** - this is where quality is assured
2. **Document all deviations with clear justifications** - transparency is key for maintainability
3. **Categorize risks appropriately** - distinguish between new problems and existing issues
4. **Maintain independent milestone integration capability** - each milestone should be deployable
5. **Follow established architectural patterns** - consistency reduces integration risks
6. **Comprehensive testing at each milestone** - don't accumulate testing debt`;