import * as vscode from 'vscode';
import { GitService } from './services/gitService';
import { ApiService } from './services/apiService';
import { UserService } from './services/userService';
import { DomainConceptsService } from './services/domainConceptsService';
import { DesignDocWorkflowService } from './services/designDocWorkflowService';
import { WebviewManager } from './ui/webviewManager';
import { StatusBarManager } from './ui/statusBarManager';
import { ConfigManager } from './config/configManager';
import { CommandHandlers } from './commands/commandHandlers';
import { ArchKnowUriHandler } from './handlers/uriHandler';
import { handleRequestAiReview, handleAddressAiComment, handleUpdateCommentStatus } from './commands/aiReviewCommands';
import { McpBridgeServer } from './mcp-bridge';

// Global extension context for access across modules
let globalExtensionContext: vscode.ExtensionContext;
let mcpBridgeServer: McpBridgeServer;

export async function activate(context: vscode.ExtensionContext) {
    console.log('ArchKnow Companion is now active!');
    
    // Store context globally
    globalExtensionContext = context;

    // Start the MCP bridge server
    mcpBridgeServer = new McpBridgeServer(context);
    mcpBridgeServer.start();

    // Initialize services
    const gitService = new GitService();
    const userService = new UserService(context);
    const statusBarManager = new StatusBarManager();
    const webviewManager = new WebviewManager(context);

    // Get workspace and initialize paths
    const workspaceFolders = vscode.workspace.workspaceFolders;
    let workspacePath: string | undefined;
    if (workspaceFolders && workspaceFolders.length > 0) {
        workspacePath = workspaceFolders[0].uri.fsPath;
    } else {
        vscode.window.showWarningMessage('ArchKnow: No workspace folder open. Some features might be unavailable.');
    }

    // Initialize configuration-dependent services
    const config = ConfigManager.getConfig();
    let apiService: ApiService | undefined;
    let domainConceptsService: DomainConceptsService | undefined;
    let designDocWorkflowService: DesignDocWorkflowService | undefined;

    if (config.apiKey) {
        apiService = new ApiService(config.apiUrl, config.apiKey);
        await apiService.validateApiKey();
    } else {
        const newApiKey = await ConfigManager.promptAndSaveApiKey();
        if (newApiKey) {
            apiService = new ApiService(config.apiUrl, newApiKey);
            await apiService.validateApiKey();
        }
    }

    if (workspacePath) {
        // Initialize workspace-dependent services
        if (apiService) {
            domainConceptsService = new DomainConceptsService(workspacePath, apiService);
        }
        
        const workspaceUri = vscode.Uri.file(workspacePath);
        designDocWorkflowService = new DesignDocWorkflowService(workspaceUri, apiService);
    }

    // Ensure we have all required services
    if (!apiService) {
        apiService = new ApiService(config.apiUrl, ''); // Dummy API service for structure
    }
    if (!domainConceptsService && workspacePath) {
        domainConceptsService = new DomainConceptsService(workspacePath, apiService);
    }
    if (!designDocWorkflowService && workspacePath) {
        const workspaceUri = vscode.Uri.file(workspacePath);
        designDocWorkflowService = new DesignDocWorkflowService(workspaceUri, apiService);
    }

    // Initialize command handlers with dependencies
    const commandHandlers = new CommandHandlers({
        gitService,
        apiService,
        userService,
        domainConceptsService: domainConceptsService!,
        designDocWorkflowService: designDocWorkflowService!,
        webviewManager,
        context
    });

    // Initialize Git repository information
    await gitService.initializeGitRepoInfo();

    // Register URI handler
    context.subscriptions.push(vscode.window.registerUriHandler(new ArchKnowUriHandler()));

    // Register status bar manager
    statusBarManager.registerEventListeners(context);

    // Register workspace change handler to reinitialize services
    context.subscriptions.push(vscode.workspace.onDidChangeWorkspaceFolders(async () => {
        console.log("ArchKnow: Workspace folders changed, re-initializing git info.");
        await gitService.initializeGitRepoInfo();
    }));

    // Ensure AI Agent Protocol file exists - DISABLED to prevent downloading to target repository
    // await commandHandlers.ensureAiAgentProtocolFile();

    // Setup GitHub user handle
    const gitInstance = gitService.getGitInstance();
    let userHandle = await userService.getGitHubUserHandle(gitInstance || undefined);

    if (!userHandle) {
        console.log('ArchKnow: GitHub handle not found after initial check, prompting user.');
        userHandle = await userService.promptForGitHubHandle();
    }

    // Fetch and refresh domain concepts if we have the necessary services
    if (domainConceptsService && config.apiKey) {
        const repoInfo = gitService.getRepoInfo();
        if (repoInfo) {
            vscode.window.showInformationMessage(`Detected repository: ${repoInfo.slug}`);
         //   await domainConceptsService.fetchAndStoreDomainConceptsForRepo(repoInfo.slug);
        } else {
            vscode.window.showWarningMessage(`ArchKnow: Could not get repository slug/root. Domain concepts cannot be fetched.`);
        }
    }

    // Register all commands
    registerCommands(context, commandHandlers);

    console.log('ArchKnow extension is now active!');
    console.log('ArchKnow API URL:', config.apiUrl);

    // Listener for opening text documents (disabled auto-context functionality)
    vscode.workspace.onDidOpenTextDocument(async (document: vscode.TextDocument) => {
        // Auto-context functionality disabled - now available via right-click context menu
        // if (document && !document.uri.fsPath.includes('.git')) {
        //     commandHandlers.handleFileOpen(document);
        // }
    });

    // Prompt to install MCP server if it's the first time
    if (!context.globalState.get('archknow.mcp.prompted')) {
        promptToInstallMcpServer(context);
    }
}

function registerCommands(context: vscode.ExtensionContext, handlers: CommandHandlers): void {
    // Core functionality commands
    context.subscriptions.push(
        vscode.commands.registerCommand('archknow.installMcpServer', () => promptToInstallMcpServer(context)),
        vscode.commands.registerCommand('archknow.validateApiKey', () => handlers.validateApiKey()),
        vscode.commands.registerCommand('archknow.getContextForFile', () => handlers.getContextForFile()),
        vscode.commands.registerCommand('archknow.showContextForFile', (uri?: vscode.Uri) => {
            // New command for showing context for a specific file (right-click context menu)
            if (uri) {
                // If called from context menu with URI
                return handlers.getContextForSpecificFile(uri);
            } else {
                // If called without URI, use active editor
                return handlers.getContextForFile();
            }
        }),
        vscode.commands.registerCommand('archknow.getDecisionsByConcepts', () => handlers.getDecisionsByConcepts()),
        vscode.commands.registerCommand('archknow.openContextGenerator', () => handlers.openContextGenerator()),
        vscode.commands.registerCommand('archknow.syncDomainConcepts', () => handlers.syncDomainConcepts()),
        vscode.commands.registerCommand('archknow.getFeedback', () => handlers.getFeedback()),
        vscode.commands.registerCommand('archknow.generateDesignDoc', () => handlers.generateDesignDoc()),
        vscode.commands.registerCommand('archknow.startTask', () => handlers.startTask()),
        vscode.commands.registerCommand('archknow.openDesignDoc', (uri) => handlers.openDesignDoc(uri)),
        vscode.commands.registerCommand('archknow.showDesignDocJobsStatus', () => handlers.showDesignDocJobsStatus()),
        vscode.commands.registerCommand('archknow.showDecisionById', (decisionId) => handlers.showDecisionById(decisionId)),
        vscode.commands.registerCommand('archknow.showDomainConceptsTree', async () => {
            // Access services via handlers.deps.serviceName
            const domainConceptsService = (handlers as any).deps?.domainConceptsService;
            const webviewManager = (handlers as any).deps?.webviewManager;

            if (domainConceptsService && webviewManager) {
                try {
                    const treeData = await domainConceptsService.getTreeDataFromDomainConceptsCsv();
                    if (treeData && treeData.length > 0) {
                        webviewManager.createOrShowDomainConceptsTreePanel(treeData);
                    } else {
                        vscode.window.showInformationMessage('ArchKnow: No domain concepts data found to display in the tree.');
                    }
                } catch (error: any) {
                    vscode.window.showErrorMessage(`ArchKnow: Error preparing domain concepts tree: ${error.message}`);
                    console.error('ArchKnow: Error showing domain concepts tree:', error);
                }
            } else {
                vscode.window.showErrorMessage('ArchKnow: DomainConceptsService or WebviewManager not available through command handler dependencies.');
                 console.error('ArchKnow: deps object or services not found on CommandHandlers instance:', (handlers as any).deps);
            }
        }),
        vscode.commands.registerCommand('archknow.reviewMilestone', () => handlers.reviewMilestone())
    );

    // Configuration commands
    context.subscriptions.push(
        vscode.commands.registerCommand('archknow.configureApiKey', () => handlers.configureApiKey())
    );

    // Design document workflow commands
    context.subscriptions.push(
        vscode.commands.registerCommand('archknow.generateImplementationPlan', async (docUri?: vscode.Uri) => {
            // This is now legacy, consider marking as such or providing different UX
            vscode.window.showWarningMessage('This command generates a plan from Phase 3 DB output. For finalized docs, use the context menu option.');
            
            const designDocWorkflowService = (handlers as any).deps?.designDocWorkflowService;
            if (!designDocWorkflowService) {
                vscode.window.showErrorMessage('ArchKnow: DesignDocWorkflowService is not available.');
                return;
            }
            // Original logic for legacy command needs to be preserved if it calls a different backend/logic
            // For now, let's assume it might need a specific Job ID input differently
            const jobId = await vscode.window.showInputBox({ prompt: "Enter the Job ID for which to generate the implementation plan (legacy flow)." });
            if (jobId) {
                await designDocWorkflowService.generateImplementationPlanLegacy(jobId); // Expects a method that directly triggers based on JobID from DB
            } else {
                vscode.window.showInformationMessage('Implementation plan generation cancelled.');
            }
        }),
        vscode.commands.registerCommand('archknow.generateImplementationPlanFromFinalizedDoc', async (docUri?: vscode.Uri) => {
            const designDocWorkflowService = (handlers as any).deps?.designDocWorkflowService;
            if (!designDocWorkflowService) {
                vscode.window.showErrorMessage('ArchKnow: DesignDocWorkflowService is not available.');
                console.error('ArchKnow: DesignDocWorkflowService not found on CommandHandlers instance:', (handlers as any).deps);
                return;
            }

            if (!docUri) {
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor && activeEditor.document.languageId === 'markdown' && 
                    activeEditor.document.uri.fsPath.includes('.archknow/design_docs/ready_for_implementation/')) {
                    docUri = activeEditor.document.uri;
                } else {
                    vscode.window.showErrorMessage('ArchKnow: Please run this command from a Markdown file within the ".archknow/design_docs/ready_for_implementation/" directory, or right-click the file in the explorer.');
                    return;
                }
            }
            // Forward to the service method
            await designDocWorkflowService.finalizeAndGenerateImplementationPlan(docUri);
        })
    );

    // AI Review commands
    context.subscriptions.push(
        vscode.commands.registerCommand('archknow.requestAiReview', (docUri?: vscode.Uri) => {
            if (!docUri) {
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor && activeEditor.document.languageId === 'markdown') {
                    docUri = activeEditor.document.uri;
                } else {
                    vscode.window.showErrorMessage('No design document specified or active editor is not a Markdown file.');
                    return;
                }
            }
            return handleRequestAiReview(docUri);
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('archknow.addressAiComment', (docUri: vscode.Uri, commentIds: string[]) => {
            return handleAddressAiComment(docUri, commentIds);
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('archknow.updateCommentStatus', (docUri: vscode.Uri, commentId: string, newStatus: string, resolutionText?: string) => {
            return handleUpdateCommentStatus(docUri, commentId, newStatus as 'addressed' | 'wont-fix' | 'pending-ai-addressal', resolutionText);
        })
    );

    // Listener for opening text documents (disabled auto-context functionality)
    vscode.workspace.onDidOpenTextDocument(async (document: vscode.TextDocument) => {
        // Auto-context functionality disabled - now available via right-click context menu
        // if (document && !document.uri.fsPath.includes('.git')) {
        //     commandHandlers.handleFileOpen(document);
        // }
    });
}

function promptToInstallMcpServer(context: vscode.ExtensionContext) {
    // Mark that we've prompted the user so we don't ask again.
    context.globalState.update('archknow.mcp.prompted', true);

    const mcpServerScriptPath = vscode.Uri.joinPath(context.extensionUri, 'out', 'mcp_server', 'server.js').fsPath;

    const mcpConfig = {
        "mcpServers": {
            "archknow-mcp": {
                "command": "node",
                "args": [
                    mcpServerScriptPath
                ]
            }
        }
    };

    const configString = JSON.stringify(mcpConfig, null, 2);

    vscode.window.showInformationMessage(
        'To use ArchKnow with Cursor, please add the following configuration to your MCP settings file (~/.cursor/mcp.json).',
        { modal: true },
        'Copy Configuration'
    ).then(selection => {
        if (selection === 'Copy Configuration') {
            vscode.env.clipboard.writeText(configString);
            vscode.window.showInformationMessage('ArchKnow MCP server configuration copied to clipboard.');
        }
    });
}

export function deactivate() {
    console.log('ArchKnow extension deactivated');
    if (mcpBridgeServer) {
        mcpBridgeServer.stop();
    }
}