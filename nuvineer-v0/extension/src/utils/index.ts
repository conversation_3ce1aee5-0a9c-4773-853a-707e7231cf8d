import * as vscode from 'vscode';
import * as path from 'path';

export function escapeHtml(unsafe: string): string {
    if (!unsafe) return '';
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

export function getNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}

export function isValidGitHubHandle(handle: string): boolean {
    if (!handle || handle.trim() === '') return false;
    const regex = /^[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*$/;
    if (!regex.test(handle)) return false;
    if (handle.length > 39) return false;
    return true;
}

export function getRepoSlug(remoteUrl: string): string | null {
    if (!remoteUrl) {
        return null;
    }
    const match = remoteUrl.match(/(?:[:\/])([^\/]+\/[^\/.]+)(?:\.git)?$/);
    return match ? match[1] : null;
}

export function linkifyDecisionIds(content: string, referencedDecisions: any[]): string {
    if (!content || !referencedDecisions || !referencedDecisions.length) {
        return content;
    }
    
    let linkedContent = content;
    
    const decisionMap = new Map();
    referencedDecisions.forEach(decision => {
        const id = decision.id || 'unknown';
        decisionMap.set(id, decision);
    });
    
    const decisionIdRegex = /\b(ID:\s*|decision[\s-]ID:\s*|ID\s+|decision[\s-]ID\s+|[(]ID:?\s*|[(]decision[\s-]ID:?\s*)(decision_[a-zA-Z0-9_]+)\b/gi;
    
    linkedContent = linkedContent.replace(decisionIdRegex, (match, prefix, decisionId) => {
        if (decisionMap.has(decisionId)) {
            return `${prefix}[${decisionId}](#referenced-decisions)`;
        }
        return match;
    });
    
    const standaloneDecisionIdRegex = /\b(decision_[a-zA-Z0-9_]+)\b/g;
    linkedContent = linkedContent.replace(standaloneDecisionIdRegex, (match, decisionId) => {
        if (decisionMap.has(decisionId)) {
            return `[${decisionId}](#referenced-decisions)`;
        }
        return match;
    });
    
    return linkedContent;
}

export async function ensureDirectory(dirPath: string): Promise<void> {
    const dirUri = vscode.Uri.file(dirPath);
    try {
        await vscode.workspace.fs.createDirectory(dirUri);
    } catch (error: any) {
        if (error.code !== 'FileExists') {
            throw error;
        }
    }
}