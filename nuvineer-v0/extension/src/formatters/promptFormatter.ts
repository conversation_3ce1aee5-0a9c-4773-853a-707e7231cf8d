import { Decision, Risk } from '../services/apiService';
import { escapeHtml } from '../utils';

export class PromptFormatter {
    static formatDecisionsForPrompt(
        decisions: Decision[], 
        type: 'file' | 'concepts' | 'prompt', 
        contextValue: string | string[]
    ): string {
        if (!decisions || decisions.length === 0) {
            const contextString = Array.isArray(contextValue) ? contextValue.join(', ') : contextValue;
            return `No specific ArchKnow decisions found for the selected context (${type}: ${contextString}).\nProceed with your request, keeping general best practices in mind.\n\n---\n**My Request:**\n[Your prompt here]`;
        }

        // Aggregate all developer guidance
        let aggregatedDevGuidance = new Set<string>();
        decisions.forEach(d => {
            if (d.metadata?.dev_prompt && typeof d.metadata.dev_prompt === 'string') {
                const trimmedPrompt = d.metadata.dev_prompt.trim();
                if (trimmedPrompt !== '') {
                    aggregatedDevGuidance.add(trimmedPrompt);
                }
            }
        });

        // Aggregate all risks by severity
        let highRisks: {decision: string; description: string; migration?: string}[] = [];
        let mediumRisks: {decision: string; description: string; migration?: string}[] = [];

        decisions.forEach(d => {
            const title = d.metadata?.title || `Decision ID: ${d.id}`;
            try {
                let risks: Risk[] | null = null;
                const risksData = d.metadata?.risks_extracted;

                if (Array.isArray(risksData)) {
                    risks = risksData as Risk[];
                } else if (typeof risksData === 'string' && risksData.trim().length > 2) {
                    try {
                        risks = JSON.parse(risksData);
                    } catch (parseError) {
                        console.error(`ArchKnow [Extension]: Error parsing risks_extracted JSON string for ${d.id}:`, parseError);
                        risks = null;
                    }
                }

                if (Array.isArray(risks)) {
                    risks.filter(r => r?.severity?.toLowerCase() === 'high').forEach(risk => {
                        highRisks.push({
                            decision: title,
                            description: risk.description || 'No description',
                            migration: risk.migration
                        });
                    });
                    
                    risks.filter(r => r?.severity?.toLowerCase() === 'medium').forEach(risk => {
                        mediumRisks.push({
                            decision: title,
                            description: risk.description || 'No description',
                            migration: risk.migration
                        });
                    });
                }
            } catch (e) {
                console.error(`ArchKnow [Extension]: Unexpected error processing risks for decision ${d.id}:`, e);
            }
        });

        // Start building the final content
        let output = '';
        
        // Add aggregated developer guidance
        if (aggregatedDevGuidance.size > 0) {
            output += `## Developer Guidance (Must Follow)\n\n`;
            aggregatedDevGuidance.forEach(guidance => {
                output += `* ${guidance}\n`;
            });
            output += `\n---\n\n`;
        }
        
        // Add aggregated risks
        if (highRisks.length > 0 || mediumRisks.length > 0) {
            output += `## Known Risks\n\n`;
            
            if (highRisks.length > 0) {
                output += `### High Severity Risks\n\n`;
                highRisks.forEach(risk => {
                    output += `* ${risk.description}\n`;
                    if (risk.migration) output += `  *Mitigation:* ${risk.migration}\n`;
                });
                output += `\n`;
            }
            
            if (mediumRisks.length > 0) {
                output += `### Medium Severity Risks\n\n`;
                mediumRisks.forEach(risk => {
                    output += `* ${risk.description}\n`;
                    if (risk.migration) output += `  *Mitigation:* ${risk.migration}\n`;
                });
                output += `\n`;
            }
            
            output += `---\n\n`;
        }

        // Generate decision summaries
        const decisionSummaries = decisions.map(d => {
            const title = d.metadata?.title || `Decision ID: ${d.id}`;
            let summary = `### ${title}\n`; 
            const decisionLinkUrl = `cursor://archknow.archknow/viewDecision?id=${encodeURIComponent(d.id)}`;
            summary += `[${decisionLinkUrl}](${decisionLinkUrl})\n\n`;

            // Add Relevance Reason if available (from relevant-to-prompt)
            if (d.metadata?.relevance_reason) {
                summary += `**Relevance:** ${d.metadata.relevance_reason}\n\n`;
            }

            // Optional: Add date and PR link if available
            if (d.metadata?.pr_merged_at) {
                 try {
                    const date = new Date(d.metadata.pr_merged_at);
                    summary += `Merged: ${date.toISOString().split('T')[0]}\n`;
                } catch (e) {}
            }
            
            if (d.metadata?.pr_number) {
                 summary += `PR: ${d.metadata.pr_number}\n`;
            }

            if (d.metadata?.description) {
                summary += `Description:\n\`\`\`\n${d.metadata.description}\n\`\`\`\n\n`; 
            }
            
            if (d.metadata?.rationale) {
                summary += `Rationale:\n\`\`\`\n${d.metadata.rationale}\n\`\`\`\n\n`; 
            }

            // Add Related Files
            if (Array.isArray(d.metadata?.related_files) && d.metadata.related_files.length > 0) {
                summary += `Related Files:\n`;
                d.metadata.related_files.forEach((file: string) => {
                    summary += `* \`${file}\`\n`; 
                });
                summary += `\n`;
            }

            return summary;
        }).join('\n---\n\n'); // Use horizontal rule between decisions

        // Combine everything
        output += `${decisionSummaries}\n\n---\n**My Request:**\n[Your prompt here]`; 
        return output;
    }
}