import express from 'express';
import * as http from 'http';
import * as vscode from 'vscode';
import { ApiService } from './services/apiService';
import { ConfigManager } from './config/configManager';
import { WebviewManager } from './ui/webviewManager';

export class McpBridgeServer {
    private app: express.Express;
    private server: http.Server | null = null;
    private apiService: ApiService;
    private webviewManager: WebviewManager;

    constructor(context: vscode.ExtensionContext) {
        this.app = express();
        this.app.use(express.json());
        
        const config = ConfigManager.getConfig();
        // This assumes that the API key has been validated and is available.
        // The main extension activation should ensure this.
        this.apiService = new ApiService(config.apiUrl, config.apiKey || '');
        this.webviewManager = new WebviewManager(context);

        this.setupRoutes();
    }

    private setupRoutes() {
        this.app.post('/api/issue-details', async (req, res) => {
            const { issueUrl } = req.body;
            console.log(`[MCP Bridge] Received request for issue: ${issueUrl}`);

            if (!issueUrl) {
                return res.status(400).json({ success: false, error: 'issueUrl is required' });
            }
            
            // The API service needs a new method to handle this call.
            // For now, let's define the expected interaction.
            try {
                // This is a new method we need to add to ApiService
                const result = await this.apiService.fetchIssueDetailsForMcp(issueUrl);

                if (result.success) {
                    res.json({ success: true, details: result.details });
                } else {
                    res.status(500).json({ success: false, error: result.error });
                }
            } catch (error: any) {
                console.error('[MCP Bridge] Error calling backend:', error);
                res.status(500).json({ success: false, error: 'Failed to fetch issue details from backend.' });
            }
        });

        this.app.post('/api/milestone-review', async (req, res) => {
            const { 
                repoSlug, 
                codeChanges, 
                designDoc, 
                implementationPlan, 
                milestoneId, 
                existingContext, 
                issueUrl,
                reviewIteration,
                previouslyAcceptedRisks,
                forceMinimalReview
            } = req.body;
            console.log(`[MCP Bridge] Received milestone review request for repo: ${repoSlug}, milestone: ${milestoneId}`);

            if (!repoSlug || !codeChanges || !designDoc || !implementationPlan) {
                return res.status(400).json({ 
                    success: false, 
                    error: 'repoSlug, codeChanges, designDoc, and implementationPlan are required' 
                });
            }
            
            try {
                const result = await this.apiService.fetchMilestoneReviewForMcp(
                    repoSlug, 
                    codeChanges, 
                    designDoc, 
                    implementationPlan, 
                    milestoneId,  // Correct parameter order: milestoneId first
                    undefined,    // milestoneTitle - auto-generated from implementation plan
                    existingContext,
                    issueUrl,
                    reviewIteration,
                    previouslyAcceptedRisks,
                    forceMinimalReview
                );

                res.json({ success: true, result });
            } catch (error: any) {
                console.error('[MCP Bridge] Error calling milestone review backend:', error);
                res.status(500).json({ 
                    success: false, 
                    error: `Failed to fetch milestone review: ${error.message}` 
                });
            }
        });

        this.app.post('/api/milestone-review-webview', async (req, res) => {
            const { 
                repoSlug, 
                codeChanges, 
                designDoc, 
                implementationPlan, 
                milestoneId, 
                existingContext, 
                issueUrl,
                reviewIteration,
                previouslyAcceptedRisks,
                forceMinimalReview,
                designSessionId
            } = req.body;
            console.log(`[MCP Bridge] Received milestone review webview request for repo: ${repoSlug}, milestone: ${milestoneId}${issueUrl ? `, issue: ${issueUrl}` : ''}`);

            if (!repoSlug || !codeChanges || !designDoc || !implementationPlan) {
                return res.status(400).json({ 
                    success: false, 
                    error: 'repoSlug, codeChanges, designDoc, and implementationPlan are required' 
                });
            }
            
            try {
                const result = await this.apiService.fetchMilestoneReviewForMcp(
                    repoSlug, 
                    codeChanges, 
                    designDoc, 
                    implementationPlan, 
                    milestoneId,  // Correct parameter order: milestoneId first
                    undefined,    // milestoneTitle - auto-generated from implementation plan
                    existingContext,
                    issueUrl,
                    reviewIteration,
                    previouslyAcceptedRisks,
                    forceMinimalReview,
                    designSessionId
                );

                // Show the result in the webview
                // Use milestone title from API response, fallback to milestone ID
                const milestoneTitle = result.milestone_context?.title || result.task_context?.task_title || `Milestone ${milestoneId}`;
                const panelTitle = `MCP Milestone Review - ${milestoneTitle}`;
                this.webviewManager.createOrShowStreamlinedFeedbackPanel(result, panelTitle, undefined, 'mcp-generated');

                // Also return the result for the MCP response
                res.json({ success: true, result, webview_opened: true });
            } catch (error: any) {
                console.error('[MCP Bridge] Error calling milestone review backend for webview:', error);
                res.status(500).json({ 
                    success: false, 
                    error: `Failed to fetch milestone review: ${error.message}` 
                });
            }
        });
    }

    public start() {
        const port = 7635; // The port our MCP server will call
        this.server = this.app.listen(port, () => {
            console.log(`[MCP Bridge] Server listening on http://localhost:${port}`);
        });

        this.server.on('error', (error) => {
            console.error('[MCP Bridge] Server failed to start:', error);
        });
    }

    public stop() {
        if (this.server) {
            this.server.close(() => {
                console.log('[MCP Bridge] Server stopped.');
            });
        }
    }
} 