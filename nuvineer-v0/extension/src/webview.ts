import * as vscode from 'vscode';
import { DesignDocMetadata, DesignDocFeedbackItem, AiReviewComment } from './types';
import { escapeHtml, getNonce } from './utils';
import { marked } from 'marked';
import hljs from 'highlight.js';
import * as fs from 'fs';
import * as path from 'path';

// Helper function to render the existing comments
function renderExistingComments(feedback: DesignDocFeedbackItem[], aiComments: AiReviewComment[] = [], documentStatus?: string): string {
    if ((!feedback || feedback.length === 0) && (!aiComments || aiComments.length === 0)) {
        return '<p>No comments yet.</p>';
    }
    
    // Convert AI comments to a format similar to feedback items for unified rendering
    const aiCommentsAsItems = aiComments.map(ai => ({
        type: 'ai_comment',
        user: 'AI Review',
        timestamp: ai.timestamp || new Date().toISOString(),
        comment: ai.text,
        category: ai.type,
        status: ai.status,
        id: ai.id,
        suggestedSection: ai.suggestedSection,
        resolutionText: ai.resolutionText,
        resolutionAuthor: ai.resolutionAuthor,
        resolutionTimestamp: ai.resolutionTimestamp || new Date().toISOString()
    }));

    // Combine and sort all comments by timestamp
    const allComments = [...feedback, ...aiCommentsAsItems]
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    return allComments
        .map(item => {
            const isAiComment = item.type === 'ai_comment';
            const hasSelection = !isAiComment && 'selection' in item && item.selection && item.selection.selectedText;
            const categoryClass = item.category ? `category-${item.category}` : '';
            const categoryLabel = item.category ? item.category.replace('-', ' ') : '';
            
            // Determine if comment can be addressed by AI
            const isAddressable = isAiComment && (item.status === 'open' || item.status === 'pending-ai-addressal');
            const isAddressed = item.status === 'addressed';
            
            // Special handling for documents that should show addressed comments visually
            const shouldShowAddressed = documentStatus === 'changes-requested' || 
                                      documentStatus === 'under_review' || 
                                      documentStatus === 'ready_for_implementation';
            const showAsAddressed = shouldShowAddressed && isAddressed;
            
            return `
                <div class="comment-card ${isAiComment ? 'ai-comment' : ''} ${categoryClass} ${showAsAddressed ? 'comment-addressed' : ''}" ${isAiComment ? `data-comment-id="${item.id}"` : ''}>
                    <div class="comment-header">
                        <div class="comment-user">
                            ${isAiComment ? '<span class="ai-badge">AI</span> ' : '@'}${escapeHtml(item.user)}
                        </div>
                        <div class="comment-timestamp">${new Date(item.timestamp).toLocaleString()}</div>
                        ${isAiComment && isAddressable && documentStatus === 'changes-requested' ? `
                        <label class="comment-select">
                            <input type="checkbox" class="comment-checkbox" data-action="update-selected-comments">
                            Select for AI addressing
                        </label>
                        ` : ''}
                        ${showAsAddressed ? `
                        <div class="comment-addressed-indicator">
                            <span class="addressed-badge">✓ Addressed</span>
                        </div>
                        ` : ''}
                    </div>
                    ${hasSelection ? `
                        <div class="comment-selection">
                            "${escapeHtml(item.selection!.selectedText)}"
                        </div>
                    ` : ''}
                    ${isAiComment && item.suggestedSection ? `
                        <div class="comment-section">
                            <strong>Suggested Section:</strong> ${escapeHtml(item.suggestedSection)}
                        </div>
                    ` : ''}
                    <div class="comment-content">${escapeHtml(item.comment)}</div>
                    ${item.category ? `
                        <div class="comment-category ${categoryClass}">${escapeHtml(categoryLabel)}</div>
                    ` : ''}
                    ${isAiComment && item.status === 'open' && documentStatus !== 'changes-requested' ? `
                        <div class="comment-actions">
                            <button data-action="mark-comment-status" data-comment-id="${item.id}" data-status="addressed" class="action-button">
                                Mark as Addressed
                            </button>
                            <button data-action="mark-comment-status" data-comment-id="${item.id}" data-status="wont-fix" class="action-button secondary">
                                Won't Fix
                            </button>
                            <button data-action="mark-comment-status" data-comment-id="${item.id}" data-status="pending-ai-addressal" class="action-button">
                                Let AI Address
                            </button>
                        </div>
                    ` : ''}
                    ${isAiComment && item.resolutionText ? `
                        <div class="comment-resolution">
                            <strong>Resolution:</strong> ${escapeHtml(item.resolutionText)}
                            <div class="resolution-meta">
                                by ${item.resolutionAuthor || 'unknown'} on ${new Date(item.resolutionTimestamp || new Date()).toLocaleString()}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        })
        .join('');
}

// Helper function to generate CSS styles for the design doc webview
function getDesignDocWebviewStyles(): string {
    return `
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #6c757d;
            --bg-color: #f8f9fa;
            --text-color: #212529;
            --line-hover-color: #f0f0f0;
            --border-color: #dee2e6;
            --header-bg: #4a90e2;
            --header-text: white;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --badge-text: white;
            --button-padding: 10px 16px;
            --button-border-radius: 6px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            background-color: white;
            min-height: 100vh;
            box-shadow: 0 0 0 1px var(--border-color);
        }

        /* Header Styles - Blue/White Design */
        .header {
            background: linear-gradient(135deg, var(--header-bg) 0%, #357abd 100%);
            color: var(--header-text);
            padding: 24px 32px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .title-bar {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 16px;
            min-height: 60px;
        }

        .doc-title {
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            line-height: 1.2;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            flex: 1;
            min-width: 300px;
            word-wrap: break-word;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: flex-start;
            flex-shrink: 0;
        }

        /* Status and Tags Container */
        .status-tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: center;
            margin-bottom: 16px;
            min-height: 40px;
        }

        .metadata {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-top: 0;
            opacity: 0.95;
            align-items: center;
        }

        .metadata-item {
            background-color: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.2);
            white-space: nowrap;
        }

        /* Status Badges */
        .status-badge {
            font-weight: 600;
            text-transform: uppercase;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            letter-spacing: 0.5px;
            border: 2px solid white;
            margin-right: 16px;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .status-under-review {
            background-color: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        .status-ready-for-implementation {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        
        .status-implemented {
            background-color: #e2e3e5;
            color: #383d41;
            border-color: #d6d8db;
        }
        
        .status-changes-requested {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        /* GitHub Handle Setup Banner */
        .github-handle-setup {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            color: #2d3436;
            padding: 20px 32px;
            border-bottom: 1px solid #fdcb6e;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .github-handle-setup h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
        }

        .github-handle-setup p {
            margin: 0 0 16px 0;
            opacity: 0.8;
        }

        .github-handle-setup .form-row {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .github-handle-setup input[type="text"] {
            flex: 1;
            padding: 10px 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        .github-handle-setup input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        /* Main Content */
        .markdown-body {
            padding: 40px 32px;
            max-width: none;
            background-color: white;
        }

        .markdown-body h1, .markdown-body h2, .markdown-body h3, .markdown-body h4, .markdown-body h5, .markdown-body h6 {
            margin-top: 32px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
            color: #24292e;
        }

        .markdown-body h1 {
            font-size: 32px;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 16px;
        }

        .markdown-body h2 {
            font-size: 24px;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 12px;
        }

        .markdown-body h3 {
            font-size: 20px;
        }

        .markdown-body h4 {
            font-size: 16px;
        }

        .markdown-body h5 {
            font-size: 14px;
        }

        .markdown-body h6 {
            font-size: 12px;
            color: #6a737d;
        }

        .markdown-body p {
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .markdown-body ul, .markdown-body ol {
            padding-left: 2em;
            margin-bottom: 16px;
        }

        .markdown-body li {
            margin-bottom: 4px;
        }

        .markdown-body blockquote {
            padding: 0 16px;
            color: #6a737d;
            border-left: 4px solid #dfe2e5;
            margin: 0 0 16px 0;
        }

        .markdown-body pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f6f8fa;
            border-radius: 6px;
            border: 1px solid #e1e4e8;
        }

        .markdown-body code {
            padding: 3px 6px;
            margin: 0;
            font-size: 85%;
            background-color: rgba(27,31,35,0.05);
            border-radius: 3px;
            font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
        }

        .markdown-body pre code {
            padding: 0;
            margin: 0;
            font-size: 100%;
            word-break: normal;
            white-space: pre;
            background: transparent;
            border: 0;
        }

        /* Mermaid Diagrams */
        .markdown-body .mermaid {
            text-align: center;
            margin: 24px 0;
            padding: 20px;
            background-color: #fafbfc;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
        }

        /* Button Styles */
        button {
            padding: var(--button-padding);
            border: none;
            border-radius: var(--button-border-radius);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            line-height: 1;
        }

        .primary-button {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2);
        }

        .primary-button:hover {
            background-color: #357abd;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3);
        }

        .secondary-button {
            background-color: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .secondary-button:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .success-button {
            background-color: var(--success-color);
            color: white;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
        }

        .success-button:hover {
            background-color: #218838;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .danger-button {
            background-color: var(--danger-color);
            color: white;
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
        }

        .danger-button:hover {
            background-color: #c82333;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .info-button {
            background-color: var(--info-color);
            color: white;
            box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
        }

        .info-button:hover {
            background-color: #138496;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        /* Comments Panel */
        .comments-panel {
            margin: 0;
            padding: 32px;
            background-color: #f8f9fa;
            border-top: 1px solid var(--border-color);
        }

        .comments-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--border-color);
        }

        .comments-heading h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #24292e;
        }

        .comments-actions {
            display: flex;
            gap: 12px;
        }

        /* Review Actions Panel */
        .review-actions-panel {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .review-summary {
            margin-bottom: 20px;
        }

        .open-comments-summary h4 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #24292e;
        }

        .open-comments-summary p {
            margin: 0;
            color: #6a737d;
            font-size: 14px;
        }

        .review-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-start;
        }

        .review-actions button {
            min-width: 140px;
        }

        /* Comment Cards */
        .comment-card {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .comment-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 6px rgba(74, 144, 226, 0.1);
        }

        .comment-card.ai-comment {
            border-left: 4px solid var(--primary-color);
        }

        /* Addressed comment styling */
        .comment-card.comment-addressed {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            opacity: 0.85;
        }

        .comment-card.comment-addressed:hover {
            border-color: #adb5bd;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .comment-card.comment-addressed .comment-content {
            color: #6c757d;
        }

        .comment-card.comment-addressed .comment-user {
            color: #6c757d;
        }

        .comment-addressed-indicator {
            display: flex;
            align-items: center;
        }

        .addressed-badge {
            background-color: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .comment-user {
            font-weight: 600;
            color: #24292e;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .ai-badge {
            background-color: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .comment-timestamp {
            color: #6a737d;
            font-size: 13px;
        }

        .comment-content {
            margin: 12px 0;
            line-height: 1.6;
            color: #24292e;
        }

        .comment-selection {
            background-color: #f1f8ff;
            border: 1px solid #c8e1ff;
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
            font-style: italic;
            color: #0366d6;
        }

        .comment-category {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 12px;
        }

        .category-blocking {
            background-color: #ffeaea;
            color: #d73a49;
            border: 1px solid #f97583;
        }

        .category-non-blocking {
            background-color: #e1f5fe;
            color: #0366d6;
            border: 1px solid #79b8ff;
        }

        .category-nit {
            background-color: #f6f8fa;
            color: #586069;
            border: 1px solid #d1d5da;
        }

        /* Add Comment Form */
        .add-comment-form {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .add-comment-form textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            font-family: inherit;
        }

        .add-comment-form textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .add-comment-form .form-row {
            margin-top: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .add-comment-form select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }

        /* Dialog Styles */
        .dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .dialog-content {
            background-color: white;
            padding: 32px;
            border-radius: 12px;
            width: 480px;
            max-width: 90vw;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .dialog-content h2 {
            margin: 0 0 24px 0;
            font-size: 20px;
            font-weight: 600;
            color: #24292e;
        }

        .dialog .form-row {
            margin-bottom: 20px;
        }

        .dialog .form-row label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #24292e;
        }

        .dialog .form-row input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
            color: #24292e;
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
            pointer-events: auto;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .dialog .form-row input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .dialog .form-row textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            resize: vertical;
        }

        .dialog .form-row textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .dialog-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 0;
                box-shadow: none;
            }

            .header {
                padding: 20px;
            }

            .title-bar {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
                min-height: auto;
            }

            .doc-title {
                font-size: 24px;
                min-width: auto;
                width: 100%;
            }

            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }

            .status-tags-container {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                min-height: auto;
            }

            .metadata {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .status-badge {
                margin-right: 0;
                margin-bottom: 8px;
            }

            .markdown-body {
                padding: 24px 20px;
            }

            .comments-panel {
                padding: 20px;
            }

            .comments-heading {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .dialog-content {
                padding: 24px;
                width: 90%;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 16px;
            }

            .doc-title {
                font-size: 20px;
                line-height: 1.3;
            }

            .header-actions {
                flex-wrap: wrap;
                gap: 8px;
            }

            .metadata-item {
                font-size: 12px;
                padding: 4px 12px;
            }

            .status-badge {
                font-size: 11px;
                padding: 6px 12px;
            }
        }

        /* Side Comments Panel - NEW */
        .side-comments-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 350px;
            height: 100vh;
            background-color: white;
            border-left: 1px solid var(--border-color);
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .side-comments-panel.open {
            transform: translateX(0);
        }

        .side-panel-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            background-color: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .side-panel-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #24292e;
        }

        .close-side-panel {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            color: #6a737d;
        }

        .close-side-panel:hover {
            color: #24292e;
        }

        .side-panel-content {
            padding: 20px;
        }

        /* Context Menu Styles - NEW */
        .context-menu {
            position: absolute;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            padding: 8px 0;
            min-width: 200px;
            display: none;
        }

        .context-menu-item {
            padding: 10px 16px;
            cursor: pointer;
            font-size: 14px;
            color: #24292e;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .context-menu-item:hover {
            background-color: #f6f8fa;
        }

        .context-menu-item.disabled {
            color: #6a737d;
            cursor: not-allowed;
        }

        .context-menu-item.disabled:hover {
            background-color: transparent;
        }

        .context-menu-separator {
            height: 1px;
            background-color: var(--border-color);
            margin: 4px 0;
        }

        /* Compact comment styles for side panel */
        .side-panel-content .comment-card {
            margin-bottom: 12px;
            padding: 12px;
            font-size: 13px;
        }

        .side-panel-content .comment-header {
            margin-bottom: 8px;
        }

        .side-panel-content .comment-user {
            font-size: 12px;
        }

        .side-panel-content .comment-timestamp {
            font-size: 11px;
        }

        /* Enhanced comment selection anchoring - Google Docs style */
        .comment-selection-anchor {
            background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
            border: 1px solid #d4e7ff;
            border-left: 3px solid var(--primary-color);
            border-radius: 6px;
            padding: 10px;
            margin: 8px 0 12px 0;
            position: relative;
            font-size: 12px;
        }

        .comment-selection-anchor::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-left: 6px solid var(--primary-color);
        }

        .selection-quote-icon {
            position: absolute;
            top: -8px;
            left: 8px;
            background: white;
            padding: 2px 4px;
            font-size: 10px;
            border-radius: 50%;
            border: 1px solid #d4e7ff;
        }

        .selection-text {
            font-style: italic;
            color: #4a5568;
            line-height: 1.4;
            padding-left: 4px;
        }

        /* Comment status badges */
        .comment-status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 8px;
            letter-spacing: 0.5px;
        }

        .comment-status-badge.status-open {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fbbf24;
        }

        .comment-status-badge.status-addressed {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #34d399;
        }

        .comment-status-badge.status-pending-ai-addressal {
            background-color: #e0e7ff;
            color: #3730a3;
            border: 1px solid #818cf8;
        }

        .comment-status-badge.status-wont-fix {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #f87171;
        }

        /* Visual connection lines for anchored comments */
        .side-panel-content .comment-card:has(.comment-selection-anchor) {
            border-left: 3px solid var(--primary-color);
            background: linear-gradient(135deg, #fefefe 0%, #f8f9ff 100%);
            position: relative;
        }

        .side-panel-content .comment-card:has(.comment-selection-anchor)::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 20px;
            width: 12px;
            height: 12px;
            background: var(--primary-color);
            border-radius: 50%;
            border: 2px solid white;
        }

        /* Hover effects for better interaction */
        .comment-selection-anchor:hover {
            background: linear-gradient(135deg, #f1f3ff 0%, #e8ecff 100%);
            border-color: #b3d4ff;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        /* Text highlighting for comment anchoring */
        .comment-highlight-match {
            background-color: rgba(255, 235, 59, 0.7);
            border-radius: 3px;
            padding: 2px 4px;
            margin: 0 1px;
            box-shadow: 0 1px 3px rgba(255, 193, 7, 0.3);
            transition: all 0.2s ease;
            position: relative;
        }

        .comment-highlight-match::before {
            content: '💬';
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-color);
            color: white;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 10px;
            opacity: 0.9;
            pointer-events: none;
            z-index: 1000;
        }

        /* Highlighting for submitted comments */
        .comment-submitted-highlight {
            background: linear-gradient(135deg, #4caf50 0%, #81c784 100%);
            color: white;
            border-radius: 4px;
            padding: 3px 6px;
            margin: 0 2px;
            box-shadow: 0 2px 6px rgba(76, 175, 80, 0.4);
            transition: all 0.3s ease;
            position: relative;
            animation: pulseGreen 0.6s ease-in-out;
        }

        .comment-submitted-highlight::before {
            content: '✅';
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #4caf50;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            opacity: 0.95;
            pointer-events: none;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        @keyframes pulseGreen {
            0% { box-shadow: 0 2px 6px rgba(76, 175, 80, 0.4); }
            50% { box-shadow: 0 4px 12px rgba(76, 175, 80, 0.7); }
            100% { box-shadow: 0 2px 6px rgba(76, 175, 80, 0.4); }
        }

        /* Layout adjustments for side panel */
        .container.has-side-panel {
            margin-right: 350px;
            transition: margin-right 0.3s ease;
        }

        .toggle-side-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .toggle-side-panel:hover {
            background-color: #357abd;
            transform: scale(1.05);
        }

        /* Hide the bottom comments panel when side panel is open */
        .container.has-side-panel .comments-panel {
            display: none;
        }

        /* Selection Context Display */
        .selection-context {
            background-color: #f1f8ff;
            border: 1px solid #c8e1ff;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
            font-style: italic;
            color: #0366d6;
            border-left: 3px solid #0366d6;
        }

        .selection-context-label {
            font-weight: 600;
            font-style: normal;
            margin-bottom: 4px;
            font-size: 12px;
            text-transform: uppercase;
        }

        /* Text selection highlight */
        .text-selection-highlight {
            background-color: rgba(74, 144, 226, 0.3);
            border-radius: 2px;
            transition: background-color 0.2s ease;
        }

        /* Better text selection for review mode */
        .container[data-doc-status="under_review"] .markdown-body {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        /* Ensure selected text remains visible */
        .container[data-doc-status="under_review"] .markdown-body ::selection {
            background-color: rgba(74, 144, 226, 0.3);
            color: inherit;
        }

        .container[data-doc-status="under_review"] .markdown-body ::-moz-selection {
            background-color: rgba(74, 144, 226, 0.3);
            color: inherit;
        }

        /* Improved textarea focus in side panel */
        .side-comments-panel textarea:focus,
        .add-comment-form textarea:focus {
            outline: 2px solid var(--primary-color) !important;
            outline-offset: 2px;
            border-color: var(--primary-color) !important;
            background-color: #fafbfc !important;
        }

        /* Responsive adjustments for side panel */
        @media (max-width: 768px) {
            .side-comments-panel {
                width: 100vw;
                right: 0;
            }
            
            .container.has-side-panel {
                margin-right: 0;
            }
            
            .toggle-side-panel {
                position: fixed;
                bottom: 20px;
                right: 20px;
                top: auto;
            }
        }

        @media (min-width: 769px) {
            /* Ensure side panel doesn't exceed viewport */
            .side-comments-panel {
                max-width: 350px;
                width: 350px;
            }
            
            /* Adjust container margin only on larger screens */
            .container.has-side-panel {
                margin-right: 350px;
                max-width: calc(100vw - 350px);
            }
        }

        .comments-heading {
    `;
}

// AI Review Panel Styles
const aiReviewStyles = `
    .ai-review-panel {
        margin-top: 1em;
        padding: 1em;
        background-color: var(--vscode-sideBar-background);
        border: 1px solid var(--vscode-sideBarSectionHeader-border);
        border-radius: 5px;
    }
    
    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1em;
    }
    
    .comments-section {
        margin-top: 1em;
    }
    
    .ai-comment {
        margin: 0.5em 0;
        padding: 1em;
        border: 1px solid var(--vscode-textSeparator-foreground);
        border-radius: 3px;
        background-color: var(--vscode-editor-background);
    }
    
    .comment-header {
        display: flex;
        align-items: center;
        gap: 0.5em;
        margin-bottom: 0.5em;
    }
    
    .comment-type {
        padding: 0.2em 0.5em;
        border-radius: 3px;
        font-size: 0.8em;
        font-weight: bold;
    }
    
    .comment-type-blocking {
        background-color: var(--vscode-errorForeground);
        color: var(--vscode-editor-background);
    }
    
    .comment-type-non-blocking {
        background-color: var(--vscode-warningForeground);
        color: var(--vscode-editor-background);
    }
    
    .comment-type-nit {
        background-color: var(--vscode-textPreformat-foreground);
        color: var(--vscode-editor-background);
    }
    
    .comment-status {
        font-size: 0.8em;
        color: var(--vscode-descriptionForeground);
    }
    
    .comment-content {
        margin: 0.5em 0;
    }
    
    .comment-section {
        margin-top: 0.5em;
        font-size: 0.9em;
        color: var(--vscode-descriptionForeground);
    }
    
    .comment-actions {
        display: flex;
        gap: 0.5em;
        margin-top: 0.5em;
    }
    
    .action-button {
        font-size: 0.8em;
        padding: 0.3em 0.6em;
    }
    
    .action-button.secondary {
        background-color: transparent;
        border: 1px solid var(--vscode-button-secondaryBackground);
        color: var(--vscode-button-secondaryForeground);
    }
    
    .comment-resolution {
        margin-top: 0.5em;
        padding: 0.5em;
        background-color: var(--vscode-textBlockQuote-background);
        border-left: 3px solid var(--vscode-textLink-foreground);
        font-size: 0.9em;
    }
    
    .resolution-meta {
        margin-top: 0.3em;
        font-size: 0.8em;
        color: var(--vscode-descriptionForeground);
    }
    
    .comment-select {
        margin-left: auto;
        font-size: 0.8em;
        color: var(--vscode-descriptionForeground);
    }
`;

function generateAiCommentHtml(comment: AiReviewComment): string {
    const commentTypeClass = `comment-type-${comment.type}`;
    const commentStatusClass = `comment-status-${comment.status}`;
    const isAddressable = comment.status === 'open' || comment.status === 'pending-ai-addressal';
    
    return `
    <div class="ai-comment ${commentTypeClass} ${commentStatusClass}" data-comment-id="${comment.id}">
        <div class="comment-header">
            <span class="comment-type">${comment.type}</span>
            <span class="comment-status">${comment.status}</span>
            ${isAddressable ? `
            <label class="comment-select">
                <input type="checkbox" class="comment-checkbox" data-action="update-selected-comments">
                Select for AI addressing
            </label>
            ` : ''}
        </div>
        
        <div class="comment-content">
            <div class="comment-text">${escapeHtml(comment.text)}</div>
            ${comment.suggestedSection ? `
            <div class="comment-section">
                <strong>Suggested Section:</strong> ${escapeHtml(comment.suggestedSection)}
            </div>
            ` : ''}
        </div>
        
        ${comment.status === 'open' ? `
        <div class="comment-actions">
            <button data-action="mark-comment-status" data-comment-id="${comment.id}" data-status="addressed" class="action-button">
                Mark as Addressed
            </button>
            <button data-action="mark-comment-status" data-comment-id="${comment.id}" data-status="wont-fix" class="action-button secondary">
                Won't Fix
            </button>
            <button data-action="mark-comment-status" data-comment-id="${comment.id}" data-status="pending-ai-addressal" class="action-button">
                Let AI Address
            </button>
        </div>
        ` : ''}
    </div>
    `;
}

function generateTags(metadata: DesignDocMetadata): string {
    const tags = [];
    
    if (metadata.status) {
        tags.push(`<span class="status-badge status-${metadata.status}">${metadata.status.replace('_', ' ')}</span>`);
    }
    
    if (metadata.approver) {
        tags.push(`<span class="metadata-item">Approver: ${escapeHtml(metadata.approver)}</span>`);
    }
    
    if (metadata.reviewers && metadata.reviewers.length > 0) {
        tags.push(`<span class="metadata-item">Reviewers: ${metadata.reviewers.map(r => escapeHtml(r)).join(', ')}</span>`);
    }
    
    return `<div class="status-tags-container">${tags.join('')}</div>`;
}

function generateMetadataHtml(metadata: DesignDocMetadata, userHandle: string | undefined): string {
    const metadataItems = [];
    
    if (metadata.author) {
        metadataItems.push(`<div class="metadata-item">Author: ${escapeHtml(metadata.author)}</div>`);
    }
    
    if (metadata.lastUpdated) {
        const date = new Date(metadata.lastUpdated);
        metadataItems.push(`<div class="metadata-item">Last Updated: ${date.toLocaleDateString()}</div>`);
    }
    
    if (metadata.feedback_requested_at) {
        const date = new Date(metadata.feedback_requested_at);
        metadataItems.push(`<div class="metadata-item">Feedback Requested: ${date.toLocaleDateString()}</div>`);
    }
    
    return metadataItems.length > 0 ? `<div class="metadata">${metadataItems.join('')}</div>` : '';
}

function renderCommentsForSidePanel(feedback: DesignDocFeedbackItem[], aiComments: AiReviewComment[] = [], documentStatus?: string): string {
    const allComments: any[] = [];
    
    // Add regular feedback comments
    feedback.forEach(item => {
        allComments.push({
            ...item,
            isAiComment: false
        });
    });
    
    // Add AI comments
    aiComments.forEach(comment => {
        allComments.push({
            ...comment,
            isAiComment: true
        });
    });
    
    // Sort by timestamp (newest first for side panel)
    allComments.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    if (allComments.length === 0) {
        return `<div style="text-align: center; color: #6a737d; padding: 20px;">
            <p>No comments yet.</p>
            <p style="font-size: 12px;">Select text and right-click to add comments.</p>
        </div>`;
    }
    
    return allComments.map(item => {
        const isAiComment = item.isAiComment;
        const hasSelection = !isAiComment && 'selection' in item && item.selection && item.selection.selectedText;
        const categoryClass = item.category ? `category-${item.category}` : '';
        
        // Determine address status for documents that should show addressed comments
        const shouldShowAddressed = documentStatus === 'changes-requested' || 
                                   documentStatus === 'under_review' || 
                                   documentStatus === 'ready_for_implementation';
        const isAddressed = item.status === 'addressed';
        const showAsAddressed = shouldShowAddressed && isAddressed;
        const isAddressable = isAiComment && (item.status === 'open' || item.status === 'pending-ai-addressal');
        
        return `
            <div class="comment-card${isAiComment ? ' ai-comment' : ''} ${showAsAddressed ? 'comment-addressed' : ''}" data-comment-id="${isAiComment ? item.id : `user-${item.timestamp}`}">
                <div class="comment-header">
                    <div class="comment-user">
                        ${isAiComment ? '<span class="ai-badge">AI</span> ' : ''}
                        ${escapeHtml(item.user || item.author || 'Unknown')}
                    </div>
                    <div class="comment-timestamp">${new Date(item.timestamp).toLocaleDateString()}</div>
                    ${showAsAddressed ? `
                    <div class="comment-addressed-indicator">
                        <span class="addressed-badge">✓ Addressed</span>
                    </div>
                    ` : ''}
                </div>
                
                ${hasSelection ? `
                    <div class="comment-selection-anchor">
                        <div class="selection-quote-icon">💬</div>
                        <div class="selection-text">
                            "${escapeHtml(item.selection.selectedText.length > 80 ? item.selection.selectedText.substring(0, 80) + '...' : item.selection.selectedText)}"
                        </div>
                    </div>
                ` : ''}
                
                <div class="comment-content">
                    ${escapeHtml(item.comment || item.text || '')}
                </div>
                
                ${item.category ? `
                    <div class="comment-category ${categoryClass}">
                        ${item.category}
                    </div>
                ` : ''}
                
                ${isAiComment && item.status ? `
                    <div class="comment-status-badge status-${item.status}">
                        ${item.status.replace('-', ' ')}
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');
}

export function getDesignDocWorkflowWebviewContent(
    docUri: vscode.Uri,
    metadata: DesignDocMetadata & { webview: vscode.Webview, extensionUri: vscode.Uri },
    content: string,
    userHandle: string | undefined
): string {
    const webviewUri = (file: string) => metadata.webview.asWebviewUri(vscode.Uri.joinPath(metadata.extensionUri, 'resources', file));
    const nonce = getNonce();

    // Parse and render markdown content
    const renderer = new marked.Renderer();
    
    // Configure highlight.js for code blocks
    marked.setOptions({
        highlight: function(code, lang) {
            if (lang && hljs.getLanguage(lang)) {
                try {
                    return hljs.highlight(code, { language: lang }).value;
                } catch (err) {
                    console.error('Error highlighting code:', err);
                }
            }
            return code;
        }
    });

    const renderedContent = marked(content);
    const isUnderReview = metadata.status === 'under_review';

    const feedback = metadata.feedback || [];
    const aiComments = metadata.aiComments || [];
    const totalCommentCount = feedback.length + aiComments.length;

    // Check for open non-system_action comments (for approval logic)
    const hasOpenNonSystemComments = feedback.some(c => c.status === 'open' && c.type !== 'system_action') ||
        aiComments.some(c => c.status === 'open');

    // Calculate open comment aggregates for under_review display
    const openNonSystemComments = [
        ...feedback.filter(c => c.status === 'open' && c.type !== 'system_action'),
        ...aiComments.filter(c => c.status === 'open')
    ];

    const openBlockingComments = openNonSystemComments.filter(c => {
        // For DesignDocFeedbackItem, check category; for AiReviewComment, check type
        const category = 'category' in c ? c.category : c.type;
        return category === 'blocking';
    });
    const openOtherComments = openNonSystemComments.filter(c => {
        // For DesignDocFeedbackItem, check category; for AiReviewComment, check type
        const category = 'category' in c ? c.category : c.type;
        return category !== 'blocking';
    });

    const totalOpenComments = [
        ...feedback.filter(c => c.status === 'open'),
        ...aiComments.filter(c => c.status === 'open')
    ].length;

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${metadata.webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}' https://cdn.jsdelivr.net; img-src ${metadata.webview.cspSource} data:;">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${escapeHtml(metadata.title || 'Untitled')}</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        ${getDesignDocWebviewStyles()}
        ${aiReviewStyles}
    </style>
</head>
<body data-doc-uri="${docUri.toString()}" data-doc-status="${metadata.status}">
    <div class="container${isUnderReview ? ' has-side-panel' : ''}">
        ${!userHandle ? `
        <div class="github-handle-setup">
            <h3>GitHub Handle Setup</h3>
            <p>Please set your GitHub handle to participate in design document workflows.</p>
            <div class="form-row">
                <input type="text" id="github-handle-input" placeholder="Enter your GitHub handle" />
                <button id="set-github-handle-btn" class="primary-button">Set Handle</button>
            </div>
        </div>
        ` : ''}
        
        <div class="header">
            <div class="title-bar">
                <h1 class="doc-title">${escapeHtml(metadata.title || 'Untitled Design Document')}</h1>
                <div class="header-actions">
                    <button id="edit-title-btn" class="secondary-button">Edit Title</button>
                    <button id="edit-document-btn" class="secondary-button">Edit</button>
                    ${metadata.status === 'pending' ? `<button id="request-feedback-btn" class="primary-button">Request Feedback</button>` : ''}
                    ${metadata.status === 'changes-requested' ? `<button id="send-for-approval-btn" class="success-button">Send for Approval</button>` : ''}
                    ${metadata.status === 'ready_for_implementation' ? `<button id="generate-implementation-plan-btn" class="primary-button">Generate Implementation Plan</button>` : ''}
                </div>
            </div>
            ${generateTags(metadata)}
            ${generateMetadataHtml(metadata, userHandle)}
        </div>

        <div class="markdown-body">
            ${isUnderReview ? `
            <!-- Review Actions Panel for under_review documents -->
            <div class="review-actions-panel">
                <div class="review-summary">
                    ${openNonSystemComments.length > 0 ? `
                    <div class="open-comments-summary">
                        <h4>Open Comments (${openBlockingComments.length} Blocking${openOtherComments.length > 0 ? `, ${openOtherComments.length} Other` : ''})</h4>
                        <p>Review and address comments before approval.</p>
                    </div>
                    ` : `
                    <div class="open-comments-summary">
                        <h4>✅ No Open Comments</h4>
                    </div>
                    `}
                </div>
                <div class="review-actions">
                    ${userHandle === metadata.approver ? `
                    ${!hasOpenNonSystemComments ? `
                    <button id="approve-document-btn-main" class="success-button">Approve Document</button>
                    ` : `
                    <button id="request-changes-btn-main" class="danger-button">Request Changes</button>
                    `}
                    ` : ''}
                </div>
            </div>
            ` : ''}
            ${renderedContent}
        </div>

        ${!isUnderReview ? `
        <div class="comments-panel">
            <div class="comments-heading">
                <h3>Comments & Feedback</h3>
                <div class="comments-actions">
                    <button id="add-comment-btn" class="primary-button">Add Comment</button>
                    ${metadata.status === 'under_review' && userHandle === metadata.approver ? `
                    <button id="approve-document-btn" class="success-button" ${hasOpenNonSystemComments ? 'disabled title="Cannot approve with open non-system_action comments."' : ''}>Approve</button>
                    <button id="request-changes-btn" class="danger-button">Request Changes</button>
                    ` : ''}
                    ${metadata.status === 'changes-requested' ? `
                    <button id="address-all-comments-ai-btn" class="info-button" ${totalOpenComments === 0 ? 'disabled title="No open comments to address"' : 'title="AI will address all open comments but keep document in changes-requested status"'}>Address All Comments with AI${totalOpenComments > 0 ? ` (${totalOpenComments})` : ''}</button>
                    ` : ''}
                </div>
            </div>
            
            <div id="comments-list">
                ${renderExistingComments(metadata.feedback || [], metadata.aiComments || [], metadata.status)}
            </div>

            <div id="active-selection-container" style="display: none;">
                <div class="selection-context">
                    <span class="selection-context-label">Selected text:</span>
                    <code id="selected-text-display"></code>
                </div>
            </div>

            <div class="add-comment-form" id="add-comment-form" style="display: none;">
                <textarea id="comment-text" placeholder="Add your comment..."></textarea>
                <div class="form-row">
                    <select id="comment-category">
                        <option value="">Select category (optional)</option>
                        <option value="blocking">Blocking Issue</option>
                        <option value="non-blocking">Non-blocking Suggestion</option>
                        <option value="nit">Nit/Style</option>
                    </select>
                    <button id="submit-comment-btn" class="primary-button">Submit Comment</button>
                    <button id="cancel-comment-btn" class="secondary-button">Cancel</button>
                </div>
            </div>

            <div class="batch-actions">
                <button id="address-selected-comments" class="info-button" disabled>
                    Let AI Address Selected Comments
                </button>
            </div>
        </div>
        ` : ''}
    </div>

    ${isUnderReview ? `
    <!-- Side Comments Panel for Review Mode -->
    <div class="side-comments-panel" id="side-comments-panel">
        <div class="side-panel-header">
            <h3>Comments & Review</h3>
            <button class="close-side-panel" id="close-side-panel">×</button>
        </div>
        <div class="side-panel-content">
            <div class="comments-actions" style="margin-bottom: 20px;">
                ${userHandle === metadata.approver ? `
                <button id="approve-document-btn" class="success-button" style="width: 100%; margin-bottom: 8px;" ${hasOpenNonSystemComments ? 'disabled title="Approval is blocked by open non-system_action comments."' : 'title="Approve this design document."'}>Approve Document</button>
                <button id="request-changes-btn" class="danger-button" style="width: 100%; margin-bottom: 16px;">Request Changes</button>
                ` : ''}
                <button id="add-general-comment-btn" class="primary-button" style="width: 100%;">Add General Comment</button>
            </div>
            
            <div id="side-comments-list">
                ${renderCommentsForSidePanel(metadata.feedback || [], metadata.aiComments || [], metadata.status)}
            </div>

            <div class="add-comment-form" id="side-add-comment-form" style="display: none; margin-top: 16px;">
                <textarea id="side-comment-text" placeholder="Add your comment..." style="width: 100%; min-height: 80px; margin-bottom: 8px;"></textarea>
                <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                    <select id="side-comment-category" style="flex: 1;">
                        <option value="">Category (optional)</option>
                        <option value="blocking">Blocking</option>
                        <option value="non-blocking">Suggestion</option>
                        <option value="nit">Nit/Style</option>
                    </select>
                </div>
                <div style="display: flex; gap: 8px;">
                    <button id="side-submit-comment-btn" class="primary-button" style="flex: 1;">Submit</button>
                    <button id="side-cancel-comment-btn" class="secondary-button" style="flex: 1;">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toggle Button for Side Panel -->
    <button class="toggle-side-panel" id="toggle-side-panel" title="Toggle Comments Panel">
        💬
    </button>
    ` : ''}

    <!-- Context Menu for Text Selection -->
    <div class="context-menu" id="context-menu">
        <button class="context-menu-item" id="add-comment-to-selection">
            <span>💬</span> Add Comment
        </button>
        <div class="context-menu-separator"></div>
    </div>

    <!-- Request Feedback Dialog -->
    <div id="request-feedback-dialog" class="dialog">
        <div class="dialog-content">
            <h2>Request Feedback</h2>
            <div class="form-row">
                <label for="approver-input">Approver (required):</label>
                <input type="text" id="approver-input" placeholder="GitHub handle of approver" />
            </div>
            <div class="form-row">
                <label for="reviewers-input">Additional Reviewers (optional):</label>
                <input type="text" id="reviewers-input" placeholder="Comma-separated GitHub handles" />
            </div>
            <div class="dialog-buttons">
                <button id="submit-feedback-request-btn" class="primary-button">Send Request</button>
                <button id="close-dialog-btn" class="secondary-button">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Alert Dialog -->
    <div id="alert-dialog" class="dialog">
        <div class="dialog-content">
            <h2 id="alert-dialog-title">Alert</h2>
            <p id="alert-dialog-message" style="margin-bottom: 24px;"></p>
            <div class="dialog-buttons">
                <button id="alert-dialog-ok-btn" class="primary-button">OK</button>
            </div>
        </div>
    </div>

    <!-- Confirm Dialog -->
    <div id="confirm-dialog" class="dialog">
        <div class="dialog-content">
            <h2 id="confirm-dialog-title">Confirm</h2>
            <p id="confirm-dialog-message" style="margin-bottom: 24px;"></p>
            <div class="dialog-buttons">
                <button id="confirm-dialog-yes-btn" class="primary-button">Yes</button>
                <button id="confirm-dialog-no-btn" class="secondary-button">No</button>
            </div>
        </div>
    </div>

    <!-- Edit Title Dialog -->
    <div id="edit-title-dialog" class="dialog">
        <div class="dialog-content">
            <h2>Edit Document Title</h2>
            <div class="form-row">
                <label for="edit-title-input">New Title:</label>
                <input type="text" id="edit-title-input" />
            </div>
            <div class="dialog-buttons">
                <button id="submit-edit-title-btn" class="primary-button">Save</button>
                <button id="cancel-edit-title-btn" class="secondary-button">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Request Changes Dialog -->
    <div id="request-changes-dialog" class="dialog">
        <div class="dialog-content">
            <h2>Request Changes</h2>
            <div class="form-row">
                <label for="request-changes-reason-input">Provide a summary of the requested changes (optional):</label>
                <textarea id="request-changes-reason-input" placeholder="Summarize the required changes..."></textarea>
            </div>
            <div class="dialog-buttons">
                <button id="submit-request-changes-btn" class="primary-button">Request Changes</button>
                <button id="cancel-request-changes-btn" class="secondary-button">Cancel</button>
            </div>
        </div>
    </div>
    
    <!-- Generic Prompt Dialog -->
    <div id="generic-prompt-dialog" class="dialog">
        <div class="dialog-content">
            <h2 id="generic-prompt-title"></h2>
            <div class="form-row">
                <label id="generic-prompt-label" for="generic-prompt-input"></label>
                <textarea id="generic-prompt-input"></textarea>
            </div>
            <div class="dialog-buttons">
                <button id="submit-generic-prompt-btn" class="primary-button">Submit</button>
                <button id="cancel-generic-prompt-btn" class="secondary-button">Cancel</button>
            </div>
        </div>
    </div>

    <script nonce="${nonce}">
        const vscode = acquireVsCodeApi();
        let activeSelection = null;
        let contextMenu = null;
        let genericPromptCallback = null;
        let confirmCallback = null;

        // Initialize Mermaid
        mermaid.initialize({ 
            startOnLoad: true, 
            theme: 'neutral',
            securityLevel: 'loose'
        });

        // Check if document is in review mode
        const isReviewMode = document.body.getAttribute('data-doc-status') === 'under_review';

        // Event listeners setup
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure input fields work by preventing event interference
            // These event listeners were too broad and were causing issues with input fields.
            // They have been removed as more specific handlers are in place.
            
            // Initialize context menu for all statuses (not just review mode)
            initializeContextMenu();
            
            // Initialize side panel if in review mode
            if (isReviewMode) {
                initializeSidePanel();
                // Auto-open side panel in review mode
                setTimeout(() => {
                    const sidePanel = document.getElementById('side-comments-panel');
                    if (sidePanel) {
                        sidePanel.classList.add('open');
                    }
                }, 500);
            }

            // GitHub Handle Setup
            const setGitHubHandleBtn = document.getElementById('set-github-handle-btn');
            if (setGitHubHandleBtn) {
                setGitHubHandleBtn.addEventListener('click', function() {
                    const handle = document.getElementById('github-handle-input').value.trim();
                    if (handle) {
                        vscode.postMessage({
                            type: 'setGitHubHandle',
                            handle: handle
                        });
                    }
                });
            }

            // Header Actions
            const editTitleBtn = document.getElementById('edit-title-btn');
            if (editTitleBtn) {
                editTitleBtn.addEventListener('click', function() {
                    const titleElement = document.querySelector('.doc-title');
                    const currentTitle = titleElement.textContent;
                    const dialog = document.getElementById('edit-title-dialog');
                    const input = document.getElementById('edit-title-input');
                    if (input) {
                        input.value = currentTitle;
                    }
                    if (dialog) {
                        dialog.style.display = 'flex';
                    }
                    if (input) {
                        input.focus();
                    }
                });
            }

            const editDocumentBtn = document.getElementById('edit-document-btn');
            if (editDocumentBtn) {
                editDocumentBtn.addEventListener('click', function() {
                    vscode.postMessage({ type: 'editDocument' });
                });
            }

            const requestFeedbackBtn = document.getElementById('request-feedback-btn');
            if (requestFeedbackBtn) {
                requestFeedbackBtn.addEventListener('click', function() {
                    const dialog = document.getElementById('request-feedback-dialog');
                    const approverInput = document.getElementById('approver-input');
                    const reviewersInput = document.getElementById('reviewers-input');
                    
                    if (dialog) {
                        dialog.style.display = 'flex';
                    }
                    
                    if (approverInput) {
                        // Clear any existing attributes that might interfere
                        approverInput.removeAttribute('readonly');
                        approverInput.removeAttribute('disabled');
                        
                        setTimeout(() => {
                            approverInput.focus();
                            approverInput.select();
                        }, 100);
                    }
                    
                    if (reviewersInput) {
                        // Same for reviewers input
                        reviewersInput.removeAttribute('readonly');
                        reviewersInput.removeAttribute('disabled');
                    }
                });
            }

            const sendForApprovalBtn = document.getElementById('send-for-approval-btn');
            if (sendForApprovalBtn) {
                sendForApprovalBtn.addEventListener('click', function() {
                    showCustomConfirm(
                        'Are you sure you want to send this document for approval? This will move it to under_review status.',
                        'Send for Approval',
                        function(confirmed) {
                            if (confirmed) {
                                vscode.postMessage({ type: 'sendForApproval' });
                            }
                        }
                    );
                });
            }

            const generateImplementationPlanBtn = document.getElementById('generate-implementation-plan-btn');
            if (generateImplementationPlanBtn) {
                generateImplementationPlanBtn.addEventListener('click', function() {
                    showCustomConfirm(
                        'This will generate an implementation plan for this design document. The document will be moved to implemented status. Continue?',
                        'Generate Implementation Plan',
                        function(confirmed) {
                            if (confirmed) {
                                vscode.postMessage({ type: 'generateImplementationPlan' });
                            }
                        }
                    );
                });
            }

            // Comments Actions (for non-review mode)
            const addCommentBtn = document.getElementById('add-comment-btn');
            if (addCommentBtn) {
                addCommentBtn.addEventListener('click', function() {
                    document.getElementById('add-comment-form').style.display = 'block';
                    document.getElementById('comment-text').focus();
                });
            }

            // Side Panel Comments Actions (for review mode)
            const addGeneralCommentBtn = document.getElementById('add-general-comment-btn');
            if (addGeneralCommentBtn) {
                addGeneralCommentBtn.addEventListener('click', function() {
                    showCommentForm('side-add-comment-form', 'side-comment-text');
                });
            }

            const approveDocumentBtn = document.getElementById('approve-document-btn');
            if (approveDocumentBtn) {
                approveDocumentBtn.addEventListener('click', function() {
                    showCustomConfirm('Are you sure you want to approve this design document?', 'Confirm Approval', function(confirmed) {
                        if (confirmed) {
                            vscode.postMessage({ type: 'approveDocument' });
                        }
                    });
                });
            }

            // Main panel approve button (for under_review documents)
            const approveDocumentBtnMain = document.getElementById('approve-document-btn-main');
            if (approveDocumentBtnMain) {
                approveDocumentBtnMain.addEventListener('click', function() {
                    showCustomConfirm('Are you sure you want to approve this design document?', 'Confirm Approval', function(confirmed) {
                        if (confirmed) {
                            vscode.postMessage({ type: 'approveDocument' });
                        }
                    });
                });
            }

            const requestChangesBtn = document.getElementById('request-changes-btn');
            if (requestChangesBtn) {
                requestChangesBtn.addEventListener('click', function() {
                    const totalCommentCount = ${totalCommentCount};
                    if (totalCommentCount === 0) {
                        showCustomAlert('Please add comments detailing the required changes before requesting changes.');
                        return;
                    }
                    const dialog = document.getElementById('request-changes-dialog');
                    if(dialog) {
                        dialog.style.display = 'flex';
                    }
                });
            }

            // Main panel request changes button (for under_review documents)
            const requestChangesBtnMain = document.getElementById('request-changes-btn-main');
            if (requestChangesBtnMain) {
                requestChangesBtnMain.addEventListener('click', function() {
                    const totalCommentCount = ${totalCommentCount};
                    if (totalCommentCount === 0) {
                        showCustomAlert('Please add comments detailing the required changes before requesting changes.');
                        return;
                    }
                    const dialog = document.getElementById('request-changes-dialog');
                    if(dialog) {
                        dialog.style.display = 'flex';
                    }
                });
            }

            // Address All Comments with AI (for changes-requested status)
            const addressAllCommentsAiBtn = document.getElementById('address-all-comments-ai-btn');
            if (addressAllCommentsAiBtn) {
                addressAllCommentsAiBtn.addEventListener('click', function() {
                    const totalOpenComments = ${totalOpenComments};
                    if (totalOpenComments === 0) {
                        showCustomAlert('No open comments found to address.');
                        return;
                    }
                    
                    const commentText = totalOpenComments === 1 ? 'comment' : 'comments';
                    const message = 'This will have AI address all ' + totalOpenComments + ' open ' + commentText + ' in the document. The document will remain in changes-requested status. Are you sure you want to proceed?';
                    showCustomConfirm(message, 'Confirm AI Address Comments', function(confirmed) {
                        if (confirmed) {
                            vscode.postMessage({
                                type: 'addressAllOpenComments'
                            });
                        }
                    });
                });
            }

            // Comment Form Actions (main form)
            const submitCommentBtn = document.getElementById('submit-comment-btn');
            if (submitCommentBtn) {
                submitCommentBtn.addEventListener('click', function() {
                    console.log('ArchKnow [WebView]: Submit comment button clicked (main form)');
                    submitComment('comment-text', 'comment-category', 'add-comment-form');
                });
            }

            const cancelCommentBtn = document.getElementById('cancel-comment-btn');
            if (cancelCommentBtn) {
                cancelCommentBtn.addEventListener('click', function() {
                    console.log('ArchKnow [WebView]: Cancel comment button clicked (main form)');
                    cancelComment('add-comment-form');
                });
            }

            // Side Panel Comment Form Actions
            const sideSubmitCommentBtn = document.getElementById('side-submit-comment-btn');
            if (sideSubmitCommentBtn) {
                sideSubmitCommentBtn.addEventListener('click', function() {
                    console.log('ArchKnow [WebView]: Submit comment button clicked (side panel)');
                    submitComment('side-comment-text', 'side-comment-category', 'side-add-comment-form');
                });
            }

            const sideCancelCommentBtn = document.getElementById('side-cancel-comment-btn');
            if (sideCancelCommentBtn) {
                sideCancelCommentBtn.addEventListener('click', function() {
                    console.log('ArchKnow [WebView]: Cancel comment button clicked (side panel)');
                    cancelComment('side-add-comment-form');
                });
            }

            // Dialog Actions
            const submitFeedbackRequestBtn = document.getElementById('submit-feedback-request-btn');
            if (submitFeedbackRequestBtn) {
                submitFeedbackRequestBtn.addEventListener('click', function() {
                    const approver = document.getElementById('approver-input').value.trim();
                    const reviewers = document.getElementById('reviewers-input').value.trim();
                    
                    if (!approver) {
                        showCustomAlert('Please specify an approver.');
                        return;
                    }

                    vscode.postMessage({
                        type: 'requestFeedback',
                        approver: approver,
                        reviewers: reviewers ? reviewers.split(',').map(r => r.trim()).filter(r => r) : []
                    });

                    document.getElementById('request-feedback-dialog').style.display = 'none';
                });
            }

            const closeDialogBtn = document.getElementById('close-dialog-btn');
            if (closeDialogBtn) {
                closeDialogBtn.addEventListener('click', function() {
                    document.getElementById('request-feedback-dialog').style.display = 'none';
                });
            }

            // Address Selected Comments
            const addressSelectedCommentsBtn = document.getElementById('address-selected-comments');
            if (addressSelectedCommentsBtn) {
                addressSelectedCommentsBtn.addEventListener('click', function() {
                    // Only select checkboxes from open comments (not addressed ones)
                    const checkboxes = document.querySelectorAll('.comment-checkbox:checked');
                    const commentIds = Array.from(checkboxes).map(cb => {
                        const commentCard = cb.closest('.comment-card');
                        // Skip addressed comments
                        if (commentCard.classList.contains('comment-addressed')) {
                            return null;
                        }
                        return commentCard.dataset.commentId;
                    }).filter(id => id);

                    if (commentIds.length === 0) {
                        showCustomAlert('Please select open comments to address.');
                        return;
                    }

                    vscode.postMessage({
                        type: 'addressSelectedComments',
                        commentIds: commentIds
                    });
                });
            }

            // AI Review Panel Buttons
            const requestAiReviewPanelBtn = document.getElementById('request-ai-review-panel');
            if (requestAiReviewPanelBtn) {
                requestAiReviewPanelBtn.addEventListener('click', function() {
                    requestAiReview();
                });
            }

            const addressSelectedCommentsAiBtn = document.getElementById('address-selected-comments-ai');
            if (addressSelectedCommentsAiBtn) {
                addressSelectedCommentsAiBtn.addEventListener('click', function() {
                    // Only select checkboxes from open comments (not addressed ones)
                    const checkboxes = document.querySelectorAll('.comment-checkbox:checked');
                    const commentIds = Array.from(checkboxes).map(cb => {
                        const commentCard = cb.closest('.ai-comment');
                        // Skip addressed comments
                        if (commentCard && commentCard.classList.contains('comment-addressed')) {
                            return null;
                        }
                        return commentCard ? commentCard.dataset.commentId : null;
                    }).filter(id => id);

                    if (commentIds.length === 0) {
                        showCustomAlert('Please select open comments to address.');
                        return;
                    }

                    vscode.postMessage({
                        type: 'addressSelectedComments',
                        commentIds: commentIds
                    });
                });
            }

            // Event delegation for dynamically generated AI comment actions
            document.addEventListener('click', function(event) {
                const target = event.target;
                
                // Don't interfere with input field interactions
                if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
                    return;
                }
                
                const action = target.getAttribute('data-action');

                if (action === 'mark-comment-status') {
                    const commentId = target.getAttribute('data-comment-id');
                    const status = target.getAttribute('data-status');
                    if (commentId && status) {
                        markCommentStatus(commentId, status);
                    }
                }
            });

            // Event delegation for checkbox changes
            document.addEventListener('change', function(event) {
                const target = event.target;
                const action = target.getAttribute('data-action');

                if (action === 'update-selected-comments') {
                    updateSelectedComments();
                }
            });

             // Alert Dialog
            document.getElementById('alert-dialog-ok-btn')?.addEventListener('click', () => {
                document.getElementById('alert-dialog').style.display = 'none';
            });

            // Confirm Dialog
            document.getElementById('confirm-dialog-yes-btn')?.addEventListener('click', () => {
                document.getElementById('confirm-dialog').style.display = 'none';
                if (confirmCallback) {
                    confirmCallback(true);
                    confirmCallback = null;
                }
            });
            document.getElementById('confirm-dialog-no-btn')?.addEventListener('click', () => {
                document.getElementById('confirm-dialog').style.display = 'none';
                if (confirmCallback) {
                    confirmCallback(false);
                    confirmCallback = null;
                }
            });

            // Edit Title Dialog
            document.getElementById('submit-edit-title-btn')?.addEventListener('click', () => {
                const dialog = document.getElementById('edit-title-dialog');
                const input = document.getElementById('edit-title-input');
                const newTitle = input.value.trim();
                const titleElement = document.querySelector('.doc-title');
                const currentTitle = titleElement.textContent;
                if (newTitle && newTitle !== currentTitle) {
                    vscode.postMessage({
                        type: 'editTitle',
                        title: newTitle
                    });
                }
                dialog.style.display = 'none';
            });
            document.getElementById('cancel-edit-title-btn')?.addEventListener('click', () => {
                document.getElementById('edit-title-dialog').style.display = 'none';
            });

            // Request Changes Dialog
            const requestChangesDialog = document.getElementById('request-changes-dialog');
            document.getElementById('submit-request-changes-btn')?.addEventListener('click', function() {
                const reason = document.getElementById('request-changes-reason-input').value.trim();
                vscode.postMessage({
                    type: 'requestChanges',
                    reason: reason || 'Changes requested based on comments.'
                });
                requestChangesDialog.style.display = 'none';
                document.getElementById('request-changes-reason-input').value = '';
            });
            document.getElementById('cancel-request-changes-btn')?.addEventListener('click', function() {
                requestChangesDialog.style.display = 'none';
                document.getElementById('request-changes-reason-input').value = '';
            });

            // Generic Prompt Dialog
            document.getElementById('submit-generic-prompt-btn')?.addEventListener('click', () => {
                const input = document.getElementById('generic-prompt-input');
                if (genericPromptCallback) {
                    if (input.required && !input.value.trim()) {
                        input.style.border = '1px solid red';
                        setTimeout(() => { input.style.border = ''; }, 2000);
                        return;
                    }
                    input.style.border = '';
                    genericPromptCallback(input.value);
                }
                genericPromptCallback = null;
                document.getElementById('generic-prompt-dialog').style.display = 'none';
            });
            document.getElementById('cancel-generic-prompt-btn')?.addEventListener('click', () => {
                genericPromptCallback = null;
                document.getElementById('generic-prompt-dialog').style.display = 'none';
            });
        });

        // Side Panel Functions
        function initializeSidePanel() {
            const toggleBtn = document.getElementById('toggle-side-panel');
            const closeBtn = document.getElementById('close-side-panel');
            const sidePanel = document.getElementById('side-comments-panel');

            if (toggleBtn && sidePanel) {
                toggleBtn.addEventListener('click', function() {
                    sidePanel.classList.toggle('open');
                });
            }

            if (closeBtn && sidePanel) {
                closeBtn.addEventListener('click', function() {
                    sidePanel.classList.remove('open');
                });
            }

            // Close side panel when clicking outside
            document.addEventListener('click', function(event) {
                if (sidePanel && sidePanel.classList.contains('open') && 
                    !sidePanel.contains(event.target) && 
                    !toggleBtn.contains(event.target)) {
                    // Don't close if clicking on context menu
                    if (!contextMenu || !contextMenu.contains(event.target)) {
                        sidePanel.classList.remove('open');
                    }
                }
            });
        }

        // Context Menu Functions
        function initializeContextMenu() {
            contextMenu = document.getElementById('context-menu');
            const addCommentToSelectionBtn = document.getElementById('add-comment-to-selection');

            if (addCommentToSelectionBtn) {
                addCommentToSelectionBtn.addEventListener('click', function() {
                    hideContextMenu();
                    if (activeSelection) {
                        if (isReviewMode) {
                            // In review mode, use side panel
                            showSidePanel();
                            showCommentForm('side-add-comment-form', 'side-comment-text');
                        } else {
                            // In non-review mode, use main comment form
                            showCommentForm('add-comment-form', 'comment-text');
                        }
                    }
                });
            }

            // Hide context menu when clicking elsewhere
            document.addEventListener('click', function(event) {
                if (contextMenu && !contextMenu.contains(event.target)) {
                    hideContextMenu();
                }
            });

            // Hide context menu on scroll or escape key
            document.addEventListener('scroll', hideContextMenu);
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    hideContextMenu();
                    
                    // Only clear selection if we're not actively typing in a form
                    const activeElement = document.activeElement;
                    const isInForm = activeElement && (
                        activeElement.tagName === 'TEXTAREA' || 
                        activeElement.tagName === 'INPUT' ||
                        activeElement.closest('.add-comment-form')
                    );
                    
                    if (!isInForm) {
                        clearSelection();
                    }
                }
            });
        }

        function showContextMenu(x, y) {
            if (!contextMenu) return;
            
            contextMenu.style.left = x + 'px';
            contextMenu.style.top = y + 'px';
            contextMenu.style.display = 'block';

            // Adjust position if menu goes off screen
            const rect = contextMenu.getBoundingClientRect();
            if (rect.right > window.innerWidth) {
                contextMenu.style.left = (x - rect.width) + 'px';
            }
            if (rect.bottom > window.innerHeight) {
                contextMenu.style.top = (y - rect.height) + 'px';
            }
        }

        function hideContextMenu() {
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
        }

        function showSidePanel() {
            const sidePanel = document.getElementById('side-comments-panel');
            if (sidePanel) {
                sidePanel.classList.add('open');
            }
        }

        function showCommentForm(formId, textareaId) {
            const form = document.getElementById(formId);
            const textarea = document.getElementById(textareaId);
            if (form && textarea) {
                form.style.display = 'block';
                
                // Show selection context if available
                if (activeSelection && activeSelection.selectedText) {
                    showSelectionContext(formId, activeSelection.selectedText);
                    textarea.placeholder = 'Add your comment about the selected text...';
                } else {
                    hideSelectionContext(formId);
                    textarea.placeholder = 'Add your comment...';
                }
                
                // Ensure focus with a small delay to let DOM update
                setTimeout(() => {
                    textarea.focus();
                    // Prevent any interference with focus
                    textarea.addEventListener('blur', function(e) {
                        // Immediately refocus if blur was caused by other events
                        setTimeout(() => {
                            if (form.style.display !== 'none' && document.activeElement !== textarea) {
                                textarea.focus();
                            }
                        }, 50);
                    }, { once: true });
                }, 100);
            }
        }

        function showSelectionContext(formId, selectedText) {
            // Create or update selection context display
            let contextDiv = document.getElementById('selection-context-' + formId);
            if (!contextDiv) {
                contextDiv = document.createElement('div');
                contextDiv.id = 'selection-context-' + formId;
                contextDiv.className = 'selection-context';
                
                const form = document.getElementById(formId);
                if (form) {
                    form.insertBefore(contextDiv, form.firstChild);
                }
            }
            
            const truncatedText = selectedText.length > 100 ? selectedText.substring(0, 100) + '...' : selectedText;
            contextDiv.innerHTML = '<div class="selection-context-label">Commenting on selected text:</div>' +
                                   '<div>"' + truncatedText + '"</div>';
            contextDiv.style.display = 'block';
        }

        function hideSelectionContext(formId) {
            const contextDiv = document.getElementById('selection-context-' + formId);
            if (contextDiv) {
                contextDiv.style.display = 'none';
            }
        }

        function clearSelection() {
            // Clear browser selection
            if (window.getSelection) {
                window.getSelection().removeAllRanges();
            }
            
            // Clear our active selection
            activeSelection = null;
            
            // Hide selection indicators
            hideSelectionContext('side-add-comment-form');
            hideSelectionContext('add-comment-form');
            
            if (document.getElementById('selected-text-display')) {
                document.getElementById('selected-text-display').textContent = '';
            }
            if (document.getElementById('active-selection-container')) {
                document.getElementById('active-selection-container').style.display = 'none';
            }
        }

        function preserveSelection() {
            // Keep the visual selection but store our data
            // Don't clear the browser selection, just store our data
            if (activeSelection) {
                console.log('Preserving selection:', activeSelection.selectedText);
            }
        }

        // Comment Functions
        function submitComment(textareaId, categoryId, formId) {
            console.log('ArchKnow [WebView]: submitComment called with:', textareaId, categoryId, formId);
            
            const text = document.getElementById(textareaId).value.trim();
            const category = document.getElementById(categoryId).value;
            
            console.log('ArchKnow [WebView]: Comment text:', text);
            console.log('ArchKnow [WebView]: Category:', category);
            
            if (!text) {
                showCustomAlert('Please enter a comment.');
                return;
            }

            // Store the selection for later use
            const commentSelection = activeSelection ? { ...activeSelection } : null;

            // Include selection information if available
            const commentData = {
                type: 'addComment',
                comment: text,
                category: category || undefined,
                selection: commentSelection
            };

            console.log('ArchKnow [WebView]: Submitting comment with data:', commentData);
            console.log('ArchKnow [WebView]: VSCode API available:', !!vscode);
            
            // Show loading state
            const submitBtn = document.getElementById(formId.includes('side') ? 'side-submit-comment-btn' : 'submit-comment-btn');
            if (submitBtn) {
                submitBtn.textContent = 'Submitting...';
                submitBtn.disabled = true;
            }

            try {
                vscode.postMessage(commentData);
                
                // Show success feedback
                showCommentSubmissionFeedback(formId, commentSelection);
                
                // Clear form but keep webview open
                clearCommentForm(formId);
                
                // If there was a text selection, highlight it briefly to show where comment was added
                if (commentSelection && commentSelection.selectedText) {
                    highlightSubmittedCommentText(commentSelection.selectedText);
                }
                
            } catch (error) {
                console.error('ArchKnow [WebView]: Error submitting comment:', error);
                showCustomAlert('Failed to submit comment. Please try again.');
                
                // Reset submit button
                if (submitBtn) {
                    submitBtn.textContent = 'Submit Comment';
                    submitBtn.disabled = false;
                }
            }
        }

        function clearCommentForm(formId) {
            const form = document.getElementById(formId);
            if (form) {
                form.style.display = 'none';
            }
            
            const textarea = formId.includes('side') ? 
                document.getElementById('side-comment-text') : 
                document.getElementById('comment-text');
            const categorySelect = formId.includes('side') ? 
                document.getElementById('side-comment-category') : 
                document.getElementById('comment-category');
                
            if (textarea) {
                textarea.value = '';
                textarea.placeholder = 'Add your comment...';
            }
            
            if (categorySelect) {
                categorySelect.value = '';
            }
            
            // Reset submit button text
            const submitBtn = document.getElementById(formId.includes('side') ? 'side-submit-comment-btn' : 'submit-comment-btn');
            if (submitBtn) {
                submitBtn.textContent = 'Submit Comment';
                submitBtn.disabled = false;
            }
            
            // Clear selection context but don't clear activeSelection yet (for highlighting)
            hideSelectionContext(formId);
        }

        function showCommentSubmissionFeedback(formId, selection) {
            // Create a temporary success message
            const successMsg = document.createElement('div');
            successMsg.className = 'comment-success-feedback';
            successMsg.innerHTML = '<div style="background: #d4edda; color: #155724; padding: 12px; border-radius: 6px; margin: 10px 0; border: 1px solid #c3e6cb;">' +
                '✅ Comment submitted successfully' + (selection ? ' for selected text' : '') + '!' +
                '</div>';
            
            const form = document.getElementById(formId);
            if (form && form.parentNode) {
                form.parentNode.insertBefore(successMsg, form.nextSibling);
                
                // Remove the success message after 3 seconds
                setTimeout(() => {
                    if (successMsg.parentNode) {
                        successMsg.parentNode.removeChild(successMsg);
                    }
                }, 3000);
            }
        }

        function highlightSubmittedCommentText(selectedText) {
            if (!selectedText || selectedText.length < 3) return;
            
            // Use the same highlighting function but with a different style
            const markdownBody = document.querySelector('.markdown-body');
            if (!markdownBody) return;
            
            // Find and highlight the text briefly
            const walker = document.createTreeWalker(
                markdownBody,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }
            
            for (const textNode of textNodes) {
                const text = textNode.textContent;
                const cleanSearchText = selectedText.trim();
                
                if (text.includes(cleanSearchText)) {
                    const parent = textNode.parentNode;
                    const index = text.indexOf(cleanSearchText);
                    
                    if (index !== -1) {
                        const beforeText = text.substring(0, index);
                        const matchText = text.substring(index, index + cleanSearchText.length);
                        const afterText = text.substring(index + cleanSearchText.length);
                        
                        const beforeNode = document.createTextNode(beforeText);
                        const highlightSpan = document.createElement('span');
                        highlightSpan.className = 'comment-submitted-highlight';
                        highlightSpan.textContent = matchText;
                        const afterNode = document.createTextNode(afterText);
                        
                        parent.insertBefore(beforeNode, textNode);
                        parent.insertBefore(highlightSpan, textNode);
                        parent.insertBefore(afterNode, textNode);
                        parent.removeChild(textNode);
                        
                        // Scroll to the highlighted text
                        highlightSpan.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        
                        // Remove highlight after 4 seconds
                        setTimeout(() => {
                            if (highlightSpan.parentNode) {
                                const parent = highlightSpan.parentNode;
                                parent.insertBefore(document.createTextNode(highlightSpan.textContent), highlightSpan);
                                parent.removeChild(highlightSpan);
                                parent.normalize();
                            }
                        }, 4000);
                        
                        break;
                    }
                }
            }
            
            // Clear active selection after highlighting
            setTimeout(() => {
                clearSelection();
            }, 1000);
        }

        function cancelComment(formId) {
            clearCommentForm(formId);
            clearSelection();
        }

        // Global functions that may be called from dynamically generated content
        function requestAiReview() {
            vscode.postMessage({ type: 'requestAiReview' });
        }

        function markCommentStatus(commentId, status) {
            const postMessage = (resolutionText) => {
                vscode.postMessage({
                    type: 'markCommentStatus',
                    commentId: commentId,
                    status: status,
                    resolutionText: resolutionText
                });
            };

            if (status === 'addressed') {
                showGenericPrompt({
                    title: 'Mark as Addressed',
                    label: 'Optional: Add a note about how this was addressed:',
                    callback: postMessage,
                });
            } else if (status === 'wont-fix') {
                showGenericPrompt({
                    title: "Won't Fix",
                    label: "Please explain why this won't be fixed:",
                    callback: (resolutionText) => {
                        if (resolutionText && resolutionText.trim()) {
                            postMessage(resolutionText);
                        } else {
                            const input = document.getElementById('generic-prompt-input');
                            input.style.border = '1px solid red';
                             setTimeout(() => { input.style.border = ''; }, 2000);
                        }
                    },
                    required: true
                });
            } else {
                postMessage(null);
            }
        }

        function updateSelectedComments() {
            // Only count checkboxes from open comments (not addressed ones)
            const checkboxes = document.querySelectorAll('.comment-checkbox:checked');
            const openSelectedCount = Array.from(checkboxes).filter(cb => {
                const commentCard = cb.closest('.comment-card');
                return commentCard && !commentCard.classList.contains('comment-addressed');
            }).length;
            
            const button = document.getElementById('address-selected-comments');
            if (button) {
                button.disabled = openSelectedCount === 0;
            }
        }

        // Enhanced text selection for review mode
        document.addEventListener('mouseup', function(event) {
            // Small delay to ensure selection is complete
            setTimeout(() => {
                const selection = window.getSelection();
                if (selection && selection.toString().length > 0) {
                    const range = selection.getRangeAt(0);
                    const container = range.commonAncestorContainer;
                    
                    // Only allow selection within markdown content
                    if (!container.closest('.markdown-body')) {
                        return;
                    }

                    if (container.nodeType === Node.TEXT_NODE || container.closest('.markdown-body')) {
                        processTextSelection(selection, range);
                    }
                } else {
                    // Only clear selection if we're not in a comment form or dialog
                    const target = event.target;
                    if (!target.closest('.add-comment-form') && 
                        !target.closest('.side-comments-panel') &&
                        !target.closest('.context-menu') &&
                        !target.closest('.dialog')) {
                        clearSelection();
                    }
                }
            }, 10);
        });

        function processTextSelection(selection, range) {
            const selectedText = selection.toString().trim();
            if (selectedText.length === 0) {
                return; // Don't clear selection here
            }

            // Store selection details - only serializable data
            activeSelection = {
                selectedText: selectedText,
                startOffset: range.startOffset,
                endOffset: range.endOffset,
                // Don't store DOM node references - they can't be serialized
                // startContainer: range.startContainer,
                // endContainer: range.endContainer
            };

            console.log('Text selected:', selectedText);

            // For all document statuses, prepare for context menu when text is selected
            // Keep the visual selection active for context menu
            preserveSelection();
            
            // Also show selection indicator for non-review modes
            if (!isReviewMode) {
                if (document.getElementById('selected-text-display')) {
                    document.getElementById('selected-text-display').textContent = selectedText;
                }
                if (document.getElementById('active-selection-container')) {
                    document.getElementById('active-selection-container').style.display = 'block';
                }
            }
        }

        // Add hover functionality for comment anchoring
        document.addEventListener('mouseover', function(event) {
            const commentAnchor = event.target.closest('.comment-selection-anchor');
            if (commentAnchor && isReviewMode) {
                const selectionText = commentAnchor.querySelector('.selection-text');
                if (selectionText) {
                    const textContent = selectionText.textContent.replace(/['"]/g, '').trim();
                    if (textContent.endsWith('...')) {
                        // Get the full text from the comment data
                        const commentCard = commentAnchor.closest('.comment-card');
                        const commentId = commentCard?.dataset.commentId;
                        // Could implement full text search here if needed
                    }
                    highlightTextInDocument(textContent);
                }
            }
        });

        document.addEventListener('mouseout', function(event) {
            const commentAnchor = event.target.closest('.comment-selection-anchor');
            if (commentAnchor && isReviewMode) {
                removeTextHighlightFromDocument();
            }
        });

        function highlightTextInDocument(searchText) {
            if (!searchText || searchText.length < 3) return; // Avoid highlighting very short text
            
            const markdownBody = document.querySelector('.markdown-body');
            if (!markdownBody) return;
            
            // Remove existing highlights first
            removeTextHighlightFromDocument();
            
            // Create a text walker to find matching text
            const walker = document.createTreeWalker(
                markdownBody,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }
            
            // Find and highlight matching text
            for (const textNode of textNodes) {
                const text = textNode.textContent;
                const cleanSearchText = searchText.replace(/\.\.\.$/, '').trim();
                
                if (text.includes(cleanSearchText)) {
                    const parent = textNode.parentNode;
                    const index = text.indexOf(cleanSearchText);
                    
                    if (index !== -1) {
                        // Split the text node and wrap the matching part
                        const beforeText = text.substring(0, index);
                        const matchText = text.substring(index, index + cleanSearchText.length);
                        const afterText = text.substring(index + cleanSearchText.length);
                        
                        const beforeNode = document.createTextNode(beforeText);
                        const highlightSpan = document.createElement('span');
                        highlightSpan.className = 'comment-highlight-match';
                        highlightSpan.textContent = matchText;
                        const afterNode = document.createTextNode(afterText);
                        
                        parent.insertBefore(beforeNode, textNode);
                        parent.insertBefore(highlightSpan, textNode);
                        parent.insertBefore(afterNode, textNode);
                        parent.removeChild(textNode);
                        
                        // Only highlight the first match to avoid overwhelming the UI
                        break;
                    }
                }
            }
        }

        function removeTextHighlightFromDocument() {
            const highlights = document.querySelectorAll('.comment-highlight-match');
            highlights.forEach(highlight => {
                const parent = highlight.parentNode;
                parent.insertBefore(document.createTextNode(highlight.textContent), highlight);
                parent.removeChild(highlight);
                parent.normalize(); // Merge adjacent text nodes
            });
        }

        // Right-click context menu for all document statuses when text is selected
        document.addEventListener('contextmenu', function(event) {
            if (activeSelection && activeSelection.selectedText) {
                const container = event.target.closest('.markdown-body');
                if (container) {
                    event.preventDefault();
                    showContextMenu(event.pageX, event.pageY);
                    // Preserve the selection when showing context menu
                    preserveSelection();
                }
            }
        });

        // Clear selection when clicking outside content area - but be more selective
        document.addEventListener('click', function(event) {
            const target = event.target;
            
            // Don't clear selection if clicking on:
            // - Markdown body (content area)
            // - Context menu
            // - Side comments panel
            // - Comment forms
            // - Selected text or form elements
            if (!target.closest('.markdown-body') && 
                !target.closest('.context-menu') && 
                !target.closest('.side-comments-panel') &&
                !target.closest('.add-comment-form') &&
                !target.closest('textarea') &&
                !target.closest('input') &&
                !target.closest('select') &&
                !target.closest('button')) {
                clearSelection();
            }
        });

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.type) {
                case 'refresh':
                    location.reload();
                    break;
                case 'focusComment':
                    const textarea = isReviewMode ? 
                        document.getElementById('side-comment-text') : 
                        document.getElementById('comment-text');
                    if (textarea) {
                        textarea.focus();
                    }
                    break;
            }
        });

        function showCustomAlert(message, title = 'Alert') {
            document.getElementById('alert-dialog-title').textContent = title;
            document.getElementById('alert-dialog-message').textContent = message;
            document.getElementById('alert-dialog').style.display = 'flex';
        }

        function showCustomConfirm(message, title = 'Confirm', callback) {
            document.getElementById('confirm-dialog-title').textContent = title;
            document.getElementById('confirm-dialog-message').textContent = message;
            confirmCallback = callback;
            document.getElementById('confirm-dialog').style.display = 'flex';
        }

        function showGenericPrompt({ title, label, callback, required = false }) {
            document.getElementById('generic-prompt-title').textContent = title;
            document.getElementById('generic-prompt-label').textContent = label;
            const input = document.getElementById('generic-prompt-input');
            input.value = '';
            input.required = required;
            
            genericPromptCallback = callback;
            
            document.getElementById('generic-prompt-dialog').style.display = 'flex';
            input.focus();
        }
    </script>
</body>
</html>`;
}