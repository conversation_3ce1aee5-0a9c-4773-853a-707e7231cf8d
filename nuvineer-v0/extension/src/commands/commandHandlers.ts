import * as vscode from 'vscode';
import * as path from 'path';
import { GitService } from '../services/gitService';
import { ApiService, DesignDocJobStatusResponse } from '../services/apiService';
import { UserService } from '../services/userService';
import { DomainConceptsService } from '../services/domainConceptsService';
import { DesignDocWorkflowService } from '../services/designDocWorkflowService';
import { WebviewManager } from '../ui/webviewManager';
import { ConfigManager } from '../config/configManager';
import { PromptFormatter } from '../formatters/promptFormatter';
import { 
    getDecisionsWebviewContent, 
    getPromptGeneratorWebviewContent,
    getFeedbackWebviewContent,
    getDesignDocGeneratorHtml,
    getDesignDocJobsStatusHtml,
    getSingleDecisionWebviewContent // Added import
} from '../ui/htmlGenerators';
import { getDesignDocWorkflowWebviewContent } from '../webview';
import { AI_AGENT_PROTOCOL_CONTENT, AI_AGENT_PROTOCOL_FILE_NAME } from '../aiAgentProtocol';
import { ensureDirectory } from '../utils';
import { convertFrontmatterToMetadata, DesignDocFrontmatter, ReferencedDecisionForDoc, DesignDocFeedbackItem } from '../types';
import { DESIGN_DOC_STATUS_POLL_INTERVAL, DESIGN_DOC_STATUS_POLL_TIMEOUT } from '../config/constants';
import { DomainConcept } from '../services/domainConceptsService';
import { escapeHtml } from '../utils';
import { handleAddressAiComment } from './aiReviewCommands';

export interface CommandDependencies {
    gitService: GitService;
    apiService: ApiService;
    userService: UserService;
    domainConceptsService: DomainConceptsService;
    designDocWorkflowService: DesignDocWorkflowService;
    webviewManager: WebviewManager;
    context: vscode.ExtensionContext;
}

export class CommandHandlers {
    private selectedDomainConcepts: string[] = [];
    private activeJobPollers: Map<string, { 
        intervalId: NodeJS.Timeout, 
        timeoutId: NodeJS.Timeout, 
        statusBarItem: vscode.StatusBarItem | null,
        jobInfo: {
            jobId: string;
            taskTitle: string;
            status: string;
            currentPhase?: string;
            phaseProgress?: string;
            progressPercentage?: number;
            message?: string;
            output_file_path?: string;
            phase3_output?: any;
            referenced_decisions?: any[];
            created_at: string;
            updated_at: string;
        }
    }> = new Map();

    constructor(private deps: CommandDependencies) {}

    async validateApiKey(): Promise<void> {
        let config = ConfigManager.getConfig();

        if (!config.apiKey) {
            config.apiKey = await ConfigManager.promptAndSaveApiKey();
            if (!config.apiKey) {
                vscode.window.showErrorMessage('ArchKnow API Key is required for validation.');
                return;
            }
        }

        const apiService = new ApiService(config.apiUrl, config.apiKey);
        await apiService.validateApiKey();
    }

    async getContextForFile(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('ArchKnow: No active editor open.');
            return;
        }

        const document = editor.document;
        if (document.uri.scheme !== 'file') {
            vscode.window.showWarningMessage('ArchKnow: Please open a file from your workspace.');
            return;
        }

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "ArchKnow: Fetching relevant context...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 10, message: "Getting repository info..." });

            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
            if (!repoInfo) {
                vscode.window.showErrorMessage('ArchKnow: Could not determine repository info. Is this a Git repository with an "origin" remote?');
                return;
            }

            const absoluteFilePath = document.uri.fsPath;
            const repoRelativePath = path.relative(repoInfo.repoRoot, absoluteFilePath);
            console.log(`ArchKnow: Active file repo-relative path: ${repoRelativePath}`);

            const config = ConfigManager.getConfig();
            if (!config.apiKey) {
                vscode.window.showErrorMessage('ArchKnow: API Key not configured. Please set it using the command palette.');
                return;
            }

            progress.report({ increment: 30, message: `Fetching decisions for ${repoRelativePath}...` });
            console.log(`ArchKnow: Fetching context for ${repoRelativePath} in ${repoInfo.slug}`);
            
            const apiService = new ApiService(config.apiUrl, config.apiKey);
            const decisions = await apiService.fetchRelevantDecisions(repoInfo.slug, repoRelativePath);

            if (decisions.length > 0) {
                console.log('ArchKnow: First decision received:', JSON.stringify(decisions[0], null, 2));
                if (decisions[0].metadata) {
                    console.log('ArchKnow: Type of related_files in first decision:', typeof decisions[0].metadata.related_files, 'Is Array:', Array.isArray(decisions[0].metadata.related_files));
                } else {
                    console.log('ArchKnow: First decision received has no metadata property.');
                }
            } else {
                console.log('ArchKnow: No decisions received from API.');
            }

            progress.report({ increment: 50, message: "Displaying results..." });

            const htmlContent = getDecisionsWebviewContent(decisions, config.apiUrl);
            const panel = this.deps.webviewManager.createOrShowDecisionsPanel(htmlContent, config.apiUrl);
            this.deps.webviewManager.setupFileOpenHandler(panel, repoInfo.repoRoot);

            progress.report({ increment: 10 });
        });
    }

    async getContextForSpecificFile(fileUri: vscode.Uri): Promise<void> {
        if (fileUri.scheme !== 'file') {
            vscode.window.showWarningMessage('ArchKnow: Please select a file from your workspace.');
            return;
        }

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "ArchKnow: Fetching relevant context...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 10, message: "Getting repository info..." });

            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
            if (!repoInfo) {
                vscode.window.showErrorMessage('ArchKnow: Could not determine repository info. Is this a Git repository with an "origin" remote?');
                return;
            }

            const absoluteFilePath = fileUri.fsPath;
            const repoRelativePath = path.relative(repoInfo.repoRoot, absoluteFilePath);
            console.log(`ArchKnow: Selected file repo-relative path: ${repoRelativePath}`);

            const config = ConfigManager.getConfig();
            if (!config.apiKey) {
                vscode.window.showErrorMessage('ArchKnow: API Key not configured. Please set it using the command palette.');
                return;
            }

            progress.report({ increment: 30, message: `Fetching decisions for ${repoRelativePath}...` });
            console.log(`ArchKnow: Fetching context for ${repoRelativePath} in ${repoInfo.slug}`);
            
            const apiService = new ApiService(config.apiUrl, config.apiKey);
            const decisions = await apiService.fetchRelevantDecisions(repoInfo.slug, repoRelativePath);

            if (decisions.length > 0) {
                console.log('ArchKnow: First decision received:', JSON.stringify(decisions[0], null, 2));
                if (decisions[0].metadata) {
                    console.log('ArchKnow: Type of related_files in first decision:', typeof decisions[0].metadata.related_files, 'Is Array:', Array.isArray(decisions[0].metadata.related_files));
                } else {
                    console.log('ArchKnow: First decision received has no metadata property.');
                }
            } else {
                console.log('ArchKnow: No decisions received from API.');
            }

            progress.report({ increment: 50, message: "Displaying results..." });

            const fileName = path.basename(absoluteFilePath);
            const htmlContent = getDecisionsWebviewContent(decisions, config.apiUrl);
            const panel = this.deps.webviewManager.createOrShowDecisionsPanel(htmlContent, `Context: ${fileName}`);
            this.deps.webviewManager.setupFileOpenHandler(panel, repoInfo.repoRoot);

            progress.report({ increment: 10 });
        });
    }

    async getDecisionsByConcepts(domainConcepts?: string[]): Promise<void> {
        if (!Array.isArray(domainConcepts) || domainConcepts.length === 0) {
            vscode.window.showInformationMessage('Domain concepts argument not provided, prompting user.');
            const conceptsInput = await vscode.window.showInputBox({
                prompt: "Enter comma-separated domain concepts",
                placeHolder: "e.g., authentication, database, caching",
                ignoreFocusOut: true,
                validateInput: text => {
                    return text && text.trim().length > 0 ? null : "Please enter at least one domain concept.";
                }
            });

            if (!conceptsInput) {
                vscode.window.showInformationMessage('Operation cancelled by user.');
                return;
            }

            domainConcepts = conceptsInput.split(',').map(c => c.trim()).filter(Boolean);

            if (domainConcepts.length === 0) {
                vscode.window.showErrorMessage('ArchKnow: No valid domain concepts entered.');
                return;
            }
            vscode.window.showInformationMessage(`Concepts received from input: ${domainConcepts.join(', ')}`);
        }

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `ArchKnow: Fetching decisions for concepts: ${domainConcepts.join(', ')}...`,
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 10, message: "Getting repository info..." });
            
            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
            const config = ConfigManager.getConfig();

            if (!repoInfo || !config.apiKey) {
                vscode.window.showErrorMessage('ArchKnow: Could not determine repository slug/root or API Key not configured.');
                return;
            }

            progress.report({ increment: 30, message: `Fetching decisions...` });
            const apiService = new ApiService(config.apiUrl, config.apiKey);
            const decisions = await apiService.fetchDecisionsByConcepts(repoInfo.slug, domainConcepts);

            progress.report({ increment: 50, message: "Displaying results..." });

            // For now, reuse the decisions panel - could be split into separate panels later
            const htmlContent = getDecisionsWebviewContent(decisions, config.apiUrl);
            const panel = this.deps.webviewManager.createOrShowDecisionsPanel(htmlContent, config.apiUrl);
            this.deps.webviewManager.setupFileOpenHandler(panel, repoInfo.repoRoot);
        });
    }

    async openContextGenerator(): Promise<void> {
        const column = vscode.window.activeTextEditor?.viewColumn;

        // Get repoSlug for refreshDomainConceptsIfNeeded
        const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
        if (repoInfo && repoInfo.slug) {
            await this.deps.domainConceptsService.refreshDomainConceptsIfNeeded(repoInfo.slug);
        } else {
            console.warn("ArchKnow: Could not get repoSlug in openContextGenerator, domain concepts might not be up-to-date.");
            // Optionally, show a warning to the user or skip refresh
        }

        const htmlContent = getPromptGeneratorWebviewContent(this.deps.context.extensionUri, this.deps.webviewManager.createOrShowPromptGeneratorPanel('').webview);
        const panel = this.deps.webviewManager.createOrShowPromptGeneratorPanel(htmlContent);

        // Send concepts to webview
        const domainConceptsWithCounts = await this.deps.domainConceptsService.getDomainConcepts();
        panel.webview.postMessage({ 
            command: 'loadConcepts', 
            concepts: domainConceptsWithCounts,
            selectedConcepts: this.selectedDomainConcepts
        });

        // Set up message handler
        panel.webview.onDidReceiveMessage(
            async message => {
                await this.handlePromptGeneratorMessage(message);
            },
            undefined,
            this.deps.context.subscriptions
        );
    }

    private async handlePromptGeneratorMessage(message: any): Promise<void> {
        switch (message.command) {
            case 'conceptsSelected':
                if (Array.isArray(message.concepts)) {
                    this.selectedDomainConcepts = message.concepts
                    ;
                }
                break;

            case 'refreshDomainConcepts':
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: "ArchKnow: Refreshing domain concepts...",
                    cancellable: false
                }, async (progress) => {
                    try {
                        const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
                        const config = ConfigManager.getConfig();
                        
                        if (!repoInfo || !config.apiKey) {
                            throw new Error('Repository info or API key missing');
                        }

                        // Use refreshDomainConceptsIfNeeded instead of direct fetch
                        await this.deps.domainConceptsService.refreshDomainConceptsIfNeeded(repoInfo.slug);
                        
                        const updatedConcepts = await this.deps.domainConceptsService.getDomainConcepts();
                        const panel = this.deps.webviewManager.createOrShowPromptGeneratorPanel('');
                        panel.webview.postMessage({
                            command: 'loadConcepts',
                            concepts: updatedConcepts,
                            selectedConcepts: this.selectedDomainConcepts
                        });
                        
                        vscode.window.showInformationMessage('ArchKnow: Domain concepts refreshed successfully.');
                    } catch (error: any) {
                        console.error('ArchKnow: Failed to refresh domain concepts:', error);
                        vscode.window.showErrorMessage(`ArchKnow: Failed to refresh domain concepts: ${error.message}`);
                    }
                });
                break;

            case 'requestFileSelection':
                try {
                    const excludePattern = '**/{node_modules,.git,.vscode,dist,build,out,*.log,*.lock,*.bak,*.tmp}/**';
                    const files = await vscode.workspace.findFiles('**/*', excludePattern, 5000);

                    if (files.length === 0) {
                        vscode.window.showInformationMessage("ArchKnow: No files found in the workspace.");
                        return;
                    }

                    const items: vscode.QuickPickItem[] = files.map(fileUri => ({
                        label: vscode.workspace.asRelativePath(fileUri, false),
                        description: path.dirname(vscode.workspace.asRelativePath(fileUri, false))
                    }));

                    const selectedItem = await vscode.window.showQuickPick(items, {
                        placeHolder: 'Search for a file by name or path to get ArchKnow context',
                        matchOnDescription: true,
                        matchOnDetail: false
                    });

                    if (selectedItem && selectedItem.label) {
                        const panel = this.deps.webviewManager.createOrShowPromptGeneratorPanel('');
                        panel.webview.postMessage({
                            command: 'fileSelected',
                            filePath: selectedItem.label
                        });
                    }
                } catch (error: any) {
                    console.error("ArchKnow: Error during file selection:", error);
                    vscode.window.showErrorMessage(`ArchKnow: Error searching for files: ${error.message}`);
                }
                break;

            case 'fetchContext':
                await this.handleFetchContext(message);
                break;
        }
    }

    private async handleFetchContext(message: any): Promise<void> {
        console.log("ArchKnow: Received fetchContext message:", message);
        
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "ArchKnow: Fetching context...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 10, message: "Preparing..." });

            const config = ConfigManager.getConfig();
            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();

            if (!repoInfo || !config.apiKey) {
                vscode.window.showErrorMessage('ArchKnow: Repository slug/root or API Key missing. Configure first.');
                return;
            }

            const workspaceFolders = vscode.workspace.workspaceFolders;
            const workspaceRoot = workspaceFolders && workspaceFolders.length > 0 ? workspaceFolders[0].uri.fsPath : undefined;
            if (!workspaceRoot) {
                vscode.window.showErrorMessage('ArchKnow: Cannot determine workspace root path.');
                return;
            }

            const apiService = new ApiService(config.apiUrl, config.apiKey);
            let decisions = [];

            try {
                if (message.type === 'file') {
                    const workspaceRelativePath = message.value as string;
                    if (!workspaceRelativePath || typeof workspaceRelativePath !== 'string') {
                        vscode.window.showErrorMessage('ArchKnow: No file selected for context.');
                        return;
                    }

                    const absoluteFilePath = path.join(workspaceRoot, workspaceRelativePath);
                    const repoRelativePath = path.relative(repoInfo.repoRoot, absoluteFilePath);
                    console.log(`ArchKnow: Fetching for file (Repo Relative): ${repoRelativePath}`);

                    progress.report({ increment: 30, message: `Fetching for file ${repoRelativePath}...` });
                    decisions = await apiService.fetchRelevantDecisions(repoInfo.slug, repoRelativePath);
                } else if (message.type === 'concepts' && Array.isArray(message.value) && message.value.length > 0) {
                    progress.report({ increment: 30, message: `Fetching for concepts: ${message.value.join(', ')}...` });
                    decisions = await apiService.fetchDecisionsByConcepts(repoInfo.slug, message.value);
                } else if (message.type === 'prompt') {
                    progress.report({ increment: 30, message: `Fetching for prompt: "${message.value}"...` });
                    decisions = await apiService.fetchDecisionsByPrompt(repoInfo.slug, message.value);
                } else {
                    console.warn('ArchKnow: Invalid fetchContext message type or missing context value.');
                    return;
                }

                progress.report({ increment: 50, message: "Formatting results..." });
                const contextValue: string | string[] = message.value as string | string[];
                const formattedPrompt = PromptFormatter.formatDecisionsForPrompt(decisions, message.type, contextValue);
                console.log("ArchKnow: Formatted prompt ready.");

                progress.report({ increment: 100, message: "Done." });
                
                const panel = this.deps.webviewManager.createOrShowPromptGeneratorPanel('');
                panel.webview.postMessage({ command: 'displayPrompt', content: formattedPrompt });

            } catch (error: any) {
                console.error("ArchKnow: Error during fetchContext:", error);
                vscode.window.showErrorMessage(`ArchKnow: Error fetching context: ${error.message}`);
            }
        });
    }

    async syncDomainConcepts(): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "ArchKnow: Syncing ArchKnow data...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 10, message: "Checking repository info..." });
            
            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
            const config = ConfigManager.getConfig();
            
            if (!repoInfo || !config.apiKey) {
                vscode.window.showErrorMessage('ArchKnow: Cannot sync ArchKnow data - missing repository info or API key.');
                return;
            }
            
            progress.report({ increment: 20, message: "Fetching domain concepts..." });
            
            try {
                // Use refreshDomainConceptsIfNeeded instead of direct fetch
                await this.deps.domainConceptsService.refreshDomainConceptsIfNeeded(repoInfo.slug);
                
                // Assuming these also need similar age checks - for now, keeping them as is,
                // but they might need their own refreshIfNeeded logic if they are also CSV based and frequently called.
                progress.report({ increment: 40, message: "Fetching best practices..." });
                await this.deps.domainConceptsService.fetchAndStoreBestPracticesForRepo(repoInfo.slug);
                progress.report({ increment: 60, message: "Fetching developer guidance..." });
                await this.deps.domainConceptsService.fetchAndStoreDevGuidanceForRepo(repoInfo.slug);
                
                const panel = this.deps.webviewManager.createOrShowPromptGeneratorPanel('');
                if (panel) {
                    const domainConceptsWithCounts = await this.deps.domainConceptsService.getDomainConcepts();
                    panel.webview.postMessage({ 
                        command: 'loadConcepts', 
                        concepts: domainConceptsWithCounts,
                        selectedConcepts: this.selectedDomainConcepts
                    });
                }
                
                progress.report({ increment: 100, message: "ArchKnow data synced successfully." });
                vscode.window.showInformationMessage('ArchKnow: ArchKnow data synced successfully.');
            } catch (error: any) {
                vscode.window.showErrorMessage(`ArchKnow: Failed to sync ArchKnow data: ${error.message}`);
                console.error('ArchKnow: ArchKnow Data Sync Error:', error);
            }
        });
    }

    async getFeedback(): Promise<void> {
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "ArchKnow: Analyzing Staged Changes",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 0, message: "Initializing..." });

            const config = ConfigManager.getConfig();
            const apiKey = config.apiKey;

            if (!apiKey) {
                vscode.window.showErrorMessage("API key not configured. Please configure it first via 'ArchKnow: Configure API Key'.");
                return;
            }
            progress.report({ increment: 10, message: "Validated API key." });

            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
            if (!repoInfo) {
                vscode.window.showErrorMessage("Failed to get repository information. Ensure you are in a Git repository.");
                return;
            }
            progress.report({ increment: 20, message: "Fetched repository info." });

            // Ask for milestone ID first
            const milestoneId = await vscode.window.showInputBox({
                prompt: "Enter the Milestone ID to validate against (e.g., M1.0, M2.0)",
                placeHolder: "e.g., M1.0",
                ignoreFocusOut: true,
                validateInput: (value: string) => {
                    if (!value || !value.trim()) {
                        return "Milestone ID is required";
                    }
                    if (!/^M\d+\.\d+$/.test(value.trim())) {
                        return "Milestone ID must be in format M#.# (e.g., M1.0, M2.0)";
                    }
                    return undefined;
                }
            });

            if (!milestoneId) {
                vscode.window.showInformationMessage("Milestone ID is required for feedback validation. Aborting.");
                return;
            }

            const useJobContext = 'Yes';

            let jobId: string | undefined;
            if (useJobContext === 'Yes') {
                jobId = await vscode.window.showInputBox({
                    prompt: "Enter the Job ID for the Design Document (optional, for enhanced context)",
                    placeHolder: "",
                    ignoreFocusOut: true,
                });
            }

            // Ask for GitHub issue URL for auto-extraction of design doc, or manual session ID
            const linkMethod = await vscode.window.showQuickPick(
                [
                    { label: 'GitHub Issue URL', description: 'Automatically extract design doc from issue (recommended)' },
                    { label: 'Manual Session ID', description: 'Manually enter design document session ID' },
                    { label: 'Skip', description: 'No design document linking' }
                ],
                {
                    placeHolder: 'How would you like to link to a design document?',
                    ignoreFocusOut: true
                }
            );

            let designSessionId: string | undefined;
            let issueUrl: string | undefined;

            if (linkMethod?.label === 'GitHub Issue URL') {
                issueUrl = await vscode.window.showInputBox({
                    prompt: "Enter GitHub Issue URL (design doc will be auto-extracted)",
                    placeHolder: "e.g., https://github.com/owner/repo/issues/123",
                    ignoreFocusOut: true,
                    validateInput: (value) => {
                        if (value && value.trim() !== '') {
                            const githubIssueRegex = /^https:\/\/github\.com\/[^\/]+\/[^\/]+\/issues\/\d+$/;
                            if (!githubIssueRegex.test(value.trim())) {
                                return 'Please enter a valid GitHub issue URL (e.g., https://github.com/owner/repo/issues/123)';
                            }
                        }
                        return null;
                    }
                });
            } else if (linkMethod?.label === 'Manual Session ID') {
                designSessionId = await vscode.window.showInputBox({
                    prompt: "Enter Design Document Session ID (from Design Doc Wizard URL)",
                    placeHolder: "e.g., 29a4008d-b0d5-4cde-a608-bea96cece4c5",
                    ignoreFocusOut: true,
                    validateInput: (value) => {
                        if (value && value.trim() !== '') {
                            // Simple UUID validation
                            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
                            if (!uuidRegex.test(value.trim())) {
                                return 'Please enter a valid UUID format (e.g., 29a4008d-b0d5-4cde-a608-bea96cece4c5)';
                            }
                        }
                        return null;
                    }
                });
            }

            progress.report({ increment: 30, message: "Configuration collected." });

            try {
                progress.report({ increment: 40, message: "Getting staged changes..." });
                const stagedChanges = await this.deps.gitService.getStagedChanges();
                if (stagedChanges.length === 0) {
                    vscode.window.showInformationMessage("No staged changes found to analyze.");
                    return;
                }

                const branchName = await this.deps.gitService.getCurrentBranch();
                progress.report({ increment: 60, message: "Analyzing changes with milestone context..." });
                
                progress.report({ increment: 70, message: "Fetching streamlined feedback from API..." });
                const feedbackResponse = await this.deps.apiService.fetchMilestoneFeedback(
                    repoInfo.slug,
                    stagedChanges,
                    milestoneId,
                    jobId,
                    undefined, // existingContext
                    undefined, // implementationPlan
                    designSessionId?.trim() || undefined,
                    issueUrl?.trim() || undefined
                );
                progress.report({ increment: 90, message: "Processing feedback..." });

                if (feedbackResponse) {
                    const panelTitle = `Milestone ${milestoneId} Validation - ${branchName}`;
                    this.deps.webviewManager.createOrShowStreamlinedFeedbackPanel(feedbackResponse, panelTitle);
                    
                    // Show summary notification
                    const summary = feedbackResponse.summary || 'Feedback analysis complete';
                    const integrationStatus = feedbackResponse.integration_blocked ? '🚫 BLOCKED' : '✅ READY';
                    vscode.window.showInformationMessage(`${integrationStatus}: ${summary}`);
                } else {
                    vscode.window.showErrorMessage("Failed to get feedback from the API or no feedback was returned.");
                }
            } catch (error: any) {
                console.error("Error in getFeedback:", error); // Using console.error
                vscode.window.showErrorMessage(`Error getting feedback: ${error.message}`);
            } finally {
                progress.report({ increment: 100, message: "Completed." });
            }
        });
    }

    async startTask(): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "ArchKnow: Starting Task from GitHub Issue...",
            cancellable: true
        }, async (progress, token) => {
            progress.report({ increment: 10, message: "Getting repository and user info..." });

            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
            if (!repoInfo) {
                vscode.window.showErrorMessage('ArchKnow: Could not determine repository info.');
                return;
            }

            const userHandle = await this.deps.userService.getGitHubUserHandle();
            if (!userHandle) {
                vscode.window.showErrorMessage('Please set your GitHub handle first.');
                return;
            }

            if (token.isCancellationRequested) return;

            progress.report({ increment: 30, message: "Fetching GitHub issues..." });
            const issues = await this.deps.apiService.fetchGitHubIssues(repoInfo.slug, userHandle);

            if (token.isCancellationRequested) return;

            if (issues.length === 0) {
                vscode.window.showInformationMessage(`No open issues found for you in ${repoInfo.slug}.`);
                return;
            }

            progress.report({ increment: 60, message: "Waiting for issue selection..." });

            const issueItems = issues.map(issue => ({
                label: `#${issue.number}: ${issue.title}`,
                description: `Opened by ${issue.user.login}`,
                detail: issue.body,
                issue: issue
            }));

            const selectedIssueItem = await vscode.window.showQuickPick(issueItems, {
                placeHolder: 'Select a GitHub issue to start working on',
                matchOnDescription: true,
                matchOnDetail: true
            });

            if (token.isCancellationRequested) return;

            if (selectedIssueItem) {
                const { title, body, html_url } = selectedIssueItem.issue;
                
                // Create enhanced task description with GitHub issue link
                const enhancedTaskDescription = `**GitHub Issue:** ${html_url}

${body || ''}`;
                
                const htmlContent = getDesignDocGeneratorHtml({
                    taskTitle: title,
                    taskDescription: enhancedTaskDescription
                });
                const panel = this.deps.webviewManager.createDesignDocGeneratorPanel(htmlContent);

                panel.webview.onDidReceiveMessage(async (message) => {
                    await this.handleDesignDocGenerationRequest(message, repoInfo, userHandle, panel);
                });
            } else {
                vscode.window.showInformationMessage('No issue selected.');
            }
        });
    }

    async generateDesignDoc(): Promise<void> {
        console.log('ArchKnow [GenerateDesignDoc]: Command triggered');
        
        // Clear any existing pollers for previous jobs if the panel is reopened.
        this.activeJobPollers.forEach(poller => {
            clearInterval(poller.intervalId);
            clearTimeout(poller.timeoutId);
        });
        this.activeJobPollers.clear();

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "ArchKnow: Generating design document...",
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 10, message: "Initializing..." });
            
            const config = ConfigManager.getConfig();
            console.log('ArchKnow [GenerateDesignDoc]: API URL configured as', config.apiUrl);
            
            progress.report({ increment: 20, message: "Checking repository info..." });
            console.log('ArchKnow [GenerateDesignDoc]: Current repo info before refresh:', this.deps.gitService.getRepoInfo() ? 'exists' : 'null');
            
            let repoInfo = this.deps.gitService.getRepoInfo();
            if (!repoInfo) {
                console.log('ArchKnow [GenerateDesignDoc]: Initializing Git repo info');
                await this.deps.gitService.initializeGitRepoInfo();
                repoInfo = this.deps.gitService.getRepoInfo();
                console.log('ArchKnow [GenerateDesignDoc]: After refresh:', repoInfo ? 'initialized' : 'still null');
            }
            
            if (!config.apiKey) {
                console.log('ArchKnow [GenerateDesignDoc]: No API key found');
                vscode.window.showErrorMessage('ArchKnow API Key is required. Please set it first.');
                return;
            }
            
            if (!repoInfo || !repoInfo.slug) {
                console.log('ArchKnow [GenerateDesignDoc]: No repo info found:', repoInfo);
                vscode.window.showErrorMessage('Could not determine repository information.');
                return;
            }

            // Get GitHub handle
            const userHandle = await this.deps.userService.getGitHubUserHandle();
            if (!userHandle) {
                vscode.window.showErrorMessage('Please set your GitHub handle first to generate a design document.');
                return;
            }
            
            console.log('ArchKnow [GenerateDesignDoc]: Using repo slug:', repoInfo.slug);
            console.log('ArchKnow [GenerateDesignDoc]: Using GitHub handle:', userHandle);
            
            progress.report({ increment: 30, message: "Creating design doc form..." });
            
            const htmlContent = getDesignDocGeneratorHtml();
            const panel = this.deps.webviewManager.createDesignDocGeneratorPanel(htmlContent);
            console.log('ArchKnow [GenerateDesignDoc]: Webview panel created and displayed');
            
            panel.webview.onDidReceiveMessage(async (message) => {
                await this.handleDesignDocGenerationRequest(message, repoInfo, userHandle, panel, progress);
            });
        });
    }

    private async handleDesignDocGenerationRequest(
        message: any, 
        repoInfo: { slug: string; repoRoot: string; }, 
        userHandle: string,
        panel: vscode.WebviewPanel,
        progress?: vscode.Progress<{ message?: string; increment?: number }>
    ) {
        console.log('ArchKnow [handleDesignDocGenerationRequest]: Received message from webview:', message.command);
        if (message.command === 'generateDesignDoc') {
            const { taskTitle, taskDescription, initialApproachIdeas } = message;
            console.log('ArchKnow [handleDesignDocGenerationRequest]: Task details -',
                `Title: ${taskTitle?.substring(0, 30)}..., ` +
                `Description length: ${taskDescription?.length || 0}, ` +
                `Ideas provided: ${initialApproachIdeas ? 'yes' : 'no'}`);

            if (!taskTitle || !taskDescription) {
                console.log('ArchKnow [handleDesignDocGenerationRequest]: Missing required fields');
                vscode.window.showErrorMessage('Task title and description are required.');
                panel.webview.postMessage({ command: 'error', message: 'Task title and description are required.' });
                return;
            }

            if (progress) progress.report({ increment: 50, message: "Generating design document..." });

            try {
                panel.webview.postMessage({ command: 'generationStarted' });

                const config = ConfigManager.getConfig();
                const apiService = new ApiService(config.apiUrl, config.apiKey!!);
                const jobSubmissionResponse = await apiService.generateDesignDoc(
                    taskTitle,
                    taskDescription,
                    initialApproachIdeas,
                    repoInfo.slug,
                    userHandle
                );

                console.log('ArchKnow [handleDesignDocGenerationRequest]: Job submitted:', jobSubmissionResponse);

                vscode.window.showInformationMessage(`Design document generation for "${taskTitle}" started (Job ID: ${jobSubmissionResponse.jobId}). You'll be notified upon completion.`);
                panel.webview.postMessage({
                    command: 'jobSubmitted',
                    jobId: jobSubmissionResponse.jobId,
                    message: jobSubmissionResponse.message
                });

                this.startPollingForJobStatus(jobSubmissionResponse.jobId, taskTitle, apiService);

                panel.dispose();
                if (progress) progress.report({ increment: 100, message: "Job submitted to background processor." });

            } catch (error: any) {
                console.error('ArchKnow [handleDesignDocGenerationRequest]: Error generating design document:', error);
                vscode.window.showErrorMessage(`Failed to generate design document: ${error.message}`);
                panel.webview.postMessage({ command: 'error', message: error.message });
            }
        }
    }

    private startPollingForJobStatus(jobId: string, taskTitle: string, apiService: ApiService): void {
        console.log(`ArchKnow [JobPolling]: Starting to poll for job ID: ${jobId}, Task: "${taskTitle}"`);

        // If we're already polling for this job, don't start another poller
        if (this.activeJobPollers.has(jobId)) {
            console.log(`ArchKnow [JobPolling]: Already polling for job ID: ${jobId}`);
            return;
        }

        const startTime = Date.now();
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.show();

        // Initialize job info
        const jobInfo = {
            jobId,
            taskTitle,
            status: 'pending',
            progressPercentage: 0,
            phaseProgress: undefined as string | undefined,
            message: undefined as string | undefined,
            referenced_decisions: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        const intervalId = setInterval(async () => {
            try {
                // Check if we've exceeded the timeout
                if (Date.now() - startTime > DESIGN_DOC_STATUS_POLL_TIMEOUT) {
                    console.warn(`ArchKnow [JobPolling]: Timeout for job ID: ${jobId}. Stopping polling.`);
                    vscode.window.showWarningMessage(`ArchKnow: Timed out waiting for design document generation for "${taskTitle}".`);
                    statusBarItem.dispose();
                    this.stopPollingForJob(jobId);
                    return;
                }

                const statusResponse = await apiService.getJobStatus(jobId);
                if (!statusResponse) {
                    console.error(`ArchKnow [JobPolling]: No status response for job ${jobId}. Stopping polling.`);
                    statusBarItem.dispose();
                    this.stopPollingForJob(jobId);
                    return;
                }

                console.log(`ArchKnow [JobPolling]: Status for job ${jobId}: ${statusResponse.status}, Phase: ${statusResponse.currentPhase}`);
                console.log(`ArchKnow [JobPolling]: For job ${jobId}, output_file_path: "${statusResponse.output_file_path}"`);
                if (statusResponse.phase3_output) {
                console.log(`ArchKnow [JobPolling]: For job ${jobId}, phase3_output object (raw):`, statusResponse.phase3_output);
                console.log(`ArchKnow [JobPolling]: For job ${jobId}, phase3_output?.title value: "${statusResponse.phase3_output?.title}" (type: ${typeof statusResponse.phase3_output?.title})`);
                }
                if (statusResponse.phase3_5_refined_data_models_output) {
                console.log(`ArchKnow [JobPolling]: For job ${jobId}, phase3_5_refined_data_models_output object (raw):`, statusResponse.phase3_5_refined_data_models_output);
                }

                // Check if we have newer data in our local job info to prevent overriding with stale data
                const currentJobInfo = this.activeJobPollers.get(jobId)?.jobInfo;
                if (currentJobInfo && statusResponse.updated_at && currentJobInfo.updated_at) {
                    const responseUpdatedTime = new Date(statusResponse.updated_at).getTime();
                    const currentUpdatedTime = new Date(currentJobInfo.updated_at).getTime();
                    
                    // If our current data is newer than the response, skip this update to prevent reverting to older state
                    if (currentUpdatedTime > responseUpdatedTime) {
                        console.log(`ArchKnow [JobPolling]: Skipping update for job ${jobId} as current data is newer (${currentJobInfo.updated_at} > ${statusResponse.updated_at})`);
                        return;
                    }
                }

                // Update job info with proper fallback handling
                Object.assign(jobInfo, {
                    status: statusResponse.status,
                    currentPhase: statusResponse.currentPhase,
                    phaseProgress: statusResponse.phaseProgress || jobInfo.phaseProgress, // Keep existing if response doesn't have it
                    progressPercentage: statusResponse.progressPercentage !== undefined ? statusResponse.progressPercentage : jobInfo.progressPercentage,
                    message: statusResponse.message || jobInfo.message,
                    output_file_path: statusResponse.output_file_path,
                    phase3_output: statusResponse.phase3_output,
                    referenced_decisions: statusResponse.referenced_decisions,
                    updated_at: statusResponse.updated_at || new Date().toISOString()
                });

                // Update status bar with progress
                if (statusResponse.status === 'processing' || statusResponse.status === 'pending') {
                    const progressText = statusResponse.phaseProgress || 'Processing...';
                    const percentage = statusResponse.progressPercentage || 0;
                    statusBarItem.text = `$(sync~spin) ArchKnow: ${progressText} (${percentage}%)`;
                    statusBarItem.tooltip = `Generating design document for: ${taskTitle}\nStatus: ${statusResponse.message || 'Processing...'}`;
                }

                // Only update the status panel if we have valid progress information
                if (statusResponse.phaseProgress || statusResponse.progressPercentage !== undefined) {
                    this.deps.webviewManager.updateDesignDocJobStatus(jobId, statusResponse);
                }

                if (statusResponse.status === 'completed') {
                    statusBarItem.dispose();
                    console.log(`ArchKnow [JobPolling]: Job ${jobId} marked COMPLETED. Evaluating content for document opening decision.`);
                    console.log(`ArchKnow [JobPolling]: Condition Part 1 (output_file_path): ${!!statusResponse.output_file_path} (Value: "${statusResponse.output_file_path}")`);
                    console.log(`ArchKnow [JobPolling]: Condition Part 2 (phase3_output object): ${!!statusResponse.phase3_output}`);
                    if (statusResponse.phase3_output) {
                        console.log(`ArchKnow [JobPolling]: Condition Part 3 (phase3_output.title): ${!!statusResponse.phase3_output.title} (Value: "${statusResponse.phase3_output.title}", Type: ${typeof statusResponse.phase3_output.title})`);
                    } else {
                        console.log(`ArchKnow [JobPolling]: Condition Part 3 (phase3_output.title): SKIPPED (phase3_output object is null/undefined)`);
                    }

                    if (statusResponse.phase3_5_refined_data_models_output) {
                        console.log(`ArchKnow [JobPolling]: Condition Part 4 (phase3_5_refined_data_models_output object): ${!!statusResponse.phase3_5_refined_data_models_output}`);
                    } else {
                        console.log(`ArchKnow [JobPolling]: Condition Part 4 (phase3_5_refined_data_models_output object): SKIPPED (phase3_5_refined_data_models_output object is null/undefined)`);
                    }

                    vscode.window.showInformationMessage(`Design document '${statusResponse.taskTitle}' generated successfully!`, 'Open Document')
                        .then(async selection => {
                            if (selection === 'Open Document') {
                                if (statusResponse.output_file_path && statusResponse.phase3_output) {
                                    console.error(`ArchKnow [JobPolling]: Job ${jobId} PASSED content validation (output_file_path, phase3_output, and phase3_output.title all valid). Proceeding to create/open document.`);
                                    
                                    const phase3OutputForDoc = { ...statusResponse.phase3_output };
                                    if (phase3OutputForDoc.hasOwnProperty('referenced_decisions')) {
                                        console.log(`ArchKnow [JobPolling]: Removing 'referenced_decisions' from phase3_output for job ${jobId} before passing to createAndOpenDesignDocFromJob, as it will be passed separately.`);
                                        delete phase3OutputForDoc.referenced_decisions;
                                    }

                                    const referencedDecisionsArg = statusResponse.referenced_decisions
                                        ? statusResponse.referenced_decisions as ReferencedDecisionForDoc[] // Cast and handle null/undefined
                                        : undefined;
                                    console.log("Referenced decisions for job " + jobId + ": " + JSON.stringify(referencedDecisionsArg, null, 2));
                                    console.error(`ArchKnow [JobPolling]: Data being passed to createAndOpenDesignDocFromJob for job ${jobId}:`, {
                                        output_file_path: statusResponse.output_file_path,
                                        phase3_output: phase3OutputForDoc,
                                        taskTitle: statusResponse.taskTitle,
                                        jobId: jobId,
                                        phase3_5_refined_data_models_output: statusResponse.phase3_5_refined_data_models_output,
                                    });
                                    console.error(`ArchKnow [JobPolling]: Explicit referencedDecisions arg count for job ${jobId}: ${referencedDecisionsArg?.length || 0}`);
                                    
                                    try {
                                        const userHandle = await this.deps.userService.getGitHubUserHandle();
                                        console.error('ArchKnow [JobPolling]: Creating and opening design document from job');
                                        const docUri = await this.deps.designDocWorkflowService.createAndOpenDesignDocFromJob(
                                            {
                                                output_file_path: statusResponse.output_file_path,
                                                phase3_output: phase3OutputForDoc,
                                                taskTitle: statusResponse.taskTitle,
                                                jobId: jobId,
                                                phase3_5_refined_data_models_output: statusResponse.phase3_5_refined_data_models_output,
                                            },
                                            userHandle,
                                            referencedDecisionsArg // Pass explicitly typed and handled referenced_decisions
                                        );
                                        if (docUri) {
                                            console.log(`ArchKnow [JobPolling]: Requested to open document: ${docUri.fsPath}`);
                                        }
                                    } catch (e: any) {
                                        vscode.window.showErrorMessage(`ArchKnow: Error creating or opening generated document: ${e.message}`);
                                        console.error('ArchKnow [JobPolling]: Error in createAndOpenDesignDocFromJob call:', e);
                                    }
                                } else {
                                    console.warn(`ArchKnow [JobPolling]: Job ${jobId} FAILED content validation. Not calling createAndOpenDesignDocFromJob.`);
                                    let missingParts = [];
                                    if (!statusResponse.output_file_path) missingParts.push("output file path");
                                    if (!statusResponse.phase3_output) missingParts.push("core document content (phase3_output)");
                                    else if (!statusResponse.phase3_output.title) missingParts.push("document title in content (phase3_output.title)");
                                    // Optionally, add a check for referenced_decisions if it's considered critical for opening
                                    // if (!statusResponse.referenced_decisions) missingParts.push("referenced decisions");
                                    
                                    const errorMessage = `ArchKnow: Document generation complete for '${statusResponse.taskTitle}', but critical information is missing from the server response: ${missingParts.join(', ')}. Cannot open document.`;
                                    vscode.window.showErrorMessage(errorMessage);
                                    console.error(errorMessage, statusResponse);
                                }
                            }
                        });
                    this.stopPollingForJob(jobId);
                } else if (statusResponse.status === 'error') {
                    statusBarItem.dispose();
                    vscode.window.showErrorMessage(`ArchKnow: Failed to generate design document '${statusResponse.taskTitle}'. ${statusResponse.message || 'Unknown error.'}`);
                    this.stopPollingForJob(jobId);
                }

            } catch (error: any) {
                console.error(`ArchKnow [JobPolling]: Error polling for job ${jobId} status:`, error);
                // Update status bar to show error state
                statusBarItem.text = `$(error) ArchKnow: Error checking status`;
                statusBarItem.tooltip = `Error: ${error.message}`;
                
                // If it's a fatal error (like 404), stop polling
                if (error.status === 404 || error.message.includes('not found')) {
                    console.warn(`ArchKnow [JobPolling]: Job ${jobId} not found. Stopping polling.`);
                    statusBarItem.dispose();
                    this.stopPollingForJob(jobId);
                }
            }
        }, DESIGN_DOC_STATUS_POLL_INTERVAL);

        const timeoutId = setTimeout(() => {
            if (this.activeJobPollers.has(jobId)) {
                console.warn(`ArchKnow [JobPolling]: Absolute timeout reached for job ID: ${jobId}. Stopping polling.`);
                vscode.window.showWarningMessage(`ArchKnow: Timed out waiting for design document generation for "${taskTitle}" (absolute).`);
                statusBarItem.dispose();
                this.stopPollingForJob(jobId);
            }
        }, DESIGN_DOC_STATUS_POLL_TIMEOUT + 5000);

        this.activeJobPollers.set(jobId, { intervalId, timeoutId, statusBarItem, jobInfo });
    }

    private stopPollingForJob(jobId: string): void {
        const poller = this.activeJobPollers.get(jobId);
        if (poller) {
            clearInterval(poller.intervalId);
            clearTimeout(poller.timeoutId);
            this.activeJobPollers.delete(jobId);
            console.log(`ArchKnow [JobPolling]: Stopped polling for job ID: ${jobId}`);

            // Update the status panel to reflect the removed job
            const activeJobs = Array.from(this.activeJobPollers.values()).map(p => p.jobInfo);
            this.deps.webviewManager.updateDesignDocJobs(activeJobs);
        }
    }

    // Add a new command to show the status panel
    async showDesignDocJobsStatus(): Promise<void> {
        try {
            // First, ensure we have the GitHub handle
            const userHandle = await this.deps.userService.getGitHubUserHandle();
            if (!userHandle) {
                vscode.window.showErrorMessage('Please set your GitHub handle first to view your jobs.');
                return;
            }

            // Get the current repository slug
            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
            if (!repoInfo || !repoInfo.slug) {
                vscode.window.showErrorMessage('ArchKnow: Could not determine the current repository. Unable to fetch repository-specific jobs.');
                return;
            }
            const repositorySlug = repoInfo.slug;

            const htmlContent = getDesignDocJobsStatusHtml();
            const panel = this.deps.webviewManager.createOrShowDesignDocJobsStatusPanel(htmlContent);
            
            // Fetch all jobs for the current user's GitHub handle and repository
            const jobs = await this.deps.apiService.getAllDesignDocJobs(userHandle, repositorySlug);
            
            // Update the panel with all jobs
            this.deps.webviewManager.updateDesignDocJobs(jobs);

            // Start polling for any in-progress jobs
            for (const job of jobs) {
                if (job.status === 'pending' || job.status === 'processing') {
                    // Only start polling if we're not already polling for this job
                    if (!this.activeJobPollers.has(job.jobId)) {
                        this.startPollingForJobStatus(job.jobId, job.taskTitle, this.deps.apiService);
                    }
                }
            }

            // Set up auto-refresh for jobs every 30 seconds
            let refreshInterval: NodeJS.Timeout | undefined = setInterval(async () => {
                try {
                    // Ensure repoInfo is still valid or re-fetch if necessary, though for a stable panel, it should be.
                    // For simplicity, we'll reuse the repositorySlug obtained when the panel was opened.
                    const updatedJobs = await this.deps.apiService.getAllDesignDocJobs(userHandle, repositorySlug);
                    
                    // Filter and enhance jobs with any newer data from active pollers
                    const enhancedJobs = updatedJobs.map(job => {
                        const activePoller = this.activeJobPollers.get(job.jobId);
                        if (activePoller && activePoller.jobInfo.updated_at) {
                            const dbUpdatedTime = new Date(job.updated_at || 0).getTime();
                            const pollerUpdatedTime = new Date(activePoller.jobInfo.updated_at).getTime();
                            
                            // If active poller has newer data, use it instead
                            if (pollerUpdatedTime > dbUpdatedTime) {
                                console.log(`ArchKnow [AutoRefresh]: Using active poller data for job ${job.jobId} (${activePoller.jobInfo.updated_at} > ${job.updated_at})`);
                                return {
                                    ...job,
                                    ...activePoller.jobInfo,
                                    // Ensure we keep the database's essential data
                                    jobId: job.jobId,
                                    taskTitle: job.taskTitle,
                                    created_at: job.created_at
                                };
                            }
                        }
                        return job;
                    });
                    
                    this.deps.webviewManager.updateDesignDocJobs(enhancedJobs);

                    // Check for new in-progress jobs that we're not polling for
                    for (const job of updatedJobs) {
                        if ((job.status === 'pending' || job.status === 'processing') && !this.activeJobPollers.has(job.jobId)) {
                            this.startPollingForJobStatus(job.jobId, job.taskTitle, this.deps.apiService);
                        }
                    }
                } catch (error) {
                    console.error('Error refreshing jobs:', error);
                }
            }, 30000);

            // Clean up the refresh interval when the panel is closed
            panel.onDidDispose(() => {
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                    refreshInterval = undefined;
                }
            }, null, this.deps.context.subscriptions);

            // Handle messages from the webview
            panel.webview.onDidReceiveMessage(
                async message => {
                    if (message.command === 'openDocument') {
                        try {
                            // Fetch fresh job data instead of relying on potentially stale captured array
                            console.log(`ArchKnow [OpenDocument]: Fetching fresh job data for jobId: ${message.jobId}`);
                            const freshJobs = await this.deps.apiService.getAllDesignDocJobs(userHandle, repositorySlug);
                            const job = freshJobs.find(j => j.jobId === message.jobId);
                            
                            if (!job) {
                                console.error(`ArchKnow [OpenDocument]: Job ${message.jobId} not found in fresh data`);
                                vscode.window.showErrorMessage(`Job ${message.jobId} not found. It may have been deleted or moved.`);
                                return;
                            }
                            
                            console.log(`ArchKnow [OpenDocument]: Found job ${job.jobId}, status: ${job.status}, has output_file_path: ${!!job.output_file_path}`);
                            
                            if (job.output_file_path && job.status === 'completed') {
                                const userHandle = await this.deps.userService.getGitHubUserHandle();

                                let phase3OutputForDocFromJob = { ...job.phase3_output }; 
                                if (phase3OutputForDocFromJob.hasOwnProperty('referenced_decisions')) {
                                    console.log(`ArchKnow [OpenDocument]: Removing 'referenced_decisions' from job.phase3_output for job ${job.jobId} before passing to createAndOpenDesignDocFromJob, as it will be passed separately.`);
                                    delete phase3OutputForDocFromJob.referenced_decisions;
                                }

                                const referencedDecisionsArgFromJob = job.referenced_decisions
                                    ? job.referenced_decisions as ReferencedDecisionForDoc[]
                                    : undefined;
                                
                                console.log(`ArchKnow [OpenDocument]: Creating document for job ${job.jobId}:`, {
                                    output_file_path: job.output_file_path,
                                    taskTitle: job.taskTitle,
                                    jobId: job.jobId,
                                    has_phase3_output: !!job.phase3_output,
                                    has_phase3_5_output: !!job.phase3_5_refined_data_models_output,
                                    referenced_decisions_count: referencedDecisionsArgFromJob?.length || 0
                                });
                                
                                await this.deps.designDocWorkflowService.createAndOpenDesignDocFromJob(
                                    {
                                        output_file_path: job.output_file_path,
                                        phase3_output: phase3OutputForDocFromJob,
                                        taskTitle: job.taskTitle,
                                        jobId: job.jobId,
                                        phase3_5_refined_data_models_output: job.phase3_5_refined_data_models_output
                                    },
                                    userHandle,
                                    referencedDecisionsArgFromJob
                                );
                                console.log(`ArchKnow [OpenDocument]: Document creation completed for job ${job.jobId}`);
                            } else {
                                const missingItems = [];
                                if (!job.output_file_path) missingItems.push("output file path");
                                if (job.status !== 'completed') missingItems.push(`status is '${job.status}' instead of 'completed'`);
                                
                                const errorMsg = `Cannot open document for job ${job.jobId}: ${missingItems.join(', ')}.`;
                                console.warn(`ArchKnow [OpenDocument]: ${errorMsg}`);
                                vscode.window.showWarningMessage(errorMsg);
                            }
                        } catch (error: any) {
                            console.error(`ArchKnow [OpenDocument]: Error opening document for job ${message.jobId}:`, error);
                            vscode.window.showErrorMessage(`Failed to open document: ${error.message}`);
                        }
                    } else if (message.command === 'refreshJobs') {
                        // Manual refresh of jobs
                        // Reuse the repositorySlug obtained when the panel was opened.
                        const updatedJobs = await this.deps.apiService.getAllDesignDocJobs(userHandle, repositorySlug);
                        this.deps.webviewManager.updateDesignDocJobs(updatedJobs);
                        
                        // Check for new in-progress jobs
                        for (const job of updatedJobs) {
                            if ((job.status === 'pending' || job.status === 'processing') && !this.activeJobPollers.has(job.jobId)) {
                                this.startPollingForJobStatus(job.jobId, job.taskTitle, this.deps.apiService);
                            }
                        }
                    }
                },
                undefined,
                this.deps.context.subscriptions
            );
        } catch (error) {
            console.error('Error showing design doc jobs status:', error);
            vscode.window.showErrorMessage('Failed to load design doc jobs status.');
        }
    }

    async showDecisionById(decisionId: string): Promise<void> {
        if (!decisionId) {
            vscode.window.showErrorMessage('ArchKnow: No decision ID provided.');
            return;
        }

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `ArchKnow: Fetching decision ${decisionId}...`,
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 10, message: "Initializing..." });

            const config = ConfigManager.getConfig();
            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug(); // Get repo slug and root

            if (!config.apiKey || !config.apiUrl || !repoInfo?.slug || !repoInfo?.repoRoot) {
                vscode.window.showErrorMessage('ArchKnow: Configuration (API Key, URL, or Repo Info) missing.');
                progress.report({ increment: 100 });
                return;
            }

            progress.report({ increment: 30, message: "Fetching decision details..." });
            // Ensure this.deps.apiService is used, not a new instance if it relies on constructor-injected config
            const decision = await this.deps.apiService.fetchDecisionById(repoInfo.slug, decisionId);

            if (!decision) {
                // The fetchDecisionById method now shows its own error messages.
                progress.report({ increment: 100 });
                return;
            }

            progress.report({ increment: 70, message: "Displaying decision..." });

            const panelTitle = `Decision: ${decision.metadata?.title || decisionId}`;
            const htmlContent = getSingleDecisionWebviewContent(decision, config.apiUrl, panelTitle);
            
            const panel = this.deps.webviewManager.createOrShowSingleDecisionPanel(htmlContent, panelTitle);
            
            // Setup message handler for interactions from the webview (e.g., opening files or other decisions)
            panel.webview.onDidReceiveMessage(
                async (message) => {
                    if (message.command === 'openFile') {
                        try {
                            if (!message.filePath) throw new Error("No file path provided in message");
                            // Ensure filePath is treated as relative to repoRoot
                            const absoluteFilePath = path.join(repoInfo.repoRoot, message.filePath);
                            const fileUri = vscode.Uri.file(absoluteFilePath);
                            
                            // Check if file exists before trying to open
                            await vscode.workspace.fs.stat(fileUri);
                            const document = await vscode.workspace.openTextDocument(fileUri);
                            await vscode.window.showTextDocument(document, { preview: false });
                        } catch (error: any) {
                            console.error(`ArchKnow: Error opening file ${message.filePath}:`, error);
                            vscode.window.showErrorMessage(`ArchKnow: Could not open file: ${message.filePath}. ${error.message}`);
                        }
                    } else if (message.command === 'viewDecision' && message.decisionId) {
                        // Recursively call showDecisionById to display another decision
                        // This allows navigating between linked decisions within the webview
                        await this.showDecisionById(message.decisionId);
                    } else if (message.command === 'log') { // Optional: for debugging messages from webview
                        console.log(`ArchKnow Webview (${panelTitle}):`, message.logMessage);
                    }
                },
                undefined,
                this.deps.context.subscriptions
            );
            progress.report({ increment: 100, message: "Done." });
        });
    }

    async configureApiKey(): Promise<void> {
        const newApiKey = await ConfigManager.promptAndSaveApiKey();
        if (newApiKey) {
            const config = ConfigManager.getConfig();
            const apiService = new ApiService(config.apiUrl, newApiKey);
            await apiService.validateApiKey();
        }
    }

    async openDesignDoc(docUri?: vscode.Uri): Promise<void> {
        console.log('ArchKnow [CommandHandlers.openDesignDoc]: Starting with URI:', docUri?.toString());
        
        if (!docUri) {
            if (vscode.window.activeTextEditor) {
                docUri = vscode.window.activeTextEditor.document.uri;
                console.log('ArchKnow [CommandHandlers.openDesignDoc]: Using active editor URI:', docUri.toString());
            } else {
                console.log('ArchKnow [CommandHandlers.openDesignDoc]: No URI provided and no active editor');
                vscode.window.showInformationMessage('Please open a design document file first, or right-click it in the explorer.');
                return;
            }
        }

        if (!this.deps.designDocWorkflowService.isDesignDocPath(docUri)) {
            console.log('ArchKnow [CommandHandlers.openDesignDoc]: Invalid design doc path:', docUri.toString());
            vscode.window.showWarningMessage('This command is for Markdown design documents in the .archknow/design_docs folder.');
            return;
        }
        
        console.log('ArchKnow [CommandHandlers.openDesignDoc]: Reading document metadata and content');
        let { metadata: frontmatter, content } = await this.deps.designDocWorkflowService.readDesignDocMetadata(docUri);

        // Log the content structure
        console.log('ArchKnow [CommandHandlers.openDesignDoc]: Content received:', {
            hasContent: !!content,
            contentLength: content?.length,
            firstLine: content?.split('\n')[0],
            numberOfLines: content?.split('\n').length,
            sections: content?.match(/^##?\s+(.+)$/gm)?.map(h => h.trim()) || []
        });

        // Log the actual frontmatter for debugging
        console.log('ArchKnow [CommandHandlers.openDesignDoc]: Raw frontmatter:', JSON.stringify(frontmatter, null, 2));
        console.log('ArchKnow [CommandHandlers.openDesignDoc]: Frontmatter status:', frontmatter?.status);
        console.log('ArchKnow [CommandHandlers.openDesignDoc]: Frontmatter validation result:', 
            'frontmatter exists:', !!frontmatter, 
            'has status:', !!frontmatter?.status,
            'status value:', frontmatter?.status);

        // Check if we have valid ArchKnow frontmatter
        // FIXED: More robust validation that handles edge cases
        const hasValidFrontmatter = frontmatter && 
                                   typeof frontmatter === 'object' && 
                                   frontmatter.status && 
                                   typeof frontmatter.status === 'string' &&
                                   frontmatter.status.trim() !== '';

        if (!hasValidFrontmatter) {
            console.log('ArchKnow [CommandHandlers.openDesignDoc]: Invalid or missing frontmatter detected');
            
            const choice = await vscode.window.showWarningMessage(
                `Design document ${path.basename(docUri.fsPath)} has no valid ArchKnow frontmatter. Initialize with 'pending' status?`,
                'Yes', 'No'
            );
            
            if (choice === 'Yes') {
                // Read the raw content without frontmatter parsing to preserve the original content
                const fileData = await vscode.workspace.fs.readFile(docUri);
                const rawContent = Buffer.from(fileData).toString('utf8');
                
                // Extract title from the first heading if available, or use existing title
                const titleMatch = rawContent.match(/^#\s+(.+)$/m);
                const extractedTitle = titleMatch ? titleMatch[1].trim() : 
                                     (frontmatter?.title && typeof frontmatter.title === 'string' ? frontmatter.title : 
                                      path.basename(docUri.fsPath, '.md'));
                
                // Create new metadata, preserving any existing valid fields
                const newMetadataInit: DesignDocFrontmatter = { 
                    status: 'pending', 
                    title: extractedTitle,
                    author: frontmatter?.author || '',
                    reviewers: Array.isArray(frontmatter?.reviewers) ? frontmatter.reviewers : []
                };
                
                // If we have existing frontmatter, preserve other valid fields using explicit property access
                if (frontmatter && typeof frontmatter === 'object') {
                    if (frontmatter.approver !== undefined) newMetadataInit.approver = frontmatter.approver;
                    if (frontmatter.feedback_requested_at !== undefined) newMetadataInit.feedback_requested_at = frontmatter.feedback_requested_at;
                    if (frontmatter.approved_at !== undefined) newMetadataInit.approved_at = frontmatter.approved_at;
                    if (frontmatter.implemented_at !== undefined) newMetadataInit.implemented_at = frontmatter.implemented_at;
                    if (frontmatter.feedback !== undefined) newMetadataInit.feedback = frontmatter.feedback;
                    if (frontmatter.implementation_plan_generated !== undefined) newMetadataInit.implementation_plan_generated = frontmatter.implementation_plan_generated;
                    if (frontmatter.implementation_plan_uri !== undefined) newMetadataInit.implementation_plan_uri = frontmatter.implementation_plan_uri;
                    if (frontmatter.lastUpdated !== undefined) newMetadataInit.lastUpdated = frontmatter.lastUpdated;
                }
                
                // Determine content to use
                let contentToWrite = content;
                
                // If content is empty or null, try to extract from raw content
                if (!contentToWrite || contentToWrite.trim() === '') {
                    // Check if raw content has frontmatter delimiters
                    const frontmatterMatch = rawContent.match(/^---\s*\n[\s\S]*?\n---\s*\n([\s\S]*)$/);
                    if (frontmatterMatch) {
                        contentToWrite = frontmatterMatch[1].trim();
                    } else {
                        // No frontmatter delimiters found, use entire content
                        contentToWrite = rawContent.trim();
                    }
                }
                
                console.log('ArchKnow [CommandHandlers.openDesignDoc]: Writing new frontmatter:', JSON.stringify(newMetadataInit, null, 2));
                console.log('ArchKnow [CommandHandlers.openDesignDoc]: Content to write (first 100 chars):', contentToWrite.substring(0, 100));
                
                // Write the updated document with proper frontmatter
                await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUri, newMetadataInit, contentToWrite);
                
                // Re-read the document after initialization
                const updatedDoc = await this.deps.designDocWorkflowService.readDesignDocMetadata(docUri);
                if (!updatedDoc.metadata) { 
                    vscode.window.showErrorMessage('Failed to initialize and read metadata after attempting to write.');
                    return;
                }
                frontmatter = updatedDoc.metadata; 
                content = updatedDoc.content;
                
                console.log('ArchKnow [CommandHandlers.openDesignDoc]: Successfully initialized frontmatter');
            } else {
                return; 
            }
        } else {
            console.log('ArchKnow [CommandHandlers.openDesignDoc]: Valid frontmatter found, proceeding normally');
        }
        
        // At this point frontmatter is guaranteed to be non-null due to the validation above
        if (!frontmatter) {
            vscode.window.showErrorMessage('Failed to read or initialize design document metadata.');
            return;
        }
        
        const metadata = convertFrontmatterToMetadata(frontmatter);
        const userHandle = await this.deps.userService.getGitHubUserHandle();
        
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            vscode.window.showErrorMessage('ArchKnow: Please open a workspace folder.');
            return;
        }
        const workspaceRoot = workspaceFolders[0].uri;
        
        // Create the panel first to get webview and extensionUri
        const panelTitle = `Doc: ${metadata.title || path.basename(docUri.fsPath)}`;
        const panel = this.deps.webviewManager.createOrShowDesignDocWorkflowPanel('', panelTitle, workspaceRoot);
        
        // Now generate the HTML content with the proper metadata structure
        const metadataWithWebview = {
            ...metadata,
            webview: panel.webview,
            extensionUri: this.deps.context.extensionUri
        };
        
        console.log('ArchKnow [CommandHandlers.openDesignDoc]: Calling getDesignDocWorkflowWebviewContent with content length:', content?.length);
        const htmlContent = getDesignDocWorkflowWebviewContent(docUri, metadataWithWebview, content, userHandle);
        console.log('ArchKnow [CommandHandlers.openDesignDoc]: Generated HTML content length:', htmlContent?.length);
        if (htmlContent) {
            if (htmlContent.length < 2000) { // Avoid flooding console for very large HTML
                 console.log('ArchKnow [CommandHandlers.openDesignDoc]: Generated HTML content (full):', htmlContent);
            } else {
                 console.log('ArchKnow [CommandHandlers.openDesignDoc]: Generated HTML content (first 1000 chars):', htmlContent.substring(0,1000));
                 console.log('ArchKnow [CommandHandlers.openDesignDoc]: Generated HTML content (last 1000 chars):', htmlContent.substring(htmlContent.length - 1000));
            }
        } else {
            console.error('ArchKnow [CommandHandlers.openDesignDoc]: CRITICAL: htmlContent is null or empty after calling getDesignDocWorkflowWebviewContent!');
        }

        // Set the HTML content on the panel
        panel.webview.html = htmlContent;

        // Handle messages from the webview
        let currentDocUri = docUri; // Track the current document URI
        
        const messageHandler = async (message: any) => {
            await this.handleDesignDocWorkflowMessage(message, currentDocUri);
        };
        
        panel.webview.onDidReceiveMessage(messageHandler, undefined, this.deps.context.subscriptions);
    }

    private async handleDesignDocWorkflowMessage(message: any, originalDocUri: vscode.Uri): Promise<void> {
        let docUriForOperation = originalDocUri;

        // Normalize message format - webview sends 'type' but handler expects 'command'
        if (message.type && !message.command) {
            message.command = message.type;
        }

        // Additional message command normalization for specific cases
        if (message.command === 'requestChanges') {
            message.command = 'requestChangesDocument';
        }

        // If a specific message provides its own URI string, and it's different, parse it.
        // This might be relevant for commands that explicitly target a *different* URI than originalDocUri.
        // For most current commands, originalDocUri is the correct context.
        if (message.docUriString && vscode.Uri.parse(message.docUriString).toString() !== originalDocUri.toString()) {
            // Potentially update docUriForOperation if the message intends to target a different file.
            // For now, we assume most operations relate to originalDocUri unless explicitly handled.
            // Example: A future 'linkToAnotherDoc' command might use message.docUriString.
            // For current commands like 'requestFeedback', 'saveDocument', etc., originalDocUri is appropriate.
            // So, originalDocUri is the one being edited.
        }

        let { metadata: currentMetadata, content: currentContent } = await this.deps.designDocWorkflowService.readDesignDocMetadata(docUriForOperation);
        
        if (!currentMetadata && message.command !== 'setGitHubHandle' && message.command !== 'saveDocument') { 
            vscode.window.showErrorMessage('Could not read document metadata to process action. The file might be missing or corrupted.');
            return;
        }

        let newUriForRefresh: vscode.Uri | null = docUriForOperation;
        const userHandle = await this.deps.userService.getGitHubUserHandle();
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri;

        if (!workspaceRoot && (
            message.command === 'requestFeedback' || 
            message.command === 'approveDesignDoc' || 
            message.command === 'approveDocument' || 
            message.command === 'requestChangesDocument'
        )) {
            vscode.window.showErrorMessage("ArchKnow: No workspace folder found. Cannot process document action that may involve moving files.");
            return;
        }

        switch (message.command) {
            case 'requestAiReview':
                console.log('ArchKnow [RequestAiReview]: Handling AI review request for document:', docUriForOperation.toString());
                if (!userHandle || !currentMetadata) {
                    console.warn('ArchKnow [RequestAiReview]: Missing user handle or metadata');
                    vscode.window.showWarningMessage('Cannot request AI review: Missing user handle or document metadata.');
                    return;
                }
                try {
                    await vscode.commands.executeCommand('archknow.requestAiReview', docUriForOperation);
                    console.log('ArchKnow [RequestAiReview]: AI review request completed successfully');
                    newUriForRefresh = docUriForOperation;
                } catch (error) {
                    console.error('ArchKnow [RequestAiReview]: Error requesting AI review:', error);
                    vscode.window.showErrorMessage(`Failed to get AI review: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'requestFeedback':
                console.log('ArchKnow [RequestFeedback]: Handling feedback request for document:', docUriForOperation.toString());
                if (!userHandle || !currentMetadata) {
                    console.warn('ArchKnow [RequestFeedback]: Missing user handle or metadata');
                    vscode.window.showWarningMessage('Cannot request feedback: Missing user handle or document metadata.');
                    return;
                }
                
                if (currentMetadata.status !== 'pending') {
                    vscode.window.showWarningMessage('Document is not in pending status. Cannot request feedback.');
                    return;
                }

                const approver = message.approver;
                const reviewers = message.reviewers || [];

                if (!approver || typeof approver !== 'string' || approver.trim() === '') {
                    vscode.window.showWarningMessage('Approver is required to request feedback.');
                    return;
                }

                console.log('ArchKnow [RequestFeedback]: Updating metadata for feedback request');
                console.log('ArchKnow [RequestFeedback]: Approver:', approver);
                console.log('ArchKnow [RequestFeedback]: Reviewers:', reviewers);

                currentMetadata.status = 'under_review';
                currentMetadata.approver = approver.trim();
                currentMetadata.reviewers = reviewers.length > 0 ? reviewers : [];
                currentMetadata.feedback_requested_at = new Date().toISOString();
                currentMetadata.lastUpdated = new Date().toISOString();
                if (!currentMetadata.feedback) currentMetadata.feedback = [];
                currentMetadata.feedback.push({
                    user: userHandle,
                    comment: `Feedback requested from ${approver}${reviewers.length > 0 ? ` and ${reviewers.join(', ')}` : ''}. Document moved to under_review.`,
                    timestamp: new Date().toISOString(),
                    type: 'system_action'
                });

                console.log('ArchKnow [RequestFeedback]: Updated metadata before save:', JSON.stringify({
                    author: currentMetadata.author,
                    approver: currentMetadata.approver,
                    reviewers: currentMetadata.reviewers,
                    status: currentMetadata.status,
                    feedback_requested_at: currentMetadata.feedback_requested_at
                }, null, 2));

                await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);
                
                // Verify the metadata was written correctly
                const { metadata: verifyMetadata } = await this.deps.designDocWorkflowService.readDesignDocMetadata(docUriForOperation);
                if (verifyMetadata) {
                    console.log('ArchKnow [RequestFeedback]: Verified metadata after save:', JSON.stringify({
                        author: verifyMetadata.author,
                        approver: verifyMetadata.approver,
                        reviewers: verifyMetadata.reviewers,
                        status: verifyMetadata.status
                    }, null, 2));
                    
                    if (verifyMetadata.approver !== currentMetadata.approver) {
                        console.error(`ArchKnow [RequestFeedback]: Approver mismatch! Expected: ${currentMetadata.approver}, Got: ${verifyMetadata.approver}`);
                    }
                    if (JSON.stringify(verifyMetadata.reviewers) !== JSON.stringify(currentMetadata.reviewers)) {
                        console.error(`ArchKnow [RequestFeedback]: Reviewers mismatch! Expected: ${JSON.stringify(currentMetadata.reviewers)}, Got: ${JSON.stringify(verifyMetadata.reviewers)}`);
                    }
                } else {
                    console.error('ArchKnow [RequestFeedback]: Failed to verify metadata after save');
                }
                const movedToReviewUri = await this.deps.designDocWorkflowService.moveDesignDoc(docUriForOperation, 'under_review');
                if (movedToReviewUri) {
                    newUriForRefresh = movedToReviewUri;
                    vscode.window.showInformationMessage(`Feedback requested for ${path.basename(newUriForRefresh.fsPath)}. Document moved to 'under_review'.`);
                } else {
                    vscode.window.showErrorMessage('Failed to move document after requesting feedback. Displaying current state.');
                    newUriForRefresh = docUriForOperation; // Refresh with original URI to show metadata updates
                }
                break;

            case 'editTitle':
                if (!currentMetadata) {
                    vscode.window.showWarningMessage('Cannot edit title: Document metadata not found.');
                    return;
                }
                try {
                    const newTitle = message.title;
                    if (!newTitle || typeof newTitle !== 'string' || newTitle.trim() === '') {
                        vscode.window.showWarningMessage('Invalid title provided.');
                        return;
                    }
                    
                    currentMetadata.title = newTitle.trim();
                    currentMetadata.lastUpdated = new Date().toISOString();
                    
                    await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);
                    vscode.window.showInformationMessage('Document title updated successfully.');
                    newUriForRefresh = docUriForOperation;
                } catch (error) {
                    console.error('ArchKnow [EditTitle]: Error updating title:', error);
                    vscode.window.showErrorMessage(`Failed to update title: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'editDocument':
                try {
                    // Open the document in VS Code's text editor for editing
                    const document = await vscode.workspace.openTextDocument(docUriForOperation);
                    await vscode.window.showTextDocument(document, { preview: false });
                    vscode.window.showInformationMessage('Document opened for editing in VS Code editor.');
                    newUriForRefresh = null; // Don't refresh webview as user is now editing
                } catch (error) {
                    console.error('ArchKnow [EditDocument]: Error opening document for editing:', error);
                    vscode.window.showErrorMessage(`Failed to open document for editing: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'approveDesignDoc':
            case 'approveDocument':
                if (!userHandle || !currentMetadata || !workspaceRoot) { 
                    vscode.window.showWarningMessage('Cannot approve document: Missing user handle, document metadata, or workspace configuration.');
                    return; 
                }
                
                if (currentMetadata.approver && userHandle.toLowerCase() !== currentMetadata.approver.toLowerCase()) {
                    vscode.window.showWarningMessage(`Only the designated approver (${currentMetadata.approver}) can approve this document. Your handle: ${userHandle}`);
                    return;
                }
                if (!currentMetadata.approver) {
                     vscode.window.showWarningMessage('No approver is designated for this document.');
                    return;
                }

                currentMetadata.status = 'ready_for_implementation';
                currentMetadata.approved_at = new Date().toISOString();
                if (!currentMetadata.feedback) currentMetadata.feedback = [];
                currentMetadata.feedback.push({
                    user: userHandle,
                    comment: 'Document approved.',
                    timestamp: new Date().toISOString(),
                    type: 'approval_comment'
                });

                await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);
                newUriForRefresh = await this.deps.designDocWorkflowService.moveDesignDoc(docUriForOperation, 'ready_for_implementation');
                if (newUriForRefresh) {
                    vscode.window.showInformationMessage(`Document ${path.basename(newUriForRefresh.fsPath)} approved and moved to 'ready_for_implementation'.`);
                } else {
                    vscode.window.showErrorMessage('Failed to move document after approval.');
                }
                break;

            case 'requestChangesDocument':
                if (!userHandle || !currentMetadata || !workspaceRoot) { 
                    vscode.window.showWarningMessage('Cannot request changes: Missing user handle, document metadata, or workspace configuration.');
                    return; 
                }
                
                if (currentMetadata.approver && userHandle.toLowerCase() !== currentMetadata.approver.toLowerCase()) {
                    vscode.window.showWarningMessage(`Only the designated approver (${currentMetadata.approver}) can request changes for this document. Your handle: ${userHandle}`);
                    return;
                }

                currentMetadata.status = 'changes-requested';
                if (!currentMetadata.feedback) currentMetadata.feedback = [];
                currentMetadata.feedback.push({
                    user: userHandle,
                    comment: 'Changes requested - document returned to author for revision.',
                    timestamp: new Date().toISOString(),
                    type: 'system_action'
                });

                await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);
                newUriForRefresh = await this.deps.designDocWorkflowService.moveDesignDoc(docUriForOperation, 'changes-requested');
                if (newUriForRefresh) {
                    vscode.window.showInformationMessage(`Changes requested for ${path.basename(newUriForRefresh.fsPath)}. Document moved to 'changes-requested'.`);
                } else {
                    vscode.window.showErrorMessage('Failed to move document after requesting changes.');
                }
                break;

            case 'generateImplementationPlan': 
                if (docUriForOperation) {
                    await this.deps.designDocWorkflowService.generateImplementationPlan(docUriForOperation, userHandle);
                    // The generateImplementationPlan method handles its own UI updates and document opening
                    // No need to refresh the webview as the user will be working with the implementation plan
                    newUriForRefresh = null;
                } else {
                    vscode.window.showErrorMessage('Document URI is not available for generating implementation plan.');
                    newUriForRefresh = null;
                }
                break;

            case 'setGitHubHandle':
                if (message.handle && typeof message.handle === 'string') {
                    const success = await this.deps.userService.setGitHubUserHandle(message.handle);
                    if (success) {
                        vscode.window.showInformationMessage(`GitHub handle set to: @${message.handle}`);
                        newUriForRefresh = message.docUriString ? vscode.Uri.parse(message.docUriString) : docUriForOperation;
                    } else {
                        vscode.window.showErrorMessage('Failed to set GitHub handle.');
                        newUriForRefresh = null;
                    }
                } else {
                    vscode.window.showWarningMessage('Invalid GitHub handle provided.');
                    newUriForRefresh = null;
                }
                break;

            case 'saveDocument':
                try {
                    // For saveDocument, we might not have currentMetadata if it's a new document
                    const frontmatterToSave = currentMetadata ? 
                        { ...currentMetadata } : 
                        { status: 'pending' } as any;

                    frontmatterToSave.title = message.title || frontmatterToSave.title || path.basename(docUriForOperation.fsPath, '.md');
                    frontmatterToSave.author = message.author || frontmatterToSave.author || userHandle;
                    frontmatterToSave.lastUpdated = new Date().toISOString();

                    await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, frontmatterToSave, message.content);
                    
                    // Send success message back to webview
                    const panel = this.deps.webviewManager.createOrShowDesignDocWorkflowPanel('', '', workspaceRoot!);
                    panel.webview.postMessage({
                        command: 'documentSaved',
                        content: message.content
                    });
                    
                    vscode.window.showInformationMessage('Document saved successfully.');
                    newUriForRefresh = docUriForOperation;
                } catch (error) {
                    console.error('ArchKnow [DesignDocWorkflow]: Error saving document:', error);
                    
                    // Send error message back to webview
                    if (workspaceRoot) {
                        const panel = this.deps.webviewManager.createOrShowDesignDocWorkflowPanel('', '', workspaceRoot);
                        panel.webview.postMessage({
                            command: 'documentSaveError',
                            error: error instanceof Error ? error.message : String(error)
                        });
                    }
                    
                    vscode.window.showErrorMessage(`Failed to save document: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'saveMetadata':
                if (!currentMetadata) {
                    vscode.window.showErrorMessage('ArchKnow: Cannot save metadata. Document metadata not found.');
                    newUriForRefresh = null;
                    return;
                }
                try {
                    // Update metadata with new values
                    if (message.updatedMetadata) {
                        Object.assign(currentMetadata, message.updatedMetadata);
                        currentMetadata.lastUpdated = new Date().toISOString();
                    }

                    await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);
                    vscode.window.showInformationMessage('Document metadata updated successfully.');
                    newUriForRefresh = docUriForOperation;
                } catch (error) {
                    console.error('ArchKnow [DesignDocWorkflow]: Error saving metadata:', error);
                    vscode.window.showErrorMessage(`Failed to save metadata: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'addComment':
                if (!currentMetadata) {
                    vscode.window.showErrorMessage('Cannot add comment: Document metadata not found.');
                    newUriForRefresh = null;
                    return;
                }
                try {
                    const comment = message.comment;
                    const selectedText = message.selectedText;
                    
                    if (!comment || typeof comment !== 'string' || comment.trim() === '') {
                        vscode.window.showWarningMessage('Comment text is required.');
                        return;
                    }
                    
                    if (!currentMetadata.feedback) currentMetadata.feedback = [];
                    currentMetadata.feedback.push({
                        user: userHandle || 'Anonymous',
                        comment: comment.trim(),
                        timestamp: new Date().toISOString(),
                        type: 'comment',
                        status: 'open'
                    });
                    currentMetadata.lastUpdated = new Date().toISOString();

                    await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);
                    
                    vscode.window.showInformationMessage('Comment added successfully.');
                    newUriForRefresh = docUriForOperation;
                } catch (error) {
                    console.error('ArchKnow [DesignDocWorkflow]: Error adding comment:', error);
                    vscode.window.showErrorMessage(`Failed to add comment: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'summarizeCommentsAi':
                if (!currentMetadata) {
                    vscode.window.showErrorMessage('Cannot summarize comments: Document metadata not found.');
                    newUriForRefresh = null;
                    return;
                }
                if (!this.deps.apiService) {
                    vscode.window.showErrorMessage('Cannot summarize comments: API service is not available.');
                    newUriForRefresh = null;
                    return;
                }
                try {
                    const allComments = [
                        ...(currentMetadata.feedback || []),
                        ...(currentMetadata.aiComments || [])
                    ];
                    
                    // Only summarize if we have comments to summarize
                    if (allComments.length === 0) {
                        vscode.window.showInformationMessage('No comments to summarize.');
                        newUriForRefresh = null;
                        return;
                    }
                    
                    const summary = await this.deps.apiService.summarizeComments(allComments);
                    if (summary) {
                        await this.deps.designDocWorkflowService.addSummaryToDocument(docUriForOperation, summary, 'AI Summarizer');
                        newUriForRefresh = docUriForOperation;
                    } else {
                        vscode.window.showErrorMessage('Failed to generate summary.');
                        newUriForRefresh = null;
                    }
                } catch (error) {
                    console.error('ArchKnow [DesignDocWorkflow]: Error summarizing comments:', error);
                    vscode.window.showErrorMessage(`Failed to summarize comments: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'addressSelectedComments':
                if (!currentMetadata) {
                    vscode.window.showErrorMessage('Cannot address comments: Document metadata not found.');
                    newUriForRefresh = null;
                    return;
                }
                
                const commentIds = message.commentIds;
                if (!Array.isArray(commentIds) || commentIds.length === 0) {
                    vscode.window.showWarningMessage('No comments selected to address.');
                    return;
                }
                
                console.log('ArchKnow [AddressSelectedComments]: Addressing comment IDs:', commentIds);
                
                // Find the selected comments from both feedback and AI comments
                const allComments = [
                    ...(currentMetadata.feedback || []),
                    ...(currentMetadata.aiComments || [])
                ];
                
                const selectedComments = allComments.filter(comment => 
                    commentIds.includes(comment.id || comment.timestamp)
                );
                
                if (selectedComments.length === 0) {
                    vscode.window.showWarningMessage('Selected comments not found.');
                    return;
                }
                
                try {
                    await vscode.window.withProgress({
                        location: vscode.ProgressLocation.Notification,
                        title: `ArchKnow: Addressing ${selectedComments.length} selected comments...`,
                        cancellable: false
                    }, async (progress) => {
                        try {
                            progress.report({ increment: 20, message: "Preparing comments for AI addressing..." });
                            
                            // Mark selected comments as pending AI addressal
                            selectedComments.forEach(comment => {
                                comment.status = 'pending-ai-addressal';
                            });

                            currentMetadata.lastUpdated = new Date().toISOString();
                            await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);

                            progress.report({ increment: 30, message: "Invoking AI service..." });

                            // Call the AI service to address the comments
                            await handleAddressAiComment(docUriForOperation, commentIds);
                            
                            progress.report({ increment: 50, message: "AI processing complete." });

                            vscode.window.showInformationMessage(`${selectedComments.length} comments addressed by AI.`);
                            newUriForRefresh = docUriForOperation;
                        } catch (error: any) {
                            console.error('ArchKnow [AddressSelectedComments]: Error addressing comments:', error);
                            vscode.window.showErrorMessage(`Failed to address comments: ${error.message}`);
                            newUriForRefresh = null;
                        }
                    });
                } catch (error) {
                    console.error('ArchKnow [AddressSelectedComments]: Error addressing selected comments:', error);
                    vscode.window.showErrorMessage(`Failed to address selected comments: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'addressAllOpenComments':
                if (!currentMetadata) {
                    vscode.window.showErrorMessage('Cannot address comments: Document metadata not found.');
                    newUriForRefresh = null;
                    return;
                }
                
                // Find all open comments
                const allOpenComments = [
                    ...(currentMetadata.feedback || []),
                    ...(currentMetadata.aiComments || [])
                ].filter(comment => comment.status === 'open');
                
                if (allOpenComments.length === 0) {
                    vscode.window.showInformationMessage('No open comments to address.');
                    newUriForRefresh = null;
                    return;
                }
                
                const allOpenCommentIds = allOpenComments.map(comment => comment.id || comment.timestamp);
                console.log('ArchKnow [AddressAllOpenComments]: Addressing all open comment IDs:', allOpenCommentIds);

                try {
                    await vscode.window.withProgress({
                        location: vscode.ProgressLocation.Notification,
                        title: "ArchKnow: AI is addressing all open comments...",
                        cancellable: false
                    }, async (progress) => {
                        progress.report({ increment: 20, message: "Marking comments for AI..." });

                        // Mark all open comments as pending AI addressal
                        allOpenComments.forEach(comment => {
                            comment.status = 'pending-ai-addressal';
                        });
                        currentMetadata.lastUpdated = new Date().toISOString();
                        await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);
                        
                        progress.report({ increment: 40, message: "Invoking AI service..." });
                        await handleAddressAiComment(docUriForOperation, allOpenCommentIds);

                        // Remove automatic status change - user will manually send for approval using the new button
                        progress.report({ increment: 100, message: "AI processing complete." });
                        vscode.window.showInformationMessage(`AI has addressed all ${allOpenCommentIds.length} open comments. Use "Send for Approval" to submit for review.`);
                    });
                    if (!newUriForRefresh) {
                        newUriForRefresh = docUriForOperation;
                    }
                } catch (error) {
                    console.error('ArchKnow [AddressAllOpenComments]: Error addressing comments:', error);
                    vscode.window.showErrorMessage(`Failed to address all open comments: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            case 'sendForApproval':
                if (!currentMetadata) {
                    vscode.window.showWarningMessage('Cannot send for approval: No document metadata found.');
                    return;
                }

                if (currentMetadata.status !== 'changes-requested') {
                    vscode.window.showWarningMessage('Document is not in changes-requested status.');
                    return;
                }

                // Check if there are any open comments remaining
                const remainingOpenComments = [
                    ...(currentMetadata.aiComments || []),
                    ...(currentMetadata.feedback || [])
                ].filter(c => c.status === 'open');

                if (remainingOpenComments.length > 0) {
                    const proceed = await vscode.window.showWarningMessage(
                        `There are still ${remainingOpenComments.length} open comments. Send for approval anyway?`,
                        { modal: true },
                        'Send Anyway',
                        'Cancel'
                    );
                    
                    if (proceed !== 'Send Anyway') {
                        return;
                    }
                }

                try {
                    // Move document to under_review status
                    currentMetadata.status = 'under_review';
                    if (!currentMetadata.feedback) currentMetadata.feedback = [];
                    currentMetadata.feedback.push({
                        user: userHandle || 'Author',
                        comment: `Document sent for approval after addressing comments.`,
                        timestamp: new Date().toISOString(),
                        type: 'system_action'
                    });
                    currentMetadata.lastUpdated = new Date().toISOString();

                    await this.deps.designDocWorkflowService.writeDesignDocMetadata(docUriForOperation, currentMetadata, currentContent);
                    const newUri = await this.deps.designDocWorkflowService.moveDesignDoc(docUriForOperation, 'under_review');
                    if (newUri) {
                        newUriForRefresh = newUri;
                        vscode.window.showInformationMessage(`Document sent for approval and moved to 'under_review'.`);
                    } else {
                        vscode.window.showErrorMessage('Failed to move document to under_review folder.');
                    }
                } catch (error) {
                    console.error('ArchKnow [SendForApproval]: Error sending for approval:', error);
                    vscode.window.showErrorMessage(`Failed to send document for approval: ${error instanceof Error ? error.message : String(error)}`);
                    newUriForRefresh = null;
                }
                break;

            default:
                console.warn(`ArchKnow [DesignDocWorkflow]: Unknown command received: ${message.command}`);
                break;
        }

        // Refresh webview logic - only refresh if we have a valid URI and workspace
        if (newUriForRefresh && workspaceRoot) {
            try {
                const panel = this.deps.webviewManager.createOrShowDesignDocWorkflowPanel('', '', workspaceRoot);
                const latestDocData = await this.deps.designDocWorkflowService.readDesignDocMetadata(newUriForRefresh);
                if (latestDocData.metadata) {
                    const metadata = convertFrontmatterToMetadata(latestDocData.metadata);
                    
                    // Create metadata with webview and extensionUri
                    const metadataWithWebview = {
                        ...metadata,
                        webview: panel.webview,
                        extensionUri: this.deps.context.extensionUri
                    };
                    
                    const htmlContent = getDesignDocWorkflowWebviewContent(newUriForRefresh, metadataWithWebview, latestDocData.content, userHandle);
                    panel.title = `Doc: ${metadata.title || path.basename(newUriForRefresh.fsPath)}`;
                    panel.webview.html = htmlContent;
                    
                    // CRITICAL FIX: Update the message handler to use the new URI
                    // Clear existing message handlers and set up new one with updated URI
                    if (newUriForRefresh) {
                        panel.webview.onDidReceiveMessage(
                            async (message) => {
                                await this.handleDesignDocWorkflowMessage(message, newUriForRefresh!);
                            },
                            undefined,
                            this.deps.context.subscriptions
                        );
                    }
                } else {
                    vscode.window.showErrorMessage('Failed to refresh document view: could not re-read metadata.');
                }
            } catch (error) {
                console.error('ArchKnow [DesignDocWorkflow]: Error refreshing webview:', error);
                vscode.window.showErrorMessage('Failed to refresh document view after operation.');
            }
        }
    }

    async ensureAiAgentProtocolFile(): Promise<void> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            console.warn('ArchKnow: Workspace path is undefined. Cannot ensure AI Agent Protocol file.');
            return;
        }

        const workspacePath = workspaceFolders[0].uri.fsPath;

        try {
            const archknowDir = path.join(workspacePath, '.archknow');
            const protocolFilePath = path.join(archknowDir, AI_AGENT_PROTOCOL_FILE_NAME);

            await ensureDirectory(archknowDir);
            console.log(`ArchKnow: Created directory: ${archknowDir}`);

            const protocolFileUri = vscode.Uri.file(protocolFilePath);
            try {
                await vscode.workspace.fs.stat(protocolFileUri);
                console.log(`ArchKnow: AI Agent Protocol file already exists at: ${protocolFilePath}`);
            } catch {
                await vscode.workspace.fs.writeFile(protocolFileUri, Buffer.from(AI_AGENT_PROTOCOL_CONTENT, 'utf8'));
                console.log(`ArchKnow: Created AI Agent Protocol file at: ${protocolFilePath}`);
                vscode.window.showInformationMessage('ArchKnow: AI Agent Implementation Protocol file created in .archknow folder.');
            }
        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to create or verify AI Agent Protocol file: ${error.message}`);
            console.error('ArchKnow: Error ensuring AI Agent Protocol file exists:', error);
        }
    }

    async handleFileOpen(document: vscode.TextDocument): Promise<void> {
        if (!this.deps.apiService) {
            vscode.window.showWarningMessage('ArchKnow API service is not available.');
            return;
        }

        const filePath = document.uri.fsPath;
        console.log(`ArchKnow: File opened: ${filePath}`);

        try {
            const repoInfo = await this.deps.gitService.getRepoInfo();
            if (!repoInfo || !repoInfo.slug) {
                vscode.window.showWarningMessage('ArchKnow: Could not determine repository slug. Cannot fetch context.');
                return;
            }
            const repoSlug = repoInfo.slug;

            // Convert absolute path to repository-relative path
            const repoRelativePath = path.relative(repoInfo.repoRoot, filePath);
            console.log(`ArchKnow: Repository-relative path: ${repoRelativePath}`);

            // Fetch relevant architectural decisions using repo-relative path
            const decisions = await this.deps.apiService.fetchRelevantDecisions(repoSlug, repoRelativePath);
            console.log(`ArchKnow: Fetched ${decisions.length} decisions for ${repoRelativePath}`);

            if (decisions.length > 0) {
                // Only refresh and get domain concepts if we have specific decisions to show
                await this.deps.domainConceptsService.refreshDomainConceptsIfNeeded(repoSlug);
                // const allDomainConcepts = await this.deps.domainConceptsService.getDomainConcepts(); // Not directly used for this panel anymore
                // console.log(`ArchKnow: Fetched ${allDomainConcepts.length} total domain concepts for context.`);

                const htmlContent = getDecisionsWebviewContent(decisions, this.deps.apiService.getApiUrl());
                const panel = this.deps.webviewManager.createOrShowDecisionsPanel(htmlContent, "File Context: " + path.basename(filePath));
                
                // Setup file open handler for links within the webview
                if (repoInfo.repoRoot) {
                    this.deps.webviewManager.setupFileOpenHandler(panel, repoInfo.repoRoot);
                }
            } else {
                // No specific decisions found, so do not show the panel and don't process domain concepts here.
                console.log(`ArchKnow: No specific architectural decisions found for ${repoRelativePath}. Panel will not be shown.`);
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Error fetching context for file: ${error.message}`);
            console.error(`ArchKnow: Error in handleFileOpen for ${filePath}:`, error);
        }
    }

    private generateGeneralContextHtml(concepts: DomainConcept[]): string {
        if (!concepts || concepts.length === 0) return "<p>No general domain concepts available.</p>";
        let html = "<h2>General Domain Concepts</h2><ul>";
        concepts.slice(0, 10).forEach(concept => {
            html += `<li>${escapeHtml(concept.concept)} (${concept.count})</li>`;
        });
        html += "</ul>";
        return html;
    }

    async reviewMilestone(): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "ArchKnow: Review Milestone Changes",
            cancellable: true
        }, async (progress, token) => {
            progress.report({ increment: 10, message: "Initializing..." });

            const config = ConfigManager.getConfig();
            if (!config.apiKey) {
                vscode.window.showErrorMessage("API key not configured. Please configure it first via 'ArchKnow: Configure API Key'.");
                return;
            }

            const repoInfo = await this.deps.gitService.getWorkspaceRepoSlug();
            if (!repoInfo) {
                vscode.window.showErrorMessage("Failed to get repository information. Ensure you are in a Git repository.");
                return;
            }
             if (token.isCancellationRequested) return;

            progress.report({ increment: 20, message: "Getting staged changes..." });
            const codeChanges = await this.deps.gitService.getStagedChanges();
            if (codeChanges.length === 0) {
                vscode.window.showInformationMessage("No staged changes found to review.");
                return;
            }
             if (token.isCancellationRequested) return;

            // Ask for GitHub issue URL instead of local files
            progress.report({ increment: 30, message: "Waiting for GitHub issue URL..." });
            const issueUrl = await vscode.window.showInputBox({
                prompt: "Enter the GitHub issue URL containing the design document and implementation plan",
                placeHolder: "https://github.com/owner/repo/issues/123",
                ignoreFocusOut: true,
                validateInput: (value) => {
                    if (!value || !value.trim()) {
                        return "GitHub issue URL is required";
                    }
                    if (!value.includes('github.com') || !value.includes('/issues/')) {
                        return "Please enter a valid GitHub issue URL";
                    }
                    return undefined;
                }
            });

            if (!issueUrl) {
                vscode.window.showInformationMessage('Milestone review cancelled: GitHub issue URL is required.');
                return;
            }
            if (token.isCancellationRequested) return;

            progress.report({ increment: 40, message: "Fetching design document and implementation plan from GitHub issue..." });
            
            let designDocContent: string;
            let implementationPlanContent: string;
            
            try {
                const issueDetails = await this.deps.apiService.fetchIssueDetailsForMcp(issueUrl);
                
                if (!issueDetails.success) {
                    vscode.window.showErrorMessage(`Failed to fetch issue details: ${issueDetails.error}`);
                    return;
                }

                if (!issueDetails.details) {
                    vscode.window.showErrorMessage("No design document or implementation plan found in the GitHub issue. Please ensure the issue has the 'ready-for-implementation' label and contains the required documents.");
                    return;
                }

                // Parse the issue details to extract design doc and implementation plan
                const detailsContent = issueDetails.details;
                
                // Extract design document (look for "## Design Document" section)
                const designDocMatch = detailsContent.match(/## Design Document\n\nFound in comment by [^\n]+:\n\n([\s\S]*?)(?=\n---\n|## Implementation Plan|$)/);
                designDocContent = designDocMatch ? designDocMatch[1].trim() : "Design document not found in issue.";
                
                // Extract implementation plan (look for "## Implementation Plan" section)
                const implPlanMatch = detailsContent.match(/## Implementation Plan\n\nFound in comment by [^\n]+:\n\n([\s\S]*?)(?=\n---\n|$)/);
                implementationPlanContent = implPlanMatch ? implPlanMatch[1].trim() : "Implementation plan not found in issue.";

                if (designDocContent === "Design document not found in issue." && implementationPlanContent === "Implementation plan not found in issue.") {
                    vscode.window.showErrorMessage("Neither design document nor implementation plan found in the GitHub issue. Please ensure the issue contains both documents.");
                    return;
                }

                progress.report({ increment: 50, message: "Documents extracted from GitHub issue." });

            } catch (error: any) {
                console.error("[ReviewMilestone] Error fetching issue details:", error);
                vscode.window.showErrorMessage(`Failed to fetch issue details: ${error.message}`);
                return;
            }
             if (token.isCancellationRequested) return;

            // Prompt for optional inputs
            const milestoneId = await vscode.window.showInputBox({ 
                prompt: "Enter the Milestone ID to review against (e.g., M1.0)",
                validateInput: (value) => value ? null : "Milestone ID is required."
            });
            if (!milestoneId) {
                vscode.window.showInformationMessage('Milestone review cancelled: Milestone ID is required.');
                return;
            }
            if (token.isCancellationRequested) return;

            const reviewIterationStr = await vscode.window.showInputBox({ prompt: "Enter review iteration number (e.g., 1 for first review)", value: "1" });
             if (token.isCancellationRequested) return;
            const reviewIteration = reviewIterationStr ? parseInt(reviewIterationStr, 10) : 1;
            
            progress.report({ increment: 60, message: "Sending to ArchKnow AI for review..." });
            
            try {
                const result = await this.deps.apiService.fetchMilestoneReviewForMcp(
                    repoInfo.slug,
                    codeChanges,
                    designDocContent,
                    implementationPlanContent,
                    milestoneId,
                    undefined, // milestoneTitle - will be auto-generated from implementation plan
                    undefined, // existingContext
                    issueUrl, // Pass the issue URL for context
                    reviewIteration,
                    undefined, // previouslyAcceptedRisks
                    undefined  // forceMinimalReview
                );
                 if (token.isCancellationRequested) return;

                progress.report({ increment: 90, message: "Displaying review results..." });
                
                // Use milestone title from API response, fallback to milestone ID
                const milestoneTitle = result.milestone_context?.title || result.task_context?.task_title || `Milestone ${milestoneId}`;
                const panelTitle = `Milestone Review: ${milestoneTitle}`;
                this.deps.webviewManager.createOrShowStreamlinedFeedbackPanel(result, panelTitle, undefined, milestoneId);
                
                const summary = result.summary || 'Milestone review complete.';
                const integrationStatus = result.integration_blocked ? '🚫 BLOCKED' : '✅ READY';
                vscode.window.showInformationMessage(`${integrationStatus}: ${summary}`);

            } catch (error: any) {
                console.error("[ReviewMilestone] Error fetching review:", error);
                vscode.window.showErrorMessage(`Failed to get milestone review: ${error.message}`);
            }

            progress.report({ increment: 100 });
        });
    }
}