import * as vscode from 'vscode';
import { AiReviewComment } from '../types';
import { ConfigManager } from '../config/configManager';
import { ArchKnowConfig } from '../config/configManager';
import { DesignDocFrontmatter } from '../types';

export async function handleRequestAiReview(docUri: vscode.Uri): Promise<void> {
    try {
        console.log('ArchKnow [handleRequestAiReview]: Starting AI review request');
        const document = await vscode.workspace.openTextDocument(docUri);
        const content = document.getText();
        const config: ArchKnowConfig = ConfigManager.getConfig();

        if (!config.apiKey) {
            throw new Error('API key not configured. Please set your ArchKnow API key in settings.');
        }

        // Extract repository slug from the document path
        const pathParts = docUri.fsPath.split('/');
        const repoSlug = pathParts.slice(-4, -2).join('/'); // Assuming path structure: .../owner/repo/.archknow/...
        console.log('ArchKnow [handleRequestAiReview]: Using repository slug:', repoSlug);

        console.log('ArchKnow [handleRequestAiReview]: Sending request to API');
        // Call the AI review API endpoint
        const response = await fetch(`${config.apiUrl}/api/extension/ai-review-doc`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                documentContent: content,
                repository_slug: repoSlug,
                api_key: config.apiKey
            }),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('ArchKnow [handleRequestAiReview]: API request failed:', {
                status: response.status,
                statusText: response.statusText,
                error: errorText
            });
            throw new Error(`AI review request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        console.log('ArchKnow [handleRequestAiReview]: Processing API response');
        const result = await response.json();
        
        // Update the document with the reviewed content
        const edit = new vscode.WorkspaceEdit();
        edit.replace(docUri, new vscode.Range(0, 0, document.lineCount, 0), result.reviewedDocumentContent);
        await vscode.workspace.applyEdit(edit);

        // Update the metadata to include the AI comments
        const { metadata } = await vscode.commands.executeCommand<{ metadata: DesignDocFrontmatter | null, content: string }>('archknow.readDesignDocMetadata', docUri);
        if (metadata) {
            metadata.aiComments = result.aiComments;
            await vscode.commands.executeCommand('archknow.writeDesignDocMetadata', docUri, metadata);
        }

        console.log('ArchKnow [handleRequestAiReview]: AI review completed successfully');
        vscode.window.showInformationMessage(`AI review completed with ${result.aiComments.length} comments`);

    } catch (error: any) {
        console.error('ArchKnow [handleRequestAiReview]: Error:', error);
        vscode.window.showErrorMessage(`Failed to get AI review: ${error.message}`);
    }
}

export async function handleAddressAiComment(docUri: vscode.Uri, commentIds: string[]): Promise<void> {
    try {
        console.log('ArchKnow [handleAddressAiComment]: Starting AI comment addressing');
        const document = await vscode.workspace.openTextDocument(docUri);
        const content = document.getText();
        const config: ArchKnowConfig = ConfigManager.getConfig();

        if (!config.apiKey) {
            throw new Error('API key not configured. Please set your ArchKnow API key in settings.');
        }

        // Extract repository slug from the document path
        const pathParts = docUri.fsPath.split('/');
        const repoSlug = pathParts.slice(-4, -2).join('/'); // Assuming path structure: .../owner/repo/.archknow/...
        console.log('ArchKnow [handleAddressAiComment]: Using repository slug:', repoSlug);

        console.log('ArchKnow [handleAddressAiComment]: Sending request to API');
        // Call the AI address feedback API endpoint
        const response = await fetch(`${config.apiUrl}/api/extension/ai-address-feedback`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                documentContentWithComments: content,
                repository_slug: repoSlug,
                api_key: config.apiKey,
                targetCommentIds: commentIds,
            }),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('ArchKnow [handleAddressAiComment]: API request failed:', {
                status: response.status,
                statusText: response.statusText,
                error: errorText
            });
            throw new Error(`AI feedback addressing failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        console.log('ArchKnow [handleAddressAiComment]: Processing API response');
        const result = await response.json();
        
        // Update the document with the addressed feedback
        const edit = new vscode.WorkspaceEdit();
        edit.replace(docUri, new vscode.Range(0, 0, document.lineCount, 0), result.updatedDocumentContent);
        await vscode.workspace.applyEdit(edit);

        console.log('ArchKnow [handleAddressAiComment]: AI feedback addressing completed successfully');
        vscode.window.showInformationMessage(`AI addressed ${commentIds.length} comments`);

    } catch (error: any) {
        console.error('ArchKnow [handleAddressAiComment]: Error:', error);
        vscode.window.showErrorMessage(`Failed to address AI comments: ${error.message}`);
    }
}

export async function handleUpdateCommentStatus(
    docUri: vscode.Uri,
    commentId: string,
    newStatus: 'addressed' | 'wont-fix' | 'pending-ai-addressal',
    resolutionText?: string
): Promise<void> {
    try {
        const document = await vscode.workspace.openTextDocument(docUri);
        const content = document.getText();

        // Find and update the comment in the document
        const commentRegex = new RegExp(
            `<!-- ARCHKNOW_COMMENT_START\\s*\\nID: ${commentId}[\\s\\S]*?ARCHKNOW_COMMENT_END -->`,
            'g'
        );

        const match = commentRegex.exec(content);
        if (!match) {
            throw new Error(`Comment with ID ${commentId} not found`);
        }

        const commentBlock = match[0];
        const updatedComment = commentBlock
            .replace(/STATUS: [^\n]+/, `STATUS: ${newStatus}`)
            .replace(/RESOLUTION_AUTHOR: [^\n]+\n/, '') // Remove existing resolution if any
            .replace(/RESOLUTION_TIMESTAMP: [^\n]+\n/, '')
            .replace(/RESOLUTION_TEXT: [^\n]+\n/, '');

        // Add resolution information if provided
        const resolutionInfo = resolutionText ? `
RESOLUTION_AUTHOR: ${vscode.workspace.getConfiguration('archknow').get('githubHandle') || 'unknown'}
RESOLUTION_TIMESTAMP: ${new Date().toISOString()}
RESOLUTION_TEXT: ${resolutionText}` : '';

        const finalComment = updatedComment.replace(
            'ARCHKNOW_COMMENT_END',
            `${resolutionInfo}\nARCHKNOW_COMMENT_END`
        );

        // Apply the update
        const edit = new vscode.WorkspaceEdit();
        const startPos = document.positionAt(match.index);
        const endPos = document.positionAt(match.index + commentBlock.length);
        edit.replace(docUri, new vscode.Range(startPos, endPos), finalComment);
        await vscode.workspace.applyEdit(edit);

        vscode.window.showInformationMessage(`Comment status updated to ${newStatus}`);

    } catch (error: any) {
        vscode.window.showErrorMessage(`Failed to update comment status: ${error.message}`);
    }
}