// Configuration constants for the ArchKnow extension

export const DESIGN_DOCS_BASE_FOLDER = '.archknow/design_docs';
export const DESIGN_DOCS_PENDING_FOLDER = `${DESIGN_DOCS_BASE_FOLDER}/pending`;
export const DESIGN_DOCS_UNDER_REVIEW_FOLDER = `${DESIGN_DOCS_BASE_FOLDER}/under_review`;
export const DESIGN_DOCS_READY_FOR_IMPLEMENTATION_FOLDER = `${DESIGN_DOCS_BASE_FOLDER}/ready_for_implementation`;
export const DESIGN_DOCS_IMPLEMENTED_FOLDER = `${DESIGN_DOCS_BASE_FOLDER}/implemented`;
export const DESIGN_DOCS_CHANGES_REQUESTED_FOLDER = `${DESIGN_DOCS_BASE_FOLDER}/changes_requested`;

export const AI_AGENT_PROTOCOL_FILE_NAME = 'AI_AGENT_IMPLEMENTATION_PROTOCOL.md';

export const GITHUB_HANDLE_KEY = 'archknow.githubUserHandle';

export const API_ENDPOINTS = {
    VALIDATE_KEY: '/api/settings/keys',
    RELEVANT_TO_FILE: '/api/extension/relevant-to-file',
    DECISIONS: '/api/extension/decisions',
    BY_CONCEPTS: '/api/extension/by-concepts',
    RELEVANT_TO_PROMPT: '/api/extension/relevant-to-prompt',
    FEEDBACK: '/api/extension/feedback',
    GENERATE_DESIGN_DOC: '/api/extension/generate-design-doc',
    GENERATE_DESIGN_DOC_STATUS: '/api/extension/generate-design-doc/status',
    DOMAIN_CONCEPTS: '/api/extension/domain-concepts',
    SINGLE_DECISION: '/api/extension/decisions',
    DESIGN_DOC_FEEDBACK: '/api/extension/design-doc-feedback',
    BEST_PRACTICES: '/api/extension/best-practices',
    DEV_GUIDANCE: '/api/extension/dev-guidance',
    FETCH_GITHUB_ISSUES: '/api/github/issues'
} as const;

export const UI_CONFIG = {
    STATUS_BAR_PRIORITY: 100,
    WEBVIEW_RETAIN_CONTEXT: true,
    MAX_CONCEPT_DISPLAY_LENGTH: 100,
    DOMAIN_CONCEPTS_REFRESH_INTERVAL: 24 * 60 * 60 * 1000 // 24 hours
} as const;

export const DESIGN_DOC_DIR_NAME = '.archknow/design_docs';
export const DESIGN_DOC_STATUS_POLL_INTERVAL = 10000; // 10 seconds
export const DESIGN_DOC_STATUS_POLL_TIMEOUT = 300000; // 5 minutes