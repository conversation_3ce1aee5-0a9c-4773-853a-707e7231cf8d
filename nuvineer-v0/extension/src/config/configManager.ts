import * as vscode from 'vscode';

export interface ArchKnowConfig {
    apiKey?: string;
    apiUrl: string;
}

export class ConfigManager {
    private static readonly DEFAULT_API_URL = 'https://archknow.vercel.app';

    static getConfig(): ArchKnowConfig {
        const config = vscode.workspace.getConfiguration('archknow');
        return {
            apiKey: config.get<string>('apiKey'),
            apiUrl: config.get<string>('apiUrl') || this.DEFAULT_API_URL
        };
    }

    static async saveApiKey(apiKey: string): Promise<void> {
        await vscode.workspace.getConfiguration('archknow')
            .update('apiKey', apiKey, vscode.ConfigurationTarget.Global);
    }

    static async promptAndSaveApiKey(): Promise<string | undefined> {
        const apiKey = await vscode.window.showInputBox({
            prompt: "Enter your ArchKnow API Key",
            placeHolder: "API Key",
            ignoreFocusOut: true,
            validateInput: text => {
                return text && text.trim().length > 0 ? null : "API Key cannot be empty";
            }
        });

        if (apiKey) {
            await this.saveApiKey(apiKey);
            vscode.window.showInformationMessage('ArchKnow API Key saved.');
            return apiKey;
        }
        return undefined;
    }
}