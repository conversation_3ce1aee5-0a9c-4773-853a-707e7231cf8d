import * as vscode from 'vscode';
import fetch from 'node-fetch';
import { API_ENDPOINTS } from '../config/constants';

export interface Decision {
    id: string;
    metadata: DecisionMetadata;
    score?: number;
    referenced_decisions?: any[];
}

export interface DecisionMetadata {
    title: string;
    description?: string | null;
    implications?: string | null;
    risks_extracted?: string | null;
    rationale?: string | null;
    pr_number?: number | null;
    pr_url?: string | null;
    domain_concepts?: string[] | null;
    pr_merged_at?: number | null;
    related_files?: string[] | null;
    dev_prompt?: string | null;
    confidence_score?: number | null;
    extracted_at?: string | null;
    follows_standard_practice?: boolean | null;
    follows_standard_practice_reason?: string | null;
    is_extension?: boolean | null;
    repository_slug?: string | null;
    alternatives_analysis?: string | null;
    relevance_reason?: string | null;
    data_model_changes?: string | null;
}

export interface Risk {
    severity: string;
    description: string;
    migration?: string;
}

export interface ApiResponse {
    decisions: Decision[];
}

export interface TechnicalDecision {
    decision: string;
    rationale: string;
    implications: string;
}

export interface SystemConstraint {
    constraint: string;
    enforcement: string;
    userCommunication: string;
}

export interface ErrorScenario {
    scenario: string;
    detection: string;
    userMessage: string;
    recovery: string;
}

export interface Component {
    name: string;
    responsibility: string;
}

export interface TechnicalDecisions {
    criticalChoices: TechnicalDecision[];
    systemConstraints: SystemConstraint[];
}

export interface ErrorHandling {
    scenarios: ErrorScenario[];
    retryStrategy: string;
    fallbackBehavior: string;
}

export interface CoreFlow {
    overview: string;
    components: Component[];
    dataModelChanges: string;
}

export interface InfrastructureAndOps {
    deployment_strategy: string;
    monitoring_approach: string;
    rollback_plan: string;
    security_considerations: string;
}

export interface DeveloperNotes {
    referencedDecisions: string[];
    implementationGuidance: string;
    coreFlow: CoreFlow;
    processFlow: string;
    infrastructure_and_ops: InfrastructureAndOps;
    developerNotes: DeveloperNotes;
}


export interface GeneratedDesignDocFromApi {
    [key: string]: any;
}
export interface Phase3_5_RefinedDataModels {
    refined_data_models: {
        [key: string]: any;
    };
}

export interface DesignDocArtifactsFromApi {
    phase3_output: GeneratedDesignDocFromApi;
    phase3_5_refined_data_models_output: Phase3_5_RefinedDataModels;
}

export interface Milestone {
    name: string;
    description: string;
    priority: 'High' | 'Medium' | 'Low';
    complexity: 'High' | 'Medium' | 'Low';
    owner?: string;
    dependencies?: string[];
    successIndicators?: string[];
}

export interface EvaluatedApproach {
    approachName: string;
    description: string;
    pros?: string[];
    cons?: string[];
    alignmentWithContext?: string;
    referencedDecisionIds?: string[];
}

export interface AlternativesAnalysis {
    evaluatedApproaches: EvaluatedApproach[];
    recommendation?: string;
    overall_solution_alternatives: string[];
    key_technical_decision_alternatives: string[];
    overall_recommendation_and_justification: string;
}

export interface ReferencedDecisionForDoc {
    id: string;
    title: string;
    summaryOfRelevance: string;
    implications?: string;
    rationale?: string;
    dev_prompt?: string;
    related_files?: string[];
}

export interface DesignDocJobSubmissionResponse {
    jobId: string;
    message: string;
}

// New interface for the job status response
export interface DesignDocJobStatusResponse {
    jobId: string;
    status: 'pending' | 'processing' | 'completed' | 'error';
    currentPhase?: string;
    phaseProgress?: string;
    progressPercentage?: number;
    taskTitle: string;
    message?: string;
    output_file_path?: string | null;
    phase1_output?: any | null;
    phase2_output?: any | null;
    phase3_output?: any | null;
    phase3_5_refined_data_models_output?: any | null;
    referenced_decisions?: any[] | null;
    created_at?: string;
    updated_at?: string;
}

// Define interfaces for BestPractice and DevGuidance if not already defined
// Assuming they match the structure in DomainConceptsService
export interface BestPractice {
    reason: string;
    relatedFiles: string[];
    decision_title?: string;
    // Add other relevant fields as returned by the API
}

export interface DevGuidance {
    guidance: string;
    relatedFiles: string[];
    decision_title?: string;
    // Add other relevant fields as returned by the API
}

export interface GitHubIssue {
    number: number;
    title: string;
    body: string;
    html_url: string;
    state: 'open' | 'closed';
    assignee?: {
        login: string;
    } | null;
    user: {
        login: string;
    };
}

export class ApiService {
    constructor(private apiUrl: string, private apiKey: string) {}

    public getApiUrl(): string {
        return this.apiUrl;
    }

    async validateApiKey(): Promise<boolean> {
        vscode.window.showInformationMessage(`Validating ArchKnow API Key against ${this.apiUrl}...`);
        const validationUrl = `${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.VALIDATE_KEY}`;
        
        try {
            const response = await fetch(validationUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `ApiKey ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
            });

            if (response.ok) {
                vscode.window.showInformationMessage('ArchKnow API Key is valid!');
                return true;
            } else {
                console.error(`ArchKnow: API Key validation failed for key starting with: ${this.apiKey.substring(0, 10)}...`);
                console.error(`ArchKnow: Validation URL: ${validationUrl}`);
                console.error(`ArchKnow: Status: ${response.status}`);
                
                let errorDetail = '';
                try {
                    const errorJson: unknown = await response.json();
                    if (typeof errorJson === 'object' && errorJson !== null && 'error' in errorJson && typeof errorJson.error === 'string') {
                        errorDetail = errorJson.error;
                    } else {
                        errorDetail = JSON.stringify(errorJson);
                    }
                } catch (e) {
                    errorDetail = await response.text();
                }
                vscode.window.showErrorMessage(`API Key Validation Failed: ${response.status} ${response.statusText}. Server response: ${errorDetail}`);
                return false;
            }
        } catch (error: any) {
            vscode.window.showErrorMessage(`Error validating API Key: ${error.message}`);
            console.error('API Key validation error:', error);
            return false;
        }
    }

    async fetchRelevantDecisions(repoSlug: string, filePath: string): Promise<Decision[]> {
        try {
            const response = await fetch(`${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.RELEVANT_TO_FILE}`, {
                method: 'POST',
                headers: {
                    'Authorization': `ApiKey ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ repository_slug: repoSlug, filePath: filePath })
            });

            if (!response.ok) {
                const errorText = await response.text();
                try {
                    const errorJson = JSON.parse(errorText);
                    if (errorJson && errorJson.error) {
                        throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorJson.error}`);
                    }
                } catch (parseError) {}
                throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json() as { success?: boolean, decisions?: Decision[], error?: string };
            console.log("ArchKnow: Raw API Response Data (Relevant):", JSON.stringify(data, null, 2));

            if (data.success && Array.isArray(data.decisions)) {
                return data.decisions;
            } else {
                const errorMessage = data.error || 'Invalid response structure from API.';
                throw new Error(`API Error: ${errorMessage}`);
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch relevant decisions: ${error.message}`);
            console.error('ArchKnow: Fetch relevant decisions error:', error);
            return [];
        }
    }

    async fetchAllDecisions(repoSlug: string, options?: { limit?: number, prUrl?: string }): Promise<Decision[]> {
        console.log(`ArchKnow: Fetching all decisions for repo: ${repoSlug}` + (options?.prUrl ? ` with PR filter: ${options.prUrl}` : ''));
        
        try {
            const body: { repository_slug: string, limit?: number, pr_url?: string } = {
                repository_slug: repoSlug
            };
            if (options?.limit) {
                body.limit = options.limit;
            }
            if (options?.prUrl) {
                body.pr_url = options.prUrl;
            }

            const response = await fetch(`${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.DECISIONS}`, {
                method: 'POST',
                headers: {
                    'Authorization': `ApiKey ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(body)
            });

            if (!response.ok) {
                const errorText = await response.text();
                try {
                    const errorJson = JSON.parse(errorText);
                    if (errorJson && errorJson.error) {
                        throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorJson.error}`);
                    }
                } catch (parseError) {}
                throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json() as { success?: boolean, decisions?: Decision[], error?: string };
            console.log("ArchKnow: Raw API Response Data (All Decisions):", JSON.stringify(data, null, 2));

            if (data.success && Array.isArray(data.decisions)) {
                return data.decisions;
            } else {
                const errorMessage = data.error || 'Invalid response structure from API.';
                throw new Error(`API Error: ${errorMessage}`);
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch decisions: ${error.message}`);
            console.error('ArchKnow: Fetch decisions error:', error);
            return [];
        }
    }

    async fetchDecisionsByConcepts(repoSlug: string, concepts: string[]): Promise<Decision[]> {
        console.log(`ArchKnow: Fetching decisions for concepts: ${concepts.join(', ')} in repo: ${repoSlug}`);
        
        try {
            const response = await fetch(`${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.BY_CONCEPTS}`, {
                method: 'POST',
                headers: {
                    'Authorization': `ApiKey ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ repository_slug: repoSlug, domain_concepts: concepts })
            });

            if (!response.ok) {
                const errorText = await response.text();
                try {
                    const errorJson = JSON.parse(errorText);
                    if (errorJson && errorJson.error) {
                        throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorJson.error}`);
                    }
                } catch (parseError) {}
                throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json() as { success?: boolean, decisions?: Decision[], error?: string };
            console.log("ArchKnow: Raw API Response Data (By Concepts):", JSON.stringify(data, null, 2));

            if (data.success && Array.isArray(data.decisions)) {
                return data.decisions;
            } else {
                const errorMessage = data.error || 'Invalid response structure from API.';
                throw new Error(`API Error: ${errorMessage}`);
            }
        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch decisions by concept: ${error.message}`);
            console.error('ArchKnow: Fetch decisions by concept error:', error);
            return [];
        }
    }

    async fetchDecisionsByPrompt(repoSlug: string, userPrompt: string): Promise<Decision[]> {
        console.log(`ArchKnow: Fetching decisions for prompt in repo: ${repoSlug}`);
        
        try {
            const response = await fetch(`${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.RELEVANT_TO_PROMPT}`, {
                method: 'POST',
                headers: {
                    'Authorization': `ApiKey ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ repository_slug: repoSlug, prompt: userPrompt })
            });

            if (!response.ok) {
                const errorText = await response.text();
                try {
                    const errorJson = JSON.parse(errorText);
                    if (errorJson && errorJson.error) {
                        throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorJson.error}`);
                    }
                } catch (parseError) {}
                throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json() as { success?: boolean, decisions?: Decision[], error?: string };
            console.log("ArchKnow: Raw API Response Data (By Prompt):", JSON.stringify(data, null, 2));

            if (data.success && Array.isArray(data.decisions)) {
                return data.decisions;
            } else {
                const errorMessage = data.error || 'Invalid response structure from API.';
                throw new Error(`API Error: ${errorMessage}`);
            }
        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch decisions by prompt: ${error.message}`);
            console.error('ArchKnow: Fetch decisions by prompt error:', error);
            return [];
        }
    }

    async fetchDecisionById(repoSlug: string, decisionId: string): Promise<Decision | null> {
        try {
            const url = `${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.SINGLE_DECISION}/${decisionId.trim()}`;
            console.log(`[ApiService] Fetching decision by ID. URL: ${url}, Repo: ${repoSlug}, DecisionID: ${decisionId}`);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `ApiKey ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ repository_slug: repoSlug })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`[ApiService] Error fetching decision ${decisionId} for ${repoSlug}. Status: ${response.status}. Body: ${errorText}`);
                vscode.window.showErrorMessage(`ArchKnow: Failed to fetch decision details (Status: ${response.status}). Check console for more info.`);
                return null;
            }

            const data = await response.json() as { success: boolean; decision?: Decision; error?: string };
            if (data.success && data.decision) {
                console.log(`[ApiService] Successfully fetched decision ID: ${data.decision.id}`);
                return data.decision;
            } else {
                console.error(`[ApiService] Failed to fetch decision ${decisionId} for ${repoSlug}. API Error: ${data.error || 'Unknown error from API'}`);
                vscode.window.showErrorMessage(`ArchKnow: Could not retrieve decision: ${data.error || 'Unknown error'}`);
                return null;
            }
        } catch (error: any) {
            console.error(`[ApiService] Exception in fetchDecisionById for ${decisionId} in ${repoSlug}:`, error);
            vscode.window.showErrorMessage(`ArchKnow: Error while fetching decision: ${error.message}`);
            return null;
        }
    }

    async fetchFeedback(repoSlug: string, codeChanges: Array<{ filename: string; patch: string }>, branchName?: string, jobId?: string, featurePrefix?: string): Promise<string | null> {
        if (!this.apiKey) {
            vscode.window.showErrorMessage("API key not set. Please configure it first.");
            return null;
        }

        const payload: any = {
            repository_slug: repoSlug,
            code_changes: codeChanges,
            branch_name: branchName,
        };

        if (jobId) {
            payload.job_id = jobId;
        }

        if (featurePrefix) {
            payload.feature_prefix = featurePrefix;
        }

        console.log('ArchKnow [fetchFeedback]: Sending payload:', JSON.stringify(payload, null, 2));

        try {
            const feedbackUrl = `${this.apiUrl.replace(/\/$/, '')}/api/extension/feedback`;
            console.log('ArchKnow [fetchFeedback]: Calling URL:', feedbackUrl);
            
            const response = await fetch(
                feedbackUrl,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `ApiKey ${this.apiKey}`,
                        'Content-Type': 'application/json',
                        'X-Client-Version': vscode.extensions.getExtension('ArchKnow.archknow-vscode')?.packageJSON.version || 'unknown'
                    },
                    body: JSON.stringify(payload)
                }
            );

            console.log('ArchKnow [fetchFeedback]: Response status:', response.status);
            console.log('ArchKnow [fetchFeedback]: Response headers:', Object.fromEntries(response.headers.entries()));

            // Get the raw response text first
            const responseText = await response.text();
            console.log('ArchKnow [fetchFeedback]: Raw response text (first 500 chars):', responseText.substring(0, 500));

            if (!response.ok) {
                console.error('ArchKnow [fetchFeedback]: HTTP error response:', response.status, response.statusText);
                vscode.window.showErrorMessage(`Failed to fetch feedback. Status: ${response.status}. Response: ${responseText.substring(0, 200)}`);
                return null;
            }

            // Try to parse the response as JSON
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            } catch (jsonError: any) {
                console.error('ArchKnow [fetchFeedback]: Failed to parse JSON response:', jsonError);
                console.error('ArchKnow [fetchFeedback]: Raw response was:', responseText);
                vscode.window.showErrorMessage(`Invalid JSON response from feedback API. Response: ${responseText.substring(0, 200)}`);
                return null;
            }

            console.log('ArchKnow [fetchFeedback]: Parsed response data:', JSON.stringify(responseData, null, 2));

            if (responseData && responseData.feedback) {
                return responseData.feedback;
            } else if (responseData && responseData.success === false) {
                const errorMessage = responseData.error || 'Unknown error from API';
                console.error('ArchKnow [fetchFeedback]: API returned error:', errorMessage);
                vscode.window.showErrorMessage(`Feedback API error: ${errorMessage}`);
                return null;
            } else {
                console.error('ArchKnow [fetchFeedback]: Unexpected response structure:', responseData);
                vscode.window.showErrorMessage(`Unexpected response structure from feedback API`);
                return null;
            }
        } catch (error: any) {
            console.error("ArchKnow [fetchFeedback]: Exception during fetch:", error);
            
            // Provide more specific error messages based on error type
            if (error.name === 'FetchError' && error.message.includes('invalid json')) {
                vscode.window.showErrorMessage(`API returned invalid JSON. This might indicate a server error or network issue.`);
            } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
                vscode.window.showErrorMessage(`Network error: Unable to reach the API. Check your internet connection.`);
            } else {
                vscode.window.showErrorMessage(`Error fetching feedback: ${error.message}`);
            }
            return null;
        }
    }

    async generateDesignDoc(taskTitle: string, taskDescription: string, initialApproachIdeas: string, repoSlug: string, githubHandle: string): Promise<DesignDocJobSubmissionResponse> {
        console.log('ArchKnow [ApiService.generateDesignDoc]: Submitting job with repoSlug:', repoSlug, 'and handle:', githubHandle);
        try {
            console.log('ArchKnow [GenerateDesignDoc]: Calling API at:', `${this.apiUrl}${API_ENDPOINTS.GENERATE_DESIGN_DOC}`);
            console.log('ArchKnow [GenerateDesignDoc]: Using GitHub handle:', githubHandle);
            
            const response = await fetch(`${this.apiUrl}${API_ENDPOINTS.GENERATE_DESIGN_DOC}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `ApiKey ${this.apiKey}`
                },
                body: JSON.stringify({
                    taskTitle,
                    taskDescription,
                    initialApproachIdeas,
                    repository_slug: repoSlug,
                    api_key: this.apiKey,
                    github_handle: githubHandle
                })
            });

            console.log('ArchKnow [GenerateDesignDoc]: API response status:', response.status);

            if (response.status === 202) {
                const jobResponse = await response.json();
                console.log('ArchKnow [GenerateDesignDoc]: Job submission successful:', jobResponse);
                return jobResponse as DesignDocJobSubmissionResponse;
            } else if (!response.ok) {
                let errorText = await response.text();
                console.log('ArchKnow [GenerateDesignDoc]: Error response:', errorText);
                try {
                    const errorJson = JSON.parse(errorText);
                    errorText = errorJson.error || errorJson.message || errorText;
                } catch (e) {
                    console.log('ArchKnow [GenerateDesignDoc]: Error parsing error response:', e);
                }
                throw new Error(`API returned error: ${response.status} - ${errorText}`);
            }

            // Should not reach here if status is 202 or not ok
            throw new Error('Unexpected API response state');
        } catch (error: any) {
            console.error('ArchKnow [ApiService.generateDesignDoc]: Error:', error);
            throw error;
        }
    }

    async getJobStatus(jobId: string): Promise<DesignDocJobStatusResponse | null> {
        console.log(`ArchKnow [JobStatus]: Checking status for job ID: ${jobId}`);
        const statusUrl = `${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.GENERATE_DESIGN_DOC_STATUS}/${jobId}`;

        try {
            const response = await fetch(statusUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `ApiKey ${this.apiKey}` 
                }
            });

            console.log(`ArchKnow [JobStatus]: API response status for ${jobId}: ${response.status}`);

            if (!response.ok) {
                let errorText = await response.text();
                try {
                    const errorJson = JSON.parse(errorText);
                    errorText = errorJson.error || errorJson.message || errorText;
                } catch (e) { /* Do nothing */ }
                console.error(`API returned error for job status ${jobId}: ${response.status} - ${errorText}`);
                // Optionally, display a vscode error message or let the caller handle it based on null return
                // vscode.window.showErrorMessage(`Failed to get job status for ${jobId}: ${errorText}`);
                return null; // Return null on error to match Promise<... | null>
            }

            const statusResponse: DesignDocJobStatusResponse = await response.json();
            
            // Ensure progress information is always available by calculating it client-side if missing
            if (!statusResponse.phaseProgress && !statusResponse.progressPercentage && statusResponse.currentPhase) {
                const calculatedProgress = this.calculateJobProgress(statusResponse.currentPhase, statusResponse.status);
                statusResponse.phaseProgress = calculatedProgress.phaseProgressMessage;
                statusResponse.progressPercentage = calculatedProgress.progressPercentage;
                statusResponse.message = statusResponse.message || calculatedProgress.phaseProgressMessage;
            }
            
            // Optional: Add more detailed logging of the raw response if needed for debugging
            // console.log(`ArchKnow [ApiService JobStatus Raw]: Raw JSON response for job ${jobId}:`, JSON.stringify(statusResponse, null, 2));
            
            console.log(`ArchKnow [JobStatus]: Status for ${jobId}:`, statusResponse.status);
            return statusResponse;

        } catch (error: any) {
            console.error(`ArchKnow [JobStatus]: Exception while fetching status for job ${jobId}:`, error);
            // Optionally, display a vscode error message
            // vscode.window.showErrorMessage(`Error fetching job status for ${jobId}: ${error.message}`);
            return null; // Return null on exception
        }
    }

    // Add client-side progress calculation as fallback
    private calculateJobProgress(currentPhase: string, jobStatus: string): { phaseProgressMessage: string, progressPercentage: number } {
        let phaseProgressMessage = 'Processing...';
        let progressPercentage = 0;

        if (jobStatus === 'error') {
            phaseProgressMessage = `Error during: ${currentPhase}. Check error message.`;
            if (currentPhase.startsWith('phase0_')) progressPercentage = 5;
            else if (currentPhase.startsWith('phase1_')) progressPercentage = 20;
            else if (currentPhase.startsWith('phase2_')) progressPercentage = 40;
            else if (currentPhase.startsWith('phase3_doc_generation') || currentPhase === 'phase3_pending') progressPercentage = 60;
            else if (currentPhase.startsWith('phase3_5_')) progressPercentage = 80;
            else progressPercentage = 2; 
            return { phaseProgressMessage, progressPercentage };
        }

        // Final completed state
        if (jobStatus === 'completed' && currentPhase === 'phase3_5_datamodel_refinement_complete') {
            progressPercentage = 100;
            phaseProgressMessage = 'Design document successfully generated.';
            return { phaseProgressMessage, progressPercentage };
        }
        if (jobStatus === 'completed' && currentPhase === 'completed') {
            progressPercentage = 100;
            phaseProgressMessage = 'Design document successfully generated (legacy completion state).';
            return { phaseProgressMessage, progressPercentage };
        }

        // Phase 0: Initialization and Broad Context
        if (currentPhase.startsWith('phase0_')) {
            if (currentPhase === 'phase0_pending') { 
                progressPercentage = 0; 
                phaseProgressMessage = 'Phase 0: Pending initialization...'; 
            }
            else if (currentPhase === 'phase0_extraction_context_inprogress') { 
                progressPercentage = 5; 
                phaseProgressMessage = 'Phase 0: Extracting concepts & context...'; 
            }
            else if (currentPhase === 'phase0_extraction_context_complete') {
                progressPercentage = 15;
                phaseProgressMessage = jobStatus === 'pending' ? 'Phase 0 Complete. Waiting for Phase 1.' : 'Phase 0 Complete.';
            }
        }
        // Phase 1: Goals & Decision Points
        else if (currentPhase.startsWith('phase1_')) {
            if (currentPhase === 'phase1_pending') { 
                progressPercentage = 15; 
                phaseProgressMessage = 'Phase 1: Pending goals & decisions...';
            }
            else if (currentPhase === 'phase1_goals_decisions_inprogress') { 
                progressPercentage = 20; 
                phaseProgressMessage = 'Phase 1: Identifying goals & decision points...'; 
            }
            else if (currentPhase === 'phase1_goals_decisions_complete') {
                progressPercentage = 35;
                phaseProgressMessage = jobStatus === 'pending' ? 'Phase 1 Complete. Waiting for Phase 2.' : 'Phase 1 Complete.';
            }
        }
        // Phase 2: Decision Point Analysis
        else if (currentPhase.startsWith('phase2_')) {
            if (currentPhase === 'phase2_pending') {
                progressPercentage = 35; 
                phaseProgressMessage = 'Phase 2: Pending decision analysis...';
            } 
            else if (currentPhase === 'phase2_decision_context_inprogress') { 
                progressPercentage = 40; 
                phaseProgressMessage = 'Phase 2: Analyzing technical decision points...'; 
            }
            else if (currentPhase === 'phase2_decision_context_complete') {
                progressPercentage = 55;
                phaseProgressMessage = jobStatus === 'pending' ? 'Phase 2 Complete. Waiting for Phase 3 (Initial Draft).' : 'Phase 2 Complete.';
            }
        }
        // Phase 3: Initial Full Document Generation
        else if (currentPhase.startsWith('phase3_doc_generation') || currentPhase === 'phase3_pending') {
            if (currentPhase === 'phase3_pending') {
                progressPercentage = 55; 
                phaseProgressMessage = 'Phase 3: Pending initial document draft...';
            } 
            else if (currentPhase === 'phase3_doc_generation_inprogress') { 
                progressPercentage = 60; 
                phaseProgressMessage = 'Phase 3: Generating initial design document draft...'; 
            }
            else if (currentPhase === 'phase3_doc_generation_complete') { 
                progressPercentage = 75; 
                phaseProgressMessage = jobStatus === 'pending' ? 'Phase 3 Draft Complete. Waiting for Phase 3.5 (Data Model Refinement).' : 'Phase 3 Draft Complete.';
            }
        }
        // Phase 3.5: Data Model Refinement
        else if (currentPhase.startsWith('phase3_5_')) {
            if (currentPhase === 'phase3_5_datamodel_refinement_pending') { 
                progressPercentage = 75; 
                phaseProgressMessage = 'Phase 3.5: Pending data model refinement...';
            }
            else if (currentPhase === 'phase3_5_datamodel_refinement_inprogress') { 
                progressPercentage = 85; 
                phaseProgressMessage = 'Phase 3.5: Refining data models for compliance...'; 
            }
            else if (currentPhase === 'phase3_5_datamodel_refinement_complete' && jobStatus === 'pending'){
                progressPercentage = 95;
                phaseProgressMessage = 'Phase 3.5 Complete. Finalizing job status...';
            }
        }
        // Fallback for unknown phases
        else {
            phaseProgressMessage = `Processing: ${currentPhase}`;
            progressPercentage = Math.max(progressPercentage, 2); 
        }

        return { phaseProgressMessage, progressPercentage };
    }

    async fetchDomainConcepts(repoSlug: string): Promise<Array<{ concept: string, count: number }>> {
        vscode.window.showInformationMessage(`ArchKnow: Fetching all domain concepts for ${repoSlug}...`);
        
        try {
            const fetchUrl = `${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.DOMAIN_CONCEPTS}`;
            console.log(`ArchKnow: Fetching domain concepts from: ${fetchUrl}`);

            const response = await fetch(fetchUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `ApiKey ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ repository_slug: repoSlug })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();
            console.log("ArchKnow: Raw API Response Data:", JSON.stringify(data, null, 2));

            if (!data.success || !Array.isArray(data.aggregated_concepts)) {
                throw new Error("Invalid response format from server");
            }

            console.log(`ArchKnow: Found ${data.aggregated_concepts.length} domain concepts.`);
            return data.aggregated_concepts;

        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch domain concepts: ${error.message}`);
            console.error('ArchKnow: Fetch Domain Concepts Error:', error);
            return [];
        }
    }

    async fetchBestPractices(repoSlug: string): Promise<BestPractice[]> {
        try {
            const response = await fetch(`${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.BEST_PRACTICES}`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `ApiKey ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ repository_slug: repoSlug })
                });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
            }
            // Adjust the expected response structure according to your API
            const data = await response.json() as { success?: boolean, best_practices?: BestPractice[], error?: string };

            if (data.success && Array.isArray(data.best_practices)) {
                return data.best_practices;
            } else {
                const errorMessage = data.error || 'Invalid response structure from API for best practices.';
                throw new Error(`API Error: ${errorMessage}`);
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch best practices: ${error.message}`);
            console.error('ArchKnow: Fetch best practices error:', error);
            return [];
        }
    }

    async fetchDevGuidance(repoSlug: string): Promise<DevGuidance[]> {
        try {
            const response = await fetch(`${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.DEV_GUIDANCE}`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `ApiKey ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ repository_slug: repoSlug })
                });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
            }
            // Adjust the expected response structure according to your API
            const data = await response.json() as { success?: boolean, dev_guidance?: DevGuidance[], error?: string };

            if (data.success && Array.isArray(data.dev_guidance)) {
                return data.dev_guidance;
            } else {
                const errorMessage = data.error || 'Invalid response structure from API for dev guidance.';
                throw new Error(`API Error: ${errorMessage}`);
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch dev guidance: ${error.message}`);
            console.error('ArchKnow: Fetch dev guidance error:', error);
            return [];
        }
    }

    async fetchDesignDocFeedback(repoSlug: string, designDocContent: string): Promise<{ feedback: string; referencedDecisions?: any[] } | null> {
        try {
            const response = await fetch(`${this.apiUrl}${API_ENDPOINTS.GENERATE_DESIGN_DOC}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': this.apiKey
                },
                body: JSON.stringify({
                    repository_slug: repoSlug,
                    design_doc_content: designDocContent,
                    pr_context: {
                        number: 0,
                        title: "Standalone Design Document Review",
                        body: "Reviewing a design document outside of a specific PR.",
                        html_url: "",
                        merged_at: new Date().toISOString(),
                        user: { login: "design-doc-author" }
                    },
                    dry_run: true
                })
            });

            const responseText = await response.text();
            console.log(`ArchKnow [DesignDoc]: Raw API Response Status: ${response.status}`);
            console.log(`ArchKnow [DesignDoc]: Raw API Response Text: ${responseText}`);

            if (!response.ok) {
                let errorDetail = responseText;
                try {
                    const errorJson = JSON.parse(responseText);
                    if (errorJson && errorJson.error) {
                        errorDetail = errorJson.error;
                    }
                } catch (e) {}
                throw new Error(`API Error: ${response.status} - ${errorDetail}`);
            }

            const data = JSON.parse(responseText) as {
                success?: boolean,
                feedback?: string,
                error?: string,
                referenced_decisions?: any[]
            };

            console.log("ArchKnow [DesignDoc]: Parsed API Response Data:", data);

            if (data.success && typeof data.feedback === 'string') {
                console.log("ArchKnow [DesignDoc]: Feedback successfully retrieved.");

                if (data.referenced_decisions && Array.isArray(data.referenced_decisions)) {
                    console.log(`ArchKnow [DesignDoc]: Found ${data.referenced_decisions.length} referenced decisions.`);
                }

                return {
                    feedback: data.feedback,
                    referencedDecisions: data.referenced_decisions
                };
            } else {
                const errorMessage = data.error || 'Invalid response structure from feedback API.';
                throw new Error(`API Error: ${errorMessage}`);
            }

        } catch (error: any) {
            const errorMessage = error.message || 'Unknown error';
            let displayMessage = `ArchKnow: Failed to fetch design document feedback: ${errorMessage}`;

            if (errorMessage.includes('API Error: 403') || errorMessage.includes('forbidden') || errorMessage.includes('Access denied')) {
                displayMessage = `ArchKnow: Access Denied (403). Failed to fetch design document feedback for ${repoSlug}. Please ensure the ArchKnow GitHub App has been granted access to this specific repository in your GitHub settings. Error: ${errorMessage}`;
            }

            vscode.window.showErrorMessage(displayMessage);
            console.error(`ArchKnow: Fetch design document feedback error for ${repoSlug}:`, error);
            return null;
        }
    }

    async getAllDesignDocJobs(githubHandle: string, repositorySlug: string): Promise<DesignDocJobStatusResponse[]> {
        console.log(`ArchKnow [ApiService]: Fetching all design doc jobs for GitHub handle: ${githubHandle}, Repository: ${repositorySlug}`);
        const jobsListUrl = `${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.GENERATE_DESIGN_DOC_STATUS}?github_handle=${encodeURIComponent(githubHandle)}&repository_slug=${encodeURIComponent(repositorySlug)}`;

        const response = await fetch(jobsListUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `ApiKey ${this.apiKey}`
            }
        });

        console.log('ArchKnow [ApiService]: Jobs list response status:', response.status);

        if (!response.ok) {
            let errorText = await response.text();
            try {
                const errorJson = JSON.parse(errorText);
                errorText = errorJson.error || errorJson.message || errorText;
            } catch (e) { /* Do nothing */ }
            throw new Error(`API returned error for jobs list: ${response.status} - ${errorText}`);
        }

        const jobs = await response.json();
        console.log(`ArchKnow [ApiService]: Fetched ${jobs.length} jobs for GitHub handle ${githubHandle}, Repository: ${repositorySlug}`);
        return jobs as DesignDocJobStatusResponse[];
    }

    public async summarizeComments(comments: any[]): Promise<string> {
        console.log('ArchKnow [ApiService.summarizeComments]: Requesting summary for comments:', comments.length);
        // Placeholder: In a real scenario, you would make an API call here.
        // e.g., const response = await this.post<{ summary: string }>(`/summarize-comments`, { comments });
        // return response.summary;

        if (comments.length === 0) {
            return "No comments provided to summarize.";
        }
        // Simulate an API call delay and provide a mock summary
        await new Promise(resolve => setTimeout(resolve, 1500));
        const mockSummary = `AI-generated summary: There are ${comments.length} comments. Key themes include X, Y, and Z. Action may be needed on A and B.`;
        console.log('ArchKnow [ApiService.summarizeComments]: Mock summary generated.');
        return mockSummary;
    }

    async fetchDesignDocContent(jobId: string): Promise<DesignDocArtifactsFromApi | null> {
        console.log(`ArchKnow [ApiService]: Fetching design doc content for job ID: ${jobId}`);
        const statusUrl = `${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.GENERATE_DESIGN_DOC_STATUS}/${jobId}`;

        const response = await fetch(statusUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `ApiKey ${this.apiKey}`
            }
        });

        if (!response.ok) {
            let errorText = await response.text();
            try {
                const errorJson = JSON.parse(errorText);
                errorText = errorJson.error || errorJson.message || errorText;
            } catch (e) { /* Do nothing */ }
            throw new Error(`API returned error for job content ${jobId}: ${response.status} - ${errorText}`);
        }

        const jobData = await response.json();
        console.log(`ArchKnow [ApiService]: Job ${jobId} response:`, response);
        console.log(`ArchKnow [ApiService]: Job ${jobId} data:`, jobData);
        console.log(`ArchKnow [ApiService]: Job ${jobId} status:`, jobData.status);
        console.log(`ArchKnow [ApiService]: Job ${jobId} phase3_output:`, jobData.phase3_output);
        console.log(`ArchKnow [ApiService]: Job ${jobId} phase3_5_refined_data_models_output:`, jobData.phase3_5_refined_data_models_output);
        if (jobData.status !== 'completed' || !jobData.phase3_output) {
            console.warn(`ArchKnow [ApiService]: Job ${jobId} is not completed or has no phase3_output`);
            return null;
        }

        return {
            phase3_output: jobData.phase3_output as GeneratedDesignDocFromApi,
            phase3_5_refined_data_models_output: jobData.phase3_5_refined_data_models_output as Phase3_5_RefinedDataModels
        };
    }

    async callGenerateImplementationPlanApi(
        jobId: string,
        designDocContent: string,
        aiAgentProtocol: string,
        title: string
    ): Promise<string> {
        const endpoint = `${this.apiUrl}/api/extension/generate-design-doc/${jobId}/implementation-plan`;
        console.log(`ArchKnow [ApiService.callGenerateImplementationPlanApi]: Calling endpoint: ${endpoint} for title: "${title}"`);
        console.log(`ArchKnow [ApiService.callGenerateImplementationPlanApi]: AI Agent Protocol length: ${aiAgentProtocol.length} characters`);

        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    finalized_design_doc_markdown: designDocContent,
                    ai_agent_protocol: aiAgentProtocol,
                    document_title: title
                })
            });

            if (!response.ok) {
                const errorBody = await response.text();
                console.error(`ArchKnow [ApiService.callGenerateImplementationPlanApi]: API error ${response.status}: ${errorBody}`);
                throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
            }

            const responseData = await response.json(); 
            
            if (!responseData || !responseData.implementationPlan) {
                console.error('ArchKnow [ApiService.callGenerateImplementationPlanApi]: API response did not contain an implementationPlan field or was empty.');
                throw new Error('Invalid API response format: implementationPlan field missing or empty.');
            }
            
            // Convert the implementation plan to string if it's an object
            let implementationPlanContent: string;
            if (typeof responseData.implementationPlan === 'string') {
                implementationPlanContent = responseData.implementationPlan;
            } else {
                // Convert JSON object to formatted string
                implementationPlanContent = JSON.stringify(responseData.implementationPlan, null, 2);
            }
            
            console.log(`ArchKnow [ApiService.callGenerateImplementationPlanApi]: Successfully received implementation plan from API.`);
            return implementationPlanContent;

        } catch (error: any) {
            console.error('ArchKnow [ApiService.callGenerateImplementationPlanApi]: Error calling implementation plan API:', error);
            // Rethrow the error so the caller (DesignDocWorkflowService) can handle it.
            if (error instanceof Error) {
                throw error;
            }
            throw new Error(`Failed to generate implementation plan via API: ${error.message || String(error)}`);
        }
    }

    async fetchMilestoneFeedback(
        repoSlug: string, 
        codeChanges: Array<{ filename: string; patch: string }>, 
        milestoneId: string,
        jobId?: string,
        existingContext?: string,
        implementationPlan?: string,
        designSessionId?: string,
        issueUrl?: string
    ): Promise<{
        scope_status: 'COMPLIANT' | 'MAJOR_DEVIATION';
        scope_assessment?: {
            deliverables_status?: Array<{
                deliverable: string;
                status: 'IMPLEMENTED' | 'PARTIAL' | 'MISSING' | 'NOT_APPLICABLE';
                evidence: string;
            }>;
            additional_scope?: string;
            scope_gap?: string;
        };
        critical_security_risks: Array<{
            risk: string;
            severity: 'CRITICAL';
            impact: string;
            fix: string;
        }>;
        critical_performance_risks: Array<{
            risk: string;
            severity: 'CRITICAL';
            impact: string;
            file: string;
            fix: string;
        }>;
        critical_ux_risks: Array<{
            risk: string;
            severity: 'CRITICAL';
            impact: string;
            fix: string;
        }>;
        scope_deviations: Array<{
            id: string;
            deviation: string;
            impact: string;
            action: string;
            status: 'NEEDS_ACTION' | 'VERIFIED';
        }>;
        integration_blocked: boolean;
        summary: string;
        pr_summary?: string;
        pr_ready_summary?: {
            title: string;
            summary: string;
            deliverables_completed: string[];
            scope_status: string;
        };
        task_context?: {
            task_title: string;
            task_description: string;
            design_id: string;
        };
        milestone_context?: {
            milestone_id: string;
            title: string;
            description: string;
            priority: string;
            planned_deliverables: string[];
            verification_criteria: string[];
        };
        metadata?: {
            total_lines_changed: number;
            files_modified: number;
            integration_ready: boolean;
        };
    } | null> {
        if (!this.apiKey) {
            vscode.window.showErrorMessage("API key not set. Please configure it first.");
            return null;
        }

        const payload: any = {
            repository_slug: repoSlug,
            code_changes: codeChanges,
            milestone_id: milestoneId,
        };

        if (jobId) {
            payload.job_id = jobId;
        }

        if (existingContext) {
            payload.existing_context = existingContext;
        }

        if (implementationPlan) {
            payload.implementation_plan = implementationPlan;
        }

        if (designSessionId) {
            payload.design_session_id = designSessionId;
        }

        if (issueUrl) {
            payload.issue_url = issueUrl;
        }

        console.log('ArchKnow [fetchMilestoneFeedback]: Sending payload:', JSON.stringify(payload, null, 2));

        try {
            const feedbackUrl = `${this.apiUrl.replace(/\/$/, '')}/api/feedback/milestone`;
            console.log('ArchKnow [fetchMilestoneFeedback]: Calling URL:', feedbackUrl);
            
            const response = await fetch(
                feedbackUrl,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json',
                        'X-Client-Version': vscode.extensions.getExtension('ArchKnow.archknow-vscode')?.packageJSON.version || 'unknown'
                    },
                    body: JSON.stringify(payload)
                }
            );

            console.log('ArchKnow [fetchMilestoneFeedback]: Response status:', response.status);

            // Get the raw response text first
            const responseText = await response.text();
            console.log('ArchKnow [fetchMilestoneFeedback]: Raw response text (first 500 chars):', responseText.substring(0, 500));

            if (!response.ok) {
                console.error('ArchKnow [fetchMilestoneFeedback]: HTTP error response:', response.status, response.statusText);
                vscode.window.showErrorMessage(`Failed to fetch milestone feedback. Status: ${response.status}. Response: ${responseText.substring(0, 200)}`);
                return null;
            }

            // Try to parse the response as JSON
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            } catch (jsonError: any) {
                console.error('ArchKnow [fetchMilestoneFeedback]: Failed to parse JSON response:', jsonError);
                console.error('ArchKnow [fetchMilestoneFeedback]: Raw response was:', responseText);
                vscode.window.showErrorMessage(`Invalid JSON response from milestone feedback API. Response: ${responseText.substring(0, 200)}`);
                return null;
            }

            console.log('ArchKnow [fetchMilestoneFeedback]: Parsed response data:', JSON.stringify(responseData, null, 2));

            // A simple validation to ensure we have the expected top-level fields
            if (responseData && responseData.scope_status && Array.isArray(responseData.critical_security_risks) && Array.isArray(responseData.critical_performance_risks) && Array.isArray(responseData.critical_ux_risks)) {
                console.log(`ArchKnow [fetchMilestoneFeedback]: Parsed response data:`, responseData);
                return responseData;
            } else {
                console.error('ArchKnow [fetchMilestoneFeedback]: Unexpected response structure:', responseData);
                vscode.window.showErrorMessage(`Unexpected response structure from milestone feedback API`);
                return null;
            }
        } catch (error: any) {
            console.error("ArchKnow [fetchMilestoneFeedback]: Exception during fetch:", error);
            
            // Provide more specific error messages based on error type
            if (error.name === 'FetchError' && error.message.includes('invalid json')) {
                vscode.window.showErrorMessage(`API returned invalid JSON. This might indicate a server error or network issue.`);
            } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
                vscode.window.showErrorMessage(`Network error: Unable to reach the API. Check your internet connection.`);
            } else {
                vscode.window.showErrorMessage(`Error fetching milestone feedback: ${error.message}`);
            }
            return null;
        }
    }

    async fetchGitHubIssues(repoSlug: string, githubHandle: string): Promise<GitHubIssue[]> {
        const url = `${this.apiUrl.replace(/\/$/, '')}${API_ENDPOINTS.FETCH_GITHUB_ISSUES}`;

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    repository_slug: repoSlug,
                    github_handle: githubHandle,
                    api_key: this.apiKey 
                })
            });

            if (!response.ok) {
                const errorBody = await response.text();
                console.error(`ArchKnow: Error fetching GitHub issues: ${response.status} ${response.statusText}`, errorBody);
                throw new Error(`Failed to fetch GitHub issues: ${response.status} ${response.statusText} - ${errorBody}`);
            }

            const data = await response.json();
            return data as GitHubIssue[];
        } catch (error: any) {
            console.error('ArchKnow: Error fetching GitHub issues:', error);
            throw new Error(`Failed to fetch GitHub issues: ${error.message}`);
        }
    }

    async fetchIssueDetailsForMcp(issueUrl: string): Promise<{ success: boolean; details?: string; error?: string }> {
        const url = `${this.apiUrl.replace(/\/$/, '')}/api/github/issue-details`;

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    // The backend route validates the api key passed in the body,
                    // so no Authorization header is needed here.
                },
                body: JSON.stringify({
                    issueUrl: issueUrl,
                    apiKey: this.apiKey
                })
            });

            if (!response.ok) {
                const errorBody = await response.text();
                console.error(`ArchKnow: Error fetching issue details for MCP: ${response.status} ${response.statusText}`, errorBody);
                try {
                    const errorJson = JSON.parse(errorBody);
                    return { success: false, error: errorJson.error || 'Failed to fetch issue details.' };
                } catch (e) {
                    return { success: false, error: errorBody };
                }
            }

            const data = await response.json();
            return data;
        } catch (error: any) {
            console.error('ArchKnow: Error in fetchIssueDetailsForMcp:', error);
            return { success: false, error: `Failed to fetch issue details: ${error.message}` };
        }
    }

    async fetchMilestoneReviewForMcp(
        repoSlug: string, 
        codeChanges: Array<{ filename: string; patch: string }>, 
        designDoc: string,
        implementationPlan: string,
        milestoneId?: string,
        milestoneTitle?: string,
        existingContext?: string,
        issueUrl?: string,
        reviewIteration?: number,
        previouslyAcceptedRisks?: string[],
        forceMinimalReview?: boolean,
        designSessionId?: string
    ): Promise<any> {
        const url = `${this.apiUrl.replace(/\/$/, '')}/api/feedback/milestone-mcp`;

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'X-Client-Version': vscode.extensions.getExtension('ArchKnow.archknow-vscode')?.packageJSON.version || 'unknown'
                },
                body: JSON.stringify({
                    repository_slug: repoSlug,
                    code_changes: codeChanges,
                    design_doc: designDoc,
                    implementation_plan: implementationPlan,
                    milestone_id: milestoneId,
                    milestone_title: milestoneTitle,
                    existing_context: existingContext,
                    issue_url: issueUrl,
                    review_iteration: reviewIteration,
                    previously_accepted_risks: previouslyAcceptedRisks,
                    force_minimal_review: forceMinimalReview,
                    design_session_id: designSessionId
                })
            });

            if (!response.ok) {
                const errorBody = await response.text();
                console.error(`ArchKnow: Error fetching milestone review for MCP: ${response.status} ${response.statusText}`, errorBody);
                try {
                    const errorJson = JSON.parse(errorBody);
                    throw new Error(errorJson.error || 'Failed to fetch milestone review.');
                } catch (e) {
                    throw new Error(errorBody);
                }
            }

            const data = await response.json();
            return data;
        } catch (error: any) {
            console.error('ArchKnow: Error in fetchMilestoneReviewForMcp:', error);
            throw new Error(`Failed to fetch milestone review: ${error.message}`);
        }
    }
}

// Interface defined at the top-level of the module, AFTER the class definition
interface DesignDocUploadResponse {
    filePath: string;
}