import * as vscode from 'vscode';
import { SimpleGit } from 'simple-git';
import { GITHUB_HANDLE_KEY } from '../config/constants';
import { isValidGitHubHandle } from '../utils';

export class UserService {
    constructor(private context: vscode.ExtensionContext) {}

    async getGitHubUserHandle(gitInstance?: SimpleGit): Promise<string | undefined> {
        let userHandle = this.context.workspaceState.get<string>(GITHUB_HANDLE_KEY);

        if (userHandle && isValidGitHubHandle(userHandle)) {
            console.log('ArchKnow: Found GitHub handle in workspace state:', userHandle);
            return userHandle;
        }

        if (gitInstance) {
            try {
                const gitUserName = await gitInstance.getConfig('user.name');
                if (gitUserName && gitUserName.value && isValidGitHubHandle(gitUserName.value)) {
                    console.log('ArchKnow: Inferred GitHub handle from git config user.name:', gitUserName.value);
                    return gitUserName.value;
                }
            } catch (error) {
                console.warn('ArchKnow: Could not infer GitHub handle from git config:', error);
            }
        }

        console.log('ArchKnow: GitHub handle not found in workspace state or git config.');
        return undefined;
    }

    async setGitHubUserHandle(handle: string): Promise<boolean> {
        if (!handle || !isValidGitHubHandle(handle)) {
            vscode.window.showErrorMessage('Invalid GitHub handle format.');
            return false;
        }

        await this.context.workspaceState.update(GITHUB_HANDLE_KEY, handle);
        console.log(`GitHub handle set to: ${handle}`);
        return true;
    }

    async promptForGitHubHandle(): Promise<string | undefined> {
        const newHandle = await vscode.window.showInputBox({
            prompt: 'Enter your GitHub handle to associate with design documents and feedback.',
            placeHolder: 'your-github-handle',
            validateInput: text => {
                return isValidGitHubHandle(text) ? null : 'Invalid GitHub handle format.';
            }
        });

        if (newHandle) {
            await this.setGitHubUserHandle(newHandle);
            vscode.window.showInformationMessage(`ArchKnow: GitHub handle set to @${newHandle}`);
            return newHandle;
        } else {
            vscode.window.showWarningMessage('ArchKnow: GitHub handle not set. Some features might be limited.');
            return undefined;
        }
    }
}