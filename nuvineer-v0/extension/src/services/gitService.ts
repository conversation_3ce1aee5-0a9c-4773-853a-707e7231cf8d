import * as vscode from 'vscode';
import simpleGit, { SimpleGit, SimpleGitOptions } from 'simple-git';
import { getRepoSlug } from '../utils';

export interface RepoInfo {
    slug: string;
    repoRoot: string;
}

export class GitService {
    private gitInstance: SimpleGit | null = null;
    private repoInfo: RepoInfo | null = null;

    async getWorkspaceRepoSlug(): Promise<RepoInfo | null> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            vscode.window.showErrorMessage('ArchKnow: No workspace folder open.');
            return null;
        }

        const workspacePath = workspaceFolders[0].uri.fsPath;
        const options: Partial<SimpleGitOptions> = {
            baseDir: workspacePath,
            binary: 'git',
            maxConcurrentProcesses: 6,
        };
        const git: SimpleGit = simpleGit(options);

        try {
            const repoRoot = await git.revparse(['--show-toplevel']);
            const remotes = await git.getRemotes(true);
            const origin = remotes.find(remote => remote.name === 'origin');

            if (!origin) {
                vscode.window.showWarningMessage('ArchKnow: Could not find git remote "origin" in the current workspace.');
                return null;
            }

            const slug = getRepoSlug(origin.refs.fetch || origin.refs.push);

            if (!slug) {
                vscode.window.showWarningMessage(`ArchKnow: Could not parse repository slug from origin URL: ${origin.refs.fetch || origin.refs.push}`);
                return null;
            }

            console.log(`ArchKnow: Found repository slug: ${slug}`);
            console.log(`ArchKnow: Found repository root: ${repoRoot}`);

            return { slug, repoRoot };

        } catch (error: any) {
            if (error.message.includes('not a git repository') || error.message.includes('fatal:')) {
                vscode.window.showWarningMessage(`ArchKnow: The current workspace folder (${workspacePath}) does not appear to be a git repository or is missing git.`);
            } else {
                console.error('ArchKnow: Error getting git info:', error);
                vscode.window.showWarningMessage(`ArchKnow: Failed to get git repository information. Error: ${error.message}`);
            }
            return null;
        }
    }

    async initializeGitRepoInfo(): Promise<void> {
        this.repoInfo = await this.getWorkspaceRepoSlug();
        if (this.repoInfo) {
            const options: Partial<SimpleGitOptions> = {
                baseDir: this.repoInfo.repoRoot,
                binary: 'git',
                maxConcurrentProcesses: 6,
            };
            this.gitInstance = simpleGit(options);
        } else {
            this.gitInstance = null;
        }
    }

    getRepoInfo(): RepoInfo | null {
        return this.repoInfo;
    }

    getGitInstance(): SimpleGit | null {
        return this.gitInstance;
    }

    async getStagedChanges(): Promise<Array<{ filename: string; patch: string }>> {
        if (!this.gitInstance) {
            throw new Error('Git instance not initialized');
        }

        const codeChanges: Array<{ filename: string; patch: string }> = [];
        
        try {
            const stagedDiffSummary = await this.gitInstance.diffSummary(['--cached']);
            const stagedFiles = stagedDiffSummary.files.map(f => f.file);

            if (stagedFiles.length === 0) {
                return [];
            }

            for (const filename of stagedFiles) {
                if (filename) {
                    const patch = await this.gitInstance.diff(['--cached', '--', filename]);
                    codeChanges.push({ filename, patch });
                }
            }

            return codeChanges;
        } catch (error: any) {
            console.error('ArchKnow: Error getting staged diffs:', error);
            throw new Error(`Error getting staged diffs: ${error.message}`);
        }
    }

    async getCurrentBranch(): Promise<string> {
        if (!this.gitInstance) {
            throw new Error('Git instance not initialized');
        }

        try {
            const branchName = (await this.gitInstance.revparse(['--abbrev-ref', 'HEAD'])).trim();
            if (!branchName) {
                throw new Error('Could not determine current branch');
            }
            return branchName;
        } catch (error: any) {
            console.error('ArchKnow: Error getting current branch:', error);
            throw new Error(`Error getting current branch: ${error.message}`);
        }
    }

    async getGitUserName(): Promise<string | undefined> {
        if (!this.gitInstance) {
            return undefined;
        }

        try {
            const gitUserName = await this.gitInstance.getConfig('user.name');
            return gitUserName?.value || undefined;
        } catch (error) {
            console.warn('ArchKnow: Could not get git user.name:', error);
            return undefined;
        }
    }
}