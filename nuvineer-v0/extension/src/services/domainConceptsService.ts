import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { parse } from 'csv-parse/sync';
import { ApiService } from './apiService';
import { UI_CONFIG } from '../config/constants';

export interface DomainConcept {
    concept: string;
    count: number;
}

export interface BestPractice {
    reason: string;
    relatedFiles: string[];
    decision_title?: string;
    // Add other relevant fields for best practices
}

export interface DevGuidance {
    guidance: string;
    relatedFiles: string[];
    decision_title?: string;
    // Add other relevant fields for dev guidance
}

// Interface for raw hierarchical data expected from the API
interface RawHierarchicalDomainConcept {
    path: string[]; // Max 4 levels, e.g., ["Category", "SubCategory", "Specific"]
    count: number;  // Count for this specific path
}

// Exportable interface for tree nodes used by the webview
export interface HierarchicalDomainConceptNode {
    name: string;
    count: number; // Total items at or under this node
    children: HierarchicalDomainConceptNode[];
}

// Helper function to trim specified words from the end of a concept segment (mirrors API logic)
const WORDS_TO_TRIM_EXTENSION = ["Architecture", "Pattern"];
function trimConceptSegmentExtension(segment: string): string {
  if (typeof segment !== 'string' || !segment.trim()) {
    return segment;
  }
  const words = segment.trim().split(/\s+/);
  const lastWord = words[words.length - 1];
  
  if (WORDS_TO_TRIM_EXTENSION.some(trimWord => lastWord.toLowerCase() === trimWord.toLowerCase())) {
    if (WORDS_TO_TRIM_EXTENSION.map(w => w.toLowerCase()).includes(lastWord.toLowerCase())) {
        words.pop();
        return words.join(' ').trim();
    }
  }
  return segment.trim();
}

export class DomainConceptsService {
    private csvPath: string;
    private bestPracticesCsvPath: string;
    private devGuidanceCsvPath: string;

    constructor(private workspacePath: string, private apiService: ApiService) {
        this.csvPath = path.join(workspacePath, '.archknow', 'domain-concepts.csv');
        this.bestPracticesCsvPath = path.join(workspacePath, '.archknow', 'best-practices.csv');
        this.devGuidanceCsvPath = path.join(workspacePath, '.archknow', 'dev-guidance.csv');
    }

    async getDomainConcepts(): Promise<DomainConcept[]> {
        try {
            if (!fs.existsSync(this.csvPath)) {
                console.log('ArchKnow: .archknow/domain-concepts.csv not found.');
                return [];
            }

            const fileContent = await fs.promises.readFile(this.csvPath, 'utf8');
            const records = parse(fileContent, {
                columns: true, // Expects level1,level2,level3,level4,count
                skip_empty_lines: true,
                trim: true,
                cast: (value, context) => {
                    if (context.column === 'count') {
                        const num = parseInt(value, 10);
                        return isNaN(num) ? 0 : num;
                    }
                    return value;
                },
            });

            const flatConceptsMap = new Map<string, number>();

            records.forEach((record: any) => {
                const pathSegmentsRaw: string[] = [];
                if (record.level1 && record.level1.trim() !== '') pathSegmentsRaw.push(record.level1.trim());
                if (record.level2 && record.level2.trim() !== '') pathSegmentsRaw.push(record.level2.trim());
                if (record.level3 && record.level3.trim() !== '') pathSegmentsRaw.push(record.level3.trim());
                if (record.level4 && record.level4.trim() !== '') pathSegmentsRaw.push(record.level4.trim());

                if (pathSegmentsRaw.length > 0) {
                    // Apply trimming to non-leaf segments for constructing the flat concept string
                    const trimmedPathSegments = pathSegmentsRaw.map((segment, index) => {
                        if (index < pathSegmentsRaw.length - 1) { // Non-leaf segment
                            return trimConceptSegmentExtension(segment);
                        }
                        return segment; // Leaf segment, no trim
                    }).filter(segment => segment && segment.trim() !== ''); // Remove empty segments
                    
                    if (trimmedPathSegments.length > 0) {
                        const conceptString = trimmedPathSegments.join(' > ');
                        const count = record.count as number;
                        flatConceptsMap.set(conceptString, (flatConceptsMap.get(conceptString) || 0) + count);
                    }
                }
            });
            
            const conceptsWithCounts = Array.from(flatConceptsMap.entries())
                .map(([concept, count]) => ({ concept, count }))
                .sort((a, b) => b.count - a.count);

            console.log(`ArchKnow: Found and sorted ${conceptsWithCounts.length} flat domain concepts from hierarchical CSV.`);
            return conceptsWithCounts;

        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Error reading or parsing domain concepts CSV for flat list: ${error.message}`);
            console.error('ArchKnow: Error processing domain concepts CSV for flat list:', error);
            return [];
        }
    }

    async refreshDomainConceptsIfNeeded(repoSlug: string): Promise<void> {
        try {
            let shouldRefresh = false;
            if (!fs.existsSync(this.csvPath)) {
                console.log('ArchKnow: domain-concepts.csv not found, will fetch it.');
                shouldRefresh = true;
            } else {
                const stats = fs.statSync(this.csvPath);
                const fileAge = Date.now() - stats.mtime.getTime();
                if (fileAge > UI_CONFIG.DOMAIN_CONCEPTS_REFRESH_INTERVAL) {
                    console.log('ArchKnow: domain-concepts.csv is older than a day, refreshing...');
                    shouldRefresh = true;
                } else {
                    console.log(`ArchKnow: domain-concepts.csv is up-to-date (${Math.round(fileAge / (60 * 60 * 1000))} hours old).`);
                }
            }

            if (shouldRefresh) {
                if (!repoSlug) {
                    console.warn('ArchKnow: repoSlug not provided to refreshDomainConceptsIfNeeded. Cannot refresh.');
                    vscode.window.showWarningMessage('ArchKnow: Cannot refresh domain concepts without repository information.');
                    return;
                }
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: "ArchKnow: Refreshing domain concepts...",
                    cancellable: false
                }, async (progress) => {
                    progress.report({ increment: 10 });
                    await this.fetchAndStoreDomainConceptsForRepo(repoSlug);
                    progress.report({ increment: 100 });
                });
            }
        } catch (error: any) {
            console.error('ArchKnow: Error checking/refreshing domain concepts:', error);
            vscode.window.showWarningMessage(`ArchKnow: Error refreshing domain concepts: ${error.message}`);
        }
    }

    async fetchAndStoreDomainConceptsForRepo(repoSlug: string): Promise<void> {
        try {
            // Ensure the .archknow directory exists
            const dirPath = path.dirname(this.csvPath);
            await fs.promises.mkdir(dirPath, { recursive: true });

            const concepts = await this.apiService.fetchDomainConcepts(repoSlug) as unknown as RawHierarchicalDomainConcept[];
            await this.storeDomainConcepts(concepts);
            vscode.window.showInformationMessage(`ArchKnow: Domain concepts saved to ${path.basename(this.csvPath)}`);
            console.log(`ArchKnow: Successfully wrote domain concepts CSV to ${this.csvPath}`);
        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch or store domain concepts: ${error.message}`);
            console.error('ArchKnow: Fetch/Store Domain Concepts Error:', error);
        }
    }

    async fetchAndStoreBestPracticesForRepo(repoSlug: string): Promise<void> {
        try {
            const bestPractices = await this.apiService.fetchBestPractices(repoSlug);
            await this.storeBestPractices(bestPractices);
            vscode.window.showInformationMessage(`ArchKnow: Best practices saved to ${path.basename(this.bestPracticesCsvPath)}`);
            console.log(`ArchKnow: Successfully wrote best practices CSV to ${this.bestPracticesCsvPath}`);
        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch or store best practices: ${error.message}`);
            console.error('ArchKnow: Fetch/Store Best Practices Error:', error);
        }
    }

    async fetchAndStoreDevGuidanceForRepo(repoSlug: string): Promise<void> {
        try {
            const devGuidance = await this.apiService.fetchDevGuidance(repoSlug);
            await this.storeDevGuidance(devGuidance);
            vscode.window.showInformationMessage(`ArchKnow: Developer guidance saved to ${path.basename(this.devGuidanceCsvPath)}`);
            console.log(`ArchKnow: Successfully wrote developer guidance CSV to ${this.devGuidanceCsvPath}`);
        } catch (error: any) {
            vscode.window.showErrorMessage(`ArchKnow: Failed to fetch or store developer guidance: ${error.message}`);
            console.error('ArchKnow: Fetch/Store Developer Guidance Error:', error);
        }
    }

    private async storeDomainConcepts(concepts: RawHierarchicalDomainConcept[]): Promise<void> {
        const formatCsvField = (field: string): string => {
            const strField = String(field);
            if (strField.includes('"') || strField.includes(',') || strField.includes('\n') || strField.includes('\r')) {
                return `"${strField.replace(/"/g, '""')}"`;
            }
            return strField;
        };

        let csvContent = 'level1,level2,level3,level4,count\n'; // New header
        if (concepts.length > 0) {
            console.log(`ArchKnow: Generating CSV for ${concepts.length} hierarchical concepts.`);
            concepts.forEach((item: RawHierarchicalDomainConcept) => {
                const path = item.path;
                // Ensure path is at most 4 levels, pad with empty strings if shorter
                const row = [
                    path[0] || '',
                    path[1] || '',
                    path[2] || '',
                    path[3] || '',
                    item.count.toString()
                ];
                csvContent += row.map(formatCsvField).join(',') + '\n';
            });
        } else {
            console.log("ArchKnow: No hierarchical domain concepts found to write to CSV.");
        }

        const dirPath = path.dirname(this.csvPath);
        await fs.promises.mkdir(dirPath, { recursive: true });
        await fs.promises.writeFile(this.csvPath, csvContent);
    }

    private async storeBestPractices(bestPractices: BestPractice[]): Promise<void> {
        const formatCsvField = (field: any): string => {
            const strField = Array.isArray(field) ? field.join(';') : String(field);
            if (strField.includes('"') || strField.includes(',') || strField.includes('\n') || strField.includes('\r')) {
                return `"${strField.replace(/"/g, '""')}"`;
            }
            return strField;
        };

        let csvContent = 'reason,relatedFiles,decision_title\n';
        if (bestPractices.length > 0) {
            console.log(`ArchKnow: Generating CSV for ${bestPractices.length} best practices.`);
            bestPractices.forEach((item: BestPractice) => {
                csvContent += `${formatCsvField(item.reason)},${formatCsvField(item.relatedFiles)},${formatCsvField(item.decision_title || '')}\n`;
            });
        } else {
            console.log("ArchKnow: No best practices found to write to CSV.");
        }

        const dirPath = path.dirname(this.bestPracticesCsvPath);
        await fs.promises.mkdir(dirPath, { recursive: true });
        await fs.promises.writeFile(this.bestPracticesCsvPath, csvContent);
    }

    private async storeDevGuidance(devGuidance: DevGuidance[]): Promise<void> {
        const formatCsvField = (field: any): string => {
            const strField = Array.isArray(field) ? field.join(';') : String(field);
            if (strField.includes('"') || strField.includes(',') || strField.includes('\n') || strField.includes('\r')) {
                return `"${strField.replace(/"/g, '""')}"`;
            }
            return strField;
        };

        let csvContent = 'guidance,relatedFiles,decision_title\n';
        if (devGuidance.length > 0) {
            console.log(`ArchKnow: Generating CSV for ${devGuidance.length} dev guidance items.`);
            devGuidance.forEach((item: DevGuidance) => {
                csvContent += `${formatCsvField(item.guidance)},${formatCsvField(item.relatedFiles)},${formatCsvField(item.decision_title || '')}\n`;
            });
        } else {
            console.log("ArchKnow: No dev guidance found to write to CSV.");
        }

        const dirPath = path.dirname(this.devGuidanceCsvPath);
        await fs.promises.mkdir(dirPath, { recursive: true });
        await fs.promises.writeFile(this.devGuidanceCsvPath, csvContent);
    }

    public async getTreeDataFromDomainConceptsCsv(): Promise<HierarchicalDomainConceptNode[]> {
        if (!fs.existsSync(this.csvPath)) {
            console.log('ArchKnow: .archknow/domain-concepts.csv not found for tree data.');
            return [];
        }

        const fileContent = await fs.promises.readFile(this.csvPath, 'utf8');
        const records = parse(fileContent, {
            columns: true, // Uses headers: level1, level2, level3, level4, count
            skip_empty_lines: true,
            trim: true,
            cast: (value, context) => {
                if (context.column === 'count') {
                    const num = parseInt(value, 10);
                    return isNaN(num) ? 0 : num;
                }
                return value;
            },
        });

        const roots: HierarchicalDomainConceptNode[] = [];
        const nodeMap = new Map<string, HierarchicalDomainConceptNode>(); // Stores nodes by their full path string "L1>L2>..."

        for (const record of records) {
            const pathSegmentsRaw: string[] = [];
            if (record.level1 && record.level1.trim()) pathSegmentsRaw.push(record.level1.trim());
            if (record.level2 && record.level2.trim()) pathSegmentsRaw.push(record.level2.trim());
            if (record.level3 && record.level3.trim()) pathSegmentsRaw.push(record.level3.trim());
            if (record.level4 && record.level4.trim()) pathSegmentsRaw.push(record.level4.trim());

            const recordCount = record.count as number;
            if (pathSegmentsRaw.length === 0 || recordCount === 0) continue;

            // Apply trimming to non-leaf segments for tree construction
            const pathSegments = pathSegmentsRaw.map((segment, index) => {
                if (index < pathSegmentsRaw.length - 1) { // Non-leaf segment
                    return trimConceptSegmentExtension(segment);
                }
                return segment; // Leaf segment, no trim
            }).filter(segment => segment && segment.trim() !== ''); // Remove empty segments

            if (pathSegments.length === 0) continue; // Skip if trimming resulted in an empty path

            let currentParentChildrenList = roots;
            let pathKeySoFar = "";

            for (let i = 0; i < pathSegments.length; i++) {
                const segment = pathSegments[i];
                const parentPathKey = pathKeySoFar;
                pathKeySoFar += (i > 0 ? ">" : "") + segment;

                let node = nodeMap.get(pathKeySoFar);
                if (!node) {
                    node = { name: segment, count: 0, children: [] };
                    nodeMap.set(pathKeySoFar, node);

                    if (i === 0) { // Root level node
                        // Check if already in roots (should be redundant if map is unique by pathKeySoFar)
                        const existingRoot = roots.find(n => n.name === segment);
                        if (existingRoot) {
                            node = existingRoot; // This branch implies pathKey wasn't unique enough or logic error
                        } else {
                             roots.push(node);
                        }
                    } else {
                        const parentNode = nodeMap.get(parentPathKey);
                        if (parentNode) {
                             // Check if already a child (should be redundant)
                            const existingChild = parentNode.children.find(n => n.name === segment);
                            if (existingChild) {
                                node = existingChild;
                            } else {
                                parentNode.children.push(node);
                            }
                        } else {
                             console.error(`ArchKnow: Parent node for path ${parentPathKey} not found when adding ${segment}.`);
                             // Fallback: add to roots (indicates data integrity or processing order issue)
                             const existingRoot = roots.find(n => n.name === segment);
                             if (existingRoot) node = existingRoot; else roots.push(node);
                        }
                    }
                }

                // If this segment is the end of the path for the current CSV record, add its count to this node.
                // This count is for paths that specifically end at this node.
                if (i === pathSegments.length - 1) {
                    node.count = (node.count || 0) + recordCount;
                }
                currentParentChildrenList = node.children; // Next segment will be a child of the current node
            }
        }
        
        // Recursive function to aggregate counts: a node's final count is its own specific count 
        // plus the sum of its children's final counts.
        const visitedForAggregation = new Set<HierarchicalDomainConceptNode>();
        function aggregateTreeCounts(node: HierarchicalDomainConceptNode): number {
            if (visitedForAggregation.has(node)) { // Avoid reprocessing if graph has shared instances (shouldn't with current map logic)
                 return node.count;
            }
            visitedForAggregation.add(node);

            let childrenTotalAggregatedCount = 0;
            for (const child of node.children) {
                childrenTotalAggregatedCount += aggregateTreeCounts(child);
            }
            // The node's final count is its own count (from CSV rows ending here) + total from children.
            node.count = (node.count || 0) + childrenTotalAggregatedCount;
            return node.count;
        }

        for (const root of roots) {
            aggregateTreeCounts(root);
        }
        
        // Sort children at each level alphabetically by name for consistent display
        function sortChildrenRecursive(node: HierarchicalDomainConceptNode) {
            node.children.sort((a, b) => a.name.localeCompare(b.name));
            node.children.forEach(sortChildrenRecursive);
        }
        roots.sort((a,b) => a.name.localeCompare(b.name));
        roots.forEach(sortChildrenRecursive);

        return roots;
    }
}