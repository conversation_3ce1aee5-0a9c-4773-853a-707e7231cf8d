import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import matter from 'gray-matter';
import { 
    DesignDocFrontmatter, 
    DesignDocMetadata, 
    convertFrontmatterToMetadata, 
    DesignDocStatus, 
    DesignDocFeedbackItem, 
    AiReviewComment,
    HighLevelDesign,
    CoreArchitecturalChoice,
    KeyComponent,
    GeneratedDesignDocFromApi,
    ReferencedDecisionForDoc,
    Milestone,
    EvaluatedApproach
} from '../types';
import { 
    DESIGN_DOCS_BASE_FOLDER, 
    DESIGN_DOCS_PENDING_FOLDER,
    DESIGN_DOCS_UNDER_REVIEW_FOLDER,
    DESIGN_DOCS_READY_FOR_IMPLEMENTATION_FOLDER,
    DESIGN_DOCS_IMPLEMENTED_FOLDER,
    DESIGN_DOCS_CHANGES_REQUESTED_FOLDER
} from '../config/constants';
import { escapeHtml, linkifyDecisionIds } from '../utils';
import { ApiService, Phase3_5_RefinedDataModels } from './apiService';
import { AI_AGENT_PROTOCOL_CONTENT } from '../aiAgentProtocol';

// This interface defines the structure of the first parameter for createAndOpenDesignDocFromJob
interface CreateDocFromJobParams {
    output_file_path: string;
    phase3_output: GeneratedDesignDocFromApi; // This object itself should not contain a 'referenced_decisions' key if passed separately.
    taskTitle: string;
    jobId: string;
    phase3_5_refined_data_models_output?: Phase3_5_RefinedDataModels;
}

export class DesignDocWorkflowService {
    private activeMoves = new Set<string>();

    constructor(
        private workspaceRoot: vscode.Uri,
        private apiService?: ApiService
    ) {}

    private parseAiComments(content: string): AiReviewComment[] {
        const comments: AiReviewComment[] = [];
        const commentRegex = new RegExp(
            `<!-- ARCHKNOW_COMMENT_START([\\s\\S]*?)ARCHKNOW_COMMENT_END -->`,
            'g'
        );
        let match;
        while ((match = commentRegex.exec(content)) !== null) {
            const commentContent = match[1].trim();
            const lines = commentContent.split('\n');
            const comment: Partial<AiReviewComment> = {};

            lines.forEach(line => {
                const [key, ...valueParts] = line.split(': ');
                const value = valueParts.join(': ').trim();
                if (!key || !value) return;

                switch (key.trim().toUpperCase()) {
                    case 'ID': comment.id = value; break;
                    case 'TYPE': comment.type = value as 'blocking' | 'non-blocking' | 'nit'; break;
                    case 'STATUS': comment.status = value as 'open' | 'addressed' | 'wont-fix' | 'pending-ai-addressal'; break;
                    case 'AUTHOR': comment.author = value; break;
                    case 'TIMESTAMP': comment.timestamp = value; break;
                    case 'TEXT': comment.text = value.replace(/\\n/g, '\n'); break;
                    case 'SUGGESTED_SECTION': comment.suggestedSection = value.replace(/\\n/g, '\n'); break;
                }
            });

            if (comment.id && comment.type && comment.status && comment.author && comment.text) {
                comments.push(comment as AiReviewComment);
            }
        }
        return comments;
    }

    async readDesignDocMetadata(docUri: vscode.Uri): Promise<{ metadata: DesignDocFrontmatter | null; content: string }> {
        console.log('ArchKnow [DesignDocWorkflowService.readDesignDocMetadata]: Reading document:', docUri.toString());
        
        try {
            const fileData = await vscode.workspace.fs.readFile(docUri);
            const rawContent = Buffer.from(fileData).toString('utf8');
            
            console.log('ArchKnow [DesignDocWorkflowService.readDesignDocMetadata]: Raw content length:', rawContent.length);
            console.log('ArchKnow [DesignDocWorkflowService.readDesignDocMetadata]: First 100 chars:', rawContent.substring(0, 100));
            
            // Parse frontmatter and content using gray-matter
            const parsed = matter(rawContent);
            
            console.log('ArchKnow [DesignDocWorkflowService.readDesignDocMetadata]: Parsed frontmatter:', JSON.stringify(parsed.data, null, 2));
            console.log('ArchKnow [DesignDocWorkflowService.readDesignDocMetadata]: Content length after parsing:', parsed.content.length);
            console.log('ArchKnow [DesignDocWorkflowService.readDesignDocMetadata]: First 100 chars of content:', parsed.content.substring(0, 100));
            
            return {
                metadata: parsed.data as DesignDocFrontmatter,
                content: parsed.content
            };
        } catch (error) {
            console.error('ArchKnow [DesignDocWorkflowService.readDesignDocMetadata]: Error reading document:', error);
            throw error;
        }
    }
    
    // ADDED: Helper methods for robust validation and type conversion
    private validateStatus(value: any): DesignDocFrontmatter['status'] | undefined {
        const validStatuses = ['pending', 'under_review', 'ready_for_implementation', 'implemented', 'changes-requested'];
        if (typeof value === 'string' && validStatuses.includes(value as any)) {
            return value as DesignDocFrontmatter['status'];
        }
        return undefined;
    }
    
    private validateString(value: any): string | undefined {
        if (typeof value === 'string' && value.trim() !== '') {
            return value.trim();
        }
        return undefined;
    }
    
    private validateStringArray(value: any): string[] | undefined {
        if (Array.isArray(value)) {
            const filtered = value.filter(item => typeof item === 'string' && item.trim() !== '');
            return filtered.length > 0 ? filtered : undefined;
        }
        return undefined;
    }
    
    private validateBoolean(value: any): boolean | undefined {
        if (typeof value === 'boolean') {
            return value;
        }
        return undefined;
    }
    
    private validateFeedbackArray(value: any): any[] | undefined {
        if (Array.isArray(value) && value.length > 0) {
            return value;
        }
        return undefined;
    }
    
    // IMPROVED: Simple YAML parser with better error handling
    private parseSimpleYaml(yamlContent: string): any {
        const result: any = {};
        const lines = yamlContent.split('\n');
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine || trimmedLine.startsWith('#')) continue;
            
            const colonIndex = trimmedLine.indexOf(':');
            if (colonIndex === -1) continue;
            
            const key = trimmedLine.substring(0, colonIndex).trim();
            let value = trimmedLine.substring(colonIndex + 1).trim();
            
            // Handle different value types
            if (value === '' || value === 'null') {
                result[key] = undefined;
            } else if (value === '[]') {
                result[key] = [];
            } else if (value.startsWith('[') && value.endsWith(']')) {
                // Simple array parsing
                const arrayContent = value.slice(1, -1).trim();
                if (arrayContent) {
                    result[key] = arrayContent.split(',').map(item => {
                        const trimmed = item.trim();
                        // Remove quotes if present
                        if ((trimmed.startsWith('"') && trimmed.endsWith('"')) || 
                            (trimmed.startsWith("'") && trimmed.endsWith("'"))) {
                            return trimmed.slice(1, -1);
                        }
                        return trimmed;
                    }).filter(item => item !== '');
                } else {
                    result[key] = [];
                }
            } else if (value === 'true') {
                result[key] = true;
            } else if (value === 'false') {
                result[key] = false;
            } else {
                // Remove quotes if present
                if ((value.startsWith('"') && value.endsWith('"')) || 
                    (value.startsWith("'") && value.endsWith("'"))) {
                    result[key] = value.slice(1, -1);
                } else {
                    result[key] = value;
                }
            }
        }
        
        return result;
    }

    async writeDesignDocMetadata(docUri: vscode.Uri, metadata: DesignDocFrontmatter, mainContent: string): Promise<void> {
        try {
            let contentToUse = mainContent;
            if (!contentToUse || contentToUse.trim() === '') {
                const currentData = await this.readDesignDocMetadata(docUri);
                contentToUse = currentData.content || '';
                console.log(`Preserving existing content for ${docUri.fsPath}`);
            }
            
            // Create a clean metadata object with proper serialization
            const cleanMetadata: any = {};
            
            // Always include status
            cleanMetadata.status = metadata.status || 'pending';
            
            // Include title if provided
            if (metadata.title) {
                cleanMetadata.title = metadata.title;
            }
            
            // Include author if provided
            if (metadata.author) {
                cleanMetadata.author = metadata.author;
            }
            
            // Include approver if provided - IMPORTANT for feedback workflow
            if (metadata.approver) {
                cleanMetadata.approver = metadata.approver;
            }
            
            // Include reviewers if provided - IMPORTANT for feedback workflow
            if (metadata.reviewers) {
                if (Array.isArray(metadata.reviewers)) {
                    // Only include non-empty reviewers
                    const validReviewers = metadata.reviewers.filter(r => r && typeof r === 'string' && r.trim() !== '');
                    if (validReviewers.length > 0) {
                        cleanMetadata.reviewers = validReviewers;
                    } else {
                        cleanMetadata.reviewers = [];
                    }
                } else {
                    cleanMetadata.reviewers = [];
                }
            } else {
                cleanMetadata.reviewers = [];
            }
            
            // Include timestamps if provided
            if (metadata.feedback_requested_at) {
                cleanMetadata.feedback_requested_at = metadata.feedback_requested_at;
            }
            
            if (metadata.approved_at) {
                cleanMetadata.approved_at = metadata.approved_at;
            }
            
            if (metadata.implemented_at) {
                cleanMetadata.implemented_at = metadata.implemented_at;
            }
            
            if (metadata.lastUpdated) {
                cleanMetadata.lastUpdated = metadata.lastUpdated;
            }
            
            // Include feedback if provided
            if (metadata.feedback && Array.isArray(metadata.feedback) && metadata.feedback.length > 0) {
                cleanMetadata.feedback = metadata.feedback;
            }
            
            // Include implementation plan fields if provided
            if (metadata.implementation_plan_generated !== undefined) {
                cleanMetadata.implementation_plan_generated = metadata.implementation_plan_generated;
            }
            
            if (metadata.implementation_plan_uri) {
                cleanMetadata.implementation_plan_uri = metadata.implementation_plan_uri;
            }
            
            // Preserve AI comments
            if (metadata.aiComments && Array.isArray(metadata.aiComments) && metadata.aiComments.length > 0) {
                cleanMetadata.aiComments = metadata.aiComments;
            }
            
            // Include jobId if provided - IMPORTANT for tracking document generation
            if (metadata.jobId) {
                cleanMetadata.jobId = metadata.jobId;
            }
            
            console.log(`ArchKnow [WriteMetadata]: Writing metadata for ${docUri.fsPath}:`, JSON.stringify(cleanMetadata, null, 2));
            console.log(`ArchKnow [WriteMetadata]: Content length: ${contentToUse.length}`);
            
            // Use gray-matter to create the file content with frontmatter
            const newFileContent = matter.stringify(contentToUse, cleanMetadata);
            
            console.log(`ArchKnow [WriteMetadata]: Generated file content (first 300 chars):\n${newFileContent.substring(0, 300)}...`);
            
            await vscode.workspace.fs.writeFile(docUri, Buffer.from(newFileContent, 'utf8'));
            
            console.log(`ArchKnow [WriteMetadata]: Successfully wrote file ${docUri.fsPath}`);
            
        } catch (error) {
            console.error(`Error writing design document ${docUri.fsPath}:`, error);
            vscode.window.showErrorMessage(`Failed to save design document: ${path.basename(docUri.fsPath)}`);
        }
    }
    
    // Also add a verification method to check if metadata was written correctly
    async verifyMetadataWrite(docUri: vscode.Uri, expectedMetadata: DesignDocFrontmatter): Promise<boolean> {
        try {
            const { metadata: readMetadata } = await this.readDesignDocMetadata(docUri);
            
            if (!readMetadata) {
                console.error(`ArchKnow [VerifyMetadata]: No metadata found after write for ${docUri.fsPath}`);
                return false;
            }
            
            // Check key fields
            const checks = [
                { field: 'status', expected: expectedMetadata.status, actual: readMetadata.status },
                { field: 'approver', expected: expectedMetadata.approver, actual: readMetadata.approver },
                { field: 'reviewers', expected: expectedMetadata.reviewers, actual: readMetadata.reviewers }
            ];
            
            for (const check of checks) {
                if (check.expected !== undefined) {
                    if (Array.isArray(check.expected)) {
                        if (!Array.isArray(check.actual) || JSON.stringify(check.expected) !== JSON.stringify(check.actual)) {
                            console.error(`ArchKnow [VerifyMetadata]: Mismatch in ${check.field}. Expected: ${JSON.stringify(check.expected)}, Actual: ${JSON.stringify(check.actual)}`);
                            return false;
                        }
                    } else if (check.expected !== check.actual) {
                        console.error(`ArchKnow [VerifyMetadata]: Mismatch in ${check.field}. Expected: ${check.expected}, Actual: ${check.actual}`);
                        return false;
                    }
                }
            }
            
            console.log(`ArchKnow [VerifyMetadata]: Metadata verification successful for ${docUri.fsPath}`);
            return true;
            
        } catch (error) {
            console.error(`ArchKnow [VerifyMetadata]: Error verifying metadata for ${docUri.fsPath}:`, error);
            return false;
        }
    }

    async moveDesignDoc(oldUri: vscode.Uri, newStatus: DesignDocFrontmatter['status']): Promise<vscode.Uri | null> {
        const oldUriString = oldUri.toString();
        if (this.activeMoves.has(oldUriString)) {
            vscode.window.showWarningMessage(`Move operation already in progress for ${path.basename(oldUri.fsPath)}. Please wait.`);
            return null;
        }
        this.activeMoves.add(oldUriString);

        try {
            let targetFolder: string;
            switch (newStatus) {
                case 'pending':
                    targetFolder = DESIGN_DOCS_PENDING_FOLDER;
                    break;
                case 'under_review':
                    targetFolder = DESIGN_DOCS_UNDER_REVIEW_FOLDER;
                    break;
                case 'ready_for_implementation':
                    targetFolder = DESIGN_DOCS_READY_FOR_IMPLEMENTATION_FOLDER;
                    break;
                case 'implemented':
                    targetFolder = DESIGN_DOCS_IMPLEMENTED_FOLDER;
                    break;
                case 'changes-requested':
                    targetFolder = DESIGN_DOCS_CHANGES_REQUESTED_FOLDER;
                    break;
                default:
                    vscode.window.showErrorMessage(`Unknown status for move: ${newStatus}`);
                    return null;
            }

            const oldPath = oldUri.fsPath;
            const filename = path.basename(oldPath);
            const newPath = path.join(this.workspaceRoot.fsPath, targetFolder, filename);
            const newUri = vscode.Uri.file(newPath);

            const targetDirUri = vscode.Uri.file(path.dirname(newPath));
            try {
                await vscode.workspace.fs.createDirectory(targetDirUri);
            } catch (dirError: any) {
                if (dirError.code !== 'EEXIST' && dirError.code !== 'FileExists') {
                    console.error(`Error creating directory: ${targetDirUri.fsPath}`, dirError);
                    vscode.window.showErrorMessage(`Failed to create directory for design document: ${path.dirname(newPath)}`);
                    return null;
                } else if (dirError.code === 'FileExists') {
                    try {
                        const stat = await vscode.workspace.fs.stat(targetDirUri);
                        if (stat.type !== vscode.FileType.Directory) {
                            console.error(`Target directory path exists but is not a directory: ${targetDirUri.fsPath}`, dirError);
                            vscode.window.showErrorMessage(`Cannot create directory, a file exists at path: ${path.dirname(newPath)}`);
                            return null;
                        }
                    } catch (statError) {
                        console.error(`Error stating target directory after FileExists error: ${targetDirUri.fsPath}`, statError);
                        vscode.window.showErrorMessage(`Failed to verify target directory: ${path.dirname(newPath)}`);
                        return null;
                    }
                }
            }

            try {
                await vscode.workspace.fs.rename(oldUri, newUri, { overwrite: true });
                console.log(`Moved design document from ${oldUri.fsPath} to ${newUri.fsPath}`);
                return newUri;
            } catch (moveError) {
                console.error(`Error moving file: ${oldUri.fsPath} -> ${newUri.fsPath}`, moveError);
                vscode.window.showErrorMessage(`Failed to move design document: ${filename}. Error: ${(moveError as Error).message}`);
                return null;
            }
        } catch (error) {
            console.error('Unexpected error in moveDesignDoc:', error);
            vscode.window.showErrorMessage('An unexpected error occurred while moving the design document.');
            return null;
        } finally {
            this.activeMoves.delete(oldUriString);
        }
    }

    async createDesignDocFile(designDoc: GeneratedDesignDocFromApi, userHandle?: string, jobId?: string): Promise<void> {
        console.log('ArchKnow [CreateDesignDocFile]: Starting file creation');
        
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            console.log('ArchKnow [CreateDesignDocFile]: No workspace folder open');
            vscode.window.showErrorMessage('No workspace folder open.');
            return;
        }
        
        console.log('ArchKnow [CreateDesignDocFile]: Using workspace folder:', workspaceFolders[0].uri.fsPath);
        
        const markdown = this.convertDesignDocToMarkdown(designDoc);
        console.log('ArchKnow [CreateDesignDocFile]: Converted design doc to markdown, length:', markdown.length);
        
        try {
            const designDocDirPath = path.join(workspaceFolders[0].uri.fsPath, DESIGN_DOCS_BASE_FOLDER, 'pending');
            console.log('ArchKnow [CreateDesignDocFile]: Creating directory at:', designDocDirPath);
            
            await fs.promises.mkdir(designDocDirPath, { recursive: true });
            console.log('ArchKnow [CreateDesignDocFile]: Directory created/confirmed');
            
            const fileName = `${designDoc?.title?.toLowerCase().replace(/[^a-z0-9]+/g, '-')}.md`;
                
            const filePath = path.join(designDocDirPath, fileName);
            console.log('ArchKnow [CreateDesignDocFile]: Will write to file:', filePath);
            
            await fs.promises.writeFile(filePath, markdown);
            console.log('ArchKnow [CreateDesignDocFile]: File written successfully');
            
            const docUri = vscode.Uri.file(filePath);
            
            if (userHandle || jobId) {
                const { metadata, content } = await this.readDesignDocMetadata(docUri);
                
                if (metadata) {
                    if (userHandle) {
                        metadata.author = userHandle;
                    }
                    if (jobId) {
                        metadata.jobId = jobId;
                        console.log('ArchKnow [CreateDesignDocFile]: Adding jobId to metadata:', jobId);
                    }
                    await this.writeDesignDocMetadata(docUri, metadata, content);
                    console.log('ArchKnow [CreateDesignDocFile]: Updated file with metadata information');
                }
            }
            
            console.log('ArchKnow [CreateDesignDocFile]: Opening document in design doc webview');
            vscode.commands.executeCommand('archknow.openDesignDoc', docUri);
            console.log('ArchKnow [CreateDesignDocFile]: Document opened in design doc webview');
            
            vscode.window.showInformationMessage(`Design document "${path.basename(filePath)}" created and opened in editor.`);
            
        } catch (error: any) {
            console.error('ArchKnow [CreateDesignDocFile]: Error:', error);
            console.error('ArchKnow [CreateDesignDocFile]: Error stack:', error.stack);
            vscode.window.showErrorMessage(`Failed to create design document file: ${error.message}`);
            console.error('Error creating design doc file:', error);
        }
    }

    private convertDesignDocToMarkdown(
        designDocData: GeneratedDesignDocFromApi, 
        refinedDataModelsOutput?: Phase3_5_RefinedDataModels,
        explicitReferencedDecisions?: ReferencedDecisionForDoc[]
    ): string {
        console.log('ArchKnow [convertDesignDocToMarkdown]: Starting conversion with designDocData:', JSON.stringify(designDocData, null, 2));
        if (refinedDataModelsOutput) {
            console.log('ArchKnow [convertDesignDocToMarkdown]: Received refinedDataModelsOutput:', JSON.stringify(refinedDataModelsOutput, null, 2));
        }
        if (explicitReferencedDecisions) {
            console.log('ArchKnow [convertDesignDocToMarkdown]: Received explicitReferencedDecisions, count:', explicitReferencedDecisions.length);
        }

        const refsToUse = explicitReferencedDecisions || (designDocData.referenced_decisions as ReferencedDecisionForDoc[]) || [];
        // If designDocData.referenced_decisions was used, remove it from designDocData to prevent double processing
        // However, designDocData is an input param, better to create a copy if modification is needed or ensure processNestedObject ignores it.
        // For now, processNestedObject will be told which refs to use.

        const slugify = (text: string): string => {
            if (!text) return '';
            return text.toString().toLowerCase()
                .replace(/\\s+/g, '-')           // Replace spaces with -
                .replace(/[^\\w\\-]+/g, '')       // Remove all non-word chars (alphanumeric, underscore, hyphen)
                .replace(/\\-\\-+/g, '-')         // Replace multiple - with single -
                .replace(/^-+/, '')             // Trim - from start of text
                .replace(/-+$/, '');            // Trim - from end of text
        };
        
        let markdown = ``;

        // Handle top-level sections explicitly
        markdown += `# ${linkifyDecisionIds(designDocData.title || 'Untitled Design Document', refsToUse)}\n\n`;

        // Goals
        markdown += `## Goals\n\n`;
        if (designDocData.goals && designDocData.goals.length > 0) {
            designDocData.goals.forEach(goal => {
                markdown += `- ${linkifyDecisionIds(goal, refsToUse)}\n`;
            });
        } else {
            console.warn('ArchKnow [convertDesignDocToMarkdown]: No goals defined');
            markdown += `*No goals defined.*\n`;
        }
        markdown += '\n';

        // Non-Goals
        markdown += `## Non-Goals\n\n`;
        if (designDocData.non_goals && designDocData.non_goals.length > 0) {
            designDocData.non_goals.forEach(nonGoal => {
                markdown += `- ${linkifyDecisionIds(nonGoal, refsToUse)}\n`;
            });
        } else {
            console.warn('ArchKnow [convertDesignDocToMarkdown]: No non-goals defined');
            markdown += `*No non-goals defined.*\n`;
        }
        markdown += '\n';

        // Extract infrastructure_and_ops to be processed last
        const infraOpsData = (designDocData as any).infrastructure_and_ops;

        // Process the rest of the document structure
        const remainingSections: any = { ...designDocData };
        delete remainingSections.title;
        delete remainingSections.goals;
        delete remainingSections.non_goals;
        delete remainingSections.infrastructure_and_ops; // Remove so it's not processed here
        delete remainingSections.referenced_decisions; // Explicitly delete so it's not processed by processNestedObject
        delete remainingSections.high_level_design.data_model_changes;
        // If refinedDataModelsOutput was used for data_model_changes, ensure we don't process it again from remainingSections
        
        markdown += this.processNestedObject(remainingSections, 2, refsToUse, designDocData, slugify);

        if (refinedDataModelsOutput && refinedDataModelsOutput.refined_data_models) {
            if (remainingSections.high_level_design && remainingSections.high_level_design.hasOwnProperty('data_model_changes')) {
                // Provide a note that details are in the refined section
                remainingSections.high_level_design.data_model_changes = "";
            }
            markdown += `## Refined Data Models\n\n`;
            
            console.log('ArchKnow [convertDesignDocToMarkdown]: Refined Data Models:', JSON.stringify(refinedDataModelsOutput, null, 3));
            // Pass the full designDoc for linking context if any decision IDs are in this data
            refinedDataModelsOutput.refined_data_models.models.forEach((model:any) => {
                model.schema_definition = JSON.stringify(model.schema_definition, null);});
            console.log('ArchKnow [convertDesignDocToMarkdown]: Refined Data Models:', JSON.stringify(refinedDataModelsOutput, null, 3));
            markdown += this.processNestedObject(refinedDataModelsOutput, 3, refsToUse, designDocData, slugify);
            markdown += '\n';
        }
        
        // Handle referenced decisions separately at the end
        if (refsToUse.length > 0) {
            markdown += `## Referenced Architectural Decisions\n\n`;
            markdown += `The following architectural decisions were referenced or considered during the generation of this design document:\n\n`;
            refsToUse.forEach(ref => {
                if (!ref.id || !ref?.metadata?.title) {
                    console.warn('ArchKnow [convertDesignDocToMarkdown]: Incomplete referenced decision data:', ref);
                }
                const idParts = ref.id.split('_');
                const displayableId = idParts.length > 2 ? `${idParts[0]}_${idParts[1]}` : ref.id;
                markdown += `### [${displayableId}](cursor://archknow.archknow/viewDecision?id=${ref.id}) ${linkifyDecisionIds(ref.metadata.title, refsToUse)}\n\n`;
                if (ref.summary_of_relevance_in_this_design) {
                    markdown += `**Relevance:** ${linkifyDecisionIds(ref.summary_of_relevance_in_this_design, refsToUse)}\n\n`;
                }
                if (ref.metadata.impact) {
                    markdown += `**Impact:** ${linkifyDecisionIds(ref.metadata.impact, refsToUse)}\n\n`;
                }
                if (ref.metadata.best_practice_reason) {
                    markdown += `**Best Practice Reason:** ${linkifyDecisionIds(ref.metadata.best_practice_reason, refsToUse)}\n\n`;
                }
                if (ref.metadata.dev_prompt) {
                    markdown += `**Developer Prompt:** ${linkifyDecisionIds(ref.metadata.dev_prompt, refsToUse)}\n\n`;
                }
                if (ref.metadata.risks_extracted) {
                    if (ref.metadata.risks_extracted.high && ref.metadata.risks_extracted.high.length > 0) {
                        markdown += `**High Risks:**\n`;
                        ref.metadata.risks_extracted.high.forEach(risk => {
                            markdown += `- ${linkifyDecisionIds(risk, refsToUse)}\n`;
                        });
                        markdown += '\n';
                    }
                    if (ref.metadata.risks_extracted.medium && ref.metadata.risks_extracted.medium.length > 0) {
                        markdown += `**Medium Risks:**\n`;
                        ref.metadata.risks_extracted.medium.forEach(risk => {
                            markdown += `- ${linkifyDecisionIds(risk, refsToUse)}\n`;
                        });
                        markdown += '\n';
                    }
                }
                if (ref.metadata.related_files && ref.metadata.related_files.length > 0) {
                    markdown += `**Related Files:**\n`;
                    ref.metadata.related_files.forEach(file => {
                        markdown += `- ${linkifyDecisionIds(file, refsToUse)}\n`; // Though files are less likely to contain decision IDs
                    });
                    markdown += '\n';
                }
            });
        }

        // Add AI Agent Instructions section
        // markdown += `\n## 🤖 AI Agent Instructions (for actionable changes)\n\n`;
        // markdown += `Based on the design document above, here are specific instructions for an AI agent to help implement the changes. `;
        // markdown += `Each instruction will specify the file to be modified, the issue to address, and the action to take.\n\n`;
        // markdown += `*No specific AI agent instructions generated at this stage. This section can be populated manually or by a subsequent AI process that analyzes the design for concrete code changes.*\n`;
        
        // Add Infrastructure and Operations section at the end
        if (infraOpsData) {
            markdown += `\n## Infrastructure and Operations\n\n`;
            markdown += this.processNestedObject({ infrastructure_and_ops: infraOpsData }, 2, refsToUse, designDocData, slugify).replace(`### Infrastructure And Ops\n\n`, ''); // Remove redundant inner title if processNestedObject adds one
            markdown += '\n';
        }


        console.log('ArchKnow [convertDesignDocToMarkdown]: Successfully converted design doc to markdown');
        return markdown;
    }

    // Helper function to convert camelCase or snake_case to Title Case
    private toTitleCase(str: string): string {
        if (!str) return '';
        return str
            .replace(/_/g, ' ')
            .replace(/([A-Z])/g, ' $1') // Add space before uppercase letters
            .replace(/^./, s => s.toUpperCase())
            .trim();
    }
    
    // Helper function to process nested objects recursively
    private processNestedObject(
        obj: any, 
        level: number = 2, 
        referencedDecisionsToUse: ReferencedDecisionForDoc[],
        designDocDataForContext?: GeneratedDesignDocFromApi,
        slugify?: (text: string) => string
    ): string {
        let markdown = '';
        if (!obj) return markdown;

        const localSlugify = slugify || ((text: string) => text.toLowerCase().replace(/\s+/g, '-'));

        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                const value = obj[key];
                const title = this.toTitleCase(key.replace(/_/g, ' '));
                const anchor = localSlugify(title);

                if (value === null || value === undefined || (typeof value === 'string' && value.trim() === 'N/A') || (Array.isArray(value) && value.length === 0)) {
                    continue;
                }

                if (key === 'process_flow_diagram_mermaid' && typeof value === 'string' && value.trim()) {
                    markdown += `<h${level} id="${anchor}">${title}</h${level}>\n\n<div class="mermaid">\n${value}\n</div>\n\n`;
                    continue;
                }

                markdown += `<h${level} id="${anchor}">${title}</h${level}>\n\n`;

                if (Array.isArray(value)) {
                    if (value.every(item => typeof item === 'string')) {
                        markdown += value.map(item => `- ${item}`).join('\n') + '\n\n';
                    } else {
                        // Handle arrays of objects
                        markdown += value.map(item => {
                            return `<li>\n\n${this.processNestedObject(item, level + 1, referencedDecisionsToUse, designDocDataForContext, localSlugify)}\n\n</li>`;
                        }).join('');
                    }
                } else if (typeof value === 'object') {
                    markdown += this.processNestedObject(value, level + 1, referencedDecisionsToUse, designDocDataForContext, localSlugify);
                } else {
                    markdown += `${String(value)}\n\n`;
                }
            }
        }
        return markdown;
    }

    isDesignDocPath(docUri: vscode.Uri): boolean {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return false;
        }
        
        const workspaceRoot = workspaceFolders[0].uri;
        const designDocsRootPath = path.join(workspaceRoot.fsPath, DESIGN_DOCS_BASE_FOLDER);

        return docUri.fsPath.startsWith(designDocsRootPath) && docUri.fsPath.toLowerCase().endsWith('.md');
    }

    private formatImplementationPlanToMarkdown(implementationPlanContent: string): string {
        // Check if the content is already markdown or if it's JSON that needs formatting
        if (implementationPlanContent.trim().startsWith('#') || implementationPlanContent.includes('## ')) {
            // Already markdown format
            return implementationPlanContent;
        }

        try {
            // Try to parse as JSON and format it nicely
            const planData = JSON.parse(implementationPlanContent);
            
            let markdown = `# ${planData.implementation_plan_title || 'Implementation Plan'}\n\n`;
            markdown += `**Version:** ${planData.version || '1.0'}\n\n`;
            
            if (planData.design_document_reference_title) {
                markdown += `**Design Document:** ${planData.design_document_reference_title}\n\n`;
            }

            // Goals and Non-Goals
            if (planData.overall_project_goals && planData.overall_project_goals.length > 0) {
                markdown += `## Project Goals\n\n`;
                planData.overall_project_goals.forEach((goal: string) => {
                    markdown += `- ${goal}\n`;
                });
                markdown += '\n';
            }

            if (planData.overall_project_non_goals && planData.overall_project_non_goals.length > 0) {
                markdown += `## Non-Goals\n\n`;
                planData.overall_project_non_goals.forEach((nonGoal: string) => {
                    markdown += `- ${nonGoal}\n`;
                });
                markdown += '\n';
            }

            // Git Workflow Guidance
            if (planData.git_workflow_guidance) {
                const gitGuidance = planData.git_workflow_guidance;
                markdown += `## Git Workflow & Branch Strategy\n\n`;
                markdown += `**Primary Branch:** \`${gitGuidance.primary_branch || 'main'}\`\n\n`;
                
                if (planData.main_feature_branch_name) {
                    markdown += `**Main Feature Branch:** \`${planData.main_feature_branch_name}\`\n\n`;
                }
                
                if (gitGuidance.integration_strategy) {
                    markdown += `**Integration Strategy:** ${gitGuidance.integration_strategy}\n\n`;
                }
                
                if (gitGuidance.pr_naming_convention) {
                    markdown += `**PR Naming Convention:** ${gitGuidance.pr_naming_convention}\n\n`;
                }
                
                if (gitGuidance.commit_message_format) {
                    markdown += `**Commit Message Format:** ${gitGuidance.commit_message_format}\n\n`;
                }
                
                if (gitGuidance.milestone_integration_workflow) {
                    markdown += `**Milestone Integration Workflow:** ${gitGuidance.milestone_integration_workflow}\n\n`;
                }
            }

            // Milestones
            if (planData.milestones && planData.milestones.length > 0) {
                markdown += `## Implementation Milestones\n\n`;
                
                planData.milestones.forEach((milestone: any, index: number) => {
                    markdown += `### ${milestone.milestone_id || `Milestone ${index + 1}`}: ${milestone.title}\n\n`;
                    
                    if (milestone.description) {
                        markdown += `**Description:** ${milestone.description}\n\n`;
                    }
                    
                    if (milestone.branch_naming_rationale) {
                        markdown += `**Branch Naming Rationale:** ${milestone.branch_naming_rationale}\n\n`;
                    }
                    
                    markdown += `**Priority:** ${milestone.priority || 'Medium'} | **Complexity:** ${milestone.technical_complexity || 'Medium'}\n\n`;
                    
                    if (milestone.key_tasks_and_deliverables && milestone.key_tasks_and_deliverables.length > 0) {
                        markdown += `**Key Tasks & Deliverables:**\n`;
                        milestone.key_tasks_and_deliverables.forEach((task: string) => {
                            markdown += `- ${task}\n`;
                        });
                        markdown += '\n';
                    }
                    
                    if (milestone.verification_criteria && milestone.verification_criteria.length > 0) {
                        markdown += `**Verification Criteria:**\n`;
                        milestone.verification_criteria.forEach((criteria: string) => {
                            markdown += `- ${criteria}\n`;
                        });
                        markdown += '\n';
                    }
                    
                    if (milestone.depends_on_milestones && milestone.depends_on_milestones.length > 0 && milestone.depends_on_milestones[0] !== 'None') {
                        markdown += `**Dependencies:** ${milestone.depends_on_milestones.join(', ')}\n\n`;
                    }
                    
                    if (milestone.ai_agent_compliance) {
                        const compliance = milestone.ai_agent_compliance;
                        markdown += `**AI Agent Protocol Compliance:**\n`;
                        if (compliance.independent_integration) {
                            markdown += `- **Independent Integration:** ${compliance.independent_integration}\n`;
                        }
                        if (compliance.atomic_commit_strategy) {
                            markdown += `- **Commit Strategy:** ${compliance.atomic_commit_strategy}\n`;
                        }
                        if (compliance.testing_approach) {
                            markdown += `- **Testing Approach:** ${compliance.testing_approach}\n`;
                        }
                        markdown += '\n';
                    }
                    
                    if (milestone.git_integration_notes) {
                        markdown += `**Integration Notes:** ${milestone.git_integration_notes}\n\n`;
                    }
                    
                    // Add guidance if available
                    if (milestone.applicable_developer_guidance_and_best_practices && milestone.applicable_developer_guidance_and_best_practices.length > 0) {
                        markdown += `**Relevant Developer Guidance:**\n`;
                        milestone.applicable_developer_guidance_and_best_practices.forEach((guidance: any) => {
                            markdown += `- **${guidance.retrieved_decision_title}:** ${guidance.guidance_summary}\n  ${guidance.related_files.map((file: string) => `- ${file}`).join('\n')}\n`;
                        });
                        markdown += '\n';
                    }
                    
                    markdown += `---\n\n`;
                });
            }

            // AI Agent Protocol Compliance
            if (planData.ai_agent_protocol_integration && planData.ai_agent_protocol_integration.ai_agent_protocol_compliance) {
                const compliance = planData.ai_agent_protocol_integration.ai_agent_protocol_compliance;
                markdown += `## AI Agent Protocol Implementation Guidance\n\n`;
                
                if (compliance.phase_0_guidance) {
                    markdown += `**Phase 0 - Preparation:** ${compliance.phase_0_guidance}\n\n`;
                }
                if (compliance.phase_1_guidance) {
                    markdown += `**Phase 1 - Project Setup:** ${compliance.phase_1_guidance}\n\n`;
                }
                if (compliance.phase_2_guidance) {
                    markdown += `**Phase 2 - Implementation:** ${compliance.phase_2_guidance}\n\n`;
                }
                if (compliance.phase_3_guidance) {
                    markdown += `**Phase 3 - Integration:** ${compliance.phase_3_guidance}\n\n`;
                }
            }

            // Success Metrics
            if (planData.final_success_metrics_recap && planData.final_success_metrics_recap.length > 0) {
                markdown += `## Success Metrics\n\n`;
                planData.final_success_metrics_recap.forEach((metric: string) => {
                    markdown += `- ${metric}\n`;
                });
                markdown += '\n';
            }

            return markdown;
            
        } catch (error) {
            console.warn('ArchKnow [formatImplementationPlanToMarkdown]: Failed to parse as JSON, returning original content:', error);
            // If parsing fails, return original content
            return implementationPlanContent;
        }
    }

    async generateImplementationPlan(docUri: vscode.Uri, userHandle?: string): Promise<void> {
        const { metadata, content: designDocContent } = await this.readDesignDocMetadata(docUri);

        if (!metadata || !designDocContent) {
            vscode.window.showErrorMessage('Could not read design document metadata or content.');
            return;
        }

        if (metadata.status !== 'ready_for_implementation') {
            const proceed = await vscode.window.showWarningMessage(
                `This document is currently in status '${metadata.status}'. Implementation plans are typically generated for documents marked as 'ready_for_implementation'. Proceed anyway?`,
                { modal: true }, 
                'Proceed'
            );
            if (proceed !== 'Proceed') {
                return;
            }
        }

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "Generating Implementation Plan...",
            cancellable: false
        }, async (progress) => {
            try {
                progress.report({ increment: 10, message: "Preparing design document content..." });

                if (!this.apiService) {
                    vscode.window.showErrorMessage('API Service is not available. Cannot generate implementation plan.');
                    console.error("Error generating implementation plan: ApiService is not available.");
                    return;
                }

                const jobId = metadata.jobId;
                if (!jobId) {
                    vscode.window.showErrorMessage('Job ID not found in design document metadata. Cannot generate implementation plan.');
                    console.error("Error generating implementation plan: Job ID is missing from metadata.");
                    return;
                }

                progress.report({ increment: 20, message: "Calling AI to generate implementation plan..." });

                let implementationPlanContent: string;
                try {
                    implementationPlanContent = await this.apiService.callGenerateImplementationPlanApi(
                        jobId,
                        designDocContent,
                        AI_AGENT_PROTOCOL_CONTENT,
                        metadata.title || 'Untitled Design Document'
                    );
                    if (!implementationPlanContent || implementationPlanContent.trim() === "") {
                        throw new Error("API returned empty or invalid implementation plan.");
                    }
                } catch (apiError: any) {
                    vscode.window.showErrorMessage(`Failed to generate implementation plan from API: ${apiError.message}`);
                    console.error("Error calling implementation plan API:", apiError);
                    return; // Stop further processing
                }
                
                progress.report({ increment: 30, message: "Formatting implementation plan..." });

                // Format the implementation plan content as readable markdown
                const formattedPlanContent = this.formatImplementationPlanToMarkdown(implementationPlanContent);
                
                progress.report({ increment: 20, message: "Saving implementation plan..." });

                const workspaceFolders = vscode.workspace.workspaceFolders;
                if (!workspaceFolders || workspaceFolders.length === 0) {
                    vscode.window.showErrorMessage('No workspace folder found to save the implementation plan.');
                    return;
                }
                const workspaceRoot = workspaceFolders[0].uri;
                
                const IMPLEMENTATION_PLANS_SUBDIR = 'implementation_plans';
                const plansFullRelativeDir = path.join(DESIGN_DOCS_BASE_FOLDER, IMPLEMENTATION_PLANS_SUBDIR);

                const plansDirUri = vscode.Uri.joinPath(workspaceRoot, plansFullRelativeDir);
                await vscode.workspace.fs.createDirectory(plansDirUri);

                const safeTitle = (metadata.title || path.basename(docUri.fsPath).replace(/\.md$/, '')).replace(/[^a-z0-9_\-]+/gi, '_');
                const planFileName = `${safeTitle}_ImplementationPlan.md`;
                const planUri = vscode.Uri.joinPath(plansDirUri, planFileName);

                await vscode.workspace.fs.writeFile(planUri, Buffer.from(formattedPlanContent, 'utf8'));
                vscode.window.showInformationMessage(`Implementation plan generated and saved to: ${planUri.fsPath}`);

                progress.report({ increment: 10, message: "Updating original design document..." });
                metadata.status = 'implemented'; // Or a new status like 'implementation_plan_generated' if preferred
                metadata.implementation_plan_uri = planUri.toString(); // Store as string
                metadata.implementation_plan_generated = true; // Explicit flag
                metadata.implemented_at = new Date().toISOString(); // Or a more specific timestamp like 'implementation_plan_generated_at'
                if (!metadata.feedback) metadata.feedback = [];
                metadata.feedback.push({
                    user: userHandle || 'system',
                    comment: `Implementation plan created: ${planFileName}`,
                    timestamp: new Date().toISOString(),
                    type: 'system_action'
                });

                await this.writeDesignDocMetadata(docUri, metadata, designDocContent);
                const newDesignDocUri = await this.moveDesignDoc(docUri, 'implemented');

                progress.report({ increment: 10, message: "Opening plan..." });
                await vscode.window.showTextDocument(planUri);

                if (newDesignDocUri) {
                    vscode.window.showInformationMessage(`Design document ${path.basename(newDesignDocUri.fsPath)} moved to 'implemented'.`);
                } else {
                    vscode.window.showWarningMessage('Implementation plan generated, but failed to move original design document.');
                }

            } catch (error: any) {
                vscode.window.showErrorMessage(`Failed to generate implementation plan: ${error.message}`);
                console.error("Error generating implementation plan:", error);
            }
        });
    }

    async createAndOpenDesignDocFromJob(
        jobParams: CreateDocFromJobParams, // Updated to use the interface
        userHandle?: string,
        referencedDecisions?: ReferencedDecisionForDoc[] // Added new parameter
    ): Promise<vscode.Uri | null> {
        console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: START for task: "${jobParams.taskTitle}"`);
        console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Received jobParams.output_file_path: "${jobParams.output_file_path}"`);
        console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Received jobParams.jobId: "${jobParams.jobId}"`);
        if (referencedDecisions) {
            console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Received explicit referencedDecisions, count: ${referencedDecisions.length}`);
        } else {
            console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: No explicit referencedDecisions received.`);
        }
        
        // Enhanced logging for initial state of phase3_output
        console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Initial typeof jobParams.phase3_output: ${typeof jobParams.phase3_output}`);
        if (typeof jobParams.phase3_output === 'undefined') {
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Initial jobParams.phase3_output IS undefined.`);
        } else if (jobParams.phase3_output === null) {
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Initial jobParams.phase3_output IS null.`);
        } else {
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Initial jobParams.phase3_output keys: ${Object.keys(jobParams.phase3_output).join(', ') || 'NONE'}`);
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Initial jobParams.phase3_output.title: "${jobParams.phase3_output.title}"`);
            if (jobParams.phase3_output.referenced_decisions) {
                console.warn(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Initial jobParams.phase3_output CONTAINS a 'referenced_decisions' key. This should ideally be handled by the caller if explicit referencedDecisions are also passed.`);
            }
        }

        // Keep a reference to the initial phase3_output to work with
        let effectivePhase3Output = jobParams.phase3_output;

        if (!this.workspaceRoot) {
            console.error('ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: ERROR - Workspace root not found.');
            vscode.window.showErrorMessage('ArchKnow: Workspace root not found. Cannot create design document.');
            return null;
        }
        console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Workspace root: "${this.workspaceRoot.fsPath}"`);

        if (!jobParams.output_file_path) {
            console.error('ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: ERROR - jobParams.output_file_path is missing.');
            vscode.window.showErrorMessage('ArchKnow: Output file path is missing. Cannot create design document.');
            return null;
        }

        const absoluteFilePath = path.join(this.workspaceRoot.fsPath, jobParams.output_file_path);
        const docUri = vscode.Uri.file(absoluteFilePath);
        console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Calculated absoluteFilePath for new doc: "${absoluteFilePath}"`);
        console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Document URI: "${docUri.toString()}"`);

        try {
            try {
                console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Checking existence of local file: "${absoluteFilePath}"`);
                await vscode.workspace.fs.stat(docUri);
                console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Document already exists locally: "${absoluteFilePath}"`);
                
                // If file exists locally, open it directly instead of overwriting
                console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Opening existing local file directly without overwriting.`);
                await vscode.commands.executeCommand('archknow.openDesignDoc', docUri);
                console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: archknow.openDesignDoc command executed for existing file: "${docUri.fsPath}"`);
                
                vscode.window.showInformationMessage(`Design document "${path.basename(absoluteFilePath)}" opened from local file.`);
                console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: END for task: "${jobParams.taskTitle}" - Success (existing file opened)`);
                return docUri;
                
            } catch (error) { // Local file does NOT exist
                console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Local file "${absoluteFilePath}" not found. Processing with provided or fetched content.`);

                // Check if the initially provided phase3_output is usable (has a title)
                if (effectivePhase3Output && effectivePhase3Output.title) {
                    console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Using pre-loaded phase3_output (Title: "${effectivePhase3Output.title}") as local file not found.`);
                    // effectivePhase3Output is already good, no server fetch needed for it.
                } else {
                    // Pre-loaded phase3_output is not good (falsy, or missing title), so we MUST attempt to fetch.
                    console.warn(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Pre-loaded phase3_output (Title: ${effectivePhase3Output?.title}) is invalid or missing title. Attempting server fetch for jobId: "${jobParams.jobId}"`);
                    if (jobParams.jobId && this.apiService) {
                        try {
                            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: About to fetch phase3_output from server for jobId: "${jobParams.jobId}"`);
                            const docContentFromServer = await this.apiService.fetchDesignDocContent(jobParams.jobId);
                            if (docContentFromServer) { // If fetch returned actual content
                                console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Successfully fetched document content from server.`);
                                effectivePhase3Output = docContentFromServer.phase3_output; 
                                jobParams.phase3_5_refined_data_models_output = docContentFromServer.phase3_5_refined_data_models_output; // Update with fetched content
                            } else {
                                // Fetch returned null/undefined, this is the specific error condition user reported
                                console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Server fetch for jobId "${jobParams.jobId}" returned null/undefined content. The pre-loaded content was also insufficient.`);
                                throw new Error('Could not fetch document content from server (content was null/undefined after explicit fetch)');
                            }
                        } catch (fetchError: any) { // Catches errors from fetchDesignDocContent or the error thrown above
                            console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: ERROR during server fetch attempt:`, fetchError);
                            throw new Error(`Failed to obtain valid document content after server fetch attempt: ${fetchError.message}`);
                        }
                    } else {
                        // Cannot fetch (no jobId/apiService), and pre-loaded was bad. The validation below will catch this state.
                        console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Cannot fetch from server (no jobId/apiService) and pre-loaded phase3_output is invalid. Document creation will likely fail validation.`);
                    }
                }
            }

            const dirToCreate = path.dirname(absoluteFilePath);
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Ensuring directory exists: "${dirToCreate}"`);
            await vscode.workspace.fs.createDirectory(vscode.Uri.file(dirToCreate));
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Directory ensured: "${dirToCreate}"`);

            // Validate the effectivePhase3Output (either initial, or fetched and updated)
            if (!effectivePhase3Output) {
                console.error('ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: ERROR - Effective design document content (effectivePhase3Output) is missing or invalid (no title).', effectivePhase3Output);
                vscode.window.showErrorMessage('ArchKnow: Generated/fetched design document content is missing or invalid.');
                return null;
            }
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Valid effectivePhase3Output found, title: "${jobParams.taskTitle}"`);
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Phase 3_5, title: "${jobParams.phase3_5_refined_data_models_output}"`);

            // Make a copy of effectivePhase3Output to safely delete referenced_decisions if it exists,
            // to ensure we only use the explicitly passed `referencedDecisions` parameter.
            const phase3DataForMarkdown = { ...effectivePhase3Output };
            if (phase3DataForMarkdown.referenced_decisions) {
                console.warn('ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Removing "referenced_decisions" from phase3_output data before passing to convertDesignDocToMarkdown, as explicit ones are provided.');
                delete phase3DataForMarkdown.referenced_decisions;
            }

            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Converting phase3DataForMarkdown to markdown...`);
            const markdownContent = this.convertDesignDocToMarkdown(
                phase3DataForMarkdown, 
                jobParams.phase3_5_refined_data_models_output,
                referencedDecisions // Pass the explicit referencedDecisions here
            );
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Markdown content generated (length: ${markdownContent.length}).`);
            
            const initialMetadata: DesignDocFrontmatter = {
                title: effectivePhase3Output.title || jobParams.taskTitle, // Use title from effectivePhase3Output
                status: 'pending',
                author: userHandle || '',
                lastUpdated: new Date().toISOString(),
                jobId: jobParams.jobId
            };

            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Writing design document to URI: "${docUri.toString()}" with metadata:`, JSON.stringify(initialMetadata));
            await this.writeDesignDocMetadata(docUri, initialMetadata, markdownContent);
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Successfully wrote design document with metadata to: "${absoluteFilePath}"`);

            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: Requesting to open document via archknow.openDesignDoc command for URI: "${docUri.toString()}"`);
            await vscode.commands.executeCommand('archknow.openDesignDoc', docUri);
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: archknow.openDesignDoc command executed for: "${docUri.fsPath}"`);
            
            vscode.window.showInformationMessage(`Design document "${path.basename(absoluteFilePath)}" created/updated and initiated open action.`);
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: END for task: "${jobParams.taskTitle}" - Success`);
            return docUri;

        } catch (error: any) {
            console.error(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: ERROR creating/saving design doc from job: "${jobParams.taskTitle}"`, error);
            vscode.window.showErrorMessage(`ArchKnow: Failed to create or save design document: ${error.message}`);
            console.log(`ArchKnow [DesignDocWorkflowService.createAndOpenDesignDocFromJob]: END for task: "${jobParams.taskTitle}" - Failure`);
            return null;
        }
    }

    async addSummaryToDocument(docUri: vscode.Uri, summary: string, userHandle: string = 'AI Summarizer'): Promise<void> {
        console.log(`ArchKnow [DesignDocWorkflowService.addSummaryToDocument]: Adding summary to ${docUri.fsPath}`);
        try {
            const { metadata, content } = await this.readDesignDocMetadata(docUri);
            if (!metadata) {
                vscode.window.showErrorMessage('Failed to add summary: Could not read document metadata.');
                return;
            }

            if (!metadata.feedback) {
                metadata.feedback = [];
            }

            metadata.feedback.push({
                user: userHandle,
                comment: `AI Generated Summary:\n${summary}`,
                timestamp: new Date().toISOString(),
                type: 'system_action' // Or a new type like 'ai_summary' if you prefer
            });

            metadata.lastUpdated = new Date().toISOString();

            await this.writeDesignDocMetadata(docUri, metadata, content);
            console.log(`ArchKnow [DesignDocWorkflowService.addSummaryToDocument]: Summary added and document saved.`);
            vscode.window.showInformationMessage('AI-generated summary added to comments.');

        } catch (error: any) {
            console.error(`ArchKnow [DesignDocWorkflowService.addSummaryToDocument]: Error adding summary:`, error);
            vscode.window.showErrorMessage(`Failed to add AI summary to document: ${error.message}`);
        }
    }
}