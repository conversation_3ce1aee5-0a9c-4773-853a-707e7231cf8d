{"name": "archknow-mcp-server", "version": "0.1.0", "description": "MCP server for ArchKnow to implement GitHub issues.", "main": "dist/server.js", "bin": {"archknow-mcp-server": "dist/server.js"}, "scripts": {"build": "esbuild src/server.ts --bundle --platform=node --outfile=../out/mcp_server/server.js", "start": "node dist/server.js", "dev": "ts-node src/server.ts"}, "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.0", "axios": "^1.4.0", "zod": "^3.21.4"}, "devDependencies": {"@types/node": "^20.4.5", "esbuild": "^0.20.0", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}