import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import axios from "axios";

// The local API endpoint provided by the VS Code extension
const EXTENSION_API_URL = "http://localhost:7635/api/issue-details";

const server = new McpServer({
  name: "archknow-mcp",
  version: "1.0.0",
});

const implementGitHubIssueParams = {
  issueUrl: z.string().url().describe("The full URL of the GitHub issue to implement."),
};

server.tool(
  "implement-github-issue",
  "Fetches the details of a GitHub issue marked as 'ready-for-implementation' and provides the title, design document, and implementation plan.",
  implementGitHubIssueParams,
  async ({ issueUrl }: { issueUrl: string }) => {
    try {
      // The MCP server will call the extension's local server
      const response = await axios.post(EXTENSION_API_URL, { issueUrl });

      if (response.data.success) {
        return {
          content: [
            {
              type: "text",
              text: response.data.details,
            },
          ],
        };
      } else {
        return {
          content: [
            {
              type: "text",
              text: `Error: ${response.data.error}`,
            },
          ],
        };
      }
    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response) {
        return {
          content: [
            {
              type: "text",
              text: `Failed to connect to the ArchKnow extension. Please ensure the extension is running. Details: ${error.response.data.error || error.message}`,
            },
          ],
        };
      }
      return {
        content: [
          {
            type: "text",
            text: `An unexpected error occurred: ${error.message}`,
          },
        ],
      };
    }
  }
);

/**
 * PROGRESSIVE MILESTONE REVIEW SYSTEM
 * 
 * This system prevents "moving goalposts" by implementing staged review logic:
 * 
 * FIRST REVIEW (reviewIteration: 1):
 * - Comprehensive analysis with standard criteria
 * - Identifies all critical risks and scope issues
 * 
 * SUBSEQUENT REVIEWS (reviewIteration: 2+):
 * - FOCUSED mode with higher severity threshold
 * - Only flags NEW critical issues not previously identified
 * - More lenient scope compliance (accepts reasonable variations)
 * - Respects previously accepted risks
 * 
 * USAGE EXAMPLES:
 * 
 * // Initial comprehensive review
 * review_milestone_webview({
 *   repoSlug: "owner/repo",
 *   codeChanges: [...],
 *   designDoc: "...",
 *   implementationPlan: "...",
 *   reviewIteration: 1
 * })
 * 
 * // Follow-up focused review after addressing feedback
 * review_milestone_webview({
 *   repoSlug: "owner/repo", 
 *   codeChanges: [...],
 *   designDoc: "...",
 *   implementationPlan: "...",
 *   reviewIteration: 2,
 *   existingContext: "Previous review found X, Y, Z issues. Addressed X and Y.",
 *   previouslyAcceptedRisks: ["Security risk: API rate limiting not implemented"]
 * })
 * 
 * // Force minimal review even on first iteration
 * review_milestone_webview({
 *   repoSlug: "owner/repo",
 *   codeChanges: [...], 
 *   designDoc: "...",
 *   implementationPlan: "...",
 *   forceMinimalReview: true
 * })
 */

// const reviewMilestoneChangesParams = {
//   repoSlug: z.string().describe("The repository slug (owner/repo) for the changes being reviewed."),
//   codeChanges: z.array(z.object({
//     filename: z.string(),
//     patch: z.string(),
//     additions: z.number().optional(),
//     deletions: z.number().optional()
//   })).describe("Array of code changes with filename and patch information."),
//   designDoc: z.string().describe("The design document content for the milestone."),
//   implementationPlan: z.string().describe("The implementation plan content for the milestone."),
//   milestoneTitle: z.string().optional().describe("Optional title for the milestone being reviewed."),
//   milestoneId: z.string().optional().describe("The ID of the milestone to review (e.g., M1.0). If provided, review will be scoped to this milestone within the implementation plan."),
//   existingContext: z.string().optional().describe("Optional existing context for the review."),
//   issueUrl: z.string().optional().describe("Optional GitHub issue URL to link with the commit message."),
//   reviewIteration: z.number().optional().describe("The review iteration number (1 for initial review, 2+ for subsequent reviews)."),
//   previouslyAcceptedRisks: z.array(z.string()).optional().describe("Array of risk descriptions that have been previously accepted and should not be re-flagged."),
//   forceMinimalReview: z.boolean().optional().describe("Force a minimal, focused review even on first iteration.")
// };

// server.tool(
//   "review-milestone-changes",
//   "Reviews staged code changes against a milestone's design document and implementation plan to assess scope compliance and identify critical risks.",
//   reviewMilestoneChangesParams,
//   async ({ 
//     repoSlug, 
//     codeChanges, 
//     designDoc, 
//     implementationPlan, 
//     milestoneTitle, 
//     milestoneId,
//     existingContext, 
//     issueUrl,
//     reviewIteration,
//     previouslyAcceptedRisks,
//     forceMinimalReview
//   }: {
//     repoSlug: string;
//     codeChanges: Array<{ filename: string; patch: string; additions?: number; deletions?: number }>;
//     designDoc: string;
//     implementationPlan: string;
//     milestoneTitle?: string;
//     milestoneId?: string;
//     existingContext?: string;
//     issueUrl?: string;
//     reviewIteration?: number;
//     previouslyAcceptedRisks?: string[];
//     forceMinimalReview?: boolean;
//   }) => {
//     try {
//       const response = await axios.post(`${EXTENSION_API_URL.replace('/api/issue-details', '/api/milestone-review')}`, {
//         repoSlug,
//         codeChanges,
//         designDoc,
//         implementationPlan,
//         milestoneTitle,
//         milestoneId,
//         existingContext,
//         issueUrl,
//         reviewIteration,
//         previouslyAcceptedRisks,
//         forceMinimalReview
//       }, {
//         headers: {
//           'Content-Type': 'application/json'
//         }
//       });

//       if (response.data.success) {
//         const result = response.data.result;
        
//         // Format the response for better readability
//         let formattedResponse = `# Milestone Review Results\n\n`;
        
//         if (result.milestone_context) {
//           formattedResponse += `## Milestone: ${result.milestone_context.title}\n`;
//           formattedResponse += `**Status:** ${result.scope_status}\n`;
//           formattedResponse += `**Integration:** ${result.integration_blocked ? 'BLOCKED' : 'READY'}\n\n`;
//         }
        
//         formattedResponse += `## Summary\n${result.summary}\n\n`;
        
//         if (result.scope_assessment) {
//           formattedResponse += `## Scope Assessment\n`;
//           if (result.scope_assessment.deliverables_status?.length > 0) {
//             formattedResponse += `### Deliverables Status\n`;
//             result.scope_assessment.deliverables_status.forEach((d: any) => {
//               formattedResponse += `- **${d.deliverable}**: ${d.status}\n  ${d.evidence}\n`;
//             });
//             formattedResponse += '\n';
//           }
//         }
        
//         // Add critical risks if any
//         const allRisks = [
//           ...(result.critical_security_risks || []),
//           ...(result.critical_performance_risks || []),
//           ...(result.critical_ux_risks || [])
//         ];
        
//         if (allRisks.length > 0) {
//           formattedResponse += `## Critical Risks (${allRisks.length})\n`;
//           allRisks.forEach((risk: any, index: number) => {
//             formattedResponse += `### ${index + 1}. ${risk.risk}\n`;
//             formattedResponse += `**File:** ${risk.file}\n`;
//             formattedResponse += `**Impact:** ${risk.impact}\n`;
//             formattedResponse += `**Fix:** ${risk.fix}\n\n`;
//           });
//         }
        
//         if (result.pr_summary) {
//           formattedResponse += `## PR Summary\n${result.pr_summary}\n`;
//         }
        
//         return {
//           content: [
//             {
//               type: "text",
//               text: formattedResponse,
//             },
//           ],
//         };
//       } else {
//         return {
//           content: [
//             {
//               type: "text",
//               text: `Error: ${response.data.error}`,
//             },
//           ],
//         };
//       }
//     } catch (error: any) {
//       if (axios.isAxiosError(error) && error.response) {
//         return {
//           content: [
//             {
//               type: "text",
//               text: `Failed to connect to the ArchKnow extension for milestone review. Please ensure the extension is running. Details: ${error.response.data.error || error.message}`,
//             },
//           ],
//         };
//       }
//       return {
//         content: [
//           {
//             type: "text",
//             text: `An unexpected error occurred during milestone review: ${error.message}`,
//           },
//         ],
//       };
//     }
//   }
// );

// server.tool(
//   "review-milestone-webview",
//   "Reviews staged code changes against a milestone's design document and implementation plan to assess scope compliance and identify critical risks. Results are displayed in VS Code webview AND returned to Cursor.",
//   reviewMilestoneChangesParams,
//   async ({ 
//     repoSlug, 
//     codeChanges, 
//     designDoc, 
//     implementationPlan, 
//     milestoneTitle, 
//     milestoneId,
//     existingContext, 
//     issueUrl,
//     reviewIteration,
//     previouslyAcceptedRisks,
//     forceMinimalReview
//   }: {
//     repoSlug: string;
//     codeChanges: Array<{ filename: string; patch: string; additions?: number; deletions?: number }>;
//     designDoc: string;
//     implementationPlan: string;
//     milestoneTitle?: string;
//     milestoneId?: string;
//     existingContext?: string;
//     issueUrl?: string;
//     reviewIteration?: number;
//     previouslyAcceptedRisks?: string[];
//     forceMinimalReview?: boolean;
//   }) => {
//     try {
//       const response = await axios.post(`${EXTENSION_API_URL.replace('/api/issue-details', '/api/milestone-review-webview')}`, {
//         repoSlug,
//         codeChanges,
//         designDoc,
//         implementationPlan,
//         milestoneTitle,
//         milestoneId,
//         existingContext,
//         issueUrl,
//         reviewIteration,
//         previouslyAcceptedRisks,
//         forceMinimalReview
//       }, {
//         headers: {
//           'Content-Type': 'application/json'
//         }
//       });

//       if (response.data.success) {
//         const result = response.data.result;
        
//         // Format the response for better readability
//         let formattedResponse = `# Milestone Review Results\n\n`;
        
//         if (result.milestone_context) {
//           formattedResponse += `## Milestone: ${result.milestone_context.title}\n`;
//           formattedResponse += `**Status:** ${result.scope_status}\n`;
//           formattedResponse += `**Integration:** ${result.integration_blocked ? 'BLOCKED' : 'READY'}\n`;
//           formattedResponse += `**Webview:** ✅ Results displayed in VS Code\n\n`;
//         }
        
//         formattedResponse += `## Summary\n${result.summary}\n\n`;
        
//         if (result.scope_assessment) {
//           formattedResponse += `## Scope Assessment\n`;
//           if (result.scope_assessment.deliverables_status?.length > 0) {
//             formattedResponse += `### Deliverables Status\n`;
//             result.scope_assessment.deliverables_status.forEach((d: any) => {
//               formattedResponse += `- **${d.deliverable}**: ${d.status}\n  ${d.evidence}\n`;
//             });
//             formattedResponse += '\n';
//           }
//         }
        
//         // Add critical risks if any
//         const allRisks = [
//           ...(result.critical_security_risks || []),
//           ...(result.critical_performance_risks || []),
//           ...(result.critical_ux_risks || [])
//         ];
        
//         if (allRisks.length > 0) {
//           formattedResponse += `## Critical Risks (${allRisks.length})\n`;
//           allRisks.forEach((risk: any, index: number) => {
//             formattedResponse += `### ${index + 1}. ${risk.risk}\n`;
//             formattedResponse += `**File:** ${risk.file}\n`;
//             formattedResponse += `**Impact:** ${risk.impact}\n`;
//             formattedResponse += `**Fix:** ${risk.fix}\n\n`;
//           });
//         }
        
//         if (result.pr_summary) {
//           formattedResponse += `## PR Summary\n${result.pr_summary}\n`;
//         }
        
//         formattedResponse += `\n---\n\n*💡 Detailed results with interactive features are now available in the VS Code ArchKnow extension webview.*`;
        
//         return {
//           content: [
//             {
//               type: "text",
//               text: formattedResponse,
//             },
//           ],
//         };
//       } else {
//         return {
//           content: [
//             {
//               type: "text",
//               text: `Error: ${response.data.error}`,
//             },
//           ],
//         };
//       }
//     } catch (error: any) {
//       if (axios.isAxiosError(error) && error.response) {
//         return {
//           content: [
//             {
//               type: "text",
//               text: `Failed to connect to the ArchKnow extension for milestone review with webview. Please ensure the extension is running. Details: ${error.response.data.error || error.message}`,
//             },
//           ],
//         };
//       }
//       return {
//         content: [
//           {
//             type: "text",
//             text: `An unexpected error occurred during milestone review with webview: ${error.message}`,
//           },
//         ],
//       };
//     }
//   }
// );

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("ArchKnow MCP Server running on stdio");
}

main().catch((error) => {
  console.error("Fatal error in main():", error);
  process.exit(1);
}); 