"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mcp_js_1 = require("@modelcontextprotocol/sdk/server/mcp.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const zod_1 = require("zod");
const axios_1 = __importDefault(require("axios"));
// The local API endpoint provided by the VS Code extension
const EXTENSION_API_URL = "http://localhost:7635/api/issue-details";
const server = new mcp_js_1.McpServer({
    name: "archknow-mcp",
    version: "1.0.0",
});
const implementGitHubIssueParams = {
    issueUrl: zod_1.z.string().url().describe("The full URL of the GitHub issue to implement."),
};
server.tool("implement_github_issue", "Fetches the details of a GitHub issue marked as 'ready-for-implementation' and provides the title, design document, and implementation plan.", implementGitHubIssueParams, async ({ issueUrl }) => {
    try {
        // The MCP server will call the extension's local server
        const response = await axios_1.default.post(EXTENSION_API_URL, { issueUrl });
        if (response.data.success) {
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify({ "details": response.data.details,
                                "prompt": "Confirm with user which milestone stating the ID and names of all that exist in implementation plan, then follow the github branch, scope and instructions strictly, clarify where needed. make sure user can follow along  context of the milestone without being verbose. dont generate unnecessary documentation. After milestone 1 code is complete, stage the changes and ask user to run review of milestone."
                        })
                    },
                ],
            };
        }
        else {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error: ${response.data.error}`,
                    },
                ],
            };
        }
    }
    catch (error) {
        if (axios_1.default.isAxiosError(error) && error.response) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Failed to connect to the ArchKnow extension. Please ensure the extension is running. Details: ${error.response.data.error || error.message}`,
                    },
                ],
            };
        }
        return {
            content: [
                {
                    type: "text",
                    text: `An unexpected error occurred: ${error.message}`,
                },
            ],
        };
    }
});
async function main() {
    const transport = new stdio_js_1.StdioServerTransport();
    await server.connect(transport);
    console.error("ArchKnow MCP Server running on stdio");
}
main().catch((error) => {
    console.error("Fatal error in main():", error);
    process.exit(1);
});
//# sourceMappingURL=server.js.map