{"name": "archknow", "displayName": "ArchKnow Companion", "description": "Access ArchKnow decision records from VSCode.", "version": "0.0.1", "main": "./out/extension.js", "publisher": "archknow", "engines": {"vscode": "^1.87.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished", "onCommand:archknow.openPromptGenerator", "onCommand:archknow.generateDesignDoc", "onCommand:archknow.showDecisionById", "onCommand:archknow.openDesignDoc", "onUri:cursor://archknow.archknow/*"], "contributes": {"commands": [{"command": "archknow.installMcpServer", "title": "ArchKnow: Install MCP Server for Cursor", "category": "ArchKnow"}, {"command": "archknow.validateApiKey", "title": "ArchKnow: Validate API Key"}, {"command": "archknow.reviewMilestone", "category": "ArchKnow", "title": "Review Milestone Changes"}], "menus": {"explorer/context": [{"command": "archknow.showContextForFile", "when": "!explorerResourceIsFolder", "group": "archknow"}], "editor/context": [{"command": "archknow.openDesignDoc", "when": "editorLangId == 'markdown'", "group": "archknow"}, {"command": "archknow.showContextForFile", "when": "editorTextFocus", "group": "archknow"}], "editor/title": [{"command": "archknow.openDesignDoc", "when": "editorLangId == 'markdown'", "group": "navigation"}]}, "keybindings": [{"command": "archknow.openDesignDoc", "key": "alt+d", "mac": "alt+d", "when": "editorLangId == 'markdown'"}], "configuration": {"title": "ArchKnow", "properties": {"archknow.apiUrl": {"type": "string", "default": "https://localhost:3000", "description": "The URL of the ArchKnow API server"}, "archknow.apiKey": {"type": "string", "description": "Your ArchKnow API key"}, "archknow.githubHandle": {"type": "string", "description": "Your GitHub username"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./ && (cd mcp_server && npm install && npm run build)", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "vscode-test"}, "devDependencies": {"@types/express": "^4.17.21", "@types/js-yaml": "^4.0.9", "@types/mocha": "^10.0.6", "@types/node": "^20.11.1", "@types/node-fetch": "^2.6.13", "@types/uuid": "^10.0.0", "@types/vscode": "^1.87.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.0", "eslint": "^8.57.0", "typescript": "^5.4.5"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "@types/highlight.js": "^9.12.4", "csv-parse": "^5.5.6", "express": "^4.17.21", "gray-matter": "^4.0.3", "highlight.js": "^11.11.1", "marked": "^7.0.5", "node-fetch": "^2.7.0", "simple-git": "^3.24.0", "uuid": "^11.1.0", "zod": "^3.25.67"}}