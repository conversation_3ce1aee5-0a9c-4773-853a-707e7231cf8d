#!/usr/bin/env node

import { program } from 'commander';
import dotenv from 'dotenv';
import { getPR, getPRFiles, getPRComments, getRecentPRs } from '../src/analyzer/github.js';
import { processMergedPR, exportProcessedPRsCSV } from '../src/orchestrator.js';
import { generateADR } from '../src/index.js';
import { logger } from '../src/utils/logger.js';
import fs from 'fs-extra';
import path from 'path';
import { Octokit } from '@octokit/rest';
import { testDecisionSimilarity, findSimilarDecisions } from '../src/orchestrator.js';

// Load environment variables
dotenv.config();

// Function to get processed PR log file path for a repository
function getProcessedPRLogPath(owner, repo) {
  const dataDir = path.resolve(process.cwd(), '.archknow-data');
  // Ensure the data directory exists
  fs.ensureDirSync(dataDir);
  return path.resolve(dataDir, `${owner}_${repo}_processed_prs.json`);
}

// Function to load the set of already processed PRs
function loadProcessedPRs(owner, repo) {
  const logPath = getProcessedPRLogPath(owner, repo);
  try {
    if (fs.existsSync(logPath)) {
      const data = fs.readFileSync(logPath, 'utf8');
      const processed = JSON.parse(data);
      logger.info(`Loaded ${Object.keys(processed).length} previously processed PRs from log`);
      return processed;
    }
  } catch (error) {
    logger.warn(`Error loading processed PR log: ${error.message}`);
  }
  // Return empty object if file doesn't exist or can't be read
  return {};
}

// Function to save processed PRs to the log file
function saveProcessedPR(owner, repo, prNumber, status = 'success', metadata = {}) {
  const logPath = getProcessedPRLogPath(owner, repo);
  try {
    // Load existing data
    let processed = {};
    if (fs.existsSync(logPath)) {
      const data = fs.readFileSync(logPath, 'utf8');
      processed = JSON.parse(data);
    }
    
    // Add the new PR
    processed[prNumber] = {
      status,
      processed_at: new Date().toISOString(),
      ...metadata
    };
    
    // Save back to file
    fs.writeFileSync(logPath, JSON.stringify(processed, null, 2), 'utf8');
    logger.debug(`Updated processed PR log for PR #${prNumber}`);
    return true;
  } catch (error) {
    logger.warn(`Error saving to processed PR log: ${error.message}`);
    return false;
  }
}

// Check for required environment variables
const requiredEnvVars = ['GITHUB_TOKEN', 'ANTHROPIC_API_KEY'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  logger.error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
  logger.info('Create a .env file or set these environment variables before running the tool.');
  process.exit(1);
}

/**
 * Validate date string
 * @param {string} dateStr - Date string to validate
 * @returns {string} - Valid ISO date string
 */
function validateDate(dateStr) {
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) {
    throw new Error(`Invalid date format: "${dateStr}". Please use YYYY-MM-DD or a valid ISO 8601 format.`);
  }
  return date.toISOString();
}

/**
 * Get PRs for analysis with options for oldest first
 * @param {string} owner - Repository owner
 * @param {string} repo - Repository name
 * @param {Object} options - Options for fetching PRs
 * @returns {Promise<any[]>} - Filtered PRs
 */
async function getPRsForAnalysis(owner, repo, options) {
  try {
    // Common validation for date filters
    let olderThan = null;
    let newerThan = null;

    if (options.olderThan) {
      olderThan = validateDate(options.olderThan);
      logger.info(`Filtering for PRs merged before: ${new Date(olderThan).toLocaleString()}`);
    }

    if (options.newerThan) {
      newerThan = validateDate(options.newerThan);
      logger.info(`Filtering for PRs merged after: ${new Date(newerThan).toLocaleString()}`);
    }

    // Fetch PRs using the GitHub helper
    const prs = await getRecentPRs(owner, repo, {
      count: options.count,
      olderThan,
      newerThan,
    });

    return prs;
  } catch (error) {
    logger.error('Error fetching PRs for analysis:', error);
    throw error;
  }
}

/**
 * Process a batch of PRs
 * @param {string} owner - Repository owner
 * @param {string} repo - Repository name
 * @param {Array} prs - Array of PR objects to process
 * @param {Object} options - Options for processing
 * @param {boolean} options.skipProcessed - Whether to skip already processed PRs (default: true)
 * @param {boolean} options.forceReprocess - Force reprocessing even if PR was already processed (default: false)
 * @returns {Promise<any>} - Stats about processed PRs
 */
async function processPRBatch(owner, repo, prs, options = { skipProcessed: true, forceReprocess: false }) {
  const { skipProcessed = true, forceReprocess = false } = options;
  let processedCount = prs.length;
  let successCount = 0;
  let failedCount = 0;
  let skippedCount = 0;
  const failedPRs = [];
  const skippedPRs = [];

  // Load the set of already processed PRs
  const processedPRs = skipProcessed ? loadProcessedPRs(owner, repo) : {};
  
  // Loop through PRs and call the orchestrator
  for (const prSummary of prs) {
    const prNumber = prSummary.number;
    
    // Check if this PR has already been processed
    if (skipProcessed && !forceReprocess && processedPRs[prNumber]) {
      logger.info(`--- Skipping PR #${prNumber}: Already processed on ${new Date(processedPRs[prNumber].processed_at).toLocaleString()} ---`);
      skippedCount++;
      skippedPRs.push(prNumber);
      processedCount--; // Don't count as processed in this batch
      continue;
    }
    
    logger.info(`--- Processing PR #${prNumber}: ${prSummary.title} ---`);
    try {
      // 1. Fetch full PR context
      logger.debug(`[PR #${prNumber}] Fetching PR details...`);
      const prContext = await getPR(owner, repo, prNumber);

      // Skip if not merged
      if (!prContext.merged) {
        logger.warn(`[PR #${prNumber}] Skipping as it is not merged.`);
        processedCount--; // Adjust count as we didn't fully process
        continue;
      }

      // 2. Fetch files/code changes
      logger.debug(`[PR #${prNumber}] Fetching files and changes...`);
      const codeChanges = await getPRFiles(owner, repo, prNumber);

      // 3. Fetch comments
      logger.debug(`[PR #${prNumber}] Fetching comments...`);
      const comments = await getPRComments(owner, repo, prNumber);

      // 4. Call the main orchestrator function
      logger.info(`[PR #${prNumber}] Starting analysis via orchestrator...`);
      await processMergedPR(prContext, codeChanges, comments);
      logger.success(`[PR #${prNumber}] Successfully processed and stored knowledge.`);
      
      // Record this PR as processed
      saveProcessedPR(owner, repo, prNumber, 'success', {
        title: prContext.title,
        merged_at: prContext.merged_at
      });
      
      successCount++;

    } catch (error) {
      logger.error(`[PR #${prNumber}] Failed to process:`, error.message || error);
      if (process.env.DEBUG === 'true') {
          console.error(error); // Log full stack trace in debug
      }
      
      // Record this PR as failed
      saveProcessedPR(owner, repo, prNumber, 'failed', {
        title: prSummary.title,
        merged_at: prSummary.merged_at,
        error: error.message || String(error)
      });
      
      failedCount++;
      failedPRs.push(prNumber);
    }
    logger.info(`--- Finished processing PR #${prNumber} ---`);
  }

  return { processedCount, successCount, failedCount, skippedCount, failedPRs, skippedPRs };
}

// Function to fetch PRs with pagination and early stopping when enough filtered PRs are found
async function fetchFilteredPRs(octokit, owner, repo, options = {}) {
  const {
    sort = 'created',
    direction = 'desc',
    state = 'closed',
    targetCount = 0,            // How many PRs we want after filtering
    minPR = undefined,          // Minimum PR number filter
    maxPR = undefined,          // Maximum PR number filter
    olderThan = undefined,      // Date filters
    newerThan = undefined,
    fetchLimit = 1000           // Max PRs to fetch (10 pages of 100)
  } = options;
  
  // Calculate max pages based on fetchLimit
  const maxPages = Math.min(Math.ceil(fetchLimit / 100), 10);
  
  logger.info(`Fetching PRs from ${owner}/${repo} with optimized pagination (max: ${fetchLimit} PRs, ${maxPages} pages)...`);
  
  let allPRs = [];
  let filteredPRs = [];
  let page = 1;
  let hasMorePages = true;
  const perPage = 100; // Maximum allowed by GitHub
  
  // For early stopping - track if we have enough PRs
  const hasFilters = minPR !== undefined || maxPR !== undefined || olderThan !== undefined || newerThan !== undefined;
  const shouldEarlyStop = targetCount > 0 && hasFilters;
  
  if (shouldEarlyStop) {
    logger.info(`Using early stopping - will stop fetching once we have ${targetCount} PRs matching filters`);
  }
  
  while (hasMorePages) {
    logger.info(`Fetching page ${page} of PRs (${perPage} PRs per page)...`);
    
    const { data: prs } = await octokit.pulls.list({
      owner,
      repo,
      state,
      sort,
      direction,
      per_page: perPage,
      page
    });
    
    if (prs.length === 0) {
      hasMorePages = false;
      logger.info('No more PRs returned by GitHub API');
    } else {
      // First, filter to only merged PRs
      const mergedPRs = prs.filter(pr => pr.merged_at !== null);
      
      // Add to all PRs
      allPRs = allPRs.concat(mergedPRs);
      
      // Apply filters for early stopping if needed
      if (shouldEarlyStop) {
        let currentPageFiltered = [...mergedPRs];
        
        // Apply number filters
        if (minPR !== undefined) {
          currentPageFiltered = currentPageFiltered.filter(pr => pr.number >= minPR);
        }
        
        if (maxPR !== undefined) {
          currentPageFiltered = currentPageFiltered.filter(pr => pr.number <= maxPR);
        }
        
        // Apply date filters
        if (olderThan) {
          currentPageFiltered = currentPageFiltered.filter(pr => new Date(pr.merged_at) < new Date(olderThan));
        }
        
        if (newerThan) {
          currentPageFiltered = currentPageFiltered.filter(pr => new Date(pr.merged_at) > new Date(newerThan));
        }
        
        // Add filtered PRs to our running list
        filteredPRs = filteredPRs.concat(currentPageFiltered);
        
        // Check if we have enough PRs after filtering
        if (filteredPRs.length >= targetCount) {
          logger.info(`Early stopping - found ${filteredPRs.length} matching PRs after ${page} pages (target: ${targetCount})`);
          hasMorePages = false;
        }
      }
      
      logger.info(`Retrieved ${mergedPRs.length} merged PRs from page ${page}, total so far: ${allPRs.length}`);
      page++;
      
      // Stop if we've fetched enough pages based on fetchLimit
      if (page > maxPages) {
        logger.info(`Reached page limit of ${maxPages} pages (${fetchLimit} PRs). Use --fetch-limit to adjust.`);
        hasMorePages = false;
      }
    }
  }
  
  logger.info(`Finished fetching ${allPRs.length} total merged PRs from GitHub (${page-1} pages)`);
  
  // Sort by merged_at date to ensure chronological order
  allPRs.sort((a, b) => {
    if (direction === 'asc') {
      // Oldest first
      return new Date(a.merged_at).getTime() - new Date(b.merged_at).getTime();
    } else {
      // Newest first
      return new Date(b.merged_at).getTime() - new Date(a.merged_at).getTime();
    }
  });
  
  logger.info(`PRs sorted chronologically by merge date (${direction === 'asc' ? 'oldest' : 'newest'} first)`);
  
  return allPRs;
}

program
  .name('archknow')
  .description('Extract architectural knowledge from GitHub PRs')
  .version('0.1.0');

program
  .command('analyze')
  .description('Analyze a specific PR and extract/store architectural knowledge via orchestrator')
  .argument('<repo>', 'GitHub repository in format owner/repo')
  .option('-p, --pr <number>', 'Pull request number to analyze', parseInt)
  .option('--skip-processed', 'Skip PR if it has already been processed (default: false)', false)
  .option('--force-reprocess', 'Force reprocessing of PR even if it was already processed', false)
  .action(async (repo, options) => {
    // Declare variables here to ensure they are available in the catch block
    let owner, repoName, prNumber;
    try {
      [owner, repoName] = repo.split('/');
      
      if (!owner || !repoName) {
        logger.error('Invalid repository format. Use owner/repo format.');
        process.exit(1);
      }

      // Assign prNumber after validation
      prNumber = options.pr;

      logger.info(`--- Processing PR #${prNumber} from ${repo} ---`);

      // Check if this PR has already been processed
      if (options.skipProcessed) {
        const processedPRs = loadProcessedPRs(owner, repoName);
        if (processedPRs[prNumber] && !options.forceReprocess) {
          logger.info(`Skipping PR #${prNumber}: Already processed on ${new Date(processedPRs[prNumber].processed_at).toLocaleString()}`);
          logger.info(`Use --force-reprocess to reprocess this PR`);
          return;
        }
      }

      // 1. Fetch full PR context
      logger.debug(`[PR #${prNumber}] Fetching PR details...`);
      const prContext = await getPR(owner, repoName, prNumber);

      // Skip if not merged 
      if (!prContext.merged) {
          logger.warn(`[PR #${prNumber}] Skipping as it is not merged.`);
          logger.info(`--- Finished processing PR #${prNumber} (Skipped Not Merged) ---`);
          return; // Stop if not merged
      }

      // 2. Fetch files/code changes
      logger.debug(`[PR #${prNumber}] Fetching files and changes...`);
      const codeChanges = await getPRFiles(owner, repoName, prNumber);

      // 3. Fetch comments
      logger.debug(`[PR #${prNumber}] Fetching comments...`);
      const comments = await getPRComments(owner, repoName, prNumber);

      // 4. Call the main orchestrator function
      logger.info(`[PR #${prNumber}] Starting analysis via orchestrator...`);
      await processMergedPR(prContext, codeChanges, comments);
      logger.success(`[PR #${prNumber}] Successfully processed and stored knowledge.`);
      
      // Record this PR as processed
      saveProcessedPR(owner, repoName, prNumber, 'success', {
        title: prContext.title,
        merged_at: prContext.merged_at
      });
      
      logger.info(`--- Finished processing PR #${prNumber} ---`);

    } catch (error) {
      // Now owner, repoName, prNumber are accessible
      logger.error(`[PR #${options.pr || 'Unknown'}] Failed to process:`, error.message || error);
      
      // Record this PR as failed - use the declared variables
      if (owner && repoName && prNumber) { // Check if they were successfully assigned
          saveProcessedPR(owner, repoName, prNumber, 'failed', {
            error: error.message || String(error)
          });
      } else {
           logger.error("Could not save failure status: owner, repoName or prNumber was not correctly assigned before the error.");
      }
      
      if (process.env.DEBUG === 'true') {
          console.error(error); 
      }
      process.exit(1);
    }
  });

program
  .command('analyze-recent')
  .description('Analyze recent PRs and extract/store architectural knowledge via orchestrator')
  .argument('<repo>', 'GitHub repository in format owner/repo')
  .option('-c, --count <number>', 'Number of recent PRs to analyze', (value) => {
    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
      throw new Error(`Invalid count value: ${value}. Please provide a valid number.`);
    }
    return parsed;
  }, 5)
  .option('--older-than <date>', 'Analyze PRs merged before this date (YYYY-MM-DD or ISO format)')
  .option('--newer-than <date>', 'Analyze PRs merged after this date (YYYY-MM-DD or ISO format)')
  .option('--pr <number>', 'Analyze a specific PR number instead of recent ones', parseInt)
  .option('--min-pr <number>', 'Analyze PRs with number >= this value', parseInt)
  .option('--max-pr <number>', 'Analyze PRs with number <= this value', parseInt)
  .option('--fetch-limit <number>', 'Maximum number of PRs to fetch from GitHub API', parseInt, 1000)
  .option('--skip-processed', 'Skip PR if it has already been processed (default: false)', false)
  .option('--force-reprocess', 'Force reprocessing of PR even if it was already processed', false)
  .action(async (repo, options) => {
    try {
      const [owner, repoName] = repo.split('/');

      if (!owner || !repoName) {
        logger.error('Invalid repository format. Use owner/repo format.');
        process.exit(1);
      }

      // Log warning about PR number filter behavior  
      if (options.minPr !== undefined || options.maxPr !== undefined) {
        logger.info(`Note: PR numbers are used only for filtering, not for determining chronological order`);
        
        if (options.minPr !== undefined) {
          logger.info(`  - Will include only PRs with numbers >= ${options.minPr}`);
        }
        
        if (options.maxPr !== undefined) {
          logger.info(`  - Will include only PRs with numbers <= ${options.maxPr}`);
        }
        
        logger.info(`PRs will still be processed in chronological order by merge date`);
      }

      // Create a new Octokit instance
      const octokit = new Octokit({
        auth: process.env.GITHUB_TOKEN
      });
      
      logger.info(`Querying GitHub API for recent PRs in ${owner}/${repoName}...`);
      
      // Determine the best sort direction based on filters
      let sortDirection = 'desc'; // Default to newest first for this command
      
      // For analyze-recent command, we always want newest PRs by date
      // PR number filters are just for filtering, not for determining sort order
      logger.info(`Sorting PRs by update date (newest first)`);
      
      // Query PRs for recent PRs using pagination with the appropriate direction
      const prs = await fetchFilteredPRs(octokit, owner, repoName, {
        sort: 'updated', // Sort by update date
        direction: sortDirection,
        targetCount: options.count, // How many PRs we want after filtering
        minPR: options.minPr,
        maxPR: options.maxPr,
        olderThan: options.olderThan ? validateDate(options.olderThan) : undefined,
        newerThan: options.newerThan ? validateDate(options.newerThan) : undefined,
        fetchLimit: options.fetchLimit
      });
      
      logger.info(`GitHub API returned ${prs.length} total PRs for ${owner}/${repoName}`);
      
      // Filter for merged PRs
      let mergedPRs = prs.filter(pr => pr.merged_at !== null);
      logger.info(`Found ${mergedPRs.length} merged PRs out of ${prs.length} total PRs`);
      
      // Apply date filters if specified
      if (options.olderThan) {
        const olderThanDate = new Date(validateDate(options.olderThan));
        const countBefore = mergedPRs.length;
        mergedPRs = mergedPRs.filter(pr => new Date(pr.merged_at) < olderThanDate);
        logger.info(`Filtering for PRs merged before: ${olderThanDate.toLocaleString()}, filtered out ${countBefore - mergedPRs.length} PRs`);
      }
      
      if (options.newerThan) {
        const newerThanDate = new Date(validateDate(options.newerThan));
        const countBefore = mergedPRs.length;
        mergedPRs = mergedPRs.filter(pr => new Date(pr.merged_at) > newerThanDate);
        logger.info(`Filtering for PRs merged after: ${newerThanDate.toLocaleString()}, filtered out ${countBefore - mergedPRs.length} PRs`);
      }
      
      // Apply PR number filters BEFORE limiting by count
      if (options.minPr !== undefined) {
        const countBefore = mergedPRs.length;
        mergedPRs = mergedPRs.filter(pr => pr.number >= options.minPr);
        logger.info(`Filtering for PRs with number >= ${options.minPr}, filtered out ${countBefore - mergedPRs.length} PRs`);
      }
      
      if (options.maxPr !== undefined) {
        const countBefore = mergedPRs.length;
        mergedPRs = mergedPRs.filter(pr => pr.number <= options.maxPr);
        logger.info(`Filtering for PRs with number <= ${options.maxPr}, filtered out ${countBefore - mergedPRs.length} PRs`);
      }
      
      // Get the requested number of PRs after filtering
      const filteredPRs = mergedPRs.slice(0, options.count).map(pr => ({
        number: pr.number,
        title: pr.title,
        merged_at: pr.merged_at
      }));
      
      logger.info(`Found ${filteredPRs.length} recent PRs to analyze.`);
      
      if (filteredPRs.length === 0) {
        logger.warn('No PRs remaining after filtering. Try adjusting your filter criteria.');
        return;
      }

      // We'll process PRs in the date order they were returned by the API
      // This means newest first for analyze-recent
      logger.info('PRs will be processed from newest to oldest by merge date.');

      // Process PRs
      const { processedCount, successCount, failedCount, skippedCount, failedPRs, skippedPRs } = 
        await processPRBatch(owner, repoName, filteredPRs, {
          skipProcessed: options.skipProcessed,
          forceReprocess: options.forceReprocess
        });

      // Final Summary
      logger.info('-------------------- Analysis Summary --------------------');
      logger.info(`Total recent PRs identified for analysis: ${filteredPRs.length}`);
      if (skippedCount > 0) {
        logger.info(`PRs skipped (already processed): ${skippedCount} (${skippedPRs.join(', ')})`);
      }
      if (processedCount !== filteredPRs.length - skippedCount) {
        logger.info(`Total PRs processed (e.g., excluding non-merged): ${processedCount}`);
      }
      logger.success(`Successfully processed: ${successCount}`);
      if (failedCount > 0) {
        logger.error(`Failed to process: ${failedCount} (PRs: ${failedPRs.join(', ')})`);
      } else {
        logger.info('Failed to process: 0');
      }
      logger.info('----------------------------------------------------------');

    } catch (error) {
      logger.error('An unexpected error occurred during recent PR analysis:', error);
      process.exit(1);
    }
  });

program
  .command('analyze-oldest')
  .description('Analyze oldest PRs to build knowledge base incrementally')
  .argument('<repo>', 'GitHub repository in format owner/repo')
  .option('-c, --count <number>', 'Number of oldest PRs to analyze', (value) => {
    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
      throw new Error(`Invalid count value: ${value}. Please provide a valid number.`);
    }
    return parsed;
  }, 5)
  .option('--older-than <date>', 'Analyze PRs merged before this date (YYYY-MM-DD or ISO format)')
  .option('--newer-than <date>', 'Analyze PRs merged after this date (YYYY-MM-DD or ISO format)')
  .option('--chronological', 'Process PRs in chronological order (oldest first)', true)
  .option('--reverse-order', 'Process PRs in reverse chronological order (newest first)', false)
  .option('--min-pr <number>', 'Analyze PRs with number >= this value', parseInt)
  .option('--max-pr <number>', 'Analyze PRs with number <= this value', parseInt)
  .option('--fetch-limit <number>', 'Maximum number of PRs to fetch from GitHub API', parseInt, 1000)
  .option('--skip-processed', 'Skip PRs that have already been processed (default: true)', true)
  .option('--force-reprocess', 'Force reprocessing of PRs even if they were already processed', false)
  .action(async (repo, options) => {
    try {
      const [owner, repoName] = repo.split('/');

      if (!owner || !repoName) {
        logger.error('Invalid repository format. Use owner/repo format.');
        process.exit(1);
      }

      // Get PRs for analysis using the helper function
      logger.info(`Fetching up to ${options.count} oldest PRs from ${repo}...`);
      
      // Log warning about PR number filter behavior
      if (options.minPr !== undefined || options.maxPr !== undefined) {
        logger.info(`Note: PR numbers are used only for filtering, not for determining chronological order`);
        
        if (options.minPr !== undefined) {
          logger.info(`  - Will include only PRs with numbers >= ${options.minPr}`);
        }
        
        if (options.maxPr !== undefined) {
          logger.info(`  - Will include only PRs with numbers <= ${options.maxPr}`);
        }
        
        logger.info(`PRs will still be processed in chronological order by merge date`);
      }

      // Create a new Octokit instance
      const octokit = new Octokit({
        auth: process.env.GITHUB_TOKEN
      });
      
      logger.info(`Querying GitHub API for PRs in ${owner}/${repoName}...`);
      
      // Determine the best sort direction based on filters
      let sortDirection = 'asc'; // Default to oldest first for this command
      
      // For oldest command, we always want to sort by date (oldest or newest)
      // PR number filters are just for filtering, not for determining sort order
      logger.info(`Sorting PRs by creation date (${sortDirection === 'asc' ? 'oldest' : 'newest'} first)`);
      
      // Query PRs with 'created' sorting, using the appropriate direction
      const prs = await fetchFilteredPRs(octokit, owner, repoName, {
        sort: 'created',
        direction: sortDirection,
        targetCount: options.count, // How many PRs we want after filtering
        minPR: options.minPr,
        maxPR: options.maxPr,
        olderThan: options.olderThan ? validateDate(options.olderThan) : undefined,
        newerThan: options.newerThan ? validateDate(options.newerThan) : undefined,
        fetchLimit: options.fetchLimit
      });
      
      logger.info(`GitHub API returned ${prs.length} total PRs for ${owner}/${repoName}`);
      
      // Filter for merged PRs
      let mergedPRs = prs.filter(pr => pr.merged_at !== null);
      logger.info(`Found ${mergedPRs.length} merged PRs out of ${prs.length} total PRs`);
      
      // Log PR numbers for debugging
      if (mergedPRs.length > 0) {
        const prNumbers = mergedPRs.map(pr => pr.number).slice(0, 10);
        logger.info(`Sample PR numbers (first 10): ${prNumbers.join(', ')}${mergedPRs.length > 10 ? '...' : ''}`);
      } else {
        logger.warn('No merged PRs found in this repository');
        return;  // Exit early if no PRs found
      }
      
      // Apply date filters if specified
      if (options.olderThan) {
        const olderThanDate = new Date(validateDate(options.olderThan));
        const countBefore = mergedPRs.length;
        mergedPRs = mergedPRs.filter(pr => new Date(pr.merged_at) < olderThanDate);
        logger.info(`Filtering for PRs merged before: ${olderThanDate.toLocaleString()}, filtered out ${countBefore - mergedPRs.length} PRs`);
      }
      
      if (options.newerThan) {
        const newerThanDate = new Date(validateDate(options.newerThan));
        const countBefore = mergedPRs.length;
        mergedPRs = mergedPRs.filter(pr => new Date(pr.merged_at) > newerThanDate);
        logger.info(`Filtering for PRs merged after: ${newerThanDate.toLocaleString()}, filtered out ${countBefore - mergedPRs.length} PRs`);
      }
      
      // Apply PR number filters BEFORE slicing by count
      if (options.minPr !== undefined) {
        const countBefore = mergedPRs.length;
        mergedPRs = mergedPRs.filter(pr => pr.number >= options.minPr);
        logger.info(`Filtering for PRs with number >= ${options.minPr}, filtered out ${countBefore - mergedPRs.length} PRs`);
      }
      
      if (options.maxPr !== undefined) {
        const countBefore = mergedPRs.length;
        mergedPRs = mergedPRs.filter(pr => pr.number <= options.maxPr);
        logger.info(`Filtering for PRs with number <= ${options.maxPr}, filtered out ${countBefore - mergedPRs.length} PRs`);
      }
      
      // Only NOW get the requested number of PRs (after all filtering)
      const prsToProcess = mergedPRs.slice(0, options.count).map(pr => ({
        number: pr.number,
        title: pr.title,
        merged_at: pr.merged_at
      }));
      
      logger.info(`Found ${prsToProcess.length} oldest PRs to analyze.`);
      
      if (prsToProcess.length === 0) {
        logger.warn('No PRs remaining after filtering. Try adjusting your filter criteria.');
        return;
      }

      // Sort PRs by merge date, not PR number
      let filteredPRs = [...prsToProcess];
      
      // Sort chronologically based on flags
      if (options.reverseOrder) {
        // Reverse to process newest of the oldest first
        filteredPRs.reverse();
        logger.info('PRs will be processed from newest to oldest within the selected batch.');
      } else if (!options.chronological) {
        // For backward compatibility - this condition will rarely be hit now that chronological defaults to true
        filteredPRs.reverse();
        logger.info('PRs will be processed from newest to oldest within the selected batch.');
      } else {
        logger.info('PRs will be processed chronologically (oldest first) within the selected batch.');
      }

      // Process PRs using the common function
      const { processedCount, successCount, failedCount, skippedCount, failedPRs, skippedPRs } = 
        await processPRBatch(owner, repoName, filteredPRs, {
          skipProcessed: options.skipProcessed,
          forceReprocess: options.forceReprocess
        });

      // Final Summary
      logger.info('-------------------- Analysis Summary --------------------');
      logger.info(`Total oldest PRs identified for analysis: ${filteredPRs.length}`);
      if (skippedCount > 0) {
        logger.info(`PRs skipped (already processed): ${skippedCount} (${skippedPRs.join(', ')})`);
      }
      if (processedCount !== filteredPRs.length - skippedCount) {
        logger.info(`Total PRs processed (e.g., excluding non-merged): ${processedCount}`);
      }
      logger.success(`Successfully processed: ${successCount}`);
      if (failedCount > 0) {
        logger.error(`Failed to process: ${failedCount} (PRs: ${failedPRs.join(', ')})`);
      } else {
        logger.info('Failed to process: 0');
      }
      logger.info('----------------------------------------------------------');

    } catch (error) {
      logger.error('An unexpected error occurred during oldest PR analysis:', error);
      process.exit(1);
    }
  });

program.parse(process.argv);
