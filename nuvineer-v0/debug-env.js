// Simple script to debug environment variable loading
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import fs from 'fs';

// Get the directory of the current module and resolve path to project root
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = process.cwd();

console.log('Current working directory:', process.cwd());
console.log('Project root (based on script location):', projectRoot);

// Check if .env file exists
const envPath = resolve(projectRoot, '.env');
console.log('.env file path:', envPath);
console.log('.env file exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  console.log('.env file content:');
  const content = fs.readFileSync(envPath, 'utf8')
    .split('\n')
    .map(line => {
      // Mask API keys for security
      if (line.includes('_KEY=') || line.includes('TOKEN=')) {
        const parts = line.split('=');
        if (parts.length >= 2) {
          const value = parts[1];
          if (value.length > 8) {
            return `${parts[0]}=${value.substring(0, 4)}...${value.substring(value.length - 4)}`;
          }
        }
      }
      return line;
    })
    .join('\n');
  console.log(content);
}

// Load environment variables
const result = dotenv.config({ path: envPath });
console.log('dotenv.config() result:', result.error ? 'Error: ' + result.error.message : 'Success');

// Check for required environment variables
const requiredVars = [
  'OPENAI_API_KEY',
  'ANTHROPIC_API_KEY',
  'PINECONE_API_KEY',
  'PINECONE_ENVIRONMENT',
  'PINECONE_INDEX_NAME',
  'GITHUB_TOKEN',
];

console.log('\nEnvironment Variables:');
for (const varName of requiredVars) {
  const value = process.env[varName];
  const maskedValue = value 
    ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}`
    : 'Not set';
  console.log(`${varName}: ${value ? maskedValue : 'Not set'} (length: ${value?.length || 0})`);
}

console.log('\nAll environment variables:');
console.log(Object.keys(process.env)
  .filter(key => !key.startsWith('npm_'))
  .sort()
  .join(', ')); 