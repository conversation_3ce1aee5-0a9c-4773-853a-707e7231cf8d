# GitHub App Setup

This guide explains how to create and configure the GitHub App used by the application, including required permissions, webhook events, and environment variables.

## Overview
- The app uses a GitHub App for authenticated access to private repositories and to receive webhooks.
- Webhooks drive analysis when pull requests are merged or commits are pushed to the main branch.
- The backend authenticates as the installation to fetch PR data, files, comments, and repository contents.

## Existing App Instance
- A live GitHub App instance named "aiguardrails" exists under the `jabubaker` GitHub account. Manage it here: `https://github.com/settings/apps/aiguardrails`.
- You can install this app on repositories you want analyzed. Ensure permissions and webhooks match the configuration below.

## Prerequisites
- Your deployment base URL (e.g., `NEXT_PUBLIC_BASE_URL`): `https://your-app.example.com`
- Admin access to create a GitHub App in your user or organization account

## Create the GitHub App
1. Go to `https://github.com/settings/apps/new` (or the org-level Apps page) and create a new GitHub App.
2. Fill in basic fields:
   - Name: a unique name for your app
   - Homepage URL: your `NEXT_PUBLIC_BASE_URL`
   - Webhook URL: `https://<your-domain>/api/webhooks/github`
   - Webhook secret: generate a strong secret and save it (you will set `GITHUB_WEBHOOK_SECRET` to this value)
   - Where can this GitHub App be installed?: Any account
3. Save the app.

## Permissions
Set the following Repository permissions:
- Metadata: Read-only
- Contents: Read-only
- Pull requests: Read-only
- Issues: Read and write (needed to create comments and add labels)

No Organization permissions are required.

## Subscribe to Webhook Events
Enable these events:
- Pull request (used for `pull_request.closed` when merged)
- Push (used to process direct commits/squash merges to the main branch)

The application verifies signatures using the `x-hub-signature-256` header and your `GITHUB_WEBHOOK_SECRET`.

Webhook handler endpoint:
- `POST /api/webhooks/github`

## Generate App Credentials
1. On the GitHub App page, click “Generate a private key” and download the PEM file.
2. Note the App ID shown on the App page.

## Install the App
- Click “Install App” and select the repositories the application should access.
- You can install at the org level or on individual repositories.

## Configure Environment Variables
Set the following in your deployment environment:
- `GITHUB_APP_ID`: The numeric GitHub App ID.
- `GITHUB_APP_PRIVATE_KEY`: The PEM contents of the private key. Preserve newlines. If storing in a single-line env var, replace newlines with `\n`.
- `GITHUB_WEBHOOK_SECRET`: The webhook secret configured on the GitHub App.
- `GITHUB_TOKEN` (optional): A personal access token used for public repositories when no installation context is available.

Other relevant variables referenced by the webhook handler and background jobs:
- `NEXT_PUBLIC_SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`
- `MAIN_BRANCH_NAME` (defaults to `main`)

## What the App Does
The application uses the installation token to:
- Read PR metadata and files (`pulls.get`, `pulls.listFiles`)
- Read PR comments (`issues.listComments`)
- Read repository contents (`repos.getContent`)
- Optionally post issue comments and labels (`issues.createComment`, `issues.addLabels`)

Webhook processing handles:
- `pull_request.closed` when merged: queues the PR for analysis
- `push` events targeting the main branch: queues distinct commits as pseudo-PRs if they are not standard merge commits

## Validation Checklist
- App installed on target repositories
- Permissions exactly as listed above
- Webhook URL set to `/api/webhooks/github` and secret configured
- `GITHUB_APP_ID`, `GITHUB_APP_PRIVATE_KEY`, and `GITHUB_WEBHOOK_SECRET` set in your environment

## Troubleshooting
- 401/403 from GitHub when fetching repo data: verify installation exists for the repo, correct permissions, and that the installation token is used.
- Webhook signature verification failing: ensure `GITHUB_WEBHOOK_SECRET` matches the App’s webhook secret and that your platform provides raw request bodies to the handler.
- Missing or truncated private key: ensure newlines are preserved. If stored with `\n`, code converts to newlines before use. 