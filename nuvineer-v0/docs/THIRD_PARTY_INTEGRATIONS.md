# Third-Party Service Integrations

This document provides a detailed overview of how the ArchKnow application integrates with its key third-party services.

## 1. GitHub

GitHub is the primary data source and a core part of the authentication flow. The integration is managed through a GitHub App.

- **Setup**: See `docs/GITHUB_APP_SETUP.md` for creating the GitHub App, permissions, webhook events, and required environment variables.
- **Existing App**: The production GitHub App instance is `aiguardrails` under the `jabubaker` account. Manage it at `https://github.com/settings/apps/aiguardrails`.

- **Authentication**: The user authentication flow is built on top of GitHub OAuth. The application uses the `@supabase/ssr` library in combination with Next.js API routes to handle the OAuth callback and session management. The main logic for this can be found in `src/app/auth/callback/route.ts`.

- **Data Access**: The application uses the `Octokit` library to interact with the GitHub API. It uses both app-level and installation-level authentication to fetch repository data, such as pull requests, commits, and file contents. The `getInstallationOctokit` function in `src/lib/github.ts` is used to create an Octokit client that is authenticated for a specific installation of the GitHub App.

- **Webhooks**: The application receives real-time updates from GitHub via webhooks. The endpoint for handling these webhooks is located at `src/app/api/webhooks/github/route.ts`. This is used to trigger analyses when new pull requests are merged and when commits are pushed to the main branch.

## 2. Supabase

Supabase is used as the primary backend-as-a-service provider, handling the Postgres database and user authentication.

- **Database**: The application uses Supabase's Postgres database to store all of its relational data. The schema is defined in the `sql/migrations/` directory.

- **Client Initialization**: The application uses two different Supabase clients:
    -   A **server-side client** (`src/lib/supabase-server.ts`) is used in Next.js Server Components and API routes. It uses a service role key to bypass row-level security (RLS) when necessary for administrative tasks.
    -   A **client-side client** (`src/lib/supabase-client.ts`) is used in the browser. It is authenticated with the user's session and is subject to RLS policies.

- **Authentication**: Supabase Auth is used to manage user sessions. It integrates directly with the GitHub OAuth flow.

## 3. Pinecone

Pinecone is used as the vector database for storing and querying embeddings for semantic search.

- **Client Initialization**: The Pinecone client is initialized in `src/orchestrator.js` using the `PINECONE_API_KEY` environment variable.

- **Data Storage**: When a decision is processed, a text embedding is generated using an LLM (see below), and the resulting vector is "upserted" into a Pinecone index. The `storeDecisionRecord` function in `orchestrator.js` handles this.

- **Data Querying**: To find related decisions, the application generates an embedding for a query and uses the Pinecone API to find the most similar vectors. This is handled by the `queryRAG` function in `orchestrator.js`.

- **Namespaces**: The Pinecone index is organized into namespaces, with each repository having its own namespace. This ensures that searches are scoped to the correct repository.

## 4. Large Language Models (LLMs)

The application uses both Anthropic and OpenAI models for different tasks.

-   **Anthropic (Claude)**: Used for generative tasks, such as extracting architectural decisions from pull requests and generating human-readable text. The `callLLM` function in `orchestrator.js` is the primary interface for this.

-   **OpenAI**: Used for generating text embeddings. The `generateEmbedding` function in `orchestrator.js` calls the OpenAI API to create vector representations of text data, which are then stored in Pinecone. 