# Design Doc Wizard Flow

This document provides a detailed walkthrough of the web-based Design Document Wizard, explaining each step of the user journey and the underlying processes.

## Wizard Overview

The Design Doc Wizard is a multi-step process that guides users through the creation of a comprehensive design document. It helps users define tasks, analyze their impact, make architectural decisions, and generate a formal document.

The state of the wizard is managed by the `useDesignDocWizard` hook and stored in the `design_doc_sessions` table in Supabase.

## Wizard Steps

The wizard consists of the following steps, represented by the components in `src/components/design-doc-wizard/`:

1.  **Task Definition (`TaskDefinitionStep.tsx`)**: The user starts by defining the task or feature they want to build. This is typically imported from a GitHub issue.

2.  **Task Verification (`TaskVerificationStep.tsx`)**: The system verifies the task details and may ask the user for clarification.

3.  **User Journey Definition (`UserJourneyDefinitionStep.tsx`)**: The user drafts the user journeys that will be affected by the new feature.

4.  **Strategic Assessment Preferences (`StrategicAssessmentPreferencesStep.tsx`)**: The user configures their preferences for the strategic assessment of the task.

5.  **Strategic Value Assessment (`StrategicValueAssessmentStep.tsx`)**: The system analyzes the strategic value of the proposed feature based on the user's input and project context.

6.  **Deployment Constitution Check (`DeploymentConstitutionCheckStep.tsx`)**: The wizard checks for a "deployment constitution" for the repository. If one doesn't exist, the user is guided through the `ConstitutionStep.tsx`.

7.  **Decision Discovery (`DecisionDiscoveryStep.tsx`)**: The system analyzes the task and the codebase to discover potential architectural decisions that need to be made.

8.  **Decision Making (`DecisionMakingStep.tsx` & `StreamlinedDecisionMakingStep.tsx`)**: The user is presented with the discovered decisions and is guided through the process of making them. This is one of the most complex parts of the wizard. The `DecisionOverrideModal.tsx` allows the user to override the system's suggestions.

9.  **Decision Review Preferences (`DecisionReviewPreferencesStep.tsx`)**: The user configures their preferences for reviewing the decisions.

10. **Review Generation (`ReviewGenerationStep.tsx`)**: The system generates a review of the design document based on the user's input and the decisions made.

11. **Document Generation**: The final design document is generated and can be viewed in the `DesignDocumentViewer.tsx`.

12. **Sharing**: The user can share the design document using the `ShareButton.tsx`.

## Backend Processes

Each step in the wizard is supported by a set of API routes in `src/app/api/design-doc-wizard/`. These endpoints handle everything from analyzing tasks and discovering decisions to generating the final document. The specific API called at each step corresponds to the functionality of that step (e.g., `analyze-task`, `discover-decisions`, `generate`).

### Flow Diagram

Here is a diagram that visualizes the Design Doc Wizard flow:

```mermaid
graph TD
    A[Start: Define Task] --> B{Task Verification}
    B --> C[Draft User Journeys]
    C --> D[Set Strategic Assessment Preferences]
    D --> E{Strategic Value Assessment}
    E --> F{Deployment Constitution Check}
    F --> G[Discover Architectural Decisions]
    G --> H[Make Decisions]
    H --> I[Set Decision Review Preferences]
    I --> J{Generate Review}
    J --> K[View Final Design Document]
    K --> L[Share Document]

    subgraph "Wizard Steps"
        A
        B
        C
        D
        E
        F
        G
        H
        I
        J
        K
        L
    end
    
    style A fill:#D5F5E3,stroke:#333,stroke-width:2px
    style K fill:#D5F5E3,stroke:#333,stroke-width:2px
    style L fill:#D5F5E3,stroke:#333,stroke-width:2px
```

This concludes the documentation of the Design Doc Wizard flow. 