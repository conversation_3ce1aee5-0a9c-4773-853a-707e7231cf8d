# ArchKnow User Flows

This document describes the key user journeys within the ArchKnow application, detailing both the user-facing steps and the backend processes that support them.

## 1. User Onboarding via GitHub Login

This flow describes how a new user signs up and logs into the application using their GitHub account.

### User Journey

1.  The user visits the ArchKnow homepage.
2.  The user clicks the "Login with GitHub" button.
3.  The user is redirected to GitHub and prompted to authorize the ArchKnow application.
4.  After granting authorization, the user is redirected back to the ArchKnow application and is now logged in.

### Behind the Scenes: Authentication Flow

Here is a sequence diagram illustrating the authentication process:

```mermaid
sequenceDiagram
    participant User
    participant ArchKnow Frontend
    participant ArchKnow Backend (Next.js API)
    participant GitHub
    participant <PERSON><PERSON><PERSON>

    User->>ArchKnow Frontend: Clicks "Login with GitHub"
    ArchKnow Frontend->>ArchKnow Backend: Initiates login
    ArchKnow Backend->>GitHub: Redirects user for authorization
    GitHub-->>User: Prompts for authorization
    User->>GitHub: Authorizes application
    GitHub->>ArchKnow Backend: Sends authorization code via redirect (callback URL)
    ArchKnow Backend->>GitHub: Exchanges authorization code for an access token
    GitHub-->>ArchKnow Backend: Returns access token
    ArchKnow Backend->>Supabase: Upserts user and creates session
    Supabase-->>ArchKnow Backend: Returns session
    ArchKnow Backend-->>ArchKnow Frontend: Redirects user to dashboard with active session
    ArchKnow Frontend-->>User: Displays dashboard
```

**Detailed Steps:**

1.  **Initiation**: The user clicks the "Login with GitHub" button, which triggers a call to a Next.js API route (likely `src/app/auth/callback/route.ts`).
2.  **GitHub Authorization**: The backend redirects the user to GitHub's authorization page. The user is asked to grant the ArchKnow GitHub App access to their account.
3.  **Callback and Code Exchange**: After the user authorizes the app, GitHub redirects them back to the specified callback URL (`src/app/auth/callback/route.ts`). This request includes a temporary authorization code.
4.  **Token Exchange**: The backend receives the authorization code and exchanges it with GitHub for an access token. This token allows the application to make API requests on behalf of the user.
5.  **Supabase User Management**: With the access token, the backend retrieves the user's GitHub profile information. It then uses the Supabase client to either create a new user or update an existing one in the `auth.users` table.
6.  **Session Creation**: Supabase creates a new session for the user and returns it to the backend.
7.  **Redirection**: The backend redirects the user to the main dashboard of the application. The user's session is now stored in the browser, allowing them to make authenticated requests to the frontend and backend.

## 2. Repository Analysis

This flow describes how a user selects a repository and initiates an analysis.

### User Journey

1.  From the dashboard, the user navigates to the repository selection page.
2.  The user sees a list of their GitHub repositories that have the ArchKnow GitHub App installed.
3.  The user selects a repository to analyze.
4.  The application may ask the user to configure the analysis, for example, by providing information about the repository's deployment constitution.
5.  The user initiates the analysis.
6.  The application shows a progress indicator while the analysis is running.
7.  Once the analysis is complete, the user is presented with a dashboard showing the results.

### Behind the Scenes: Analysis Flow

Here is a sequence diagram illustrating the repository analysis process:

```mermaid
sequenceDiagram
    participant User
    participant ArchKnow Frontend
    participant ArchKnow Backend (Next.js API)
    participant Vercel Worker
    participant GitHub
    participant Pinecone
    participant Supabase

    User->>ArchKnow Frontend: Selects repository and starts analysis
    ArchKnow Frontend->>ArchKnow Backend: Calls analyze-repository endpoint
    ArchKnow Backend->>Supabase: Creates a `repository_loading_jobs` record
    ArchKnow Backend-->>ArchKnow Frontend: Returns job ID
    ArchKnow Frontend->>ArchKnow Frontend: Polls for job status
    
    ArchKnow Backend->>Vercel Worker: Triggers background analysis job
    Vercel Worker->>GitHub: Fetches repository data (PRs, commits, files)
    Vercel Worker->>ArchKnow Backend: Sends data for processing (e.g., to LLM)
    ArchKnow Backend->>Pinecone: Embeds and stores vector representations
    Vercel Worker->>Supabase: Updates `repository_loading_jobs` status
    
    ArchKnow Frontend-->>User: Displays analysis results when complete
```

**Detailed Steps:**

1.  **Initiation**: The user selects a repository and clicks the "Analyze" button in the frontend. This triggers a call to the `POST /api/analyze-repository` endpoint.
2.  **Job Creation**: The `analyze-repository` endpoint receives the request, which includes the `repositorySlug` and `installationId`. It creates a new record in the `repository_loading_jobs` table in Supabase to track the status of the analysis.
3.  **Background Job**: The backend then triggers a background worker to handle the actual analysis. This is done to avoid long-running requests that could time out. In a Vercel environment, this is likely a Vercel Function.
4.  **Data Fetching**: The worker starts by fetching data from the GitHub API. This includes pull requests, commit history, and the contents of relevant files.
5.  **Processing and Embedding**: As the data is fetched, it's processed. This involves using Large Language Models (LLMs) to extract architectural decisions, identify key concepts, and generate summaries. The processed data is then converted into vector embeddings.
6.  **Vector Storage**: The vector embeddings are stored in a Pinecone index, which is namespaced by the repository. This allows for efficient similarity searches later on.
7.  **Status Updates**: Throughout the process, the worker updates the status of the job in the `repository_loading_jobs` table in Supabase.
8.  **Polling and Display**: The frontend polls a status endpoint (e.g., `/api/repository-status`) to get the current status of the analysis job. Once the job is marked as "completed", the frontend fetches the results and displays them to the user in a dashboard.
9.  **Dead Code Note**: The codebase contains `src/pages/api/analysis/start.ts`, which appears to be an older version of the analysis endpoint. The current implementation is in `src/app/api/analyze-repository/route.ts`.

This completes the documentation of the main user flows.
