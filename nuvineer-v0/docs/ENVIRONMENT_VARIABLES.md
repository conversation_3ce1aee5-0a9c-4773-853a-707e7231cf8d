# Environment Variables

This document lists all the environment variables required to run the ArchKnow application.

## Vercel / Next.js

These variables are primarily used by the Next.js frontend and backend.

-   `NEXT_PUBLIC_SUPABASE_URL`: The public URL for your Supabase project.
-   `NEXT_PUBLIC_SUPABASE_ANON_KEY`: The public anonymous key for your Supabase project.
-   `SUPABASE_SERVICE_ROLE_KEY`: The secret service role key for your Supabase project. This is used for admin-level access on the backend.
-   `NEXT_PUBLIC_ALLOWED_EMAILS`: A comma-separated list of email addresses that are allowed to sign up.
-   `NEXT_PUBLIC_ALLOWED_GITHUB_IDS`: A comma-separated list of GitHub user IDs that are allowed to sign up.
-   `NEXT_PUBLIC_BASE_URL`: The base URL of the deployed application (e.g., `https://archknow.vercel.app`).
-   `CRON_SECRET`: A secret key used to authorize cron jobs.
-   `MAIN_BRANCH_NAME`: The name of the main branch in your repositories (e.g., `main` or `master`). Defaults to `main`.
-   `NODE_ENV`: The application environment (e.g., `development` or `production`).

## Third-Party Services

These variables are for connecting to the various third-party services that ArchKnow depends on.

### GitHub

-   `GITHUB_APP_ID`: Numeric GitHub App ID for the installed GitHub App.
-   `GITHUB_APP_PRIVATE_KEY`: PEM contents of the GitHub App private key. Preserve newlines; if single-line, store with `\n` and the code will convert to newlines.
-   `GITHUB_WEBHOOK_SECRET`: Webhook secret configured on the GitHub App; used to verify `x-hub-signature-256`.
-   `GITHUB_TOKEN` (optional): A personal access token used for public repositories when no installation context is available.

See `docs/GITHUB_APP_SETUP.md` for the full GitHub App configuration, required permissions, and webhook events.

### Pinecone

-   `PINECONE_API_KEY`: Your API key for the Pinecone service.
-   `PINECONE_INDEX_NAME`: The name of the Pinecone index where decision vectors are stored. Defaults to `architecture-decisions`.

### Large Language Models (LLMs)

-   `ANTHROPIC_API_KEY`: Your API key for the Anthropic (Claude) API.
-   `ANTHROPIC_MODEL`: The specific Claude model to use for generative tasks.
-   `OPENAI_API_KEY`: Your API key for the OpenAI API.

## Debugging

-   `DEBUG`: Set to `true` to enable more verbose logging throughout the application. 