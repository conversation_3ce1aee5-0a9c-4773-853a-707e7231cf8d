# RCA Wizard Flow

**Note:** This feature is experimental and currently being iterated on. The flow and components are subject to change.

## Wizard Overview

The Root Cause Analysis (RCA) Wizard is a guided workflow to help developers investigate incidents, identify the root cause, and document the findings.

The state of the wizard is managed by the `useRCAWizard` hook and stored in the `rca_analysis_sessions` table in Supabase.

## Wizard Steps

The wizard consists of the following steps, represented by the components in `src/components/rca-wizard/`:

1.  **Symptom Investigation**: The user starts by describing the symptoms of the incident. This is likely handled on the main `rca-wizard` page.
2.  **Investigation Plan (`InvestigationPlanView.tsx`)**: Based on the symptoms, the system generates a plan for investigating the incident.
3.  **Forensic Analysis (`ForensicAnalysisView.tsx`)**: The system performs a forensic analysis of the codebase, looking for clues related to the incident.
4.  **Decision Discovery (`DecisionDiscoveryView.tsx`)**: The wizard attempts to discover any architectural decisions that may have contributed to the incident.
5.  **Coding Agent Prompt (`CodingAgentPrompt.tsx`)**: The wizard can generate a prompt for a coding agent to help with fixing the issue.

## Backend Processes

Each step in the RCA wizard is supported by a set of API routes in `src/app/api/rca-wizard/`. These endpoints handle everything from investigating symptoms to discovering related decisions.

### Flow Diagram

Here is a diagram that visualizes the RCA Wizard flow:

```mermaid
graph TD
    A[Start: Describe Symptoms] --> B{Generate Investigation Plan}
    B --> C{Perform Forensic Analysis}
    C --> D{Discover Related Decisions}
    D --> E[Generate Coding Agent Prompt]

    subgraph "Wizard Steps (Experimental)"
        A
        B
        C
        D
        E
    end

    style A fill:#FADBD8,stroke:#333,stroke-width:2px
    style E fill:#D5F5E3,stroke:#333,stroke-width:2px
```

This concludes the documentation of the RCA Wizard flow. 