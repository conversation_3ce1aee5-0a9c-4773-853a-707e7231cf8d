# ArchKnow Application Architecture

This document provides a comprehensive overview of the ArchKnow application's architecture. It is intended for the engineering team to understand the current state of the system for the migration to a new architecture.

## High-Level Architecture

The ArchKnow application is comprised of several key components that work together to provide its functionality. The system is built as a monorepo containing a Next.js web application, a VSCode extension, and various services for analyzing software repositories.

### System Architecture Overview

```mermaid
graph TB
    subgraph "User Layer"
        WEB["Next.js Web App<br/>(React Frontend)"]
        VSC["VSCode Extension<br/>(TypeScript)"]
        USER["Users"]
    end

    subgraph "Application Layer"
        API["Next.js API Routes<br/>(Serverless Functions)"]
        ORCH["Orchestrator<br/>(Analysis Engine)"]
        JOBS["Background Jobs<br/>(Cron Processing)"]
    end

    subgraph "External Services"
        GH["GitHub<br/>(Source Data)"]
        GHAPP["GitHub App<br/>(aiguardrails)"]
    end

    subgraph "Data Layer"
        SB["Supabase<br/>(PostgreSQL + Auth)"]
        PC["Pinecone<br/>(Vector Database)"]
    end

    subgraph "AI/ML Services"
        ANT["Anthropic Claude<br/>(Text Generation)"]
        OAI["OpenAI<br/>(Embeddings)"]
    end

    subgraph "Infrastructure"
        VER["Vercel<br/>(Hosting Platform)"]
        CRON["Vercel Cron<br/>(Scheduler)"]
    end

    USER --> WEB
    USER --> VSC
    WEB --> API
    VSC --> API
    API --> ORCH
    API --> SB
    CRON --> JOBS
    JOBS --> ORCH

    ORCH --> PC
    ORCH --> ANT
    ORCH --> OAI
    ORCH --> SB

    API --> GH
    GHAPP --> API
    GH -.->|"Webhooks"| API

    VER -.->|"Hosts"| WEB
    VER -.->|"Hosts"| API
    VER -.->|"Runs"| JOBS

    style WEB fill:#e1f5fe
    style VSC fill:#e1f5fe
    style API fill:#e8f5e8
    style ORCH fill:#fff3e0
    style SB fill:#fce4ec
    style PC fill:#fce4ec
    style GH fill:#f3e5f5
    style ANT fill:#fff8e1
    style OAI fill:#fff8e1
```

### Infrastructure Components

```mermaid
graph TB
    subgraph "Deployment Infrastructure"
        VER_EDGE["Vercel Edge Network<br/>(Global CDN)"]
        VER_FUNC["Vercel Functions<br/>(Serverless Runtime)"]
        VER_CRON["Vercel Cron Jobs<br/>(Scheduled Tasks)"]
    end

    subgraph "Database Infrastructure"
        SB_DB["Supabase PostgreSQL<br/>(Primary Database)"]
        SB_AUTH["Supabase Auth<br/>(User Management)"]
        SB_RLS["Row Level Security<br/>(Multi-tenant Isolation)"]
        PC_INDEX["Pinecone Index<br/>(Vector Storage)"]
        PC_NS["Pinecone Namespaces<br/>(Repository Scoping)"]
    end

    subgraph "External APIs"
        GH_REST["GitHub REST API<br/>(Repository Data)"]
        GH_WEBHOOK["GitHub Webhooks<br/>(Real-time Events)"]
        ANT_API["Anthropic API<br/>(Claude Models)"]
        OAI_API["OpenAI API<br/>(Embedding Models)"]
    end

    subgraph "Security & Auth"
        GH_APP["GitHub App<br/>(aiguardrails)"]
        GH_OAUTH["GitHub OAuth<br/>(User Authentication)"]
        API_KEYS["API Key Management<br/>(Service Authentication)"]
        WEBHOOK_SIG["Webhook Signatures<br/>(Request Verification)"]
    end

    subgraph "Data Flow"
        EVENTS["GitHub Events<br/>(PR Merge, Push)"]
        QUEUE_TBL["Analysis Queue<br/>(repository_pr_analysis_status)"]
        PROC_JOB["Processing Job<br/>(Background Worker)"]
        VECTOR_STORE["Vector Storage<br/>(Embeddings + Metadata)"]
    end

    VER_EDGE --> VER_FUNC
    VER_FUNC --> SB_DB
    VER_FUNC --> PC_INDEX
    VER_CRON --> PROC_JOB

    SB_DB --> SB_RLS
    SB_AUTH --> GH_OAUTH
    PC_INDEX --> PC_NS

    GH_WEBHOOK --> VER_FUNC
    GH_REST --> VER_FUNC
    ANT_API --> VER_FUNC
    OAI_API --> VER_FUNC

    GH_APP --> GH_REST
    GH_APP --> GH_WEBHOOK
    GH_OAUTH --> SB_AUTH
    WEBHOOK_SIG --> GH_WEBHOOK

    EVENTS --> QUEUE_TBL
    QUEUE_TBL --> PROC_JOB
    PROC_JOB --> VECTOR_STORE
    VECTOR_STORE --> PC_INDEX

    style VER_FUNC fill:#00d084
    style SB_DB fill:#3ecf8e
    style PC_INDEX fill:#000000,color:#ffffff
    style GH_APP fill:#24292e,color:#ffffff
    style ANT_API fill:#d97706
    style OAI_API fill:#10a37f
```

### Application Components

```mermaid
graph LR
    subgraph "Frontend Components"
        WIZARD["Design Doc Wizard<br/>(Multi-step Form)"]
        RCA["RCA Wizard<br/>(Root Cause Analysis)"]
        DASH["Dashboards<br/>(Launch/Risk)"]
        REPO_SEL["Repository Selector<br/>(GitHub Integration)"]
    end

    subgraph "API Endpoints"
        AUTH_API["Authentication<br/>(/api/auth/*)"]
        REPO_API["Repository Analysis<br/>(/api/analyze-repository)"]
        WIZARD_API["Design Doc API<br/>(/api/design-doc-wizard/*)"]
        WEBHOOK_API["Webhook Handler<br/>(/api/webhooks/github)"]
        CRON_API["Cron Processor<br/>(/api/cron/process-prs)"]
    end

    subgraph "Core Services"
        GITHUB_SVC["GitHub Service<br/>(Octokit Client)"]
        DECISION_SVC["Decision Service<br/>(CRUD Operations)"]
        LLM_SVC["LLM Service<br/>(Anthropic/OpenAI)"]
        VECTOR_SVC["Vector Service<br/>(Pinecone Client)"]
    end

    subgraph "Data Models"
        USERS["Users<br/>(Supabase Auth)"]
        REPOS["Repositories<br/>(Installation Mapping)"]
        PRS["Pull Requests<br/>(Analysis Status)"]
        DECISIONS_TBL["Decisions<br/>(Extracted Content)"]
        RELATIONSHIPS["Decision Relationships<br/>(Graph Structure)"]
    end

    WIZARD --> WIZARD_API
    RCA --> WIZARD_API
    DASH --> REPO_API
    REPO_SEL --> REPO_API

    AUTH_API --> GITHUB_SVC
    REPO_API --> GITHUB_SVC
    WIZARD_API --> DECISION_SVC
    WEBHOOK_API --> GITHUB_SVC
    CRON_API --> GITHUB_SVC

    GITHUB_SVC --> REPOS
    DECISION_SVC --> DECISIONS_TBL
    LLM_SVC --> DECISIONS_TBL
    VECTOR_SVC --> DECISIONS_TBL

    REPOS --> PRS
    PRS --> DECISIONS_TBL
    DECISIONS_TBL --> RELATIONSHIPS

    style WIZARD fill:#e3f2fd
    style WIZARD_API fill:#e8f5e8
    style GITHUB_SVC fill:#fff3e0
    style DECISIONS_TBL fill:#fce4ec
```

### Data Processing Pipeline

```mermaid
graph TD
    subgraph "GitHub Integration"
        REPO["Repository<br/>(Source Code)"]
        PR["Pull Requests<br/>(Code Changes)"]
        WEBHOOK["Webhook Events<br/>(push, PR merged)"]
        OAUTH["OAuth Flow<br/>(User Auth)"]
    end

    subgraph "Processing Pipeline"
        QUEUE["Analysis Queue<br/>(Supabase Table)"]
        ANALYZER["PR Analyzer<br/>(orchestrator.js)"]
        EXTRACT["Decision Extractor<br/>(LLM Prompts)"]
        RELATE["Relationship Analysis<br/>(Multi-step Process)"]
    end

    subgraph "Data Storage"
        DECISIONS["Architectural Decisions<br/>(PostgreSQL)"]
        VECTORS["Decision Embeddings<br/>(Pinecone Vectors)"]
        SESSIONS["User Sessions<br/>(Supabase Auth)"]
        DOCS["Design Documents<br/>(Database BLOBs)"]
    end

    subgraph "Analysis Methods"
        SEMANTIC["Semantic Search<br/>(Vector Similarity)"]
        FILES["File Overlap<br/>(Shared Files)"]
        CONCEPTS["Domain Concepts<br/>(Shared Terms)"]
    end

    REPO --> PR
    PR --> WEBHOOK
    WEBHOOK --> QUEUE
    QUEUE --> ANALYZER
    ANALYZER --> EXTRACT
    EXTRACT --> DECISIONS
    DECISIONS --> VECTORS
    ANALYZER --> RELATE
    RELATE --> SEMANTIC
    RELATE --> FILES
    RELATE --> CONCEPTS
    OAUTH --> SESSIONS

    SEMANTIC --> VECTORS
    FILES -.->|"Metadata Query"| VECTORS
    CONCEPTS -.->|"Metadata Query"| VECTORS

    style WEBHOOK fill:#ffcdd2
    style QUEUE fill:#fff9c4
    style ANALYZER fill:#c8e6c9
    style DECISIONS fill:#e1bee7
    style VECTORS fill:#e1bee7
    style DOCS fill:#ffccbc
```

## Core Components

### 1. Next.js Web Application
- **Location:** `src/app`, `src/components`, `src/hooks`, etc.
- **Description:** The primary user interface for ArchKnow. It's a modern web application built with Next.js and React.
- **Hosting:** Deployed on Vercel.

### 2. VSCode Extension
- **Location:** `extension/`
- **Description:** Provides ArchKnow's functionality directly within the Visual Studio Code editor, allowing developers to interact with the system without leaving their IDE.

### 3. Backend (Next.js API Routes)
- **Location:** `src/app/api/`
- **Description:** The backend logic is implemented as serverless functions using Next.js API routes. These handle everything from user authentication to repository analysis.
- **Hosting:** Deployed on Vercel as part of the Next.js application.

### 4. Supabase
- **Description:** Used as the primary database (Postgres) and for handling user authentication.

### 5. Pinecone
- **Description:** A vector database used for similarity searches, primarily for finding related architectural decisions and concepts.

### 6. Large Language Models (LLMs)
- **Description:** Integrated for various AI-powered features, such as generating design documents, analyzing code, and providing recommendations.

### 7. GitHub Integration
- **Description:** The application interacts heavily with the GitHub API to access repository data, analyze pull requests, and manage issues. It also uses GitHub webhooks to receive real-time updates.

## Frontend Architecture

The frontend of the ArchKnow application is a Next.js application. This section details the structure and key components of the frontend.

### Directory Structure

The main directories for the frontend are:
- `src/app`: Contains the main pages of the application, following the Next.js App Router paradigm.
- `src/components`: Contains reusable React components used throughout the application.
- `src/hooks`: Contains custom React hooks for managing state and side effects.
- `src/lib`: Contains utility functions and client-side libraries.
- `src/styles`: Contains global styles and component-specific styles.
- `src/types`: Contains TypeScript type definitions.

### Key Features and Flows

1.  **Authentication**: User authentication is handled via Supabase Auth. The flow is initiated from the frontend, and the session is managed in the browser. The relevant files are in `src/app/auth`.
2.  **Repository Analysis**: Users can select and analyze GitHub repositories. The main UI for this is in `src/components/RepositorySelector.tsx` and the analysis pages.
3.  **Design Doc Wizard**: A multi-step wizard to guide users through creating a design document. The components for this are located in `src/components/design-doc-wizard`.
4.  **RCA Wizard**: A similar wizard for performing Root Cause Analysis, with components in `src/components/rca-wizard`.
5.  **Dashboards**: Various dashboards for visualizing data, such as the Launch Dashboard and Risk Dashboard.

### Component Breakdown

-   `BacklogGroomingFlow.tsx`: Manages the UI for the backlog grooming feature.
-   `DecisionDetailModal.tsx`: A modal to display the details of a specific architectural decision.
-   `RepositorySetupFlow.tsx`: A wizard to guide users through setting up a repository for analysis.
-   `design-doc-wizard/`: This directory contains all the components for the multi-step design document creation wizard. Each step in the wizard has its own component.
-   `rca-wizard/`: Similar to the design doc wizard, this contains the components for the root cause analysis wizard.
-   `ui/`: Contains general-purpose, reusable UI components like buttons, cards, and inputs, forming the basic design system of the application.

This concludes the frontend architecture section. Next, I will document the backend.

## Backend Architecture (API Routes)

The backend is implemented as a set of serverless functions using Next.js API routes. The API is organized by feature, with each main directory in `src/app/api/` corresponding to a different area of functionality.

### API Route Breakdown

-   `admin/`: Administrative functions, such as triggering a semantic search indexing.
-   `analysis/`: Endpoints for running various analyses on repositories, like building the knowledge graph.
-   `analyze-repository/`: The main endpoint for initiating a full analysis of a repository.
-   `backlog-grooming/`: APIs to support the backlog grooming feature.
-   `constitution/`: Endpoints for managing and seeding the deployment constitution for a repository.
-   `cron/`: Cron jobs, such as processing pull requests periodically.
-   `decisions/`: APIs for managing architectural decisions.
-   `design-doc-wizard/`: A comprehensive set of endpoints to support the multi-step design document wizard.
-   `extension/`: APIs specifically for the VSCode extension.
-   `feedback/`: Endpoints for collecting user feedback.
-   `github/`: A proxy or wrapper for the GitHub API, handling interactions with repositories, issues, and pull requests.
-   `launch-dashboard/`: APIs for the launch dashboard.
-   `rca-wizard/`: Endpoints to support the root cause analysis wizard.
-al`repository/`: APIs for repository-specific actions.
-   `user/`: User management APIs, such as managing API keys.
-   `webhooks/`: The endpoint for receiving and processing GitHub webhooks.
-   `worker/`: Endpoints for background jobs that are processed by a worker.

This provides a high-level map of the backend API. Next, I will document the VSCode extension.

## VSCode Extension Architecture

The VSCode extension allows developers to use ArchKnow's features directly in their editor.

### MCP Server Integration

A key part of the extension's architecture is the bundled **MCP (Model Context Protocol) Server**. This is a small, local Node.js server that is managed by the extension and runs in the background.

-   **Purpose**: The MCP server acts as a bridge between the VSCode extension and the main Cursor application. It allows Cursor to invoke functionality within the extension by making HTTP requests to a local port (`7635`).

-   **Implementation**:
    -   The server is an Express.js application, with the source code located in `extension/mcp_server/`.
    -   It is automatically built and bundled with the extension via an `npm` script in `extension/package.json`.
    -   The `McpBridgeServer` class in `extension/src/mcp-bridge.ts` is responsible for starting and stopping the server.
    -   The main extension activation function in `extension/src/extension.ts` creates an instance of `McpBridgeServer` and starts it when the extension is loaded.

-   **User Configuration**: For Cursor to be able to communicate with the MCP server, the user must add a configuration snippet to their `~/.cursor/mcp.json` file. The extension provides a command (`archknow.installMcpServer`) to help the user with this setup.

### Directory Structure

-   `src/commands`: Defines the commands that can be executed from the VSCode command palette.
-   `src/config`: Manages configuration and settings for the extension.
-   `src/formatters`: Contains logic for formatting text, such as for prompts sent to the LLM.
-   `src/handlers`: Handles events, such as URI handling for custom protocols.
-   `src/services`: Contains services that encapsulate business logic, such as interacting with the ArchKnow API or Git.
-   `src/ui`: Manages user interface elements, like webviews and the status bar.
-   `src/utils`: Contains utility functions.

### Key Files

-   `extension.ts`: The main entry point for the extension. It registers commands, creates webviews, and initializes services.
-   `mcp-bridge.ts`: A bridge to communicate with the MCP (Multi-Code-Pal) server.
-   `webview.ts`: A very large file that seems to manage the webview content and interactions. This might be a candidate for refactoring.
-   `aiAgentProtocol.ts`: Defines the protocol for communication with the AI agent.

This provides an overview of the VSCode extension. The next step is to document the database schema.

## Database Schema

The database is managed by Supabase (Postgres). The schema is defined through a series of migration files located in `sql/migrations`.

### Tables

-   **`repository_loading_jobs`**:
    -   **Purpose**: Tracks the progress of loading repository data (pull requests and commits) from GitHub.
    -   **Key Columns**: `repository_slug`, `installation_id`, `current_phase`, `next_page_to_process`, `status`.

-   **`repository_pr_commit_shas`**:
    -   **Purpose**: Stores commit SHAs that are associated with pull requests to avoid duplicate processing.
    -   **Key Columns**: `repository_slug`, `installation_id`, `commit_sha`.

-   **`design_doc_sessions`**:
    -   **Purpose**: Stores the state of design document wizard sessions.
    -   **Key Columns**: `repository_slug`, `user_id`, `title`, `github_issue_url`, `wizard_state`.

-   **`deployment_constitutions`**:
    -   **Purpose**: Stores the structured deployment context (e.g., from IaC files) for a repository.
    -   **Key Columns**: `repository_slug`, `installation_id`, `constitution_data`, `source_repo_url`.

-   **`rca_analysis_sessions`**:
    -   **Purpose**: Stores the state of Root Cause Analysis wizard sessions.
    -   **Key Columns**: `repository_slug`, `user_id`, `wizard_state`.

This provides a high-level overview of the database schema. The next and final step is to identify any dead code.

## Dead Code and Refactoring Candidates

This section identifies parts of the codebase that are likely no longer used or could be refactored.

### Dead Code

-   **`src/pages/`**:
    -   **Reasoning**: This directory seems to be a remnant of the Next.js Pages Router. Since the application now uses the App Router (`src/app/`), the API routes within `src/pages/api/` are likely unused duplicates of the routes in `src/app/api/`.
    -   **Recommendation**: The entire `src/pages/` directory can likely be deleted.

### Refactoring Candidates

-   **`orchestrator.js`**:
    -   **Reasoning**: This is a very large, monolithic file. It's difficult to understand and maintain. Its name suggests it might contain complex logic that would be better broken down into smaller, more focused modules. It might also contain logic that is now handled by the newer API routes.
    -   **Recommendation**: Investigate this file to determine if its functionality is still needed. If it is, it should be refactored into smaller, more manageable services.

-   **`src/analyzer/`**:
    -   **Reasoning**: Similar to `orchestrator.js`, this directory contains what appears to be older analysis logic. It's possible that the functionality in this directory has been superseded by the API routes in `src/app/api/analysis/` and other related endpoints.
    -   **Recommendation**: Analyze the code in this directory to see if it's still being used. If not, it should be removed. If it is, consider refactoring it to align with the current architecture.

-   **`extension/src/webview.ts`**:
    -   **Reasoning**: At over 3,000 lines of code, this file is extremely large and complex. It manages the webview for the VSCode extension and likely handles many different responsibilities.
    -   **Recommendation**: This file should be broken down into smaller, more focused modules to improve readability and maintainability. For example, the logic for each type of webview panel could be extracted into its own file.

-   **`extension/src/services/designDocWorkflowService.ts`**:
    -   **Reasoning**: This service appears to contain the logic for a design document workflow that was initiated from the VSCode extension. Since the team has confirmed that the design doc wizard is now exclusively web-based, this file and its related components are no longer in use.
    -   **Recommendation**: This file should be deleted. Other related artifacts in the `extension/` directory should also be reviewed and removed if they are no longer needed.

## Detailed Processing Flows

For a detailed breakdown of the internal processing logic, such as how pull requests and commits are analyzed, please refer to the [Processing Flows document](./PROCESSING_FLOWS.md).

For a detailed walkthrough of the Design Doc Wizard, please refer to the [Design Doc Wizard Flow document](./DESIGN_DOC_WIZARD_FLOW.md).

For a detailed walkthrough of the experimental RCA Wizard, please refer to the [RCA Wizard Flow document](./RCA_WIZARD_FLOW.md).

For a detailed overview of how the application integrates with third-party services, please refer to the [Third-Party Service Integrations document](./THIRD_PARTY_INTEGRATIONS.md).

For an overview of the current scaling and reliability challenges, please refer to the [Scaling and Reliability Challenges document](./SCALING_AND_RELIABILITY.md).

For a complete list of the environment variables required to run the application, please refer to the [Environment Variables document](./ENVIRONMENT_VARIABLES.md).

This concludes the architectural documentation. It should provide a solid foundation for the new team to understand the current state of the ArchKnow application.
