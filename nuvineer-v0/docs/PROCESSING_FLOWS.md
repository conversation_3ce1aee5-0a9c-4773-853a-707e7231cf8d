# ArchKnow Processing Flows

This document details the low-level processing flows for analyzing pull requests and commits.

## 1. Multi-Step Filtering Pipeline

Before any PR or commit undergoes deep architectural analysis, it passes through a multi-step filtering pipeline designed to ensure only architecturally significant changes receive expensive processing. This pipeline operates in three distinct stages:

### Stage 1: Heuristic Filter (Fast & Conservative)

**Purpose**: Quickly eliminate commits that are definitely not architectural using pattern matching.

**Implementation**: `isDefinitelyNotArchitectural()` function in `src/lib/heuristics.ts`

**Criteria**:
- **Conventional Commit Prefixes**: `chore:`, `docs:`, `fix(docs):`, `style:`, `ci:`, `test:`, `build:`, `refactor(docs):`
- **Non-Architectural Keywords**: `translation`, `localization`, `i18n`, `l10n`, `crowdin`, `typo`, `lint`, `formatting`, `update snapshot`

**Status Transition**: Items that pass move to `pending_early_stage` status; filtered items are excluded entirely.

**Design Philosophy**: Highly conservative - designed to avoid false negatives (missing significant commits) rather than false positives.

### Stage 2: LLM Lightweight Filter (Intelligent Significance Assessment)

**Purpose**: Use a fast, cheap LLM (Claude Haiku) to assess architectural significance with file context.

**Implementation**: `callLightweightLlmForSignificance()` in `src/lib/llmUtils.ts` and processing in `src/pages/api/analysis/llm-filter-job.ts`

**Input Context**:
- Commit/PR title and description
- File context (if available):
  - Number of files changed
  - Total additions/deletions
  - Directories affected
  - File types modified

**LLM Prompt**: Simple binary assessment asking "is this commit architecturally significant?" with response format `{ "sig": 1 }` or `{ "sig": 0 }`

**Status Transitions**:
- `pending_early_stage` → `pending` (architecturally significant)
- `pending_early_stage` → `skipped_not_significant` (not significant)

### Stage 3: Deep Architectural Analysis

**Purpose**: Full analysis with expensive LLM processing for decision extraction and relationship analysis.

**Trigger**: Only items with `pending` status undergo this stage.

**Implementation**: `processMergedPR()` function in `src/orchestrator.js`

**Processing**:
- Fetch deployment constitution
- Extract architectural decisions using detailed prompts
- Analyze relationships with existing decisions
- Generate embeddings and store in Pinecone
- Store decisions and relationships in database

### Filtering Flow Diagram

```mermaid
graph TD
    START["PR/Commit Event"] --> HEURISTIC["Stage 1: Heuristic Filter<br/>(Pattern Matching)"]
    
    HEURISTIC -->|"Passes"| EARLY_STAGE["Status: pending_early_stage"]
    HEURISTIC -->|"Filtered Out"| EXCLUDED["Excluded from Analysis"]
    
    EARLY_STAGE --> LLM_FILTER["Stage 2: LLM Lightweight Filter<br/>(Significance Assessment)"]
    
    LLM_FILTER -->|"sig: 1"| PENDING["Status: pending"]
    LLM_FILTER -->|"sig: 0"| SKIPPED["Status: skipped_not_significant"]
    
    PENDING --> DEEP_ANALYSIS["Stage 3: Deep Analysis<br/>(Full Processing)"]
    
    DEEP_ANALYSIS --> ANALYZED["Status: analyzed/completed"]
    DEEP_ANALYSIS --> NO_DECISIONS["Status: no_decisions"]
    DEEP_ANALYSIS --> FAILED["Status: failed"]
    
    style HEURISTIC fill:#fff9c4
    style LLM_FILTER fill:#e1f5fe
    style DEEP_ANALYSIS fill:#c8e6c9
    style EXCLUDED fill:#ffcdd2
    style SKIPPED fill:#ffcdd2
    style ANALYZED fill:#c8e6c9
```

### File Content Filtering

In addition to PR/commit level filtering, individual file changes are also filtered during processing:

**Implementation**: `shouldSkipFileContent()` in `src/lib/analysisUtils.js`

**Filtered File Types**:
- Documentation: `.md`, `.txt`, `.rst`
- Media files: `.jpg`, `.png`, `.gif`, `.svg`, etc.
- Configuration: `.yml`, `.yaml`, `.json` (except `package.json`)
- Lock files: `package-lock.json`, `yarn.lock`
- CI/CD files: `.github/`, `.gitlab-ci.yml`
- Development tools: `jest.config.*`, `tsconfig.*`, etc.

**Preserved Files**: `package.json` is specifically preserved as it often contains architecturally relevant dependency changes.

## 2. Merged Pull Request Processing

The core logic for processing merged pull requests resides in the `processMergedPR` function within `src/orchestrator.js`. This function is triggered after a pull request has been identified as containing potentially significant architectural changes.

### High-Level Flow

1.  **Initialization**: The function is called with the pull request context, code changes, comments, and other metadata.
2.  **Constitution Fetch**: It fetches the "deployment constitution" for the repository from the `deployment_constitutions` table in Supabase. This provides context about the repository's deployment environment.
3.  **LLM-based Decision Extraction**: The function formats a prompt containing the PR details and the deployment constitution and sends it to a Large Language Model (LLM) to extract architectural decisions.
4.  **Decision Processing Loop**: If the LLM returns one or more decisions, the orchestrator iterates through each one:
    *   **Enrichment**: The decision is enriched with metadata from the pull request, such as the PR number and merge date.
    *   **Alternatives Analysis (Heuristics)**: The `shouldAnalyzeAlternatives` function is called. This function uses a set of heuristics (e.g., checking for keywords like "new pattern" or "refactor", and analyzing the number of lines changed) to determine if an analysis of alternative solutions is warranted. *Note: The actual alternatives analysis is not performed automatically; this step only determines if it should be recommended.*
    *   **Relationship Analysis**: The `analyzeRelationships` function is called to identify relationships between the new decision and existing decisions. This involves querying Pinecone for similar decisions based on semantic similarity, file overlap, and domain concept overlap.
    *   **Storage**: The `storeDecisionRecord` function is called to store the enriched decision in Pinecone. This involves generating a unique ID, creating a text embedding of the decision, and upserting it into the appropriate Pinecone namespace.
5.  **Logging**: The outcome of the processing (success, failure, or no decisions found) is logged.

### Sequence Diagram

Here is a sequence diagram that visualizes the `processMergedPR` flow:

```mermaid
sequenceDiagram
    participant Orchestrator as orchestrator.js
    participant Supabase
    participant LLM as Large Language Model
    participant Pinecone

    Orchestrator->>Supabase: Fetch Deployment Constitution
    Supabase-->>Orchestrator: Return Constitution

    Orchestrator->>LLM: Extract Architectural Decisions from PR
    LLM-->>Orchestrator: Return Decisions

    loop For each Decision
        Orchestrator->>Orchestrator: Enrich Decision with PR Metadata
        Orchestrator->>Orchestrator: Analyze Alternatives (Heuristics)
        
        Orchestrator->>Pinecone: Query for similar decisions (RAG)
        Pinecone-->>Orchestrator: Return similar decisions
        
        Orchestrator->>Orchestrator: Analyze Relationships
        
        Orchestrator->>LLM: Generate Embedding for Decision
        LLM-->>Orchestrator: Return Embedding

        Orchestrator->>Pinecone: Store Decision Record
        Pinecone-->>Orchestrator: Acknowledge Storage
    end

    Orchestrator->>Orchestrator: Log Final Status
```

## 3. End-to-End Analysis Flow

This diagram shows the complete workflow from user repository selection through webhook-triggered analysis to background processing:

```mermaid
sequenceDiagram
    participant User
    participant Web as "Web App"
    participant API as "API Routes"
    participant GH as "GitHub"
    participant Queue as "Analysis Queue"
    participant Cron as "Cron Job"
    participant Orchestrator
    participant LLM as "LLM Services"
    participant Vector as "Pinecone"
    participant DB as "Supabase"

    Note over User,DB: Repository Analysis Flow

    User->>Web: Select Repository
    Web->>API: POST /api/analyze-repository
    API->>GH: Fetch Repository Data
    GH-->>API: Repository Metadata
    API->>DB: Store Repository Info
    API-->>Web: Analysis Started

    Note over GH,Queue: Webhook-Triggered Analysis

    GH->>API: Webhook (PR Merged)
    API->>Queue: Add to Analysis Queue
    API-->>GH: 200 OK

    Note over Cron,Vector: Background Processing

    Cron->>Queue: Check Pending Items
    Queue-->>Cron: PR Analysis Tasks
    Cron->>Orchestrator: Process PR
    Orchestrator->>GH: Fetch PR Details
    GH-->>Orchestrator: PR Data + Files
    Orchestrator->>LLM: Extract Decisions
    LLM-->>Orchestrator: Structured Decisions
    Orchestrator->>LLM: Generate Embeddings
    LLM-->>Orchestrator: Vector Embeddings
    Orchestrator->>Vector: Store Vectors
    Orchestrator->>DB: Store Decisions
    Orchestrator->>Vector: Query Related Decisions
    Vector-->>Orchestrator: Similar Decisions
    Orchestrator->>LLM: Analyze Relationships
    LLM-->>Orchestrator: Relationship Types
    Orchestrator->>DB: Store Relationships
    Orchestrator-->>Cron: Analysis Complete
    Cron->>Queue: Update Status
```

This concludes the detailed documentation of the merged pull request processing flow. 