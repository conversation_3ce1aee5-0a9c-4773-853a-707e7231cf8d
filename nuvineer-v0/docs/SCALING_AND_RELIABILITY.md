# Scaling and Reliability Challenges

This document outlines the current scaling and reliability challenges of the ArchKnow application. It is intended to provide the new engineering team with a clear understanding of the areas that need to be addressed in the new architecture.

## 1. Job Processing and Scalability

The current system for processing background jobs, such as repository analysis, presents significant scaling and reliability challenges.

-   **Vercel Cron Jobs**: The repository analysis process is triggered by a Vercel cron job that runs every minute. This job checks the `repository_loading_jobs` table for pending tasks and processes them.
    -   **Challenge**: This approach does not scale well. If a large number of repositories are being analyzed simultaneously, the one-minute interval may not be sufficient to process all the jobs, leading to delays. There is no sophisticated queueing or prioritization mechanism.
    -   **Reliability**: Serverless functions have execution time limits. A very large repository could cause the analysis function to time out, leaving the job in a failed or inconsistent state.

-   **Onboarding Scalability**: The user onboarding and initial repository analysis flow are handled by a series of serverless functions.
    -   **Challenge**: While serverless functions are generally scalable, the application could experience cold start delays and other performance issues under a high load of concurrent new users. The lack of a dedicated job queue makes it difficult to manage the load effectively.

## 2. Multi-Tenancy Strategy

The application uses a multi-tenant architecture to segregate data between different users and repositories.

-   **Pinecone Namespaces**:
    -   **Construction**: The Pinecone vector database is organized into **namespaces**. Each namespace corresponds to a specific repository. The namespace is constructed from the repository slug (e.g., `owner/repo`).
    -   **Benefit**: This ensures that when the system is searching for related architectural decisions, the search is scoped to the correct repository, preventing data from one repository from "leaking" into the search results of another.

-   **Database Schema**:
    -   **Multi-Tenancy**: The Supabase (Postgres) database schema is designed for multi-tenancy. Most tables have `repository_slug` and `installation_id` columns to scope the data.
    -   **Row-Level Security (RLS)**: The application uses Supabase's Row-Level Security (RLS) feature to enforce data access policies at the database level. This ensures that users can only access data that they are authorized to see.

## 3. Agent and Prompt Management

The AI agents and the prompts used to interact with them are a core part of the application's functionality. This area presents some unique challenges.

-   **Prompt Quality vs. Optimization**:
    -   **Current State**: The prompts used for the AI agents (e.g., in `orchestrator.js`) have been carefully crafted to produce high-quality, accurate output. They are not, however, optimized for performance or cost.
    -   **Challenge**: When building the new system, care must be taken when updating or optimizing these prompts. Any changes could have a significant impact on the quality of the output from the LLMs.

-   **Lack of Evaluation Framework**:
    -   **Current State**: There is currently **no evaluation framework** in place for the AI agents.
    -   **Challenge**: This is a critical gap. Without an evaluation framework, it is impossible to quantitatively measure the impact of changes to the prompts, models, or other parts of the system. This makes it very difficult to iterate on the AI-powered features in a data-driven way. The new team should prioritize building an evaluation framework to be able to test and compare the performance of different agent and prompt versions.

## 4. Decision Relationship Analysis Fragility

The process for analyzing the relationships between architectural decisions is complex and has several points of fragility that should be addressed.

-   **Multi-Step Heuristic Process**:
    -   **Current State**: The `analyzeRelationships` function in `orchestrator.js` uses a multi-step process to find related decisions. It combines three different methods:
        1.  **Semantic Search (`queryRAGForRelationship`)**: Finds decisions that are semantically similar based on their text embeddings.
        2.  **File Overlap Search (`findDecisionsByFileOverlap`)**: Finds decisions that share one or more related files.
        3.  **Domain Concept Overlap (`findDecisionsByDomainConcepts`)**: Finds decisions that share common domain concepts.
    -   **Challenge**: This process is complex and can be brittle. The final list of related decisions is a de-duplicated combination of the results from these three methods, which could lead to inconsistent or unpredictable results. A simpler, more robust method would be preferable.

-   **Reliance on LLM for Final Analysis**:
    -   **Current State**: After gathering a list of potentially related decisions, the system sends this list back to an LLM to determine the final relationship type (e.g., "supersedes", "extends", "conflicts with").
    -   **Challenge**: This is a potential point of failure. If the LLM prompt is not well-tuned, or if the model's output format is inconsistent, the relationship analysis could fail or produce inaccurate results. This also adds extra latency and cost to the process.

-   **Pinecone Metadata Query Limitations**:
    -   **Current State**: The file and domain concept overlap searches are implemented by querying Pinecone's metadata. For example, the file overlap search uses a filter like `{ related_files: { $in: [...] } }`.
    -   **Challenge**: While this works, it's not the most efficient or scalable way to perform these kinds of queries. Pinecone is optimized for vector-based similarity search, not complex metadata filtering. A relational database would be better suited for this kind of query. A hybrid approach, where Pinecone is used for the initial semantic search and a relational database is used for filtering, would be more robust.

## 5. Design Document Storage and Auditability

-   **Current State**: Design documents generated by the wizard are stored in the database as rich-text content.
-   **Challenge**: As the number and size of design documents grow, large blobs in the database complicate versioning, diffing, and human review workflows. This limits auditability and collaboration (e.g., code-review-like feedback, line-by-line diffs).
-   **Recommendation**: Store design documents in a dedicated Git repository (per tenant or per source repository) as Markdown. Use pull requests for review and discussion, while keeping only metadata, pointers (repository, path, commit SHA), and indexing data in the database.
-   **Operational Considerations**:
    - If adopting repo storage, the GitHub App would need write access to the target docs repository (Repository Contents: Read & write) limited to that repo.
    - Emit commits on publish/update and link back to the app UI. Mirror a canonical pointer in the DB for fast lookup.
-   **Migration Path**: Export existing documents from the database into files with provenance metadata and backfill them into the repository with an initial commit history. 