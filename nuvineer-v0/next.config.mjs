/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone output for Docker optimization
  output: 'standalone',

  // Disable ESLint during builds to prevent build failures from warnings
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Your existing Next.js config might be here
  async rewrites() {
    return [
      {
        source: '/analysis/:path*',
        destination: '/', // Route /analysis/* to be handled by src/app/page.tsx
      },
    ];
  },
};

export default nextConfig; 