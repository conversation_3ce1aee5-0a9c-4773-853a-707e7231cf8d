import { jest } from '@jest/globals';
import { extractKnowledge } from '../src/analyzer/index.js';

// Mock the Anthropic client
jest.mock('@anthropic-ai/sdk', () => {
  return {
    Anthropic: jest.fn().mockImplementation(() => {
      return {
        messages: {
          create: jest.fn().mockResolvedValue({
            content: [
              {
                text: `\`\`\`json
{
  "architectural_decisions": [
    {
      "title": "Test Decision",
      "description": "This is a test architectural decision.",
      "rationale": "We made this decision for testing purposes.",
      "implications": "This will impact testing.",
      "alternatives": "We could have done it differently.",
      "related_files": ["src/test.js"]
    }
  ]
}
\`\`\``
              }
            ]
          })
        }
      };
    })
  };
});

// Mock environment variables
process.env.ANTHROPIC_API_KEY = 'test-key';

describe('Analyzer', () => {
  test('extractKnowledge should return formatted architectural decisions', async () => {
    const pr = {
      number: 123,
      title: 'Test PR',
      body: 'Test PR description',
      author: 'testuser',
      created_at: '2023-01-01T00:00:00Z',
      html_url: 'https://github.com/test/repo/pull/123'
    };

    const files = [
      {
        filename: 'src/test.js',
        status: 'modified',
        additions: 10,
        deletions: 5,
        content: 'console.log("test");'
      }
    ];

    const comments = [
      {
        author: 'reviewer',
        body: 'This looks good!'
      }
    ];

    const result = await extractKnowledge(pr, files, comments);

    expect(result).toHaveProperty('architectural_decisions');
    expect(result.architectural_decisions).toHaveLength(1);
    expect(result.architectural_decisions[0].title).toBe('Test Decision');
    expect(result).toHaveProperty('metadata');
    expect(result.metadata.pr_number).toBe(123);
  });
});
