services:
  nuvineer-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      # Next.js / Vercel Environment Variables
      - NODE_ENV=production
      - NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL:-http://localhost:3000}
      - CRON_SECRET=${CRON_SECRET}
      - MAIN_BRANCH_NAME=${MAIN_BRANCH_NAME:-main}
      
      # Supabase Configuration
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      
      # Authentication & Authorization
      - NEXT_PUBLIC_ALLOWED_EMAILS=${NEXT_PUBLIC_ALLOWED_EMAILS}
      - NEXT_PUBLIC_ALLOWED_GITHUB_IDS=${NEXT_PUBLIC_ALLOWED_GITHUB_IDS}
      
      # GitHub App Configuration
      - GITHUB_APP_ID=${GITHUB_APP_ID}
      - GITHUB_APP_PRIVATE_KEY=${GITHUB_APP_PRIVATE_KEY}
      - GITHUB_WEBHOOK_SECRET=${GITHUB_WEBHOOK_SECRET}
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      
      # AI/LLM Services
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ANTHROPIC_MODEL=${ANTHROPIC_MODEL:-claude-sonnet-4-20250514}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # Vector Database (Pinecone)
      - PINECONE_API_KEY=${PINECONE_API_KEY}
      - PINECONE_INDEX_NAME=${PINECONE_INDEX_NAME:-architecture-decisions}
      
      # Database Configuration (if using external Postgres)
      - POSTGRES_URL=${POSTGRES_URL}
      - POSTGRES_URL_NON_POOLING=${POSTGRES_URL_NON_POOLING}
      - POSTGRES_PRISMA_URL=${POSTGRES_PRISMA_URL}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_DATABASE=${POSTGRES_DATABASE}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      
      # Feature Flags
      - ENABLE_RELATIONSHIP_ANALYSIS=${ENABLE_RELATIONSHIP_ANALYSIS:-true}
      
      # Debugging
      - DEBUG=${DEBUG:-false}
      
      # Doppler Configuration (if using Doppler for secrets management)
      - DOPPLER_PROJECT=${DOPPLER_PROJECT}
      - DOPPLER_CONFIG=${DOPPLER_CONFIG}
      - DOPPLER_ENVIRONMENT=${DOPPLER_ENVIRONMENT}
    
    # Health check to ensure the app is running
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Restart policy
    restart: unless-stopped
