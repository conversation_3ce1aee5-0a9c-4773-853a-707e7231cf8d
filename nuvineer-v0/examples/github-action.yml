name: Extract Architectural Knowledge

on:
  pull_request:
    types: [closed]
    branches: [main]

jobs:
  extract-knowledge:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm install -g archknow
          
      - name: Extract architectural knowledge
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        run: |
          archknow analyze ${{ github.repository }} --pr ${{ github.event.pull_request.number }} --output ./docs/architecture
          
      - name: Commit and push changes
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add ./docs/architecture
          git commit -m "docs: add architectural knowledge from PR #${{ github.event.pull_request.number }}" || echo "No changes to commit"
          git push
