# Docker Deployment Guide

This guide explains how to build and run the Nuvineer Next.js application using Docker.

## Prerequisites

- Docker installed on your system
- Docker Compose installed (usually comes with Docker Desktop)
- All required environment variables configured

## Quick Start

### 1. Environment Setup

Copy the example environment file and configure your variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your actual values. See `docs/ENVIRONMENT_VARIABLES.md` for detailed descriptions of each variable.

### 2. Build and Run with Docker Compose

```bash
# Build and start the application
docker-compose up --build

# Or run in detached mode (background)
docker-compose up --build -d
```

The application will be available at `http://localhost:3000`.

### 3. Stop the Application

```bash
# Stop the application
docker-compose down

# Stop and remove volumes (if any)
docker-compose down -v
```

## Manual Docker Commands

If you prefer to use Docker directly without Docker Compose:

### Build the Image

```bash
docker build -t nuvineer-app .
```

### Run the Container

```bash
docker run -p 3000:3000 --env-file .env nuvineer-app
```

## Production Deployment

### Environment Variables

For production deployment, ensure you have:

1. **Required Services**: Supabase, GitHub App, Anthropic/OpenAI API keys, Pinecone
2. **Security**: Strong secrets for `CRON_SECRET`, `GITHUB_WEBHOOK_SECRET`
3. **URLs**: Correct `NEXT_PUBLIC_BASE_URL` for your domain
4. **Authentication**: Proper `NEXT_PUBLIC_ALLOWED_EMAILS` and `NEXT_PUBLIC_ALLOWED_GITHUB_IDS`

### Docker Compose Override

For production, create a `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  nuvineer-app:
    restart: always
    environment:
      - NODE_ENV=production
    # Add any production-specific configurations
```

Run with:

```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## Health Checks

The Docker Compose configuration includes health checks. You can check the status:

```bash
docker-compose ps
```

## Logs

View application logs:

```bash
# View logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f

# View logs for specific service
docker-compose logs nuvineer-app
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**: Change the port mapping in `docker-compose.yml` from `3000:3000` to `3001:3000`

2. **Environment Variables Not Loading**: Ensure your `.env` file is in the same directory as `docker-compose.yml`

3. **Build Failures**: Check that all dependencies in `package.json` are compatible with Node.js 18

4. **Database Connection Issues**: Verify your Supabase/Postgres connection strings and credentials

### Debug Mode

To run with debug logging:

```bash
# Set DEBUG=true in your .env file, then:
docker-compose up --build
```

## Performance Optimization

The Dockerfile uses multi-stage builds and Next.js standalone output for optimal performance:

- **Multi-stage build**: Reduces final image size
- **Standalone output**: Includes only necessary files
- **Alpine Linux**: Lightweight base image
- **Non-root user**: Security best practice

## Scaling

For horizontal scaling, you can run multiple instances:

```bash
docker-compose up --scale nuvineer-app=3
```

Note: You'll need a load balancer in front of multiple instances.

## Security Considerations

1. **Secrets Management**: Consider using Docker secrets or external secret management
2. **Network Security**: Use custom networks for multi-container setups
3. **User Permissions**: The container runs as a non-root user
4. **Environment Variables**: Never commit `.env` files with real credentials

## Monitoring

Add monitoring tools to your Docker Compose setup:

```yaml
# Example monitoring service (add to docker-compose.yml)
  monitoring:
    image: prom/prometheus
    ports:
      - "9090:9090"
```

## Backup

If using volumes for persistent data, ensure regular backups:

```bash
# Backup volumes
docker run --rm -v nuvineer_data:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz /data
```
