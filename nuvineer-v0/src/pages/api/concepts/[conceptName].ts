import type { NextApiRequest, NextApiResponse } from 'next';
import { Pinecone } from '@pinecone-database/pinecone';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';

// Helper to safely get env vars
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value && defaultValue === null) {
        throw new Error(`Environment variable ${name} not found and no default value was provided.`);
    }
    return value || defaultValue!;
}

// Initialize Pinecone
const pinecone = new Pinecone({
  apiKey: getEnvVar('PINECONE_API_KEY'),
});
const pineconeIndexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');
const pineconeIndex = pinecone.Index(pineconeIndexName);

// ---> ADDED: Dedicated index for concepts
const conceptsIndexName = getEnvVar('PINECONE_CONCEPTS_INDEX_NAME', 'architecture-concepts');
const conceptsIndex = pinecone.Index(conceptsIndexName);
// --- END ADDED


interface ConceptDetails {
    concept: {
        name: string;
    };
    decisions: any[]; // Define a proper Decision interface later
    related_concepts: { name: string; score: number }[];
}

interface ErrorResponse {
    error: string;
    message: string;
}

// const CONCEPT_NAMESPACE = 'repository-concepts'; // No longer a single global namespace

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ConceptDetails | ErrorResponse>
) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({
            error: 'Method Not Allowed',
            message: `Method ${req.method} is not allowed.`,
        });
    }

    const { repositorySlug, conceptName, installationId, isPublic } = req.query;

    if (!repositorySlug || typeof repositorySlug !== 'string' || !conceptName || typeof conceptName !== 'string') {
        return res.status(400).json({
            error: 'Bad Request',
            message: 'repositorySlug and conceptName query parameters are required.',
        });
    }

    if (isPublic === undefined || (isPublic === 'false' && !installationId)) {
        return res.status(400).json({
            error: 'Bad Request',
            message: 'isPublic and installationId (for private repos) are required.',
        });
    }

    try {
        const effectiveInstallationId = isPublic === 'true' ? 0 : parseInt(installationId as string, 10);
        const namespace = getRepositoryNamespace(effectiveInstallationId, repositorySlug);

        // Decode the concept name to ensure it's properly handled
        const decodedConceptName = decodeURIComponent(conceptName);
        console.log(`[API /concepts] Raw conceptName from URL: '${conceptName}'`);
        console.log(`[API /concepts] Decoded conceptName: '${decodedConceptName}'`);
        console.log(`[API /concepts] Fetching details for concept '${decodedConceptName}' in namespace '${namespace}'`);

        // --- 1. Fetch Decisions for this Concept ---
        const dummyEmbedding = new Array(1536).fill(0); // Assuming OpenAI 'text-embedding-3-small'
        
        console.log(`[API /concepts] Querying decisions with filter: domain_concepts contains '${decodedConceptName}'`);
        const decisionsResponse = await pineconeIndex
            .namespace(namespace)
            .query({
                vector: dummyEmbedding,
                topK: 100,
                filter: {
                    '$and': [
                        { domain_concepts: { '$in': [decodedConceptName] } },
                        { is_superseded: { '$ne': true } }
                    ]
                },
                includeMetadata: true,
            });

        console.log(`[API /concepts] Found ${decisionsResponse.matches?.length || 0} decisions for concept '${decodedConceptName}'`);
        const decisions = decisionsResponse.matches?.map(match => ({
            id: match.id,
            ...match.metadata
        })) || [];


        // --- 2. Fetch Related Concepts ---
        const conceptsNamespace = conceptsIndex.namespace(namespace);

        console.log(`[API /concepts] Fetching concept vector for '${decodedConceptName}' from concepts index`);
        const conceptVectorResponse = await conceptsNamespace.fetch([decodedConceptName]);

        console.log(`[API /concepts] Concept vector fetch result: found=${!!conceptVectorResponse.records?.[decodedConceptName]}`);
        const conceptVector = conceptVectorResponse.records?.[decodedConceptName]?.values;

        let related_concepts: { name: string; score: number }[] = [];
        if (conceptVector) {
            console.log(`[API /concepts] Querying for related concepts using vector for '${decodedConceptName}'`);
            const relatedConceptsResponse = await conceptsNamespace
                .query({
                    vector: conceptVector,
                    topK: 6, // The concept itself + 5 related
                    includeMetadata: true,
                });
            
            console.log(`[API /concepts] Found ${relatedConceptsResponse.matches?.length || 0} related concepts`);
            related_concepts = relatedConceptsResponse.matches
                ?.filter(match => match.id !== decodedConceptName)
                .map(match => ({
                    name: match.id,
                    score: match.score || 0,
                })) || [];
        } else {
            console.warn(`[API /concepts] Vector for concept '${decodedConceptName}' not found in namespace '${namespace}' of index '${conceptsIndexName}'. Cannot fetch related concepts.`);
        }


        // --- 3. Combine and Respond ---
        const responseData: ConceptDetails = {
            concept: {
                name: decodedConceptName,
            },
            decisions,
            related_concepts,
        };

        console.log(`[API /concepts] Returning response for '${decodedConceptName}': ${decisions.length} decisions, ${related_concepts.length} related concepts`);
        return res.status(200).json(responseData);

    } catch (error: any) {
        console.error(`[API /concepts] Error fetching details for concept '${conceptName}' in repo '${repositorySlug}':`, error);
        return res.status(500).json({
            error: 'Internal Server Error',
            message: error.message || 'An unknown error occurred.',
        });
    }
} 