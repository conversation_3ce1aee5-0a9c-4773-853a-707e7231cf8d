import type { NextApiRequest, NextApiResponse } from 'next';
import { Pinecone } from '@pinecone-database/pinecone';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';

// Helper to safely get env vars
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value && defaultValue === null) {
        throw new Error(`Environment variable ${name} not found and no default value was provided.`);
    }
    return value || defaultValue!;
}

// Initialize Pinecone
const pinecone = new Pinecone({
  apiKey: getEnvVar('PINECONE_API_KEY'),
});
const pineconeIndexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');
const pineconeIndex = pinecone.Index(pineconeIndexName);

interface ConceptResponse {
  name: string;
  // TODO: Add decision_count in a more performant way later
}

interface ErrorResponse {
  error: string;
  message: string;
}

const MAX_DECISIONS_TO_SCAN = 5000; // Safety limit for the query

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ConceptResponse[] | ErrorResponse>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      error: 'Method Not Allowed',
      message: `Method ${req.method} is not allowed.`,
    });
  }

  const { repositorySlug, installationId, isPublic } = req.query;

  if (!repositorySlug || typeof repositorySlug !== 'string') {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'repositorySlug query parameter is required.',
    });
  }
  if (isPublic === undefined || (isPublic === 'false' && !installationId)) {
    return res.status(400).json({ 
        error: 'Bad Request',
        message: 'isPublic and installationId (for private repos) are required.'
    });
  }

  try {
    const effectiveInstallationId = isPublic === 'true' ? 0 : parseInt(installationId as string, 10);
    const namespace = getRepositoryNamespace(effectiveInstallationId, repositorySlug);
    
    console.log(`[API /concepts] Fetching concepts for repo ${repositorySlug} in namespace ${namespace}`);

    // Fetch all decision IDs from the main index for the given namespace
    const decisionsNamespace = pineconeIndex.namespace(namespace);
    let allIds: string[] = [];
    let paginationToken: string | undefined;
    do {
      const listResponse = await decisionsNamespace.listPaginated({ paginationToken });
      if (listResponse.vectors) {
        // Filter out superseded decisions at the metadata fetching step
        allIds.push(...listResponse.vectors.map((v: any) => v.id));
      }
      paginationToken = listResponse.pagination?.next;
    } while (paginationToken);

    if (allIds.length === 0) {
      console.log(`[API /concepts] No decisions found in namespace '${namespace}'.`);
      return res.status(200).json([]);
    }

    // Fetch metadata for all decisions in batches and aggregate concepts
    const conceptsSet = new Set<string>();
    const batchSize = 100;
    for (let i = 0; i < allIds.length; i += batchSize) {
        const batchIds = allIds.slice(i, i + batchSize);
        const fetchResponse = await decisionsNamespace.fetch(batchIds);
        
        Object.values(fetchResponse.records).forEach(record => {
            // Only add concepts from non-superseded decisions
            if (record.metadata?.is_superseded !== true) {
                const concepts = record.metadata?.domain_concepts;
                if (Array.isArray(concepts)) {
                    concepts.forEach(concept => {
                        if (typeof concept === 'string' && concept.trim()) {
                            conceptsSet.add(concept.trim());
                        }
                    });
                }
            }
        });
    }
    
    const uniqueConcepts = Array.from(conceptsSet).sort();
    
    const response: ConceptResponse[] = uniqueConcepts.map(name => ({ name }));

    return res.status(200).json(response);

  } catch (error: any) {
    console.error(`[API /concepts] General error for repo ${repositorySlug}:`, error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: error.message || 'An unknown error occurred.',
    });
  }
} 