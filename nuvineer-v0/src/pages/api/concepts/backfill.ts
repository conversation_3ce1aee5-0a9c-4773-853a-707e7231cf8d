import type { NextApiRequest, NextApiResponse } from 'next';
import { Pinecone } from '@pinecone-database/pinecone';
import OpenAI from 'openai';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';

// Helper to safely get env vars
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value && defaultValue === null) {
        throw new Error(`Environment variable ${name} not found and no default value was provided.`);
    }
    return value || defaultValue!;
}

// Initialize clients
const openai = new OpenAI({
  apiKey: getEnvVar('OPENAI_API_KEY'),
});
const pinecone = new Pinecone({
  apiKey: getEnvVar('PINECONE_API_KEY'),
});
const decisionsIndex = pinecone.Index(getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions'));
const conceptsIndexName = getEnvVar('PINECONE_CONCEPTS_INDEX_NAME', 'architecture-concepts');
const conceptsIndex = pinecone.Index(conceptsIndexName);

/**
 * Generates an embedding using OpenAI API.
 * This is a copy of the function in orchestrator.js to make this endpoint self-contained.
 * @param {string} text - The text to embed.
 * @param {string} model - The OpenAI embedding model to use.
 * @returns {Promise<number[]>} - The embedding vector.
 */
async function generateEmbedding(text: string, model="text-embedding-3-small"): Promise<number[]> {
    const callId = `embedding-call-${Date.now()}`;
    console.log(`[${callId}] [Embedding] Generating OpenAI embedding using ${model}`);
    try {
        const response = await openai.embeddings.create({
            model: model,
            input: text.replace(/\n/g, ' '), // API recommends replacing newlines
            encoding_format: "float",
        });
        const embedding = response.data[0].embedding;
        console.log(`[${callId}] [Embedding] Successfully generated embedding vector (length: ${embedding?.length})`);
        if (!embedding) {
            throw new Error('OpenAI embedding generation returned no result.');
        }
        return embedding;
    } catch (error) {
        console.error(`[${callId}] [Embedding] Error generating OpenAI embedding:`, error);
        throw new Error(`OpenAI embedding generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
}

const MAX_DECISIONS_TO_SCAN = 10000; // Safety limit for the query

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }

  const { repositorySlug, installationId, isPublic } = req.body;

  if (!repositorySlug || typeof repositorySlug !== 'string') {
    return res.status(400).json({ message: 'repositorySlug is required in the request body.' });
  }
  if (isPublic === undefined || (isPublic === false && !installationId)) {
    return res.status(400).json({ message: 'isPublic and installationId (for private repos) are required.' });
  }

  try {
    const effectiveInstallationId = isPublic ? 0 : parseInt(installationId, 10);
    const namespace = getRepositoryNamespace(effectiveInstallationId, repositorySlug);

    console.log(`[Backfill Concepts] Starting backfill for repository: ${repositorySlug} in namespace: ${namespace}`);
    
    const decisionsNamespace = decisionsIndex.namespace(namespace);

    // 1. Fetch all decision IDs from the main index for the given namespace
    let allIds: string[] = [];
    let paginationToken: string | undefined;
    do {
      const listResponse = await decisionsNamespace.listPaginated({
        paginationToken,
      });
      if (listResponse.vectors) {
        allIds.push(...listResponse.vectors.map((v: any) => v.id));
      }
      paginationToken = listResponse.pagination?.next;
    } while (paginationToken);

    if (allIds.length === 0) {
      console.log(`[Backfill Concepts] No decisions found in namespace '${namespace}'. Nothing to backfill.`);
      return res.status(200).json({ message: 'No concepts found to backfill.', count: 0 });
    }
    console.log(`[Backfill Concepts] Found ${allIds.length} total decisions in namespace. Fetching metadata...`);

    // 2. Fetch metadata for all decisions in batches
    const conceptsSet = new Set<string>();
    const batchSize = 100; // Pinecone fetch limit is 1000, but smaller batches are safer
    for (let i = 0; i < allIds.length; i += batchSize) {
      const batchIds = allIds.slice(i, i + batchSize);
      const fetchResponse = await decisionsNamespace.fetch(batchIds);
      
      Object.values(fetchResponse.records).forEach(record => {
        const concepts = record.metadata?.domain_concepts;
        if (Array.isArray(concepts)) {
          concepts.forEach(concept => {
            if (typeof concept === 'string' && concept.trim()) {
              conceptsSet.add(concept.trim());
            }
          });
        }
      });
    }

    const uniqueConcepts = Array.from(conceptsSet);
    console.log(`[Backfill Concepts] Found ${uniqueConcepts.length} unique concepts for ${repositorySlug}.`);

    if (uniqueConcepts.length === 0) {
        return res.status(200).json({ message: 'No concepts found to backfill.', count: 0 });
    }

    // 3. Generate embeddings and prepare for upsert in batches
    console.log(`[Backfill Concepts] Processing ${uniqueConcepts.length} concepts in batches.`);
    const upsertBatchSize = 10; // Process 10 concepts at a time
    let totalUpsertedCount = 0;

    for (let i = 0; i < uniqueConcepts.length; i += upsertBatchSize) {
      const batchConcepts = uniqueConcepts.slice(i, i + upsertBatchSize);
      console.log(`[Backfill Concepts] Processing batch ${Math.floor(i / upsertBatchSize) + 1} of ${Math.ceil(uniqueConcepts.length / upsertBatchSize)} (${batchConcepts.length} concepts).`);
      
      const vectorsToUpsert = [];
      for (const conceptName of batchConcepts) {
          try {
              const embedding = await generateEmbedding(conceptName);
              vectorsToUpsert.push({
                  id: conceptName,
                  values: embedding,
              });
          } catch (error) {
              console.error(`[Backfill Concepts] Failed to generate embedding for '${conceptName}'. Skipping.`, error);
          }
      }

      // 4. Batch upsert to the concepts index
      if (vectorsToUpsert.length > 0) {
          console.log(`[Backfill Concepts] Upserting batch of ${vectorsToUpsert.length} concept vectors to index '${conceptsIndexName}' in namespace '${namespace}'.`);
          await conceptsIndex.namespace(namespace).upsert(vectorsToUpsert);
          console.log(`[Backfill Concepts] Successfully upserted batch of ${vectorsToUpsert.length} concepts.`);
          totalUpsertedCount += vectorsToUpsert.length;
      }
    }

    return res.status(200).json({
        message: `Successfully backfilled ${totalUpsertedCount} concepts for repository ${repositorySlug}.`,
        count: totalUpsertedCount,
    });

  } catch (error: any) {
    console.error(`[Backfill Concepts] Error during backfill for ${repositorySlug}:`, error);
    return res.status(500).json({
      message: 'Internal Server Error',
      error: error.message || 'An unknown error occurred.',
    });
  }
} 