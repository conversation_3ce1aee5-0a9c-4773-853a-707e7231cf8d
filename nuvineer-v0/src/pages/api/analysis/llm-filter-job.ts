import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { callLightweightLlmForSignificance } from '@/lib/llmUtils';
import { getOctokit, getAuthToken, checkRateLimit } from '@/lib/github-auth';

// Initialize Supabase client
const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

// Security check for cron job
const CRON_SECRET_VALUE = process.env.CRON_SECRET;

interface LlmFilterResponse {
  action: 'processed_items' | 'no_action' | 'error';
  message: string;
  details?: any;
}

interface PendingEarlyStageItem {
  id: number;
  repository_slug: string;
  installation_id: number;
  pr_number: number;
  pr_title: string;
  pr_url: string;
  pr_created_at: string;
  pr_merged_at: string;
  is_pseudo_pr: boolean;
  commit_sha?: string;
}

/**
 * Get the next batch of pending_early_stage items for LLM filtering
 */
async function getNextPendingEarlyStageItems(batchSize: number = 10): Promise<PendingEarlyStageItem[]> {
  try {
    const { data: items, error } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .select('*')
      .eq('status', 'pending_early_stage')
      .order('pr_merged_at', { ascending: false }) // Process newest first
      .limit(batchSize);

    if (error) {
      console.error('[LLM Filter Job] Error fetching pending_early_stage items:', error);
      return [];
    }

    return items || [];
  } catch (error) {
    console.error('[LLM Filter Job] Error in getNextPendingEarlyStageItems:', error);
    return [];
  }
}

/**
 * Extract file context from GitHub API file data
 */
function extractFileContext(files: any[]) {
  const fileNames = files.map(file => file.filename);
  const directories = [...new Set(fileNames.map(filename => {
    const parts = filename.split('/');
    return parts.length > 1 ? parts.slice(0, -1).join('/') : '.';
  }))].filter(dir => dir !== '.');
  
  const fileTypes = [...new Set(fileNames.map(filename => {
    const ext = filename.split('.').pop();
    return ext ? `.${ext}` : 'no-extension';
  }))];
  
  const totalAdditions = files.reduce((sum, file) => sum + (file.additions || 0), 0);
  const totalDeletions = files.reduce((sum, file) => sum + (file.deletions || 0), 0);
  
  return {
    filesChanged: files.length,
    totalAdditions,
    totalDeletions,
    directories,
    fileNames,
    fileTypes
  };
}

/**
 * Process a single item through LLM filtering
 */
async function processItemWithLlm(item: PendingEarlyStageItem): Promise<{ success: boolean; newStatus: string; error?: string }> {
  try {
    console.log(`[LLM Filter Job] Processing ${item.is_pseudo_pr ? 'commit' : 'PR'} #${item.pr_number} from ${item.repository_slug}`);

    // Prepare content for LLM assessment
    let itemDetails = `Title: ${item.pr_title}\n\nDescription: No description provided.`;
    let fileContext = undefined;
    
    // For commits, we can include more context if available
    if (item.is_pseudo_pr && item.commit_sha) {
      itemDetails = `Commit: ${item.commit_sha.substring(0, 7)}\nTitle: ${item.pr_title}\n\nDescription: Direct commit to repository.`;
      
      // Fetch file context for commits
      try {
        const [owner, repo] = item.repository_slug.split('/');
        const effectiveInstallationId = item.installation_id === 0 ? undefined : item.installation_id.toString();
        
        // Get GitHub client
        const octokit = await getOctokit(effectiveInstallationId);
        
        // Fetch commit details including file changes
        const { data: commitDetail } = await octokit.repos.getCommit({
          owner,
          repo,
          ref: item.commit_sha
        });
        
        if (commitDetail.files && commitDetail.files.length > 0) {
          fileContext = extractFileContext(commitDetail.files);
          console.log(`[LLM Filter Job] Fetched file context for commit ${item.commit_sha.substring(0, 7)}: ${fileContext.filesChanged} files, ${fileContext.totalAdditions}+ ${fileContext.totalDeletions}- lines`);
        }
      } catch (fileError) {
        console.warn(`[LLM Filter Job] Could not fetch file context for commit ${item.commit_sha.substring(0, 7)}:`, fileError);
        // Continue without file context - this is not a critical failure
      }
    } else {
      // For PRs, try to fetch file context too
      try {
        const [owner, repo] = item.repository_slug.split('/');
        const effectiveInstallationId = item.installation_id === 0 ? undefined : item.installation_id.toString();
        
        // Get GitHub client
        const octokit = await getOctokit(effectiveInstallationId);
        
        // Fetch PR files
        const prNumber = parseInt(String(item.pr_number), 10);
        const { data: files } = await octokit.pulls.listFiles({
          owner,
          repo,
          pull_number: prNumber
        });
        
        if (files && files.length > 0) {
          fileContext = extractFileContext(files);
          console.log(`[LLM Filter Job] Fetched file context for PR #${prNumber}: ${fileContext.filesChanged} files, ${fileContext.totalAdditions}+ ${fileContext.totalDeletions}- lines`);
        }
      } catch (fileError) {
        console.warn(`[LLM Filter Job] Could not fetch file context for PR #${item.pr_number}:`, fileError);
        // Continue without file context - this is not a critical failure
      }
    }

    // Call LLM for significance assessment with file context
    const significanceResult = await callLightweightLlmForSignificance(itemDetails, fileContext);
    const isSignificant = significanceResult.sig === 1;
    
    const newStatus = isSignificant ? 'pending' : 'skipped_not_significant';
    
    const contextSummary = fileContext ? 
      `${fileContext.filesChanged} files, ${fileContext.directories.slice(0, 3).join(', ')}` : 
      'no file context';
    
    console.log(`[LLM Filter Job] ${item.is_pseudo_pr ? 'Commit' : 'PR'} #${item.pr_number} (${contextSummary}) - LLM result: ${isSignificant ? 'SIGNIFICANT (→ pending)' : 'NOT SIGNIFICANT (→ skipped_not_significant)'}`);
    
    return { success: true, newStatus };
  } catch (error) {
    console.error(`[LLM Filter Job] Error processing item #${item.pr_number}:`, error);
    // On error, mark as pending (fail-safe: include rather than exclude)
    return { success: true, newStatus: 'pending', error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Update item status in database
 */
async function updateItemStatus(item: PendingEarlyStageItem, newStatus: string, error?: string): Promise<boolean> {
  try {
    const updateData: any = {
      status: newStatus,
      last_analyzed_at: new Date().toISOString()
    };

    if (error) {
      updateData.analysis_error = error;
    }

    const { error: updateError } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .update(updateData)
      .eq('repository_slug', item.repository_slug)
      .eq('pr_number', item.pr_number)
      .eq('installation_id', item.installation_id);

    if (updateError) {
      console.error(`[LLM Filter Job] Error updating status for item #${item.pr_number}:`, updateError);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`[LLM Filter Job] Error in updateItemStatus for item #${item.pr_number}:`, error);
    return false;
  }
}

/**
 * Process a batch of items in parallel
 */
async function processBatch(items: PendingEarlyStageItem[]): Promise<{ processed: number; successful: number; failed: number; pending: number; skipped: number }> {
  console.log(`[LLM Filter Job] Processing batch of ${items.length} items...`);

  const results = await Promise.all(items.map(async (item) => {
    // Process with LLM
    const llmResult = await processItemWithLlm(item);
    
    // Update database
    const updateSuccess = await updateItemStatus(item, llmResult.newStatus, llmResult.error);
    
    return {
      item,
      llmResult,
      updateSuccess,
      finalStatus: llmResult.newStatus
    };
  }));

  // Count results
  let successful = 0;
  let failed = 0;
  let pending = 0;
  let skipped = 0;

  for (const result of results) {
    if (result.updateSuccess) {
      successful++;
      if (result.finalStatus === 'pending') {
        pending++;
      } else if (result.finalStatus === 'skipped_not_significant') {
        skipped++;
      }
    } else {
      failed++;
    }
  }

  console.log(`[LLM Filter Job] Batch complete: ${successful} successful updates, ${failed} failed updates`);
  console.log(`[LLM Filter Job] Status distribution: ${pending} → pending, ${skipped} → skipped_not_significant`);

  return {
    processed: items.length,
    successful,
    failed,
    pending,
    skipped
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<LlmFilterResponse>
) {
  console.log(`[LLM Filter Job] Received request: ${req.method}`);

  // Validate secret
  if (CRON_SECRET_VALUE) {
    const providedSecret = req.headers['authorization']?.split(' ')[1] || (req.query.secret as string);
    if (providedSecret !== CRON_SECRET_VALUE) {
      console.warn("[LLM Filter Job] Unauthorized attempt: Incorrect or missing secret.");
      return res.status(401).json({
        action: 'error',
        message: 'Unauthorized: Incorrect or missing secret.'
      });
    }
  } else {
    console.warn("[LLM Filter Job] WARNING: CRON_SECRET environment variable is not set.");
  }

  // Check GitHub rate limit before starting any work
  try {
    const octokit = await getOctokit(); // Get a generic octokit instance for rate limit check
    const rateLimitStatus = await checkRateLimit(octokit, 100); // Lower threshold for the lightweight job
    if (rateLimitStatus.isLimited) {
      const message = `GitHub rate limit is low (${rateLimitStatus.remaining} remaining). Pausing job until ${rateLimitStatus.resetTime.toLocaleTimeString()}.`;
      console.warn(`[LLM Filter Job] ${message}`);
      return res.status(200).json({
        action: 'no_action',
        message: message,
        details: { reason: 'RATE_LIMIT_PAUSE' }
      });
    }
  } catch (error) {
    console.warn(`[LLM Filter Job] Could not check rate limit, proceeding with caution:`, error);
  }

  if (req.method !== 'POST' && req.method !== 'GET') {
    res.setHeader('Allow', ['POST', 'GET']);
    return res.status(405).json({
      action: 'error',
      message: `Method ${req.method} Not Allowed`
    });
  }

  // Main logic
  try {
    // Get batch of pending_early_stage items
    const BATCH_SIZE = 20; // Process 20 items in parallel
    const pendingItems = await getNextPendingEarlyStageItems(BATCH_SIZE);

    if (pendingItems.length === 0) {
      console.log('[LLM Filter Job] No pending_early_stage items found.');
      return res.status(200).json({
        action: 'no_action',
        message: 'No pending_early_stage items to process.'
      });
    }

    console.log(`[LLM Filter Job] Found ${pendingItems.length} pending_early_stage items to process.`);

    // Process batch in parallel
    const batchResults = await processBatch(pendingItems);

    return res.status(200).json({
      action: 'processed_items',
      message: `Processed ${batchResults.processed} items: ${batchResults.pending} marked as pending, ${batchResults.skipped} marked as skipped_not_significant.`,
      details: {
        batch_size: batchResults.processed,
        successful_updates: batchResults.successful,
        failed_updates: batchResults.failed,
        items_marked_pending: batchResults.pending,
        items_marked_skipped: batchResults.skipped
      }
    });

  } catch (error) {
    console.error('[LLM Filter Job] Unhandled error:', error);
    return res.status(500).json({
      action: 'error',
      message: `Unhandled error: ${error instanceof Error ? error.message : String(error)}`
    });
  }
} 