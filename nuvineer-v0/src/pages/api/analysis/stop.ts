import type { NextApiRequest, NextApiResponse } from 'next';
import { requestStopSupabaseJob, Job } from '@/lib/job-service';

type Data = 
  | { success: true; message: string; job?: Job | null }
  | { success: false; error: string };

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Data>
) {
  if (req.method === 'POST') {
    const { jobId } = req.body;

    if (!jobId || typeof jobId !== 'string') {
      return res.status(400).json({ success: false, error: 'jobId is required in the request body and must be a string.' });
    }

    try {
      const result = await requestStopSupabaseJob(jobId as string);

      if (!result.success) {
        return res.status(404).json({ success: false, error: result.message });
      }
      
      console.log(`[API /api/analysis/stop] Stop requested for job ${jobId} (Supabase). Result: ${result.message}`);
      return res.status(200).json({ success: true, message: result.message, job: result.job });

    } catch (error) {
      console.error(`[API /api/analysis/stop] Error stopping job ${jobId} (Supabase):`, error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      return res.status(500).json({ success: false, error: `Failed to stop job: ${errorMessage}` });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
  }
} 