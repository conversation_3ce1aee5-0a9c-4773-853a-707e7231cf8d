import type { NextApiRequest, NextApiResponse } from 'next';
import { getSupabaseJobStatus, Job } from '@/lib/job-service';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ success: boolean; job?: Job | null; error?: string, jobNotFound?: boolean }>
) {
  if (req.method === 'GET') {
    const { jobId } = req.query;

    if (!jobId || typeof jobId !== 'string') {
      return res.status(400).json({ success: false, error: 'Job ID is required.' });
    }

    try {
      const jobStatus = await getSupabaseJobStatus(jobId);

      // Set Cache-Control headers to prevent caching and ensure fresh data
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');

      if (!jobStatus) {
        // Explicitly tell client job not found, rather than just 404 HTML page
        return res.status(404).json({ success: false, error: 'Job not found.', jobNotFound: true });
      }
      return res.status(200).json({ success: true, job: jobStatus });
    } catch (error) {
      console.error(`[API status] Error fetching job status for ${jobId}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      // Don't set cache headers on error, or ensure they are appropriate for error pages
      return res.status(500).json({ success: false, error: errorMessage });
    }
  } else {
    res.setHeader('Allow', ['GET']);
    // Don't set cache headers on error
    return res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
  }
} 