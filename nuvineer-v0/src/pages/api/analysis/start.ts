import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

type Data = 
  | { success: true; message: string; repositorySlug: string; } 
  | { success: false; error: string };

/**
 * This is a lightweight endpoint that creates or resets a repository loading job.
 * It does NOT perform any heavy lifting itself. All data fetching (PRs, commits)
 * is handled by the background /api/analysis/repository-loader-job.
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Data>
) {
  console.log('[API /api/analysis/start] HANDLER REACHED. Method:', req.method, 'Body:', req.body);

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
  }

  const { 
      repositorySlug,
      isPublic,
      installationId,
      testMode
  } = req.body as Partial<{
    repositorySlug: string;
    isPublic: boolean;
    installationId?: string;
    testMode: boolean;
  }>; 

  if (!repositorySlug || typeof isPublic !== 'boolean') {
    return res.status(400).json({ 
      success: false, 
      error: 'Missing required parameters: repositorySlug and isPublic are required.' 
    });
  }
  if (isPublic === false && !installationId) {
      return res.status(400).json({ 
          success: false, 
          error: 'installationId is required for private repositories.' 
      });
  }

  try {
    const jobData = {
      repository_slug: repositorySlug,
      installation_id: isPublic ? 0 : parseInt(installationId!),
      current_phase: 'prs',
      next_page_to_process: 1,
      is_completed: false,
      status: 'pending',
      updated_at: new Date().toISOString()
    };
    
    console.log(`[API /start] Upserting repository loading job for ${repositorySlug}.`, jobData);

    const { error: jobUpsertError } = await supabaseAdmin
        .from('repository_loading_jobs')
        .upsert(jobData, { onConflict: 'repository_slug, installation_id' });
        
    if (jobUpsertError) {
        console.error(`[API /start] Error upserting repository loading job:`, jobUpsertError);
        throw new Error(`Failed to create or reset loading job: ${jobUpsertError.message}`);
    }

    console.log(`[API /start] ✅ Successfully created/reset loading job for ${repositorySlug}.`);

    // In test mode, we still need to convert one item to 'pending' so that the deep analysis job runs.
    // The new loader job will quickly populate one item as 'pending_early_stage', and the LLM filter
    // will pick it up. We need a way to then get that one item to 'pending'.
    // For now, let's rely on the background jobs to handle this. The test mode's primary goal
    // is to kick off the process, which this now does.
    if (testMode) {
        console.log(`[API /start] Test mode enabled. The background jobs will now pick up the loading job.`);
    }

    return res.status(201).json({ 
      success: true, 
      repositorySlug: repositorySlug,
      message: `Repository loading job for ${repositorySlug} has been successfully queued. Processing will begin in the background.`
    });

  } catch (error) {
    console.error('[API /api/analysis/start] Error creating job:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    
    return res.status(500).json({ success: false, error: `Failed to start analysis job: ${errorMessage}` });
  }
} 