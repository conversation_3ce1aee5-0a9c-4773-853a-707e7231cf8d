import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { getOctokit, getAuthToken, checkRateLimit } from '@/lib/github-auth';
import { getRecentCommits, getCommitDetail, createPseudoPrFromCommit, getRecentPRs } from '@/analyzer/github';
import { isDefinitelyNotArchitectural } from '@/lib/heuristics';

const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
const CRON_SECRET_VALUE = process.env.CRON_SECRET;
const BATCH_SIZE = 100;

// #region Type Definitions
interface LoaderResponse {
  action: 'processed_batch' | 'switched_phase' | 'completed_job' | 'no_jobs' | 'error' | 'skipped_concurrency';
  message: string;
  details?: any;
}

interface RepositoryLoadingJob {
  id: number;
  repository_slug: string;
  installation_id: number;
  current_phase: 'prs' | 'commits' | 'completed';
  next_page_to_process: number;
  is_completed: boolean;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error_message?: string;
}
// #endregion

// #region Database Job Management
async function getNextPendingJob(): Promise<RepositoryLoadingJob | null> {
  const { data, error } = await supabaseAdmin
    .from('repository_loading_jobs')
    .select('*')
    .eq('is_completed', false)
    .eq('status', 'pending')
    .order('updated_at', { ascending: true })
    .limit(1)
    .single();

  if (error && error.code !== 'PGRST116') { // Ignore 'No rows found'
    console.error('[LoaderJob] Error getting next pending job:', error);
    return null;
  }
  return data;
}

async function markJobAsProcessing(jobId: number): Promise<boolean> {
  const { error } = await supabaseAdmin
    .from('repository_loading_jobs')
    .update({ status: 'processing', last_processed_at: new Date().toISOString() })
    .eq('id', jobId);
  if (error) {
    console.error(`[LoaderJob] Error marking job ${jobId} as processing:`, error);
    return false;
  }
  return true;
}

async function updateJobProgress(
  jobId: number, 
  updates: Partial<RepositoryLoadingJob>
): Promise<boolean> {
  const { error } = await supabaseAdmin
    .from('repository_loading_jobs')
    .update({ ...updates, status: 'pending', updated_at: new Date().toISOString() })
    .eq('id', jobId);

  if (error) {
    console.error(`[LoaderJob] Error updating job ${jobId} progress:`, error);
    return false;
  }
  return true;
}
// #endregion

// #region Data Processing Logic
async function processPrBatch(job: RepositoryLoadingJob, octokit: any): Promise<{ success: boolean; itemsAdded: number; hasMore: boolean; }> {
    const [owner, repo] = job.repository_slug.split('/');
    console.log(`[LoaderJob][PRs] Processing PRs for ${job.repository_slug}, page ${job.next_page_to_process}`);

    // Get auth token for the getRecentPRs function
    const effectiveInstallationId = job.installation_id === 0 ? undefined : job.installation_id.toString();
    const authToken = await getAuthToken(effectiveInstallationId);

    const result = await getRecentPRs(owner, repo, { 
        per_page: BATCH_SIZE, 
        page: job.next_page_to_process,
        returnPaginationInfo: true
    }, authToken as any);
    
    // Extract pagination info
    const { prs, hasMore: hasMoreFromGitHub, totalFromGitHub, mergedCount, filteredCount } = result as {
        prs: any[];
        hasMore: boolean;
        totalFromGitHub: number;
        mergedCount: number;
        filteredCount: number;
    };
    
    console.log(`[LoaderJob][PRs] Fetched ${totalFromGitHub} total PRs from GitHub, ${mergedCount} merged PRs for ${job.repository_slug}. HasMore: ${hasMoreFromGitHub}`);

    if (prs.length === 0) {
        console.log(`[LoaderJob][PRs] No more PRs found for ${job.repository_slug}.`);
        return { success: true, itemsAdded: 0, hasMore: hasMoreFromGitHub };
    }

    const prItemsToAdd = prs
        .filter(pr => pr.merged_at && !isDefinitelyNotArchitectural(pr.title))
        .map(pr => ({
            installation_id: job.installation_id,
            repository_slug: job.repository_slug,
            pr_number: pr.number,
            pr_title: pr.title.substring(0, 255),
            pr_url: pr.html_url,
            pr_created_at: pr.created_at,
            pr_merged_at: pr.merged_at,
            is_pseudo_pr: false,
            status: 'pending_early_stage',
            commit_sha: pr.merge_commit_sha,
        }));
        
    console.log(`[LoaderJob][PRs] After filtering, ${prItemsToAdd.length} PRs will be upserted.`);

    if (prItemsToAdd.length > 0) {
        // Use upsert to avoid duplicates on re-runs. Conflict on (repo_slug, pr_number, installation_id)
        const { error } = await supabaseAdmin.from('repository_pr_analysis_status').upsert(prItemsToAdd, {
            onConflict: 'repository_slug, pr_number, installation_id',
            ignoreDuplicates: true
        });

        if (error) {
            console.error('[LoaderJob][PRs] Error upserting PRs:', error);
            // Decide if we should fail the whole batch or continue
        } else {
            console.log(`[LoaderJob][PRs] Successfully upserted ${prItemsToAdd.length} PRs.`);
        }

        // Store commit SHAs for each PR to avoid processing them as standalone commits
        const prCommitShas: any[] = [];
        for (const prItem of prItemsToAdd) {
            try {
                console.log(`[LoaderJob][PRs] Fetching commits for PR #${prItem.pr_number}`);
                
                // Fetch commits for this PR with pagination to handle PRs with many commits
                let page = 1;
                let hasMoreCommits = true;
                let totalCommits = 0;
                
                while (hasMoreCommits) {
                    const commits = await octokit.rest.pulls.listCommits({
                        owner,
                        repo,
                        pull_number: prItem.pr_number,
                        per_page: 100,
                        page: page
                    });

                    // Add each commit SHA to our collection
                    for (const commit of commits.data) {
                        prCommitShas.push({
                            repository_slug: job.repository_slug,
                            installation_id: job.installation_id,
                            commit_sha: commit.sha
                        });
                    }

                    totalCommits += commits.data.length;
                    
                    // Check if we have more pages
                    if (commits.data.length < 100) {
                        hasMoreCommits = false;
                    } else {
                        page++;
                    }
                }

                console.log(`[LoaderJob][PRs] Found ${totalCommits} commits for PR #${prItem.pr_number}`);
            } catch (error) {
                console.error(`[LoaderJob][PRs] Error fetching commits for PR #${prItem.pr_number}:`, error);
                // Continue processing other PRs even if one fails
            }
        }

        // Batch insert commit SHAs if we have any
        if (prCommitShas.length > 0) {
            console.log(`[LoaderJob][PRs] Storing ${prCommitShas.length} commit SHAs from ${prItemsToAdd.length} PRs`);
            
            const { error: commitError } = await supabaseAdmin
                .from('repository_pr_commit_shas')
                .upsert(prCommitShas, {
                    onConflict: 'repository_slug, installation_id, commit_sha',
                    ignoreDuplicates: true
                });

            if (commitError) {
                console.error('[LoaderJob][PRs] Error storing PR commit SHAs:', commitError);
            } else {
                console.log(`[LoaderJob][PRs] Successfully stored ${prCommitShas.length} commit SHAs.`);
            }
        }
    }

    return { success: true, itemsAdded: prItemsToAdd.length, hasMore: hasMoreFromGitHub };
}

async function getPrCommitShas(repositorySlug: string, installationId: number): Promise<Set<string>> {
  // Get commit SHAs from the dedicated table that stores all PR-associated commits
  const { data: prCommitData, error: prCommitError } = await supabaseAdmin
    .from('repository_pr_commit_shas')
    .select('commit_sha')
    .eq('repository_slug', repositorySlug)
    .eq('installation_id', installationId);

  if (prCommitError) {
    console.error(`[LoaderJob] Error fetching PR commit SHAs from repository_pr_commit_shas:`, prCommitError);
  }

  // Also get commit SHAs from the analysis status table for backward compatibility
  const { data: statusData, error: statusError } = await supabaseAdmin
    .from('repository_pr_analysis_status')
    .select('commit_sha')
    .eq('repository_slug', repositorySlug)
    .eq('installation_id', installationId)
    .not('commit_sha', 'is', null);

  if (statusError) {
    console.error(`[LoaderJob] Error fetching PR commit SHAs from repository_pr_analysis_status:`, statusError);
  }

  // Combine both sets of commit SHAs
  const allCommitShas = new Set<string>();
  
  // Add from PR commit SHAs table
  if (prCommitData) {
    prCommitData.forEach(item => allCommitShas.add(item.commit_sha));
  }
  
  // Add from analysis status table
  if (statusData) {
    statusData.forEach(item => item.commit_sha && allCommitShas.add(item.commit_sha));
  }

  console.log(`[LoaderJob] Found ${allCommitShas.size} total commit SHAs to skip (${prCommitData?.length || 0} from PR commits table, ${statusData?.length || 0} from analysis status table)`);
  
  return allCommitShas;
}

async function getExistingCommitShas(repositorySlug: string, installationId: number): Promise<Set<string>> {
  const { data, error } = await supabaseAdmin
    .from('repository_pr_analysis_status')
    .select('commit_sha')
    .eq('repository_slug', repositorySlug)
    .eq('installation_id', installationId)
    .eq('is_pseudo_pr', true)
    .not('commit_sha', 'is', null);

  if (error) {
    console.error(`[LoaderJob] Error fetching existing commit SHAs:`, error);
    return new Set();
  }
  return new Set(data?.map(item => item.commit_sha) || []);
}

async function processCommitBatch(job: RepositoryLoadingJob, authToken: string | null): Promise<{ success: boolean; itemsAdded: number; hasMore: boolean; }> {
    const [owner, repo] = job.repository_slug.split('/');
    console.log(`[LoaderJob][Commits] Processing commits for ${job.repository_slug}, page ${job.next_page_to_process}`);

    // Get both PR commit SHAs and existing standalone commit SHAs to skip
    const [prCommitShas, existingCommitShas] = await Promise.all([
        getPrCommitShas(job.repository_slug, job.installation_id),
        getExistingCommitShas(job.repository_slug, job.installation_id)
    ]);
    
    console.log(`[LoaderJob][Commits] Found ${prCommitShas.size} PR commit SHAs and ${existingCommitShas.size} existing standalone commit SHAs to skip.`);

    const commits = await getRecentCommits(owner, repo, { 
        maxCommits: BATCH_SIZE, 
        startPage: job.next_page_to_process, 
        sinceDate: '', 
        branch: '' 
    }, authToken);

    console.log(`[LoaderJob][Commits] Fetched ${commits.length} commits from GitHub for ${job.repository_slug}.`);

    if (commits.length === 0) {
        console.log(`[LoaderJob][Commits] No more commits found for ${job.repository_slug}.`);
        return { success: true, itemsAdded: 0, hasMore: false };
    }

    const newCommits = commits.filter(commit => !prCommitShas.has(commit.sha) && !existingCommitShas.has(commit.sha));
    console.log(`[LoaderJob][Commits] ${newCommits.length} commits are new (not associated with PRs or already processed standalone).`);
    const commitItemsToAdd: any[] = [];

    for (const commit of newCommits) {
        if (isDefinitelyNotArchitectural(commit.commit.message)) continue;

        try {
            const commitDetail: { files?: any[] } = await getCommitDetail(owner, repo, commit.sha, authToken);
            if (!commitDetail.files || commitDetail.files.length === 0) {
                console.log(`[LoaderJob][Commits] Skipping commit ${commit.sha.substring(0,7)} - no file changes.`);
                continue;
            }

            const pseudoPr = createPseudoPrFromCommit(commit, commitDetail, owner, repo) as {
              prContext: {
                number: number;
                title: string;
                html_url: string;
                created_at: string;
                merged_at: string;
              };
              codeChanges: Array<{ filename: string; patch: string }>;
            };
            
            commitItemsToAdd.push({
                installation_id: job.installation_id,
                repository_slug: job.repository_slug,
                pr_number: pseudoPr.prContext.number,
                pr_title: pseudoPr.prContext.title.substring(0, 255),
                pr_url: pseudoPr.prContext.html_url,
                pr_created_at: pseudoPr.prContext.created_at,
                pr_merged_at: pseudoPr.prContext.merged_at,
                is_pseudo_pr: true,
                commit_sha: commit.sha,
                status: 'pending_early_stage'
            });
        } catch (commitProcessingError) {
            console.error(`[LoaderJob][Commits] Error processing commit ${commit.sha.substring(0,7)}:`, commitProcessingError);
            // Continue with other commits
        }
    }
    
    console.log(`[LoaderJob][Commits] After filtering, ${commitItemsToAdd.length} commits will be upserted as pseudo-PRs.`);

    if (commitItemsToAdd.length > 0) {
        const { error } = await supabaseAdmin.from('repository_pr_analysis_status').upsert(commitItemsToAdd, {
             onConflict: 'repository_slug, pr_number, installation_id',
             ignoreDuplicates: true
        });

        if (error) {
            console.error('[LoaderJob][Commits] Error upserting commits:', error);
        } else {
             console.log(`[LoaderJob][Commits] Successfully upserted ${commitItemsToAdd.length} commits.`);
        }
    }
    
    return { success: true, itemsAdded: commitItemsToAdd.length, hasMore: commits.length === BATCH_SIZE };
}
// #endregion

// #region Main Handler
export default async function handler(req: NextApiRequest, res: NextApiResponse<LoaderResponse>) {
    console.log('[LoaderJob] Handler invoked via cron.');

    // Vercel Cron jobs send the secret in the Authorization header.
    if (CRON_SECRET_VALUE) {
        const providedSecret = req.headers['authorization']?.split(' ')[1] || (req.query.secret as string);
        if (providedSecret !== CRON_SECRET_VALUE) {
            console.warn('[LoaderJob] Unauthorized attempt with incorrect cron secret.');
            return res.status(401).json({ action: 'error', message: 'Unauthorized: Incorrect or missing secret.' });
        }
    } else {
        console.warn("[LoaderJob] WARNING: CRON_SECRET environment variable is not set. This is insecure and should only happen in development.");
    }

    // Check GitHub rate limit before starting any work
    try {
        const octokit = await getOctokit(); // Get a generic octokit instance for rate limit check
        const rateLimitStatus = await checkRateLimit(octokit, 200); // Higher threshold for loader jobs
        if (rateLimitStatus.isLimited) {
            const message = `[LoaderJob] GitHub rate limit is low (${rateLimitStatus.remaining} remaining). Pausing job until ${rateLimitStatus.resetTime.toLocaleTimeString()}.`;
            console.warn(message);
            return res.status(200).json({
                action: 'no_jobs',
                message: message,
            });
        }
    } catch (error) {
        console.warn(`[LoaderJob] Could not check rate limit, proceeding with caution:`, error);
    }

    const job = await getNextPendingJob();
    if (!job) {
        console.log('[LoaderJob] No pending repository loading jobs found. Exiting.');
        return res.status(200).json({ action: 'no_jobs', message: 'No pending repository loading jobs found.' });
    }
    
    console.log(`[LoaderJob] Found pending job: ID ${job.id} for ${job.repository_slug}, Phase: ${job.current_phase}, Page: ${job.next_page_to_process}`);

    const isProcessing = await markJobAsProcessing(job.id);
    if (!isProcessing) {
        // Error is logged inside the function, just exit.
        return res.status(500).json({ action: 'error', message: `Failed to mark job ${job.id} as processing.` });
    }
    
    console.log(`[LoaderJob] Marked job ${job.id} as 'processing'.`);

    const effectiveInstallationId = job.installation_id === 0 ? undefined : job.installation_id.toString();
    const authToken = await getAuthToken(effectiveInstallationId);
    let octokit;
    try {
        console.log(`[LoaderJob] Authenticating with installation ID: ${effectiveInstallationId || 'default (for public repo)'}`);
        octokit = await getOctokit(effectiveInstallationId);
        console.log(`[LoaderJob] Successfully authenticated with GitHub for job ${job.id}.`);
    } catch (error) {
        console.error(`[LoaderJob] Failed to authenticate with GitHub for job ${job.id}. Error:`, error);
        await updateJobProgress(job.id, { status: 'failed', error_message: 'GitHub authentication failed.' });
        return res.status(500).json({ action: 'error', message: `GitHub authentication failed for job ${job.id}.` });
    }

    let batchResult;

    try {
        if (job.current_phase === 'prs') {
            batchResult = await processPrBatch(job, octokit);
            if (!batchResult.hasMore) {
                console.log(`[LoaderJob] Completed PR loading for ${job.repository_slug}. Switching to commits phase.`);
                await updateJobProgress(job.id, { current_phase: 'commits', next_page_to_process: 1 });
                return res.status(200).json({ action: 'switched_phase', message: 'Switched from PRs to commits.', details: { repository: job.repository_slug }});
            } else {
                console.log(`[LoaderJob] Processed PR page ${job.next_page_to_process} for ${job.repository_slug}. Advancing to next page.`);
                await updateJobProgress(job.id, { next_page_to_process: job.next_page_to_process + 1 });
            }
        } else if (job.current_phase === 'commits') {
            batchResult = await processCommitBatch(job, authToken);
            if (!batchResult.hasMore) {
                console.log(`[LoaderJob] Completed commit loading for ${job.repository_slug}. Job finished.`);
                await updateJobProgress(job.id, { current_phase: 'completed', is_completed: true, status: 'completed' });
                return res.status(200).json({ action: 'completed_job', message: 'Finished loading all PRs and commits.', details: { repository: job.repository_slug }});
            } else {
                console.log(`[LoaderJob] Processed commit page ${job.next_page_to_process} for ${job.repository_slug}. Advancing to next page.`);
                 await updateJobProgress(job.id, { next_page_to_process: job.next_page_to_process + 1 });
            }
        }

        const response: LoaderResponse = { 
            action: 'processed_batch', 
            message: `Processed a batch of ${job.current_phase}.`,
            details: { repository: job.repository_slug, phase: job.current_phase, page: job.next_page_to_process, itemsAdded: batchResult?.itemsAdded }
        };
        console.log(`[LoaderJob] Batch processed successfully for job ${job.id}.`, response);
        return res.status(200).json(response);

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        console.error(`[LoaderJob] Unhandled error during ${job.current_phase} processing for job ${job.id} (${job.repository_slug}):`, error);
        await updateJobProgress(job.id, { status: 'failed', error_message: `Error during ${job.current_phase} phase: ${errorMessage}` });

        return res.status(500).json({ 
            action: 'error', 
            message: `An unexpected error occurred during the '${job.current_phase}' phase.`,
            details: { error: errorMessage }
        });
    }
}
// #endregion 