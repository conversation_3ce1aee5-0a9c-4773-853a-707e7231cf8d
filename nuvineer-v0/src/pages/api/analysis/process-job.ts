import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { Octokit } from '@octokit/rest';
import { getOctokit, getAuthToken, checkRateLimit } from '@/lib/github-auth';
import { processMergedPR, getAllDecisionMetadataForRepo } from '@/orchestrator';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';

// Initialize Supabase client with service role for backend operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!, 
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Security check for cron job
const CRON_SECRET_VALUE = process.env.CRON_SECRET;

// API response types
interface ProcessResponse {
  action: 'processed_prs_batch' | 'no_action' | 'error' | 'activated_skipped_prs';
  message: string;
  details?: any;
}

// Type for PRs fetched from repository_pr_analysis_status
interface PendingPR {
  pr_number: number | string; // Can be a number for real PRs or a string for commit pseudo-PRs
  pr_title: string;
  pr_url: string;
  pr_merged_at: string;
  repository_slug: string;
  installation_id: number;
  is_pseudo_pr?: boolean; // Flag to identify pseudo PRs
  commit_sha?: string; // Original commit SHA for pseudo PRs
}

// Define orchestrator types
interface OrchestratorPRContext {
  html_url: string;
  title: string;
  body: string | null;
  number: number | string; // Allow string for pseudo PRs
  merged_at: string | null; 
  user: { login: string } | string;
  is_pseudo_pr?: boolean; // Flag to identify pseudo PRs
  commit_sha?: string; // Original commit SHA for pseudo PRs
}

interface OrchestratorCodeChange {
  filename: string;
  additions?: number;
  deletions?: number;
  patch?: string;
  sha?: string; 
  status?: string;
}

interface OrchestratorComment {
  body: string;
  user: { login: string } | null;
  created_at: string;
}

interface OrchestratorAnalysisResult {
  status: 'completed_successfully' | 'completed_with_errors' | 'completed_no_decisions' | 'failed' | 'feedback_generated' | 'processing';
  decisions?: Array<{
    id?: string;
    title: string;
    description?: string;
    rationale?: string;
    implications?: string;
  }>;
  error?: string;
  reason?: string; 
  feedback?: string; 
  duration?: number;
}

// Define a proper type for the decision object
interface DecisionForAnalysis {
  id: string;
  pinecone_id: string;
  title: string;
  description: string;
  related_files: string[];
  domain_concepts: string[];
  implications: string;
  rationale: string;
  pr_number: number | string | undefined;
}

// Add the updated function signature for processMergedPR
type ProcessMergedPRFunction = (
  prContext: OrchestratorPRContext,
  codeChanges: OrchestratorCodeChange[],
  comments: OrchestratorComment[],
  namespace: string,
  installationRepositorySlug: string,
  dryRun: boolean,
  designDocContent?: string,
  skipRelationshipAnalysis?: boolean
) => Promise<OrchestratorAnalysisResult>;

// Track repositories attempted in current job run to avoid infinite loops
const attemptedRepositories = new Set<string>();

/**
 * Clear the attempted repositories tracking. Called at start of job run and after successful preload.
 */
function clearAttemptedRepositories(): void {
  attemptedRepositories.clear();
  console.log("[Worker] Cleared attempted repositories tracking.");
}

/**
 * Remove a specific repository from attempted tracking (when preload succeeds).
 */
function markRepositoryPreloadSuccessful(repositorySlug: string, installationId: number): void {
  const repoKey = `${repositorySlug}-${installationId}`;
  attemptedRepositories.delete(repoKey);
  console.log(`[Worker] Removed ${repositorySlug} from attempted repositories (preload successful).`);
}

/**
 * Get the next pending PRs to analyze from the database.
 * This function is designed to prevent race conditions in relationship analysis
 * by fetching a batch of PRs from *different* repositories.
 * It will claim up to `limit` PRs, with a maximum of one PR per repository in a single batch.
 */
async function getNextPendingPRs(limit: number): Promise<PendingPR[]> {
  try {
    // 1. Get a list of unique repos with pending PRs.
    const { data: repos, error: repoError } = await supabase
      .from('repository_pr_analysis_status')
      .select('repository_slug, installation_id')
      .eq('status', 'pending')
      .order('pr_merged_at', { ascending: false })
      .limit(100);
    
    if (repoError || !repos) {
      console.error("[Worker] Error finding repositories with pending PRs:", repoError);
      return [];
    }
    
    if (repos.length === 0) {
      return [];
    }
    
    // Deduplicate to get unique repos, up to our limit
    const uniqueReposMap = new Map<string, { repository_slug: string, installation_id: number }>();
    for (const pr of repos) {
      if (uniqueReposMap.size >= limit) break;
      const key = `${pr.repository_slug}-${pr.installation_id}`;
      if (!uniqueReposMap.has(key)) {
        uniqueReposMap.set(key, pr);
      }
    }
    const uniqueRepos = Array.from(uniqueReposMap.values());

    // 2. In parallel, attempt to claim the newest PR from each unique repository.
    const claimPromises = uniqueRepos.map(repo => getNextPendingPRForRepo(repo.repository_slug, repo.installation_id));
    const results = await Promise.allSettled(claimPromises);
    
    const claimedPRs = results
      .filter(result => result.status === 'fulfilled' && result.value !== null)
      .map(result => (result as PromiseFulfilledResult<PendingPR>).value);

    if (claimedPRs.length > 0) {
      console.log(`[Worker] Successfully claimed ${claimedPRs.length} PRs for processing from ${claimedPRs.length} different repos.`);
    }

    return claimedPRs;
    
  } catch (error) {
    console.error("[Worker] Unexpected error getting next pending PRs:", error);
    return [];
  }
}

/**
 * Atomically claims the newest pending PR for a *specific* repository.
 * This is the core unit of work for preventing race conditions.
 */
async function getNextPendingPRForRepo(repositorySlug: string, installationId: number): Promise<PendingPR | null> {
  try {
    // Step 1: Select the specific PR to claim, ensuring a deterministic order
    const { data: claimablePrData, error: selectError } = await supabase
      .from('repository_pr_analysis_status')
      .select('*') // Select all fields needed for PendingPR
      .eq('status', 'pending')
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', installationId)
      .order('pr_merged_at', { ascending: false }) // Process newest first
      .order('pr_number', { ascending: true })
      .limit(1)
      .maybeSingle();

    if (selectError) {
      console.error(`[Worker] Error selecting PR to claim for ${repositorySlug}:`, selectError);
      return null;
    }

    if (!claimablePrData) {
      return null;
    }
    
    // Step 2: Atomically update this specific PR using its unique identifiers
    const { data: updatedPr, error: updateError } = await supabase
      .from('repository_pr_analysis_status')
      .update({ 
        status: 'in_progress',
        last_analyzed_at: new Date().toISOString() 
      })
      .eq('repository_slug', claimablePrData.repository_slug)
      .eq('installation_id', claimablePrData.installation_id)
      .eq('pr_number', claimablePrData.pr_number) 
      .eq('status', 'pending') // Crucial: ensure it's still pending
      .select()
      .single();

    if (updateError) {
      // This is an expected outcome if another worker claims the PR first.
      if (updateError.code !== 'PGRST116') {
        console.error(`[Worker] Error claiming pending PR for repo ${repositorySlug}:`, updateError);
      }
      return null;
    }

    return {
      pr_number: updatedPr.pr_number,
      pr_title: updatedPr.pr_title,
      pr_url: updatedPr.pr_url,
      pr_merged_at: updatedPr.pr_merged_at,
      repository_slug: updatedPr.repository_slug,
      installation_id: updatedPr.installation_id ?? 0,
      is_pseudo_pr: updatedPr.is_pseudo_pr,
      commit_sha: updatedPr.is_pseudo_pr ? updatedPr.commit_sha : undefined,
    };
  } catch (error) {
    console.error(`[Worker] Unexpected error in getNextPendingPRForRepo for ${repositorySlug}:`, error);
    return null;
  }
}

/**
 * Process a single PR/commit.
 */
async function processPR(pr: PendingPR): Promise<boolean> {
  // console.log(`[Worker] Processing PR #${pr.pr_number} for ${pr.repository_slug}.`);
  
  try {
    // NOTE: PR is already marked as in_progress by getNextPendingPR()
    // No need to update status here again
    
    // Get GitHub client for this PR
    const octokit = await getOctokit(pr.installation_id === 0 ? undefined : pr.installation_id.toString());
    const [owner, repo] = pr.repository_slug.split('/');
    const pineconeNamespace = getRepositoryNamespace(
      pr.installation_id === 0 ? '0' : pr.installation_id.toString(),
      pr.repository_slug
    );
    
    // Check for existing decisions in Pinecone first
    try {
      const allDecisions = await getAllDecisionMetadataForRepo(pineconeNamespace, { includeSuperseded: true });
      let existingDecisions = [];

      if (pr.is_pseudo_pr && pr.commit_sha) {
        existingDecisions = allDecisions.filter(d => d.metadata?.commit_sha === pr.commit_sha);
      } else {
        const currentPrNumberStr = String(pr.pr_number); // Normalize to string for comparison
        existingDecisions = allDecisions.filter(d => String(d.metadata?.pr_number) === currentPrNumberStr);
      }

      if (existingDecisions.length > 0) {
        console.log(`[Worker] Found ${existingDecisions.length} existing decisions in Pinecone for PR #${pr.pr_number} (Commit SHA: ${pr.commit_sha || 'N/A'}) in ${pr.repository_slug}. Skipping full analysis.`);
        
        const { error: skipUpdateError } = await supabase
          .from('repository_pr_analysis_status')
          .update({
            status: 'completed',
            last_analyzed_at: new Date().toISOString(),
            analysis_error: "Skipped: Analysis already performed and decisions exist in Pinecone.",
            extracted_decision_count: existingDecisions.length
          })
          .eq('repository_slug', pr.repository_slug)
          .eq('pr_number', pr.pr_number)
          .eq('installation_id', pr.installation_id);

        if (skipUpdateError) {
          console.error(`[Worker] Error updating status for skipped PR #${pr.pr_number} due to existing analysis:`, skipUpdateError);
          // Even if DB update fails, we still skip because Pinecone has data.
          // However, this might lead to reprocessing if the job restarts.
          // For now, we'll return true, but this error should be monitored.
        }
        return true; // Indicate successful "processing" by skipping
      }
    } catch (pineconeError) {
      console.error(`[Worker] Error checking for existing decisions in Pinecone for PR #${pr.pr_number}:`, pineconeError);
      // If we can't check Pinecone, proceed with analysis to be safe, but log the error.
      // Alternatively, could mark as failed here or retry. For now, proceeding.
    }
    
    // Get code changes
      let codeChanges: OrchestratorCodeChange[] = [];
    if (pr.is_pseudo_pr && pr.commit_sha) {
      // Fetch code changes for commit
      const { data: commitDetail } = await octokit.repos.getCommit({ owner, repo, ref: pr.commit_sha });
            codeChanges = (commitDetail.files || []).map(file => ({
              filename: file.filename,
              additions: file.additions,
              deletions: file.deletions,
              patch: file.patch || '',
              sha: file.sha,
        status: file.status
      }));
        } else {
      // Fetch code changes for PR
      const numericPrNumber = typeof pr.pr_number === 'number' 
        ? pr.pr_number 
        : parseInt(String(pr.pr_number), 10);
          
          const { data: files } = await octokit.pulls.listFiles({
            owner,
            repo,
        pull_number: numericPrNumber 
          });

          codeChanges = (files || []).map(file => ({
            filename: file.filename,
            additions: file.additions,
            deletions: file.deletions,
            patch: file.patch || '',
            sha: file.sha,
        status: file.status
      }));
    }
    
    // Get PR comments
    let prComments: OrchestratorComment[] = [];
    if (!pr.is_pseudo_pr) {
      const numericPrNumber = typeof pr.pr_number === 'number' 
        ? pr.pr_number 
        : parseInt(String(pr.pr_number), 10);
      
            const { data: reviewComments } = await octokit.pulls.listReviewComments({
              owner,
              repo,
        pull_number: numericPrNumber
      });
      
      prComments = reviewComments.map(c => ({ 
        body: c.body, 
        user: c.user, 
        created_at: c.created_at 
      }));
    }
    
    // Get PR data if not a commit
      let ghPrData;
    if (!pr.is_pseudo_pr) {
      const numericPrNumber = typeof pr.pr_number === 'number' 
        ? pr.pr_number 
        : parseInt(String(pr.pr_number), 10);
      
      const {data} = await octokit.pulls.get({
        owner,
        repo,
        pull_number: numericPrNumber
      });
      
            ghPrData = data;
      }

    // Prepare PR context for orchestrator
      const prContextAPI: OrchestratorPRContext = {
      title: pr.pr_title,
      body: pr.is_pseudo_pr ? '' : (ghPrData?.body || ''),
      html_url: pr.pr_url,
      number: pr.pr_number,
      merged_at: pr.pr_merged_at,
      user: pr.is_pseudo_pr 
        ? (pr.pr_title.includes('@') ? pr.pr_title.split('@')[1] : 'unknown') 
        : (ghPrData?.user?.login || 'unknown'),
      is_pseudo_pr: pr.is_pseudo_pr,
      commit_sha: pr.is_pseudo_pr ? pr.commit_sha : undefined
    };
    
    // Process PR with orchestrator
    const result = await processMergedPR(
          prContextAPI,
          codeChanges,
          prComments,
          pineconeNamespace,
          pr.repository_slug,
          false, // Not a test mode, even if the PR is from test-pr
          undefined // No design doc
        ) as OrchestratorAnalysisResult;

    // Update PR status based on result
    let finalStatus = 'failed'; // Default
    let errorMessage = null;
    let decisionsExtracted = 0;

    if (result.status === 'completed_successfully' || result.status === 'completed_with_errors') {
      finalStatus = 'completed';
      decisionsExtracted = result.decisions?.length || 0;
    } else if (result.status === 'completed_no_decisions') {
      finalStatus = 'no_decisions';
      errorMessage = result.reason || null;
    } else { 
      errorMessage = result.error || result.reason || 'Analysis failed';
    }
    
    // Update database with final status
    const { error: finalUpdateError } = await supabase
      .from('repository_pr_analysis_status')
      .update({
        status: finalStatus,
        last_analyzed_at: new Date().toISOString(),
        analysis_error: errorMessage,
        extracted_decision_count: decisionsExtracted
      })
      .eq('repository_slug', pr.repository_slug)
      .eq('pr_number', pr.pr_number)
      .eq('installation_id', pr.installation_id);
    
    if (finalUpdateError) {
      console.error(`[Worker] Error updating final status for PR #${pr.pr_number}:`, finalUpdateError);
      return false;
    }
    
    // Check if this is the first PR being analyzed for this repository
    const { count: analyzedCount, error: countError } = await supabase
      .from('repository_pr_analysis_status')
      .select('*', { count: 'exact', head: true })
      .eq('repository_slug', pr.repository_slug)
      .eq('installation_id', pr.installation_id)
      .in('status', ['completed', 'no_decisions', 'failed']);
    
    if (countError) {
      console.error(`[Worker] Error checking analyzed PRs count:`, countError);
    } else if (analyzedCount === 1) {
      // This is the first PR that has been analyzed, mark all skipped PRs as pending
      const { error: updateError } = await supabase
        .from('repository_pr_analysis_status')
        .update({ status: 'pending' })
        .eq('repository_slug', pr.repository_slug)
        .eq('installation_id', pr.installation_id)
        .eq('status', 'skipped');
      
      if (updateError) {
        console.error(`[Worker] Error updating skipped PRs to pending:`, updateError);
      } else {
        console.log(`[Worker] Successfully marked remaining PRs as pending after first PR analysis.`);
      }
    }
    
    return true;
  } catch (error) {
    console.error(`[Worker] Error processing PR #${pr.pr_number}:`, error);
    
    // Mark as failed
    try {
      await supabase
        .from('repository_pr_analysis_status')
        .update({
          status: 'failed',
          last_analyzed_at: new Date().toISOString(),
          analysis_error: error instanceof Error ? error.message : String(error)
        })
        .eq('repository_slug', pr.repository_slug)
        .eq('pr_number', pr.pr_number)
        .eq('installation_id', pr.installation_id);
    } catch (dbError) {
      console.error(`[Worker] Failed to update failed status for PR #${pr.pr_number}:`, dbError);
    }
    
    return false;
  }
}

/**
 * Check if a repository has "skipped" PRs and activate them by changing their status to "pending".
 * This allows transitioning from test mode (with just one PR analyzed) to full analysis.
 */
async function checkAndActivateSkippedPRs(repositorySlug: string, installationId: number): Promise<number> {
  try {
    // console.log(`[Worker] Checking for skipped PRs in repository ${repositorySlug}...`);
    
    // First check if there are any skipped PRs
    const { data: skippedPRs, error: countError } = await supabase
      .from('repository_pr_analysis_status')
      .select('*')
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', installationId)
      .eq('status', 'skipped');
    
    if (countError) {
      console.error(`[Worker] Error checking for skipped PRs in ${repositorySlug}:`, countError);
      return 0;
    }
    
    if (!skippedPRs || skippedPRs.length === 0) {
      // console.log(`[Worker] No skipped PRs found in repository ${repositorySlug}.`);
      return 0;
    }
    
    // console.log(`[Worker] Found ${skippedPRs.length} skipped PRs in repository ${repositorySlug}. Activating them...`);
    
    // Update all skipped PRs to pending status
    const { error: updateError } = await supabase
      .from('repository_pr_analysis_status')
      .update({ status: 'pending' })
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', installationId)
      .eq('status', 'skipped');
    
    if (updateError) {
      console.error(`[Worker] Error activating skipped PRs in ${repositorySlug}:`, updateError);
      return 0;
    }
    
    // console.log(`[Worker] Successfully activated ${skippedPRs.length} skipped PRs in repository ${repositorySlug}.`);
    return skippedPRs.length;
  } catch (error) {
    console.error(`[Worker] Error checking and activating skipped PRs in ${repositorySlug}:`, error);
    return 0;
  }
}

async function handler(req: NextApiRequest, res: NextApiResponse<ProcessResponse>) {
  console.log('[Worker] Cron job handler invoked.');
  clearAttemptedRepositories();

  const BATCH_SIZE = 5; // Process up to 5 PRs from different repos in parallel
  const pendingPRs = await getNextPendingPRs(BATCH_SIZE);

  if (pendingPRs.length === 0) {
    console.log('[Worker] No pending PRs found to process.');
    return res.status(200).json({ action: 'no_action', message: 'No pending PRs found.' });
  }

  console.log(`[Worker] Starting parallel processing for ${pendingPRs.length} PRs.`);
  
  const processingPromises = pendingPRs.map(pr => processPR(pr));
  const results = await Promise.allSettled(processingPromises);

  const successfulCount = results.filter(r => r.status === 'fulfilled' && r.value === true).length;
  const failedCount = results.length - successfulCount;

  console.log(`[Worker] Batch processing complete. Success: ${successfulCount}, Failed: ${failedCount}.`);

  return res.status(200).json({
    action: 'processed_prs_batch',
    message: `Processed a batch of ${pendingPRs.length} PRs.`,
    details: {
      successful_count: successfulCount,
      failed_count: failedCount,
      processed_repos: pendingPRs.map(pr => pr.repository_slug),
    },
  });
}

export default async function (req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST' || req.method === 'GET') {
    const providedSecret = req.headers['authorization']?.split(' ')[1] || (req.query.secret as string);
    if (CRON_SECRET_VALUE && providedSecret === CRON_SECRET_VALUE) {
      try {
        await handler(req, res);
      } catch (error) {
        console.error('[Worker] Unhandled error in cron handler:', error);
        res.status(500).json({ action: 'error', message: 'Internal Server Error' });
      }
    } else {
      console.warn('[Worker] Unauthorized cron job access attempt.');
      res.status(401).json({ action: 'error', message: 'Unauthorized' });
    }
  } else {
    res.setHeader('Allow', ['POST', 'GET']);
    res.status(405).end('Method Not Allowed');
  }
}
