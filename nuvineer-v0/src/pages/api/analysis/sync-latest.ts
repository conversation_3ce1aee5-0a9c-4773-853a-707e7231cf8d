import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { Octokit } from '@octokit/rest';
import { getOctokit, getAuthToken } from '@/lib/github-auth';
import { getRecentCommits, getCommitDetail, createPseudoPrFromCommit } from '@/analyzer/github';

// Initialize Supabase client
const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

async function fetchAndStoreLatestChanges(
    repositorySlug: string,
    installationIdParam: string | undefined,
    isPublicRepo: boolean,
    octokit: Octokit
) {
    console.log(`[API /sync-latest] Starting sync for ${repositorySlug}`);
    const [owner, repo] = repositorySlug.split('/');
    const effectiveInstallationId = isPublicRepo ? undefined : (installationIdParam === '0' ? undefined : installationIdParam);
    const installationIdForDb = effectiveInstallationId ? parseInt(effectiveInstallationId) : 0;
    
    let newItemsAddedCount = 0;
    const itemsToInsert: any[] = [];
    const prCommitShas = new Set<string>(); // To track commit SHAs from newly fetched PRs

    // 1. Fetch existing PR numbers and commit SHAs from the database to avoid duplicates
    console.log(`[API /sync-latest] Fetching existing PRs/commits from DB for ${repositorySlug}, InstallID: ${installationIdForDb}`);
    const { data: existingItems, error: fetchError } = await supabaseAdmin
        .from('repository_pr_analysis_status')
        .select('pr_number, commit_sha, is_pseudo_pr')
        .eq('repository_slug', repositorySlug)
        .eq('installation_id', installationIdForDb);

    if (fetchError) {
        console.error(`[API /sync-latest] Error fetching existing items for ${repositorySlug}:`, fetchError);
        throw new Error(`Database error fetching existing items: ${fetchError.message}`);
    }

    const existingPrNumbers = new Set(existingItems?.filter(item => !item.is_pseudo_pr).map(item => item.pr_number) || []);
    const existingCommitShas = new Set(existingItems?.filter(item => item.is_pseudo_pr && item.commit_sha).map(item => item.commit_sha!) || []);
    console.log(`[API /sync-latest] Found ${existingPrNumbers.size} existing PRs and ${existingCommitShas.size} existing pseudo PRs (commits) in DB for ${repositorySlug}.`);

    // 2. Fetch latest merged PRs from GitHub
    // We fetch a small number, e.g., the latest 20-30, assuming syncs are relatively frequent.
    // If a repo hasn't been synced in a long time, this might not catch everything super old *and* new.
    // The `start.ts` endpoint is better for massive backfills.
    console.log(`[API /sync-latest] Fetching latest 30 merged PRs from GitHub for ${repositorySlug}.`);
    try {
        const listPrParams = {
            owner: owner,
            repo: repo,
            state: 'closed' as const,
            sort: 'created' as const, // Changed from 'updated' to 'created'
            direction: 'desc' as const,
            per_page: 30, // Fetch a batch, we'll take the first new one
        };
        const { data: prs } = await octokit.pulls.list(listPrParams);
        const mergedPrs = prs.filter(pr => pr.merged_at);
        console.log(`[API /sync-latest] Fetched ${mergedPrs.length} merged PRs from GitHub for ${repositorySlug}.`);

        for (const pr of mergedPrs) {
            if (!existingPrNumbers.has(pr.number.toString())) { // Check if PR number (as string for DB consistency) exists
                // For each new PR, fetch its commits to add their SHAs to prCommitShas
                try {
                    console.log(`[API /sync-latest] Fetching commits for new PR #${pr.number}`);
                    const { data: prCommitsData } = await octokit.pulls.listCommits({
                        owner,
                        repo,
                        pull_number: pr.number,
                        per_page: 100 
                    });
                    prCommitsData.forEach(commit => prCommitShas.add(commit.sha));
                } catch (commitFetchError) {
                    console.warn(`[API /sync-latest] Could not fetch commits for PR #${pr.number}:`, commitFetchError);
                }

                itemsToInsert.push({
                    installation_id: installationIdForDb,
                    repository_slug: repositorySlug,
                    pr_number: pr.number.toString(), // Ensure pr_number is string
                    pr_title: pr.title.substring(0, 255),
                    pr_url: pr.html_url,
                    pr_created_at: pr.created_at,
                    pr_merged_at: pr.merged_at,
                    status: 'pending', // New PRs are pending analysis
                    is_pseudo_pr: false,
                    relationships_processed: false
                });
                console.log(`[API /sync-latest] Found latest new PR #${pr.number}. Will not process further PRs in this sync.`);
                break; // Stop after processing the first new PR
            }
        }
    } catch (fetchError) {
        console.error(`[API /sync-latest] Error fetching PRs from GitHub for ${repositorySlug}:`, fetchError);
        // Continue to commit fetching even if PRs fail
    }
    console.log(`[API /sync-latest] Added ${itemsToInsert.length} new PRs to insert list.`);

    // 3. Fetch latest commits from GitHub (main/default branch)
    // Similar to PRs, fetch a limited number for a "latest" sync.
    console.log(`[API /sync-latest] Fetching latest 30 commits from GitHub for ${repositorySlug}.`);
    const authToken = await getAuthToken(effectiveInstallationId);
    try {
        // Fetch recent commits (e.g., last 30)
        const recentCommits = await getRecentCommits(owner, repo, { maxCommits: 30, branch: '', sinceDate: '', startPage: 1 }, authToken);
        console.log(`[API /sync-latest] Fetched ${recentCommits.length} raw commits.`);

        for (const commit of recentCommits) {
            if (!existingCommitShas.has(commit.sha) && !prCommitShas.has(commit.sha)) { // Check if commit SHA exists or was part of a new PR
                try {
                    const commitDetail: { files?: any[] } = await getCommitDetail(owner, repo, commit.sha, authToken);
                    if (!commitDetail.files || commitDetail.files.length === 0) {
                        console.log(`[API /sync-latest] Skipping commit ${commit.sha.substring(0,7)} - no file changes.`);
                        continue;
                    }
                    const pseudoPr = createPseudoPrFromCommit(commit, commitDetail, owner, repo) as {
                        prContext: any;
                        codeChanges: Array<{ filename: string; patch: string }>;
                    };

                    itemsToInsert.push({
                        installation_id: installationIdForDb,
                        repository_slug: repositorySlug,
                        pr_number: pseudoPr.prContext.number, // Formatted commit ID
                        pr_title: pseudoPr.prContext.title.substring(0, 255),
                        pr_url: pseudoPr.prContext.html_url,
                        pr_created_at: pseudoPr.prContext.created_at,
                        pr_merged_at: pseudoPr.prContext.merged_at,
                        status: 'pending',
                        is_pseudo_pr: true,
                        commit_sha: commit.sha,
                        relationships_processed: false
                    });
                } catch (commitProcessingError) {
                    console.error(`[API /sync-latest] Error processing commit ${commit.sha.substring(0,7)}:`, commitProcessingError);
                }
            }
        }
    } catch (commitError) {
        console.error(`[API /sync-latest] Error fetching/processing commits for ${repositorySlug}:`, commitError);
    }
    
    const finalNewPRsCount = itemsToInsert.filter(item => !item.is_pseudo_pr).length;
    const finalNewCommitsCount = itemsToInsert.filter(item => item.is_pseudo_pr).length;
    console.log(`[API /sync-latest] After processing commits, total items to insert: ${itemsToInsert.length} (${finalNewPRsCount} PRs, ${finalNewCommitsCount} commits).`);


    // 4. Insert new items into the database
    if (itemsToInsert.length > 0) {
        // Sort by merged_at (or created_at for pseudo PRs from commits) before inserting
        // The pseudoPr object has merged_at which is the commit author date.
        itemsToInsert.sort((a, b) => new Date(a.pr_merged_at || a.pr_created_at).getTime() - new Date(b.pr_merged_at || b.pr_created_at).getTime());

        console.log(`[API /sync-latest] Attempting to insert ${itemsToInsert.length} new items into DB for ${repositorySlug}.`);
        const { error: insertError } = await supabaseAdmin
            .from('repository_pr_analysis_status')
            .insert(itemsToInsert);

        if (insertError) {
            console.error(`[API /sync-latest] CRITICAL: Error inserting new items into DB for ${repositorySlug}:`, insertError);
            throw new Error(`Database error inserting new items: ${insertError.message}`);
        }
        newItemsAddedCount = itemsToInsert.length;
        console.log(`[API /sync-latest] Successfully inserted ${newItemsAddedCount} new items into DB for ${repositorySlug}.`);
    } else {
        console.log(`[API /sync-latest] No new PRs or commits found to add for ${repositorySlug}.`);
    }

    return {
        repository_slug: repositorySlug,
        new_items_added: newItemsAddedCount,
    };
}

type Data =
  | { success: true; repositorySlug: string; newItemsAdded: number; message: string; }
  | { success: false; error: string };

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Data>
) {
  console.log('[API /api/analysis/sync-latest] HANDLER REACHED. Method:', req.method, 'Body:', req.body);

  if (req.method === 'POST') {
    const {
        repositorySlug,
        installationId,
        isPublic // Optional, defaults to false if not provided and installationId is present
    } = req.body as Partial<{
      repositorySlug: string;
      installationId?: string;
      isPublic?: boolean;
    }>;

    if (!repositorySlug) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: repositorySlug is required.'
      });
    }

    // Determine if it's a public or private repo context
    // If isPublic is explicitly true, it's public.
    // If installationId is provided (and not '0'), it's private.
    // If neither, or installationId is '0' and isPublic is not true, it's ambiguous or defaults to public for safety if allowed.
    // For this sync, we'll require either isPublic:true or a valid installationId for private.
    let isPublicRepo = !!isPublic; // if isPublic is true, then it's a public repo
    if (!isPublicRepo && !installationId) {
        // If not explicitly public AND no installationId, assume public IF THE LOGIC ALLOWS.
        // However, client sends installationId '0' for public, or actual ID for private.
        // If installationId is '0', treat as public.
        if (installationId === '0') {
            isPublicRepo = true;
        } else {
             return res.status(400).json({
                success: false,
                error: 'For private repositories, installationId is required. For public, set isPublic to true or use installationId "0".'
            });
        }
    }
    if (!isPublicRepo && installationId === '0'){ // Catch case where isPublic is false/undefined but installationId is '0'
        isPublicRepo = true;
    }


    try {
      const octokitInstallationId = isPublicRepo ? undefined : (installationId === '0' ? undefined : installationId);
      const octokit = await getOctokit(octokitInstallationId);

      console.log(`[API /sync-latest] Starting latest changes sync for ${repositorySlug}. Public: ${isPublicRepo}, InstallID for Octokit: ${octokitInstallationId}`);

      const syncResult = await fetchAndStoreLatestChanges(
        repositorySlug,
        installationId, // Pass original installationId for DB storage (it handles '0' or actual ID)
        isPublicRepo,
        octokit
      );

      const message = syncResult.new_items_added > 0
        ? `Successfully synced ${syncResult.new_items_added} new PRs/commits for ${repositorySlug}. They are now pending analysis.`
        : `Repository ${repositorySlug} is up-to-date. No new PRs or commits found.`;
      
      return res.status(200).json({
        success: true,
        repositorySlug: syncResult.repository_slug,
        newItemsAdded: syncResult.new_items_added,
        message: message
      });

    } catch (error) {
      console.error('[API /sync-latest] Error syncing latest changes:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      return res.status(500).json({ success: false, error: `Failed to sync latest changes: ${errorMessage}` });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
  }
} 