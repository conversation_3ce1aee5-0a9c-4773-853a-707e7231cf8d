import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { getOctokit, getAuthToken, checkRateLimit } from '@/lib/github-auth';
import { getRecentCommits, getCommitDetail, createPseudoPrFromCommit } from '@/analyzer/github';
import { isDefinitelyNotArchitectural } from '@/lib/heuristics';

// Initialize Supabase client
const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);

// Security check for cron job
const CRON_SECRET_VALUE = process.env.CRON_SECRET;

interface CommitLoaderResponse {
  action: 'loaded_commits' | 'no_jobs' | 'completed_job' | 'error';
  message: string;
  details?: any;
}

interface CommitLoadingJob {
  id: number;
  repository_slug: string;
  installation_id: number;
  current_page: number;
  total_commits_loaded: number;
  is_completed: boolean;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
  error_message?: string;
  last_processed_at?: string;
}

const COMMIT_BATCH_SIZE = 100;
const MAX_COMMIT_PAGES = 50; // Max 5000 commits total (50 * 100)

async function getNextPendingCommitJob(): Promise<CommitLoadingJob | null> {
  try {
    const { data: jobs, error } = await supabaseAdmin
      .from('commit_loading_jobs')
      .select('*')
      .eq('status', 'pending')
      .eq('is_completed', false)
      .order('created_at', { ascending: true })
      .limit(1);

    if (error) {
      console.error(`[CommitLoader] Error fetching pending jobs:`, error);
      return null;
    }

    return jobs && jobs.length > 0 ? jobs[0] : null;
  } catch (error) {
    console.error(`[CommitLoader] Error in getNextPendingCommitJob:`, error);
    return null;
  }
}

async function markJobAsProcessing(jobId: number): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from('commit_loading_jobs')
      .update({ 
        status: 'processing',
        updated_at: new Date().toISOString(),
        last_processed_at: new Date().toISOString()
      })
      .eq('id', jobId);

    if (error) {
      console.error(`[CommitLoader] Error marking job ${jobId} as processing:`, error);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`[CommitLoader] Error in markJobAsProcessing:`, error);
    return false;
  }
}

async function updateJobProgress(
  jobId: number, 
  newPage: number, 
  commitsLoaded: number, 
  isCompleted: boolean = false,
  errorMessage?: string
): Promise<boolean> {
  try {
    const updateData: any = {
      current_page: newPage,
      total_commits_loaded: commitsLoaded,
      updated_at: new Date().toISOString(),
      last_processed_at: new Date().toISOString()
    };

    if (isCompleted) {
      updateData.is_completed = true;
      updateData.status = 'completed';
    } else if (errorMessage) {
      updateData.status = 'failed';
      updateData.error_message = errorMessage;
    } else {
      updateData.status = 'pending'; // Ready for next batch
    }

    const { error } = await supabaseAdmin
      .from('commit_loading_jobs')
      .update(updateData)
      .eq('id', jobId);

    if (error) {
      console.error(`[CommitLoader] Error updating job ${jobId} progress:`, error);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`[CommitLoader] Error in updateJobProgress:`, error);
    return false;
  }
}

async function getPrCommitShas(repositorySlug: string, installationId: number): Promise<Set<string>> {
  try {
    // Get all PR commit SHAs to avoid duplicates
    const { data: prItems, error } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .select('commit_sha')
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', installationId)
      .eq('is_pseudo_pr', false)
      .not('commit_sha', 'is', null);

    if (error) {
      console.error(`[CommitLoader] Error fetching PR commit SHAs:`, error);
      return new Set();
    }

    const prCommitShas = new Set<string>();
    if (prItems) {
      prItems.forEach(item => {
        if (item.commit_sha) {
          prCommitShas.add(item.commit_sha);
        }
      });
    }

    return prCommitShas;
  } catch (error) {
    console.error(`[CommitLoader] Error in getPrCommitShas:`, error);
    return new Set();
  }
}

async function getExistingCommitShas(repositorySlug: string, installationId: number): Promise<Set<string>> {
  try {
    // Get all existing commit SHAs to avoid duplicates
    const { data: commitItems, error } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .select('commit_sha')
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', installationId)
      .eq('is_pseudo_pr', true)
      .not('commit_sha', 'is', null);

    if (error) {
      console.error(`[CommitLoader] Error fetching existing commit SHAs:`, error);
      return new Set();
    }

    const existingCommitShas = new Set<string>();
    if (commitItems) {
      commitItems.forEach(item => {
        if (item.commit_sha) {
          existingCommitShas.add(item.commit_sha);
        }
      });
    }

    return existingCommitShas;
  } catch (error) {
    console.error(`[CommitLoader] Error in getExistingCommitShas:`, error);
    return new Set();
  }
}

async function processCommitBatch(job: CommitLoadingJob): Promise<{ success: boolean; commitsAdded: number; hasMoreCommits: boolean; error?: string }> {
  try {
    console.log(`[CommitLoader] Processing batch for ${job.repository_slug}, page ${job.current_page}`);
    
    const [owner, repo] = job.repository_slug.split('/');
    const effectiveInstallationId = job.installation_id === 0 ? undefined : job.installation_id.toString();
    
    // Get GitHub auth
    const authToken = await getAuthToken(effectiveInstallationId);
    
    // Get existing commit SHAs to avoid duplicates
    const [prCommitShas, existingCommitShas] = await Promise.all([
      getPrCommitShas(job.repository_slug, job.installation_id),
      getExistingCommitShas(job.repository_slug, job.installation_id)
    ]);
    
    console.log(`[CommitLoader] Found ${prCommitShas.size} PR commits and ${existingCommitShas.size} existing commits to skip`);
    
    // Fetch this batch of commits
    const batchCommits = await getRecentCommits(owner, repo, { 
      maxCommits: COMMIT_BATCH_SIZE,
      branch: '', 
      sinceDate: '',
      startPage: job.current_page
    }, authToken);
    
    console.log(`[CommitLoader] Fetched ${batchCommits.length} commits for page ${job.current_page}`);
    
    if (batchCommits.length === 0) {
      console.log(`[CommitLoader] No more commits available for ${job.repository_slug}`);
      return { success: true, commitsAdded: 0, hasMoreCommits: false };
    }
    
    const commitItemsToAdd: any[] = [];
    const processedCommitShas = new Set<string>();
    
    for (const commit of batchCommits) {
      const sha = commit.sha;
      
      // Skip if already processed or is a PR commit
      if (processedCommitShas.has(sha) || prCommitShas.has(sha) || existingCommitShas.has(sha)) {
        continue;
      }
      
      processedCommitShas.add(sha);

      try {
        const commitDetail = await getCommitDetail(owner, repo, sha, authToken);
        const pseudoPr = createPseudoPrFromCommit(commit, commitDetail, owner, repo) as {
          prContext: any; 
          codeChanges: Array<{ filename: string; patch: string }>;
        };

        if (!pseudoPr.codeChanges || pseudoPr.codeChanges.length === 0) {
          continue;
        }

        // Apply heuristic filter
        const passesHeuristic = !isDefinitelyNotArchitectural(pseudoPr.prContext.title);
        console.log(`[CommitLoader] Commit ${sha.substring(0, 7)} - Heuristic: ${passesHeuristic ? 'PASS' : 'SKIP'} - "${pseudoPr.prContext.title}"`);
        
        if (passesHeuristic) {
          commitItemsToAdd.push({
            installation_id: job.installation_id,
            repository_slug: job.repository_slug,
            pr_number: pseudoPr.prContext.number,
            pr_title: pseudoPr.prContext.title.substring(0, 255),
            pr_url: pseudoPr.prContext.html_url,
            pr_created_at: pseudoPr.prContext.created_at,
            pr_merged_at: pseudoPr.prContext.merged_at,
            is_pseudo_pr: true,
            commit_sha: commit.sha,
            relationships_processed: false,
            status: 'pending_early_stage'
          });
        }
      } catch (error) {
        console.warn(`[CommitLoader] Error processing commit ${sha.substring(0, 7)}:`, error);
      }
    }
    
    // Insert commits from this batch
    let commitsAdded = 0;
    if (commitItemsToAdd.length > 0) {
      console.log(`[CommitLoader] Inserting ${commitItemsToAdd.length} commits as 'pending_early_stage'...`);
      
      const { error: insertError } = await supabaseAdmin
        .from('repository_pr_analysis_status')
        .insert(commitItemsToAdd);
        
      if (insertError) {
        console.error(`[CommitLoader] Error inserting commits:`, insertError);
        return { success: false, commitsAdded: 0, hasMoreCommits: false, error: insertError.message };
      }
      
      commitsAdded = commitItemsToAdd.length;
      console.log(`[CommitLoader] ✅ Successfully inserted ${commitsAdded} commits for LLM filtering`);
    } else {
      console.log(`[CommitLoader] No commits passed heuristic filter in this batch`);
    }

    // Determine if there are more commits
    const hasMoreCommits = batchCommits.length === COMMIT_BATCH_SIZE && job.current_page < MAX_COMMIT_PAGES;
    
    return { success: true, commitsAdded, hasMoreCommits };
    
  } catch (error) {
    console.error(`[CommitLoader] Error processing commit batch:`, error);
    return { 
      success: false, 
      commitsAdded: 0, 
      hasMoreCommits: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CommitLoaderResponse>
) {
  // Security check for cron job
  const authHeader = req.headers.authorization;
  if (authHeader !== `Bearer ${CRON_SECRET_VALUE}`) {
    return res.status(401).json({ 
      action: 'error', 
      message: 'Unauthorized: Invalid cron secret' 
    });
  }

  // Check GitHub rate limit before starting any work
  try {
    const octokit = await getOctokit(); // Get a generic octokit instance for rate limit check
    const rateLimitStatus = await checkRateLimit(octokit, 200); // Higher threshold for loader jobs
    if (rateLimitStatus.isLimited) {
        const message = `[CommitLoader] GitHub rate limit is low (${rateLimitStatus.remaining} remaining). Pausing job until ${rateLimitStatus.resetTime.toLocaleTimeString()}.`;
        console.warn(message);
        return res.status(200).json({
            action: 'no_jobs',
            message: message,
        });
    }
  } catch (error) {
      console.warn(`[CommitLoader] Could not check rate limit, proceeding with caution:`, error);
  }

  console.log(`[CommitLoader] === COMMIT LOADER JOB STARTED ===`);

  try {
    // Get the next pending commit loading job
    const job = await getNextPendingCommitJob();
    
    if (!job) {
      console.log(`[CommitLoader] No pending commit loading jobs found`);
      return res.status(200).json({
        action: 'no_jobs',
        message: 'No pending commit loading jobs to process'
      });
    }

    console.log(`[CommitLoader] Processing job for ${job.repository_slug}, page ${job.current_page}`);

    // Mark job as processing
    const marked = await markJobAsProcessing(job.id);
    if (!marked) {
      return res.status(500).json({
        action: 'error',
        message: `Failed to mark job ${job.id} as processing`
      });
    }

    // Process the current batch
    const result = await processCommitBatch(job);
    
    if (!result.success) {
      // Update job with error
      await updateJobProgress(job.id, job.current_page, job.total_commits_loaded, false, result.error);
      
      return res.status(500).json({
        action: 'error',
        message: `Failed to process commit batch: ${result.error}`,
        details: { jobId: job.id, repositorySlug: job.repository_slug }
      });
    }

    // Update job progress
    const newTotalCommits = job.total_commits_loaded + result.commitsAdded;
    const nextPage = job.current_page + 1;
    const isCompleted = !result.hasMoreCommits;

    await updateJobProgress(job.id, nextPage, newTotalCommits, isCompleted);

    if (isCompleted) {
      console.log(`[CommitLoader] ✅ Completed commit loading for ${job.repository_slug}. Total commits loaded: ${newTotalCommits}`);
      
      return res.status(200).json({
        action: 'completed_job',
        message: `Completed commit loading for ${job.repository_slug}`,
        details: {
          repositorySlug: job.repository_slug,
          totalCommitsLoaded: newTotalCommits,
          totalPages: job.current_page
        }
      });
    } else {
      console.log(`[CommitLoader] ✅ Processed page ${job.current_page} for ${job.repository_slug}. Added ${result.commitsAdded} commits. Total: ${newTotalCommits}`);
      
      return res.status(200).json({
        action: 'loaded_commits',
        message: `Loaded commits batch for ${job.repository_slug}`,
        details: {
          repositorySlug: job.repository_slug,
          currentPage: job.current_page,
          commitsAddedThisBatch: result.commitsAdded,
          totalCommitsLoaded: newTotalCommits,
          hasMoreCommits: result.hasMoreCommits
        }
      });
    }

  } catch (error) {
    console.error(`[CommitLoader] Unexpected error:`, error);
    return res.status(500).json({
      action: 'error',
      message: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
  }
} 