import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { analyzeRelationships } from '../../../orchestrator'; 
import { getRepositoryNamespace } from '../../../lib/pinecone-utils';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

type Data = 
  | { success: true; message: string; details: { totalDecisions: number; relationshipsUpdated: number; } } 
  | { success: false; error: string };

interface Relationship {
  existing_decision_id: string;
  relationship_type: 'supersedes';
  confidence_score: number;
  justification: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Data>
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ success: false, error: `Method ${req.method} Not Allowed` });
  }

  const { repositorySlug, installationId, isPublic } = req.body as {
    repositorySlug: string;
    installationId?: string;
    isPublic: boolean;
  };

  if (!repositorySlug) {
    return res.status(400).json({ success: false, error: 'repositorySlug is required.' });
  }
  if (!isPublic && !installationId) {
    return res.status(400).json({ success: false, error: 'installationId is required for private repositories.' });
  }

  const effectiveInstallationId = isPublic ? 0 : parseInt(installationId!, 10);
  const namespace = getRepositoryNamespace(repositorySlug, effectiveInstallationId.toString());

  console.log(`[API Backfill] Starting relationship analysis backfill for ${repositorySlug} in namespace ${namespace}`);

  try {
    // 1. Fetch all decisions for the repository, ordered newest to oldest
    const { data: decisions, error: fetchError } = await supabaseAdmin
      .from('decisions')
      .select('id, content, file_path, relevant_code_block, created_at, repository_slug, installation_id, pr_merged_at, title, description, rationale, implications, domain_concepts, related_files') // Ensure all fields needed by analyzeRelationships are selected
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', effectiveInstallationId)
      .order('created_at', { ascending: false });

    if (fetchError) {
      console.error(`[API Backfill] Error fetching decisions for ${repositorySlug}:`, fetchError);
      throw new Error(`Failed to fetch decisions: ${fetchError.message}`);
    }

    if (!decisions || decisions.length === 0) {
      return res.status(200).json({ success: true, message: 'No decisions found for this repository, nothing to do.', details: { totalDecisions: 0, relationshipsUpdated: 0 } });
    }

    console.log(`[API Backfill] Found ${decisions.length} decisions to process for ${repositorySlug}.`);

    let relationshipsUpdated = 0;

    // 2. Iterate through each decision and re-run relationship analysis
    for (const decision of decisions) {
      console.log(`[API Backfill] Analyzing decision ID: ${decision.id}`);
      
      const relationshipResult = await analyzeRelationships(
        decision, 
        namespace, 
        { direction: 'forward' }
      );

      const supersedingRelationship = relationshipResult.relationships?.find((rel: Relationship) => rel.relationship_type === 'supersedes');
      
      if (supersedingRelationship) {
        console.log(`[API Backfill] Decision ID ${decision.id} is superseded by Decision ID ${supersedingRelationship.existing_decision_id}. Updating database.`);
        
        const { error: updateError } = await supabaseAdmin
          .from('decisions')
          .update({ 
            is_superseded: true,
            superseded_by_decision_id: supersedingRelationship.existing_decision_id 
          })
          .eq('id', decision.id);

        if (updateError) {
          console.error(`[API Backfill] Failed to update decision ${decision.id}:`, updateError);
          // Optionally, you could track failures instead of just logging
        } else {
          relationshipsUpdated++;
        }
      } else if (relationshipResult.error) {
        console.warn(`[API Backfill] Error analyzing relationships for decision ${decision.id}: ${relationshipResult.error}`);
        // Decide if you want to stop on error or continue. For a backfill, continuing might be better.
      }
    }

    const message = `Relationship backfill completed for ${repositorySlug}. Processed ${decisions.length} decisions and updated ${relationshipsUpdated} relationships.`;
    console.log(`[API Backfill] ${message}`);

    return res.status(200).json({ 
      success: true, 
      message: message,
      details: {
        totalDecisions: decisions.length,
        relationshipsUpdated: relationshipsUpdated
      }
    });

  } catch (error) {
    console.error(`[API Backfill] General error during backfill for ${repositorySlug}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return res.status(500).json({ success: false, error: `Backfill failed: ${errorMessage}` });
  }
} 