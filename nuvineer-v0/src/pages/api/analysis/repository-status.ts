import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase with service role for backend access
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Define detailed status response type (previously RepositoryStatusResponse)
interface RepositoryStatusDetails {
  repository_slug: string;
  total_prs: number; // Total relevant PRs for the current mode
  pending_prs: number;
  completed_prs: number;
  no_decisions_prs: number;
  failed_prs: number;
  total_decisions: number;
  relationships_processed: boolean;
  progress_percent: number; // Overall progress
  pr_processing_progress_percent: number; // Progress specific to PRs/commits
  relationship_processing_progress_percent: number; // Progress specific to relationships
  latest_pr_details?: Array<{
    pr_number: number;
    pr_title: string;
    status: 'completed' | 'pending' | 'in_progress' | 'failed' | 'no_decisions';
    decisions?: number;
    error?: string;
  }>;
  current_phase: 'populating_repository' | 'processing_prs' | 'processing_relationships' | 'completed' | 'error_phase';
}

// Define the main response type including the overall status
interface MainRepositoryStatusResponse {
  success: boolean;
  repositorySlug: string;
  analysisOverallStatus: 'not_started' | 'in_progress' | 'initial_analysis_complete' | 'completed' | 'error';
  message: string;
  historical_backfill_in_progress: boolean;
  details?: { // Optional: if you want to include counts or specific messages
    totalItems?: number;
    pendingItems?: number;
    completedItems?: number;
    errorItems?: number;
    pendingEarlyStageItems?: number;
    skippedNotSignificantItems?: number;
    lastProcessed?: string; // e.g., PR number or timestamp
  };
  error?: string;
  pendingPrCommitsCount?: number | null;
  pendingRelationshipsCount?: number | null;
  currentAnalysisStage?: string | null;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<MainRepositoryStatusResponse>
) {
  if (req.method === 'GET') {
    const { repositorySlug, isPublic, installationId } = req.query;

    if (!repositorySlug || typeof repositorySlug !== 'string' || !repositorySlug.includes('/')) {
      return res.status(400).json({ 
        success: false, 
        repositorySlug: '', 
        analysisOverallStatus: 'error',
        message: 'Invalid or missing repositorySlug parameter.',
        historical_backfill_in_progress: false,
        error: 'Invalid or missing repositorySlug parameter.'
      });
    }
    if (isPublic === undefined || (isPublic === 'false' && !installationId)) {
        return res.status(400).json({ 
            success: false, 
            repositorySlug: repositorySlug, 
            analysisOverallStatus: 'error', 
            message: 'isPublic parameter is required, and installationId is required for private repositories.',
            historical_backfill_in_progress: false,
            error: 'isPublic parameter is required, and installationId is required for private repositories.'
        });
    }

    const effectiveInstallationId = isPublic === 'true' ? 0 : parseInt(installationId as string, 10);

    try {
      console.log(`[API Repo Status] Checking status for ${repositorySlug}, InstallID: ${effectiveInstallationId}`);

      // Fetch all statuses for the given repository and installation ID
      const { data: allStatuses, error: fetchError } = await supabaseAdmin
        .from('repository_pr_analysis_status')
        .select('status')
        .eq('repository_slug', repositorySlug)
        .eq('installation_id', effectiveInstallationId);

      if (fetchError) {
        console.error(`[API Repo Status] Error fetching status for ${repositorySlug}:`, fetchError);
        return res.status(500).json({ 
          success: false, 
          repositorySlug: repositorySlug,
          analysisOverallStatus: 'error', 
          message: `Database error: ${fetchError.message}`,
          historical_backfill_in_progress: false,
          error: `Database error: ${fetchError.message}`
        });
      }

      if (!allStatuses || allStatuses.length === 0) {
        console.log(`[API Repo Status] No records found for ${repositorySlug}, InstallID: ${effectiveInstallationId}. Considering as 'not_started'.`);
        return res.status(200).json({ 
          success: true, 
          repositorySlug: repositorySlug,
          analysisOverallStatus: 'not_started', 
          message: 'Repository analysis has not been initiated yet.',
          historical_backfill_in_progress: false,
          pendingPrCommitsCount: null,
          pendingRelationshipsCount: null,
          currentAnalysisStage: null,
        });
      }

      const totalItems = allStatuses.length;
      const pendingItems = allStatuses.filter(item => item.status === 'pending').length;
      const pendingEarlyStageItems = allStatuses.filter(item => item.status === 'pending_early_stage').length;
      const skippedNotSignificantItems = allStatuses.filter(item => item.status === 'skipped_not_significant').length;
      const completedItems = allStatuses.filter(item => item.status === 'completed' || item.status === 'success' || item.status === 'analyzed' || item.status === 'no_decisions').length; // 'analyzed' and 'no_decisions' also mean completion for that item
      const errorItems = allStatuses.filter(item => item.status === 'failed' || item.status === 'error' || item.status === 'db_update_failed').length;
      
      const totalProcessedCount = completedItems + skippedNotSignificantItems;
      const hasPendingItems = pendingItems > 0 || pendingEarlyStageItems > 0;

      // Check if the "initial analysis" threshold has been met
      const INITIAL_ANALYSIS_THRESHOLD_COUNT = 100;
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

      let isInitialDateMet = false;
      if (totalProcessedCount > 0) {
        const { data: oldestProcessedItem, error: oldestItemError } = await supabaseAdmin
          .from('repository_pr_analysis_status')
          .select('pr_merged_at')
          .eq('repository_slug', repositorySlug)
          .eq('installation_id', effectiveInstallationId)
          .in('status', ['completed', 'no_decisions', 'skipped_not_significant'])
          .order('pr_merged_at', { ascending: true }) // Find the oldest processed item
          .limit(1)
          .single();
        
        if (!oldestItemError && oldestProcessedItem && new Date(oldestProcessedItem.pr_merged_at) <= threeMonthsAgo) {
          isInitialDateMet = true;
        }
      }

      const isInitialCountMet = totalProcessedCount >= INITIAL_ANALYSIS_THRESHOLD_COUNT;
      const isInitialAnalysisComplete = isInitialCountMet || isInitialDateMet;
      
      // Check commit loading job status
      let commitLoadingStatus = null;
      let commitLoadingQuery = supabaseAdmin
        .from('commit_loading_jobs')
        .select('*')
        .eq('repository_slug', repositorySlug);

      if (effectiveInstallationId) {
        commitLoadingQuery = commitLoadingQuery.eq('installation_id', effectiveInstallationId);
      } else {
        commitLoadingQuery = commitLoadingQuery.eq('installation_id', 0);
      }

      const { data: commitJobs, error: commitJobError } = await commitLoadingQuery;
      
      if (!commitJobError && commitJobs && commitJobs.length > 0) {
        commitLoadingStatus = commitJobs[0];
      }
      
      // Determine overall status and current stage
      let overallStatus: 'not_started' | 'in_progress' | 'initial_analysis_complete' | 'completed' | 'error' = 'not_started';
      let currentStage: string | null = null;
      let historicalBackfillInProgress = false;

      if (errorItems > 0) {
        overallStatus = 'error';
        currentStage = `Analysis errors (${errorItems} failed items)`;
        historicalBackfillInProgress = hasPendingItems;
      } else if (totalItems === 0) {
        overallStatus = 'not_started';
      } else if (hasPendingItems || (commitLoadingStatus && !commitLoadingStatus.is_completed)) {
        // Analysis is running. Check if it has passed the initial threshold.
        historicalBackfillInProgress = true;
        if (isInitialAnalysisComplete) {
          overallStatus = 'initial_analysis_complete';
          currentStage = `Historical analysis in progress... (${totalProcessedCount} items analyzed)`;
        } else {
        overallStatus = 'in_progress';
          // Determine specific sub-stage of "in_progress"
        if (commitLoadingStatus && !commitLoadingStatus.is_completed) {
          const loadingProgress = commitLoadingStatus.current_page || 1;
            currentStage = `Loading commits & PRs (page ${loadingProgress}, ${totalProcessedCount} analyzed so far)`;
        } else if (pendingEarlyStageItems > 0) {
            currentStage = `Filtering PRs/Commits (${pendingEarlyStageItems} pending LLM review, ${totalProcessedCount} analyzed)`;
        } else if (pendingItems > 0) {
            currentStage = `Deep analysis (${pendingItems} pending, ${totalProcessedCount} analyzed)`;
          }
        }
      } else {
        // No pending items of any kind, so the analysis is fully complete.
          overallStatus = 'completed';
        historicalBackfillInProgress = false;
          currentStage = null;
      }

      return res.status(200).json({
        success: true,
        repositorySlug: repositorySlug,
        analysisOverallStatus: overallStatus,
        historical_backfill_in_progress: historicalBackfillInProgress,
        message: overallStatus === 'completed' 
          ? `Analysis complete for ${repositorySlug}. ${completedItems} items analyzed.`
          : overallStatus === 'in_progress'
          ? `Analysis in progress for ${repositorySlug}. ${completedItems}/${totalItems} items completed.`
          : overallStatus === 'error'
          ? `Analysis errors for ${repositorySlug}. ${errorItems} items failed.`
          : `No analysis started for ${repositorySlug}`,
        details: {
          totalItems,
          pendingItems,
          completedItems,
          errorItems,
          pendingEarlyStageItems,
          skippedNotSignificantItems,
          lastProcessed: totalItems > 0 ? `${totalItems} total items` : undefined
        },
        pendingPrCommitsCount: pendingItems + pendingEarlyStageItems,
        pendingRelationshipsCount: null,
        currentAnalysisStage: currentStage
      });

    } catch (error) {
      console.error('[API Repo Status] General error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      return res.status(500).json({ 
        success: false, 
        repositorySlug: repositorySlug as string, // Might be undefined if error is very early
        analysisOverallStatus: 'error',
        message: `Failed to get repository status: ${errorMessage}`,
        historical_backfill_in_progress: false,
        error: `Failed to get repository status: ${errorMessage}`
      });
    }
  } else {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ 
        success: false, 
        repositorySlug: '',
        analysisOverallStatus: 'error',
        message: `Method ${req.method} Not Allowed`,
        historical_backfill_in_progress: false,
        error: `Method ${req.method} Not Allowed`
    });
  }
} 