import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { processDesignDocJob } from '../../../lib/designDocProcessor';

// Define response types
interface ProcessResponse {
  success: boolean;
  message?: string;
  error?: string;
  details?: any;
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const CRON_SECRET_VALUE = process.env.CRON_SECRET;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error(
    'Supabase URL or Service Role Key not configured for Design Doc Worker.'
  );
}

const supabaseAdmin: SupabaseClient = createClient(supabaseUrl, supabaseServiceRoleKey);

// Helper to update job status
async function updateJobStatus(jobId: string, status: string, current_phase: string, errorMessage?: string) {
  const updates: any = {
    status,
    current_phase,
    updated_at: new Date().toISOString(),
  };
  if (errorMessage) {
    updates.error_message = errorMessage;
  }
  const { error } = await supabaseAdmin
    .from('design_doc_jobs')
    .update(updates)
    .eq('id', jobId);

  if (error) {
    console.error(`[Worker Job ${jobId}] Error updating job status to ${status}:`, error);
  } else {
    console.log(`[Worker Job ${jobId}] Status updated to ${status}, phase ${current_phase}`);
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ProcessResponse>
) {
  console.log('[Design Doc Worker] Received request:', req.method);

  // Validate request method
  if (req.method !== 'POST' && req.method !== 'GET') {
    res.setHeader('Allow', ['POST', 'GET']);
    return res.status(405).json({
      success: false,
      error: `Method ${req.method} Not Allowed`
    });
  }

  // Validate secret
  if (CRON_SECRET_VALUE) {
    const providedSecret = req.headers.authorization?.split(' ')[1] || req.query.secret as string;
    if (providedSecret !== CRON_SECRET_VALUE) {
      console.warn("[Design Doc Worker] Unauthorized attempt: Incorrect or missing secret.");
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Incorrect or missing secret.'
      });
    }
  } else {
    console.warn("[Design Doc Worker] WARNING: CRON_SECRET environment variable is not set.");
  }

  let jobToProcess = null;

  try {
    // 1. Fetch a pending job
    const { data: pendingJobs, error: fetchError } = await supabaseAdmin
      .from('design_doc_jobs')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: true })
      .limit(1)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('[Design Doc Worker] Error fetching pending job:', fetchError);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch pending job',
        details: fetchError
      });
    }

    if (!pendingJobs) {
      console.log('[Design Doc Worker] No pending jobs found.');
      return res.status(200).json({
        success: true,
        message: 'No pending jobs to process.'
      });
    }

    jobToProcess = pendingJobs;
    const jobId = jobToProcess.id;
    console.log(`[Worker Job ${jobId}] Picked up job. Current phase: ${jobToProcess.current_phase}`);

    // 2. Mark the job as 'processing'
    await updateJobStatus(jobId, 'processing', jobToProcess.current_phase || 'processing_started');

    // 3. Process the job
    console.log(`[Worker Job ${jobId}] Starting processing...`);
    await processDesignDocJob(jobId);
    console.log(`[Worker Job ${jobId}] Processing completed by processDesignDocJob.`);

    return res.status(200).json({
      success: true,
      message: `Job ${jobId} processed.`,
      details: { jobId, status: 'completed' }
    });

  } catch (error: any) {
    console.error('[Design Doc Worker] Error processing job:', error);
    if (jobToProcess && jobToProcess.id) {
      await updateJobStatus(
        jobToProcess.id,
        'error',
        jobToProcess.current_phase || 'error_state',
        error.message
      );
      return res.status(500).json({
        success: false,
        error: `Error processing job ${jobToProcess.id}: ${error.message}`,
        details: { jobId: jobToProcess.id, error: error.message }
      });
    }
    return res.status(500).json({
      success: false,
      error: 'An unknown error occurred in the worker.',
      details: { error: error.message }
    });
  }
} 