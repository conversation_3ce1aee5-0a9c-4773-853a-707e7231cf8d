import type { Session } from '@supabase/supabase-js';

// Types for the wizard flow
export interface TaskDetails {
  title: string;
  description: string;
  initialIdeas?: string;
  isExperimental?: boolean;
}

// Technical priorities type
export const TECHNICAL_PRIORITIES = [
  { id: 'time-to-market', name: 'Time to Market', description: 'Prioritizes speed of delivery and simplicity of implementation.' },
  { id: 'reliability', name: 'Reliability', description: 'Focuses on building a stable, fault-tolerant, and resilient system.' },
  { id: 'scalability', name: 'Scalability', description: 'Ensures the system can handle growth in users, data, and traffic.' },
  { id: 'maintainability', name: 'Maintainability', description: 'Prioritizes ease of future updates, debugging, and understanding.' },
  { id: 'cost-of-ownership', name: 'Cost of Ownership', description: 'Considers long-term expenses including infrastructure, licenses, and developer time.' },
  { id: 'developer-experience', name: 'Developer Experience', description: 'Focuses on how easy and efficient it is for developers to work on the system.' },
  { id: 'strategic-alignment', name: 'Strategic Alignment', description: 'Aligns with long-term product vision, existing tech, and team skills.' },
] as const;

export type PriorityId = typeof TECHNICAL_PRIORITIES[number]['id'];

// Decision Review Configuration Types
export type DecisionReviewMode = 'detailed' | 'summary' | 'auto';

export interface DecisionReviewCriteria {
  // Technical Impact Criteria
  newTechnologies: boolean; // New frameworks, libraries, languages
  schemaChanges: boolean;   // Database schema modifications
  architecturalChanges: boolean; // Changes to system architecture
  externalDependencies: boolean; // New external services or APIs
  securityChanges: boolean; // Authentication, authorization, security models
  performanceImpact: boolean; // Changes that could significantly affect performance
  
  // Risk & Complexity Thresholds
  highRiskDecisions: boolean; // Decisions marked as high risk
  highComplexityDecisions: boolean; // Decisions marked as high complexity
  highMaintenanceBurden: boolean; // Decisions with high maintenance burden
  
  // Strategic Criteria
  deviatesFromStandards: boolean; // Decisions that deviate from existing patterns
  affectsMultipleServices: boolean; // Cross-service or cross-domain impact
  irreversibleChanges: boolean; // Changes that are hard to undo
  
  // Custom Criteria
  customKeywords: string[]; // User-defined keywords that trigger review
}

export interface DecisionReviewPreferences {
  // Global review mode
  defaultReviewMode: DecisionReviewMode;
  
  // Step-by-step configuration
  reviewStepByStep: boolean; // Whether to show each step or auto-advance
  
  // Criteria for requiring detailed review
  requireReviewCriteria: DecisionReviewCriteria;
  
  // Auto-processing settings
  autoSelectRecommendedOptions: boolean; // Auto-select recommended options for low-impact decisions
  showAutoProcessingSummary: boolean; // Show summary of auto-processed decisions
  
  // UI preferences
  showProgressIndicators: boolean;
  showDecisionSummaries: boolean;
  enableQuickReview: boolean; // Allow quick approve/modify workflow
}

export const DEFAULT_REVIEW_CRITERIA: DecisionReviewCriteria = {
  newTechnologies: true,
  schemaChanges: true,
  architecturalChanges: true,
  externalDependencies: true,
  securityChanges: true,
  performanceImpact: true,
  highRiskDecisions: true,
  highComplexityDecisions: true,
  highMaintenanceBurden: true,
  deviatesFromStandards: true,
  affectsMultipleServices: true,
  irreversibleChanges: true,
  customKeywords: []
};

export const DEFAULT_REVIEW_PREFERENCES: DecisionReviewPreferences = {
  defaultReviewMode: 'summary',
  reviewStepByStep: false,
  requireReviewCriteria: DEFAULT_REVIEW_CRITERIA,
  autoSelectRecommendedOptions: true,
  showAutoProcessingSummary: true,
  showProgressIndicators: true,
  showDecisionSummaries: true,
  enableQuickReview: true
};

// Decision Classification Results
export interface DecisionClassification {
  requiresReview: boolean;
  reviewMode: DecisionReviewMode;
  reasons: string[]; // Why this decision needs review
  impactScore: number; // 0-100 impact score
  riskFactors: string[]; // Specific risk factors identified
}

// Enhanced Decision Processing State
export interface DecisionProcessingResult {
  decisionId: string;
  classification: DecisionClassification;
  selectedOption?: string;
  rationale?: string;
  alternatives?: Array<{
    option: DecisionOption;
    reason: string;
  }>;
  autoProcessed: boolean;
  userOverride?: boolean;
}

// Project Constitution Interface
export interface ProjectConstitution {
  companyStage: 'startup' | 'growth' | 'mature' | 'not-set';
  projectType: 'b2c-saas' | 'b2b-saas' | 'library' | 'internal-tool' | 'data-platform' | 'other' | 'not-set';
  priorities: PriorityId[];
  architecturalPrinciples: string;
  productAndBusinessContext: string;
  primaryLanguage: string;
  userPersonas?: string[];
  
  // Decision Review Preferences
  decisionReviewPreferences?: DecisionReviewPreferences;
  
  // Strategic Assessment Preferences
  strategicAssessmentPreferences?: StrategicAssessmentPreferences;
}

// Graph-based interfaces to match the API
export interface EnrichedDecision {
  id: string;
  title: string;
  summary: string;
  rationale?: string;
  implications?: string;
  dev_prompt?: string;
  related_files?: string[];
  relevanceScore: number;
  relationshipContext: string[];
  core_architectural_characteristics: string[];
}

export interface DecisionCluster {
  coreDecisions: EnrichedDecision[];
  supportingDecisions: EnrichedDecision[];
  constraintDecisions: EnrichedDecision[];
  impactedDecisions: EnrichedDecision[];
  patternDecisions: EnrichedDecision[];
}

export interface KnowledgeGraphEdge {
  id: number;
  created_at: string;
  source_decision_id: string;
  repository_slug: string;
  subject: string;
  predicate: string;
  object: string;
}

export interface GraphContext {
  clusters: DecisionCluster;
  edges: KnowledgeGraphEdge[];
  seedDecisionIds: string[];
  analysisNarrative: string;
}

// Represents the output of the Phase 2 analysis for a single decision point
export interface DecisionPointAnalysis {
  core_problem_statement: string;
  past_decisions_analysis: {
    past_decision_id: string;
    past_decision_title: string;
    relevance_explanation: string;
  }[];
  initial_proposal_evaluation: {
    proposal_present: boolean;
    proposal_description: string;
    technical_assessment: {
      strengths: string[];
      weaknesses: string[];
      past_context_alignment: string;
      assessment_summary: string;
    };
  };
  potential_approaches: {
    approach_name: string;
    description: string;
    source: string;
  }[];
  recommended_approach: {
    approach_name: string;
    justification: string;
    alignment_details: {
      score: number;
      supporting_decisions: {
        decision_id: string;
        alignment_reason: string;
      }[];
    };
    override_justification_prompt: string;
  };
  overall_summary_of_context: string;
}

export interface DecisionOption {
  id: string;
  name: string;
  description: string;
  pros: string[];
  cons: string[];
  mitigations?: string[]; // Mitigation analysis for cons, only for recommended option
  riskLevel: 'low' | 'medium' | 'high';
  complexity: 'low' | 'medium' | 'high';
  maintenanceBurden: 'low' | 'medium' | 'high';
  debuggingComplexity: string;
  architecturalAlignment: 'high' | 'medium' | 'low';
  alignmentJustification: string;
  changeScope: 'minimal' | 'moderate' | 'significant';
  alignmentScore?: number;
  alignmentReasoning?: string;
  conflictingDecisions?: string[];
  isBasedOnInitialProposal?: boolean;
  initialProposalAssessment?: string;
}

export interface RelatedDecision {
  id: string;
  title: string;
  summary: string;
  relevanceScore: number;
  implications: string[];
  guidance?: string;
}

export interface DecisionPoint {
  id: string;
  title: string;
  description: string;
  category?: 'architecture' | 'technology' | 'data' | 'integration' | 'security' | 'performance';
  priority?: 'high' | 'medium' | 'low';
  options?: DecisionOption[];
  selectedOption?: string;
  rationale?: string;
  // Updated to use new graph context instead of relatedDecisions
  graphContext?: GraphContext;
  relatedDecisions?: RelatedDecision[]; // Keep for backward compatibility during transition
  searchQueries?: string[];
  initial_proposal_to_evaluate?: string;
  analysis?: DecisionPointAnalysis; // To store Phase 2 output
  recommended_option_id?: string;
  recommendation_rationale?: string;
}

// New types for enhanced decision removal
export type DecisionRemovalReason = 
  | 'already-decided'    // We've already made this choice
  | 'out-of-scope'      // This is a non-goal/out of scope
  | 'not-relevant'      // Not relevant to this specific task
  | 'duplicate'         // Duplicate of another decision
  | 'custom';           // User provides custom reasoning

export interface RemovedDecisionContext {
  decisionId: string;
  decisionTitle: string;
  decisionDescription: string;
  removalReason: DecisionRemovalReason;
  contextNote: string; // User's explanation
  capturedAs: 'constraint' | 'non-goal' | 'assumption' | 'note'; // Where the context was stored
  removedAt: string; // ISO timestamp
}

export interface ProjectConstraint {
  id: string;
  title: string;
  description: string;
  source: 'user' | 'removed-decision' | 'constitution';
  createdAt: string;
}

export interface UserJourney {
  id: string; // Auto-generated UUID
  title: string; // AI-generated, user-editable
  description?: string; // Optional context
  userRole: string; // e.g., 'Guest User', 'Admin', 'Logged-in User'
  steps: string[]; // List of user-facing actions
  status: 'new' | 'modified' | 'unchanged' | 'draft' | 'deprecated'; // To track changes
  isDraft: boolean; // True if AI-generated and not yet confirmed by user
}

export interface TaskAnalysis {
  summary: string;
  functional_requirements: string[];
  functional_requirements_diagram: string;
  implied_NFRs: { nfr: string; evidence: string }[];
  core_architectural_characteristics: string[];
  non_goals?: string[];
}

export interface GitHubIssue {
  number: number;
  title: string;
  body: string | null;
  html_url: string;
  assignee?: {
    login: string;
  } | null;
  user: {
    login: string;
  };
}

// Strategic Assessment Configuration Types
export type StrategicAssessmentMode = 'detailed' | 'summary' | 'auto-build' | 'skip';

export interface StrategicAssessmentCriteria {
  // Project characteristics that require detailed strategic review
  newProjectTypes: boolean; // Projects significantly different from past work
  highComplexityFeatures: boolean; // Features with high maintenance burden
  majorArchitecturalChanges: boolean; // Changes affecting system architecture
  crossTeamDependencies: boolean; // Features requiring coordination across teams
  regulatoryCompliance: boolean; // Features with compliance implications
  performanceCritical: boolean; // Features with significant performance impact
  
  // Business impact criteria  
  highBusinessRisk: boolean; // Features with high business risk if wrong
  strategicInitiatives: boolean; // Features tied to strategic company initiatives
  customerCommitments: boolean; // Features with customer commitments/deadlines
  competitiveAdvantage: boolean; // Features providing competitive differentiation
  
  // Resource & timing criteria
  longTermMaintenance: boolean; // Features with long-term maintenance implications
  irreversibleDecisions: boolean; // Decisions hard to reverse later
  
  // Custom criteria
  customKeywords: string[]; // Custom keywords that trigger strategic review
}

export interface StrategicAssessmentPreferences {
  // Global assessment mode
  assessmentMode: StrategicAssessmentMode;
  
  // Criteria for requiring detailed strategic review
  requireReviewCriteria: StrategicAssessmentCriteria;
  
  // Auto-processing settings
  autoBuildOnPositiveAssessment: boolean; // Auto-proceed when assessment recommends BUILD
  showAssessmentSummary: boolean; // Show summary of assessment results
  
  // Experimental feature handling
  skipForExperimentalFeatures: boolean; // Skip assessment for experimental features
  
  // Threshold settings
  confidenceLevelThreshold: 'any' | 'medium' | 'high'; // Minimum confidence for auto-processing
}

export const DEFAULT_STRATEGIC_ASSESSMENT_CRITERIA: StrategicAssessmentCriteria = {
  newProjectTypes: true,
  highComplexityFeatures: true,
  majorArchitecturalChanges: true,
  crossTeamDependencies: true,
  regulatoryCompliance: true,
  performanceCritical: true,
  highBusinessRisk: true,
  strategicInitiatives: true,
  customerCommitments: true,
  competitiveAdvantage: true,
  longTermMaintenance: true,
  irreversibleDecisions: true,
  customKeywords: []
};

export const DEFAULT_STRATEGIC_ASSESSMENT_PREFERENCES: StrategicAssessmentPreferences = {
  assessmentMode: 'summary',
  requireReviewCriteria: DEFAULT_STRATEGIC_ASSESSMENT_CRITERIA,
  autoBuildOnPositiveAssessment: true,
  showAssessmentSummary: true,
  skipForExperimentalFeatures: true,
  confidenceLevelThreshold: 'medium'
};

// Strategic Assessment Classification Results
export interface StrategicAssessmentClassification {
  requiresReview: boolean;
  reviewMode: StrategicAssessmentMode;
  reasons: string[]; // Why this assessment needs review
  riskScore: number; // 0-100 risk score
  complexityFactors: string[]; // Specific complexity factors identified
}

// Strategic Assessment Processing Result
export interface StrategicAssessmentProcessingResult {
  classification: StrategicAssessmentClassification;
  assessment?: any; // The actual assessment result from the API
  autoProcessed: boolean;
  recommendation?: 'BUILD' | 'DOCUMENT' | 'DEFER';
  rationale?: string;
  userOverride?: boolean;
}

export interface StrategicAssessmentAlternative {
  approach: string;
  effort_required: 'low' | 'medium' | 'high';
  value_delivered: string;
  implementation_notes: string;
}

export interface StrategicAssessment {
  recommendation: 'BUILD' | 'DOCUMENT' | 'DEFER';
  confidence_level: 'low' | 'medium' | 'high';
  strategic_rationale: string;
  complexity_assessment: {
    implementation_complexity: 'low' | 'medium' | 'high';
    maintenance_burden: 'low' | 'medium' | 'high';
    user_value_delivered: 'low' | 'medium' | 'high';
    complexity_justification: string;
  };
  alignment_analysis: {
    constitutional_alignment: 'strong' | 'moderate' | 'weak';
    priority_alignment: string;
    stage_appropriateness: string;
    strategic_concerns: string[];
  };
  recommended_alternatives: StrategicAssessmentAlternative[];
  override_justification_needed: string;
  next_steps: string;
}

export interface WizardState {
  sessionId: string; // Add unique session ID for persistence
  currentStep: 'initializing' | 'constitution' | 'deployment-constitution-check' | 'task-definition' | 'user-journey-definition' | 'requirements-review' | 'task-verification' | 'strategic-assessment' | 'decision-discovery' | 'decision-making' | 'review-generation';
  taskDetails: TaskDetails;
  userJourneys?: UserJourney[]; // Add user journeys to the state
  decisionPoints: DecisionPoint[];
  currentDecisionIndex: number;
  generatedDoc?: any;
  isLoading: boolean;
  error?: string;
  isEditingDecisions?: boolean;
  nonGoals?: string[];
  isEditingNonGoals?: boolean;
  isEditingTaskDetails?: boolean;
  isEditingOptions?: boolean;
  taskAnalysis?: TaskAnalysis;
  isEditingRequirements?: boolean;
  projectConstitution?: ProjectConstitution;
  isSeedingConstitution?: boolean;
  isSavingConstitution?: boolean;
  // New fields for enhanced decision removal
  removedDecisions?: RemovedDecisionContext[];
  projectConstraints?: ProjectConstraint[];
  showRemovalModal?: boolean;
  removalModalDecisionId?: string;
  showRemovedDecisions?: boolean;
  strategicAnalysis?: {
    core_vs_deferrable_analysis?: any;
    strategic_recommendations?: any;
  };
  strategicAssessment?: StrategicAssessment;
  isMinimalScope?: boolean; // Flag to track when we're assessing a minimal scope version
  
  // Decision Processing State
  decisionProcessingResults?: DecisionProcessingResult[];
  autoProcessedDecisions?: DecisionPoint[];
  pendingReviewDecisions?: DecisionPoint[];
  showDecisionProcessingSummary?: boolean;
  
  // Strategic Assessment Processing State
  strategicAssessmentProcessingResult?: StrategicAssessmentProcessingResult;
  showStrategicAssessmentSummary?: boolean;
  
  // Implementation and Rollout Plans
  implementationPlan?: any;
  rolloutStrategy?: any;
  
  // Deployment Constitution State
  hasDeploymentConstitution?: boolean;
  isCheckingDeploymentConstitution?: boolean;
  deploymentConstitutionError?: string;
  isGeneratingDeploymentConstitution?: boolean;
}

// RCA Wizard Types
export interface RCASymptoms {
  errorType: string;
  affectedComponents: string[];
  userImpact: string;
  timePattern: string;
  environment: string;
  errorLogs?: string;
  affectedFiles?: string[];
  reproductionSteps?: string;
  businessImpact?: string;
}

export interface RCAHypothesis {
  hypothesis: string;
  likelihood: 'HIGH' | 'MEDIUM' | 'LOW';
  reasoning: string;
  search_queries: string[];
  expected_decision_patterns: string;
}

export interface RCAInvestigationPlan {
  symptom_analysis?: {
    user_action: string;
    observed_behavior: string;
    expected_behavior: string;
    affected_functionality: string;
    timeline_clues: string;
  };
  issue_classification?: {
    primary_type: 'REGRESSION' | 'MISSING_FEATURE' | 'DESIGN_FLAW' | 'INTEGRATION_ISSUE' | 'ENVIRONMENTAL_ISSUE';
    confidence: number;
    classification_reasoning: string;
  };
  critical_information_gaps?: {
    severity_assessment: {
      missing_info: string[];
      impact_on_analysis: string;
      recommended_questions: string[];
    };
    technical_context: {
      missing_info: string[];
      impact_on_analysis: string;
      recommended_questions: string[];
    };
    timeline_causation: {
      missing_info: string[];
      impact_on_analysis: string;
      recommended_questions: string[];
    };
    validation_concerns: {
      missing_info: string[];
      impact_on_analysis: string;
      recommended_questions: string[];
    };
    overall_confidence: number;
    blocking_gaps: string[];
    investigation_readiness: 'READY' | 'NEEDS_MORE_INFO' | 'REQUIRES_VALIDATION';
  };
  investigation_hypotheses: RCAHypothesis[];
  investigation_strategy: string;
  next_steps?: string;
}

export interface RCADecisionSuspicion {
  decision_id: string;
  causation_confidence: number;
  failure_mechanism: string;
  evidence: string[];
  timeline_fit: string;
}

export interface RCAContributingFactor {
  decision_id: string;
  contribution_type: 'amplifier' | 'enabler' | 'catalyst';
  explanation: string;
}

export interface RCAForensicAnalysis {
  primary_root_cause: RCADecisionSuspicion;
  contributing_factors: RCAContributingFactor[];
  failure_chain: string;
  alternative_theories: string[];
}

export interface RCATimelineEvent {
  decision_id: string;
  title: string;
  date: string;
  cascade_role: 'root' | 'amplifier' | 'enabler' | 'trigger';
  contribution: string;
}

export interface RCATimelineForensics {
  root_cause_decision: {
    decision_id: string;
    title: string;
    date: string;
    why_root_cause: string;
  };
  failure_incubation: {
    duration_days: number;
    enabling_conditions: string[];
    missed_warning_signs: string[];
  };
  decision_cascade: RCATimelineEvent[];
  prevention_insights: {
    decision_quality_patterns: string[];
    architectural_violations: string[];
    recommended_gates: string[];
  };
}

export interface RCAWizardState {
  sessionId: string;
  currentStep: 'symptom-analysis' | 'investigation-planning' | 'decision-discovery' | 'forensic-analysis' | 'timeline-analysis' | 'pattern-analysis' | 'report-generation';
  symptoms: RCASymptoms;
  investigationPlan?: RCAInvestigationPlan;
  suspiciousDecisions?: any[];
  forensicAnalysis?: RCAForensicAnalysis;
  timelineForensics?: RCATimelineForensics;
  finalReport?: any;
  isLoading: boolean;
  error?: string;
  repositorySlug?: string;
  installationId?: string;
  isPublic?: boolean;
  issues?: any[];
  selectedIssue?: any;
}

// Hook Props Interface
export interface UseDesignDocActionsProps {
  wizardState: WizardState;
  setWizardState: React.Dispatch<React.SetStateAction<WizardState>>;
  repositorySlug: string;
  installationId: string;
  isPublic: boolean;
  supabase: any;
  apiKey: string;
  githubHandle: string;
  setIssues: React.Dispatch<React.SetStateAction<GitHubIssue[]>>;
  setIsLoadingIssues: React.Dispatch<React.SetStateAction<boolean>>;
} 