import React from 'react';

/**
 * Utility functions for handling decision references in text
 */

export interface DecisionReference {
  id: string;
  metadata: {
    title?: string;
    description?: string;
    rationale?: string;
    implications?: string;
    related_files?: string[];
    domain_concepts?: string[];
    pr_url?: string;
    dev_prompt?: string;
    risks_extracted?: string;
    pr_number?: number | string;
    pr_merged_at?: number | string;
    [key: string]: any;
  };
}

// Helper to get display values from the decision metadata
export function getDecisionDisplayValues(decision: DecisionReference) {
  return {
    title: decision.metadata?.title || 'Untitled Decision',
    description: decision.metadata?.description,
    dev_prompt: decision.metadata?.dev_prompt,
    implications: decision.metadata?.implications,
    related_files: decision.metadata?.related_files || [],
    pr_url: decision.metadata?.pr_url,
    follows_standard_practice_reason: decision.metadata?.follows_standard_practice_reason,
    rationale: decision.metadata?.rationale,
    risks_extracted: decision.metadata?.risks_extracted,
    confidence_score: decision.metadata?.confidence_score,
  };
}

/**
 * Parses text for decision ID references in the format "Decision ID-123" or "Decision ID-abc"
 * and replaces them with clickable decision titles
 */
export function parseDecisionReferences(
  text: string,
  decisions: Record<string, DecisionReference>,
  onDecisionClick: (decisionId: string) => void
): JSX.Element[] {
  if (!text) return [<span key="empty"></span>];

  console.log(`[parseDecisionReferences] Processing text: "${text.substring(0, 100)}...")`);
  console.log(`[parseDecisionReferences] Available decisions:`, Object.keys(decisions));

  // Updated regex to handle multiple decision ID formats:
  // - "Decision decision_commit_231281ad_1693247798000_1752571762511_25161dd9" (commit-based format with prefix)
  // - "decision_commit_231281ad_1693247798000_1752571762511_25161dd9" (standalone commit-based format) - PRIMARY
  // - "Decision decision_1342_1752529271439_e9fb892b" (numeric-based format with prefix)
  // - "decision_1342_1752529271439_e9fb892b" (standalone numeric-based format) - PRIMARY
  // - "Decision ID-123" (legacy simple format) - FALLBACK ONLY
  const decisionIdRegex = /(?:Decision (ID-[a-zA-Z0-9_-]+|decision_(?:commit_)?[a-zA-Z0-9_-]+)|(decision_(?:commit_)?[a-zA-Z0-9_-]+))/g;
  const parts: JSX.Element[] = [];
  let lastIndex = 0;
  let match;

  while ((match = decisionIdRegex.exec(text)) !== null) {
    // Group 1: decisions with "Decision" prefix, Group 2: standalone decision IDs
    let decisionId = match[1] || match[2];
    // Remove "ID-" prefix if present, since our decision records use the raw ID
    if (decisionId && decisionId.startsWith('ID-')) {
      decisionId = decisionId.substring(3);
    }
    
    const decision = decisions[decisionId];
    console.log(`[parseDecisionReferences] Looking up decision ${decisionId}, found:`, !!decision);
    
    // Add text before the match
    if (match.index > lastIndex) {
      parts.push(
        <span key={`text-${lastIndex}`}>
          {text.substring(lastIndex, match.index)}
        </span>
      );
    }

    // Add clickable decision reference
    if (decision) {
      const { title } = getDecisionDisplayValues(decision);
      console.log(`[parseDecisionReferences] Creating link for ${decisionId} with title: ${title}`);
      parts.push(
        <button
          key={`decision-${decisionId}`}
          onClick={(e) => {
            e.stopPropagation(); // Prevent parent click
            onDecisionClick(decisionId);
          }}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline font-medium"
        >
          {title}
        </button>
      );
    } else {
      // Fallback if decision not found - show the original text
      console.log(`[parseDecisionReferences] Decision ${decisionId} not found in available decisions`);
      parts.push(
        <span key={`missing-${decisionId}`} className="text-gray-500 italic">
          Decision {decisionId} (details not available)
        </span>
      );
    }

    lastIndex = decisionIdRegex.lastIndex;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    parts.push(
      <span key={`text-end`}>
        {text.substring(lastIndex)}
      </span>
    );
  }

  return parts.length > 0 ? parts : [<span key="original">{text}</span>];
}

/**
 * Component to render text with decision references as clickable links
 */
interface DecisionLinkedTextProps {
  text: string;
  decisions: Record<string, DecisionReference>;
  onDecisionClick: (decisionId: string) => void;
  className?: string;
}

export function DecisionLinkedText({ 
  text, 
  decisions, 
  onDecisionClick, 
  className = "" 
}: DecisionLinkedTextProps) {
  const elements = parseDecisionReferences(text, decisions, onDecisionClick);
  
  return (
    <span className={className}>
      {elements}
    </span>
  );
} 