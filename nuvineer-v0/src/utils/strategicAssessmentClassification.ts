import type { 
  TaskDetails,
  TaskAnalysis,
  StrategicAssessmentCriteria, 
  StrategicAssessmentPreferences,
  StrategicAssessmentClassification,
  StrategicAssessmentProcessingResult
} from '../types/design-doc-wizard';

/**
 * Intelligently classifies strategic assessments to determine if they require user review
 * based on project characteristics, business impact, and user-configured criteria.
 */
export class StrategicAssessmentClassifier {
  
  /**
   * Classifies whether a strategic assessment requires user review
   */
  static classifyAssessment(
    taskDetails: TaskDetails,
    taskAnalysis: TaskAnalysis,
    criteria: StrategicAssessmentCriteria
  ): StrategicAssessmentClassification {
    const reasons: string[] = [];
    const complexityFactors: string[] = [];
    let riskScore = 0;

    // Project Characteristics Analysis
    riskScore += this.analyzeProjectCharacteristics(taskDetails, taskAnalysis, criteria, reasons, complexityFactors);
    
    // Business Impact Analysis  
    riskScore += this.analyzeBusinessImpact(taskDetails, taskAnalysis, criteria, reasons, complexityFactors);
    
    // Resource & Timing Analysis
    riskScore += this.analyzeResourceTiming(taskDetails, taskAnalysis, criteria, reasons, complexityFactors);
    
    // Content Analysis for Keywords
    riskScore += this.analyzeContent(taskDetails, criteria, reasons, complexityFactors);

    // Normalize risk score to 0-100
    riskScore = Math.min(riskScore, 100);

    // Determine review requirement and mode
    const requiresReview = riskScore >= 30 || reasons.length > 0;
    const reviewMode = riskScore >= 70 ? 'detailed' : 
                      riskScore >= 30 ? 'summary' : 'auto-build';

    return {
      requiresReview,
      reviewMode,
      reasons,
      riskScore,
      complexityFactors
    };
  }

  /**
   * Analyzes project characteristics that may require strategic review
   */
  private static analyzeProjectCharacteristics(
    taskDetails: TaskDetails,
    taskAnalysis: TaskAnalysis,
    criteria: StrategicAssessmentCriteria,
    reasons: string[],
    complexityFactors: string[]
  ): number {
    let score = 0;
    const title = taskDetails.title.toLowerCase();
    const description = taskDetails.description.toLowerCase();

    // New Project Types
    if (criteria.newProjectTypes) {
      const newProjectKeywords = [
        'new platform', 'new service', 'new product', 'first time', 'pilot',
        'proof of concept', 'poc', 'experiment', 'innovation', 'greenfield'
      ];
      
      if (this.containsKeywords(title + ' ' + description, newProjectKeywords)) {
        reasons.push('Project involves new or unfamiliar domain/technology');
        complexityFactors.push('New project type complexity');
        score += 25;
      }
    }

    // High Complexity Features
    if (criteria.highComplexityFeatures) {
      const complexityKeywords = [
        'complex', 'sophisticated', 'advanced', 'enterprise', 'scalable',
        'distributed', 'real-time', 'high performance', 'machine learning',
        'ai', 'algorithm', 'optimization'
      ];
      
      if (this.containsKeywords(title + ' ' + description, complexityKeywords)) {
        reasons.push('Feature has high technical complexity');
        complexityFactors.push('Technical implementation complexity');
        score += 20;
      }
    }

    // Major Architectural Changes
    if (criteria.majorArchitecturalChanges) {
      const archKeywords = [
        'architecture', 'redesign', 'refactor', 'migrate', 'system design',
        'infrastructure', 'platform change', 'core system', 'fundamental change'
      ];
      
      if (this.containsKeywords(title + ' ' + description, archKeywords)) {
        reasons.push('Involves major architectural changes');
        complexityFactors.push('Architectural impact');
        score += 30;
      }
    }

    // Cross-team Dependencies
    if (criteria.crossTeamDependencies) {
      const teamKeywords = [
        'cross-team', 'multiple teams', 'coordination', 'collaboration',
        'dependencies', 'integration', 'shared service', 'platform team',
        'backend team', 'frontend team', 'mobile team'
      ];
      
      if (this.containsKeywords(title + ' ' + description, teamKeywords)) {
        reasons.push('Requires coordination across multiple teams');
        complexityFactors.push('Cross-team coordination complexity');
        score += 25;
      }
    }

    // Regulatory Compliance
    if (criteria.regulatoryCompliance) {
      const complianceKeywords = [
        'compliance', 'regulation', 'gdpr', 'hipaa', 'sox', 'pci',
        'security audit', 'privacy', 'data protection', 'legal',
        'certification', 'audit trail'
      ];
      
      if (this.containsKeywords(title + ' ' + description, complianceKeywords)) {
        reasons.push('Has regulatory or compliance implications');
        complexityFactors.push('Regulatory compliance risk');
        score += 35;
      }
    }

    // Performance Critical
    if (criteria.performanceCritical) {
      const perfKeywords = [
        'performance', 'optimization', 'scaling', 'latency', 'throughput',
        'load', 'high volume', 'mission critical', 'sla', 'uptime'
      ];
      
      if (this.containsKeywords(title + ' ' + description, perfKeywords)) {
        reasons.push('Performance-critical feature');
        complexityFactors.push('Performance impact risk');
        score += 20;
      }
    }

    return score;
  }

  /**
   * Analyzes business impact factors
   */
  private static analyzeBusinessImpact(
    taskDetails: TaskDetails,
    taskAnalysis: TaskAnalysis,
    criteria: StrategicAssessmentCriteria,
    reasons: string[],
    complexityFactors: string[]
  ): number {
    let score = 0;
    const title = taskDetails.title.toLowerCase();
    const description = taskDetails.description.toLowerCase();

    // High Business Risk
    if (criteria.highBusinessRisk) {
      const riskKeywords = [
        'revenue', 'critical', 'business critical', 'customer impact',
        'user experience', 'conversion', 'retention', 'churn',
        'competitive', 'market share', 'business model'
      ];
      
      if (this.containsKeywords(title + ' ' + description, riskKeywords)) {
        reasons.push('High business risk if implemented incorrectly');
        complexityFactors.push('Business impact risk');
        score += 30;
      }
    }

    // Strategic Initiatives
    if (criteria.strategicInitiatives) {
      const strategyKeywords = [
        'strategic', 'initiative', 'roadmap', 'vision', 'transformation',
        'growth', 'expansion', 'key feature', 'milestone', 'objective',
        'kpi', 'okr', 'quarterly goal'
      ];
      
      if (this.containsKeywords(title + ' ' + description, strategyKeywords)) {
        reasons.push('Part of strategic company initiative');
        complexityFactors.push('Strategic alignment complexity');
        score += 25;
      }
    }

    // Customer Commitments
    if (criteria.customerCommitments) {
      const commitmentKeywords = [
        'customer request', 'client requirement', 'deadline', 'committed',
        'promised', 'contract', 'sla', 'agreement', 'enterprise',
        'customer success', 'escalation'
      ];
      
      if (this.containsKeywords(title + ' ' + description, commitmentKeywords)) {
        reasons.push('Has customer commitments or deadlines');
        complexityFactors.push('Customer commitment risk');
        score += 25;
      }
    }

    // Competitive Advantage
    if (criteria.competitiveAdvantage) {
      const competitiveKeywords = [
        'competitive', 'differentiator', 'unique', 'advantage', 'edge',
        'innovation', 'first to market', 'patent', 'proprietary',
        'breakthrough', 'game changer'
      ];
      
      if (this.containsKeywords(title + ' ' + description, competitiveKeywords)) {
        reasons.push('Provides competitive advantage');
        complexityFactors.push('Competitive positioning complexity');
        score += 20;
      }
    }

    return score;
  }

  /**
   * Analyzes resource and timing factors
   */
  private static analyzeResourceTiming(
    taskDetails: TaskDetails,
    taskAnalysis: TaskAnalysis,
    criteria: StrategicAssessmentCriteria,
    reasons: string[],
    complexityFactors: string[]
  ): number {
    let score = 0;
    const title = taskDetails.title.toLowerCase();
    const description = taskDetails.description.toLowerCase();

    // Long-term Maintenance
    if (criteria.longTermMaintenance) {
      const maintenanceKeywords = [
        'maintenance', 'support', 'ongoing', 'long-term', 'lifecycle',
        'monitoring', 'operations', 'devops', 'infrastructure',
        'platform', 'service', 'system administration'
      ];
      
      if (this.containsKeywords(title + ' ' + description, maintenanceKeywords)) {
        reasons.push('Significant long-term maintenance implications');
        complexityFactors.push('Maintenance burden');
        score += 20;
      }
    }

    // Irreversible Decisions
    if (criteria.irreversibleDecisions) {
      const irreversibleKeywords = [
        'permanent', 'irreversible', 'migration', 'deprecation',
        'breaking change', 'one-way', 'cannot undo', 'legacy',
        'end of life', 'sunset', 'major version'
      ];
      
      if (this.containsKeywords(title + ' ' + description, irreversibleKeywords)) {
        reasons.push('Contains decisions that are hard to reverse');
        complexityFactors.push('Decision reversibility risk');
        score += 30;
      }
    }

    return score;
  }

  /**
   * Analyzes content for custom keywords
   */
  private static analyzeContent(
    taskDetails: TaskDetails,
    criteria: StrategicAssessmentCriteria,
    reasons: string[],
    complexityFactors: string[]
  ): number {
    let score = 0;
    
    if (criteria.customKeywords && criteria.customKeywords.length > 0) {
      const content = `${taskDetails.title} ${taskDetails.description}`.toLowerCase();
      const matchedKeywords = criteria.customKeywords.filter(keyword => 
        content.includes(keyword.toLowerCase())
      );
      
      if (matchedKeywords.length > 0) {
        reasons.push(`Contains strategic keywords: ${matchedKeywords.join(', ')}`);
        score += matchedKeywords.length * 15;
      }
    }

    return score;
  }

  /**
   * Utility method to check if text contains any of the specified keywords
   */
  private static containsKeywords(text: string, keywords: string[]): boolean {
    const lowerText = text.toLowerCase();
    return keywords.some(keyword => lowerText.includes(keyword.toLowerCase()));
  }

  /**
   * Processes a strategic assessment request and determines if it needs user review
   */
  static processAssessment(
    taskDetails: TaskDetails,
    taskAnalysis: TaskAnalysis,
    preferences: StrategicAssessmentPreferences,
    assessmentResult?: any
  ): StrategicAssessmentProcessingResult {
    const classification = this.classifyAssessment(taskDetails, taskAnalysis, preferences.requireReviewCriteria);
    
    let autoProcessed = false;
    let recommendation: 'BUILD' | 'DOCUMENT' | 'DEFER' | undefined;
    let rationale: string | undefined;

    // Handle experimental features
    if (taskDetails.isExperimental && preferences.skipForExperimentalFeatures) {
      autoProcessed = true;
      recommendation = 'BUILD';
      rationale = 'Auto-approved for experimental feature (designed to be easily reversible)';
      classification.requiresReview = false;
    }

    // Auto-process based on assessment results if available
    if (assessmentResult && !classification.requiresReview) {
      const confidence = assessmentResult.confidence_level;
      const assessmentRecommendation = assessmentResult.recommendation;
      
      // Check confidence threshold
      const meetsConfidenceThreshold = (
        preferences.confidenceLevelThreshold === 'any' ||
        (preferences.confidenceLevelThreshold === 'medium' && ['medium', 'high'].includes(confidence)) ||
        (preferences.confidenceLevelThreshold === 'high' && confidence === 'high')
      );

      if (meetsConfidenceThreshold) {
        if (assessmentRecommendation === 'BUILD' && preferences.autoBuildOnPositiveAssessment) {
          autoProcessed = true;
          recommendation = 'BUILD';
          rationale = assessmentResult.strategic_rationale || 'Auto-approved based on positive strategic assessment';
        } else if (assessmentRecommendation === 'DOCUMENT') {
          // Could auto-process DOCUMENT recommendations too, but keeping conservative for now
          autoProcessed = false;
        } else if (assessmentRecommendation === 'DEFER') {
          // Never auto-process DEFER recommendations - always needs user review
          classification.requiresReview = true;
        }
      }
    }

    return {
      classification,
      assessment: assessmentResult,
      autoProcessed,
      recommendation,
      rationale,
      userOverride: false
    };
  }

  /**
   * Gets a summary of processing results
   */
  static getProcessingSummary(result: StrategicAssessmentProcessingResult): {
    autoProcessed: boolean;
    requiresReview: boolean;
    recommendation?: string;
    riskScore: number;
    complexityFactors: string[];
  } {
    return {
      autoProcessed: result.autoProcessed,
      requiresReview: result.classification.requiresReview,
      recommendation: result.recommendation,
      riskScore: result.classification.riskScore,
      complexityFactors: result.classification.complexityFactors
    };
  }
} 