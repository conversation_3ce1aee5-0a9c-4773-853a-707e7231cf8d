import { logger } from './logger.js';
import path from 'path';
import fs from 'fs-extra';

/**
 * Ensure a path is absolute
 * @param {string} inputPath - Input path
 * @returns {string} - Absolute path
 */
export function getAbsolutePath(inputPath) {
  if (path.isAbsolute(inputPath)) {
    return inputPath;
  }
  return path.join(process.cwd(), inputPath);
}

/**
 * Validate GitHub repository format
 * @param {string} repo - Repository string (owner/repo)
 * @returns {Object|null} - Parsed repository or null if invalid
 */
export function validateRepository(repo) {
  const parts = repo.split('/');
  if (parts.length !== 2 || !parts[0] || !parts[1]) {
    return null;
  }
  return { owner: parts[0], repo: parts[1] };
}

/**
 * Check if a directory is empty
 * @param {string} dirPath - Directory path
 * @returns {boolean} - True if empty
 */
export async function isDirectoryEmpty(dirPath) {
  try {
    const files = await fs.readdir(dirPath);
    return files.length === 0;
  } catch (error) {
    if (error.code === 'ENOENT') {
      return true; // Directory doesn't exist, consider it empty
    }
    throw error;
  }
}

/**
 * Clean a string for use in filenames
 * @param {string} str - Input string
 * @returns {string} - Cleaned string
 */
export function cleanForFilename(str) {
  return str
    .replace(/[^\w\s-]/g, '')
    .trim()
    .replace(/\s+/g, '-')
    .toLowerCase();
}

export {
  logger
};
