import type { 
  DecisionPoint, 
  DecisionOption, 
  DecisionReviewCriteria, 
  DecisionReviewPreferences,
  DecisionClassification,
  DecisionProcessingResult
} from '../types/design-doc-wizard';

/**
 * Intelligently classifies decisions using LLM-generated analysis data
 * instead of generic heuristics. This provides more accurate, context-specific
 * classification based on the actual decision analysis.
 */
export class DecisionClassifier {
  
  /**
   * Classifies a single decision point using LLM analysis data
   */
  static classifyDecision(
    decision: DecisionPoint,
    criteria: DecisionReviewCriteria
  ): DecisionClassification {
    const reasons: string[] = [];
    const riskFactors: string[] = [];
    let impactScore = 0;

    // Use LLM-generated analysis data if available, otherwise fall back to basic analysis
    if (decision.options && decision.recommended_option_id) {
      impactScore += this.analyzeLLMGeneratedData(decision, criteria, reasons, riskFactors);
    } else {
      // Fallback to basic analysis for decisions without LLM analysis
      impactScore += this.analyzeBasicDecisionData(decision, criteria, reasons, riskFactors);
    }

    // Normalize impact score to 0-100
    impactScore = Math.min(impactScore, 100);

    // Determine review requirement and mode
    const requiresReview = impactScore >= 30 || reasons.length > 0;
    const reviewMode = impactScore >= 70 ? 'detailed' : 
                      impactScore >= 30 ? 'summary' : 'auto';

    return {
      requiresReview,
      reviewMode,
      reasons,
      impactScore,
      riskFactors
    };
  }

  /**
   * Analyzes decisions using rich LLM-generated data
   */
  private static analyzeLLMGeneratedData(
    decision: DecisionPoint,
    criteria: DecisionReviewCriteria,
    reasons: string[],
    riskFactors: string[]
  ): number {
    let score = 0;
    
    const recommendedOption = decision.options?.find(opt => opt.id === decision.recommended_option_id);
    if (!recommendedOption) return score;

    // 1. Risk Level Analysis (from LLM assessment)
    if (criteria.highRiskDecisions && recommendedOption.riskLevel === 'high') {
      reasons.push('AI analysis identified high implementation risk');
      riskFactors.push('High implementation risk');
      score += 35;
    } else if (recommendedOption.riskLevel === 'medium') {
      score += 15;
    }

    // 2. Complexity Analysis (from LLM assessment)
    if (criteria.highComplexityDecisions && recommendedOption.complexity === 'high') {
      reasons.push('AI analysis identified high implementation complexity');
      riskFactors.push('Implementation complexity');
      score += 30;
    } else if (recommendedOption.complexity === 'medium') {
      score += 10;
    }

    // 3. Maintenance Burden (from LLM assessment)
    if (criteria.highMaintenanceBurden && recommendedOption.maintenanceBurden === 'high') {
      reasons.push('AI analysis identified high long-term maintenance burden');
      riskFactors.push('Maintenance complexity');
      score += 25;
    }

    // 4. Architectural Alignment Analysis
    if (criteria.deviatesFromStandards && recommendedOption.architecturalAlignment === 'low') {
      reasons.push('Low alignment with existing architectural patterns');
      riskFactors.push('Architectural deviation');
      score += 30;
    } else if (recommendedOption.architecturalAlignment === 'medium') {
      score += 10;
    }

    // 5. Change Scope Analysis (from LLM assessment)
    if (recommendedOption.changeScope === 'significant') {
      if (criteria.architecturalChanges) {
        reasons.push('AI analysis indicates significant system changes required');
        riskFactors.push('Significant change scope');
        score += 25;
      }
    }

    // 6. Debugging Complexity Analysis
    if (recommendedOption.debuggingComplexity && 
        (recommendedOption.debuggingComplexity.toLowerCase().includes('complex') ||
         recommendedOption.debuggingComplexity.toLowerCase().includes('difficult'))) {
      reasons.push('AI analysis indicates complex debugging requirements');
      riskFactors.push('Debugging complexity');
      score += 20;
    }

    // 7. Analyze specific cons from LLM analysis
    if (recommendedOption.cons && recommendedOption.cons.length > 0) {
      this.analyzeOptionCons(recommendedOption.cons, criteria, reasons, riskFactors, score);
    }

    // 8. Analyze recommendation rationale for specific concerns
    if (decision.recommendation_rationale) {
      score += this.analyzeRecommendationRationale(decision.recommendation_rationale, criteria, reasons, riskFactors);
    }

    return score;
  }

  /**
   * Analyzes the cons of the recommended option for specific review triggers
   */
  private static analyzeOptionCons(
    cons: string[],
    criteria: DecisionReviewCriteria,
    reasons: string[],
    riskFactors: string[],
    score: number
  ): number {
    let additionalScore = 0;
    
    cons.forEach(con => {
      const lowerCon = con.toLowerCase();
      
      // Check for new technology mentions
      if (criteria.newTechnologies && 
          (lowerCon.includes('new dependency') || lowerCon.includes('new library') || 
           lowerCon.includes('new framework') || lowerCon.includes('unfamiliar'))) {
        reasons.push('Introduces new technology dependency');
        riskFactors.push('Technology adoption risk');
        additionalScore += 20;
      }
      
      // Check for external dependency mentions
      if (criteria.externalDependencies && 
          (lowerCon.includes('external') || lowerCon.includes('third-party') || 
           lowerCon.includes('api dependency'))) {
        reasons.push('Adds external service dependency');
        riskFactors.push('External dependency risk');
        additionalScore += 15;
      }
      
      // Check for performance concerns
      if (criteria.performanceImpact && 
          (lowerCon.includes('performance') || lowerCon.includes('latency') || 
           lowerCon.includes('memory') || lowerCon.includes('slower'))) {
        reasons.push('Potential performance impact identified');
        riskFactors.push('Performance risk');
        additionalScore += 15;
      }
      
      // Check for security concerns
      if (criteria.securityChanges && 
          (lowerCon.includes('security') || lowerCon.includes('auth') || 
           lowerCon.includes('permission') || lowerCon.includes('vulnerability'))) {
        reasons.push('Security implications identified');
        riskFactors.push('Security risk');
        additionalScore += 25;
      }
      
      // Check for irreversible changes
      if (criteria.irreversibleChanges && 
          (lowerCon.includes('irreversible') || lowerCon.includes('breaking') || 
           lowerCon.includes('migration') || lowerCon.includes('cannot undo'))) {
        reasons.push('Contains irreversible changes');
        riskFactors.push('Irreversibility risk');
        additionalScore += 30;
      }
    });
    
    return additionalScore;
  }

  /**
   * Analyzes the recommendation rationale for specific concerns
   */
  private static analyzeRecommendationRationale(
    rationale: string | string[],
    criteria: DecisionReviewCriteria,
    reasons: string[],
    riskFactors: string[]
  ): number {
    let score = 0;
    const rationaleText = Array.isArray(rationale) ? rationale.join(' ') : rationale;
    const lowerRationale = rationaleText.toLowerCase();
    
    // Check for mentions of multiple services/systems
    if (criteria.affectsMultipleServices && 
        (lowerRationale.includes('multiple services') || lowerRationale.includes('cross-service') || 
         lowerRationale.includes('system-wide') || lowerRationale.includes('affects multiple'))) {
      reasons.push('Affects multiple services or systems');
      riskFactors.push('Cross-system coordination');
      score += 25;
    }
    
    // Check for schema/data model mentions
    if (criteria.schemaChanges && 
        (lowerRationale.includes('schema') || lowerRationale.includes('database') || 
         lowerRationale.includes('data model') || lowerRationale.includes('migration'))) {
      reasons.push('Involves database or schema changes');
      riskFactors.push('Data migration risk');
      score += 30;
    }
    
    return score;
  }

  /**
   * Fallback analysis for decisions without LLM analysis data
   */
  private static analyzeBasicDecisionData(
    decision: DecisionPoint,
    criteria: DecisionReviewCriteria,
    reasons: string[],
    riskFactors: string[]
  ): number {
    let score = 0;
    const title = decision.title.toLowerCase();
    const description = decision.description.toLowerCase();
    const category = decision.category;

    // High priority decisions generally need more attention
    if (decision.priority === 'high') {
      reasons.push('Marked as high priority decision');
      score += 20;
    }

    // Category-based analysis
    if (category === 'security' && criteria.securityChanges) {
      reasons.push('Security-related decision requires review');
      riskFactors.push('Security implications');
      score += 35;
    }
    
    if (category === 'data' && criteria.schemaChanges) {
      reasons.push('Data-related decision may affect schema');
      riskFactors.push('Data model impact');
      score += 30;
    }
    
    if (category === 'architecture' && criteria.architecturalChanges) {
      reasons.push('Architectural decision requires review');
      riskFactors.push('System architecture impact');
      score += 25;
    }

    // Custom keyword analysis
    if (criteria.customKeywords && criteria.customKeywords.length > 0) {
      const content = `${title} ${description}`.toLowerCase();
      const matchedKeywords = criteria.customKeywords.filter(keyword => 
        content.includes(keyword.toLowerCase())
      );
      
      if (matchedKeywords.length > 0) {
        reasons.push(`Contains custom keywords: ${matchedKeywords.join(', ')}`);
        score += matchedKeywords.length * 10;
      }
    }

    return score;
  }

  /**
   * Processes a list of decisions and auto-selects options for low-impact ones
   */
  static processDecisions(
    decisions: DecisionPoint[],
    preferences: DecisionReviewPreferences
  ): DecisionProcessingResult[] {
    return decisions.map(decision => {
      const classification = this.classifyDecision(decision, preferences.requireReviewCriteria);
      
      let selectedOption: string | undefined;
      let rationale: string | undefined;
      let alternatives: Array<{ option: DecisionOption; reason: string }> = [];
      let autoProcessed = false;

      // Auto-process decisions that don't require review
      if (!classification.requiresReview && preferences.autoSelectRecommendedOptions) {
        const recommendedOption = decision.options?.find(opt => 
          opt.id === decision.recommended_option_id
        );
        
        if (recommendedOption) {
          selectedOption = recommendedOption.id;
          rationale = Array.isArray(decision.recommendation_rationale) 
            ? decision.recommendation_rationale.join(' ') 
            : decision.recommendation_rationale || 'Auto-selected based on AI analysis';
          autoProcessed = true;

          // Find top 2 alternatives
          const otherOptions = decision.options?.filter(opt => 
            opt.id !== recommendedOption.id
          ).slice(0, 2) || [];
          
          alternatives = otherOptions.map(opt => ({
            option: opt,
            reason: `Alternative approach with ${opt.riskLevel} risk and ${opt.complexity} complexity`
          }));
        }
      }

      return {
        decisionId: decision.id,
        classification,
        selectedOption,
        rationale,
        alternatives,
        autoProcessed,
        userOverride: false
      };
    });
  }

  /**
   * Gets a summary of processing results
   */
  static getProcessingSummary(results: DecisionProcessingResult[]): {
    total: number;
    autoProcessed: number;
    requireReview: number;
    highImpact: number;
    riskFactors: string[];
  } {
    const total = results.length;
    const autoProcessed = results.filter(r => r.autoProcessed).length;
    const requireReview = results.filter(r => r.classification.requiresReview).length;
    const highImpact = results.filter(r => r.classification.impactScore >= 70).length;
    
    const allRiskFactors = results.flatMap(r => r.classification.riskFactors);
    const uniqueRiskFactors = Array.from(new Set(allRiskFactors));

    return {
      total,
      autoProcessed,
      requireReview,
      highImpact,
      riskFactors: uniqueRiskFactors
    };
  }
} 