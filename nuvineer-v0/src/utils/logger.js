import chalk from 'chalk';
import util from 'util';

const isDebugMode = process.env.DEBUG === 'true';

/**
 * Format an object for logging
 * @param {any} obj - Object to format
 * @returns {string} - Formatted string
 */
function formatObject(obj) {
  if (obj === undefined) return 'undefined';
  if (obj === null) return 'null';
  
  if (typeof obj === 'object') {
    try {
      // Try to stringify with indentation for readability
      return util.inspect(obj, { depth: 3, colors: true });
    } catch (error) {
      return String(obj);
    }
  }
  
  return String(obj);
}

/**
 * Enhanced logger utility
 */
export const logger = {
  /**
   * Log info message
   * @param {string} message - Message to log
   * @param {any} data - Optional data to log
   */
  info: (message, data) => {
    console.log(chalk.blue('ℹ️ '), message);
    if (data !== undefined) {
      console.log(formatObject(data));
    }
  },
  
  /**
   * Log success message
   * @param {string} message - Message to log
   * @param {any} data - Optional data to log
   */
  success: (message, data) => {
    console.log(chalk.green('✓ '), message);
    if (data !== undefined) {
      console.log(formatObject(data));
    }
  },
  
  /**
   * Log warning message
   * @param {string} message - Message to log
   * @param {any} data - Optional data to log
   */
  warn: (message, data) => {
    console.log(chalk.yellow('⚠️ '), message);
    if (data !== undefined) {
      console.log(formatObject(data));
    }
  },
  
  /**
   * Log error message
   * @param {string} message - Message to log
   * @param {any} data - Optional data to log
   */
  error: (message, data) => {
    console.error(chalk.red('✗ '), message);
    if (data !== undefined) {
      // For errors, we want to see the stack trace if available
      if (data instanceof Error) {
        console.error(chalk.red(data.stack || data.message));
      } else {
        console.error(formatObject(data));
      }
    }
  },
  
  /**
   * Log debug message (only in debug mode)
   * @param {string} message - Message to log
   * @param {any} data - Optional data to log
   */
  debug: (message, data) => {
    if (isDebugMode) {
      console.log(chalk.gray('🔍 '), chalk.gray(message));
      if (data !== undefined) {
        console.log(chalk.gray(formatObject(data)));
      }
    }
  }
};