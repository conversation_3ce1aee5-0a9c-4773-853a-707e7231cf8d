import type { RelatedDecision, DecisionPoint, GraphContext } from '../types/design-doc-wizard';

// Helper function to extract related decisions from graph context
export const getRelatedDecisions = (decisionPoint: DecisionPoint): RelatedDecision[] => {
  // If we have the old format, use it
  if (decisionPoint.relatedDecisions && decisionPoint.relatedDecisions.length > 0) {
    return decisionPoint.relatedDecisions;
  }
  
  // If we have the new graph context, convert it to the old format
  if (decisionPoint.graphContext?.clusters) {
    const allDecisions: RelatedDecision[] = [];
    const clusters = decisionPoint.graphContext.clusters;
    
    // Convert all clustered decisions to RelatedDecision format
    [...clusters.coreDecisions, ...clusters.supportingDecisions, ...clusters.constraintDecisions, 
     ...clusters.impactedDecisions, ...clusters.patternDecisions].forEach(enrichedDecision => {
      allDecisions.push({
        id: enrichedDecision.id,
        title: enrichedDecision.title,
        summary: enrichedDecision.summary,
        relevanceScore: enrichedDecision.relevanceScore,
        implications: enrichedDecision.implications ? [enrichedDecision.implications] : [],
        guidance: enrichedDecision.dev_prompt
      });
    });
    
    // Sort by relevance score and return top decisions
    return allDecisions
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 10); // Limit to top 10 for UI performance
  }
  
  return [];
};

// Helper functions for document formatting
export const formatDesignDocForDisplay = (doc: any): string => {
  if (!doc) return '';
  
  let markdown = `# ${doc.title || 'Design Document'}\n\n`;
  
  if (doc.goals && doc.goals.length > 0) {
    markdown += `## Goals\n\n`;
    doc.goals.forEach((goal: string) => {
      markdown += `- ${goal}\n`;
    });
    markdown += '\n';
  }
  
  if (doc.non_goals && doc.non_goals.length > 0) {
    markdown += `## Non-Goals\n\n`;
    doc.non_goals.forEach((nonGoal: string) => {
      markdown += `- ${nonGoal}\n`;
    });
    markdown += '\n';
  }

  if (doc.high_level_design) {
    markdown += `## High-Level Design\n\n`;
    
    if (doc.high_level_design.overall_system_overview) {
      markdown += `### System Overview\n\n${doc.high_level_design.overall_system_overview}\n\n`;
    }

    if (doc.high_level_design.core_architectural_choices && doc.high_level_design.core_architectural_choices.length > 0) {
      markdown += `### Core Architectural Decisions\n\n`;
      doc.high_level_design.core_architectural_choices.forEach((choice: any) => {
        markdown += `#### ${choice.title}\n\n`;
        markdown += `**Approach:** ${choice.recommended_approach_description}\n\n`;
        if (choice.justification_and_context) {
          markdown += `**Justification:** ${choice.justification_and_context}\n\n`;
        }
        if (choice.new_constraints_introduced && choice.new_constraints_introduced !== 'N/A') {
          markdown += `**Constraints:** ${choice.new_constraints_introduced}\n\n`;
        }
      });
    }

    if (doc.high_level_design.process_flow_diagram_mermaid && doc.high_level_design.process_flow_diagram_mermaid !== 'N/A') {
      markdown += `### Process Flow\n\n\`\`\`mermaid\n${doc.high_level_design.process_flow_diagram_mermaid}\n\`\`\`\n\n`;
    }

    if (doc.high_level_design.key_components_and_responsibilities && doc.high_level_design.key_components_and_responsibilities.length > 0) {
      markdown += `### Key Components\n\n`;
      doc.high_level_design.key_components_and_responsibilities.forEach((component: any) => {
        markdown += `- **${component.name}:** ${component.responsibility}\n`;
      });
      markdown += `\n`;
    }

    if (doc.high_level_design.data_model_changes && doc.high_level_design.data_model_changes !== 'N/A') {
      markdown += `### Data Model Changes\n\n${doc.high_level_design.data_model_changes}\n\n`;
    }

    if (doc.high_level_design.security_considerations && doc.high_level_design.security_considerations !== 'N/A') {
      markdown += `### Security Considerations\n\n${doc.high_level_design.security_considerations}\n\n`;
    }

    if (doc.high_level_design.error_handling_and_recovery) {
      markdown += `### Error Handling & Recovery\n\n`;
      if (doc.high_level_design.error_handling_and_recovery.critical_error_scenarios) {
        markdown += `**Critical Error Scenarios:**\n`;
        doc.high_level_design.error_handling_and_recovery.critical_error_scenarios.forEach((scenario: string) => {
          markdown += `- ${scenario}\n`;
        });
        markdown += `\n`;
      }
      if (doc.high_level_design.error_handling_and_recovery.overall_strategy) {
        markdown += `**Overall Strategy:** ${doc.high_level_design.error_handling_and_recovery.overall_strategy}\n\n`;
      }
    }
  }

  if (doc.alternatives_analysis) {
    markdown += `## Alternatives Analysis\n\n`;
    
    if (doc.alternatives_analysis.overall_solution_alternatives && doc.alternatives_analysis.overall_solution_alternatives.length > 0) {
      markdown += `### Alternative Approaches Considered\n\n`;
      doc.alternatives_analysis.overall_solution_alternatives.forEach((alt: any, index: number) => {
        markdown += `#### Alternative ${index + 1}: ${alt.approach_name}\n\n`;
        markdown += `${alt.description}\n\n`;
        if (alt.pros && alt.pros.length > 0) {
          markdown += `**Pros:**\n`;
          alt.pros.forEach((pro: string) => markdown += `- ${pro}\n`);
          markdown += `\n`;
        }
        if (alt.cons && alt.cons.length > 0) {
          markdown += `**Cons:**\n`;
          alt.cons.forEach((con: string) => markdown += `- ${con}\n`);
          markdown += `\n`;
        }
        if (alt.alignment_with_context) {
          markdown += `**Alignment with Context:** ${alt.alignment_with_context}\n\n`;
        }
      });
    }

    if (doc.alternatives_analysis.key_technical_decision_alternatives && doc.alternatives_analysis.key_technical_decision_alternatives.length > 0) {
      markdown += `### Key Technical Decision Alternatives\n\n`;
      doc.alternatives_analysis.key_technical_decision_alternatives.forEach((alt: any) => {
        markdown += `#### ${alt.decision_point_title}\n\n`;
        markdown += `**Alternative Considered:** ${alt.alternative_considered}\n\n`;
        markdown += `**Reason Not Chosen:** ${alt.reason_not_chosen}\n\n`;
      });
    }

    if (doc.alternatives_analysis.overall_recommendation_and_justification) {
      markdown += `### Recommendation\n\n${doc.alternatives_analysis.overall_recommendation_and_justification}\n\n`;
    }
  }

  if (doc.milestones_sketch && doc.milestones_sketch !== 'N/A') {
    markdown += `## Implementation Milestones\n\n${doc.milestones_sketch}\n\n`;
  }

  if (doc.success_metrics && doc.success_metrics.length > 0) {
    markdown += `## Success Metrics\n\n`;
    doc.success_metrics.forEach((metric: string) => {
      markdown += `- ${metric}\n`;
    });
    markdown += '\n';
  }

  if (doc.referenced_decisions && doc.referenced_decisions.length > 0) {
    markdown += `## Referenced Decisions\n\n`;
    doc.referenced_decisions.forEach((decision: any) => {
      markdown += `### ${decision.id}: ${decision.title || 'Untitled Decision'}\n\n`;
      if (decision.summary_of_relevance_in_this_design || decision.summaryOfRelevance) {
        markdown += `**Relevance:** ${decision.summary_of_relevance_in_this_design || decision.summaryOfRelevance}\n\n`;
      }
    });
  }
  
  return markdown;
};

export const formatDesignDocForHTML = (doc: any): string => {
  const markdown = formatDesignDocForDisplay(doc);
  // Simple markdown to HTML conversion for basic formatting
  return markdown
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/^(.*)$/gm, '<p>$1</p>')
    .replace(/<p><h/g, '<h')
    .replace(/<\/h([1-6])><\/p>/g, '</h$1>')
    .replace(/<p><li>/g, '<ul><li>')
    .replace(/<\/li><\/p>/g, '</li></ul>')
    .replace(/<\/ul><ul>/g, '');
};

export const formatDesignDocAsMarkdown = (doc: any): string => {
  if (!doc) return '';
  
  let markdown = `# ${doc.title || 'Design Document'}\n\n`;
  
  if (doc.goals && doc.goals.length > 0) {
    markdown += `## Goals\n\n`;
    doc.goals.forEach((goal: string) => {
      markdown += `- ${goal}\n`;
    });
    markdown += '\n';
  }
  
  if (doc.non_goals && doc.non_goals.length > 0) {
    markdown += `## Non-Goals\n\n`;
    doc.non_goals.forEach((nonGoal: string) => {
      markdown += `- ${nonGoal}\n`;
    });
    markdown += '\n';
  }

  if (doc.high_level_design) {
    markdown += `## High-Level Design\n\n`;
    
    if (doc.high_level_design.overall_system_overview) {
      markdown += `### System Overview\n\n${doc.high_level_design.overall_system_overview}\n\n`;
    }

    if (doc.high_level_design.core_architectural_choices && doc.high_level_design.core_architectural_choices.length > 0) {
      markdown += `### Core Architectural Decisions\n\n`;
      doc.high_level_design.core_architectural_choices.forEach((choice: any, index: number) => {
        markdown += `#### ${choice.title}\n\n`;
        markdown += `**Approach:** ${choice.recommended_approach_description}\n\n`;
        if (choice.justification_and_context) {
          markdown += `**Justification:** ${choice.justification_and_context}\n\n`;
        }
      });
    }

    if (doc.high_level_design.process_flow_diagram_mermaid && doc.high_level_design.process_flow_diagram_mermaid !== 'N/A') {
      markdown += `### Process Flow\n\n`;
      markdown += `\`\`\`mermaid\n${doc.high_level_design.process_flow_diagram_mermaid}\n\`\`\`\n\n`;
    }

    if (doc.high_level_design.key_components_and_responsibilities && doc.high_level_design.key_components_and_responsibilities.length > 0) {
      markdown += `### Key Components\n\n`;
      doc.high_level_design.key_components_and_responsibilities.forEach((component: any) => {
        markdown += `- **${component.name}:** ${component.responsibility}\n`;
      });
      markdown += '\n';
    }

    if (doc.high_level_design.data_model_changes && doc.high_level_design.data_model_changes !== 'N/A') {
      markdown += `### Data Model Changes\n\n${doc.high_level_design.data_model_changes}\n\n`;
    }

    if (doc.high_level_design.security_considerations) {
      markdown += `### Security Considerations\n\n${doc.high_level_design.security_considerations}\n\n`;
    }

    if (doc.high_level_design.error_handling_and_recovery) {
      markdown += `### Error Handling & Recovery\n\n`;
      if (doc.high_level_design.error_handling_and_recovery.critical_error_scenarios && doc.high_level_design.error_handling_and_recovery.critical_error_scenarios.length > 0) {
        markdown += `**Critical Error Scenarios:**\n\n`;
        doc.high_level_design.error_handling_and_recovery.critical_error_scenarios.forEach((scenario: string) => {
          markdown += `- ${scenario}\n`;
        });
        markdown += '\n';
      }
      if (doc.high_level_design.error_handling_and_recovery.overall_strategy) {
        markdown += `**Overall Strategy:**\n\n${doc.high_level_design.error_handling_and_recovery.overall_strategy}\n\n`;
      }
    }
  }

  if (doc.alternatives_analysis) {
    markdown += `## Alternatives Analysis\n\n`;
    
    if (doc.alternatives_analysis.overall_solution_alternatives && doc.alternatives_analysis.overall_solution_alternatives.length > 0) {
      markdown += `### Alternative Approaches Considered\n\n`;
      doc.alternatives_analysis.overall_solution_alternatives.forEach((alt: any, index: number) => {
        markdown += `#### Alternative ${index + 1}: ${alt.approach_name}\n\n`;
        markdown += `${alt.description}\n\n`;
        if (alt.pros && alt.pros.length > 0) {
          markdown += `**Pros:**\n`;
          alt.pros.forEach((pro: string) => markdown += `- ${pro}\n`);
          markdown += '\n';
        }
        if (alt.cons && alt.cons.length > 0) {
          markdown += `**Cons:**\n`;
          alt.cons.forEach((con: string) => markdown += `- ${con}\n`);
          markdown += '\n';
        }
        if (alt.alignment_with_context) {
          markdown += `**Context Alignment:** ${alt.alignment_with_context}\n\n`;
        }
      });
    }

    if (doc.alternatives_analysis.key_technical_decision_alternatives && doc.alternatives_analysis.key_technical_decision_alternatives.length > 0) {
      markdown += `### Key Technical Decision Alternatives\n\n`;
      doc.alternatives_analysis.key_technical_decision_alternatives.forEach((alt: any, index: number) => {
        markdown += `#### ${alt.decision_point_title}\n\n`;
        markdown += `**Alternative Considered:** ${alt.alternative_considered}\n\n`;
        markdown += `**Reason Not Chosen:** ${alt.reason_not_chosen}\n\n`;
      });
    }

    if (doc.alternatives_analysis.overall_recommendation_and_justification) {
      markdown += `### Recommendation\n\n${doc.alternatives_analysis.overall_recommendation_and_justification}\n\n`;
    }
  }

  // Implementation Strategy - New Structured Format
  if (doc.implementation_strategy) {
    markdown += `## Implementation Strategy\n\n`;
    
    if (doc.implementation_strategy.strategic_recommendations) {
      markdown += `### Strategic Recommendations\n\n`;
      if (doc.implementation_strategy.strategic_recommendations.immediate_focus && doc.implementation_strategy.strategic_recommendations.immediate_focus.length > 0) {
        markdown += `**Immediate Focus:**\n\n`;
        doc.implementation_strategy.strategic_recommendations.immediate_focus.forEach((item: string) => {
          markdown += `- ${item}\n`;
        });
        markdown += '\n';
      }
      if (doc.implementation_strategy.strategic_recommendations.defer_until_later && doc.implementation_strategy.strategic_recommendations.defer_until_later.length > 0) {
        markdown += `**Defer Until Later:**\n\n`;
        doc.implementation_strategy.strategic_recommendations.defer_until_later.forEach((item: string) => {
          markdown += `- ${item}\n`;
        });
        markdown += '\n';
      }
      if (doc.implementation_strategy.strategic_recommendations.complexity_concerns && doc.implementation_strategy.strategic_recommendations.complexity_concerns.length > 0) {
        markdown += `**Complexity Concerns:**\n\n`;
        doc.implementation_strategy.strategic_recommendations.complexity_concerns.forEach((item: string) => {
          markdown += `- ${item}\n`;
        });
        markdown += '\n';
      }
      if (doc.implementation_strategy.strategic_recommendations.constitution_alignment) {
        markdown += `**Strategic Alignment:**\n\n${doc.implementation_strategy.strategic_recommendations.constitution_alignment}\n\n`;
      }
    }

    if (doc.implementation_strategy.core_milestones && doc.implementation_strategy.core_milestones.length > 0) {
      markdown += `### Core Milestones\n\n`;
      doc.implementation_strategy.core_milestones.forEach((milestone: any, index: number) => {
        markdown += `#### ${milestone.milestone_id || `M${index + 1}`}: ${milestone.title}\n\n`;
        markdown += `**Priority:** ${milestone.priority} | **Complexity:** ${milestone.estimated_complexity}\n\n`;
        markdown += `${milestone.description}\n\n`;
        
        if (milestone.user_value_delivered) {
          markdown += `**User Value:** ${milestone.user_value_delivered}\n\n`;
        }
        
        if (milestone.success_criteria && milestone.success_criteria.length > 0) {
          markdown += `**Success Criteria:**\n`;
          milestone.success_criteria.forEach((criteria: string) => {
            markdown += `- ${criteria}\n`;
          });
          markdown += '\n';
        }
        
        if (milestone.dependencies && milestone.dependencies.length > 0) {
          markdown += `**Dependencies:**\n`;
          milestone.dependencies.forEach((dep: string) => {
            markdown += `- ${dep}\n`;
          });
          markdown += '\n';
        }
        
        if (milestone.risks && milestone.risks.length > 0) {
          markdown += `**Risks:**\n`;
          milestone.risks.forEach((risk: string) => {
            markdown += `- ${risk}\n`;
          });
          markdown += '\n';
        }
      });
    }

    if (doc.implementation_strategy.deferrable_milestones && doc.implementation_strategy.deferrable_milestones.length > 0) {
      markdown += `### Deferrable Milestones\n\n`;
      doc.implementation_strategy.deferrable_milestones.forEach((milestone: any, index: number) => {
        markdown += `#### ${milestone.milestone_id || `D${index + 1}`}: ${milestone.title}\n\n`;
        markdown += `${milestone.description}\n\n`;
        
        if (milestone.deferral_rationale) {
          markdown += `**Deferral Rationale:** ${milestone.deferral_rationale}\n\n`;
        }
        
        if (milestone.dependency_complexity) {
          markdown += `**Dependency Complexity:** ${milestone.dependency_complexity}\n\n`;
        }
        
        if (milestone.future_implementation_notes) {
          markdown += `**Future Implementation Notes:** ${milestone.future_implementation_notes}\n\n`;
        }
      });
    }
  }

  // Legacy Implementation Milestones - Backward Compatibility
  if (doc.milestones_sketch && doc.milestones_sketch !== 'N/A' && !doc.implementation_strategy) {
    markdown += `## Implementation Milestones\n\n${doc.milestones_sketch}\n\n`;
  }

  if (doc.success_metrics && doc.success_metrics.length > 0) {
    markdown += `## Success Metrics\n\n`;
    doc.success_metrics.forEach((metric: string) => {
      markdown += `- ${metric}\n`;
    });
    markdown += '\n';
  }

  if (doc.referenced_decisions && doc.referenced_decisions.length > 0) {
    markdown += `## Referenced Decisions\n\n`;
    doc.referenced_decisions.forEach((decision: any) => {
      markdown += `- **${decision.id}:** ${decision.summary_of_relevance_in_this_design}\n`;
    });
    markdown += '\n';
  }

  return markdown.trim();
};

export const formatImplementationPlanAsMarkdown = (plan: any): string => {
  if (!plan) return '';
  
  let markdown = `# ${plan.implementation_plan_title || 'Implementation Plan'}\n\n`;
  
  // Main Feature Branch
  if (plan.main_feature_branch_name) {
    markdown += `## Project Setup\n\n`;
    markdown += `**Main Feature Branch:** \`${plan.main_feature_branch_name}\`\n\n`;
    markdown += `This branch will accumulate all milestone work and serve as the integration point for the complete feature.\n\n`;
  }

  // AI Agent Protocol Compliance
  if (plan.ai_agent_protocol_compliance) {
    markdown += `## Implementation Protocol\n\n`;
    const compliance = plan.ai_agent_protocol_compliance;
    markdown += `**Protocol Source:** ${compliance.protocol_source === 'custom' ? 'Custom AI Agent Protocol' : 'Default Protocol'}\n\n`;
    markdown += `**Quality Assurance Features:**\n`;
    if (compliance.milestone_driven_approach) markdown += `- ✅ Milestone-driven development approach\n`;
    if (compliance.branch_naming_enforced) markdown += `- ✅ Standardized branch naming conventions\n`;
    if (compliance.independent_milestone_integration) markdown += `- ✅ Independent milestone integration capability\n`;
    if (compliance.quality_control_integrated) markdown += `- ✅ Built-in quality control at each milestone\n`;
    if (compliance.scope_validation_required) markdown += `- ✅ Mandatory scope validation before integration\n`;
    if (compliance.pre_integration_validation_required) markdown += `- ✅ Pre-integration validation and risk assessment\n`;
    markdown += `\n`;
  }

  // Enhanced Milestones
  if (plan.enhanced_milestones && plan.enhanced_milestones.length > 0) {
    markdown += `## Implementation Milestones\n\n`;
    markdown += `The implementation is broken down into ${plan.enhanced_milestones.length} sequential milestones, each independently testable and deployable.\n\n`;
    
    plan.enhanced_milestones.forEach((milestone: any, index: number) => {
      markdown += `### Milestone ${index + 1}: ${milestone.title || milestone.milestone_id}\n\n`;
      
      // Priority and Branch
      if (milestone.priority) {
        markdown += `**Priority:** ${milestone.priority}\n`;
      }
      if (milestone.git_must_use_milestone_branch_name) {
        markdown += `**Milestone Branch:** \`${milestone.git_must_use_milestone_branch_name}\`\n\n`;
      }
      
      // Description
      if (milestone.description) {
        markdown += `${milestone.description}\n\n`;
      }
      
      // Key Tasks and Deliverables
      if (milestone.key_tasks_and_deliverables && milestone.key_tasks_and_deliverables.length > 0) {
        markdown += `**Key Tasks and Deliverables:**\n`;
        milestone.key_tasks_and_deliverables.forEach((task: any) => {
          const taskText = typeof task === 'string' ? task : task.task || task.description || 'Task not specified';
          markdown += `- ${taskText}\n`;
        });
        markdown += `\n`;
      }
      
      // Scope Validation
      if (milestone.scope_validation) {
        markdown += `**Scope Validation:**\n`;
        if (milestone.scope_validation.planned_deliverables && milestone.scope_validation.planned_deliverables.length > 0) {
          markdown += `- **Planned Deliverables:**\n`;
          milestone.scope_validation.planned_deliverables.forEach((deliverable: string) => {
            markdown += `  - ${deliverable}\n`;
          });
        }
        markdown += `\n`;
      }
      
      // Verification Criteria
      if (milestone.verification_criteria && milestone.verification_criteria.length > 0) {
        markdown += `**Verification Criteria:**\n`;
        milestone.verification_criteria.forEach((criteria: string) => {
          markdown += `- ${criteria}\n`;
        });
        markdown += `\n`;
      }
      
      // Developer Guidance and Best Practices
      if (milestone.applicable_developer_guidance_and_best_practices && milestone.applicable_developer_guidance_and_best_practices.length > 0) {
        markdown += `**Applicable Developer Guidance:**\n`;
        milestone.applicable_developer_guidance_and_best_practices.forEach((guidance: any) => {
          markdown += `- **${guidance.retrieved_decision_title}**\n`;
          if (guidance.guidance_summary) {
            markdown += `  - ${guidance.guidance_summary}\n`;
          }
          if (guidance.related_files && guidance.related_files.length > 0) {
            markdown += `  - Related files: ${guidance.related_files.join(', ')}\n`;
          }
        });
        markdown += `\n`;
      }
      
      markdown += `---\n\n`;
    });
  }

  // Data Model Summary
  if (plan.data_model_summary && plan.data_model_summary !== 'N/A') {
    markdown += `## Data Model Changes\n\n`;
    markdown += `${plan.data_model_summary}\n\n`;
  }

  // Referenced Decisions
  if (plan.referenced_decisions && plan.referenced_decisions.length > 0) {
    markdown += `## Referenced Architectural Decisions\n\n`;
    plan.referenced_decisions.forEach((decision: string) => {
      markdown += `- ${decision}\n`;
    });
    markdown += `\n`;
  }

  // Overall Success Metrics
  if (plan.overall_success_metrics && plan.overall_success_metrics.length > 0) {
    markdown += `## Success Metrics\n\n`;
    plan.overall_success_metrics.forEach((metric: string) => {
      markdown += `- ${metric}\n`;
    });
    markdown += `\n`;
  }

  // Implementation Notes
  if (plan.full_design_doc_analyzed) {
    markdown += `## Implementation Notes\n\n`;
    markdown += `- ✅ Complete design document analyzed for comprehensive planning\n`;
    if (plan.finalized_design_doc_content_used) {
      markdown += `- ✅ Finalized design document content incorporated\n`;
    }
    markdown += `- ✅ Knowledge base integration for developer guidance\n`;
    markdown += `- ✅ Branch naming conventions enforced for consistency\n`;
    markdown += `- ✅ Quality control checkpoints built into each milestone\n\n`;
  }
  
  return markdown;
};

export const formatDesignDocAsMarkdownForGitHub = (doc: any): string => {
  let markdown = formatDesignDocAsMarkdown(doc);
  markdown += '\n\n---\n*Technical spec generated by [Nuvineer](https://nuvineer.ai)*';
  return markdown;
}

export const formatImplementationPlanAsMarkdownForGitHub = (plan: any): string => {
  let markdown = formatImplementationPlanAsMarkdown(plan);
  markdown += '\n\n---\n*Execution plan generated by [Nuvineer](https://nuvineer.ai)*';
  return markdown;
}

// Session ID generator
export const generateSessionId = () => `design-doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`; 