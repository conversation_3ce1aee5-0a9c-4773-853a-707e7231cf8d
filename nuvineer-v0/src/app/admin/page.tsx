'use client'

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface SearchResult {
  id: string;
  score: number;
  metadata: {
    title?: string;
    description?: string;
    rationale?: string;
    implications?: string;
    pr_number?: number;
    pr_url?: string;
    domain_concepts?: string[] | string;
    risks_extracted?: string;
    related_files?: string[] | string;
    is_superseded?: boolean;
    [key: string]: any;
  };
}

interface SearchResponse {
  success: boolean;
  results: SearchResult[];
  namespace: string;
  query: string;
  error?: string;
}

export default function AdminPage() {
  const [repositorySlug, setRepositorySlug] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isPublic, setIsPublic] = useState<boolean>(true);
  const [installationId, setInstallationId] = useState<string>('0');
  const [topK, setTopK] = useState<number>(10);
  const [minScore, setMinScore] = useState<number>(0.7);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(null);
  const router = useRouter();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!repositorySlug || !searchQuery) {
      setError('Repository slug and search query are required');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults([]);
    setSearchResponse(null);

    try {
      const response = await fetch('/api/admin/semantic-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repositorySlug,
          searchQuery,
          isPublic,
          installationId: isPublic ? '0' : installationId,
          topK,
          minScore,
        }),
      });

      const data: SearchResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to perform search');
      }

      setSearchResponse(data);
      setResults(data.results || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDomainConcepts = (concepts: string[] | string | undefined): string[] => {
    if (!concepts) return [];
    if (Array.isArray(concepts)) return concepts;
    if (typeof concepts === 'string') {
      return concepts.split(',').map(c => c.trim()).filter(Boolean);
    }
    return [];
  };

  const formatRisks = (risks: string | undefined): any[] => {
    if (!risks) return [];
    try {
      const parsed = JSON.parse(risks);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  };

  const formatRelatedFiles = (files: string[] | string | undefined): string[] => {
    if (!files) return [];
    if (Array.isArray(files)) return files;
    if (typeof files === 'string') {
      try {
        const parsed = JSON.parse(files);
        return Array.isArray(parsed) ? parsed : [];
      } catch {
        return files.split(',').map(f => f.trim()).filter(Boolean);
      }
    }
    return [];
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Admin - Semantic Search Debug
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Test semantic search against Pinecone decisions to debug context retrieval issues
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Repository Slug (owner/repo)
                </label>
                <input
                  type="text"
                  value={repositorySlug}
                  onChange={(e) => setRepositorySlug(e.target.value)}
                  placeholder="e.g., facebook/react"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Repository Type
                </label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={isPublic}
                      onChange={() => setIsPublic(true)}
                      className="mr-2"
                    />
                    Public
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      checked={!isPublic}
                      onChange={() => setIsPublic(false)}
                      className="mr-2"
                    />
                    Private
                  </label>
                </div>
              </div>
            </div>

            {!isPublic && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Installation ID
                </label>
                <input
                  type="text"
                  value={installationId}
                  onChange={(e) => setInstallationId(e.target.value)}
                  placeholder="GitHub App installation ID"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Search Query
              </label>
              <textarea
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Enter your semantic search query..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Top K Results
                </label>
                <input
                  type="number"
                  value={topK}
                  onChange={(e) => setTopK(parseInt(e.target.value) || 10)}
                  min="1"
                  max="50"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Min Score
                </label>
                <input
                  type="number"
                  value={minScore}
                  onChange={(e) => setMinScore(parseFloat(e.target.value) || 0.7)}
                  min="0"
                  max="1"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {isLoading ? 'Searching...' : 'Search'}
            </button>
          </form>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong className="font-bold">Error: </strong>
            <span>{error}</span>
          </div>
        )}

        {searchResponse && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Search Results
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                <strong>Namespace:</strong> {searchResponse.namespace}
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                <strong>Query:</strong> {searchResponse.query}
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                <strong>Results:</strong> {results.length} found
              </div>
            </div>
          </div>
        )}

        {results.length > 0 && (
          <div className="space-y-6">
            {results.map((result, index) => (
              <div key={result.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {result.metadata.title || 'Untitled Decision'}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      ID: {result.id} | Score: {result.score.toFixed(4)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      result.metadata.is_superseded 
                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' 
                        : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    }`}>
                      {result.metadata.is_superseded ? 'Superseded' : 'Active'}
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  {result.metadata.description && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Description:</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{result.metadata.description}</p>
                    </div>
                  )}

                  {result.metadata.rationale && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Rationale:</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{result.metadata.rationale}</p>
                    </div>
                  )}

                  {result.metadata.implications && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Implications:</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{result.metadata.implications}</p>
                    </div>
                  )}

                  {result.metadata.pr_number && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Source:</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        PR #{result.metadata.pr_number}
                        {result.metadata.pr_url && (
                          <span> - <a href={result.metadata.pr_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">View PR</a></span>
                        )}
                      </p>
                    </div>
                  )}

                  {formatDomainConcepts(result.metadata.domain_concepts).length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Domain Concepts:</h4>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {formatDomainConcepts(result.metadata.domain_concepts).map((concept, i) => (
                          <span key={i} className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-xs">
                            {concept}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {formatRisks(result.metadata.risks_extracted).length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Risks:</h4>
                      <div className="space-y-2 mt-1">
                        {formatRisks(result.metadata.risks_extracted).map((risk, i) => (
                          <div key={i} className="text-sm bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded">
                            <span className="font-medium">{risk.severity || 'Unknown'} - {risk.category || 'General'}:</span>
                            <p className="text-gray-600 dark:text-gray-400">{risk.description}</p>
                            {risk.mitigation && (
                              <p className="text-gray-600 dark:text-gray-400 mt-1">
                                <strong>Mitigation:</strong> {risk.mitigation}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {formatRelatedFiles(result.metadata.related_files).length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Related Files:</h4>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {formatRelatedFiles(result.metadata.related_files).slice(0, 10).map((file, i) => (
                          <span key={i} className="px-2 py-1 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded text-xs">
                            {file}
                          </span>
                        ))}
                        {formatRelatedFiles(result.metadata.related_files).length > 10 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded text-xs">
                            +{formatRelatedFiles(result.metadata.related_files).length - 10} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  <details className="mt-4">
                    <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                      Show Full Metadata
                    </summary>
                    <pre className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(result.metadata, null, 2)}
                    </pre>
                  </details>
                </div>
              </div>
            ))}
          </div>
        )}

        {searchResponse && results.length === 0 && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
            <p>No results found for the given search query and parameters.</p>
          </div>
        )}
      </div>
    </div>
  );
} 