'use client'

import { useEffect, useState, useCallback, useMemo, Suspense, useRef } from 'react';
import { useSearchParams, usePathname, useRouter } from 'next/navigation';
import { createBrowserClient } from '@supabase/ssr';
import type { Session } from '@supabase/supabase-js';
// import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'; // This package seems not installed/needed with @supabase/ssr
import DecisionFeedback from '@/components/DecisionFeedback';
import GeneralFeedback from '@/components/GeneralFeedback';
import Link from 'next/link';
// Remove the Heroicons import
// import { GlobeIcon, LockClosedIcon } from '@heroicons/react/24/outline';

// Custom SVG components to replace Heroicons
const GlobeIcon = ({ className = "h-6 w-6" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
  </svg>
);

const LockClosedIcon = ({ className = "h-6 w-6" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
  </svg>
);

// Simple interface for the decision structure returned by the API
interface Decision {
  id: string;
  metadata: Record<string, any>; // Define more specific structure if needed
}

// Interface for GitHub Installation
interface Installation {
  id: number;
  account: {
    login: string;
    avatar_url: string;
    type?: string; // Optional type
  };
}

// Interface for GitHub Repository
interface Repository {
  id: number;
  name: string;
  full_name: string; // owner/repo
  private?: boolean; // Optional private flag
}

// Interface for Analysis API Response Details
interface AnalysisResultDetail {
    pr_number: number;
    status: 'success' | 'no_decisions' | 'skipped' | 'failed' | 'analyzed' | 'db_update_failed'; // Added analyzed
    decisions?: number;
    reason?: string;
    error?: string;
}

// Interface for relationship data
interface DecisionRelationship {
    id: number;
    source_decision_pinecone_id: string;
    target_decision_pinecone_id: string;
    relationship_type: 'supersedes' | 'amends' | 'conflicts_with';
    confidence_score?: number;
    repository_slug: string;
    source_pr_number: number;
    created_at: string;
}

// Add debounce hook implementation
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Import the new component
import { RepositorySetupFlow } from '@/components/RepositorySetupFlow';

function HomeContent() {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const searchParams = useSearchParams();
  const pathname = usePathname(); 
  const router = useRouter(); 

  // State for installations
  const [installations, setInstallations] = useState<Installation[]>([]);
  const [selectedInstallationId, setSelectedInstallationId] = useState<string>('');
  const [isLoadingInstallations, setIsLoadingInstallations] = useState(false);
  const [installationError, setInstallationError] = useState<string | null>(null);

  // State for repositories
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [selectedRepositorySlug, setSelectedRepositorySlug] = useState<string>('');
  const [isLoadingRepositories, setIsLoadingRepositories] = useState(false);
  const [repositoryError, setRepositoryError] = useState<string | null>(null);

  // State for decisions
  const [decisions, setDecisions] = useState<Decision[]>([]);
  const [isFetchingDecisions, setIsFetchingDecisions] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  // State for Repository Analysis
  const [isAnalyzingRepo, setIsAnalyzingRepo] = useState(false);
  const [analyzeRepoStatus, setAnalyzeRepoStatus] = useState<string | null>(null);
  const [analysisHasMore, setAnalysisHasMore] = useState<boolean>(true);
  const [analysisDetails, setAnalysisDetails] = useState<AnalysisResultDetail[]>([]);
  const [analysisBatchSize, setAnalysisBatchSize] = useState<number>(1); // Changed from 5 to 1
  const [isLoadingDecisions, setIsLoadingDecisions] = useState<boolean>(false);
  const [relationships, setRelationships] = useState<DecisionRelationship[]>([]);
  const [isLoadingRelationships, setIsLoadingRelationships] = useState(false);
  const [relationshipError, setRelationshipError] = useState<string | null>(null);
  const [showOnlyActiveDecisions, setShowOnlyActiveDecisions] = useState(false);
  const [additionalDecisions, setAdditionalDecisions] = useState<Record<string, Decision | null>>({});
  const [isLoadingAdditionalDecisions, setIsLoadingAdditionalDecisions] = useState(false);
  const [sinceDate, setSinceDate] = useState<string>('');

  // ---> NEW State for Public/Private Flow
  const [analysisType, setAnalysisType] = useState<'public' | 'private' | null>(null);
  const [publicRepoInput, setPublicRepoInput] = useState<string>(''); // Input for public owner/repo
  const debouncedPublicRepoInput = useDebounce<string>(publicRepoInput, 800); // 800ms debounce
  const [repoSelectionError, setRepoSelectionError] = useState<string | null>(null); // Combined error for repo selection/input

  // State for domain filtering
  const [selectedDomainFilter, setSelectedDomainFilter] = useState<string | null>(null);

  // Add new state for setup workflow
  const [isSetupMode, setIsSetupMode] = useState(false);
  const [maxHistoricalPRs, setMaxHistoricalPRs] = useState<number>(50);

  // Add new state for "submitted" repository
  const [submittedRepoInput, setSubmittedRepoInput] = useState<string>('');
  
  // Add new state to track if analysis has run or data exists
  const [hasAnalysisRunOrDataExists, setHasAnalysisRunOrDataExists] = useState<boolean>(false);
  
  // Add new state for repository status check
  const [repoCheckedStatus, setRepoCheckedStatus] = useState<
    'idle' | 'checking' | 'not_started' | 'in_progress' | 'completed' | 'error' // Updated states
  >('idle');
  const [repoCheckError, setRepoCheckError] = useState<string | null>(null);
  const [pendingPrCommitsCount, setPendingPrCommitsCount] = useState<number | null>(null);
  const [pendingRelationshipsCount, setPendingRelationshipsCount] = useState<number | null>(null);
  const [currentAnalysisStage, setCurrentAnalysisStage] = useState<string | null>(null);

  // NEW: State for concept backfilling
  const [isBackfilling, setIsBackfilling] = useState(false);
  const [backfillStatus, setBackfillStatus] = useState<string | null>(null);

  // NEW: State to hold repository context derived from the URL path
  const [pathDerivedRepoContext, setPathDerivedRepoContext] = useState<{
    type: 'public' | 'private' | null;
    slug: string;
    installationId: string;
    source: 'path' | 'query' | 'none';
  }>({ type: null, slug: '', installationId: '', source: 'none' });

  // Ref to ensure initial state restoration from localStorage runs only once
  const initialStateRestoredRef = useRef(false);

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // --- Helper function for risk severity colors ---
  const getRiskColor = (severity: string): string => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // ---> ADDED: Helper for risk border colors
  const getRiskBorderColor = (severity: string): string => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return 'border-red-500';
      case 'medium':
        return 'border-yellow-500';
      case 'low':
        return 'border-green-500';
      default:
        return 'border-gray-500';
    }
  };
  // <--- END ADDED

  // ---> NEW: Helper function to format relative time
  const formatTimeAgo = (dateString: string | undefined | null): string | null => {
    if (!dateString) return null;

    const date = new Date(dateString);
    const now = new Date();
    const seconds = Math.round((now.getTime() - date.getTime()) / 1000);

    if (isNaN(seconds) || seconds < 0) return null; // Invalid date or future date

    const minutes = Math.round(seconds / 60);
    const hours = Math.round(minutes / 60);
    const days = Math.round(hours / 24);
    const weeks = Math.round(days / 7);
    const months = Math.round(days / 30.44); // Average days per month
    const years = Math.round(days / 365.25); // Account for leap years

    if (seconds < 60) return `${seconds}s ago`;
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    if (weeks < 5) return `${weeks}w ago`; // Up to 4 weeks
    if (months < 12) return `${months}mo ago`;
    return `${years}y ago`;
  };
  // <--- END HELPER

  // Create a ref to store the fetchDecisions function
  const fetchDecisionsRef = useRef<(() => Promise<void>) | null>(null);

  // ---> REVISED: Function to fetch decisions from the API
  const fetchDecisions = useCallback(async () => {
    let targetRepoSlug = '';
    let installationQueryParam = '';

    // Store the function in ref for access in other effects
    fetchDecisionsRef.current = fetchDecisions;

    // Determine target repo and parameters based on analysis type
    if (analysisType === 'public') {
      if (!submittedRepoInput || !submittedRepoInput.includes('/')) {
          setFetchError('Please enter a valid public repository slug (e.g., owner/repo) and press Enter or Submit.');
          return;
      }
      targetRepoSlug = submittedRepoInput;
    } else if (analysisType === 'private') {
      if (!selectedInstallationId || !selectedRepositorySlug) {
        setFetchError('Please select an installation and a repository.');
        return;
      }
      targetRepoSlug = selectedRepositorySlug;
      installationQueryParam = `&installationId=${selectedInstallationId}`;
    } else {
      setFetchError('Please select an analysis type (Public or Private).');
      return;
    }

    setIsFetchingDecisions(true);
    setFetchError(null);
    setDecisions([]); // Clear previous results

    // Construct API URL
    // Backend needs to handle calls with or without installationId
    const apiUrl = `/api/decisions?repository=${encodeURIComponent(targetRepoSlug)}${installationQueryParam}&limit=100`;

    console.log(`[fetchDecisions] Fetching decisions for ${targetRepoSlug} (Type: ${analysisType}, AnalysisHasRun: ${hasAnalysisRunOrDataExists})`);
    try {
      const response = await fetch(apiUrl);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to fetch decisions (Status: ${response.status})`);
      }

      console.log(`[fetchDecisions] API response received, found ${data.decisions?.length || 0} decisions`);
      
      const processedDecisions = (data.decisions || []).map((dec: any, index: number) => ({
        ...dec,
        id: dec.id || `${dec.metadata?.pr_number || 'unknown'}-${dec.metadata?.title?.replace(/\s+/g, '-') || index}`
      }));

      processedDecisions.sort((a: Decision, b: Decision) => {
        const dateA = a.metadata?.pr_merged_at ? new Date(a.metadata.pr_merged_at).getTime() : 0;
        const dateB = b.metadata?.pr_merged_at ? new Date(b.metadata.pr_merged_at).getTime() : 0;
        if (dateA === 0 && dateB === 0) return 0;
        if (dateA === 0) return 1;
        if (dateB === 0) return -1;
        return dateB - dateA;
      });

      setDecisions(processedDecisions);

      if (!data.decisions || data.decisions.length === 0) {
        if (analyzeRepoStatus && !analyzeRepoStatus.startsWith('Error:')) {
            setFetchError('Analysis complete, but no decisions were extracted or found. Check analysis logs.');
        } else {
            setFetchError('No decisions found matching the criteria.');
        }
      } else {
          setFetchError(null);
          console.log(`[fetchDecisions] Successfully set ${processedDecisions.length} decisions in state. Setting hasAnalysisRunOrDataExists to true.`);
          setHasAnalysisRunOrDataExists(true); // Found existing decisions, mark as true
      }
    } catch (error: any) {
      console.error('[fetchDecisions] Error fetching decisions:', error);
      setFetchError(error.message);
    } finally {
      setIsFetchingDecisions(false);
    }
  }, [analysisType, submittedRepoInput, selectedInstallationId, selectedRepositorySlug, analyzeRepoStatus, hasAnalysisRunOrDataExists]);

  // ---> NEW: Initialize state from URL parameters on mount
  useEffect(() => {
    if (!searchParams) return; // Guard against null searchParams

    const domainFilterParam = searchParams.get('domainFilter');
    
    if (domainFilterParam && !selectedDomainFilter) {
      console.log(`[Page Load - URL Params] Setting initial domain filter from URL: ${domainFilterParam}`);
      setSelectedDomainFilter(domainFilterParam);
    }

    // Initialize pathDerivedRepoContext from URL
    // This logic should run early to determine if we are on a specific repo path.
    if (!pathname) return; // Guard for null pathname
    const pathSegments = pathname.split('/').filter(Boolean);
    let type: 'public' | 'private' | null = null;
    let slug = '';
    let installationId = '';
    let source: 'path' | 'query' | 'none' = 'none';

    if (pathSegments[0] === 'analysis') {
      source = 'path';
      type = pathSegments[1] as 'public' | 'private';
      if (type === 'public' && pathSegments[2] && pathSegments[3]) {
        slug = `${pathSegments[2]}/${pathSegments[3]}`;
      } else if (type === 'private' && pathSegments[2] && pathSegments[3] && pathSegments[4]) {
        installationId = pathSegments[2];
        slug = `${pathSegments[3]}/${pathSegments[4]}`;
      } else {
        // Invalid path structure
        source = 'none'; type = null; slug = ''; installationId = '';
      }
    } else {
      // Not an analysis path, check query parameters for legacy or shared links
      const queryRepo = searchParams.get('repositorySlug') || searchParams.get('repo');
      const queryIsPublic = searchParams.get('isPublic');
      const queryInstallId = searchParams.get('installationId');

      if (queryRepo && queryRepo.includes('/')) {
        slug = queryRepo;
        source = 'query';
        if (queryIsPublic === 'true') {
          type = 'public';
        } else if (queryInstallId) {
          type = 'private';
          installationId = queryInstallId;
        } else {
          // Ambiguous query, treat as no direct context
          source = 'none'; type = null; slug = '';
        }
      }
    }
    console.log(`[Page Load - Path/Query Parsing] Pathname: ${pathname}, Derived Context:`, { type, slug, installationId, source });
    setPathDerivedRepoContext({ type, slug, installationId, source });

  }, [searchParams, pathname, selectedDomainFilter]); // Keep selectedDomainFilter if it's set by this effect

  // ---> NEW: Effect to load state from localStorage on initial mount (after pathDerivedRepoContext is set)
  useEffect(() => {
    if (typeof window !== 'undefined' && !initialStateRestoredRef.current && pathDerivedRepoContext.source === 'none') {
      // Only restore from localStorage if no repo context is derived from the URL path/query
      console.log("[Page Load - localStorage] Attempting to restore state from localStorage as no URL context found.");
      const storedAnalysisType = localStorage.getItem('analysisType') as 'public' | 'private' | null;
      const storedSelectedInstallationId = localStorage.getItem('selectedInstallationId');
      const storedSelectedRepositorySlug = localStorage.getItem('selectedRepositorySlug');
      const storedPublicRepoInput = localStorage.getItem('publicRepoInput');
      const storedSubmittedRepoInput = localStorage.getItem('submittedRepoInput');

      if (storedAnalysisType) {
        setAnalysisType(storedAnalysisType);
        console.log(`[Page Load - localStorage] Restored analysisType: ${storedAnalysisType}`);
      }
      if (storedSelectedInstallationId) {
        setSelectedInstallationId(storedSelectedInstallationId);
        console.log(`[Page Load - localStorage] Restored selectedInstallationId: ${storedSelectedInstallationId}`);
      }
      if (storedSelectedRepositorySlug) {
        setSelectedRepositorySlug(storedSelectedRepositorySlug);
        console.log(`[Page Load - localStorage] Restored selectedRepositorySlug: ${storedSelectedRepositorySlug}`);
      }
      if (storedPublicRepoInput) {
        setPublicRepoInput(storedPublicRepoInput);
        console.log(`[Page Load - localStorage] Restored publicRepoInput: ${storedPublicRepoInput}`);
      }
      if (storedSubmittedRepoInput) {
        setSubmittedRepoInput(storedSubmittedRepoInput);
        console.log(`[Page Load - localStorage] Restored submittedRepoInput: ${storedSubmittedRepoInput}`);
      }
      initialStateRestoredRef.current = true;
    }
  }, [pathDerivedRepoContext.source]); // Depends on pathDerivedRepoContext to ensure it runs after URL parsing

  // ---> NEW: Effects to save state to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (analysisType) localStorage.setItem('analysisType', analysisType);
      else localStorage.removeItem('analysisType');
    }
  }, [analysisType]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (selectedInstallationId) localStorage.setItem('selectedInstallationId', selectedInstallationId);
      else localStorage.removeItem('selectedInstallationId');
    }
  }, [selectedInstallationId]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (selectedRepositorySlug) localStorage.setItem('selectedRepositorySlug', selectedRepositorySlug);
      else localStorage.removeItem('selectedRepositorySlug');
    }
  }, [selectedRepositorySlug]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (publicRepoInput) localStorage.setItem('publicRepoInput', publicRepoInput);
      else localStorage.removeItem('publicRepoInput');
    }
  }, [publicRepoInput]);
  
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (submittedRepoInput) localStorage.setItem('submittedRepoInput', submittedRepoInput);
      else localStorage.removeItem('submittedRepoInput');
    }
  }, [submittedRepoInput]);

  // Access control: Check if user is in allowed list
  useEffect(() => {
    if (session?.user) {
      // Get allowed emails from environment variable
      const allowedEmails = process.env.NEXT_PUBLIC_ALLOWED_EMAILS?.split(',').map(email => email.trim().toLowerCase()) || [];
      const userEmail = session.user.email?.toLowerCase() || '';
      
      // Handle GitHub users that might be identified by ID instead of email
      const allowedGithubIds = process.env.NEXT_PUBLIC_ALLOWED_GITHUB_IDS?.split(',').map(id => id.trim()) || [];
      const githubId = session.user.user_metadata?.user_name || '';
      
      const isEmailAllowed = allowedEmails.length === 0 || allowedEmails.includes(userEmail);
      const isGithubIdAllowed = allowedGithubIds.length === 0 || allowedGithubIds.includes(githubId);
      
      // If either check passes, or if the allow lists are empty, authorize the user
      const userIsAuthorized = isEmailAllowed || isGithubIdAllowed;
      
      console.log(`[Access Control] Checking authorization for user: ${userEmail} / ${githubId}`);
      console.log(`[Access Control] User is ${userIsAuthorized ? 'authorized' : 'NOT authorized'}`);
      
      setIsAuthorized(userIsAuthorized);
    } else {
      setIsAuthorized(false);
    }
  }, [session]);

  // ---> REVISED: Fetch session and handle auth state changes
  useEffect(() => {
    // Only depends on supabase.auth, runs once to set up listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, newSession) => {
      console.log("Auth state change:", _event, !!newSession);
      setSession(newSession); // Update session state
      setIsLoadingSession(false);

      // Logic to restore pending analysis type after login/callback
      // This is now partially handled in the State Sync Effect.
      // Keep this for the logout scenario.
      if (!newSession) {
          console.log("[onAuthStateChange] User logged out, resetting state.");
          setInstallations([]);
          setSelectedInstallationId('');
          setRepositories([]);
          setDecisions([]);
          setFetchError(null);
          setAnalyzeRepoStatus(null);
          setAnalysisHasMore(true);
          setAnalysisDetails([]);
          setRelationships([]);
          setAdditionalDecisions({});
          setRepoSelectionError(null);
          setSinceDate('');
          setAnalysisType(null); 
          setHasAnalysisRunOrDataExists(false); 
          setInstallationError(null);
          setRepositoryError(null);
          setSelectedRepositorySlug('');
          setPublicRepoInput('');
          setSubmittedRepoInput('');
          setRepoCheckedStatus('idle');
          setRepoCheckError(null);
          // Navigate to root on logout if on an analysis path
          if (pathname && pathname.startsWith('/analysis/')) { // Guard for null pathname
            router.push('/');
          }
      }
    });

    supabase.auth.getSession().then(({ data: { session: initialSession } }) => {
        // Initial session check, onAuthStateChange will also fire.
        // This is mainly to ensure isLoadingSession is handled if onAuthStateChange is slow.
        if (!initialSession && isLoadingSession) { // only if not already handled
            setIsLoadingSession(false);
        }
    });

    return () => subscription.unsubscribe();
  }, [supabase.auth, isLoadingSession, pathname, router]); // Added pathname, router
  // --- END REVISED EFFECT

  // ---> NEW: Helper to reset state when changing repo type or selection, or entering setup
  const resetForNewSetup = useCallback(() => {
      console.log("Resetting state for new setup selection...");
      // Don't reset analysisType here if user is choosing between public/private in setup mode
      setDecisions([]);
      setFetchError(null);
      setAnalyzeRepoStatus(null);
      setAnalysisHasMore(true);
      setAnalysisDetails([]);
      setRelationships([]);
      setAdditionalDecisions({});
      setRepoSelectionError(null); 
      setSinceDate(''); 
      setHasAnalysisRunOrDataExists(false); 
      
      // Private repo state
      setInstallationError(null);
      setRepositoryError(null);
      setSelectedInstallationId('');
      // setInstallations([]); // Keep installations if fetched for the session
      setRepositories([]);
      setSelectedRepositorySlug(''); 
      
      // Public repo state
      setPublicRepoInput(''); 
      setSubmittedRepoInput(''); 
      
      setRepoCheckedStatus('idle'); 
      setRepoCheckError(null);
      
      // If currently on an analysis path, navigate to root as selection is being reset
      if (pathname && pathname.startsWith('/analysis/')) { // Guard for null pathname
          router.push('/');
      }
  }, [pathname, router]); // Add setters if they become dependencies, router, pathname

  // ---> REVISED: Fetch installations ONLY if 'private' mode is selected AND user is logged in.
  useEffect(() => {
    if (analysisType === 'private' && session) {
      console.log("Session available for private repo, fetching installations...");
      setIsLoadingInstallations(true);
      setInstallationError(null);
      fetch('/api/github/installations')
        .then(res => {
            if (!res.ok) {
                return res.json().then(err => { 
                  throw new Error(err.error || `Failed to fetch installations: ${res.status} ${res.statusText}`);
                });
            }
            return res.json();
        })
        .then(data => {
            if (data.success && data.installations) {
                console.log("Raw installations data:", data.installations);
                setInstallations(data.installations);
                console.log("Installations fetched:", data.installations.length);
                 if (data.installations.length === 0) {
                    setInstallationError("No installations found. Ensure the AIGuardRails GitHub App is installed and you've granted access.");
                }
            } else {
                 throw new Error(data.error || 'No installations found in response');
            }
        })
        .catch(err => {
            console.error("Error fetching installations:", err);
            setInstallationError(err.message);
            setInstallations([]); // Clear on error
        })
        .finally(() => setIsLoadingInstallations(false));
    } else {
        // Clear installations if not in private mode or not logged in
        setInstallations([]);
        setSelectedInstallationId('');
    }
  }, [analysisType, session]); // Depend on analysisType and session


  // ---> REVISED: Fetch repositories ONLY if an installation is selected (implicitly means private mode and logged in)
  useEffect(() => {
    if (selectedInstallationId && session && analysisType === 'private') { // Extra checks
      console.log(`Installation ${selectedInstallationId} selected, fetching repositories...`);
      setIsLoadingRepositories(true);
      setRepositoryError(null);
      setRepositories([]); // Clear previous list before fetching

      fetch(`/api/github/repositories?installationId=${selectedInstallationId}`)
        .then(res => {
           if (!res.ok) {
               return res.json().then(err => { 
                 throw new Error(err.error || `Failed to fetch repositories: ${res.status} ${res.statusText}`);
               });
           }
            return res.json();
        })
        .then(data => {
             if (data.success && data.repositories) {
                console.log("Raw repositories data:", data.repositories);
                setRepositories(data.repositories);
                console.log("Repositories fetched:", data.repositories.length);
                 if (data.repositories.length === 0) {
                    setRepositoryError("No repositories found for this installation.");
                }
             } else {
                throw new Error(data.error || 'No repositories found in response');
             }
        })
        .catch(err => {
            console.error("Error fetching repositories:", err);
            setRepositoryError(err.message);
            setRepositories([]); // Clear on error
        })
        .finally(() => setIsLoadingRepositories(false));
    } else {
        // Clear repositories if installation deselected, not logged in, or not private mode
        setRepositories([]);
        setSelectedRepositorySlug(''); // Reset selection
    }
  }, [selectedInstallationId, session, analysisType]); // Added analysisType


  // --- UI Event Handlers ---

  // ---> REVISED: Function to trigger repository analysis
  const startRepositoryAnalysis = useCallback(async () => {
    let apiBody: Record<string, any> = {
        batchSize: analysisBatchSize,
        sinceDate: sinceDate || null,
        maxHistoricalPRs: maxHistoricalPRs, // Add the max historical PRs parameter
    };
    let targetRepoForLog = '';

    // Validate common input: batch size
    if (analysisBatchSize <= 0 || analysisBatchSize > 100) {
       setAnalyzeRepoStatus('Error: Batch size must be between 1 and 100.');
       return;
    }

    // Prepare API body based on analysis type
    if (analysisType === 'public') {
        if (!submittedRepoInput || !submittedRepoInput.includes('/')) {
            setAnalyzeRepoStatus('Error: Please enter a valid public repository slug (e.g., owner/repo) and press Enter or Submit.');
            return;
        }
        targetRepoForLog = submittedRepoInput;
        apiBody.repositorySlug = targetRepoForLog;
        apiBody.isPublic = true; // Signal public analysis to backend
        // No installationId needed for public
    } else if (analysisType === 'private') {
        if (!selectedInstallationId || !selectedRepositorySlug) {
            setAnalyzeRepoStatus('Error: Please select an installation and a repository first.');
            return;
        }
        targetRepoForLog = selectedRepositorySlug;
        apiBody.installationId = selectedInstallationId;
        apiBody.repositorySlug = targetRepoForLog;
        apiBody.isPublic = false;
    } else {
        setAnalyzeRepoStatus('Error: Please select an analysis type (Public or Private).');
        return;
    }

    setIsAnalyzingRepo(true);
    setAnalyzeRepoStatus(prev =>
        prev && !prev.startsWith('Error:')
        ? `${prev} Starting next analysis batch...`
        : `Analyzing repository...This may take a while for repositories with extensive history.`
    );
    setFetchError(null); // Clear decision fetch error

    console.log(`Starting repository analysis batch for ${targetRepoForLog} (Type: ${analysisType})`);

    try {
      const response = await fetch('/api/analyze-repository', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `Failed to start repository analysis (Status: ${response.status})`);
      }

      setAnalyzeRepoStatus(data.message || `Analysis batch completed successfully.`);
      setAnalysisHasMore(data.has_more || false);
      setAnalysisDetails(prev => [...prev, ...(data.details || [])]);
      setHasAnalysisRunOrDataExists(true); // Mark that analysis has run
      console.log('Repository analysis API call successful:', data);

      if (data.processed_count > 0 || data.skipped_count > 0 || data.failed_count > 0) {
          setTimeout(() => {
              console.log("Analysis batch finished, refreshing decisions...");
              fetchDecisions(); // Refresh decisions after analysis
          }, 1000);
      }

    } catch (error: any) {
      console.error('Error starting repository analysis:', error);
      setAnalyzeRepoStatus(`Error analyzing batch: ${error.message}`);
      setAnalysisHasMore(false);
    } finally {
      setIsAnalyzingRepo(false);
    }
  }, [
      analysisType,
      submittedRepoInput, // Updated from debouncedPublicRepoInput 
      selectedInstallationId,
      selectedRepositorySlug,
      analysisBatchSize,
      fetchDecisions,
      sinceDate,
      maxHistoricalPRs,
  ]);

  // ---> REVISED: Implement handleLogin with Supabase OAuth and improved error handling
  const handleLogin = async () => {
    console.log("Attempting GitHub login with auth flow...");
    try {
      // Show a visual indicator that login is being attempted
      setIsLoadingSession(true);
      setAnalyzeRepoStatus("Connecting to GitHub...");
      
      // Set up scopes for private repo access
      const scopes = ['repo', 'read:user', 'user:email', 'read:org'];
      console.log(`Requesting scopes: ${scopes.join(', ')}`);
      
      const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'github',
      options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          scopes: scopes.join(','),
      },
    });
      
    if (error) {
        console.error('Error initiating GitHub login:', error);
        setAnalyzeRepoStatus(`Error: Failed to start GitHub login: ${error.message}`);
        setIsLoadingSession(false); // Reset loading state on error
        return;
      }
      
      console.log("GitHub login flow initiated successfully", data);
      
      // Store current analysis type in localStorage to restore after redirect
      if (analysisType) {
        console.log(`Storing analysis type '${analysisType}' for post-login restoration`);
        localStorage.setItem('pendingAnalysisType', analysisType);
      }
      
      setAnalyzeRepoStatus("Redirecting to GitHub for authorization...");
      // Don't reset isLoadingSession since we're redirecting
      // It will be reset when the user returns from GitHub OAuth flow
      
    } catch (exception) {
      console.error('Exception during GitHub login:', exception);
      setAnalyzeRepoStatus(`Error: Login process failed with an exception: ${exception instanceof Error ? exception.message : String(exception)}`);
      setIsLoadingSession(false);
    }
  };

  const handleLogout = async () => {
    setIsLoadingSession(true); // Indicate loading during logout
    // Before signing out, reset relevant state
    // resetStateForNewSelection(); // Reset repo-specific state
    // setAnalysisType(null); // Go back to initial choice screen
    await supabase.auth.signOut();
    // State clearing for session happens via onAuthStateChange listener
    // Explicitly reset analysis type and selections after logout completes in the listener
  };

  // ---> REVISED: Feedback submission needs the correct repository context
  const getCurrentRepoSlug = () => {
      return analysisType === 'public' ? submittedRepoInput : selectedRepositorySlug;
  };

  const handleDecisionFeedbackSubmit = useCallback(async (decisionId: string, reaction: string | null, comment: string) => {
    const repoSlug = getCurrentRepoSlug();
    if (!repoSlug) {
        alert("Cannot submit feedback: Repository context is missing.");
        console.error("Feedback submission failed: repoSlug is empty.");
        return; // Or throw an error
    }
    console.log("Attempting to submit Decision Feedback:", { decisionId, reaction, comment, repo: repoSlug });
    try {
      // Assuming the backend can handle feedback based on repo slug alone
      // If installationId is strictly required by backend even for public, this needs adjustment
      const response = await fetch('/api/feedback/decision', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ decisionId, reaction, comment, repo: repoSlug }),
      });

      if (!response.ok) {
        let errorMsg = `HTTP error! Status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMsg = errorData.error || errorData.message || JSON.stringify(errorData);
        } catch (parseError) {
          try {
            const textError = await response.text();
             console.error("Non-JSON error response body:", textError);
             errorMsg += ` - Response body is not JSON.`;
          } catch (textErrorErr) { }
        }
        throw new Error(errorMsg);
      }
      console.log('Decision feedback API call successful.');
    } catch (error) {
      console.error("Error submitting decision feedback via API:", error);
      alert(`Error submitting feedback: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }, [analysisType, submittedRepoInput, selectedRepositorySlug]); // Updated from publicRepoInput


  const handleGeneralFeedbackSubmit = useCallback(async (feedbackType: string, comment: string) => {
    const repoSlug = getCurrentRepoSlug();
     if (!repoSlug) {
        alert("Cannot submit feedback: Repository context is missing.");
        console.error("Feedback submission failed: repoSlug is empty.");
        return; // Or throw an error
    }
    console.log("Attempting to submit General Feedback:", { feedbackType, comment, repo: repoSlug });
    try {
      // Assuming backend handles feedback based on repo slug
      const response = await fetch('/api/feedback/general', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ feedbackType, comment, repo: repoSlug }),
      });

      if (!response.ok) {
        let errorMsg = `HTTP error! Status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMsg = errorData.error || errorData.message || JSON.stringify(errorData);
        } catch (parseError) {
           try {
            const textError = await response.text();
             console.error("Non-JSON error response body:", textError);
             errorMsg += ` - Response body is not JSON.`;
          } catch (textErrorErr) { }
        }
        throw new Error(errorMsg);
      }
      console.log('General feedback API call successful.');
    } catch (error) {
      console.error("Error submitting general feedback via API:", error);
      alert(`Error submitting feedback: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }, [analysisType, submittedRepoInput, selectedRepositorySlug]); // Updated from publicRepoInput


  const parseMitigation = (mitigationText: string) => {
    const shortTermPrefix = 'Short-term:';
    const longTermPrefix = 'Long-term:';
    const shortTermIndex = mitigationText.indexOf(shortTermPrefix);
    const longTermIndex = mitigationText.indexOf(longTermPrefix);

    if (shortTermIndex !== -1 && longTermIndex !== -1) {
      const shortTerm = mitigationText.substring(shortTermIndex + shortTermPrefix.length, longTermIndex).trim();
      const longTerm = mitigationText.substring(longTermIndex + longTermPrefix.length).trim();
      return { shortTerm, longTerm };
    } else if (shortTermIndex !== -1) {
      return { shortTerm: mitigationText.substring(shortTermIndex + shortTermPrefix.length).trim(), longTerm: null };
    } else if (longTermIndex !== -1) {
      return { shortTerm: null, longTerm: mitigationText.substring(longTermIndex + longTermPrefix.length).trim() };
    } else {
      return { shortTerm: mitigationText, longTerm: null };
    }
  };


  const supersededDecisionIds = useMemo(() => {
     return new Set(
         relationships
             .filter(rel => rel.relationship_type === 'supersedes')
             .map(rel => rel.target_decision_pinecone_id)
     );
  }, [relationships]);


  const displayedDecisions = useMemo(() => {
      let filtered = decisions;

      // Filter by active status first
      if (showOnlyActiveDecisions) {
          filtered = filtered.filter(dec => !supersededDecisionIds.has(dec.id));
      }

      // Filter by domain tag
      if (selectedDomainFilter) {
          console.log(`Applying domain filter: ${selectedDomainFilter}`);
          filtered = filtered.filter(dec => {
              // Handle both array and comma-separated string formats for domain_concepts
              const domains = dec.metadata?.domain_concepts;
              if (Array.isArray(domains)) {
                  return domains.includes(selectedDomainFilter);
              } else if (typeof domains === 'string') {
                  // Simple comma-separated check (adjust regex if needed for complex cases)
                  return domains.split(',').map(d => d.trim()).includes(selectedDomainFilter);
              }
              return false; // No domains match if not array or string
          });
      }

      return filtered;
  }, [decisions, showOnlyActiveDecisions, supersededDecisionIds, selectedDomainFilter]); // Add selectedDomainFilter dependency


  // ---> REVISED: Fetch relationships needs the correct repo slug
  const fetchRelationships = useCallback(async () => {
      const repoSlug = getCurrentRepoSlug();
    // Only proceed if repoSlug is a valid repository pattern (owner/repo)
    if (!repoSlug || !repoSlug.includes('/')) return;

      setIsLoadingRelationships(true);
      setRelationshipError(null);
      console.log(`Fetching relationships for ${repoSlug}...`);

      try {
          // Assuming backend API can find relationships by slug alone
          const params = new URLSearchParams({ repositorySlug: repoSlug });
          const response = await fetch(`/api/relationships?${params.toString()}`);
          const data = await response.json();

          if (!response.ok) {
              throw new Error(data.error || 'Failed to fetch relationships');
          }

          if (data.success) {
              setRelationships(data.relationships || []);
              console.log(`Fetched ${data.relationships?.length || 0} relationships.`);
          } else {
               throw new Error(data.error || 'API indicated failure');
          }
      } catch (error: any) {
          console.error('Error fetching relationships:', error);
          setRelationshipError(error.message);
          setRelationships([]);
      } finally {
          setIsLoadingRelationships(false);
      }
  }, [analysisType, submittedRepoInput, selectedRepositorySlug]); // Note the dependency change


  // ---> REVISED: Trigger relationship fetch when the effective repository changes
  useEffect(() => {
      const repoSlug = getCurrentRepoSlug();
      console.log(`[Effect - Trigger Relationships] Running. RepoSlug: '${repoSlug}', Type: ${analysisType}, Submitted: '${submittedRepoInput}', Selected: '${selectedRepositorySlug}', HasAnalysisRun: ${hasAnalysisRunOrDataExists}`); 
      
      // Check if analysis has run AND the repoSlug is valid before calling fetchRelationships
      if (hasAnalysisRunOrDataExists && repoSlug && repoSlug.includes('/')) { 
           console.log(`[Effect - Trigger Relationships] Conditions met (AnalysisRun: true, SlugValid: true), calling fetchRelationships.`); 
           fetchRelationships();
      } else {
          console.log(`[Effect - Trigger Relationships] Conditions not met (AnalysisRun: ${hasAnalysisRunOrDataExists}, SlugValid: ${!!(repoSlug && repoSlug.includes('/'))}), clearing relationships.`); 
          setRelationships([]); // Clear if conditions not met
      }
  }, [analysisType, submittedRepoInput, selectedRepositorySlug, hasAnalysisRunOrDataExists]); // Added hasAnalysisRunOrDataExists dependency


  // --- REVISED: Calculate Link properties based on current repo context ---
  // REMOVED analysisHref calculation as it's no longer used for navigation trigger
  // const currentRepoSlugForNav = getCurrentRepoSlug();
  // const canNavigateAnalysis = !!currentRepoSlugForNav && (analysisType === 'public' || (analysisType === 'private' && !!selectedInstallationId));
  // const analysisHref = useMemo(() => { ... }, [ ... ]); // REMOVED
  // const analysisTitle = canNavigateAnalysis ? ... : ...; // REMOVED, title handled directly on button


  // ---> REVISED: Fetch decision by ID needs correct repo context and potentially installation ID
  const fetchDecisionById = useCallback(async (decisionId: string) => {
    if (additionalDecisions[decisionId] !== undefined) return;
    setAdditionalDecisions(prev => ({ ...prev, [decisionId]: null })); // Mark loading

    const repoSlug = getCurrentRepoSlug();
    if (!repoSlug) {
        console.error(`[fetchDecisionById] Cannot fetch ${decisionId}, repository context missing.`);
        setAdditionalDecisions(prev => ({ ...prev, [decisionId]: null })); // Mark failed (no context)
        return;
    }

    let installationQueryParam = '';
    if (analysisType === 'private' && selectedInstallationId) {
        installationQueryParam = `&installationId=${encodeURIComponent(selectedInstallationId)}`;
    }
    // Backend /api/decisions/[id] needs to handle calls with or without installationId

    const apiUrl = `/api/decisions/${decisionId}?repositorySlug=${encodeURIComponent(repoSlug)}${installationQueryParam}`;

    try {
      const response = await fetch(apiUrl);
      if (!response.ok) {
          let errorMsg = `API Error: ${response.statusText} (Status: ${response.status})`;
          try {
              const errorData = await response.json();
              errorMsg = errorData.error || errorData.message || JSON.stringify(errorData);
          } catch (e) {
              try {
                const textError = await response.text();
                console.error("[fetchDecisionById] Non-JSON error response body:", textError);
                errorMsg += " - Response body is not JSON.";
              } catch (textE) {
                console.error(`[fetchDecisionById] Error fetching ${decisionId} for ${repoSlug}:`, errorMsg);
                setAdditionalDecisions(prev => ({ ...prev, [decisionId]: null }));
              }
          }
      }
      const data = await response.json();
      if (data.success && data.decision) {
         setAdditionalDecisions(prev => ({ ...prev, [decisionId]: data.decision }));
      } else {
         console.warn(`[fetchDecisionById] Decision ${decisionId} not found or API error:`, data.error || 'No specific error');
         setAdditionalDecisions(prev => ({ ...prev, [decisionId]: null }));
      }
    } catch (error) {
      console.error(`[fetchDecisionById] Network/fetch error fetching ${decisionId} for ${repoSlug}:`, error);
      setAdditionalDecisions(prev => ({ ...prev, [decisionId]: null }));
    }
  }, [additionalDecisions, analysisType, selectedInstallationId, submittedRepoInput, selectedRepositorySlug]); // Updated dependency


  // ---> REVISED: Effect to fetch missing related decisions needs repo context
  useEffect(() => {
    const repoSlug = getCurrentRepoSlug();
    // Only fetch if we have relationships, primary decisions, AND a repo context
    if (relationships.length > 0 && decisions.length > 0 && repoSlug) {
      const fetchedDecisionIds = new Set(decisions.map(d => d.id));
      const relatedIds = new Set<string>();
      relationships.forEach(rel => {
        relatedIds.add(rel.source_decision_pinecone_id);
        relatedIds.add(rel.target_decision_pinecone_id);
      });

      const missingIds = Array.from(relatedIds).filter(id =>
          !fetchedDecisionIds.has(id) &&
          additionalDecisions[id] === undefined
      );

      if (missingIds.length > 0) {
        console.log('[Effect] Found missing related decision IDs:', missingIds);
        setIsLoadingAdditionalDecisions(true);
        Promise.all(missingIds.map(id => fetchDecisionById(id)))
            .finally(() => {
                setIsLoadingAdditionalDecisions(false);
                console.log('[Effect] Finished fetching additional decisions.');
            });
      }
    }
     // Clear additional decisions if repo context is lost? Maybe not, could be useful if user switches back.
    // else {
    //    setAdditionalDecisions({});
    // }
  }, [relationships, decisions, fetchDecisionById, additionalDecisions, analysisType, submittedRepoInput, selectedRepositorySlug]); // Updated dependency


 // ---> NEW: Click handler for relationship links
  const handleRelationshipClick = (event: React.MouseEvent<HTMLAnchorElement>, targetDecisionId: string) => {
    console.log(`Attempting to navigate to: #decision-${targetDecisionId}`);
    const targetElement = document.getElementById(`decision-${targetDecisionId}`);
    if (targetElement) {
      // Element exists, scroll to it smoothly
      targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // Optional: Add a temporary highlight effect to the target card
      targetElement.classList.add('animate-pulse'); // Example effect
      setTimeout(() => targetElement.classList.remove('animate-pulse'), 1500);
    } else {
      // Element doesn't exist (yet?), let the default href handle it
      // or potentially show a "Loading..." state if we know it's being fetched.
      console.warn(`Target element #decision-${targetDecisionId} not found. Relying on href.`);
    }
    // Do not preventDefault, allow href to work
  };
  // <--- END HELPER


 // ---> NEW: Click handler for domain tags
  const handleDomainTagClick = (tag: string) => {
    console.log(`Toggling filter for domain: ${tag}`);
    setSelectedDomainFilter(prevFilter => (prevFilter === tag ? null : tag)); // Toggle filter on/off
  };
  // <--- END HELPER


 // ---> NEW: Helper function to handle analysis type selection
  const handleAnalysisTypeChange = (type: 'public' | 'private') => {
    if (type === analysisType) return; // No change

    console.log(`Changing analysis type to: ${type}`);
    console.log(`[handleAnalysisTypeChange] Current session state:`, session);
    // Reset everything before setting the new type
    resetForNewSetup();
    setAnalysisType(type);

    // Show clear message when private is selected without login
    if (type === 'private' && !session) {
      console.log("[handleAnalysisTypeChange] Private selected and no session detected. Triggering login...");
      setAnalyzeRepoStatus('GitHub authentication required for private repositories');
      // ---> ADDED: Automatically trigger login if private is selected without session
      handleLogin();
      // <--- END ADDED
    }
  };

  // --- ADDED: Helper for reset state when entering setup mode ---
  const enterSetupMode = () => {
    // resetForNewSetup(); // Resetting here might be too early if user is just switching between existing selections
    setIsSetupMode(true);
    // If not on an analysis path already, and analysisType is not set, user will pick type.
    // If on an analysis path, this button might not be shown, or means "change settings" for current repo.
    // If user is on decision view (isSetupMode=false, currentRepoSlug=valid), and clicks "Change Settings"
    // then isSetupMode becomes true. RepositorySetupFlow will show for currentRepoSlug.
  };

  // --- ADDED: Debug function for auth status ---
  const logAuthStatus = () => {
    console.log({
      session: !!session,
      isLoadingSession,
      analysisType,
      publicRepoInput,
      selectedInstallationId,
      selectedRepositorySlug,
      installations: installations.length,
      repositories: repositories.length,
    });
  };

  // ---> NEW: Function to check repository status via API
  const checkRepositoryAnalysisStatus = useCallback(async (repoSlug: string, isPublic: boolean, instId: string | null) => {
    if (!repoSlug || !repoSlug.includes('/')) return;
    
    setRepoCheckedStatus('checking');
    setRepoCheckError(null);
    console.log(`[checkRepositoryAnalysisStatus] Checking status for ${repoSlug} (Public: ${isPublic})`);

    try {
      const params = new URLSearchParams({ repositorySlug: repoSlug, isPublic: String(isPublic) });
      if (!isPublic && instId) {
        params.append('installationId', instId);
      } else if (!isPublic && !instId) {
          // This scenario should ideally be prevented by UI logic before calling
          console.error("[checkRepositoryAnalysisStatus] Error: Installation ID is required for private repository status check but was not provided.");
          setRepoCheckedStatus('error');
          setRepoCheckError("Installation ID is required for private repository status check.");
          setPendingPrCommitsCount(null);
          setPendingRelationshipsCount(null);
          setCurrentAnalysisStage(null);
          return;
      }
      
      const response = await fetch(`/api/analysis/repository-status?${params.toString()}`);
      const data = await response.json(); // data is of type MainRepositoryStatusResponse

      if (!response.ok) {
        // Attempt to get error from API response, otherwise use status text
        const errorMsg = data?.error || `API error (${response.status} ${response.statusText})`;
        throw new Error(errorMsg);
      }

      if (data.success) {
        setRepoCheckedStatus(data.analysisOverallStatus); // Use the new overall status directly
        setPendingPrCommitsCount(data.pendingPrCommitsCount ?? null);
        setPendingRelationshipsCount(data.pendingRelationshipsCount ?? null);
        setCurrentAnalysisStage(data.currentAnalysisStage ?? null);
        console.log(`[checkRepositoryAnalysisStatus] Status for ${repoSlug}: ${data.analysisOverallStatus}, Pending PRs: ${data.pendingPrCommitsCount}, Pending Rel: ${data.pendingRelationshipsCount}, Stage: ${data.currentAnalysisStage}`);
        // Potentially store data.details if needed for more granular UI updates later
      } else {
        // API returned success: false
        throw new Error(data.error || 'API indicated failure but did not provide an error message.');
      }
    } catch (error: any) {
      console.error(`[checkRepositoryAnalysisStatus] Error checking status for ${repoSlug}:`, error);
      setRepoCheckedStatus('error');
      setRepoCheckError(error.message);
      setPendingPrCommitsCount(null);
      setPendingRelationshipsCount(null);
      setCurrentAnalysisStage(null);
    }
  }, []); // No dependencies needed as it reads props/state directly when called

  // ---> REVISED: useEffect that triggers when repo context changes
  useEffect(() => {
    const repoSlug = getCurrentRepoSlug();
    console.log(`[Effect - Repo Context Change] Running. RepoSlug: '${repoSlug}', Type: ${analysisType}, Submitted: '${submittedRepoInput}', Selected: '${selectedRepositorySlug}'`);
    
    // Reset check status if repo context becomes invalid
    if (!repoSlug || !repoSlug.includes('/')) {
      setRepoCheckedStatus('idle');
      setRepoCheckError(null);
      // Also clear relationships if context is lost
      setRelationships([]); 
      setHasAnalysisRunOrDataExists(false); // Reset this flag too
      return; 
    }

    // If we have a valid slug, trigger the status check
    console.log(`[Effect - Repo Context Change] Valid repo slug detected, calling checkRepositoryAnalysisStatus.`);
    checkRepositoryAnalysisStatus(
      repoSlug,
      analysisType === 'public',
      analysisType === 'private' ? selectedInstallationId : null
    );

  }, [analysisType, submittedRepoInput, selectedRepositorySlug, checkRepositoryAnalysisStatus, selectedInstallationId]); // Added check function and installationId to dependencies

  // ---> NEW: Polling effect for live status updates during analysis
  useEffect(() => {
    const repoSlug = getCurrentRepoSlug();
    
    // Only poll if we have a valid repo and status is in_progress
    if (!repoSlug || !repoSlug.includes('/') || repoCheckedStatus !== 'in_progress') {
      return;
    }

    console.log(`[Status Polling] Starting polling for ${repoSlug} (status: ${repoCheckedStatus})`);
    
    // Poll every 3 seconds while analysis is in progress
    const pollInterval = setInterval(() => {
      console.log(`[Status Polling] Polling status for ${repoSlug}...`);
      checkRepositoryAnalysisStatus(
        repoSlug,
        analysisType === 'public',
        analysisType === 'private' ? selectedInstallationId : null
      );
    }, 3000); // Poll every 3 seconds

    // Cleanup interval on unmount or when dependencies change
    return () => {
      console.log(`[Status Polling] Stopping polling for ${repoSlug}`);
      clearInterval(pollInterval);
    };
  }, [repoCheckedStatus, analysisType, submittedRepoInput, selectedRepositorySlug, selectedInstallationId, checkRepositoryAnalysisStatus]);

  // --- Render Logic ---
  const currentRepoSlug = getCurrentRepoSlug();

  // Effect to fetch decisions when analysis state changes
  useEffect(() => {
    if (hasAnalysisRunOrDataExists && currentRepoSlug) {
      console.log(`[Effect] Analysis data exists for ${currentRepoSlug}, fetching decisions...`);
      fetchDecisions();
    } else if (!currentRepoSlug && decisions.length > 0) {
      console.log(`[Effect] currentRepoSlug is empty, clearing decisions.`);
      setDecisions([]); // Clear decisions if no repo is selected
    }
  }, [hasAnalysisRunOrDataExists, currentRepoSlug, fetchDecisions]); // Removed submittedRepoInput, selectedRepositorySlug as currentRepoSlug covers them

  // Add dedicated effect for autoFetch parameter from query (only if context NOT from path)
  useEffect(() => {
    if (!searchParams || pathDerivedRepoContext.source === 'path') return; 

    const autoFetchParam = searchParams.get('autoFetch');
    const repositoryParam = pathDerivedRepoContext.slug; // Use slug from context if source is 'query'
    
    if (autoFetchParam === 'true' && repositoryParam && pathDerivedRepoContext.source === 'query') {
      console.log(`[AutoFetch Query] Auto-fetching decisions for ${repositoryParam} due to autoFetch query parameter`);
      // Ensure other states like analysisType are set from pathDerivedRepoContext by its own effect first
      const timer = setTimeout(() => {
        if (currentRepoSlug === repositoryParam && hasAnalysisRunOrDataExists) { // Check if context is ready
          fetchDecisions();
        } else if (currentRepoSlug === repositoryParam && !hasAnalysisRunOrDataExists) {
          // This case is if setHasAnalysisRunOrDataExists was missed or needs re-trigger for query
          // This might have been set true by the State Sync Effect too.
          console.warn("[AutoFetch Query] hasAnalysisRunOrDataExists is false, but attempting fetch. This might be okay if status check will confirm.");
          fetchDecisions();
        }
      }, 300); // Small delay for state propagation
      
      return () => clearTimeout(timer);
    }
  }, [searchParams, pathDerivedRepoContext, currentRepoSlug, fetchDecisions, hasAnalysisRunOrDataExists]);
  
  // When user clicks "Get Started" or "Change Repository"
  const handleEnterSetupMode = () => {
    if (currentRepoSlug) {
      console.log(`[handleEnterSetupMode] Entering setup for existing repo: ${currentRepoSlug}`);
      setIsSetupMode(true); 
    } else {
      console.log(`[handleEnterSetupMode] Entering setup for new repo selection.`);
      resetForNewSetup(); 
      setIsSetupMode(true); 
    }
  };

  useEffect(() => {
    console.log(`[State Sync Effect] Running. PathDerived: `, pathDerivedRepoContext, `isSetupMode: ${isSetupMode}`);
    const autoFetchParam = searchParams ? searchParams.get('autoFetch') === 'true' : false; // Added null check for searchParams

    if (pathDerivedRepoContext.type && pathDerivedRepoContext.slug) {
      console.log(`[State Sync Effect] Applying path derived context: Type=${pathDerivedRepoContext.type}, Slug=${pathDerivedRepoContext.slug}`);
      setAnalysisType(pathDerivedRepoContext.type);
      if (pathDerivedRepoContext.type === 'public') {
        setPublicRepoInput(pathDerivedRepoContext.slug);
        setSubmittedRepoInput(pathDerivedRepoContext.slug);
        // setSelectedRepositorySlug(pathDerivedRepoContext.slug); // Not for public type
        setSelectedInstallationId('');
      } else { // private
        setPublicRepoInput('');
        // setSubmittedRepoInput(''); // Not for private type
        setSelectedInstallationId(pathDerivedRepoContext.installationId);
        setSelectedRepositorySlug(pathDerivedRepoContext.slug);
      }
      
      if (pathDerivedRepoContext.source === 'path') { 
        console.log(`[State Sync Effect] Context from path. Current isSetupMode: ${isSetupMode}. Setting isSetupMode to false.`); // MODIFIED LOG
        setIsSetupMode(false);
      }
      
      setMaxHistoricalPRs(50); 
      setAnalysisBatchSize(1);  
      setSinceDate('');         
      
    }

    // Logic to restore pending analysis type after login/callback
    if (session && !analysisType) {
      const pendingType = localStorage.getItem('pendingAnalysisType');
      if (pendingType === 'public' || pendingType === 'private') {
        console.log(`[State Sync Effect] Restoring pending analysis type: ${pendingType}`);
        setAnalysisType(pendingType as 'public' | 'private');
        localStorage.removeItem('pendingAnalysisType');
        if (pathDerivedRepoContext.source !== 'path') {
          setIsSetupMode(true);
        }
      }
    }

  }, [
      pathDerivedRepoContext, 
      session, 
      // analysisType, // Removed: analysisType is set by this effect based on pathDerivedRepoContext or session state.
                     // Its inclusion could cause re-runs that might have unintended consequences if not perfectly managed.
      setAnalysisType, 
      setPublicRepoInput, 
      setSubmittedRepoInput, 
      setSelectedRepositorySlug, 
      setSelectedInstallationId, 
      setIsSetupMode,
      // No setMaxHistoricalPRs, etc. in deps as they are set inside
  ]);

  // ---> NEW: Function to trigger the knowledge graph build
  const handleBuildKnowledgeGraph = useCallback(async () => {
    const repoSlug = getCurrentRepoSlug();
    if (!repoSlug) {
      setBackfillStatus('Error: No repository selected.');
      return;
    }

    setIsBackfilling(true);
    setBackfillStatus(`Starting knowledge graph build for ${repoSlug}...`);

    try {
      const response = await fetch('/api/analysis/build-knowledge-graph', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repositorySlug: repoSlug,
          isPublic: analysisType === 'public',
          installationId: analysisType === 'private' ? selectedInstallationId : null,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start knowledge graph build process.');
      }

      setBackfillStatus(data.message || 'Build process started successfully.');
      console.log('Knowledge graph build API call successful:', data);
    } catch (error: any) {
      console.error('Error during knowledge graph build:', error);
      setBackfillStatus(`Error: ${error.message}`);
    } finally {
      // Keep the "building" state active as this is an async process
      // A more robust solution would involve polling a status endpoint.
      // For now, we'll just leave the button disabled and message active.
      // setIsBackfilling(false); 
    }
  }, [analysisType, submittedRepoInput, selectedRepositorySlug, selectedInstallationId]);

  // ---> REVISED: useEffect for fetching relationships
  useEffect(() => {
    const repoSlug = getCurrentRepoSlug();
    console.log(`[Effect - Trigger Relationships Check] Running. RepoSlug: '${repoSlug}', HasAnalysisRun: ${hasAnalysisRunOrDataExists}, RepoCheckedStatus: ${repoCheckedStatus}`);

    // Fetch relationships if: Repo status known to be completed OR analysis manually run/fetched, AND slug is valid
    if ((repoCheckedStatus === 'completed' || hasAnalysisRunOrDataExists) && repoSlug && repoSlug.includes('/')) {
      console.log(`[Effect - Trigger Relationships] Conditions met, calling fetchRelationships.`);
      fetchRelationships();
    } else {
      console.log(`[Effect - Trigger Relationships] Conditions not met, clearing relationships.`);
      setRelationships([]); 
    }
  // Depend on repoCheckedStatus now as well
  }, [analysisType, submittedRepoInput, selectedRepositorySlug, hasAnalysisRunOrDataExists, repoCheckedStatus, fetchRelationships]); 

  return (
    <main className="flex min-h-screen flex-col items-center p-4 md:p-8 lg:p-16 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      {/* Header */}
      <div className="z-10 max-w-6xl w-full items-center justify-between font-mono text-sm lg:flex mb-8">
        <p className="text-lg font-semibold fixed left-0 top-0 flex w-full justify-center border-b border-gray-300 bg-gradient-to-b from-zinc-200 pb-4 pt-6 backdrop-blur-2xl dark:border-neutral-800 dark:bg-zinc-800/30 dark:from-inherit lg:static lg:w-auto lg:rounded-xl lg:border lg:bg-gray-200 lg:p-4 lg:dark:bg-zinc-800/30">
           <span>
            <span className="text-zinc-900 dark:text-zinc-100">Nuv</span>
            <span className="text-amber-500 dark:text-amber-400">ineer</span>
            <span className="text-zinc-900 dark:text-zinc-100">&gt;_</span>
           </span>
           <span className="ml-2 text-gray-500 dark:text-gray-400">// Operating System for AI-Powered Product Engineering</span>
        </p>
        <div className="fixed bottom-0 left-0 flex h-24 w-full items-end justify-center bg-gradient-to-t from-white via-white dark:from-black dark:via-black lg:static lg:h-auto lg:w-auto lg:bg-none">
          {/* Login/Logout based on session, not analysis type */}
          {isLoadingSession ? (
            <p className="text-xs">Loading session...</p>
          ) : session ? (
            <div className="flex items-center gap-2">
                <span className="text-xs text-gray-600 dark:text-gray-400 hidden sm:inline">Logged in as {session.user?.user_metadata?.user_name || session.user?.email}</span>
              
                <button
                    onClick={handleLogout}
                    className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-xs"
                    disabled={isLoadingSession}
                    >
                    Logout
                </button>
            </div>
          ) : (<span></span>
            //  <button
            //     onClick={handleLogin}
            //     className="bg-amber-500 hover:bg-amber-600 text-white font-bold py-1 px-3 rounded text-xs"
            //     disabled={isLoadingSession}
            //   >
            //     Login with GitHub
            //   </button>
          )}
        </div>
      </div>

      {/* Content based on session and authorization state */}
      {isLoadingSession ? (
        <div className="flex justify-center items-center min-h-[calc(100vh-200px)]">
          <p className="text-lg">Loading session...</p>
        </div>
      ) : !session ? (
        <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] text-center p-8">
          <h1 className="text-5xl font-bold mb-4">
            <span className="text-zinc-800 dark:text-zinc-200">Nuv</span>
            <span className="text-amber-500 dark:text-amber-400">ineer</span>
            <span className="text-zinc-800 dark:text-zinc-200">&gt;</span>
            <span className="text-zinc-800 dark:text-zinc-200 -ml-1">_</span>
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-lg">
            The Operating System for AI-Powered Product Engineering
          </p>
          <button
            onClick={handleLogin}
            className="px-8 py-3 bg-amber-500 hover:bg-amber-600 text-white rounded-lg shadow-md text-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 flex items-center"
            disabled={isLoadingSession}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 98 96" className="mr-3 fill-current"><path fillRule="evenodd" clipRule="evenodd" d="M48.854 0C21.839 0 0 22 0 49.217c0 21.756 13.993 40.172 33.405 46.69 2.427.49 3.316-1.059 3.316-2.362 0-1.141-.08-5.052-.08-9.127-13.59 2.934-16.42-5.867-16.42-5.867-2.184-5.704-5.42-7.17-5.42-7.17-4.448-3.015.324-3.015.324-3.015 4.934.326 7.523 5.052 7.523 5.052 4.367 7.496 11.404 5.378 14.235 4.074.404-3.178 1.699-5.378 3.074-6.6-10.839-1.141-22.243-5.378-22.243-24.283 0-5.378 1.94-9.778 5.014-13.2-.485-1.222-2.184-6.275.486-13.038 0 0 4.125-1.304 13.426 5.052a46.97 46.97 0 0 1 12.214-1.63c4.125 0 8.33.571 12.213 1.63 9.302-6.356 13.427-5.052 13.427-5.052 2.67 6.763.97 11.816.485 13.038 3.155 3.422 5.015 7.822 5.015 13.2 0 18.905-11.404 23.06-22.324 24.283 1.78 1.548 3.316 4.481 3.316 9.126 0 6.6-.08 11.897-.08 13.526 0 1.304.89 2.853 3.316 2.364 19.412-6.52 33.405-24.935 33.405-46.691C97.707 22 75.788 0 48.854 0z" /></svg>
            Login with GitHub
          </button>
        </div>
      ) : !isAuthorized ? (
        <div className="w-full max-w-md bg-red-50 dark:bg-red-900/30 p-6 rounded-lg border border-red-200 dark:border-red-800 shadow-md text-center mb-8 mx-auto mt-10">
          <svg className="h-12 w-12 text-red-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h2 className="text-xl font-bold text-red-700 dark:text-red-400 mb-2">Access Restricted</h2>
          <p className="text-red-600 dark:text-red-300 mb-4">
            Your account ({session.user?.email || session.user?.user_metadata?.user_name}) is not authorized to access this application.
          </p>
          <p className="text-sm text-red-500 dark:text-red-300">
            This product is in early access.
          </p>
          <button
            onClick={handleLogout}
            className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm font-medium"
          >
            Logout
          </button>
        </div>
      ) : (
        // User is logged in AND authorized
        <>
          {!isSetupMode && !currentRepoSlug && (
            <div className="flex flex-col items-center justify-center p-8 max-w-2xl mx-auto text-center">
              <h1 className="text-6xl font-bold mb-4">
                <span className="text-zinc-800 dark:text-zinc-200">Nuv</span>
                <span className="text-amber-500 dark:text-amber-400">ineer</span>
                <span className="text-zinc-800 dark:text-zinc-200">&gt;</span>
                <span className="text-zinc-800 dark:text-zinc-200 -ml-2">_</span>
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-400 mb-10">
                The Operating System for AI-Powered Product Engineering
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 w-full">
                <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-100 dark:border-amber-800">
                  <h3 className="font-semibold mb-2 flex items-center">
                    <span className="mr-2 text-amber-600 dark:text-amber-400">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M5 3a2 2 0 0 0-2 2"></path><path d="M19 3a2 2 0 0 1 2 2"></path>
                        <path d="M21 19a2 2 0 0 1-2 2"></path><path d="M5 21a2 2 0 0 1-2-2"></path>
                        <path d="M9 3h1"></path><path d="M9 21h1"></path>
                        <path d="M14 3h1"></path><path d="M14 21h1"></path>
                        <path d="M3 9v1"></path><path d="M21 9v1"></path>
                        <path d="M3 14v1"></path><path d="M21 14v1"></path>
                      </svg>
                    </span>
                    Reduce Tech Lead Bottlenecks
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Nuvineer enables senior engineers to focus on strategic work instead of repetitive explaining.
                  </p>
              </div>

                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-100 dark:border-purple-800">
                  <h3 className="font-semibold mb-2 flex items-center">
                    <span className="mr-2 text-purple-600 dark:text-purple-400">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                        <path d="M12 17h.01"></path>
                      </svg>
                    </span>
                    Democratize Product & Technical Context
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Transform tribal knowledge into accessible insights for every team member, ensuring consistent quality across your organization.
                  </p>
                    </div>
                
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-800">
                  <h3 className="font-semibold mb-2 flex items-center">
                    <span className="mr-2 text-green-600 dark:text-green-400">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M12 5l0 14"></path>
                        <path d="M18 13l-6 6"></path>
                        <path d="M6 13l6 6"></path>
                      </svg>
                    </span>
                    Accelerate Developer Onboarding
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    New team members (human or AI) build and ship features faster with Nuvineer.
                  </p>
                            </div>
                        
                <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-100 dark:border-amber-800">
                  <h3 className="font-semibold mb-2 flex items-center">
                    <span className="mr-2 text-amber-600 dark:text-amber-400">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 7v6h-6"></path>
                        <path d="M3 17v-6h6"></path>
                        <path d="M15.5 4h-7"></path>
                        <path d="M15.5 20h-7"></path>
                        <path d="M8.5 4l-5 7.5 5 7.5"></path>
                        <path d="M15.5 4l5 7.5-5 7.5"></path>
                      </svg>
                    </span>
                    Enhance AI Assisted Development
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                      Idea to implementation plans in minutes, aligned with your product vision & system architecture.
                  </p>
                </div>
                                </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button
                  onClick={enterSetupMode}
                  className="px-6 py-3 bg-amber-500 hover:bg-amber-600 text-white rounded-md shadow-sm text-base font-medium transition-colors"
                >
                  Get Started
                </button>
              </div>
                       </div>
                  )}

          {isSetupMode && (
            <RepositorySetupFlow 
              analysisType={analysisType}
              setAnalysisType={setAnalysisType}
              session={session}
              isLoadingSession={isLoadingSession}
              // handleLogin={handleLogin} // Removed as RepositorySetupFlow is now gated by session
              installations={installations}
              selectedInstallationId={selectedInstallationId}
              setSelectedInstallationId={setSelectedInstallationId}
              isLoadingInstallations={isLoadingInstallations}
              installationError={installationError}
              repositories={repositories}
              selectedRepositorySlug={selectedRepositorySlug}
              setSelectedRepositorySlug={setSelectedRepositorySlug}
              isLoadingRepositories={isLoadingRepositories}
              repositoryError={repositoryError}
              publicRepoInput={publicRepoInput}
              setPublicRepoInput={setPublicRepoInput}
              setSubmittedRepoInput={setSubmittedRepoInput}
              maxHistoricalPRs={maxHistoricalPRs}
              setMaxHistoricalPRs={setMaxHistoricalPRs}
              analysisBatchSize={analysisBatchSize}
              setAnalysisBatchSize={setAnalysisBatchSize}
              sinceDate={sinceDate}
              setSinceDate={setSinceDate}
              currentRepoSlug={currentRepoSlug}
              analyzeRepoStatus={analyzeRepoStatus}
              setAnalyzeRepoStatus={setAnalyzeRepoStatus}
              setIsSetupMode={setIsSetupMode}
              startRepositoryAnalysis={startRepositoryAnalysis} 
              logAuthStatus={logAuthStatus} 
              repoCheckedStatus={repoCheckedStatus}
              repoCheckError={repoCheckError}
              fetchDecisions={fetchDecisionsRef.current || undefined}
              setHasAnalysisRunOrDataExists={setHasAnalysisRunOrDataExists}
              pendingPrCommitsCount={pendingPrCommitsCount}
              pendingRelationshipsCount={pendingRelationshipsCount}
              currentAnalysisStage={currentAnalysisStage}
              // Pass backfill props
              handleBuildKnowledgeGraph={handleBuildKnowledgeGraph}
              isBackfilling={isBackfilling}
              backfillStatus={backfillStatus}
            />
          )}

          {!isSetupMode && currentRepoSlug && (
            <div className="w-full max-w-4xl mb-8 p-6 bg-white dark:bg-zinc-800 rounded-lg shadow-md">
              <div className="flex flex-wrap justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Repository: {currentRepoSlug}</h2>
                <div className="flex gap-2">
                  <button
                    onClick={handleEnterSetupMode}
                    className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded"
                  >
                    Change Settings
                  </button>
                </div>
              </div>

              {/* Analysis Status & Details Area */}
               {(analyzeRepoStatus || analysisDetails.length > 0) && (
                  <div className="mt-4 p-3 rounded text-sm bg-gray-100 dark:bg-zinc-700">
                      {analyzeRepoStatus && (
                         <p className={`mb-2 ${analyzeRepoStatus.startsWith('Error:') ? 'text-red-600 dark:text-red-400' : 'text-blue-600 dark:text-blue-400'}`}>
                              {analyzeRepoStatus}
                         </p>
                      )}
                      {analysisDetails.length > 0 && (
                         <details className="text-xs">
                            <summary className="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">Show Analysis Details ({analysisDetails.length} PRs processed so far)</summary>
                            <ul className="mt-2 space-y-1 list-disc list-inside max-h-40 overflow-y-auto">
                               {analysisDetails.map((detail, index) => (
                                  <li key={`${detail.pr_number}-${index}`}>
                                     PR #{detail.pr_number}: {detail.status}
                                     {(detail.status === 'success' || detail.status === 'analyzed') && ` (${detail.decisions} decisions)`}
                                     {detail.status === 'skipped' && ` (${detail.reason})`}
                                     {(detail.status === 'failed' || detail.status === 'db_update_failed') && ` - ${detail.error || detail.reason}`}
                                  </li>
                               ))}
                            </ul>
                         </details>
                      )}
                  </div>
               )}
              
              {/* View Analysis Buttons */}
              {currentRepoSlug && (repoCheckedStatus === 'completed' || hasAnalysisRunOrDataExists) && (
                <div className="flex flex-wrap items-center gap-4 mt-4 mb-4">
                  {/* DEBUG: Design Doc Wizard Button - Should be visible if Risk Summary is visible */}
                  <Link
                    href={`/design-doc-wizard?repositorySlug=${encodeURIComponent(currentRepoSlug)}&isPublic=${analysisType === 'public'}&installationId=${analysisType === 'private' ? selectedInstallationId : '0'}`}
                    className="inline-block px-4 py-2 rounded bg-amber-600 hover:bg-amber-700 text-white text-sm font-medium shadow-md border-2 border-amber-500"
                    title={`Create AI-guided design documents for ${currentRepoSlug}`}
                  >
                    <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    AI Design Doc Wizard
                  </Link>

                  {/* RCA Analysis Wizard - Only for jabubaker */}
                  {session?.user?.user_metadata?.user_name === 'jabubaker' && (
                    <Link
                      href={`/rca-wizard?repo=${encodeURIComponent(currentRepoSlug.split('/')[1])}&owner=${encodeURIComponent(currentRepoSlug.split('/')[0])}&isPublic=${analysisType === 'public'}&installationId=${analysisType === 'private' ? selectedInstallationId : '0'}`}
                      className="inline-block px-4 py-2 rounded bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium shadow-md border-2 border-purple-500"
                      title={`Investigate production incidents for ${currentRepoSlug}`}
                    >
                      <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      RCA Analysis Wizard
                    </Link>
                  )}

                  {/* Link to Risk Dashboard */}
                  <Link
                    href={`/risk-dashboard?repositorySlug=${encodeURIComponent(currentRepoSlug)}&isPublic=${analysisType === 'public'}&installationId=${analysisType === 'private' ? selectedInstallationId : '0'}`}
                    className="inline-block px-4 py-2 rounded bg-red-600 hover:bg-red-700 text-white text-sm font-medium"
                    target="_blank"
                    rel="noopener noreferrer"
                    title={`View Risk Summary for ${currentRepoSlug}`}
                  >
                    View Risk Summary
                  </Link>

                  {/* Link to Concept Explorer */}
                  <Link
                    href={`/concepts?repositorySlug=${encodeURIComponent(currentRepoSlug)}&isPublic=${analysisType === 'public'}&installationId=${selectedInstallationId || ''}`}
                    className="inline-block px-4 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium"
                    title={`Explore concepts for ${currentRepoSlug}`}
                  >
                    Explore Concepts
                  </Link>

                  {/* Button to trigger concept backfill */}
                  {/* <button
                      onClick={handleConceptBackfill}
                      disabled={isBackfilling}
                      className="inline-block px-4 py-2 rounded bg-teal-600 hover:bg-teal-700 text-white text-sm font-medium disabled:bg-gray-400 disabled:cursor-not-allowed"
                      title={`Backfill concepts for ${currentRepoSlug} from existing decisions`}
                  >
                      {isBackfilling ? 'Backfilling...' : 'Backfill Concepts'}
                  </button> */}
                  {backfillStatus && <p className="text-xs text-gray-500 dark:text-gray-400">{backfillStatus}</p>}
                </div>
              )}
            </div>
          )}

          {/* Decision Results Area */}
          {!isSetupMode && currentRepoSlug && (
            <div className="mt-6 w-full max-w-4xl">
              {fetchError && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4 text-sm" role="alert">
                  <strong className="font-bold">Error: </strong>
                  <span className="block sm:inline">{fetchError}</span>
                </div>
              )}

              {displayedDecisions.length > 0 && (
                <div className="bg-white dark:bg-zinc-800 p-6 rounded-lg shadow-md space-y-4">
                  <div className="flex flex-wrap justify-between items-center gap-4 mb-4">
                    <h3 className="text-xl font-semibold">Found Decisions ({displayedDecisions.length}{showOnlyActiveDecisions ? ' Active' : ''})</h3>
                    <div className="flex items-center">
                                 {/* Toggle Active Decisions */}
                                  <label className="flex items-center cursor-pointer text-sm">
                                      <input
                                          type="checkbox"
                                          checked={showOnlyActiveDecisions}
                                          onChange={(e) => setShowOnlyActiveDecisions(e.target.checked)}
                                          className="mr-2 form-checkbox h-4 w-4 text-amber-600 transition duration-150 ease-in-out dark:bg-zinc-600 dark:border-zinc-500"
                                      />
                                      Show Only Active Decisions
                                       {isLoadingRelationships && <span className="text-xs text-gray-500 ml-2">(Loading relationships...)</span>}
                                       {relationshipError && <span className="text-xs text-red-500 ml-2">(Error loading relationships!)</span>}
                                  </label>
                             </div>
                         </div>

                  {/* Decision Cards */}
                  <div className="space-y-4">
                    {displayedDecisions.map((decision) => {
                      let risks = [];
                      try {
                        if (decision.metadata?.risks_extracted && typeof decision.metadata.risks_extracted === 'string') {
                          risks = JSON.parse(decision.metadata.risks_extracted);
                          if (!Array.isArray(risks)) risks = [];
                        } else if (Array.isArray(decision.metadata?.risks_extracted)) {
                          risks = decision.metadata.risks_extracted;
                        }
                      } catch (e) {
                        console.error(`[Decision UI] Failed to parse risks for decision ${decision.id}:`, e);
                        risks = [];
                      }

                      let relatedFiles: string[] = [];
                      try {
                        if (decision.metadata?.related_files && typeof decision.metadata.related_files === 'string') {
                            relatedFiles = JSON.parse(decision.metadata.related_files);
                            if (!Array.isArray(relatedFiles)) relatedFiles = [];
                        } else if (Array.isArray(decision.metadata?.related_files)) {
                            relatedFiles = decision.metadata.related_files;
                        }
                      } catch (e) {
                         console.error(`[Decision UI] Failed to parse related_files for decision ${decision.id}:`, e);
                         relatedFiles = [];
                      }

                      const isExtension = String(decision.metadata?.is_extension).toLowerCase() === 'true';
                      const followsStandard = String(decision.metadata?.follows_standard_practice).toLowerCase() === 'true';
                      const decisionKey = `decision-${decision.id || decision.metadata?.pr_number + '-' + decision.metadata?.title}`;
                      const relationsFromThis = relationships.filter(r => r.source_decision_pinecone_id === decision.id);
                      const relationsToThis = relationships.filter(r => r.target_decision_pinecone_id === decision.id);
                      const isSuperseded = supersededDecisionIds.has(decision.id);

                      // Combine and deduplicate relationships by related decision ID
                      const relatedInfoMap: Record<string, { types: { type: string, direction: 'outgoing' | 'incoming' }[] }> = {};
                      relationsFromThis.forEach(rel => {
                        const targetId = rel.target_decision_pinecone_id;
                        if (!relatedInfoMap[targetId]) {
                          relatedInfoMap[targetId] = { types: [] };
                        }
                        relatedInfoMap[targetId].types.push({ type: rel.relationship_type, direction: 'outgoing' });
                      });
                      relationsToThis.forEach(rel => {
                         const sourceId = rel.source_decision_pinecone_id;
                         if (!relatedInfoMap[sourceId]) {
                           relatedInfoMap[sourceId] = { types: [] };
                         }
                         // Avoid adding duplicate types if multiple relations of the same type exist
                         if (!relatedInfoMap[sourceId].types.some(t => t.type === rel.relationship_type && t.direction === 'incoming')) {
                           relatedInfoMap[sourceId].types.push({ type: rel.relationship_type, direction: 'incoming' });
                         }
                      });
                      const uniqueRelatedIds = Object.keys(relatedInfoMap);

                      return (
                        <div key={decisionKey} id={`decision-${decision.id}`} className={`bg-white dark:bg-zinc-800 p-4 rounded-lg shadow border border-gray-200 dark:border-zinc-700 relative transition-opacity duration-300 ${isSuperseded ? 'opacity-60 hover:opacity-100' : ''}`}>
                            {isSuperseded && (
                                <span className="absolute top-2 right-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-500 text-white dark:bg-gray-600 z-10" title="This decision has been marked as superseded by another decision.">
                                     Retired / Superseded
                                </span>
                            )}
                            <h4 className="font-semibold text-lg mb-1 pr-28">{decision.metadata?.title || 'Untitled Decision'}</h4>
                            <div className="flex flex-wrap gap-2 mb-2 text-xs">
                              {isExtension && (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                    Pattern Extension
                                  </span>
                              )}
                              {followsStandard && (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200" title={decision.metadata?.follows_standard_practice_reason || 'Follows standard industry practice'}>
                                    Follows Standard Practice{decision.metadata?.follows_standard_practice_reason ? `: ${decision.metadata.follows_standard_practice_reason}` : ''}
                                  </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                Source: {
                                   (() => {
                                       const prUrl = decision.metadata?.pr_url;
                                       const prNumber = decision.metadata?.pr_number;
                                       const repoSlug = getCurrentRepoSlug(); // Use helper to get current slug

                                       let finalUrl = prUrl;
                                       if (!finalUrl && repoSlug && prNumber) {
                                           finalUrl = `https://github.com/${repoSlug}/pull/${prNumber}`;
                                       }

                                       if (finalUrl) {
                                           return (
                                               <a href={finalUrl} target="_blank" rel="noopener noreferrer" className="text-amber-600 hover:underline dark:text-amber-500">
                                                   {finalUrl.replace('https://github.com/', '')}
                                               </a>
                                           );
                                       } else {
                                           return <span className="text-gray-400">N/A</span>;
                                       }
                                   })()
                                }
                                {' | '}
                                {
                                    (() => {
                                       const timeAgo = formatTimeAgo(decision.metadata?.pr_merged_at);
                                       if (timeAgo) {
                                           return <span title={new Date(decision.metadata.pr_merged_at).toLocaleString()}>{timeAgo}</span>;
                                       } else {
                                           return null; // Don't show anything if no valid date
                                       }
                                    })()
                                }
                            </p>
                            {/* Domain Concepts (Tags) */}
                            {(decision.metadata?.domain_concepts?.length > 0) && (
                              <div className="flex flex-wrap gap-1.5 mt-1.5 mb-1">
                                {(Array.isArray(decision.metadata.domain_concepts)
                                    ? decision.metadata.domain_concepts
                                    : typeof decision.metadata.domain_concepts === 'string'
                                        ? decision.metadata.domain_concepts.split(',').map((d: string) => d.trim()).filter(Boolean)
                                        : []
                                ).map((tag: string, index: number) => (
                                    <button
                                      key={index}
                                      onClick={() => handleDomainTagClick(tag)}
                                      className={`px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-150 ease-in-out border ${ 
                                          selectedDomainFilter === tag ? 'bg-amber-500 text-white border-amber-600' : 'bg-gray-100 text-gray-700 dark:bg-zinc-700 dark:text-gray-300 border-gray-300 dark:border-zinc-600 hover:bg-gray-200 dark:hover:bg-zinc-600'
                                      }`}
                                    >
                                      {tag}
                                    </button>
                                ))}
                                    </div>
                                  )}
                            <p className="text-sm mb-1"><strong>Description:</strong> {decision.metadata?.description || 'N/A'}</p>
                            <p className="text-sm mb-1"><strong>Rationale:</strong> {decision.metadata?.rationale || 'N/A'}</p>
                            <p className="text-sm mb-1"><strong>Implications:</strong> {decision.metadata?.implications || 'N/A'}</p>
                            {decision.metadata?.dev_prompt && (
                              <p className="text-sm mb-1"><strong>Developer Guidance:</strong> {decision.metadata.dev_prompt}</p>
                            )}

                            {relatedFiles.length > 0 && (
                              <details className="mt-2 text-xs">
                                <summary className="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">Related Files ({relatedFiles.length})</summary>
                                <ul className="list-disc list-inside pl-4 mt-1 text-gray-600 dark:text-gray-400 max-h-24 overflow-y-auto bg-gray-50 dark:bg-zinc-700 p-2 rounded">
                                  {relatedFiles.map((file: string, index: number) => (
                                    <li key={index} className="truncate" title={file}>{file}</li>
                                  ))}
                                </ul>
                              </details>
                            )}

                            {risks.length > 0 && (
                              <div className="mt-3 pt-3 border-t border-gray-200 dark:border-zinc-700">
                                <h5 className="text-sm font-semibold mb-2 text-gray-800 dark:text-gray-200">Limitations:</h5>
                                <ul className="list-none space-y-2 pl-0">
                                  {risks.map((risk: any, index: number) => {
                                    const mitigationParts = parseMitigation(risk.mitigation || 'N/A');
                                    const severity = risk.severity;
                                    const borderColorClass = getRiskBorderColor(severity);
                                    const colorClass = getRiskColor(severity);
                                    return (
                                        <li key={index} className={`text-sm border-l-4 pl-3 py-1.5 ${borderColorClass} bg-opacity-50 dark:bg-opacity-20 ${
                                          severity?.toLowerCase() === 'high' ? 'bg-red-50 dark:bg-red-900' :
                                          severity?.toLowerCase() === 'medium' ? 'bg-yellow-50 dark:bg-yellow-900' :
                                          severity?.toLowerCase() === 'low' ? 'bg-green-50 dark:bg-green-900' : 'bg-gray-50 dark:bg-gray-700'
                                        }`}>
                                            <span className={`px-1.5 py-0.5 mr-1.5 rounded-full text-xs font-medium ${colorClass}`}>
                                                {severity?.toUpperCase() || 'UNKNOWN'}
                                            </span>
                                            <span className="font-medium text-gray-900 dark:text-gray-100">{(risk.category || 'General').toUpperCase()}:</span>
                                            {mitigationParts.shortTerm && (
                                                <p className="ml-4 mt-0.5 text-gray-700 dark:text-gray-300">
                                                    {mitigationParts.longTerm ? <strong className="font-normal">Short-term:</strong> : null} {mitigationParts.shortTerm}
                                                </p>
                                            )}
                                            {mitigationParts.longTerm && (
                                                <p className="ml-4 mt-0.5 text-gray-700 dark:text-gray-300">
                                                    <strong className="font-normal">Long-term:</strong> {mitigationParts.longTerm}
                                                </p>
                                            )}
                                            <p className="text-xs text-gray-500 dark:text-gray-400 block pl-4 mt-0.5"><em>(Why: {risk.description || 'No description provided'})</em></p>
                                        </li>
                                    );
                                  })}
                                </ul>
                              </div>
                            )}

                            {/* Relationship Rendering */}
                            {uniqueRelatedIds.length > 0 && (
                                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-zinc-600 text-xs">
                                    <h6 className="font-semibold mb-1.5 text-gray-600 dark:text-gray-400">Relationships:</h6>
                                    <ul className="list-none space-y-1 pl-0">
                                        {uniqueRelatedIds.map(relatedId => {
                                            const relatedInfo = relatedInfoMap[relatedId];
                                            const relatedDecision = decisions.find(d => d.id === relatedId) || additionalDecisions[relatedId];
                                            let relatedTitle = `ID: ${relatedId.substring(0, 8)}...`;
                                            let relatedLoading = false;

                                            if (relatedDecision) {
                                                relatedTitle = relatedDecision.metadata?.title || `[Untitled: ${relatedId.substring(0, 8)}...]`;
                                            } else if (additionalDecisions[relatedId] === undefined && isLoadingAdditionalDecisions && !decisions.some(d => d.id === relatedId)) {
                                                relatedTitle = '[Loading...]';
                                                relatedLoading = true;
                                            } else if (additionalDecisions[relatedId] === null) {
                                                relatedTitle = '[Not Found]';
                                            }

                                            // Format relationship types
                                            const formattedTypes = relatedInfo.types.map(t => {
                                                const baseType = t.type.charAt(0).toUpperCase() + t.type.slice(1);
                                                let prefix = '';
                                                let colorClass = '';
                                                switch(t.type) {
                                                    case 'supersedes': colorClass = 'text-red-600 dark:text-red-400'; break;
                                                    case 'amends': colorClass = 'text-amber-600 dark:text-amber-500'; break;
                                                    case 'conflicts_with': colorClass = 'text-orange-600 dark:text-orange-400'; break;
                                                }

                                                if (t.direction === 'incoming') {
                                                    prefix = t.type === 'supersedes' ? 'Superseded By' :
                                                             t.type === 'amends' ? 'Amended By' :
                                                             t.type === 'conflicts_with' ? 'Conflicts With (Incoming)' : 
                                                             'Related To';
                                                } else { // outgoing
                                                    prefix = baseType;
                                                }
                                                return { text: prefix, color: colorClass };
                                            }).filter((value, index, self) => // Deduplicate formatted strings
                                                 index === self.findIndex((t) => (
                                                   t.text === value.text && t.color === value.color
                                                 ))
                                            );

                                            return (
                                                <li key={`rel-${relatedId}`} className="text-gray-700 dark:text-gray-300">
                                                    {formattedTypes.map((ft, idx) => (
                                                        <span key={idx} className={`font-medium ${ft.color}`}>
                                                            {ft.text}{idx < formattedTypes.length - 1 ? ', ' : ': '}
                                                        </span>
                                                    ))}
                                                    <a href={`#decision-${relatedId}`}
                                                       onClick={(e) => handleRelationshipClick(e, relatedId)}
                                                       className={`ml-1 ${relatedLoading ? 'italic text-gray-500' : 'hover:underline'}`}
                                                       title={relatedId}> {relatedTitle}</a>
                                                </li>
                                            );
                                        })}
                                    </ul>
                                </div>
                            )}

                            <details className="mt-3 pt-2 border-t border-gray-200 dark:border-zinc-700 text-xs">
                                <summary className="cursor-pointer text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">Show Full Metadata</summary>
                                <pre className="mt-1 bg-gray-50 dark:bg-zinc-900 p-2 rounded overflow-auto max-h-60 text-xs border border-gray-200 dark:border-zinc-700">
                                    {JSON.stringify(decision.metadata || {}, null, 2)}
                                </pre>
                            </details>

                            <DecisionFeedback
                              decisionId={decision.id}
                              onSubmitFeedback={handleDecisionFeedbackSubmit}
                            />
                        </div>
                      );
                    })}
                  </div>
                      </div>
                  )}
            </div>
         )}


         {/* Spacer at the bottom */}
          <div className="h-20"></div>

      </>
    )}

    {/* ... existing footer ... */}
  </main>
);
}

export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HomeContent />
    </Suspense>
  );
}