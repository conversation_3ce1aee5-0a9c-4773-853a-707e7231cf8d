import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAdmin = createClient(supabaseUrl!, supabaseServiceRoleKey!);

export async function GET(request: NextRequest) {
  const requestId = `status_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const logPrefix = `[Repository Status API] [${requestId}]`;
  
  try {
    const { searchParams } = new URL(request.url);
    const repositorySlug = searchParams.get('repositorySlug');
    const installationId = searchParams.get('installationId');

    console.log(`${logPrefix} Request params: repositorySlug=${repositorySlug}, installationId=${installationId}`);

    if (!repositorySlug || !installationId) {
      const error = `Missing required parameters: repositorySlug=${!!repositorySlug}, installationId=${!!installationId}`;
      console.error(`${logPrefix} ${error}`);
      return NextResponse.json({ 
        error: 'Missing required parameters: repositorySlug and installationId' 
      }, { status: 400 });
    }

    const effectiveInstallationId = parseInt(installationId, 10);
    if (isNaN(effectiveInstallationId)) {
      const error = `Invalid installationId format: ${installationId}`;
      console.error(`${logPrefix} ${error}`);
      return NextResponse.json({ 
        error: 'Invalid installationId format - must be a number' 
      }, { status: 400 });
    }

    console.log(`${logPrefix} Checking status for ${repositorySlug} with installation ${effectiveInstallationId}`);

    // Check deployment constitution status
    console.log(`${logPrefix} Querying deployment_constitutions table`);
    const { data: deploymentData, error: deploymentError } = await supabaseAdmin
      .from('deployment_constitutions')
      .select('id, created_at, updated_at')
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', effectiveInstallationId)
      .single();

    const hasDeploymentConstitution = !deploymentError && deploymentData;
    console.log(`${logPrefix} Deployment constitution status: ${hasDeploymentConstitution ? 'EXISTS' : 'MISSING'}`);
    if (deploymentError && deploymentError.code !== 'PGRST116') {
      console.warn(`${logPrefix} Deployment constitution query error: ${deploymentError.message}`);
    }

    // Check repository analysis status
    console.log(`${logPrefix} Querying repository_pr_analysis_status table`);
    const { data: analysisData, error: analysisError } = await supabaseAdmin
      .from('repository_pr_analysis_status')
      .select('status, last_updated')
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', effectiveInstallationId)
      .single();

    const analysisStatus = analysisData?.status || 'not_started';
    console.log(`${logPrefix} Analysis status: ${analysisStatus}`);
    if (analysisError && analysisError.code !== 'PGRST116') {
      console.warn(`${logPrefix} Analysis status query error: ${analysisError.message}`);
    }

    const result = {
      success: true,
      repositorySlug,
      installationId: effectiveInstallationId,
      analysisStatus,
      hasDeploymentConstitution,
      deploymentConstitutionCreatedAt: deploymentData?.created_at || null,
      deploymentConstitutionUpdatedAt: deploymentData?.updated_at || null,
      lastAnalysisUpdate: analysisData?.last_updated || null
    };

    console.log(`${logPrefix} ✅ Status check completed:`, JSON.stringify(result, null, 2));
    return NextResponse.json(result);

  } catch (error) {
    console.error(`${logPrefix} ❌ Unexpected error:`, error);
    return NextResponse.json({ 
      error: 'Failed to check repository status',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 