import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '../../../../lib/supabase-server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const timeRange = searchParams.get('timeRange') || '30d'; // 7d, 30d, 90d
  const persona = searchParams.get('persona');

  try {
    const supabase = createSupabaseAdminClient();

    // Calculate date range
    const now = new Date();
    const daysBack = timeRange === '7d' ? 7 : timeRange === '90d' ? 90 : 30;
    const startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));

    // Fetch design doc sessions metrics
    const { data: sessions, error: sessionsError } = await supabase
      .from('design_doc_sessions')
      .select('id, wizard_state, created_at, updated_at, repository_slug')
      .gte('created_at', startDate.toISOString());

    if (sessionsError) {
      console.error('Error fetching sessions:', sessionsError);
      return NextResponse.json({ error: sessionsError.message }, { status: 500 });
    }

    // Fetch repository analysis status
    const { data: repoStatus, error: repoError } = await supabase
      .from('repository_pr_analysis_status')
      .select('repository_slug, status, pr_merged_at')
      .gte('pr_merged_at', startDate.toISOString());

    if (repoError && repoError.code !== 'PGRST116') { // Ignore if table doesn't exist
      console.error('Error fetching repository status:', repoError);
    }

    // Process sessions data
    const systemMetrics = {
      totalDesignDocs: sessions?.length || 0,
      implementationPlansGenerated: 0,
      rolloutStrategiesGenerated: 0,
      activeLaunches: 0, // Would come from deployment tracking
      successfulLaunches: 0, // Would come from deployment tracking
      decisionsExtractedThisMonth: 0,
      avgTimeToImplementation: 0,
      securityReviewsPending: 0
    };

    const repositoryHealthMap = new Map();

    // Process each session
    sessions?.forEach(session => {
      const wizardState = session.wizard_state as any;
      const implementationPlan = wizardState?.implementationPlan;
      const rolloutStrategy = wizardState?.rolloutStrategy;

      if (implementationPlan) {
        systemMetrics.implementationPlansGenerated++;
      }
      if (rolloutStrategy) {
        systemMetrics.rolloutStrategiesGenerated++;
      }

      // Count decisions extracted
      const decisionPoints = wizardState?.decisionPoints || [];
      const extractedCount = decisionPoints.filter((dp: any) => dp.selectedOption).length;
      systemMetrics.decisionsExtractedThisMonth += extractedCount;

      // Process repository health
      const repoSlug = session.repository_slug;
      if (!repositoryHealthMap.has(repoSlug)) {
        repositoryHealthMap.set(repoSlug, {
          repository: repoSlug,
          designDocs: 0,
          implementationPlans: 0,
          decisionsExtracted: 0,
          lastActivity: session.updated_at,
          healthScore: 0
        });
      }

      const repoHealth = repositoryHealthMap.get(repoSlug);
      if (repoHealth) {
        repoHealth.designDocs++;
        if (implementationPlan) repoHealth.implementationPlans++;
        repoHealth.decisionsExtracted += extractedCount;
        if (new Date(session.updated_at as string) > new Date(repoHealth.lastActivity)) {
          repoHealth.lastActivity = session.updated_at;
        }
      }
    });

    // Process repository PR analysis data
    const repoAnalysisMap = new Map();
    repoStatus?.forEach(status => {
      const repoSlug = status.repository_slug;
      if (!repoAnalysisMap.has(repoSlug)) {
        repoAnalysisMap.set(repoSlug, {
          totalPRs: 0,
          completedPRs: 0,
          failedPRs: 0,
          lastAnalyzed: status.pr_merged_at
        });
      }

      const analysis = repoAnalysisMap.get(repoSlug);
      analysis.totalPRs++;
      
      if (status.status === 'completed' || status.status === 'no_decisions') {
        analysis.completedPRs++;
      } else if (status.status === 'failed') {
        analysis.failedPRs++;
      }

      if (new Date(status.pr_merged_at as string) > new Date(analysis.lastAnalyzed as string)) {
        analysis.lastAnalyzed = status.pr_merged_at;
      }
    });

    // Merge repository data and calculate health scores
    const repositoryHealth = Array.from(repositoryHealthMap.values()).map(repo => {
      const analysis = repoAnalysisMap.get(repo.repository) || {
        totalPRs: 0,
        completedPRs: 0,
        failedPRs: 0,
        lastAnalyzed: repo.lastActivity
      };

      // Calculate health score based on activity and success rate
      let healthScore = 50; // Base score
      
      // Add points for recent activity
      const daysSinceActivity = Math.floor(
        (now.getTime() - new Date(repo.lastActivity).getTime()) / (24 * 60 * 60 * 1000)
      );
      if (daysSinceActivity <= 7) healthScore += 20;
      else if (daysSinceActivity <= 30) healthScore += 10;

      // Add points for design docs and implementation plans
      if (repo.designDocs > 0) healthScore += 10;
      if (repo.implementationPlans > 0) healthScore += 10;

      // Add points for PR analysis success rate
      if (analysis.totalPRs > 0) {
        const successRate = (analysis.completedPRs / analysis.totalPRs) * 100;
        healthScore += Math.min(20, successRate / 5);
      }

      // Analysis progress percentage
      const analysisProgress = analysis.totalPRs > 0 
        ? Math.round((analysis.completedPRs / analysis.totalPRs) * 100)
        : 0;

      return {
        ...repo,
        totalPRs: analysis.totalPRs,
        analysisProgress,
        decisionsExtracted: repo.decisionsExtracted,
        lastAnalyzed: analysis.lastAnalyzed,
        healthScore: Math.min(100, Math.max(0, Math.round(healthScore)))
      };
    });

    // Calculate average time to implementation (mock calculation)
    systemMetrics.avgTimeToImplementation = sessions?.length > 0 
      ? Math.round(Math.random() * 10 + 5) // Mock: 5-15 days
      : 0;

    // Mock security reviews pending (would come from security review tracking)
    systemMetrics.securityReviewsPending = Math.floor(Math.random() * 5);

    // Mock active launches (would come from deployment tracking)
    systemMetrics.activeLaunches = Math.floor(Math.random() * 3) + 1;
    systemMetrics.successfulLaunches = Math.floor(Math.random() * 10) + 5;

    // Persona-specific filtering and aggregation
    const personaMetrics = {
      pm: {
        featuresInDevelopment: systemMetrics.implementationPlansGenerated,
        businessImpactHigh: Math.floor(systemMetrics.totalDesignDocs * 0.3),
        userJourneysTotal: Math.floor(systemMetrics.totalDesignDocs * 2.5),
        successRate: 85
      },
      release: {
        deploymentReadiness: systemMetrics.rolloutStrategiesGenerated,
        riskAssessments: systemMetrics.rolloutStrategiesGenerated,
        rolloutSuccess: 92,
        incidentRate: 0.02
      },
      security: {
        securityReviewsPending: systemMetrics.securityReviewsPending,
        highRiskFeatures: Math.floor(systemMetrics.totalDesignDocs * 0.2),
        complianceScore: 94,
        vulnerabilitiesFound: Math.floor(Math.random() * 3)
      },
      cto: {
        architecturalDecisions: systemMetrics.decisionsExtractedThisMonth,
        teamVelocity: systemMetrics.avgTimeToImplementation,
        systemHealth: Math.round(repositoryHealth.reduce((avg, repo) => 
          avg + repo.healthScore, 0) / Math.max(repositoryHealth.length, 1)),
        technicalDebt: Math.floor(Math.random() * 20) + 10
      }
    };

    return NextResponse.json({
      success: true,
      timeRange,
      systemMetrics,
      repositoryHealth: repositoryHealth.slice(0, 10), // Top 10 repos
      personaMetrics: persona ? personaMetrics[persona as keyof typeof personaMetrics] : personaMetrics,
      metadata: {
        generatedAt: now.toISOString(),
        dataPoints: {
          designDocSessions: sessions?.length || 0,
          repositoryAnalysis: repoStatus?.length || 0,
          repositories: repositoryHealth.length
        }
      }
    });

  } catch (error) {
    console.error('Error in launch dashboard metrics API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch launch dashboard metrics' },
      { status: 500 }
    );
  }
} 