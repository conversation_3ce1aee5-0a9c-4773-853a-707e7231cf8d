import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '../../../../lib/supabase-server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const repositorySlug = searchParams.get('repository');
  const installationId = searchParams.get('installationId');
  const status = searchParams.get('status'); // filter by status
  const persona = searchParams.get('persona'); // filter by persona requirements

  try {
    const supabase = createSupabaseAdminClient();

    // Query design doc sessions with implementation plans and rollout strategies
    let query = supabase
      .from('design_doc_sessions')
      .select(`
        id,
        repository_slug,
        installation_id,
        title,
        github_issue_url,
        wizard_state,
        created_at,
        updated_at
      `)
      .order('updated_at', { ascending: false });

    // Apply filters
    if (repositorySlug) {
      query = query.eq('repository_slug', repositorySlug);
    }
    if (installationId) {
      query = query.eq('installation_id', parseInt(installationId));
    }

    const { data: sessions, error } = await query;

    if (error) {
      console.error('Error fetching design doc sessions:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Transform sessions into feature launch data
    const features = sessions?.map(session => {
      const wizardState = session.wizard_state as any;
      const generatedDoc = wizardState?.generatedDoc;
      const implementationPlan = wizardState?.implementationPlan;
      const rolloutStrategy = wizardState?.rolloutStrategy;
      const taskDetails = wizardState?.taskDetails;
      const decisionPoints = wizardState?.decisionPoints || [];

      // Determine feature status based on what's been generated
      let featureStatus = 'design_doc_generated';
      if (implementationPlan) {
        if (rolloutStrategy) {
          featureStatus = 'rollout_strategy_ready';
        } else {
          featureStatus = 'implementation_plan_ready';
        }
      }

      // Calculate milestone progress if implementation plan exists
      let milestones = { total: 0, completed: 0, inProgress: 0, planned: 0 };
      if (implementationPlan?.enhanced_milestones) {
        milestones.total = implementationPlan.enhanced_milestones.length;
        // For demo purposes, randomly assign some as completed
        // In real implementation, this would track actual milestone completion
        milestones.completed = Math.floor(Math.random() * milestones.total);
        milestones.inProgress = Math.min(1, milestones.total - milestones.completed);
        milestones.planned = milestones.total - milestones.completed - milestones.inProgress;
      }

      // Extract risk level from rollout strategy
      const riskLevel = rolloutStrategy?.executive_summary?.risk_level || 'medium';

      // Determine if security review is required based on doc content
      const securityReviewRequired = 
        generatedDoc?.high_level_design?.security_considerations ||
        generatedDoc?.high_level_design?.data_model_changes !== 'N/A' ||
        riskLevel === 'high' || riskLevel === 'critical';

      // Extract business impact from strategic assessment
      const strategicAssessment = wizardState?.strategicAssessment;
      const businessImpact = strategicAssessment?.business_impact || 'medium';

      // Count user journeys
      const userJourneys = wizardState?.userJourneys?.length || 0;

      return {
        id: session.id,
        title: session.title || taskDetails?.description || 'Untitled Feature',
        repository: session.repository_slug,
        status: featureStatus,
        riskLevel,
        designDocGenerated: generatedDoc ? session.created_at : null,
        implementationPlanGenerated: implementationPlan ? session.updated_at : null,
        rolloutStrategyGenerated: rolloutStrategy ? session.updated_at : null,
        githubIssue: session.github_issue_url,
        milestones,
        lastActivity: session.updated_at,
        owner: '<EMAIL>', // Would come from user_id lookup
        reviewers: [], // Would come from wizard state or separate table
        securityReviewRequired,
        businessImpact,
        userJourneys,
        prsMerged: 0, // Would need to be calculated from PR analysis
        decisionsExtracted: decisionPoints.filter((dp: any) => dp.selectedOption).length,
        flagsRequired: [], // Would be extracted from implementation plan
        
        // Persona-specific data
        personaData: {
          pm: {
            userJourneys,
            businessImpact,
            successMetrics: implementationPlan?.overall_success_metrics?.length || 0
          },
          release: {
            milestones,
            riskLevel,
            rolloutPhases: rolloutStrategy?.rollout_phases?.length || 0
          },
          security: {
            securityReviewRequired,
            riskLevel,
            dataModelChanges: generatedDoc?.high_level_design?.data_model_changes !== 'N/A'
          },
          cto: {
            decisionsExtracted: decisionPoints.filter((dp: any) => dp.selectedOption).length,
            technicalComplexity: milestones.total,
            architecturalChoices: generatedDoc?.high_level_design?.core_architectural_choices?.length || 0
          }
        }
      };
    }) || [];

    // Apply status filter
    const filteredFeatures = status 
      ? features.filter(f => f.status === status)
      : features;

    return NextResponse.json({
      success: true,
      features: filteredFeatures,
      total: filteredFeatures.length,
      metadata: {
        query: {
          repositorySlug,
          installationId,
          status,
          persona
        },
        statusCounts: {
          total: features.length,
          design_doc_generated: features.filter(f => f.status === 'design_doc_generated').length,
          implementation_plan_ready: features.filter(f => f.status === 'implementation_plan_ready').length,
          rollout_strategy_ready: features.filter(f => f.status === 'rollout_strategy_ready').length
        }
      }
    });

  } catch (error) {
    console.error('Error in launch dashboard features API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch features pending launch' },
      { status: 500 }
    );
  }
} 