import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { Octokit } from '@octokit/rest';
import { createAppAuth } from '@octokit/auth-app';
import Anthropic from '@anthropic-ai/sdk';
import { generateIssueTriagePrompt } from '../../../../analyzer/prompt';

// Helper to get environment variables, ensuring they are defined.
function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    console.error(`Missing required environment variable: ${name}`);
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value;
}

// Helper to verify user access to a specific GitHub App installation.
async function verifyUserAccess(userToken: string, targetInstallationId: number): Promise<boolean> {
    try {
        const userOctokit = new Octokit({ auth: userToken });
        const response = await userOctokit.request('GET /user/installations', {
             headers: { 'X-GitHub-Api-Version': '2022-11-28' }
        });
        return response.data.installations.some(inst => inst.id === targetInstallationId);
    } catch (error: any) {
        console.error(`[API Triage Auth] Error verifying access for installation ${targetInstallationId}:`, error.status, error.message);
        return false; 
    }
}

async function getInstallationOctokit(installationId: number): Promise<Octokit> {
    const appId = getRequiredEnvVar('GITHUB_APP_ID');
    const privateKey = getRequiredEnvVar('GITHUB_APP_PRIVATE_KEY').replace(/\\n/g, '\n');

    const appAuth = createAppAuth({
        appId: appId,
        privateKey: privateKey,
    });

    const installationAuth = await appAuth({
        type: 'installation',
        installationId: installationId,
    });

    return new Octokit({ auth: installationAuth.token });
}

export async function POST(request: Request) {
    const { repositorySlug, installationId, issueCount = 50 } = await request.json();

    if (!repositorySlug) {
        return NextResponse.json({ error: 'Missing required parameter: repositorySlug' }, { status: 400 });
    }

    const [owner, repo] = repositorySlug.split('/');
    if (!owner || !repo) {
        return NextResponse.json({ error: 'Invalid repositorySlug format. Expected "owner/repo".' }, { status: 400 });
    }
    
    const supabase = createSupabaseServerClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
        return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    let octokit: Octokit;

    if (installationId) {
        // Private repository flow
        if (!session.provider_token) {
            return NextResponse.json({ error: 'GitHub token not available for private repository access.' }, { status: 403 });
        }
        const isAuthorized = await verifyUserAccess(session.provider_token, installationId);
        if (!isAuthorized) {
            return NextResponse.json({ error: 'Forbidden - User does not have access to this installation.' }, { status: 403 });
        }
        octokit = await getInstallationOctokit(installationId);
    } else {
        // Public repository flow
        octokit = new Octokit(); // No auth for public data
    }

    try {
        console.log(`[API Triage] Fetching issues for ${repositorySlug}...`);
        
        // Fetch issues, excluding any that are already triaged
        const { data: issues } = await octokit.issues.listForRepo({
            owner,
            repo,
            per_page: issueCount,
            state: 'open',
            sort: 'updated',
            direction: 'desc',
            q: `-label:"triage: complete"` // Note: The 'labels' param for this endpoint is a comma-separated string, not an object. The q param is more powerful for exclusion.
        });

        if (!issues || issues.length === 0) {
            return NextResponse.json({ success: true, features: [], message: 'No open, untriaged issues found.' });
        }
        
        const formattedIssues = issues.map(issue => ({
            id: issue.id,
            number: issue.number,
            title: issue.title,
            body: issue.body?.substring(0, 500) + (issue.body && issue.body.length > 500 ? '...' : ''), // Truncate body for prompt
            labels: issue.labels.map(label => typeof label === 'string' ? label : label.name ?? ''),
            url: issue.html_url,
            author: issue.user?.login
        }));

        // === LLM Triage Step ===
        const prompt = generateIssueTriagePrompt(formattedIssues);
        
        const anthropic = new Anthropic({
            apiKey: process.env.ANTHROPIC_API_KEY,
        });

        const message = await anthropic.messages.create({
            model: 'claude-3-haiku-20240307',
            max_tokens: 2048,
            temperature: 0,
            messages: [{ role: 'user', content: prompt }],
        });

        const responseText = message.content[0].type === 'text' ? message.content[0].text : '';
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);

        if (!jsonMatch) {
            console.error('[API Triage] Failed to find JSON in LLM response:', responseText);
            return NextResponse.json({ error: 'Failed to parse AI triage response' }, { status: 500 });
        }

        const parsedResponse = JSON.parse(jsonMatch[0]);
        const triageResults = parsedResponse.triage_results as { id: number; classification: string }[];
        
        const featureRequestIds = new Set(
            triageResults.filter(r => r.classification === 'feature').map(r => r.id)
        );

        const featureRequests = formattedIssues.filter(issue => featureRequestIds.has(issue.id));

        return NextResponse.json({ success: true, feature_requests: featureRequests });

    } catch (error: any) {
        console.error(`[API Triage] Error fetching issues for ${repositorySlug}:`, error);
        let status = 500;
        let message = 'Failed to fetch issues from GitHub.';
        if (error.status) {
            status = error.status;
            message = error.message;
        }
        return NextResponse.json({ error: message }, { status });
    }
} 