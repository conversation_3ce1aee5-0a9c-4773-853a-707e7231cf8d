import { NextResponse } from 'next/server';
import { Octokit } from '@octokit/rest';
import { createAppAuth } from '@octokit/auth-app';
import Anthropic from '@anthropic-ai/sdk';
import { generateTaskAnalysisPrompt } from '../../../../analyzer/prompt';
import { performStrategicAssessment } from '@/lib/assessment';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// NOTE: Some helpers are duplicated from triage/route.ts. In a real PR, these would be moved to a shared lib.
function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) throw new Error(`Missing required environment variable: ${name}`);
  return value;
}

async function getInstallationOctokit(installationId: number): Promise<Octokit> {
    const appId = getRequiredEnvVar('GITHUB_APP_ID');
    const privateKey = getRequiredEnvVar('GITHUB_APP_PRIVATE_KEY').replace(/\\n/g, '\n');
    const appAuth = createAppAuth({ appId, privateKey });
    const installationAuth = await appAuth({ type: 'installation', installationId });
    return new Octokit({ auth: installationAuth.token });
}

// Function to generate TaskAnalysis for a single issue
async function generateAnalysisForIssue(issue: any, anthropic: Anthropic): Promise<any> {
    const taskDetails = { title: issue.title, description: issue.body };
    const prompt = generateTaskAnalysisPrompt(taskDetails);
    
    const message = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 2048,
        temperature: 0,
        messages: [{ role: 'user', content: prompt }],
    });

    const responseText = message.content[0].type === 'text' ? message.content[0].text : '';
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) throw new Error(`Failed to parse TaskAnalysis response for issue #${issue.number}`);
    
    return JSON.parse(jsonMatch[0]);
}

export async function POST(request: Request) {
    const { issues, repositorySlug, installationId, projectConstitution } = await request.json();

    if (!issues || !Array.isArray(issues) || issues.length === 0) {
        return NextResponse.json({ error: 'Missing required parameter: issues' }, { status: 400 });
    }
    if (!repositorySlug) {
        return NextResponse.json({ error: 'Missing required parameter: repositorySlug' }, { status: 400 });
    }
    if (!projectConstitution) {
        return NextResponse.json({ error: 'Missing required parameter: projectConstitution' }, { status: 400 });
    }

    const supabase = createSupabaseServerClient();
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
        return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
    let octokit: Octokit;

    if (installationId) {
        octokit = await getInstallationOctokit(installationId);
    } else {
        octokit = new Octokit();
    }

    try {
        const assessments = await Promise.all(
            issues.map(async (issue) => {
                try {
                    // 1. Fetch full issue body if it was truncated
                    const { data: fullIssue } = await octokit.issues.get({
                        owner: repositorySlug.split('/')[0],
                        repo: repositorySlug.split('/')[1],
                        issue_number: issue.number,
                    });

                    // 2. Generate Task Analysis for the issue
                    const taskAnalysis = await generateAnalysisForIssue(fullIssue, anthropic);

                    // 3. Perform Strategic Assessment
                    const taskDetails = { title: fullIssue.title, description: fullIssue.body };
                    const strategicAssessment = await performStrategicAssessment(
                        taskAnalysis,
                        projectConstitution,
                        taskDetails
                    );

                    return { issueId: issue.id, assessment: strategicAssessment, status: 'success' };

                } catch (error: any) {
                    console.error(`[API Assess] Error processing issue #${issue.number}:`, error);
                    return { issueId: issue.id, error: error.message, status: 'failed' };
                }
            })
        );

        return NextResponse.json({ success: true, assessments });

    } catch (error: any) {
        console.error('[API Assess] General error:', error);
        return NextResponse.json({ error: 'Failed to assess issues.' }, { status: 500 });
    }
} 