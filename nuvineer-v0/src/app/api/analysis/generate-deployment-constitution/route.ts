import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase-server';
import { getInstallationOctokit } from '@/lib/github';
import { Octokit } from '@octokit/rest';
import Anthropic from '@anthropic-ai/sdk';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

// LLM analysis functions
async function analyzeDockerfileWithLLM(dockerfileContent: string) {
  const logPrefix = `[LLM Dockerfile Analysis]`;
  console.log(`${logPrefix} Starting Dockerfile analysis, content length: ${dockerfileContent.length} characters`);
  
  const anthropicApiKey = process.env.ANTHROPIC_API_KEY;
  if (!anthropicApiKey) {
    console.error(`${logPrefix} ANTHROPIC_API_KEY environment variable is not set`);
    throw new Error('ANTHROPIC_API_KEY environment variable is not set');
  }

  const prompt = `Analyze this Dockerfile and extract key deployment information.

RESPONSE FORMAT:
- Return ONLY the JSON object
- NO explanatory text before or after the JSON
- NO markdown code blocks
- NO additional commentary or explanations
- Just the raw JSON object

Return ONLY valid JSON with this exact structure:

{
  "base_image": "string",
  "exposed_ports": ["port1", "port2"],
  "build_args": ["arg1", "arg2"],
  "working_dir": "string"
}

Dockerfile content:
${dockerfileContent}`;

  console.log(`${logPrefix} Sending request to Anthropic API (claude-3-haiku-20240307)`);
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': anthropicApiKey,
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify({
      model: 'claude-3-haiku-20240307',
      max_tokens: 1000,
      messages: [{
        role: 'user',
        content: prompt
      }]
    })
  });

  if (!response.ok) {
    console.error(`${logPrefix} LLM API error: ${response.status} ${response.statusText}`);
    throw new Error(`LLM API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  const content = data.content[0]?.text || '{}';
  console.log(`${logPrefix} Raw LLM response: ${content}`);
  
  try {
    // Extract JSON from the response - look for content between { and }
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    const jsonContent = jsonMatch ? jsonMatch[0] : content;
    
    const parsed = JSON.parse(jsonContent);
    console.log(`${logPrefix} ✅ Successfully parsed LLM response:`, JSON.stringify(parsed, null, 2));
    return parsed;
  } catch (e) {
    console.warn(`${logPrefix} ❌ Failed to parse LLM response, using defaults. Parse error: ${e}`);
    console.warn(`${logPrefix} Raw content that failed to parse: ${content}`);
    const defaults = { base_image: 'unknown', exposed_ports: [], build_args: [], working_dir: '/app' };
    console.log(`${logPrefix} Using default values:`, JSON.stringify(defaults, null, 2));
    return defaults;
  }
}

async function analyzeCiCdWorkflowWithLLM(workflowContent: string) {
  const logPrefix = `[LLM CI/CD Analysis]`;
  console.log(`${logPrefix} Starting CI/CD workflow analysis, content length: ${workflowContent.length} characters`);
  
  const anthropicApiKey = process.env.ANTHROPIC_API_KEY;
  if (!anthropicApiKey) {
    console.error(`${logPrefix} ANTHROPIC_API_KEY environment variable is not set`);
    throw new Error('ANTHROPIC_API_KEY environment variable is not set');
  }

  const prompt = `Analyze this CI/CD workflow file and extract deployment information.

RESPONSE FORMAT:
- Return ONLY the JSON object
- NO explanatory text before or after the JSON
- NO markdown code blocks
- NO additional commentary or explanations
- Just the raw JSON object

Return ONLY valid JSON with this exact structure:

{
  "deployment_steps": ["step1", "step2"],
  "deployed_to": ["environment1", "environment2"],
  "has_tests": true
}

Workflow content:
${workflowContent}`;

  console.log(`${logPrefix} Sending request to Anthropic API (claude-3-haiku-20240307)`);
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': anthropicApiKey,
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify({
      model: 'claude-3-haiku-20240307',
      max_tokens: 1000,
      messages: [{
        role: 'user',
        content: prompt
      }]
    })
  });

  if (!response.ok) {
    console.error(`${logPrefix} LLM API error: ${response.status} ${response.statusText}`);
    throw new Error(`LLM API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  const content = data.content[0]?.text || '{}';
  console.log(`${logPrefix} Raw LLM response: ${content}`);
  
  try {
    // Extract JSON from the response - look for content between { and }
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    const jsonContent = jsonMatch ? jsonMatch[0] : content;
    
    const parsed = JSON.parse(jsonContent);
    console.log(`${logPrefix} ✅ Successfully parsed LLM response:`, JSON.stringify(parsed, null, 2));
    return parsed;
  } catch (e) {
    console.warn(`${logPrefix} ❌ Failed to parse LLM response, using defaults. Parse error: ${e}`);
    console.warn(`${logPrefix} Raw content that failed to parse: ${content}`);
    const defaults = { deployment_steps: [], deployed_to: [], has_tests: false };
    console.log(`${logPrefix} Using default values:`, JSON.stringify(defaults, null, 2));
    return defaults;
  }
}

async function analyzePackageJsonWithLLM(packageJsonContent: string) {
  const logPrefix = `[LLM Package.json Analysis]`;
  console.log(`${logPrefix} Starting package.json analysis, content length: ${packageJsonContent.length} characters`);
  
  const anthropicApiKey = process.env.ANTHROPIC_API_KEY;
  if (!anthropicApiKey) {
    console.warn(`${logPrefix} No Anthropic API key found, skipping LLM analysis`);
    return { provider: null, data_stores: [], hosting_services: [], deployment_tools: [], monitoring_tools: [], serverless_functions: [], storage_services: [], compute_services: [] };
  }

  const prompt = `Analyze this package.json file CONSERVATIVELY for deployment infrastructure. ONLY analyze production dependencies that represent actual infrastructure services.

CRITICAL INSTRUCTIONS:
- IGNORE devDependencies, peerDependencies, and optionalDependencies completely
- IGNORE testing frameworks, build tools, linters, dev servers
- ONLY consider dependencies that represent actual runtime infrastructure
- Be VERY conservative - when in doubt, exclude it
- Focus on explicit cloud service SDKs and runtime dependencies

RESPONSE FORMAT:
- Return ONLY the JSON object
- NO explanatory text before or after the JSON
- NO markdown code blocks
- NO additional commentary or explanations
- Just the raw JSON object

Return ONLY valid JSON with this exact structure:
{
  "provider": "string or null",
  "data_stores": [{"name": "string", "type": "string"}],
  "hosting_services": ["service1", "service2"],
  "deployment_tools": ["tool1", "tool2"],
  "monitoring_tools": ["tool1", "tool2"],
  "serverless_functions": [{"name": "string", "provider": "string"}],
  "storage_services": [{"name": "string", "type": "string"}],
  "compute_services": [{"name": "string", "type": "string"}]
}

ONLY detect if you see EXPLICIT infrastructure packages in PRODUCTION dependencies:
- **Cloud SDKs**: @aws-sdk/*, @google-cloud/*, @azure/*, @supabase/*
- **Database Clients**: pg, mysql2, mongodb, redis, ioredis (NOT testing libraries)
- **Storage Services**: @vercel/blob, @aws-sdk/client-s3, @supabase/storage-js
- **Serverless Runtimes**: @vercel/node, aws-lambda, @google-cloud/functions-framework
- **Monitoring**: @sentry/node, datadog-metrics (NOT test reporters)

EXCLUDE:
- All devDependencies
- Test frameworks (jest, mocha, cypress, etc.)
- Build tools (webpack, vite, rollup, etc.)
- Linters and formatters
- Development servers
- Type definitions (@types/*)
- Mock/stub libraries

Package.json content:
${packageJsonContent}`;

  console.log(`${logPrefix} Sending request to Anthropic API (claude-3-haiku-20240307)`);
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': anthropicApiKey,
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify({
      model: 'claude-3-haiku-20240307',
      max_tokens: 1500,
      messages: [{
        role: 'user',
        content: prompt
      }]
    })
  });

  if (!response.ok) {
    console.error(`${logPrefix} LLM API error: ${response.status} ${response.statusText}`);
    throw new Error(`LLM API error: ${response.status} ${response.statusText}`);
  }

    const data = await response.json();
  const content = data.content[0]?.text || '{}';
  console.log(`${logPrefix} Raw LLM response: ${content}`);
  
  try {
    // Extract JSON from the response - look for content between { and }
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    const jsonContent = jsonMatch ? jsonMatch[0] : content;
    
    const parsed = JSON.parse(jsonContent);
    console.log(`${logPrefix} ✅ Successfully parsed LLM response:`, JSON.stringify(parsed, null, 2));
    return parsed;
  } catch (e) {
    console.warn(`${logPrefix} ❌ Failed to parse LLM response, using defaults. Parse error: ${e}`);
    console.warn(`${logPrefix} Raw content that failed to parse: ${content}`);
    const defaults = { 
      provider: null, 
      data_stores: [], 
      hosting_services: [], 
      deployment_tools: [], 
      monitoring_tools: [], 
      serverless_functions: [], 
      storage_services: [], 
      compute_services: [] 
    };
    console.log(`${logPrefix} Using default values:`, JSON.stringify(defaults, null, 2));
    return defaults;
  }
}

async function analyzeStaticConfigFile(fileContent: string, fileType: string) {
  const logPrefix = `[Static ${fileType} Analysis]`;
  console.log(`${logPrefix} Starting analysis, content length: ${fileContent.length} characters`);

  const baseResult = {
    provider: null as string | null,
    compute: [] as Array<{ type: string }>,
    data_stores: [] as Array<{ name: string; type: string }>,
    containerization: null as any,
    deploymentPipeline: null as any,
    hosting_services: [] as string[],
    deployment_tools: [] as string[],
    monitoring_tools: [] as string[],
    serverless_functions: [] as Array<{ name: string; provider: string }>,
    storage_services: [] as Array<{ name: string; type: string }>,
    compute_services: [] as Array<{ name: string; type: string }>
  };

  try {
    switch (fileType) {
      case 'netlify':
        // Parse netlify.toml
        const netlifyConfig = parseToml(fileContent);
        return {
          ...baseResult,
          provider: 'Netlify',
          compute: [{ type: 'Static Site/Serverless Functions' }],
          deploymentPipeline: {
            deployment_steps: netlifyConfig.build?.command ? [netlifyConfig.build.command] : [],
            deployed_to: ['Netlify CDN'],
            has_tests: !!netlifyConfig.build?.command?.includes('test')
          }
        };

      case 'railway':
        // Parse railway.json
        const railwayConfig = JSON.parse(fileContent);
        return {
          ...baseResult,
          provider: 'Railway',
          compute: [{ type: 'Container' }],
          deploymentPipeline: {
            deployment_steps: railwayConfig.build?.buildCommand ? [railwayConfig.build.buildCommand] : [],
            deployed_to: ['Railway'],
            has_tests: false
          }
        };

      case 'render':
        // Parse render.yaml
        const renderConfig = parseYaml(fileContent);
        const services = renderConfig.services || [];
        return {
          ...baseResult,
          provider: 'Render',
          compute: services.map((s: any) => ({ type: s.type || 'Web Service' })),
          deploymentPipeline: {
            deployment_steps: services.flatMap((s: any) => s.buildCommand ? [s.buildCommand] : []),
            deployed_to: ['Render'],
            has_tests: false
          }
        };

      case 'fly':
        // Parse fly.toml
        const flyConfig = parseToml(fileContent);
        return {
          ...baseResult,
          provider: 'Fly.io',
          compute: [{ type: 'Container' }],
          deploymentPipeline: {
            deployment_steps: flyConfig.build?.buildpacks ? ['buildpack build'] : flyConfig.build?.dockerfile ? ['docker build'] : [],
            deployed_to: ['Fly.io'],
            has_tests: false
          }
        };

      case 'cloudflare-workers':
        // Parse wrangler.toml
        const wranglerConfig = parseToml(fileContent);
        return {
          ...baseResult,
          provider: 'Cloudflare',
          compute: [{ type: 'Serverless Workers' }],
          serverless_functions: [{ name: 'Cloudflare Workers', provider: 'Cloudflare' }],
          deploymentPipeline: {
            deployment_steps: ['wrangler deploy'],
            deployed_to: ['Cloudflare Edge'],
            has_tests: false
          }
        };

      default:
        console.warn(`${logPrefix} Unknown file type: ${fileType}`);
        return baseResult;
    }
  } catch (error) {
    console.error(`${logPrefix} Failed to parse ${fileType} config:`, error);
    return baseResult;
  }
}

// Simple TOML parser for basic use cases
function parseToml(content: string): any {
  const result: any = {};
  const lines = content.split('\n');
  let currentSection = result;
  let currentSectionName = '';

  for (const line of lines) {
    const trimmed = line.trim();
    if (!trimmed || trimmed.startsWith('#')) continue;

    // Section headers
    if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
      currentSectionName = trimmed.slice(1, -1);
      currentSection = result[currentSectionName] = {};
      continue;
    }

    // Key-value pairs
    const equalIndex = trimmed.indexOf('=');
    if (equalIndex > 0) {
      const key = trimmed.slice(0, equalIndex).trim();
      let value = trimmed.slice(equalIndex + 1).trim();
      
      // Remove quotes
      if ((value.startsWith('"') && value.endsWith('"')) || 
          (value.startsWith("'") && value.endsWith("'"))) {
        value = value.slice(1, -1);
      }
      
      currentSection[key] = value;
    }
  }
  
  return result;
}

// Simple YAML parser for basic use cases
function parseYaml(content: string): any {
  try {
    // This is a very basic YAML parser - in production you'd use a proper library
    // For now, just try to parse as JSON if it's simple enough
    const jsonLike = content
      .replace(/:\s*([^"\n\r]+)/g, ': "$1"')
      .replace(/"/g, '"');
    return JSON.parse(`{${jsonLike}}`);
  } catch {
    console.warn('[YAML Parser] Failed to parse YAML, returning empty object');
    return {};
  }
}


async function scanRepositoryForIaC(octokit: Octokit, owner: string, repo: string, sourceRepoUrl?: string) {
  const logPrefix = `[Deployment Constitution] [${owner}/${repo}]`;
  console.log(`${logPrefix} Starting infrastructure analysis`);
  
  const constitution: {
    provider: string | null;
    compute: Array<{ type: string }>;
    data_stores: Array<{ name: string; type: string }>;
    containerization: {
      type: string;
      base_image: string;
      exposed_ports: string[];
      build_args: string[];
      working_dir: string;
    } | null;
    deploymentPipeline: {
      deployment_steps: string[];
      deployed_to: string[];
      has_tests: boolean;
    } | null;
    hosting_services?: string[];
    deployment_tools?: string[];
    monitoring_tools?: string[];
    serverless_functions?: Array<{ name: string; provider: string }>;
    storage_services?: Array<{ name: string; type: string }>;
    compute_services?: Array<{ name: string; type: string }>;
  } = {
    provider: null,
    compute: [],
    data_stores: [],
    containerization: null,
    deploymentPipeline: null,
    hosting_services: [],
    deployment_tools: [],
    monitoring_tools: [],
    serverless_functions: [],
    storage_services: [],
    compute_services: []
  };

  // If a separate infrastructure repo is specified, parse it
  let targetOwner = owner;
  let targetRepo = repo;
  if (sourceRepoUrl) {
    const match = sourceRepoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (match) {
      targetOwner = match[1];
      targetRepo = match[2].replace(/\.git$/, '');
      console.log(`${logPrefix} Using separate infrastructure repo: ${targetOwner}/${targetRepo}`);
    } else {
      console.warn(`${logPrefix} Invalid sourceRepoUrl format: ${sourceRepoUrl}`);
    }
  }

  // Define infrastructure files to scan, prioritized by reliability
  const infrastructureFiles = [
    // HIGH RELIABILITY - Actual deployment configuration
    { path: 'vercel.json', type: 'vercel', analysisType: 'static', priority: 'high' },
    { path: 'netlify.toml', type: 'netlify', analysisType: 'static', priority: 'high' },
    { path: 'railway.json', type: 'railway', analysisType: 'static', priority: 'high' },
    { path: 'render.yaml', type: 'render', analysisType: 'static', priority: 'high' },
    { path: 'fly.toml', type: 'fly', analysisType: 'static', priority: 'high' },
    { path: 'Dockerfile', type: 'dockerfile', analysisType: 'llm', priority: 'high' },
    { path: 'docker-compose.yml', type: 'docker-compose', analysisType: 'llm', priority: 'high' },
    { path: 'docker-compose.yaml', type: 'docker-compose', analysisType: 'llm', priority: 'high' },
    { path: '.github/workflows', type: 'github-actions', analysisType: 'llm', priority: 'high' },
    
    // MEDIUM RELIABILITY - Infrastructure as Code
    { path: 'terraform', type: 'terraform', analysisType: 'llm', priority: 'medium' },
    { path: 'infrastructure', type: 'terraform', analysisType: 'llm', priority: 'medium' },
    { path: 'k8s', type: 'kubernetes', analysisType: 'llm', priority: 'medium' },
    { path: 'kubernetes', type: 'kubernetes', analysisType: 'llm', priority: 'medium' },
    { path: 'helm', type: 'helm', analysisType: 'llm', priority: 'medium' },
    { path: 'serverless.yml', type: 'serverless', analysisType: 'llm', priority: 'medium' },
    { path: 'serverless.yaml', type: 'serverless', analysisType: 'llm', priority: 'medium' },
    { path: 'pulumi', type: 'pulumi', analysisType: 'llm', priority: 'medium' },
    { path: 'aws-sam.yml', type: 'aws-sam', analysisType: 'llm', priority: 'medium' },
    { path: 'template.yml', type: 'aws-sam', analysisType: 'llm', priority: 'medium' },
    { path: 'cloudformation.yml', type: 'cloudformation', analysisType: 'llm', priority: 'medium' },
    { path: 'wrangler.toml', type: 'cloudflare-workers', analysisType: 'static', priority: 'medium' },
    
    // LOW RELIABILITY - Package dependencies (use sparingly)
    { path: 'package.json', type: 'package', analysisType: 'llm', priority: 'low' }
  ];

  console.log(`${logPrefix} Checking ${infrastructureFiles.length} infrastructure file types`);

  for (const file of infrastructureFiles) {
    console.log(`${logPrefix} Checking for ${file.path} (${file.analysisType} analysis)`);
    
    try {
      if (file.type === 'github-actions') {
        console.log(`${logPrefix} Scanning GitHub Actions workflows directory`);
        // Check for workflow files
        const { data: workflowDir } = await octokit.repos.getContent({
          owner: targetOwner,
          repo: targetRepo,
          path: file.path
        });

        if (Array.isArray(workflowDir)) {
          console.log(`${logPrefix} Found ${workflowDir.length} items in .github/workflows`);
          const workflowFiles = workflowDir.filter(item => 
            item.name.endsWith('.yml') || item.name.endsWith('.yaml')
          );
          console.log(`${logPrefix} Found ${workflowFiles.length} workflow files: ${workflowFiles.map(f => f.name).join(', ')}`);

          for (const workflowFile of workflowFiles) {
            console.log(`${logPrefix} Analyzing workflow file: ${workflowFile.name}`);
            const { data: workflowContent } = await octokit.repos.getContent({
              owner: targetOwner,
              repo: targetRepo,
              path: workflowFile.path
            });

            if ('content' in workflowContent) {
              const content = Buffer.from(workflowContent.content, 'base64').toString('utf-8');
              console.log(`${logPrefix} ${workflowFile.name} content length: ${content.length} characters`);
              
              console.log(`${logPrefix} Starting LLM analysis of ${workflowFile.name}`);
              const analysis = await analyzeCiCdWorkflowWithLLM(content);
              console.log(`${logPrefix} LLM analysis result for ${workflowFile.name}:`, JSON.stringify(analysis, null, 2));
              
              constitution.deploymentPipeline = analysis;
              console.log(`${logPrefix} ✅ Successfully analyzed workflow: ${workflowFile.name}`);
              break; // Use the first workflow file found
            }
          }
        } else {
          console.log(`${logPrefix} .github/workflows is not a directory or is empty`);
        }
      } else {
        console.log(`${logPrefix} Fetching ${file.path}`);
        const { data: fileContent } = await octokit.repos.getContent({
          owner: targetOwner,
          repo: targetRepo,
          path: file.path
        });

        if ('content' in fileContent) {
          const content = Buffer.from(fileContent.content, 'base64').toString('utf-8');
          console.log(`${logPrefix} ✅ Found ${file.path}, content length: ${content.length} characters`);

          if (file.type === 'vercel') {
            console.log(`${logPrefix} Starting static analysis of vercel.json`);
            const vercelConfig = JSON.parse(content);
            console.log(`${logPrefix} Vercel config keys: ${Object.keys(vercelConfig).join(', ')}`);
            
            constitution.provider = 'Vercel';
            constitution.compute.push({ type: 'Serverless Function' });
            console.log(`${logPrefix} ✅ Static analysis complete: Provider=Vercel, Compute=Serverless`);
            
          } else if (file.type === 'package') {
            console.log(`${logPrefix} Starting LLM analysis of package.json`);
            const packageAnalysis = await analyzePackageJsonWithLLM(content);
            console.log(`${logPrefix} LLM analysis result for package.json:`, JSON.stringify(packageAnalysis, null, 2));
            
            constitution.provider = packageAnalysis.provider;
            constitution.data_stores = packageAnalysis.data_stores;
            constitution.hosting_services = packageAnalysis.hosting_services;
            constitution.deployment_tools = packageAnalysis.deployment_tools;
            constitution.monitoring_tools = packageAnalysis.monitoring_tools;
            constitution.serverless_functions = packageAnalysis.serverless_functions;
            constitution.storage_services = packageAnalysis.storage_services;
            constitution.compute_services = packageAnalysis.compute_services;
            console.log(`${logPrefix} ✅ LLM analysis complete: Package.json configured`);
            
          } else if (file.type === 'dockerfile') {
            console.log(`${logPrefix} Starting LLM analysis of Dockerfile`);
            const dockerAnalysis = await analyzeDockerfileWithLLM(content);
            console.log(`${logPrefix} LLM analysis result for Dockerfile:`, JSON.stringify(dockerAnalysis, null, 2));
            
            constitution.containerization = {
              type: 'Docker',
              base_image: dockerAnalysis.base_image,
              exposed_ports: dockerAnalysis.exposed_ports,
              build_args: dockerAnalysis.build_args,
              working_dir: dockerAnalysis.working_dir
            };
            console.log(`${logPrefix} ✅ LLM analysis complete: Containerization configured`);
          } else if (file.type === 'netlify' || file.type === 'railway' || file.type === 'render' || file.type === 'fly' || file.type === 'cloudflare-workers') {
            console.log(`${logPrefix} Starting static analysis of ${file.type} file`);
            const staticAnalysis = await analyzeStaticConfigFile(content, file.type);
            console.log(`${logPrefix} Static analysis result for ${file.type}:`, JSON.stringify(staticAnalysis, null, 2));

            if (staticAnalysis.provider) {
              constitution.provider = staticAnalysis.provider;
            }
            if (staticAnalysis.compute) {
              constitution.compute = staticAnalysis.compute;
            }
            if (staticAnalysis.deploymentPipeline) {
              constitution.deploymentPipeline = staticAnalysis.deploymentPipeline;
            }
            if (staticAnalysis.serverless_functions) {
              constitution.serverless_functions = staticAnalysis.serverless_functions;
            }
            if (staticAnalysis.storage_services) {
              constitution.storage_services = staticAnalysis.storage_services;
            }
            if (staticAnalysis.compute_services) {
              constitution.compute_services = staticAnalysis.compute_services;
            }
            console.log(`${logPrefix} ✅ Static analysis complete: ${file.type} configured`);
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log(`${logPrefix} ❌ Could not read ${file.path}: ${errorMessage}`);
      
      // Log additional details for GitHub API errors
      if (error && typeof error === 'object' && 'status' in error) {
        console.log(`${logPrefix} GitHub API error details: Status ${error.status}, URL: ${(error as any).request?.url || 'unknown'}`);
      }
    }
  }

  // Summary of analysis results
  const summary = {
    provider: constitution.provider,
    computeTypes: constitution.compute.length,
    dataStores: constitution.data_stores.length,
    hasContainerization: !!constitution.containerization,
    hasDeploymentPipeline: !!constitution.deploymentPipeline,
    hostingServices: constitution.hosting_services?.length || 0,
    deploymentTools: constitution.deployment_tools?.length || 0,
    monitoringTools: constitution.monitoring_tools?.length || 0,
    serverlessFunctions: constitution.serverless_functions?.length || 0,
    storageServices: constitution.storage_services?.length || 0,
    computeServices: constitution.compute_services?.length || 0
  };
  
  console.log(`${logPrefix} 🎯 Analysis Summary:`, JSON.stringify(summary, null, 2));
  console.log(`${logPrefix} ✅ Infrastructure analysis complete`);

  return constitution;
}

export async function POST(request: Request) {
  const { repositorySlug, isPublic, installationId, infraRepoUrl } = await request.json();

  if (!repositorySlug) {
    return NextResponse.json({ error: 'Repository slug is required.' }, { status: 400 });
  }

  const supabaseAdmin = createSupabaseAdminClient();
  let octokit: Octokit;

  const [owner, repo] = repositorySlug.split('/');
  let analysisOwner = owner;
  let analysisRepo = repo;
  let sourceRepoUrl = `https://github.com/${repositorySlug}`;

  try {
    if (infraRepoUrl) {
      console.log(`[DeploymentConstitution] Using separate infra repo: ${infraRepoUrl}`);
      const url = new URL(infraRepoUrl);
      const pathParts = url.pathname.split('/').filter(p => p);
      if (pathParts.length >= 2) {
        analysisOwner = pathParts[0];
        analysisRepo = pathParts[1];
        sourceRepoUrl = infraRepoUrl;
      } else {
        throw new Error('Invalid infrastructure repository URL format.');
      }
    }
    
    if (isPublic) {
      const githubToken = process.env.GITHUB_TOKEN;
      if (!githubToken) throw new Error('GITHUB_TOKEN is not set for public repo analysis.');
      octokit = new Octokit({ auth: githubToken });
    } else {
      if (!installationId) {
        return NextResponse.json({ error: 'Installation ID is required for private repo.' }, { status: 400 });
      }
      octokit = await getInstallationOctokit(installationId);
    }

    const constitutionData = await scanRepositoryForIaC(octokit, analysisOwner, analysisRepo, sourceRepoUrl);

    const { data, error } = await supabaseAdmin
      .from('deployment_constitutions')
      .upsert({
        repository_slug: repositorySlug,
        installation_id: installationId || 0,
        constitution_data: constitutionData,
        source_repo_url: sourceRepoUrl,
        updated_at: new Date().toISOString(),
      }, { onConflict: 'repository_slug,installation_id' })
      .select()
      .single();

    if (error) {
      console.error('[DeploymentConstitution] Error saving to Supabase:', error);
      throw error;
    }

    return NextResponse.json({ success: true, constitution: data.constitution_data });

  } catch (error: any) {
    console.error('[DeploymentConstitution] API Error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
