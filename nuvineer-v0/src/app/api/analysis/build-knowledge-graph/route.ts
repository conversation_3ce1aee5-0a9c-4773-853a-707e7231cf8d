import { NextRequest, NextResponse } from 'next/server';
import { buildKnowledgeGraphForRepo } from '@/orchestrator';
import { getRepositoryNamespace } from '@/lib/pinecone-utils'; // Assuming this helper exists and works for this context

export const runtime = 'nodejs';
export const fetchCache = 'force-no-store';

/**
 * API route to trigger the retroactive knowledge graph build for a repository.
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { repositorySlug, isPublic, installationId } = body;

    if (!repositorySlug) {
      return NextResponse.json({ success: false, error: 'Repository slug is required' }, { status: 400 });
    }

    // This is a long-running process, so we don't `await` it.
    // We trigger it and immediately return a response to the client.
    // For a real production system, you'd use a more robust job queue (e.g., Vercel KV, BullMQ, etc.)
    await buildKnowledgeGraphForRepo(repositorySlug, installationId)
      .then(() => {
        console.log(`[API /build-knowledge-graph] Successfully completed knowledge graph build for ${repositorySlug}.`);
      })
      .catch((error) => {
        console.error(`[API /build-knowledge-graph] Background job failed for ${repositorySlug}:`, error);
      });

    const message = `Knowledge graph build process started for ${repositorySlug}. This may take some time.`;
    console.log(`[API /build-knowledge-graph] ${message}`);
    
    return NextResponse.json({ success: true, message });

  } catch (error: any) {
    console.error('[API /build-knowledge-graph] Error:', error);
    return NextResponse.json({ success: false, error: error.message || 'An unknown error occurred' }, { status: 500 });
  }
} 