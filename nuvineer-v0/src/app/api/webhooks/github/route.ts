import { NextResponse } from 'next/server';
import { Octokit } from '@octokit/rest';
import { createTokenAuth } from '@octokit/auth-token';
import { createAppAuth } from '@octokit/auth-app';
import { verify } from '@octokit/webhooks-methods'; // For signature verification
import { processMergedPR } from '@/orchestrator'; // Use alias path
import { 
    fetchAndFormatPRData, 
    PrContext, 
    CodeChange, 
    FormattedComment, 
} from '@/lib/github'; // Import shared function and types
import { createClient } from '@supabase/supabase-js'; // <-- ADDED

// Define interfaces for expected payload parts
interface Installation {
  id: number;
  // ... other fields if needed
}

interface Repository {
  name: string;
  owner: { login: string };
  full_name: string; // Added to include the full repository name (e.g., "owner/repo")
  // ... other fields if needed
}

interface PullRequest {
  number: number;
  merged: boolean;
  merged_at: string | null; // <-- <PERSON><PERSON><PERSON> merged_at
  html_url: string; // <-- ADDED html_url
  title: string; // <-- ADDED title
  // ... other fields if needed
  head?: {
    sha: string;
  };
  user?: { // User who initiated the PR
    login: string;
  };
}

// Added interfaces for Push Event
interface CommitInPush {
  id: string; // SHA of the commit
  message: string;
  timestamp: string; // ISO 8601 format
  url: string;
  author: {
    name?: string;
    email?: string;
    username?: string;
  };
  committer: {
    name?: string;
    email?: string;
    username?: string;
  };
  added?: string[];
  removed?: string[];
  modified?: string[];
  distinct: boolean; // True if this commit is new to this push, false if it was already on the ref.
  parents: { sha: string }[]; // Array of parent commit SHAs. More than 1 indicates a merge commit.
}

interface WebhookPayload {
  action?: string;
  installation?: Installation;
  repository?: Repository;
  pull_request?: PullRequest;
  // For push events
  ref?: string; // e.g., "refs/heads/main"
  before?: string; // SHA before push
  after?: string; // SHA after push
  commits?: CommitInPush[];
  pusher?: {
    name?: string;
    email?: string;
  };
  head_commit?: CommitInPush; // The most recent commit on the ref after the push.
}

// Helper function to get environment variables
function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    console.error(`Missing required environment variable: ${name}`);
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value;
}

// <-- ADDED: Supabase Client Initialization -->
let supabaseClient: import('@supabase/supabase-js').SupabaseClient | null = null;
try {
    const supabaseUrl = getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_URL');
    const supabaseServiceRoleKey = getRequiredEnvVar('SUPABASE_SERVICE_ROLE_KEY');
    if (supabaseUrl && supabaseServiceRoleKey) {
        supabaseClient = createClient(supabaseUrl, supabaseServiceRoleKey, {
            auth: {
                persistSession: false // Service role key doesn't need session persistence
            }
        });
        console.log("[Webhook/Supabase] Client initialized.");
    } else {
        console.error("[Webhook/Supabase] URL or Service Role Key missing. Cannot connect to Supabase.");
    }
} catch (error: any) {
     console.error("[Webhook/Supabase] Error initializing Supabase client:", error.message || error);
     supabaseClient = null; // Ensure it's null on error
}
// <-- END ADDED -->

// Main handler for POST requests
export async function POST(request: Request) {
  // Log that we received a webhook event, with event name and action if available
  const eventName = request.headers.get('x-github-event') ?? '';
  let action = '';
  try {
    const rawPayload = await request.clone().text();
    const payload = JSON.parse(rawPayload);
    action = payload.action || '';
    console.log(`[Webhook] Event received: event='${eventName}', action='${action}'`);
  } catch (e) {
    // If parsing fails, still log the event name
    console.log(`[Webhook] Event received: event='${eventName}', action=unavailable (payload parse error)`);
  }

  const appId = getRequiredEnvVar('GITHUB_APP_ID');
  const privateKey = getRequiredEnvVar('GITHUB_APP_PRIVATE_KEY').replace(/\n/g, '\n'); // Ensure newlines are correct
  const webhookSecret = getRequiredEnvVar('GITHUB_WEBHOOK_SECRET');

  // 1. Verify Webhook Signature
  const signature = request.headers.get('x-hub-signature-256') ?? '';
  let payload: WebhookPayload;
  let rawPayload: string;

  try {
    // IMPORTANT: Need raw body for verification. Clone the request to read it.
    // This approach might vary depending on the Next.js version and edge runtime limitations.
    // Consider using Vercel's instructions for raw body access if deployed there.
    rawPayload = await request.clone().text(); 
    payload = JSON.parse(rawPayload);
    console.log('[Webhook] Payload parsed');

    const isVerified = await verify(webhookSecret, rawPayload, signature);
    if (!isVerified) {
      console.warn('[Webhook] Signature verification failed!');
      return NextResponse.json({ error: 'Signature verification failed' }, { status: 401 });
    }
    console.log('[Webhook] Signature verified successfully');

  } catch (error: unknown) { // Type as unknown
    console.error('[Webhook] Error parsing payload or verifying signature:', error);
    // Check type before accessing message
    const message = error instanceof Error ? error.message : 'Unknown error during parsing/verification';
    return NextResponse.json({ error: 'Invalid payload or signature', details: message }, { status: 400 });
  }

  // 2. Check if it's a relevant PR event
  if (
    eventName === 'pull_request' &&
    payload.action === 'closed' &&
    payload.pull_request?.merged === true &&
    payload.pull_request?.merged_at && // Ensure merged_at is present
    payload.installation?.id &&
    payload.repository?.owner?.login &&
    payload.repository?.name &&
    payload.pull_request?.number &&
    payload.pull_request?.html_url &&
    payload.pull_request?.title
  ) {
    const installationId = payload.installation.id;
    const owner = payload.repository.owner.login;
    const repo = payload.repository.name;
    const prNumber = payload.pull_request.number;
    const mergedAt = payload.pull_request.merged_at; // Get merge timestamp
    const prUrl = payload.pull_request.html_url; // Get PR URL
    const prTitle = payload.pull_request.title; // Get PR Title
    // Construct the repository slug from owner and repo
    const installationRepositorySlug = `${owner}/${repo}`;
    const pineconeNamespace = `installation-${installationId}-repo-${installationRepositorySlug.replace('/', '-')}`; // Update namespace to use combined slug

    console.log(`[Webhook] Processing merged PR event: ${installationRepositorySlug}#${prNumber}, Installation ID: ${installationId}, Namespace: ${pineconeNamespace}`);

    // ---> MODIFIED: Insert into Supabase instead of processing directly <---
    if (!supabaseClient) {
        console.error(`[Webhook] Supabase client not available for PR ${installationRepositorySlug}#${prNumber}. Cannot queue for analysis.`);
        return NextResponse.json({ error: 'Internal Server Error: Supabase connection failed' }, { status: 500 });
    }

    try {
      console.log(`[Webhook] Attempting to insert PR #${prNumber} into repository_pr_analysis_status for ${installationRepositorySlug}`);

      const { data, error } = await supabaseClient
        .from('repository_pr_analysis_status')
        .insert({
          repository_slug: installationRepositorySlug,
          pr_number: prNumber,
          installation_id: installationId,
          pr_merged_at: mergedAt, // Use the actual merged timestamp
          status: 'pending', // Set initial status
          pr_url: prUrl, // Store PR URL
          pr_title: prTitle, // Store PR Title
          is_pseudo_pr: false, // Explicitly mark as not a pseudo PR
          commit_sha: payload.pull_request?.head?.sha || null, // Store PR head SHA, or null
          // last_attempted_at can be null initially
          // error_message can be null initially
        })
        .select(); // Optionally select to confirm insertion

      if (error) {
        console.error(`[Webhook] Supabase insert error for PR #${prNumber} in ${installationRepositorySlug}:`, error);
        // Check for unique constraint violation (e.g., PR already exists)
        if (error.code === '23505') { // PostgreSQL unique violation code
           console.warn(`[Webhook] PR #${prNumber} for ${installationRepositorySlug} already exists in the queue. Ignoring duplicate webhook event.`);
           // Return success to GitHub to avoid retries for duplicates
           return NextResponse.json({ success: true, message: 'Duplicate PR event ignored.' }); 
        }
        throw error; // Re-throw other errors
      }

      console.log(`[Webhook] Successfully queued PR #${prNumber} for analysis in ${installationRepositorySlug}. Inserted data:`, data);

      // 7. Return success, GitHub doesn't need the analysis result here
      return NextResponse.json({ success: true, message: 'Webhook processed successfully, PR queued for analysis.' });

    } catch (error: unknown) { // Type as unknown
      console.error(`[Webhook] Error queueing PR ${installationRepositorySlug}#${prNumber} for namespace ${pineconeNamespace}:`, error);
      // Check type before accessing message
      const message = error instanceof Error ? error.message : 'Unknown processing error';
      return NextResponse.json({ error: 'Failed to queue PR for analysis', details: message }, { status: 500 });
    }
    // ---> END MODIFICATION <---

  } else if (eventName === 'push' && payload.commits && payload.installation?.id && payload.repository?.full_name) {
    const installationId = payload.installation.id;
    const repositorySlug = payload.repository.full_name;
    const pusherUsername = payload.pusher?.name || payload.head_commit?.author?.username || 'unknown_pusher';

    const targetBranchRef = `refs/heads/${process.env.MAIN_BRANCH_NAME || 'main'}`;
    console.log(`[Webhook] Received push event for ref: ${payload.ref}. Target ref: ${targetBranchRef}`);

    if (payload.ref !== targetBranchRef) {
      console.log(`[Webhook] Push event is not for the target branch (${targetBranchRef}). Ignoring.`);
      return NextResponse.json({ success: true, message: 'Push event not for target branch' });
    }

    if (!supabaseClient) {
      console.error(`[Webhook] Supabase client not available for push event processing for ${repositorySlug}. Cannot queue for analysis.`);
      return NextResponse.json({ error: 'Internal Server Error: Supabase connection failed' }, { status: 500 });
    }

    console.log(`[Webhook] Processing push event for ${repositorySlug}, ref: ${payload.ref}, installationId: ${installationId}`);

    for (const commit of payload.commits) {
      if (!commit.distinct) {
        console.log(`[Webhook] Commit ${commit.id.substring(0,7)} (SHA: ${commit.id}) is not distinct. Skipping.`);
        continue;
      }

      const commitSha = commit.id;
      const commitMessage = commit.message;
      const commitTimestamp = commit.timestamp;
      const commitUrl = commit.url;
      const commitAuthorUsername = commit.author?.username || pusherUsername;

      console.log(`[Webhook] Processing commit ${commitSha.substring(0, 7)} (SHA: ${commit.id}) from push: "${commitMessage.split('\n')[0]}"`);

      // Attempt to extract PR number from commit message
      const prMatch = commitMessage.match(/Merge pull request #(\d+)/);
      if (prMatch && prMatch[1]) {
        const prNumber = parseInt(prMatch[1], 10);
        console.log(`[Webhook] Commit ${commitSha.substring(0, 7)} (SHA: ${commit.id}) from push event is associated with PR #${prNumber}. This will be handled by the 'pull_request.closed' event. Skipping from push event processing.`);
        continue; // Skip this commit, let the PR event handle it
      }

      // If we reach here, the commit is distinct and NOT a standard PR merge commit.
      // This means it could be a direct commit, a squash merge, or a branch merge without a standard PR message.
      // We will process it as a pseudo-PR.
      console.log(`[Webhook] Commit ${commitSha.substring(0, 7)} (SHA: ${commit.id}) is not a standard PR merge. Processing as pseudo-PR.`);
      const pseudoPrNumber = `commit_${commitSha.substring(0, 8)}_${new Date(commitTimestamp).getTime()}`;
      const pseudoPrTitle = commitMessage.split('\n')[0];

      try {
        // Check if this commit (as a pseudo PR) already exists by commit_sha
        const { data: existingPseudoPr, error: dbCheckError } = await supabaseClient
          .from('repository_pr_analysis_status')
          .select('commit_sha')
          .eq('repository_slug', repositorySlug)
          .eq('commit_sha', commitSha)
          .eq('installation_id', installationId)
          // .eq('is_pseudo_pr', true) // Optional: be more specific if commit_sha could be on non-pseudo PRs
          .maybeSingle();

        if (dbCheckError) {
          console.error(`[Webhook] Supabase error checking for existing pseudo PR (commit ${commitSha.substring(0,7)}):`, dbCheckError);
          continue; // Skip this commit on error
        }

        if (existingPseudoPr) {
          console.log(`[Webhook] Pseudo PR for commit ${commitSha.substring(0, 7)} already exists. Skipping duplicate.`);
          continue;
        }

        // Insert as new pseudo PR
        console.log(`[Webhook] Attempting to insert pseudo PR for commit ${commitSha.substring(0, 7)} for ${repositorySlug}`);
        const { error: insertError } = await supabaseClient
          .from('repository_pr_analysis_status')
          .insert({
            repository_slug: repositorySlug,
            pr_number: pseudoPrNumber, // Using the generated pseudo PR number
            installation_id: installationId,
            pr_merged_at: commitTimestamp,
            status: 'pending',
            pr_url: commitUrl,
            pr_title: pseudoPrTitle,
            is_pseudo_pr: true,
            commit_sha: commitSha, // Store the actual commit SHA
            // author_username: commitAuthorUsername, // if you add this column
          });

        if (insertError) {
          console.error(`[Webhook] Supabase insert error for pseudo PR (commit ${commitSha.substring(0,7)}):`, insertError);
          if (insertError.code === '23505') { // Unique constraint violation
             console.warn(`[Webhook] Pseudo PR for commit ${commitSha.substring(0,7)} likely already inserted (unique constraint). Ignoring duplicate.`);
          }
        } else {
          console.log(`[Webhook] Successfully queued pseudo PR for commit ${commitSha.substring(0, 7)} for analysis.`);
        }
      } catch (e) {
        console.error(`[Webhook] Error processing pseudo PR for commit ${commitSha.substring(0,7)}:`, e);
      }
    }
    return NextResponse.json({ success: true, message: 'Push event processed.' });

  } else {
    // Add more detail to the ignore log
    let reason = `Event: ${eventName}`;
    if (eventName === 'pull_request') {
        reason += `, Action: ${payload.action}, Merged: ${payload.pull_request?.merged}, Installation: ${payload.installation?.id ? 'present' : 'missing'}, Repo: ${payload.repository?.name ? 'present' : 'missing'}, PR #: ${payload.pull_request?.number ? 'present' : 'missing'}`;
    } else if (eventName === 'push') {
        reason += `, Ref: ${payload.ref}`;
    }
    console.log(`[Webhook] Ignoring event. Reason: ${reason}`);
    return NextResponse.json({ success: true, message: 'Event received but not relevant for queuing' });
  }
}

// Optional: Add config to disable body parsing if needed, 
// especially for Vercel deployment. Check Vercel docs for webhooks.
// export const config = {
//   api: {
//     bodyParser: false,
//   },
// }; 