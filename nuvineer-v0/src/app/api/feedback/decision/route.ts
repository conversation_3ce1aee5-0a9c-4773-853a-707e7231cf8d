import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { CookieOptions } from '@supabase/ssr'; // Import CookieOptions type

export const dynamic = 'force-dynamic'; // Ensure dynamic execution

export async function POST(request: Request) {
  const cookieStore = cookies(); // Get the cookie store instance

  // Create the Supabase client using createServerClient
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options });
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value: '', ...options });
          } catch (error) {
            // The `delete` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );

  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let payload;
  try {
    payload = await request.json();
  } catch (error) {
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  const { decisionId, reaction, comment, repo } = payload;

  // Basic validation
  if (!decisionId) {
    return NextResponse.json({ error: 'Missing decisionId' }, { status: 400 });
  }
  if (!reaction && (!comment || typeof comment !== 'string' || !comment.trim())) {
    return NextResponse.json({ error: 'Feedback requires at least a reaction or a non-empty comment' }, { status: 400 });
  }
  // Optional: Add more specific validation for reaction emojis if needed

  try {
    const { error: dbError } = await supabase
      .from('decision_feedback')
      .insert({
        decision_id: decisionId,
        reaction: reaction || null, // Ensure null if empty/undefined
        comment: comment?.trim() || null, // Trim and ensure null if empty
        repository_slug: repo || null,
        user_id: session.user.id, // Associate with the logged-in user
      });

    if (dbError) {
      console.error('Supabase error inserting decision feedback:', dbError);
      return NextResponse.json({ error: 'Database error', details: dbError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Feedback submitted successfully.' });

  } catch (error) {
    console.error('Unexpected error handling decision feedback:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 