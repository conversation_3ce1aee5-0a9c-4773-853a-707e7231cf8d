/**
 * PRAGMA<PERSON>C MILESTONE SECURITY REVIEW FRAMEWORK
 * 
 * This system prevents "security scope creep" by implementing milestone-appropriate security boundaries:
 * 
 * SECURITY POSTURE LEVELS:
 * 
 * 🔒 BASIC (UI/Frontend milestones):
 * - Only flag user-facing security issues (XSS in content, malicious redirects)
 * - Skip advanced attack vectors and backend security concerns
 * 
 * 🔒 STANDARD (General backend/API milestones):
 * - Focus on direct, exploitable vulnerabilities 
 * - Must be fixable within milestone timeline
 * - No theoretical attack chains or sophisticated exploits
 * 
 * 🔒 ENHANCED (Security-focused milestones):
 * - Apply stricter security criteria for auth/payment/sensitive data features
 * - Include defense-in-depth measures appropriate to the security context
 * 
 * SECURITY BOUNDARIES:
 * ✅ FLAG: Directly exploitable, immediate impact, milestone-scoped fixes
 * ❌ SKIP: Advanced attack vectors, theoretical vulnerabilities, architectural overhauls
 * 
 * This prevents endless security deep-dives while maintaining appropriate protection.
 */

import { NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';

export const dynamic = 'force-dynamic';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!,
});

interface MilestoneContext {
  milestone_id: string;
  title: string;
  description: string;
  planned_deliverables: string[];
  verification_criteria: string[];
  priority: string;
}

interface CodeChange {
  filename: string;
  patch: string;
  additions?: number;
  deletions?: number;
}

function determineMilestoneSecurityPosture(milestoneContext: MilestoneContext, codeChanges: CodeChange[]): {
  securityLevel: 'BASIC' | 'STANDARD' | 'ENHANCED';
  rationale: string;
  additionalGuidance: string;
} {
  const title = milestoneContext.title.toLowerCase();
  const description = milestoneContext.description.toLowerCase();
  const deliverables = milestoneContext.planned_deliverables.join(' ').toLowerCase();
  
  // Check if this is a security-focused milestone
  const securityKeywords = ['auth', 'security', 'login', 'password', 'encrypt', 'permission', 'access', 'oauth', 'jwt', 'token'];
  const hasSecurityFocus = securityKeywords.some(keyword => 
    title.includes(keyword) || description.includes(keyword) || deliverables.includes(keyword)
  );
  
  // Check if this touches sensitive data or APIs
  const sensitiveKeywords = ['payment', 'billing', 'user data', 'personal', 'admin', 'database', 'api key', 'secret'];
  const touchesSensitiveData = sensitiveKeywords.some(keyword => 
    title.includes(keyword) || description.includes(keyword) || deliverables.includes(keyword)
  );
  
  // Check code changes for security-sensitive areas
  const touchesSecurityCode = codeChanges.some(change => {
    const filename = change.filename.toLowerCase();
    const patch = change.patch.toLowerCase();
    return filename.includes('auth') || filename.includes('security') || 
           patch.includes('password') || patch.includes('token') || patch.includes('secret');
  });
  
  // Check if this is a frontend/UI-only milestone
  const uiKeywords = ['ui', 'frontend', 'component', 'style', 'design', 'layout', 'visual'];
  const isUIFocused = uiKeywords.some(keyword => 
    title.includes(keyword) || description.includes(keyword)
  ) && !hasSecurityFocus && !touchesSensitiveData;
  
  // Check if this is a backend/API milestone
  const backendKeywords = ['api', 'backend', 'server', 'database', 'endpoint'];
  const isBackendFocused = backendKeywords.some(keyword => 
    title.includes(keyword) || description.includes(keyword) || deliverables.includes(keyword)
  );

  if (hasSecurityFocus || touchesSensitiveData || touchesSecurityCode) {
    return {
      securityLevel: 'ENHANCED',
      rationale: 'Milestone involves security-sensitive functionality, user data, or authentication systems.',
      additionalGuidance: 'Apply stricter security criteria. Flag any potential vulnerabilities that could compromise user data or system security.'
    };
  } else if (isBackendFocused || milestoneContext.priority === 'High') {
    return {
      securityLevel: 'STANDARD', 
      rationale: 'Milestone involves backend systems or is high priority, requiring standard security diligence.',
      additionalGuidance: 'Focus on direct, exploitable vulnerabilities that could impact system integrity or user experience.'
    };
  } else if (isUIFocused) {
    return {
      securityLevel: 'BASIC',
      rationale: 'Milestone is primarily UI/frontend focused with limited security implications.',
      additionalGuidance: 'Only flag security issues that directly affect user interaction (XSS in user content, malicious redirects, etc.).'
    };
  } else {
    return {
      securityLevel: 'STANDARD',
      rationale: 'General milestone requiring standard security review.',
      additionalGuidance: 'Apply milestone-appropriate security criteria focusing on direct, exploitable issues.'
    };
  }
}

function generateRiskAnalysisPrompt(
  codeChanges: CodeChange[],
  milestoneContext: MilestoneContext,
  futureMilestones: { title: string; description: string }[],
  reviewMode?: string,
  previouslyAcceptedRisks?: string[]
): string {
  const formattedChanges = codeChanges.map(change => `
**${change.filename}**
${change.patch}
  `).join('\n');

  // Determine milestone-appropriate security posture
  const securityPosture = determineMilestoneSecurityPosture(milestoneContext, codeChanges);

  const futureMilestonesContext = futureMilestones && futureMilestones.length > 0 ? `

**CONTEXT ON FUTURE MILESTONES:**
To avoid flagging issues that will be addressed in upcoming work, here is a summary of planned future milestones. Do not report risks if they are explicitly covered by the description of a future milestone.
${futureMilestones.map(m => `
- **${m.title}:** ${m.description}`).join('')}
` : '';

  const reviewModeContext = reviewMode === 'FOCUSED' ? `

**🚨 FOCUSED REVIEW MODE - SUBSEQUENT REVIEW 🚨**
This is a follow-up review of code that has already been analyzed. Your analysis MUST be more conservative:

1. **ONLY flag NEW critical risks** that weren't present in the previous review
2. **FOCUS on changes since the last review** - don't re-analyze unchanged code
3. **RAISE THE BAR** - only flag issues that are genuinely blocking and severe
4. **AVOID nitpicking** - minor improvements and best practices should not be flagged
5. **Consider review fatigue** - be respectful of developer time and focus on must-fix issues only

**EVIDENCE REQUIRED:** For each risk you identify, you MUST provide clear evidence that this is:
- A NEW issue (not previously identified and addressed)
- SEVERE enough to justify blocking integration
- ACTIONABLE with a specific fix` : `

**COMPREHENSIVE INITIAL REVIEW**
This is the first review of this milestone. Conduct a thorough analysis for critical risks.`;

  const previousRisksContext = previouslyAcceptedRisks && previouslyAcceptedRisks.length > 0 ? `

**PREVIOUSLY ACCEPTED RISKS - DO NOT RE-FLAG:**
The following risks have already been identified and explicitly accepted by the development team. DO NOT flag these again:
${previouslyAcceptedRisks.map(risk => `- ${risk}`).join('\n')}
` : '';

  const securityPostureContext = `

**🔒 MILESTONE SECURITY POSTURE: ${securityPosture.securityLevel}**
${securityPosture.rationale}

**Security Analysis Guidance for this Milestone:**
${securityPosture.additionalGuidance}`;

  const severityGuidance = reviewMode === 'FOCUSED' ? `
**STRICTER SEVERITY CRITERIA FOR FOCUSED REVIEW:**
Only report risks that meet ALL of these criteria:
- **Immediately exploitable** security vulnerabilities (not theoretical ones)
- **Causes application crashes or data corruption** performance issues 
- **Makes core functionality completely unusable** UX problems
- **Newly introduced** since the last review
- **Has clear, actionable fix** that can be implemented quickly` : `
**PRAGMATIC SECURITY BOUNDARIES - MILESTONE-APPROPRIATE:**
Focus on security issues that are:
- **DIRECTLY EXPLOITABLE** in the current milestone scope (not theoretical attack chains)
- **HIGH PROBABILITY** of occurrence with the current implementation
- **IMMEDIATE IMPACT** on users or data integrity
- **FIXABLE** within milestone timeline without architectural overhaul

**DO NOT FLAG AS CRITICAL:**
- Advanced attack vectors requiring multiple exploit steps
- Theoretical vulnerabilities that require attacker to already have significant system access
- Security improvements that would require major architectural changes
- Defense-in-depth measures beyond the milestone's intended security posture
- Edge cases that would only apply to sophisticated attackers`;

  return `You are an expert software architect with a specialization in security and performance auditing. Your task is to analyze code changes for a specific milestone and identify only the most critical, blocking risks that would prevent safe integration.

**MILESTONE CONTEXT - ${milestoneContext.milestone_id}: ${milestoneContext.title}**
Description: ${milestoneContext.description}
${futureMilestonesContext}${reviewModeContext}${previousRisksContext}${securityPostureContext}

**CODE CHANGES FOR ANALYSIS:**
${formattedChanges}

**YOUR TASK: CRITICAL RISK IDENTIFICATION**
Analyze the provided code changes to identify critical risks. Do not evaluate scope compliance or question the design. Focus ONLY on implementation risks that are severe enough to block integration.

${severityGuidance}

**SECURITY ANALYSIS BOUNDARIES:**
For security risks, ONLY flag issues that meet ALL of these milestone-appropriate criteria:

1. **DIRECT EXPLOITABILITY**: The vulnerability can be exploited directly through the code changes in this milestone
2. **REALISTIC ATTACK VECTOR**: The attack requires only commonly available tools/knowledge, not sophisticated expertise
3. **IMMEDIATE CONSEQUENCE**: Successful exploitation leads to immediate data breach, system compromise, or user impact
4. **MILESTONE-SCOPED**: The fix can be implemented within the current milestone without major architectural changes

**CRITICAL SECURITY RISK EXAMPLES (MILESTONE-APPROPRIATE):**
✅ **FLAG THESE:**
- SQL injection in user input fields with direct database queries
- XSS vulnerabilities in user-generated content display
- Hardcoded secrets/credentials in source code
- Authentication bypass in login flows
- Direct file system access without path validation
- Unvalidated redirects to user-controlled URLs

❌ **DO NOT FLAG THESE:**
- Missing rate limiting (unless milestone specifically adds API endpoints)
- Lack of advanced attack detection (unless milestone is security-focused)
- Theoretical timing attack vulnerabilities
- Missing security headers (unless milestone touches HTTP responses)
- Advanced cryptographic attacks requiring specialized knowledge
- Missing audit logging (unless milestone deals with sensitive operations)

**PERFORMANCE & UX RISK CATEGORIES:**
- **Performance:** Obvious N+1 query patterns, inefficient loops over large datasets, blocking synchronous operations on main threads, memory leaks that would impact users immediately
- **UX:** Broken or non-functional UI elements, workflows that lead to data loss, actions with no feedback, navigation that prevents users from completing core tasks

**OUTPUT FORMAT:**
You MUST respond with ONLY a valid JSON object containing the identified risks. Do not include any text before or after the JSON.

{
  "critical_security_risks": [
    {
      "risk": "Specific security vulnerability description.",
      "severity": "CRITICAL",
      "impact": "Concrete impact on the system or users if exploited.",
      "file": "The file where the vulnerability exists.",
      "fix": "A specific, actionable recommendation to resolve the vulnerability."
    }
  ],
  "critical_performance_risks": [
    {
      "risk": "Specific performance issue description.",
      "severity": "CRITICAL", 
      "impact": "How this issue degrades user experience or system stability.",
      "file": "The file where the performance issue exists.",
      "fix": "A specific, actionable recommendation to resolve the issue."
    }
  ],
  "critical_ux_risks": [
    {
      "risk": "Specific UX failure description.",
      "severity": "CRITICAL",
      "impact": "Why this failure makes the feature unusable or leads to errors.",
      "file": "The file where the UX issue is apparent.", 
      "fix": "A specific, actionable recommendation to improve the UX."
    }
  ]
}

If no critical risks are found in any category, return an empty array for that category.
`;
}

function generateScopeCompliancePrompt(
  codeChanges: CodeChange[],
  milestoneContext: MilestoneContext,
  existingContext: string,
  detailedTasks?: any[],
  reviewMode?: string,
  previouslyAcceptedRisks?: string[]
): string {
  const totalLinesChanged = codeChanges.reduce((sum, change) => 
    sum + (change.additions || 0) + (change.deletions || 0), 0
  );

  const formattedChanges = codeChanges.map(change => `
**${change.filename}**
${change.patch}
  `).join('\n');

  const detailedTasksSection = detailedTasks && detailedTasks.length > 0 ? `

**INTENDED TASKS FOR THIS MILESTONE:**
${detailedTasks.map((task, i) => `${i + 1}. ${typeof task === 'string' ? task : task}`).join('\n')}
` : '';

  const reviewModeContext = reviewMode === 'FOCUSED' ? `

**🔍 FOCUSED SCOPE REVIEW - SUBSEQUENT REVIEW**
This milestone has been reviewed before. Your scope assessment should be:

1. **MORE LENIENT** - Accept reasonable implementation variations and minor deviations
2. **FOCUS ON MAJOR ISSUES** - Only flag significant scope violations that truly block milestone goals
3. **CONSIDER PROGRESS** - Acknowledge that iterative development may result in minor scope adjustments
4. **AVOID SCOPE CREEP** - Don't add new requirements or expect additional features beyond planned deliverables
5. **Be DEVELOPER-FRIENDLY** - Recognize good faith efforts to implement the milestone

**HIGHER THRESHOLD:** Only mark scope as MAJOR_DEVIATION if there are truly fundamental misalignments with milestone goals.` : `

**COMPREHENSIVE INITIAL SCOPE REVIEW**
This is the first scope assessment for this milestone. Conduct a thorough analysis of deliverable completion.`;

  const existingContextSection = existingContext ? `

**EXISTING REVIEW CONTEXT:**
Previous feedback and context for this milestone:
${existingContext}

Take this context into account and focus on NEW issues, not re-evaluating previously discussed items.` : '';

  const scopeToleranceGuidance = reviewMode === 'FOCUSED' ? `
**INCREASED SCOPE TOLERANCE FOR SUBSEQUENT REVIEWS:**
- Accept **reasonable implementation variations** (different approaches achieving same goals)
- Only flag **major missing deliverables** (not minor feature variations)
- Consider **iterative development patterns** (some deliverables may be partially complete)
- Focus on **functional completeness** rather than implementation perfection
- Recognize **good faith implementation efforts** even if not exactly as originally planned` : `
**STANDARD SCOPE CRITERIA:**
Evaluate alignment with planned deliverables and milestone goals.`;

  return `You are an expert software architect performing milestone validation. Your task is to analyze the actual code changes and assess whether they align with the intended milestone scope. Do NOT analyze for risks, security, or performance issues. Your focus is exclusively on scope compliance.

**MILESTONE REFERENCE - ${milestoneContext.milestone_id}: ${milestoneContext.title}**
Priority: ${milestoneContext.priority}
Description: ${milestoneContext.description}

**INTENDED DELIVERABLES FOR THIS MILESTONE:**
${milestoneContext.planned_deliverables.map((d, i) => `${i + 1}. ${d}`).join('\n')}

**INTENDED VERIFICATION CRITERIA:**
${milestoneContext.verification_criteria.map((c, i) => `${i + 1}. ${c}`).join('\n')}${detailedTasksSection}${reviewModeContext}${existingContextSection}

**ACTUAL CODE CHANGES SUBMITTED (${totalLinesChanged} lines):**
${formattedChanges}

**YOUR ASSESSMENT TASK:**
Based on the actual code changes above, determine:

1.  **SCOPE COMPLIANCE**: Do the submitted changes align with the intended milestone deliverables?
    -   Are the planned deliverables actually implemented?
    -   Are there major deviations from the intended scope?
    -   Are there additional features implemented beyond the intended scope?
2.  **DATABASE/STORAGE SCHEMA ANALYSIS**:
    -   **Database Schema Changes**: ONLY flag changes that impact database or storage schemas requiring migrations. This includes:
        - Database table additions, deletions, or column modifications
        - Database index changes, constraint modifications
        - Schema migrations, database structure alterations
        - Storage format changes that require data migration
    -   **DO NOT FLAG**: In-memory data structures, TypeScript interfaces, API response formats, or application-level data models that don't affect persistent storage
    -   **Significant Regressions**: Note any significant regressions in functionality that are not within the scope of this milestone but are evident from the code changes provided.

${scopeToleranceGuidance}

**EVALUATION CRITERIA:**
-   Only report on scope compliance, database/storage schema changes requiring migrations, and regressions.
- Do not comment on code quality, style, or potential risks.
- Your entire focus is on comparing the code changes to the planned deliverables.

**OUTPUT FORMAT:**
CRITICAL: You MUST respond with ONLY valid JSON. Do not include any text before or after the JSON response.

Analyze the actual code changes and provide an objective assessment:

{
  "scope_status": "COMPLIANT" | "MAJOR_DEVIATION",
  "scope_assessment": {
    "deliverables_status": [
      {
        "deliverable": "exact deliverable from intended list",
        "status": "IMPLEMENTED" | "PARTIAL" | "MISSING" | "NOT_APPLICABLE",
        "evidence": "specific files/changes that demonstrate this status"
      }
    ],
    "additional_scope": "any additional features implemented beyond the intended scope",
    "scope_gap": "any missing critical components from intended scope",
    "data_model_analysis": [
      {
        "change_description": "Description of the database/storage schema change requiring migration.",
        "change_type": "SCHEMA_ADDITION" | "SCHEMA_DELETION" | "SCHEMA_MODIFICATION",
        "file": "The file where the schema change was detected.",
        "reasoning": "Explanation of why this database/storage change requires a migration and its impact."
      }
    ],
    "significant_regressions": [
        {
            "regression_description": "Description of the significant regression detected.",
            "file": "The file where the regression is evident.",
            "impact": "The potential impact of this regression."
        }
    ]
  },
  "scope_deviations": [
    {
      "id": "a unique identifier for the deviation, e.g., a short hash of the content",
      "deviation": "what was intended vs what was actually implemented",
      "impact": "why this blocks milestone goals",
      "action": "specific next step required",
      "status": "NEEDS_ACTION"
    }
  ],
  "pr_summary": "Clear, concise 1-2 sentence summary suitable for PR description that highlights key changes and deliverables completed. Only create this if the milestone is COMPLIANT."
}
`;
}

async function getAnthropicJson(response: Anthropic.Messages.Message) {
  const content = response.content[0];
  if (content.type !== 'text') {
    throw new Error("Expected text response from Anthropic");
  }

  let text = content.text.trim();
  
  const jsonStartIndex = text.indexOf('{');
  const jsonEndIndex = text.lastIndexOf('}');
  
  if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
    text = text.substring(jsonStartIndex, jsonEndIndex + 1);
  }

  try {
    return JSON.parse(text);
  } catch (e) {
    console.error("Failed to parse AI response JSON:", text);
    console.error("Original AI response from model:", content.text);
    throw new Error("AI returned invalid JSON that could not be cleaned.");
  }
}

function extractMilestoneSection(implementationPlan: string, milestoneId: string): string {
    const milestoneIdentifier = milestoneId.toLowerCase();
    const lines = implementationPlan.split('\n');
    let inMilestoneSection = false;
    const milestoneLines: string[] = [];

    for (const line of lines) {
        const lowerLine = line.toLowerCase();
        if (lowerLine.includes(milestoneIdentifier) && lowerLine.match(/m\d+\.\d+/)) {
            inMilestoneSection = true;
        }

        if (inMilestoneSection) {
            if (lowerLine.match(/m\d+\.\d+/) && !lowerLine.includes(milestoneIdentifier)) {
                break; 
            }
            milestoneLines.push(line);
        }
    }

    return milestoneLines.join('\n');
}

function extractMilestoneFromImplementationPlan(implementationPlan: string, milestoneId: string): MilestoneContext | undefined {
  console.log(`[Milestone MCP Feedback] Parsing implementation plan for milestone ${milestoneId}`);
  
  // First, try to extract JSON from markdown code blocks
  let jsonContent = implementationPlan;
  
  // Look for ```json...``` blocks
  const jsonBlockMatch = implementationPlan.match(/```json\s*([\s\S]*?)\s*```/);
  if (jsonBlockMatch) {
    jsonContent = jsonBlockMatch[1].trim();
    console.log(`[Milestone MCP Feedback] Extracted JSON from code block, length: ${jsonContent.length} chars`);
  } else {
    // Look for any ```...``` blocks that might contain JSON
    const codeBlockMatch = implementationPlan.match(/```\s*([\s\S]*?)\s*```/);
    if (codeBlockMatch) {
      jsonContent = codeBlockMatch[1].trim();
      console.log(`[Milestone MCP Feedback] Extracted content from generic code block, length: ${jsonContent.length} chars`);
    } else {
      console.log(`[Milestone MCP Feedback] No code block found, using raw content`);
    }
  }
  
  try {
    // Try to parse as JSON first (new format)
    console.log(`[Milestone MCP Feedback] Attempting to parse JSON content (first 200 chars): ${jsonContent.substring(0, 200)}...`);
    const parsed = JSON.parse(jsonContent);
    console.log(`[Milestone MCP Feedback] ✅ Successfully parsed JSON, found ${Object.keys(parsed).length} top-level keys`);
    
    if (parsed.enhanced_milestones && Array.isArray(parsed.enhanced_milestones)) {
      console.log(`[Milestone MCP Feedback] Found ${parsed.enhanced_milestones.length} enhanced milestones in parsed JSON`);
      const milestone = parsed.enhanced_milestones.find((m: any) => m.milestone_id === milestoneId);
      
      if (milestone) {
        console.log(`[Milestone MCP Feedback] Found milestone in JSON format:`, JSON.stringify(milestone, null, 2));
        
        // Extract deliverables from scope_validation.planned_deliverables (new format)
        let plannedDeliverables: string[] = [];
        
        if (milestone.scope_validation && Array.isArray(milestone.scope_validation.planned_deliverables)) {
          plannedDeliverables = milestone.scope_validation.planned_deliverables;
          console.log(`[Milestone MCP Feedback] ✅ Extracted ${plannedDeliverables.length} deliverables from scope_validation`);
        }
        // Fallback to key_tasks_and_deliverables
        else if (Array.isArray(milestone.key_tasks_and_deliverables)) {
          plannedDeliverables = milestone.key_tasks_and_deliverables.map((task: any) => 
            typeof task === 'string' ? task : task.task || task.toString()
          );
          console.log(`[Milestone MCP Feedback] ✅ Extracted ${plannedDeliverables.length} deliverables from key_tasks_and_deliverables`);
        }
        
        return {
          milestone_id: milestone.milestone_id,
          title: milestone.title,
          description: milestone.description,
          planned_deliverables: plannedDeliverables,
          verification_criteria: milestone.verification_criteria || [],
          priority: milestone.priority || 'High'
        };
      }
    }
  } catch (e) {
    console.log(`[Milestone MCP Feedback] Implementation plan is not valid JSON, trying text parsing...`);
  }
  
  // Fallback: Try text-based parsing for legacy format
  const milestoneIdentifier = milestoneId.toLowerCase();
  const lines = implementationPlan.split('\n');
  let inMilestoneSection = false;
  const milestoneLines: string[] = [];

  for (const line of lines) {
    const lowerLine = line.toLowerCase();
    if (lowerLine.includes(milestoneIdentifier) && lowerLine.match(/m\d+\.\d+/)) {
      inMilestoneSection = true;
    }

    if (inMilestoneSection) {
      if (lowerLine.match(/m\d+\.\d+/) && !lowerLine.includes(milestoneIdentifier)) {
        break; 
      }
      milestoneLines.push(line);
    }
  }

  if (milestoneLines.length > 0) {
    // Parse milestone from text
    let title = `Milestone ${milestoneId}`;
    let description = 'Milestone description';
    const deliverables: string[] = [];
    const verificationCriteria: string[] = [];
    
    for (let i = 0; i < milestoneLines.length; i++) {
      const line = milestoneLines[i];
      const lowerLine = line.toLowerCase().trim();
      
      if (lowerLine.includes('title') && line.includes(':')) {
        title = line.split(':')[1].trim();
      }
      
      if (lowerLine.includes('description') && line.includes(':')) {
        description = line.split(':')[1].trim();
      }
      
      if (line.trim().startsWith('-') || line.trim().startsWith('*') || line.trim().match(/^\d+\./)) {
        const item = line.trim().replace(/^[-*\d.]\s*/, '');
        if (item) {
          deliverables.push(item);
        }
      }
    }
    
    console.log(`[Milestone MCP Feedback] ✅ Extracted ${deliverables.length} deliverables from text parsing`);
    
    return {
      milestone_id: milestoneId,
      title,
      description,
      planned_deliverables: deliverables,
      verification_criteria: verificationCriteria,
      priority: 'High'
    };
  }
  
  console.log(`[Milestone MCP Feedback] ❌ Could not find milestone ${milestoneId} in implementation plan`);
  return undefined;
}

function parseMilestoneFromDesignDoc(designDoc: string, implementationPlan: string, milestoneId?: string): MilestoneContext {
  console.log(`[Milestone MCP Feedback] parseMilestoneFromDesignDoc called with milestoneId: ${milestoneId}`);
  
  // If we have a milestone ID, try to extract the specific milestone from the JSON structure
  if (milestoneId) {
    const extracted = extractMilestoneFromImplementationPlan(implementationPlan, milestoneId);
    if (extracted) {
      return extracted;
    }
  }
  
  // Fallback to original text-based parsing
  console.log(`[Milestone MCP Feedback] Falling back to text-based parsing`);
  
  const lines = (designDoc + '\n' + implementationPlan).split('\n');
  let title = 'Implementation Milestone';
  let description = 'Milestone extracted from design document';
  let deliverables: string[] = [];
  let verificationCriteria: string[] = [];
  
  // Look for milestone-related sections
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].toLowerCase().trim();
    
    if (line.includes('milestone') && line.includes('#')) {
      title = lines[i].replace(/#+\s*/, '').trim();
    }
    
    if (line.includes('deliverable') || line.includes('requirement')) {
      // Collect next few lines as deliverables
      for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
        const nextLine = lines[j].trim();
        if (nextLine.startsWith('-') || nextLine.startsWith('*') || nextLine.match(/^\d+\./)) {
          deliverables.push(nextLine.replace(/^[-*\d.]\s*/, ''));
        } else if (nextLine === '' || nextLine.startsWith('#')) {
          break;
        }
      }
    }
    
    if (line.includes('verification') || line.includes('acceptance') || line.includes('criteria')) {
      // Collect next few lines as verification criteria
      for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
        const nextLine = lines[j].trim();
        if (nextLine.startsWith('-') || nextLine.startsWith('*') || nextLine.match(/^\d+\./)) {
          verificationCriteria.push(nextLine.replace(/^[-*\d.]\s*/, ''));
        } else if (nextLine === '' || nextLine.startsWith('#')) {
          break;
        }
      }
    }
  }
  
  // If no deliverables found, extract from implementation plan
  if (deliverables.length === 0) {
    const implLines = implementationPlan.split('\n');
    for (const line of implLines) {
      if (line.trim().startsWith('-') || line.trim().startsWith('*') || line.trim().match(/^\d+\./)) {
        deliverables.push(line.trim().replace(/^[-*\d.]\s*/, ''));
      }
    }
  }
  
  return {
    milestone_id: milestoneId || `milestone-${Date.now()}`,
    title,
    description,
    planned_deliverables: deliverables.slice(0, 10), // Limit to reasonable number
    verification_criteria: verificationCriteria.slice(0, 10),
    priority: 'High'
  };
}

export async function POST(request: Request) {
  const authHeader = request.headers.get('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return NextResponse.json({ error: 'Unauthorized: Missing or malformed Authorization header' }, { status: 401 });
  }

  const apiKey = authHeader.substring(7);
  const userId = await validateApiKeyAndGetUser(apiKey);

  if (!userId) {
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
  }

  let payload;
  try {
    payload = await request.json();
  } catch (error) {
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  const { 
    repository_slug, 
    code_changes, 
    design_doc,
    implementation_plan,
    milestone_title,
    milestone_id,
    existing_context,
    issue_url,
    review_iteration = 1,
    previously_accepted_risks = [],
    force_minimal_review = false,
    design_session_id,
  } = payload;

  if (!repository_slug || !Array.isArray(code_changes) || !design_doc || !implementation_plan) {
    return NextResponse.json({ 
      error: 'Missing required parameters: repository_slug, code_changes, design_doc, implementation_plan' 
    }, { status: 400 });
  }

  try {
    // Auto-extract design session ID from issue URL if provided and no explicit session ID given
    let finalDesignSessionId = design_session_id;
    if (!finalDesignSessionId && issue_url) {
      console.log(`[Milestone MCP Feedback] Attempting to extract design session ID from issue: ${issue_url}`);
      try {
        const issueResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'https://archknow.vercel.app'}/api/github/issue-details`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            issueUrl: issue_url,
            apiKey: authHeader.substring(7) // Pass the API key
          })
        });
        
        if (issueResponse.ok) {
          const issueData = await issueResponse.json();
          if (issueData.success && issueData.designSessionId) {
            finalDesignSessionId = issueData.designSessionId;
            console.log(`[Milestone MCP Feedback] Auto-extracted design session ID: ${finalDesignSessionId}`);
          }
        }
      } catch (error) {
        console.log(`[Milestone MCP Feedback] Failed to auto-extract design session ID from issue: ${error}`);
        // Continue without session ID - not a critical failure
      }
    }

    // Parse milestone context from the provided design doc and implementation plan
    // Use full implementation plan for JSON parsing, pass milestone_id for specific extraction
    const milestoneContext = parseMilestoneFromDesignDoc(design_doc, implementation_plan, milestone_id);
  
    if (milestone_title) {
      milestoneContext.title = milestone_title;
    }
    
    console.log(`[Milestone MCP Feedback] Final milestone context:`, JSON.stringify(milestoneContext, null, 2));
    console.log(`[Milestone MCP Feedback] Deliverables being passed to prompt: ${milestoneContext.planned_deliverables.length} items:`, milestoneContext.planned_deliverables);

    // For MCP, we don't have future milestones context, so we'll use an empty array
    const futureMilestones: { title: string; description: string }[] = [];

    // Determine review mode based on iteration and existing context
    const isSubsequentReview = review_iteration > 1 || existing_context || force_minimal_review;
    const reviewMode = isSubsequentReview ? 'FOCUSED' : 'COMPREHENSIVE';

    console.log(`[Milestone MCP Feedback] Review iteration: ${review_iteration}, Mode: ${reviewMode}`);

    // Determine milestone-appropriate security posture
    const securityPosture = determineMilestoneSecurityPosture(milestoneContext, code_changes);
    console.log(`[Milestone MCP Feedback] Security Posture: ${securityPosture.securityLevel} - ${securityPosture.rationale}`);

    // For subsequent reviews, be even more conservative about security findings
    if (reviewMode === 'FOCUSED' && securityPosture.securityLevel !== 'ENHANCED') {
      console.log(`[Milestone MCP Feedback] Adjusting security posture for focused review - raising threshold`);
    }

    // Generate prompts for parallel execution with review mode context
    const scopeCompliancePrompt = generateScopeCompliancePrompt(
      code_changes, 
      milestoneContext, 
      existing_context || '', 
      undefined, 
      reviewMode,
      previously_accepted_risks
    );
    
    const riskAnalysisPrompt = generateRiskAnalysisPrompt(
      code_changes, 
      milestoneContext, 
      futureMilestones,
      reviewMode,
      previously_accepted_risks
    );

    console.log('[Milestone MCP Feedback] Scope Compliance Prompt:', scopeCompliancePrompt);
    console.log('[Milestone MCP Feedback] Risk Analysis Prompt:', riskAnalysisPrompt);

    // Execute AI calls in parallel
    const [scopeComplianceResponse, riskAnalysisResponse] = await Promise.all([
      anthropic.messages.create({
        model: "claude-sonnet-4-20250514",
        max_tokens: 4096,
        messages: [{ role: "user", content: scopeCompliancePrompt }],
      }),
      anthropic.messages.create({
        model: "claude-sonnet-4-20250514",
        max_tokens: 4096,
        messages: [{ role: "user", content: riskAnalysisPrompt }],
      })
    ]);

    console.log('[Milestone MCP Feedback] Scope Compliance Response:', scopeComplianceResponse.content);
    console.log('[Milestone MCP Feedback] Risk Analysis Response:', riskAnalysisResponse.content);

    // Process and combine results
    const scopeResult = await getAnthropicJson(scopeComplianceResponse);
    const riskResult = await getAnthropicJson(riskAnalysisResponse);

    const allRisks = [
      ...(riskResult.critical_security_risks || []),
      ...(riskResult.critical_performance_risks || []),
      ...(riskResult.critical_ux_risks || [])
    ];

    const integrationBlocked = scopeResult.scope_status === 'MAJOR_DEVIATION' || 
                              allRisks.length > 0 || 
                              (scopeResult.scope_assessment?.data_model_analysis?.length || 0) > 0 || 
                              (scopeResult.scope_assessment?.significant_regressions?.length || 0) > 0;

    const combinedResult = {
      ...scopeResult,
      critical_security_risks: riskResult.critical_security_risks || [],
      critical_performance_risks: riskResult.critical_performance_risks || [],
      critical_ux_risks: riskResult.critical_ux_risks || [],
      integration_blocked: integrationBlocked,
      summary: reviewMode === 'FOCUSED' 
        ? `Follow-up review (iteration ${review_iteration}): Scope ${scopeResult.scope_status}. ${allRisks.length} new critical risk(s) identified. Integration is ${integrationBlocked ? 'BLOCKED' : 'READY'}.`
        : `Initial review: Scope ${scopeResult.scope_status}. ${allRisks.length} critical risk(s) identified. Integration is ${integrationBlocked ? 'BLOCKED' : 'READY'}.`,
      milestone_context: milestoneContext,
      task_context: {
        task_title: milestone_title || milestoneContext.title,
        task_description: milestoneContext.description,
        design_id: finalDesignSessionId || 'mcp-generated'
      },
      review_metadata: {
        review_iteration: review_iteration,
        review_mode: reviewMode,
        is_subsequent_review: isSubsequentReview,
        has_existing_context: !!existing_context,
        security_posture: {
          level: securityPosture.securityLevel,
          rationale: securityPosture.rationale,
          guidance_applied: securityPosture.additionalGuidance
        }
      },
      metadata: {
        total_lines_changed: code_changes.reduce((sum: number, change: CodeChange) => 
          sum + (change.additions || 0) + (change.deletions || 0), 0),
        files_modified: code_changes.length,
        integration_ready: !integrationBlocked
      },
      issue_url: issue_url
    };

    if (integrationBlocked) {
      delete combinedResult.pr_summary;
    }

    return NextResponse.json(combinedResult, { status: 200 });

  } catch (err) {
    const error = err as Error;
    console.error('Error processing milestone MCP feedback:', error);
    return NextResponse.json({
      error: 'An unexpected error occurred.',
      details: error.message
    }, { status: 500 });
  }
} 