import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';

export const dynamic = 'force-dynamic';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!,
});

interface MilestoneContext {
  milestone_id: string;
  title: string;
  description: string;
  planned_deliverables: string[];
  verification_criteria: string[];
  priority: string;
}

interface CodeChange {
  filename: string;
  patch: string;
  additions?: number;
  deletions?: number;
}

function generateRiskAnalysisPrompt(
  codeChanges: CodeChange[],
  milestoneContext: MilestoneContext,
  futureMilestones: { title: string; description: string }[]
): string {
  const formattedChanges = codeChanges.map(change => `
**${change.filename}**
${change.patch}
  `).join('\n');

  const futureMilestonesContext = futureMilestones && futureMilestones.length > 0 ? `

**CONTEXT ON FUTURE MILESTONES:**
To avoid flagging issues that will be addressed in upcoming work, here is a summary of planned future milestones. Do not report risks if they are explicitly covered by the description of a future milestone.
${futureMilestones.map(m => `
- **${m.title}:** ${m.description}`).join('')}
` : '';

  return `You are an expert software architect with a specialization in security and performance auditing. Your task is to analyze code changes for a specific milestone and identify only the most critical, blocking risks that would prevent safe integration.

**MILESTONE CONTEXT - ${milestoneContext.milestone_id}: ${milestoneContext.title}**
Description: ${milestoneContext.description}
${futureMilestonesContext}
**CODE CHANGES FOR ANALYSIS:**
${formattedChanges}

**YOUR TASK: CRITICAL RISK IDENTIFICATION**
Analyze the provided code changes to identify critical risks. Do not evaluate scope compliance or question the design. Focus ONLY on implementation risks that are severe enough to block integration.

**EVALUATION CRITIERA - ONLY REPORT RISKS THAT ARE:**
1.  **Directly present** in the provided code changes.
2.  **High-Severity:** Represents a significant security vulnerability, a critical performance bottleneck, or a UX failure that makes a feature unusable.
3.  **Actionable:** Has a clear, specific fix that can be implemented.
4.  **Blocking:** The risk is severe enough that it MUST be fixed before the code can be integrated. Avoid reporting minor issues, style nits, or best-practice suggestions.

**CRITICAL RISK CATEGORIES:**
- **Security:** SQL Injection, XSS, Authentication/Authorization flaws, Hardcoded Secrets, Insecure Direct Object References, Command Injection, Path Traversal, Missing Input Validation on critical paths.
- **Performance:** Obvious N+1 query patterns, inefficient loops over large datasets, blocking synchronous operations on main threads, memory leaks.
- **UX:** Broken or non-functional UI elements, workflows that lead to data loss, actions with no feedback.

**OUTPUT FORMAT:**
You MUST respond with ONLY a valid JSON object containing the identified risks. Do not include any text before or after the JSON.

{
  "critical_security_risks": [
    {
      "risk": "Specific security vulnerability description.",
      "severity": "CRITICAL",
      "impact": "Concrete impact on the system or users if exploited.",
      "file": "The file where the vulnerability exists.",
      "fix": "A specific, actionable recommendation to resolve the vulnerability."
    }
  ],
  "critical_performance_risks": [
    {
      "risk": "Specific performance issue description.",
      "severity": "CRITICAL",
      "impact": "How this issue degrades user experience or system stability.",
      "file": "The file where the performance issue exists.",
      "fix": "A specific, actionable recommendation to resolve the issue."
    }
  ],
  "critical_ux_risks": [
    {
      "risk": "Specific UX failure description.",
      "severity": "CRITICAL",
      "impact": "Why this failure makes the feature unusable or leads to errors.",
      "file": "The file where the UX issue is apparent.",
      "fix": "A specific, actionable recommendation to improve the UX."
    }
  ]
}

If no critical risks are found in any category, return an empty array for that category.
`;
}

function generateScopeCompliancePrompt(
  codeChanges: CodeChange[],
  milestoneContext: MilestoneContext,
  existingContext: string,
  detailedTasks?: any[]
): string {
  const totalLinesChanged = codeChanges.reduce((sum, change) => 
    sum + (change.additions || 0) + (change.deletions || 0), 0
  );

  const formattedChanges = codeChanges.map(change => `
**${change.filename}**
${change.patch}
  `).join('\n');

  const detailedTasksSection = detailedTasks && detailedTasks.length > 0 ? `

**INTENDED TASKS FOR THIS MILESTONE:**
${detailedTasks.map((task, i) => `${i + 1}. ${typeof task === 'string' ? task : task}`).join('\n')}
` : '';

  return `You are an expert software architect performing milestone validation. Your task is to analyze the actual code changes and assess whether they align with the intended milestone scope. Do NOT analyze for risks, security, or performance issues. Your focus is exclusively on scope compliance.

**MILESTONE REFERENCE - ${milestoneContext.milestone_id}: ${milestoneContext.title}**
Priority: ${milestoneContext.priority}
Description: ${milestoneContext.description}

**INTENDED DELIVERABLES FOR THIS MILESTONE:**
${milestoneContext.planned_deliverables.map((d, i) => `${i + 1}. ${d}`).join('\n')}

**INTENDED VERIFICATION CRITERIA:**
${milestoneContext.verification_criteria.map((c, i) => `${i + 1}. ${c}`).join('\n')}${detailedTasksSection}

**ACTUAL CODE CHANGES SUBMITTED (${totalLinesChanged} lines):**
${formattedChanges}

**YOUR ASSESSMENT TASK:**
Based on the actual code changes above, determine:

1.  **SCOPE COMPLIANCE**: Do the submitted changes align with the intended milestone deliverables?
    -   Are the planned deliverables actually implemented?
    -   Are there major deviations from the intended scope?
    -   Are there additional features implemented beyond the intended scope?
2.  **DATA MODEL & REGRESSION ANALYSIS**:
    -   **Data Model Changes**: Have there been any changes to data models (e.g., database schema, API contracts, core data structures)? If so, analyze if these changes represent feature creep or a regression from the planned implementation.
    -   **Significant Regressions**: Note any significant regressions in functionality that are not within the scope of this milestone but are evident from the code changes provided.

**EVALUATION CRITERIA:**
-   Only report on scope compliance, data model changes, and regressions.
- Do not comment on code quality, style, or potential risks.
- Your entire focus is on comparing the code changes to the planned deliverables.

**OUTPUT FORMAT:**
CRITICAL: You MUST respond with ONLY valid JSON. Do not include any text before or after the JSON response.

Analyze the actual code changes and provide an objective assessment:

{
  "scope_status": "COMPLIANT" | "MAJOR_DEVIATION",
  "scope_assessment": {
    "deliverables_status": [
      {
        "deliverable": "exact deliverable from intended list",
        "status": "IMPLEMENTED" | "PARTIAL" | "MISSING" | "NOT_APPLICABLE",
        "evidence": "specific files/changes that demonstrate this status"
      }
    ],
    "additional_scope": "any additional features implemented beyond the intended scope",
    "scope_gap": "any missing critical components from intended scope",
    "data_model_analysis": [
      {
        "change_description": "Description of the data model change detected.",
        "change_type": "FEATURE_CREEP" | "REGRESSION" | "UNEXPECTED_CHANGE",
        "file": "The file where the change was detected.",
        "reasoning": "Explanation of why this change is considered feature creep or a regression."
      }
    ],
    "significant_regressions": [
        {
            "regression_description": "Description of the significant regression detected.",
            "file": "The file where the regression is evident.",
            "impact": "The potential impact of this regression."
        }
    ]
  },
  "scope_deviations": [
    {
      "id": "a unique identifier for the deviation, e.g., a short hash of the content",
      "deviation": "what was intended vs what was actually implemented",
      "impact": "why this blocks milestone goals",
      "action": "specific next step required",
      "status": "NEEDS_ACTION"
    }
  ],
  "pr_summary": "Clear, concise 1-2 sentence summary suitable for PR description that highlights key changes and deliverables completed. Only create this if the milestone is COMPLIANT."
}

IMPORTANT: 
- Base your assessment ONLY on the actual code changes provided
- If intended deliverables are not evident in the code changes, mark them as MISSING
- If additional features beyond intended scope are implemented, note them in additional_scope
- Create "pr_summary" field ONLY when scope_status is COMPLIANT.
- Do not include fields for risks, security, or integration blocking. Your output is only about scope.`;
}

async function getAnthropicJson(response: Anthropic.Messages.Message) {
  const content = response.content[0];
  if (content?.type !== 'text') {
    throw new Error('Invalid response from AI: Content is not text.');
  }
  
  let text = content.text.trim();
  
  // Handle markdown code blocks by extracting content between the first '{' and last '}'
  const jsonStartIndex = text.indexOf('{');
  const jsonEndIndex = text.lastIndexOf('}');
  
  if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
    text = text.substring(jsonStartIndex, jsonEndIndex + 1);
  }

  try {
    return JSON.parse(text);
  } catch (e) {
    console.error("Failed to parse AI response JSON:", text);
    console.error("Original AI response from model:", content.text);
    throw new Error("AI returned invalid JSON that could not be cleaned.");
  }
}

function extractMilestoneFromImplementationPlan(implementationPlan: string, milestoneId: string): MilestoneContext | undefined {
  console.log(`[Milestone Feedback] Parsing implementation plan for milestone ${milestoneId}`);
  
  try {
    // Try to parse as JSON first (new format)
    const parsed = JSON.parse(implementationPlan);
    
    if (parsed.enhanced_milestones && Array.isArray(parsed.enhanced_milestones)) {
      const milestone = parsed.enhanced_milestones.find((m: any) => m.milestone_id === milestoneId);
      
      if (milestone) {
        console.log(`[Milestone Feedback] Found milestone in JSON format:`, JSON.stringify(milestone, null, 2));
        
        // Extract deliverables from scope_validation.planned_deliverables (new format)
        let plannedDeliverables: string[] = [];
        
        if (milestone.scope_validation && Array.isArray(milestone.scope_validation.planned_deliverables)) {
          plannedDeliverables = milestone.scope_validation.planned_deliverables;
          console.log(`[Milestone Feedback] ✅ Extracted ${plannedDeliverables.length} deliverables from scope_validation`);
        }
        // Fallback to key_tasks_and_deliverables
        else if (Array.isArray(milestone.key_tasks_and_deliverables)) {
          plannedDeliverables = milestone.key_tasks_and_deliverables.map((task: any) => 
            typeof task === 'string' ? task : task.task || task.toString()
          );
          console.log(`[Milestone Feedback] ✅ Extracted ${plannedDeliverables.length} deliverables from key_tasks_and_deliverables`);
        }
        
        return {
          milestone_id: milestone.milestone_id,
          title: milestone.title,
          description: milestone.description,
          planned_deliverables: plannedDeliverables,
          verification_criteria: milestone.verification_criteria || [],
          priority: milestone.priority || 'High'
        };
      }
    }
  } catch (e) {
    console.log(`[Milestone Feedback] Implementation plan is not valid JSON, trying text parsing...`);
  }
  
  // Fallback: Try text-based parsing for legacy format
  const milestoneIdentifier = milestoneId.toLowerCase();
  const lines = implementationPlan.split('\n');
  let inMilestoneSection = false;
  const milestoneLines: string[] = [];

  for (const line of lines) {
    const lowerLine = line.toLowerCase();
    if (lowerLine.includes(milestoneIdentifier) && lowerLine.match(/m\d+\.\d+/)) {
      inMilestoneSection = true;
    }

    if (inMilestoneSection) {
      if (lowerLine.match(/m\d+\.\d+/) && !lowerLine.includes(milestoneIdentifier)) {
        break; 
      }
      milestoneLines.push(line);
    }
  }

  if (milestoneLines.length > 0) {
    // Parse milestone from text
    let title = `Milestone ${milestoneId}`;
    let description = 'Milestone description';
    const deliverables: string[] = [];
    const verificationCriteria: string[] = [];
    
    for (let i = 0; i < milestoneLines.length; i++) {
      const line = milestoneLines[i];
      const lowerLine = line.toLowerCase().trim();
      
      if (lowerLine.includes('title') && line.includes(':')) {
        title = line.split(':')[1].trim();
      }
      
      if (lowerLine.includes('description') && line.includes(':')) {
        description = line.split(':')[1].trim();
      }
      
      if (line.trim().startsWith('-') || line.trim().startsWith('*') || line.trim().match(/^\d+\./)) {
        const item = line.trim().replace(/^[-*\d.]\s*/, '');
        if (item) {
          deliverables.push(item);
        }
      }
    }
    
    console.log(`[Milestone Feedback] ✅ Extracted ${deliverables.length} deliverables from text parsing`);
    
    return {
      milestone_id: milestoneId,
      title,
      description,
      planned_deliverables: deliverables,
      verification_criteria: verificationCriteria,
      priority: 'High'
    };
  }
  
  console.log(`[Milestone Feedback] ❌ Could not find milestone ${milestoneId} in implementation plan`);
  return undefined;
}

export async function POST(request: Request) {
  const authHeader = request.headers.get('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return NextResponse.json({ error: 'Unauthorized: Missing or malformed Authorization header' }, { status: 401 });
  }

  const apiKey = authHeader.substring(7);
  const userId = await validateApiKeyAndGetUser(apiKey);

  if (!userId) {
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceRoleKey) {
    return NextResponse.json({ error: 'Server configuration error: Supabase credentials not set.' }, { status: 500 });
  }

  const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);

  let payload;
  try {
    payload = await request.json();
  } catch (error) {
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  const { 
    repository_slug, 
    code_changes, 
    milestone_id,
    job_id,
    existing_context,
    implementation_plan,
    design_session_id,
    issue_url
  } = payload;

  if (!repository_slug || !Array.isArray(code_changes) || !milestone_id) {
    return NextResponse.json({ 
      error: 'Missing required parameters: repository_slug, code_changes, milestone_id' 
    }, { status: 400 });
  }

  try {
    // Auto-extract design session ID from issue URL if not explicitly provided
    let finalDesignSessionId = design_session_id;
    if (!finalDesignSessionId && issue_url) {
      console.log(`[Milestone Feedback] Attempting to extract design session ID from issue: ${issue_url}`);
      try {
        const issueResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'https://archknow.vercel.app'}/api/github/issue-details`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            issueUrl: issue_url,
            apiKey: apiKey // Pass the API key from auth header
          })
        });
        
        if (issueResponse.ok) {
          const issueData = await issueResponse.json();
          if (issueData.success && issueData.designSessionId) {
            finalDesignSessionId = issueData.designSessionId;
            console.log(`[Milestone Feedback] Auto-extracted design session ID: ${finalDesignSessionId}`);
          }
        }
      } catch (error) {
        console.log(`[Milestone Feedback] Failed to auto-extract design session ID from issue: ${error}`);
        // Continue without session ID - not a critical failure
      }
    }
    
    // Extract milestone context from implementation plan if provided, otherwise try job_id
    let milestoneContext: MilestoneContext | undefined;
    let detailedTasks: any[] | undefined;
    let futureMilestones: { title: string; description: string }[] = [];
    let taskContext: { task_title: string; task_description: string; design_id: string } | undefined;
    
    if (implementation_plan) {
      console.log(`[Milestone Feedback] Using implementation plan to extract milestone ${milestone_id}`);
      
      milestoneContext = extractMilestoneFromImplementationPlan(implementation_plan, milestone_id);
      
      if (!milestoneContext) {
        return NextResponse.json({ 
          error: `Milestone ${milestone_id} not found in implementation plan` 
        }, { status: 404 });
      }
      
      // Set detailedTasks from the milestone context
      detailedTasks = milestoneContext.planned_deliverables;
      
      // For implementation plan parsing, create simple task context
      taskContext = {
        task_title: milestoneContext.title,
        task_description: milestoneContext.description,
        design_id: finalDesignSessionId || 'implementation-plan'
      };
      
      console.log(`[Milestone Feedback] ✅ Successfully extracted milestone from implementation plan`);
      
    } else if (job_id) {
      console.log(`[Milestone Feedback] Fetching job ${job_id} for milestone ${milestone_id}`);
      
      const { data: jobData, error: fetchError } = await supabaseAdmin
        .from('design_doc_jobs')
        .select('phase4_output, task_title')
        .eq('id', job_id)
        .maybeSingle();

      if (fetchError) {
        console.error(`[Milestone Feedback] Database error fetching job ${job_id}:`, fetchError);
        return NextResponse.json({ 
          error: `Database error fetching job ${job_id}: ${fetchError.message}`
        }, { status: 500 });
      }

      if (!jobData || !jobData.phase4_output || !jobData.phase4_output.milestones) {
        console.error(`[Milestone Feedback] Job ${job_id} found, but milestone data is missing or in an unexpected format.`);
        return NextResponse.json({ 
          error: `Milestone data not found for job ${job_id}.`,
          details: 'The job was retrieved, but it does not contain valid milestone information in phase4_output.' 
        }, { status: 404 });
      }

      if (jobData) {
        taskContext = {
            task_title: jobData.task_title || 'Untitled Task',
            task_description: '', // This is populated from the milestone context in the UI
            design_id: finalDesignSessionId || job_id
        };
      }

      const allMilestones = jobData.phase4_output.milestones;
      const currentMilestoneIndex = allMilestones.findIndex((m: any) => m.milestone_id === milestone_id);

      if (currentMilestoneIndex === -1) {
        console.error(`[Milestone Feedback] Milestone ${milestone_id} not found in job ${job_id}.`);
        return NextResponse.json({ 
          error: `Milestone with ID ${milestone_id} not found in job ${job_id}` 
        }, { status: 404 });
      }

      const milestone = allMilestones[currentMilestoneIndex];
      
      console.log(`[Milestone Feedback] Raw milestone data for ${milestone_id}:`, JSON.stringify(milestone, null, 2));
      
      futureMilestones = allMilestones
        .slice(currentMilestoneIndex + 1)
        .map((m: any) => ({
            title: m.title,
            description: m.description,
        }));

      // Restore previous, more detailed context gathering
      detailedTasks = milestone.key_tasks_and_deliverables;

      // Extract planned deliverables (simplified since we prefer implementation_plan path)
      let plannedDeliverables: string[] = [];
      
      if (milestone.scope_validation && Array.isArray(milestone.scope_validation.planned_deliverables)) {
        plannedDeliverables = milestone.scope_validation.planned_deliverables;
      } else if (Array.isArray(milestone.key_tasks_and_deliverables)) {
        plannedDeliverables = milestone.key_tasks_and_deliverables.map((task: any) => 
          typeof task === 'string' ? task : task.task || task.toString()
        );
      }

      milestoneContext = {
        milestone_id: milestone.milestone_id,
        title: milestone.title,
        description: milestone.description,
        planned_deliverables: plannedDeliverables,
        verification_criteria: milestone.verification_criteria || [],
        priority: milestone.priority || 'High',
      };

    } else {
      return NextResponse.json({ 
        error: 'Missing required parameter: implementation_plan or job_id' 
      }, { status: 400 });
    }

    // Generate prompts for parallel execution
    const scopeCompliancePrompt = generateScopeCompliancePrompt(code_changes, milestoneContext, existing_context || '', detailedTasks);
    const riskAnalysisPrompt = generateRiskAnalysisPrompt(code_changes, milestoneContext, futureMilestones);

    console.log(`[Milestone Feedback] About to generate prompts with milestone context:`, JSON.stringify(milestoneContext, null, 2));
    console.log(`[Milestone Feedback] Deliverables being passed to prompt: ${milestoneContext.planned_deliverables.length} items:`, milestoneContext.planned_deliverables);
    
    console.log('[Milestone Feedback] Scope Compliance Prompt:', scopeCompliancePrompt);
    console.log('[Milestone Feedback] Risk Analysis Prompt:', riskAnalysisPrompt);

    // Execute AI calls in parallel
    const [scopeComplianceResponse, riskAnalysisResponse] = await Promise.all([
      anthropic.messages.create({
        model: "claude-sonnet-4-20250514",
        max_tokens: 4096,
        messages: [{ role: "user", content: scopeCompliancePrompt }],
      }),
      anthropic.messages.create({
        model: "claude-sonnet-4-20250514",
        max_tokens: 4096,
        messages: [{ role: "user", content: riskAnalysisPrompt }],
      })
    ]);

    console.log('[Milestone Feedback] Scope Compliance Response:', scopeComplianceResponse.content);
    console.log('[Milestone Feedback] Risk Analysis Response:', riskAnalysisResponse.content);

    // Process and combine results
    const scopeResult = await getAnthropicJson(scopeComplianceResponse);
    const riskResult = await getAnthropicJson(riskAnalysisResponse);

    const allRisks = [
      ...(riskResult.critical_security_risks || []),
      ...(riskResult.critical_performance_risks || []),
      ...(riskResult.critical_ux_risks || [])
    ];

    const integrationBlocked =  scopeResult.scope_status === 'MAJOR_DEVIATION' || allRisks.length > 0 || scopeResult.data_model_analysis.length > 0 || scopeResult.significant_regressions.length > 0;

    const combinedResult = {
      ...scopeResult,
      critical_security_risks: riskResult.critical_security_risks || [],
      critical_performance_risks: riskResult.critical_performance_risks || [],
      critical_ux_risks: riskResult.critical_ux_risks || [],
      integration_blocked: integrationBlocked,
      summary: `Scope: ${scopeResult.scope_status}. Risks: ${allRisks.length} critical risk(s) identified. Integration is ${integrationBlocked ? 'BLOCKED' : 'PROPOSED'}.`,
      milestone_context: milestoneContext,
      task_context: taskContext,
    };

    if (integrationBlocked) {
      delete combinedResult.pr_summary;
    }

    return NextResponse.json(combinedResult, { status: 200 });

  } catch (err) {
    const error = err as Error;
    console.error('Error processing milestone feedback:', error);
    return NextResponse.json({
      error: 'An unexpected error occurred.',
      details: error.message
    }, { status: 500 });
  }
} 