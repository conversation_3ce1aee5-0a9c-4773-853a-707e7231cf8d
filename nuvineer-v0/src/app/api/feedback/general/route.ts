import { createClient } from '@supabase/supabase-js';
// import { cookies } from 'next/headers'; // No longer needed for auth
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Supabase URL or Anon Key is missing from environment variables.');
    return NextResponse.json({ error: 'Server configuration error: Supabase credentials not set.' }, { status: 500 });
  }

  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  const authHeader = request.headers.get('Authorization');
  let authenticatedUserId: string;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return NextResponse.json({ error: 'Unauthorized: Missing or malformed Authorization header' }, { status: 401 });
  }

  const token = authHeader.substring(7);
  const { data: { user }, error: authError } = await supabase.auth.getUser(token);

  if (authError) {
    console.error('Supabase auth.getUser error:', authError.message);
    // Use Supabase error status if available, otherwise default to 401 or 500 based on context
    const status = (authError as any).status || (authError.message.toLowerCase().includes('invalid jwt') ? 401 : 500);
    return NextResponse.json({ error: 'Authentication failed', details: authError.message }, { status });
  }
  
  if (!user) {
    // Token was processed without error by Supabase, but no user object was returned.
    return NextResponse.json({ error: 'Unauthorized: No user found for token' }, { status: 401 });
  }
  authenticatedUserId = user.id;

  let payload;
  try {
    payload = await request.json();
  } catch (error) {
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  const { feedbackType, comment, repo } = payload;

  // Basic validation
  if (!feedbackType || typeof feedbackType !== 'string' || !feedbackType.trim()) {
    return NextResponse.json({ error: 'Missing or invalid feedbackType' }, { status: 400 });
  }
  if (!comment || typeof comment !== 'string' || !comment.trim()) {
    return NextResponse.json({ error: 'Missing or invalid comment' }, { status: 400 });
  }
  const validTypes = ['suggestion', 'bug_report', 'question', 'compliment', 'other'];
  if (!validTypes.includes(feedbackType)) {
     return NextResponse.json({ error: 'Invalid feedbackType value' }, { status: 400 });
  }

  try {
    const { error: dbError } = await supabase
      .from('general_feedback')
      .insert({
        feedback_type: feedbackType,
        comment: comment.trim(),
        repository_slug: repo || null,
        user_id: authenticatedUserId, // Use the ID from token authentication
      });

    if (dbError) {
      console.error('Supabase error inserting general feedback:', dbError);
      return NextResponse.json({ error: 'Database error', details: dbError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Feedback submitted successfully.' });

  } catch (error) {
    console.error('Unexpected error handling general feedback:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 