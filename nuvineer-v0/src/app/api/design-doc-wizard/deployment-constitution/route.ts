import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const repositorySlug = searchParams.get('repositorySlug');
  const installationId = searchParams.get('installationId');

  if (!repositorySlug) {
    return NextResponse.json({ error: 'Repository slug is required' }, { status: 400 });
  }

  const supabaseAdmin = createSupabaseAdminClient();
  const effectiveInstallationId = installationId === '0' ? 0 : parseInt(installationId || '0');

  try {
    console.log(`[Deployment Constitution API] Fetching deployment constitution for ${repositorySlug}, installationId: ${effectiveInstallationId}`);

    const { data: deploymentData, error: deploymentError } = await supabaseAdmin
      .from('deployment_constitutions')
      .select('*')
      .eq('repository_slug', repositorySlug)
      .eq('installation_id', effectiveInstallationId)
      .single();

    if (deploymentError && deploymentError.code !== 'PGRST116') {
      console.error('[Deployment Constitution API] Database error:', deploymentError);
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    if (!deploymentData) {
      console.log(`[Deployment Constitution API] No deployment constitution found for ${repositorySlug}`);
      return NextResponse.json({ 
        hasDeploymentConstitution: false,
        deploymentConstitution: null 
      });
    }

    console.log(`[Deployment Constitution API] Found deployment constitution for ${repositorySlug}`);
    return NextResponse.json({
      hasDeploymentConstitution: true,
      deploymentConstitution: {
        constitutionData: deploymentData.constitution_data,
        createdAt: deploymentData.created_at,
        updatedAt: deploymentData.updated_at,
        sourceRepoUrl: deploymentData.source_repo_url,
        sourceCommitHash: deploymentData.source_commit_hash
      }
    });

  } catch (error) {
    console.error('[Deployment Constitution API] Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 