import { NextRequest, NextResponse } from 'next/server';
import { draftUserJourneysFromTask } from '../../../../orchestrator.js';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { taskDetails, projectConstitution, repositorySlug, installationId, isPublic } = body;

    // Validate required fields
    if (!taskDetails || !taskDetails.title || !taskDetails.description) {
      return NextResponse.json(
        { error: 'Task details with title and description are required' },
        { status: 400 }
      );
    }

    // Create Supabase client for auth
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // For public repositories, we might want to allow access without strict auth
    if (!isPublic) {
      // Additional auth checks for private repos could go here
      console.log(`[API] Drafting user journeys for private repo: ${repositorySlug}`);
    }

    console.log(`[API] Starting user journey drafting for task: "${taskDetails.title}"`);

    // Log if this is an experimental feature
    if (taskDetails.isExperimental) {
      console.log(`[API] Feature marked as experimental - using streamlined journey generation`);
    }

    // Call the orchestrator function to draft user journeys
    const draftedJourneys = await draftUserJourneysFromTask(taskDetails, projectConstitution);

    // Mark all journeys as drafts and generate IDs
    const journeysWithMetadata = draftedJourneys.map((journey, index) => ({
      ...journey,
      id: `journey_${Date.now()}_${index}`,
      isDraft: true,
      status: 'draft' as const
    }));

    console.log(`[API] Successfully drafted ${journeysWithMetadata.length} user journeys`);

    return NextResponse.json({
      success: true,
      userJourneys: journeysWithMetadata,
      message: `Generated ${journeysWithMetadata.length} draft user journeys for review`
    });

  } catch (error: any) {
    console.error('[API] Error in draft-user-journeys:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to draft user journeys',
        details: error.message || 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
} 