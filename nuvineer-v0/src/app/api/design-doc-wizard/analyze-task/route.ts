import { NextRequest, NextResponse } from 'next/server';
import { generateTaskAnalysis } from '../../../../orchestrator.js';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { taskDetails, userJourneys, projectConstitution } = body;

    // Validate required fields
    if (!taskDetails || !taskDetails.title || !userJourneys) {
      return NextResponse.json(
        { error: 'Task details and user journeys are required' },
        { status: 400 }
      );
    }

    // Auth check
    const cookieStore = cookies();
    const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        { cookies: { get(name: string) { return cookieStore.get(name)?.value; } } }
    );
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    console.log(`[API] Starting task analysis for: "${taskDetails.title}"`);

    // Log if this is an experimental feature
    if (taskDetails.isExperimental) {
      console.log(`[API] Feature marked as experimental - using streamlined analysis approach`);
    }

    // Call the orchestrator function to analyze the task
    const taskAnalysis = await generateTaskAnalysis(taskDetails, userJourneys, projectConstitution);

    console.log(`[API] Successfully analyzed task.`);

    return NextResponse.json({
      success: true,
      taskAnalysis, // Return the analysis object
    });

  } catch (error: any) {
    console.error('[API] Error in analyze-task:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to analyze task',
        details: error.message || 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
} 