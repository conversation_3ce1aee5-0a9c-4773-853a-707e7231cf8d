import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { queryPinecone } from '@/lib/pineconeUtils';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';

// Default AI Agent Protocol
const DEFAULT_AI_AGENT_PROTOCOL = `# AI Agent Implementation Protocol

**Preamble:**
These instructions guide the AI agent in implementing features and changes outlined in an accompanying design document with rigorous quality control, risk assessment, and scope validation. The primary objectives are:
1. All development work occurs on a dedicated feature branch with milestone-driven progression
2. Changes are broken down into small, logical, and review-friendly atomic commits
3. Each milestone undergoes thorough scope validation and risk assessment before integration
4. Each defined milestone can be independently and safely integrated into the primary development branch upon completion
5. All deviations from planned scope are explicitly tracked and justified
6. Critical risks (security, performance, UX) are identified and categorized as new vs. existing

---

## Phase 0: Preparation & Milestone Definition

1. **Analyze Design Document:** Thoroughly review the provided design document to understand its goals, scope, technical details, and architectural decisions.
2. **Identify/Define Milestones:**
   * If the design document explicitly defines clear, sequential milestones with success criteria, proceed with those.
   * If milestones are not clearly defined or are too broad, the first task is to propose a logical breakdown of the work into smaller, distinct, implementable, and independently testable milestones. Each proposed milestone must have clear success criteria.
   * **Action:** If milestones were self-defined, seek confirmation/approval for this breakdown before proceeding with implementation.
3. **Establish Baseline Risk Profile:** Document the current system's risk profile in areas that will be modified (security, performance, UX patterns).

---

## Phase 1: Project Setup

1. **1.1. Identify Primary Branch:** Determine the project's main development line (eg'main, master). All integrations will ultimately target this branch.
2. **1.2. Create Main Feature Branch:**
   * Create a new branch from the latest state of the primary branch.
   * **Naming Convention:** 'feature/<design-doc-identifier-or-short-title>' (e.g., 'feature/adyen-fallback-provider'). This branch will accumulate all milestone work for this design document.

---

## Phase 2: Milestone-Driven Implementation Cycle (Repeat for each milestone)

### 2.1. Pre-Implementation Planning
1. **Create Milestone Branch:**
   * Ensure your main feature branch ('feature/<design-doc-identifier>') is up-to-date with its remote counterpart.
   * Create a new branch *from the current state of your main feature branch*.
   * **Naming Convention:** '<main-feature-branch-name>/milestone-<N>-<short-milestone-description>'

2. **Milestone Scope Validation:**
   * Review the specific deliverables, success criteria, and verification requirements for the current milestone
   * Identify all files that will be modified or created
   * Map each deliverable to specific implementation tasks
   * Document expected integration points with existing system components

### 2.2. Implementation Phase
1. **Focus on Milestone Requirements:**
   * Switch to the milestone branch and focus exclusively on implementing the requirements, tasks, and success criteria defined for the current milestone in the design document.
   * Pay close attention to any "Developer Guidance," "Related Files," or specific technical instructions mentioned in the design document relevant to this milestone.

2. **Develop with Atomic Commits:**
   * Break down the implementation work for the milestone into the smallest possible logical, atomic commits. Each commit should represent a single, complete unit of change.
   * **Commit Message Convention:** Use clear, concise, and descriptive commit messages (e.g., follow Conventional Commits format: \`feat: implement payment abstraction interface\`, \`fix: resolve issue in Stripe webhook handler\`, \`test: add unit tests for Adyen client\`).

3. **Maintain Code Quality Standards:**
   * Write comprehensive unit tests for all new or modified functionality. Ensure all existing and new tests pass.
   * Update or create any necessary documentation (e.g., code comments, README updates, API documentation).
   * Adhere strictly to the project's existing coding standards, style guides, and linting/formatting rules.
   * Follow established architectural patterns and decisions referenced in the design document.

### 2.3. Pre-Integration Validation (CRITICAL STEP)

**Before proceeding to integration, MANDATORY evaluation of staged changes:**

1. **Stage All Changes:** Use 'git add .' to stage all milestone changes for evaluation.

2. **Generate Milestone Completion Report:**
   * **Scope Compliance Assessment:**
     * Compare implemented deliverables against planned milestone requirements
     * Identify any planned features that were not implemented
     * Identify any additional features that were implemented beyond scope
     * Rate scope compliance: COMPLETE | PARTIAL | EXCEEDED | DEVIATED
   
   * **Critical Risk Analysis:**
     * **Security Risks:** Authentication bypasses, authorization issues, data exposure, input validation gaps, injection vulnerabilities
     * **Performance Risks:** Memory leaks, inefficient queries, blocking operations, resource exhaustion, scalability bottlenecks  
     * **UX Risks:** Accessibility issues, confusing workflows, error handling gaps, loading states, responsive design problems
     * **Integration Risks:** Breaking changes, API compatibility, dependency conflicts, data migration issues
     * **Categorize each risk:** NEW RISK (introduced by this milestone) | EXISTING RISK (already present in codebase) | MITIGATED RISK (existing risk reduced)

   * **Deviation Documentation:**
     * List all deviations from the original milestone plan with justifications
     * Document any architectural decisions that differ from the design document
     * Note any implementation shortcuts or technical debt introduced

   * **Integration Readiness Checklist:**
     * [ ] All milestone deliverables implemented
     * [ ] All tests pass (existing + new)
     * [ ] No critical security risks introduced
     * [ ] Performance benchmarks maintained
     * [ ] Breaking changes documented
     * [ ] Database migrations tested
     * [ ] Feature flags properly implemented
     * [ ] Error handling comprehensive

3. **Milestone Independence Verification:**
   * Critically assess whether the changes for this milestone, once merged, would leave the application in a stable, functional, and deployable state for the functionality covered by this milestone.
   * The milestone should not have hard dependencies on *future, un-merged* milestones.
   * Verify that feature gating properly isolates incomplete functionality.

---

## Phase 3: Milestone Review and Integration Cycle (Repeat for each milestone)

### 3.1. Quality Assurance Review
1. **Code Review (Simulated or Actual):**
   * The changes should be reviewed against the design document, milestone requirements, coding standards, and test coverage.
   * Validate that all identified risks have appropriate mitigations or are acceptable for integration.

2. **Risk Mitigation Planning:**
   * For any NEW RISKS identified, document mitigation strategies
   * For EXISTING RISKS that remain, note if they are acceptable or require future addressing
   * Ensure no critical security vulnerabilities are being introduced

### 3.2. Pull Request and Integration
1. **Pull Request (PR) to Main Feature Branch:**
   * Once the milestone implementation is complete and validated on its branch, create a Pull Request (PR) from the milestone branch to the main feature branch ('feature/<design-doc-identifier>').
   * **PR Title:** Clearly indicate the milestone number and its purpose (e.g., "Feat(Milestone 1): Implement Payment Provider Abstraction Layer").
   * **PR Description Must Include:**
     * Concise summary of the changes made for this milestone
     * Link to the relevant section(s) of the design document
     * Instructions on how to test or verify the changes
     * **MANDATORY Milestone Metadata:**
       * Implementation Plan Path: <path/to/your_implementation_plan.md>
       * Design Document Path: <path/to/your_design_document.md>
       * Scope Compliance: [COMPLETE|PARTIAL|EXCEEDED|DEVIATED] with details
       * Milestone Deviations Summary: [List any deviations from the design doc or implementation plan for this milestone and concise reasons, or state "No deviations."]
       * Critical Risks Introduced: [List NEW RISKS with severity levels, or state "No new critical risks."]
       * Existing Risks Status: [List any EXISTING RISKS that were modified/mitigated, or state "No existing risks affected."]
       * Integration Dependencies: [List any dependencies on other milestones or external systems]

2. **Merge to Main Feature Branch:**
   * After the PR is approved (or self-validated), merge the milestone branch into the main feature branch.
   * Delete the (now merged) milestone branch (e.g., 'feature/adyen-fallback-provider/milestone-1-abstraction-layer').

3. **Integrate Completed Milestone into Primary Branch:**
   * The objective is to integrate each milestone into the primary development branch ('main' or 'master') as it's completed and stabilized on the main feature branch.
   * **Steps for Integration:**
     1. **Synchronize:** Ensure your local main feature branch is fully synchronized with the latest state of the *remote primary development branch*.
     2. **Create PR to Primary:** Create a new Pull Request from your (now updated) main feature branch to the primary development branch.
     3. **PR Details:** Include the same milestone metadata as above, plus integration impact assessment.
     4. **Review and Merge:** After this PR is reviewed and merged into the primary development branch by the project maintainers, the milestone is officially integrated.
     5. **Post-Merge Sync:** After the merge to primary is complete, ensure your local main feature branch is again updated to reflect the state of the primary branch.

---

## Phase 4: Finalization

1. **4.1. All Milestones Complete:** Repeat Phase 2 and Phase 3 for all defined milestones.
2. **4.2. Final Integration Assessment:**
   * Conduct end-to-end testing of the complete feature
   * Validate that all milestones work together cohesively
   * Confirm that no critical risks were introduced during the full implementation
   * Document any technical debt or future improvement opportunities
3. **4.3. Cleanup:** Once the main feature branch has been fully merged into the primary development branch and all changes are live/verified, the main feature branch (feature/<design-doc-identifier>) can be deleted.

---

## Critical Success Factors

1. **Never compromise on the pre-integration validation step** - this is where quality is assured
2. **Document all deviations with clear justifications** - transparency is key for maintainability
3. **Categorize risks appropriately** - distinguish between new problems and existing issues
4. **Maintain independent milestone integration capability** - each milestone should be deployable
5. **Follow established architectural patterns** - consistency reduces integration risks
6. **Comprehensive testing at each milestone** - don't accumulate testing debt`;

// Helper function to call Claude LLM for JSON responses
async function callClaudeLlmForJson(prompt: string): Promise<any> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.ANTHROPIC_API_KEY!,
            'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
            model: 'claude-sonnet-4-20250514',
            max_tokens: 4000,
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ]
        })
    });

    if (!response.ok) {
        throw new Error(`LLM API call failed: ${response.statusText}`);
    }

    const llmResponse = await response.json();
    const responseText = llmResponse.content[0].text;
    
    // Parse the JSON response from the LLM
    try {
        // Extract JSON from the response (in case there's extra text)
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            return JSON.parse(jsonMatch[0]);
        } else {
            throw new Error('No valid JSON found in LLM response');
        }
    } catch (parseError) {
        console.error('Error parsing LLM response:', parseError);
        throw new Error('Failed to parse JSON from LLM response');
    }
}

export async function POST(req: NextRequest) {
    console.log('[Design Doc Wizard - Generate Implementation Plan] Request received');
    
    try {
        const body = await req.json();
        const { 
            design_document, 
            task_details, 
            repository_slug, 
            installation_id, 
            is_public, 
            ai_agent_protocol // Optional AI agent protocol
        } = body;
        
        console.log('[Design Doc Wizard - Generate Implementation Plan] Request parameters:', {
            design_document_title: design_document?.title,
            task_title: task_details?.title,
            repository_slug,
            installation_id,
            is_public,
            has_ai_agent_protocol: !!ai_agent_protocol
        });

        if (!design_document || !task_details) {
            console.log('[Design Doc Wizard - Generate Implementation Plan] Missing required parameters');
            return NextResponse.json({ 
                error: 'design_document and task_details are required' 
            }, { status: 400 });
        }
        
        // Create Supabase client for auth
        const cookieStore = cookies();
        const supabase = createServerClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            {
                cookies: {
                    get(name: string) {
                        return cookieStore.get(name)?.value;
                    },
                },
            }
        );

        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session) {
            console.log('[Design Doc Wizard - Generate Implementation Plan] Authentication required');
            return NextResponse.json(
                { error: 'Authentication required' },
                { status: 401 }
            );
        }

        console.log('[Design Doc Wizard - Generate Implementation Plan] User authenticated:', session.user.id);

        // Get repository namespace for knowledge base queries
        const jobNamespace = getRepositoryNamespace(installation_id || 0, repository_slug);
        console.log('[Design Doc Wizard - Generate Implementation Plan] Using namespace:', jobNamespace);

        // Convert design document to markdown format
        const formatDesignDocAsMarkdown = (doc: any) => {
            let markdown = `# ${doc.title}\n\n`;
            
            if (doc.goals && doc.goals.length > 0) {
                markdown += `## Goals\n\n`;
                doc.goals.forEach((goal: string) => {
                    markdown += `- ${goal}\n`;
                });
                markdown += `\n`;
            }

            if (doc.non_goals && doc.non_goals.length > 0) {
                markdown += `## Non-Goals\n\n`;
                doc.non_goals.forEach((nonGoal: string) => {
                    markdown += `- ${nonGoal}\n`;
                });
                markdown += `\n`;
            }

            if (doc.high_level_design) {
                markdown += `## High-Level Design\n\n`;
                
                if (doc.high_level_design.overall_system_overview) {
                    markdown += `### System Overview\n\n${doc.high_level_design.overall_system_overview}\n\n`;
                }

                if (doc.high_level_design.core_architectural_choices && doc.high_level_design.core_architectural_choices.length > 0) {
                    markdown += `### Core Architectural Decisions\n\n`;
                    doc.high_level_design.core_architectural_choices.forEach((choice: any) => {
                        markdown += `#### ${choice.title}\n\n`;
                        markdown += `**Approach:** ${choice.recommended_approach_description}\n\n`;
                        if (choice.justification_and_context) {
                            markdown += `**Justification:** ${choice.justification_and_context}\n\n`;
                        }
                    });
                }

                if (doc.high_level_design.process_flow_diagram_mermaid && doc.high_level_design.process_flow_diagram_mermaid !== 'N/A') {
                    markdown += `### Process Flow\n\n\`\`\`mermaid\n${doc.high_level_design.process_flow_diagram_mermaid}\n\`\`\`\n\n`;
                }

                if (doc.high_level_design.key_components_and_responsibilities && doc.high_level_design.key_components_and_responsibilities.length > 0) {
                    markdown += `### Key Components\n\n`;
                    doc.high_level_design.key_components_and_responsibilities.forEach((component: any) => {
                        markdown += `- **${component.name}:** ${component.responsibility}\n`;
                    });
                    markdown += `\n`;
                }

                if (doc.high_level_design.data_model_changes && doc.high_level_design.data_model_changes !== 'N/A') {
                    markdown += `### Data Model Changes\n\n${doc.high_level_design.data_model_changes}\n\n`;
                }
            }

            if (doc.alternatives_analysis) {
                markdown += `## Alternatives Analysis\n\n`;
                
                if (doc.alternatives_analysis.overall_solution_alternatives && doc.alternatives_analysis.overall_solution_alternatives.length > 0) {
                    markdown += `### Alternative Approaches Considered\n\n`;
                    doc.alternatives_analysis.overall_solution_alternatives.forEach((alt: any, index: number) => {
                        markdown += `#### Alternative ${index + 1}: ${alt.approach_name}\n\n`;
                        markdown += `${alt.description}\n\n`;
                        if (alt.pros && alt.pros.length > 0) {
                            markdown += `**Pros:**\n`;
                            alt.pros.forEach((pro: string) => markdown += `- ${pro}\n`);
                            markdown += `\n`;
                        }
                        if (alt.cons && alt.cons.length > 0) {
                            markdown += `**Cons:**\n`;
                            alt.cons.forEach((con: string) => markdown += `- ${con}\n`);
                            markdown += `\n`;
                        }
                    });
                }

                if (doc.alternatives_analysis.overall_recommendation_and_justification) {
                    markdown += `### Recommendation\n\n${doc.alternatives_analysis.overall_recommendation_and_justification}\n\n`;
                }
            }

            if (doc.milestones_sketch) {
                markdown += `## Implementation Milestones\n\n${doc.milestones_sketch}\n\n`;
            }

            if (doc.success_metrics && doc.success_metrics.length > 0) {
                markdown += `## Success Metrics\n\n`;
                doc.success_metrics.forEach((metric: string) => {
                    markdown += `- ${metric}\n`;
                });
                markdown += `\n`;
            }

            if (doc.referenced_decisions && doc.referenced_decisions.length > 0) {
                markdown += `## Referenced Decisions\n\n`;
                doc.referenced_decisions.forEach((decision: any) => {
                    markdown += `- **${decision.id}:** ${decision.summary_of_relevance_in_this_design}\n`;
                });
                markdown += `\n`;
            }

            return markdown;
        };

        const designDocMarkdown = formatDesignDocAsMarkdown(design_document);
        
        // Generate a cleaner design document identifier for branch naming
        const designDocIdentifier = task_details.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '')
            .substring(0, 50);

        // Create implementation plan prompt with AI agent protocol support
        const effectiveAiAgentProtocol = ai_agent_protocol || DEFAULT_AI_AGENT_PROTOCOL;
        const implementationPlanPrompt = `
You are an expert software architect tasked with creating a detailed, actionable implementation plan from a finalized design document with robust quality control, scope validation, and risk assessment built into every milestone.

Task: ${task_details.title}
Description: ${task_details.description}

Design Document (Complete Markdown):
${designDocMarkdown}

${ai_agent_protocol ? `
AI AGENT IMPLEMENTATION PROTOCOL:
The implementation plan must follow the AI Agent Implementation Protocol provided below. This protocol defines the specific workflow, branch naming conventions, milestone structure, and quality requirements that must be followed:

${ai_agent_protocol}

CRITICAL PROTOCOL REQUIREMENTS: Your implementation plan must strictly adhere to the above protocol, particularly:
- Branch naming conventions (feature/<design-doc-identifier> for main, feature/<design-doc-identifier>/milestone-N-<description> for milestones)
- Milestone-driven approach with independent, testable milestones that include scope validation
` : ''}

Your task is to analyze the ENTIRE design document above and create a comprehensive implementation plan that breaks down the project into actionable milestones with built-in quality control.

IMPORTANT BRANCH NAMING REQUIREMENTS:
- Main feature branch: feature/${designDocIdentifier}
- Milestone branches: feature/${designDocIdentifier}/milestone-N-<short-description>
- Branch names must be Git-friendly (lowercase, hyphens, no spaces)

QUALITY CONTROL REQUIREMENTS:
1. Each milestone must include explicit scope validation criteria
2. **CRITICAL**: Each milestone must minimize introduction of new security or ux risks
4. Each milestone must be independently testable and deployable
5. Include specific validation methods for each deliverable

Requirements:
1. Analyze the full document content - don't miss any important details
2. Create logical incremental milestones atmost 5 as dictated in the design document that build upon each other with clear validation points
3. Each milestone should have specific, actionable deliverables with measurable success criteria
5. Identify key architectural decisions and their implementation requirements with validation steps
6. Extract any success metrics or verification criteria mentioned in the design document
7. Consider all sections of the document comprehensively
8. **CRITICAL**: Provide concrete, specific branch naming conventions for each milestone
9. **CRITICAL**: Ensure each milestone can be independently integrated with proper scope validation

Output Format (JSON only):
{
  "git_must_use_main_feature_branch_name": "feature/${designDocIdentifier}",
  "milestones": [
    {
      "milestone_id": "M1.0",
      "title": "string - Clear milestone title",
      "description": "string - Detailed description with scope boundaries",
      "priority": "P0-Critical|P1-High|P2-Medium",
      "key_tasks_and_deliverables": [
        {
          "task": "string - Specific deliverable",
          "validation_criteria": "string - How to verify completion"
        }
      ],
      "scope_validation": {
        "planned_deliverables": ["specific items that must be delivered"]
      },
      "verification_criteria": ["criteria1", "criteria2"],
    }
  ],
  "data_model_summary": "string - Summary of data model changes with validation requirements",
  "referenced_decisions": ["summary of key referenced decisions"]
}
`;

        console.log('[Design Doc Wizard - Generate Implementation Plan] Calling LLM for initial implementation plan generation...');
        
        // Generate initial implementation plan
        const implementationPlanResult = await callClaudeLlmForJson(implementationPlanPrompt);

        // Enhance each milestone with relevant guidance from past decisions
        const enhancedMilestones = [];
        if (implementationPlanResult.milestones && Array.isArray(implementationPlanResult.milestones)) {
            console.log('[Design Doc Wizard - Generate Implementation Plan] Enhancing milestones with knowledge base integration...');
            
            for (let i = 0; i < implementationPlanResult.milestones.length; i++) {
                const milestone = implementationPlanResult.milestones[i];
                
                // Ensure branch naming is consistent and follows conventions
                const milestoneNumber = i + 1;
                const milestoneTitle = milestone.title || milestone.description || `Milestone ${milestoneNumber}`;
                const shortDescription = milestoneTitle
                    .toLowerCase()
                    .replace(/[^a-z0-9]+/g, '-')
                    .replace(/^-+|-+$/g, '')
                    .split('-')
                    .slice(0, 3) // Take first 3 words
                    .join('-');
                
                const correctBranchName = `feature/${designDocIdentifier}/milestone-${milestoneNumber}-${shortDescription}`;
                
                // Generate search queries for this milestone
                const searchQueriesPrompt = `
                    For the milestone: "${milestoneTitle}"
                    Description: "${milestone.description || 'No description provided'}"
                    Key Tasks: ${milestone.key_tasks_and_deliverables?.map((task: any) => 
                        typeof task === 'string' ? task : task.task || 'Task details not specified'
                    ).join(', ') || 'No specific tasks listed'}
                    
                    Generate 3-5 specific search queries to find relevant architectural decisions and best practices.
                    Focus on the core technical challenges and implementation patterns needed.
                    
                    Output Format (JSON only):
                    {
                        "search_queries": ["query1", "query2", "query3"]
                    }
                `;
                
                console.log(`[Design Doc Wizard - Generate Implementation Plan] Generating search queries for milestone ${milestone.milestone_id}...`);
                const searchQueriesResult = await callClaudeLlmForJson(searchQueriesPrompt);
                const searchQueries = searchQueriesResult.search_queries || [];

                // Search for relevant guidance
                const applicableDeveloperGuidanceAndBestPractices = [];
                const uniqueRetrievedDecisionIds = new Set<string>();

                for (const query of searchQueries) {
                    if (!query || typeof query !== 'string') continue;
                    
                    console.log(`[Design Doc Wizard - Generate Implementation Plan] Milestone ${milestone.milestone_id}: Searching with "${query}" in namespace ${jobNamespace}`);
                    try {
                        const pineconeResults = await queryPinecone(query, jobNamespace, 3, 0.55);
                        
                        // Use only results that meet the 0.55 threshold
                        const resultsToUse = pineconeResults.filter(match => match.score && match.score >= 0.55);
                        
                        console.log(`[Design Doc Wizard - Generate Implementation Plan] Query "${query}": Found ${pineconeResults.length} results, using ${resultsToUse.length} results with score >= 0.55`);
                        
                        for (const match of resultsToUse) {
                            if (match.id && !uniqueRetrievedDecisionIds.has(match.id)) {
                                uniqueRetrievedDecisionIds.add(match.id);
                                applicableDeveloperGuidanceAndBestPractices.push({
                                    retrieved_decision_title: match.metadata?.title || '',
                                    guidance_summary: match.metadata?.dev_prompt || match.metadata?.follows_standard_practice_reason || '',
                                    related_files: match.metadata?.related_files || [],
                                });
                            }
                        }
                    } catch (error) {
                        console.warn(`[Design Doc Wizard - Generate Implementation Plan] Error querying Pinecone for milestone ${milestone.milestone_id}:`, error);
                    }
                }

                // Enhance the milestone with guidance and search queries
                enhancedMilestones.push({
                    ...milestone,
                    git_must_use_milestone_branch_name: correctBranchName,
                    // Ensure title is available for display
                    title: milestoneTitle,
                    // Convert task objects to readable strings for display
                    key_tasks_and_deliverables: milestone.key_tasks_and_deliverables?.map((task: any) => {
                        if (typeof task === 'string') {
                            return task;
                        } else if (task && typeof task === 'object') {
                            const taskText = task.task || 'Task not specified';
                            const validationText = task.validation_criteria;
                            return validationText ? `${taskText} (Validation: ${validationText})` : taskText;
                        }
                        return 'Task details not available';
                    }) || [],
                    // Ensure scope_validation has proper structure
                    scope_validation: milestone.scope_validation || {
                        planned_deliverables: ['Implementation scope to be defined']
                    },
                    applicable_developer_guidance_and_best_practices: applicableDeveloperGuidanceAndBestPractices,
                });
            }
        }

        // Create the final enhanced implementation plan
        const enhancedImplementationPlan = {
            implementation_plan_title: `Implementation Plan for: ${task_details.title}`,
            main_feature_branch_name: `feature/${designDocIdentifier}`,
            ai_agent_protocol_compliance: {
                protocol_source: ai_agent_protocol ? 'custom' : 'default',
                protocol_followed: true,
                milestone_driven_approach: true,
                branch_naming_enforced: true,
                independent_milestone_integration: true,
                quality_control_integrated: true,
                scope_validation_required: true,
                pre_integration_validation_required: true
            },
            enhanced_milestones: enhancedMilestones,
            data_model_summary: implementationPlanResult.data_model_summary || "Data model details analyzed from complete design document.",
            referenced_decisions: implementationPlanResult.referenced_decisions || [],
            overall_success_metrics: design_document.success_metrics || [],
            finalized_design_doc_content_used: true,
            full_design_doc_analyzed: true
        };

        console.log('[Design Doc Wizard - Generate Implementation Plan] Implementation plan generated successfully');
        
        return NextResponse.json({
            success: true,
            implementationPlan: enhancedImplementationPlan,
            message: 'Implementation plan generated successfully with knowledge base integration'
        });

    } catch (error: any) {
        console.error('[Design Doc Wizard - Generate Implementation Plan] Error:', error);
        return NextResponse.json({ 
            error: 'Failed to generate implementation plan', 
            details: error.message 
        }, { status: 500 });
    }
} 