import { NextRequest, NextResponse } from 'next/server';
import { generatePhase2DecisionContextPrompt } from '@/analyzer/prompt.js';
import { queryRAG } from '@/orchestrator';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';
import { callClaudeLlmFor<PERSON>son } from '@/lib/llmUtils';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAdmin = createClient(supabaseUrl!, supabaseServiceRoleKey!);

interface DecisionContextRequest {
  decisionPoint: any;
  taskAnalysis: any; // Add taskAnalysis to the request
  taskDetails?: any; // Add taskDetails to check for experimental flag
  projectConstitution: any;
  repositorySlug: string;
  installationId?: string;
  isPublic: boolean;
}

interface DecisionOption {
  id: string;
  name: string;
  description: string;
  pros: string[];
  cons: string[];
  riskLevel: 'low' | 'medium' | 'high';
  complexity: 'low' | 'medium' | 'high';
  maintenanceBurden: 'low' | 'medium' | 'high';
  debuggingComplexity: string;
  architecturalAlignment: 'high' | 'medium' | 'low';
  alignmentJustification: string;
  changeScope: 'minimal' | 'moderate' | 'significant';
  conflictingDecisions?: string[];
  referencedDecisionIds?: string[];
  isBasedOnInitialProposal: boolean;
  initialProposalAssessment?: string;
  mitigations?: string[]; // Mitigation analysis for cons, only for recommended option
}

interface RelatedDecisionDetail {
  id: string;
  title: string;
  description: string;
  score: number;
  source: string;
  dev_prompt?: string;
  follows_standard_practice_reason?: string;
  domain_concepts?: string[];
  related_files?: string[];
}

interface DecisionContextResponse {
  success: boolean;
  enhancedDecisionPoint?: {
    id: string;
    title: string;
    description: string;
    options: DecisionOption[];
    contextSummary: string;
    relatedDecisionDetails: RelatedDecisionDetail[];
    recommended_option_id?: string;
    recommendation_rationale?: string;
    maintenance_analysis_summary?: string;
    architectural_alignment_summary?: string;
  };
  error?: string;
}

export async function POST(request: NextRequest) {
  const requestId = `decision-context-${Date.now()}`;
  const logPrefix = `[Design Doc Wizard - Context] [${requestId}]`;

  try {
    const body: DecisionContextRequest = await request.json();
    const { decisionPoint, taskAnalysis, taskDetails, projectConstitution, repositorySlug, installationId, isPublic } = body;

    // Validate required fields
    if (!decisionPoint || !taskAnalysis || !projectConstitution || !repositorySlug) {
      console.error(`${logPrefix} Missing required fields`, { 
        decisionPoint: !!decisionPoint, 
        taskAnalysis: !!taskAnalysis,
        projectConstitution: !!projectConstitution, 
        repositorySlug: !!repositorySlug 
      });
      return NextResponse.json({ success: false, error: 'Missing required fields' }, { status: 400 });
    }

    console.log(`${logPrefix} Getting context for decision: "${decisionPoint.title || decisionPoint.decision_point_title}"`);

    // Check if this is an experimental feature
    const isExperimental = taskDetails?.isExperimental || false;
    
    // Log experimental feature decision context generation
    if (isExperimental) {
      console.log(`${logPrefix} Generating experimental feature decision context - focusing on reversible options`);
    }

    // Get repository namespace
    const effectiveInstallationId = isPublic ? 0 : parseInt(installationId || '0', 10);
    const namespace = getRepositoryNamespace(String(effectiveInstallationId), repositorySlug);

    // --- ADDED: Fetch Deployment Constitution for Decision Context ---
    let deploymentConstitution = null;
    console.log(`${logPrefix} Fetching deployment constitution for deployment-aware decision evaluation...`);
    try {
      const { data: deploymentData, error: deploymentError } = await supabaseAdmin
        .from('deployment_constitutions')
        .select('constitution_data')
        .eq('repository_slug', repositorySlug)
        .eq('installation_id', effectiveInstallationId)
        .single();

      if (deploymentError) {
        if (deploymentError.code !== 'PGRST116') { // Not found error
          console.warn(`${logPrefix} Error fetching deployment constitution:`, deploymentError.message);
        } else {
          console.log(`${logPrefix} No deployment constitution found - decision evaluation will use general best practices`);
        }
      } else if (deploymentData) {
        deploymentConstitution = deploymentData.constitution_data;
        console.log(`${logPrefix} Successfully loaded deployment constitution - decision options will be deployment-aware`);
      }
    } catch (err) {
      console.error(`${logPrefix} Unexpected error fetching deployment constitution:`, err);
    }
    // --- END ADDED ---

    // Phase 2, Step 1: Use existing GraphContext or find related decisions (RAG)
    let pastDecisionsContext = [];

    if (decisionPoint.graphContext && decisionPoint.graphContext.clusters) {
      // Use the structured context from Phase 1 (discover-decisions)
      const { coreDecisions = [], supportingDecisions = [] } = decisionPoint.graphContext.clusters;
      pastDecisionsContext = [
        ...coreDecisions,
        ...supportingDecisions
      ];
      
      console.log(`${logPrefix} Using ${pastDecisionsContext.length} decisions from GraphContext (${coreDecisions.length} core, ${supportingDecisions.length} supporting)`);
      
      // Only do additional RAG if we don't have enough context
      if (pastDecisionsContext.length < 3) {
        console.log(`${logPrefix} Supplementing with additional RAG query (need more context)`);
        const ragResult = await queryRAG(decisionPoint, 5, { minScore: 0.4 }, namespace) as any;
        const additionalDecisions = ragResult.fullMatches || [];
        
        // Add new decisions that aren't already in our context
        const existingIds = new Set(pastDecisionsContext.map((d: any) => d.id).filter(Boolean));
        const newDecisions = additionalDecisions.filter((d: any) => d.id && !existingIds.has(d.id));
        pastDecisionsContext.push(...newDecisions);
        
        console.log(`${logPrefix} Added ${newDecisions.length} additional decisions from RAG`);
      }
    } else {
      // Fallback to current behavior if no GraphContext
      console.log(`${logPrefix} No GraphContext available, using fresh RAG query`);
      const ragResult = await queryRAG(decisionPoint, 5, { minScore: 0.4 }, namespace) as any;
      pastDecisionsContext = ragResult.fullMatches || [];
    }

    console.log(`${logPrefix} Retrieved ${pastDecisionsContext.length} past decisions for context.`);

    // Normalize and map the context to only include the fields required by the prompt.
    // This handles the two different shapes that can be returned by our data sources.
    const contextForPrompt = pastDecisionsContext.map((d: any) => {
      const source = d.metadata || d; // Handles both nested metadata and flattened objects
      return {
        id: d.id,
        title: source.title,
        implications: source.implications,
        dev_prompt: source.dev_prompt,
        follows_standard_practice_reason: source.follows_standard_practice_reason
      };
    });

    // Phase 2, Step 2: Call LLM to get enhanced context and options
    const analysisPrompt = generatePhase2DecisionContextPrompt(
      decisionPoint,
      contextForPrompt,
      taskAnalysis, // Pass the full taskAnalysis object
      projectConstitution, // Pass the complete constitution
      isExperimental, // Pass experimental flag
      deploymentConstitution // <-- ADDED: Pass deployment constitution for deployment-aware decisions
    );
    
    console.log(`${logPrefix} Generated Phase 2 analysis prompt with ${pastDecisionsContext.length} decisions.`);
    if (isExperimental) {
      console.log(`${logPrefix} Generated experimental-aware Phase 2 prompt focusing on reversible decision options`);
    }
    console.log(analysisPrompt);

    const llmResponse = await callClaudeLlmForJson(analysisPrompt, 'claude-sonnet-4-20250514');

    console.log(`${logPrefix} LLM response:`, llmResponse);
    
    // Enhance the LLM-generated options with server-side analysis (alignment, conflicts, etc.)
    const enhancedOptions = await enhanceDecisionOptions(llmResponse.options || [], pastDecisionsContext, namespace);
    
    // Add the enhanced options back to the result object
    const finalEnhancedDecisionPoint = {
      ...llmResponse,
      options: enhancedOptions,
      relatedDecisionDetails: pastDecisionsContext // Pass full context for UI
    };

    console.log(`${logPrefix} Successfully generated and enhanced options for decision.`);

    console.log(llmResponse.recommendation_rationale);
    console.log(llmResponse.recommended_option_id)
    
    return NextResponse.json({
      success: true,
      enhancedDecisionPoint: {
        id: decisionPoint.id,
        title: decisionPoint.title,
        description: decisionPoint.description,
        options: enhancedOptions,
        contextSummary: llmResponse.initial_proposal_assessment || '',
        relatedDecisionDetails: pastDecisionsContext,
        recommended_option_id: llmResponse.recommended_option_id,
        recommendation_rationale: llmResponse.recommendation_rationale,
        maintenance_analysis_summary: llmResponse.maintenance_analysis_summary,
        architectural_alignment_summary: llmResponse.architectural_alignment_summary,
      }
    });

  } catch (error: any) {
    console.error(`${logPrefix} Error getting decision context:`, error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}

async function enhanceDecisionOptions(options: any[], context: RelatedDecisionDetail[], namespace: string): Promise<DecisionOption[]> {
  const logPrefix = '[Design Doc Wizard - Context]';
  console.log(`${logPrefix} Enhancing ${options.length} decision options`);
  
  const enhancedOptions: DecisionOption[] = [];
  
  for (const option of options) {
    // Detect potential conflicts
    const conflictingDecisions = detectConflicts(option, context);
    
    // Find referenced decisions
    const referencedDecisionIds = findReferencedDecisions(option, context);
    
    enhancedOptions.push({
      id: option.id || `option-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: option.name || option.title || 'Unnamed Option',
      description: option.description || '',
      pros: Array.isArray(option.pros) ? option.pros : [],
      cons: Array.isArray(option.cons) ? option.cons : [],
      mitigations: Array.isArray(option.mitigations) ? option.mitigations : undefined,
      riskLevel: option.risk_level || assessOptionRisk(option),
      complexity: option.complexity || assessOptionComplexity(option),
      maintenanceBurden: option.maintenance_burden || 'medium',
      debuggingComplexity: option.debugging_complexity || 'moderate',
      architecturalAlignment: option.architectural_alignment || 'medium',
      alignmentJustification: option.alignment_justification || '',
      changeScope: option.change_scope || 'moderate',
      conflictingDecisions: conflictingDecisions.length > 0 ? conflictingDecisions : undefined,
      referencedDecisionIds: referencedDecisionIds.length > 0 ? referencedDecisionIds : undefined,
      isBasedOnInitialProposal: option.is_based_on_initial_proposal || false,
      initialProposalAssessment: option.initial_proposal_assessment || undefined
    });
  }
  
  return enhancedOptions;
}

function detectConflicts(option: any, context: RelatedDecisionDetail[]): string[] {
  const conflicts: string[] = [];
  
  // Simple conflict detection based on contradictory keywords
  const optionText = `${option.name} ${option.description}`.toLowerCase();
  
  for (const decision of context) {
    const decisionText = `${decision.title} ${decision.description}`.toLowerCase();
    
    // Look for contradictory patterns
    if (optionText.includes('synchronous') && decisionText.includes('asynchronous')) {
      conflicts.push(decision.title);
    }
    if (optionText.includes('sql') && decisionText.includes('nosql')) {
      conflicts.push(decision.title);
    }
    // Add more conflict detection rules as needed
  }
  
  return conflicts;
}

function findReferencedDecisions(option: any, context: RelatedDecisionDetail[]): string[] {
  const referenced: string[] = [];
  
  for (const decision of context) {
    if (decision.score && decision.score > 0.7) { // High relevance threshold
      referenced.push(decision.id);
    }
  }
  
  return referenced.slice(0, 3); // Limit to top 3 most relevant
}

function assessOptionRisk(option: any): 'low' | 'medium' | 'high' {
  const riskKeywords = {
    high: ['experimental', 'bleeding edge', 'unproven', 'complex integration'],
    medium: ['new', 'recent', 'moderate complexity'],
    low: ['established', 'proven', 'simple', 'standard']
  };
  
  const optionText = `${option.name} ${option.description}`.toLowerCase();
  
  for (const [level, keywords] of Object.entries(riskKeywords)) {
    for (const keyword of keywords) {
      if (optionText.includes(keyword)) {
        return level as 'low' | 'medium' | 'high';
      }
    }
  }
  
  return 'medium'; // Default
}

function assessOptionComplexity(option: any): 'low' | 'medium' | 'high' {
  const complexityKeywords = {
    high: ['multiple services', 'distributed', 'microservices', 'complex'],
    medium: ['integration', 'moderate', 'service'],
    low: ['simple', 'straightforward', 'basic', 'minimal']
  };
  
  const optionText = `${option.name} ${option.description}`.toLowerCase();
  
  for (const [level, keywords] of Object.entries(complexityKeywords)) {
    for (const keyword of keywords) {
      if (optionText.includes(keyword)) {
        return level as 'low' | 'medium' | 'high';
      }
    }
  }
  
  return 'medium'; // Default
} 