import { NextRequest, NextResponse } from 'next/server';
import { generatePhase3FullDesignDocPrompt } from '@/analyzer/prompt.js';
import { queryPinecone, getVectorsByIds } from '@/lib/pineconeUtils';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';
import { callClaudeLlmForJson } from '@/lib/llmUtils';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAdmin = createClient(supabaseUrl!, supabaseServiceRoleKey!);

// Local LLM function using Anthropic API
async function callLLM(prompt: string, model: string = 'claude-sonnet-4-20250514'): Promise<string> {
  const anthropicApiKey = process.env.ANTHROPIC_API_KEY;
  if (!anthropicApiKey) {
    throw new Error('ANTHROPIC_API_KEY environment variable is not set');
  }

  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': anthropicApiKey,
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify({
      model,
      max_tokens: 8000, // Larger token limit for full design doc
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Anthropic API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const data = await response.json();
  return data.content[0].text;
}

interface DecisionPoint {
  id: string;
  title: string;
  description: string;
  category: string;
  priority: string;
  selectedOption?: string;
  rationale?: string;
  options?: Array<{
    id: string;
    name: string;
    description: string;
    pros: string[];
    cons: string[];
    riskLevel: string;
    complexity: string;
  }>;
  relatedDecisions?: any[];
}

interface TaskDetails {
  title: string;
  description: string;
  initialIdeas?: string;
  isExperimental?: boolean;
}

interface GenerateDesignDocRequest {
  taskDetails: {
    title: string;
    description: string;
    initialIdeas?: string;
  };
  taskAnalysis: any; // <-- ADD THIS
  decisionPoints: Array<{
    id: string;
    title: string;
    description: string;
    selectedOption?: string;
    rationale?: string;
    options?: Array<{
      id: string;
      name: string;
      description: string;
      pros: string[];
      cons: string[];
      riskLevel: 'low' | 'medium' | 'high';
      complexity: 'low' | 'medium' | 'high';
    }>;
    relatedDecisions: Array<{
      id: string;
      title: string;
      summary: string;
      relevanceScore: number;
    }>;
  }>;
  projectConstraints?: Array<{
    id: string;
    title: string;
    description: string;
    source: 'user' | 'removed-decision' | 'constitution';
    createdAt: string;
  }>;
  projectConstitution?: any; // Add project constitution
  repositorySlug: string;
  installationId?: string;
  isPublic: boolean;
}

interface GenerateDesignDocResponse {
  success: boolean;
  designDoc?: any;
  error?: string;
}

export async function POST(request: NextRequest) {
  const requestId = `generate-${Date.now()}`;
  const logPrefix = `[Design Doc Wizard - Generate] [${requestId}]`;
  
  try {
    const { 
      taskDetails, 
      taskAnalysis, // <-- USE THIS
      decisionPoints, 
      projectConstraints, 
      projectConstitution, // Add project constitution
      repositorySlug, 
      installationId, 
      isPublic 
    } = await request.json();

    if (!taskDetails?.title || !taskDetails?.description || !decisionPoints?.length) {
      console.error(`${logPrefix} Missing required fields`);
      return NextResponse.json({ 
        success: false, 
        error: 'Missing required fields: taskDetails, decisionPoints' 
      }, { status: 400 });
    }

    console.log(`${logPrefix} Starting design document generation for: "${taskDetails.title}"`);
    console.log(`${logPrefix} Repository: ${repositorySlug}, Decisions: ${decisionPoints.length}`);

    // Get repository namespace
    const effectiveInstallationId = isPublic ? 0 : parseInt(installationId || '0', 10);
    const namespace = getRepositoryNamespace(effectiveInstallationId, repositorySlug);
    console.log(`${logPrefix} Using Pinecone namespace: ${namespace}`);

    // Fetch project constitution if not provided
    let constitution = projectConstitution;
    if (!constitution && repositorySlug) {
      console.log(`${logPrefix} Project constitution not provided, fetching from database...`);
      try {
        const { data: constitutionData, error: constitutionError } = await supabaseAdmin
          .from('project_constitutions')
          .select('constitution_data')
          .eq('repository_slug', repositorySlug)
          .single();

        if (constitutionError) {
          if (constitutionError.code !== 'PGRST116') { // Not found error
            console.warn(`${logPrefix} Error fetching project constitution:`, constitutionError.message);
          }
        } else if (constitutionData) {
          constitution = constitutionData.constitution_data;
          console.log(`${logPrefix} Successfully loaded project constitution from database`);
        }
      } catch (err) {
        console.error(`${logPrefix} Unexpected error fetching project constitution:`, err);
      }
    }

    // --- ADDED: Fetch Deployment Constitution ---
    let deploymentConstitution = null;
    if (repositorySlug) {
      console.log(`${logPrefix} Fetching deployment constitution for enhanced technical guidance...`);
      try {
        const { data: deploymentData, error: deploymentError } = await supabaseAdmin
          .from('deployment_constitutions')
          .select('constitution_data')
          .eq('repository_slug', repositorySlug)
          .eq('installation_id', effectiveInstallationId)
          .single();

        if (deploymentError) {
          if (deploymentError.code !== 'PGRST116') { // Not found error
            console.warn(`${logPrefix} Error fetching deployment constitution:`, deploymentError.message);
          } else {
            console.log(`${logPrefix} No deployment constitution found - design will use general best practices`);
          }
        } else if (deploymentData) {
          deploymentConstitution = deploymentData.constitution_data;
          console.log(`${logPrefix} Successfully loaded deployment constitution - AI will provide deployment-aware guidance`);
        }
      } catch (err) {
        console.error(`${logPrefix} Unexpected error fetching deployment constitution:`, err);
      }
    }
    // --- END ADDED ---

    // Inlined phase 1 data reconstruction
    const phase1Data = {
      critical_technical_decision_points: decisionPoints.map((dp: DecisionPoint) => ({
        id: dp.id,
        title: dp.title,
        description: dp.description,
        options: dp.options?.map(opt => opt.name),
        selected_option: dp.selectedOption,
        rationale: dp.rationale
      }))
    };
    
    // Inlined phase 2 data reconstruction
    const phase2Data = {
      analyzed_decision_points: decisionPoints.map((dp: DecisionPoint) => {
        const selectedOption = dp.options?.find(opt => opt.id === dp.selectedOption);
        return {
          decision_point_id: dp.id,
          decision_point_title: dp.title,
          decision_point_description: dp.description,
          selected_approach: selectedOption,
          rationale_for_selection: dp.rationale || 'No rationale provided',
          related_architectural_context: (dp.relatedDecisions || []).map((rd: any) => ({
            id: rd.id,
            title: rd.title,
            summary: rd.summary,
            relevance_score: rd.relevanceScore
          })),
          risk_assessment: selectedOption?.riskLevel || 'medium',
          implementation_complexity: selectedOption?.complexity || 'medium'
        };
      })
    };
    
    // Gather details for referenced past decisions
    const fullPastDecisionsDetails = await gatherReferencedDecisionDetails(decisionPoints, namespace);
    
    // Generate the final design doc prompt with project constitution
    const designDocPrompt = generatePhase3FullDesignDocPrompt(
      taskDetails,
      taskAnalysis, // <-- Using the real one now
      phase1Data,
      phase2Data.analyzed_decision_points,
      fullPastDecisionsDetails,
      projectConstraints || [],
      constitution, // Pass the project constitution for strategic guidance
      deploymentConstitution // <-- ADDED: Pass deployment constitution for technical guidance
    );

    console.log(`${logPrefix} Generated Phase 3 prompt (length: ${designDocPrompt.length})`);

    // Call LLM for design document generation
    const designDocResult = await callClaudeLlmForJson(designDocPrompt, 'claude-sonnet-4-20250514');
    console.log(`${logPrefix} LLM Phase 3 result keys:`, Object.keys(designDocResult));

    const finalDoc = enhanceDesignDocWithWizardData(designDocResult, decisionPoints, taskDetails);

    return NextResponse.json({
      success: true,
      designDoc: finalDoc
    });

  } catch (error: any) {
    console.error(`[API Generate] Error:`, error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}

async function gatherReferencedDecisionDetails(decisionPoints: DecisionPoint[], namespace: string) {
  const allRelatedDecisionIds = new Set<string>();
  decisionPoints.forEach(dp => {
    if (dp.relatedDecisions) {
      dp.relatedDecisions.forEach((rd: any) => allRelatedDecisionIds.add(rd.id));
    }
  });

  if (allRelatedDecisionIds.size === 0) {
    return [];
  }

  // Fetch full decision details from Pinecone
  const fullPastDecisionsDetails = await getVectorsByIds(Array.from(allRelatedDecisionIds), namespace);
  console.log(`[API Generate] Retrieved ${fullPastDecisionsDetails.length} full decision details`);
  return fullPastDecisionsDetails;
}

function enhanceDesignDocWithWizardData(designDoc: any, decisionPoints: DecisionPoint[], taskDetails: TaskDetails) {
  // Logic to merge wizard data into the generated doc
  // This could involve adding rationales, selected options, etc., into a more structured format if needed
  return {
    ...designDoc,
    // Add any additional structured data from the wizard that you want to persist
    wizard_metadata: {
      task_title: taskDetails.title,
      task_description: taskDetails.description,
      decision_points_summary: decisionPoints.map(dp => ({
        id: dp.id,
        title: dp.title,
        selected_option_id: dp.selectedOption,
        rationale: dp.rationale
      }))
    }
  };
} 