import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

export async function POST(request: NextRequest) {
  try {
    const { taskDetails, strategicAssessment, projectConstitution, userJourneys } = await request.json();

    // Validate required fields
    if (!taskDetails || !strategicAssessment || !projectConstitution) {
      return NextResponse.json({ 
        error: 'Task details, strategic assessment, and project constitution are required' 
      }, { status: 400 });
    }

    // Generate the prompt for minimal scope analysis
    const prompt = generateMinimalScopePrompt(taskDetails, strategicAssessment, projectConstitution, userJourneys);

    // Create Anthropic client
    const anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });

    // Call Claude API
    const message = await anthropic.messages.create({
      model: 'claude-sonnet-4-20250514',
      max_tokens: 2000,
      temperature: 0.1,
      messages: [{ role: 'user', content: prompt }],
    });

    // Parse the response
    const responseText = message.content[0].type === 'text' ? message.content[0].text : '';
    
    let minimalScopeResponse;
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        minimalScopeResponse = JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Error parsing Claude response:', parseError);
      return NextResponse.json({ error: 'Failed to parse AI response' }, { status: 500 });
    }

    if (!minimalScopeResponse) {
      return NextResponse.json({ error: 'No valid response from AI' }, { status: 500 });
    }

    return NextResponse.json({
      minimalScopeDescription: minimalScopeResponse.minimal_scope_description,
      rationale: minimalScopeResponse.rationale
    });

  } catch (error) {
    console.error('Error in generate-minimal-scope:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function generateMinimalScopePrompt(taskDetails: any, strategicAssessment: any, projectConstitution: any, userJourneys: any) {
  // Format user journeys for the prompt
  const userJourneysString = userJourneys && userJourneys.length > 0
    ? userJourneys.map((journey: any) => `
#### Journey: ${journey.title}
- **Actor:** ${journey.userRole}
- **Priority:** ${journey.priority || 'Not specified'}
- **Steps:**
  ${journey.steps.map((step: string) => `- ${step}`).join('\n      ')}
`).join('\n')
    : "No user journeys were provided.";

  return `You are a pragmatic Product Manager and Technical Lead. Your task is to analyze a feature that was recommended for ${strategicAssessment.recommendation} due to complexity concerns, but has ${strategicAssessment.complexity_assessment?.user_value_delivered} user value.

Your goal is to identify a minimal scope version that could deliver core user value while avoiding the complexity issues that caused the ${strategicAssessment.recommendation.toLowerCase()} recommendation.

**Original Task:**
- **Title:** ${taskDetails.title}
- **Description:** ${taskDetails.description}

**Verified User Journeys:**
${userJourneysString}

**Why it was recommended for ${strategicAssessment.recommendation}:**
- **Strategic Rationale:** ${strategicAssessment.strategic_rationale}
- **Complexity Issues:** ${strategicAssessment.complexity_assessment?.complexity_justification}
- **Strategic Concerns:** ${strategicAssessment.alignment_analysis?.strategic_concerns?.join(', ') || 'None specified'}

**Project Context:**
- **Company Stage:** ${projectConstitution.companyStage}
- **Project Type:** ${projectConstitution.projectType}
- **Top Priorities:** ${(projectConstitution.priorities?.[0]+","+projectConstitution.priorities?.[1]+","+projectConstitution.priorities?.[2]) || 'time-to-market'}

**Your Analysis Task:**

1. **Identify Core User Value:** Based on the user journeys above, what is the essential user benefit this feature would provide? Which journeys represent the most critical user workflows?
2. **Isolate Complexity Sources:** What specific aspects are causing the complexity/maintenance concerns?
3. **Design Minimal Alternative:** Propose a reduced scope that:
   - Supports the most critical user journeys (prioritize "Critical" priority journeys)
   - Delivers 60-80% of the core user value identified in the journeys
   - Avoids the main complexity sources identified
   - Aligns with the project's current stage and priorities
   - Could be safely implemented without the risks that caused the ${strategicAssessment.recommendation.toLowerCase()} recommendation
   - May defer some user journeys or simplify their implementation

**Output Format:**

Respond with a JSON object:

{
  "minimal_scope_description": "A clear, detailed description of the minimal scope alternative that can be appended to the task description. This should be specific enough to guide implementation while avoiding the complexity issues. Reference which user journeys are supported, which are deferred, and any simplifications made. Format as a clear scope definition that could be added to a GitHub issue.",
  "rationale": "Brief explanation of how this minimal scope addresses the ${strategicAssessment.recommendation.toLowerCase()} concerns while preserving user value. Explain what journeys/features were removed, what was simplified, and why this version is safer to implement while still serving core user needs."
}

**Guidelines:**
- Be specific about which user journeys are included/excluded in the minimal scope
- Focus on delivering immediate user value with the least risk
- Consider if this could be a stepping stone to support all journeys later
- Ensure the minimal scope aligns with the project's current capabilities and priorities
- If no reasonable minimal scope exists that serves any meaningful user journeys, explain why in the rationale and set minimal_scope_description to null

Provide ONLY the JSON object in your response.`;
} 