import { NextRequest, NextResponse } from 'next/server';
import { 
  generateTaskAnalysisPrompt,
  generatePhase1GoalsAndDecisionsPrompt, 
  generateSearchQueriesForDecisionPointPrompt,
} from '@/analyzer/prompt.js';
import { queryPinecone } from '@/lib/pineconeUtils';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';
import { callClaudeLlmFor<PERSON>son } from '@/lib/llmUtils';
import { getGraphContextForDecisions, getIntelligentSeedDecisions, GraphContext } from '@/lib/knowledgeGraphUtils';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

interface DiscoverDecisionsRequest {
  taskDetails: {
    title: string;
    description: string;
    initialIdeas?: string;
    isExperimental?: boolean;
  };
  taskAnalysis: TaskAnalysis;
  projectConstitution: any; // Can be a complex object
  repositorySlug: string;
  installationId?: string;
  isPublic: boolean;
}

interface DecisionPoint {
  id: string;
  title: string;
  description: string;
  category: 'architecture' | 'technology' | 'data' | 'integration' | 'security' | 'performance';
  priority: 'high' | 'medium' | 'low';
  graphContext: GraphContext;
  searchQueries: string[];
}

interface RelatedDecision {
  id: string;
  title: string;
  summary: string;
  relevanceScore: number;
  implications: string[];
  guidance?: string;
}

interface TaskAnalysis {
  summary: string;
  functional_requirements: string[];
  implied_NFRs: { nfr: string; evidence: string }[];
  core_architectural_characteristics: string[];
}

interface DiscoverDecisionsResponse {
  success: boolean;
  decisionPoints: DecisionPoint[];
  goals: string[];
  nonGoals: string[];
  taskAnalysis?: TaskAnalysis;
  strategicAnalysis?: {
    core_vs_deferrable_analysis?: any;
    strategic_recommendations?: any;
  };
  error?: string;
}

export async function POST(request: NextRequest) {
  const requestId = `discover-${Date.now()}`;
  const logPrefix = `[Design Doc Wizard - Discover] [${requestId}]`;

  try {
    const { taskDetails, taskAnalysis, projectConstitution, repositorySlug, installationId, isPublic } = await request.json();

    // Validate required fields
    if (!taskDetails || !taskAnalysis || !projectConstitution || !repositorySlug) {
      console.error(`${logPrefix} Missing required fields`, { taskDetails: !!taskDetails, taskAnalysis: !!taskAnalysis, projectConstitution: !!projectConstitution, repositorySlug: !!repositorySlug });
      return NextResponse.json({ success: false, error: 'Missing required fields' }, { status: 400 });
    }

    console.log(`${logPrefix} Discovering decisions for task: "${taskDetails.title}"`);
    console.log(`${logPrefix} Repository: ${repositorySlug}`);
    console.log(`${logPrefix} Constitution provided: ${!!projectConstitution}`);
    console.log(`${logPrefix} Task Analysis summary: ${taskAnalysis.summary}`);

    // Check if this is an experimental feature
    const isExperimental = taskDetails.isExperimental || false;
    
    // Log experimental feature decision discovery
    if (isExperimental) {
      console.log(`${logPrefix} Performing experimental feature decision discovery - focusing on reversible, feature-flag-friendly decisions`);
    }

    // Header for private repositories
    const headers = isPublic ? {} : {
      get(name: string) {
        if (name === 'x-github-installation-id') {
          return installationId;
        }
        return null;
      }
    };

    // Get repository namespace
    const effectiveInstallationId = isPublic ? 0 : parseInt(installationId || '0', 10);
    const namespace = getRepositoryNamespace(String(effectiveInstallationId), repositorySlug);
    console.log(`${logPrefix} Using Pinecone namespace: ${namespace}`);

    // Task analysis is now passed in from the client, so we skip generating it here.
    console.log(`${logPrefix} Using pre-verified task analysis. Summary:`, taskAnalysis.summary);

    // Pass the complete constitution directly to the Phase 1 prompt generator
    const taskDetailsWithExperimental = {
      ...taskDetails,
      isExperimental: isExperimental
    };
    
    const phase1Prompt = generatePhase1GoalsAndDecisionsPrompt(
      taskDetailsWithExperimental,
      taskAnalysis,
      projectConstitution // Pass complete constitution instead of filtered version
    );
    console.log(`${logPrefix} Generated Phase 1 prompt with task analysis and COMPLETE constitution context (length: ${phase1Prompt.length})`);
    if (isExperimental) {
      console.log(`${logPrefix} Generated experimental-aware Phase 1 prompt focusing on reversible decisions`);
    }
    
    const llmPhase1Result = await callClaudeLlmForJson(phase1Prompt, 'claude-sonnet-4-20250514');
    console.log(`${logPrefix} LLM Phase 1 raw result:`, JSON.stringify(llmPhase1Result, null, 2));

    if (!llmPhase1Result.critical_technical_decision_points) {
      throw new Error('Phase 1 LLM output is missing required field: critical_technical_decision_points.');
    }

    // Extract strategic analysis fields
    const strategicAnalysis = {
      core_vs_deferrable_analysis: llmPhase1Result.core_vs_deferrable_analysis || null,
      strategic_recommendations: llmPhase1Result.strategic_recommendations || null,
    };
    console.log(`${logPrefix} Extracted strategic analysis:`, strategicAnalysis);

    // Ensure decision points have IDs (matching designDocProcessor.ts)
    const critical_technical_decision_points = llmPhase1Result.critical_technical_decision_points.map((dp: any, index: number) => ({
      ...dp,
      id: dp.id || `tdp_${String(index + 1).padStart(3, '0')}`
    }));

    console.log(`${logPrefix} LLM identified ${critical_technical_decision_points.length} critical decision points`);

    // Phase 2: For each decision point, find related architectural context
    const enhancedDecisionPoints: DecisionPoint[] = [];

    for (const decisionPoint of critical_technical_decision_points) {
      console.log(`${logPrefix} Finding context for decision: "${decisionPoint.decision_point_title}" (ID: ${decisionPoint.id})`);
      
      // Generate optimized search queries using LLM (matching designDocProcessor approach)
      const decisionPointDetailsForQueryGen = {
        title: decisionPoint.decision_point_title,
        description: decisionPoint.decision_point_description,
        potential_impact_areas: decisionPoint.category ? [decisionPoint.category] : ['architecture'], // Map category to impact areas
      };

      const overallTaskContext = {
        taskTitle: taskDetails.title,
        taskDescription: taskDetails.description
      };

      const searchQueriesPrompt = generateSearchQueriesForDecisionPointPrompt(decisionPointDetailsForQueryGen, overallTaskContext);
      const searchQueriesResult = await callClaudeLlmForJson(searchQueriesPrompt, 'claude-sonnet-4-20250514');
      const searchQueries = searchQueriesResult.search_queries || [];

      console.log(`${logPrefix} Generated ${searchQueries.length} search queries for decision "${decisionPoint.id}"`);

      // Use the generated search queries to query Pinecone and collect related decisions
      const allRelatedDecisions = new Map<string, any>(); // Use Map to avoid duplicates
      
      for (const query of searchQueries) {
        console.log(`${logPrefix} Querying Pinecone with: "${query}"`);
        
        try {
          const ragResult = await queryPinecone(query, namespace, 3, 0.4);
          
          // Smart filtering: If we have decisions with 0.5+ similarity, only use those
          // Otherwise, use all decisions that meet the 0.4 threshold
          const highQualityResults = ragResult.filter(decision => decision.score && decision.score >= 0.5);
          const resultsToUse = highQualityResults.length > 0 ? highQualityResults : ragResult;
          
          console.log(`${logPrefix} Query "${query}": Found ${ragResult.length} results (0.4+), using ${resultsToUse.length} results (${highQualityResults.length > 0 ? '0.5+ quality filter applied' : '0.4+ threshold used'})`);
          
          // Add to our collection, using decision ID as key to avoid duplicates
          for (const decision of resultsToUse) {
            if (decision.id && !allRelatedDecisions.has(decision.id)) {
              allRelatedDecisions.set(decision.id, decision);
            }
          }
        } catch (error) {
          console.warn(`${logPrefix} Error querying Pinecone with query "${query}":`, error);
        }
      }

      const relatedDecisions = Array.from(allRelatedDecisions.values());
      console.log(`${logPrefix} Found ${relatedDecisions.length} unique related decisions for decision "${decisionPoint.id}"`);

      // Enhanced GraphContext with populated related decisions
      const graphContext: GraphContext = {
        clusters: { 
          coreDecisions: relatedDecisions.slice(0, 3), // Top 3 most relevant
          supportingDecisions: relatedDecisions.slice(3, 6), // Next 3
          constraintDecisions: [], 
          impactedDecisions: [], 
          patternDecisions: [] 
        },
        edges: [],
        seedDecisionIds: relatedDecisions.map(d => d.id),
        analysisNarrative: `Found ${relatedDecisions.length} related decisions through ${searchQueries.length} search queries.`
      };

      const enhancedDecisionPoint: DecisionPoint = {
        id: decisionPoint.id,
        title: decisionPoint.decision_point_title,
        description: decisionPoint.decision_point_description,
        category: decisionPoint.category,
        priority: decisionPoint.priority,
        graphContext,
        searchQueries: searchQueries
      };

      enhancedDecisionPoints.push(enhancedDecisionPoint);
    }

    console.log(`${logPrefix} Enhanced ${enhancedDecisionPoints.length} decision points with context`);

    return NextResponse.json({
      success: true,
      decisionPoints: enhancedDecisionPoints,
      goals: [], // Goals will be determined later in the process
      nonGoals: [], // Non-goals will be determined later in the process
      taskAnalysis: taskAnalysis,
      strategicAnalysis: strategicAnalysis // Include strategic analysis in response
    });

  } catch (error: any) {
    console.error(`${logPrefix} Error:`, error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
} 