import { createSupabaseAdminClient } from '../../../../lib/supabase-server';
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { createSupabaseServerClient } from '../../../../lib/supabase-server';

export async function POST(req: NextRequest) {
  const supabase = createSupabaseAdminClient();
  const supabaseUser = createSupabaseServerClient();
  const { repo, owner, installationId } = await req.json();

  const { data: { user } } = await supabaseUser.auth.getUser();
  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Create repository slug in the format used throughout the system
  const repositorySlug = `${owner}/${repo}`;

  const { data, error } = await supabase
    .from('design_doc_sessions')
    .insert({
      user_id: user.id,
      repository_slug: repositorySlug,
      installation_id: parseInt(installationId, 10) || 0,
      wizard_state: {
        taskDetails: {},
        decisionPoints: [],
        generatedDoc: null,
      },
    })
    .select('id')
    .single();

  if (error) {
    console.error('Error creating design doc session:', error);
    return new Response(JSON.stringify({ error: error.message }), { status: 500 });
  }

  return NextResponse.json({ sessionId: data.id });
}

export async function PUT(req: NextRequest) {
  const supabase = createSupabaseAdminClient();
  const supabaseUser = createSupabaseServerClient();
  const { sessionId, wizardState, title, githubIssueUrl } = await req.json();

  if (!sessionId) {
    return new Response(JSON.stringify({ error: 'sessionId is required' }), { status: 400 });
  }

  const { data: { user } } = await supabaseUser.auth.getUser();
  if (!user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const updateData: any = { wizard_state: wizardState };
  if (title) {
    updateData.title = title;
  }
  if (githubIssueUrl) {
    updateData.github_issue_url = githubIssueUrl;
  }


  const { data, error } = await supabase
    .from('design_doc_sessions')
    .update(updateData)
    .eq('id', sessionId)
    .eq('user_id', user.id);

  if (error) {
    console.error('Error updating design doc session:', error);
    return new Response(JSON.stringify({ error: error.message }), { status: 500 });
  }

  return NextResponse.json({ success: true });
} 