import { createSupabaseAdminClient } from '../../../../../lib/supabase-server';
import { NextResponse } from 'next/server';

interface SessionData {
  wizard_state: any;
  title: string | null;
  github_issue_url: string | null;
  created_at: string;
  updated_at: string;
  repository_slug: string;
  installation_id: number;
}

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  const supabase = createSupabaseAdminClient();
  const sessionId = params.id;

  if (!sessionId) {
    return new Response(JSON.stringify({ error: 'sessionId is required' }), { status: 400 });
  }

  console.log(`[SessionAPI] Fetching session ${sessionId}`);

  const { data, error } = await supabase
    .from('design_doc_sessions')
    .select('wizard_state, title, github_issue_url, created_at, updated_at, repository_slug, installation_id')
    .eq('id', sessionId)
    .single();

  console.log(`[SessionAPI] Query result for ${sessionId}:`, { data: data ? 'found' : 'null', error: error?.message });
  
  if (data?.wizard_state) {
    console.log(`[SessionAPI] Wizard state keys:`, Object.keys(data.wizard_state));
    console.log(`[SessionAPI] Current step:`, (data.wizard_state as any).currentStep);
  }

  if (error || !data) {
    console.error('Error fetching design doc session:', error);
    return new Response(JSON.stringify({ error: 'Session not found' }), { status: 404 });
  }

  const sessionData = data as SessionData;

  // Parse repository slug to get owner and name
  const [owner, name] = sessionData.repository_slug.split('/');

  // Transform the data to match the expected structure
  const responseData = {
    ...sessionData,
    repositories: {
      owner,
      name
    }
  };

  // Add cache control headers to prevent caching
  const response = NextResponse.json(responseData);
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');
  
  return response;
} 