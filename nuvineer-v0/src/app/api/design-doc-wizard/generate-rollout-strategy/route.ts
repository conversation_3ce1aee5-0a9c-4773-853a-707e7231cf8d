import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { queryPinecone } from '@/lib/pineconeUtils';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';
import { generateFeatureRolloutStrategyPrompt } from '@/analyzer/prompt';

// Helper function to call Claude LLM for JSON responses
async function callClaudeLlmForJson(prompt: string): Promise<any> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.ANTHROPIC_API_KEY!,
            'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
            model: 'claude-sonnet-4-20250514',
            max_tokens: 4000,
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ]
        })
    });

    if (!response.ok) {
        throw new Error(`LLM API call failed: ${response.statusText}`);
    }

    const llmResponse = await response.json();
    const responseText = llmResponse.content[0].text;
    
    // Parse the JSON response from the LLM
    try {
        // Extract JSON from the response (in case there's extra text)
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            return JSON.parse(jsonMatch[0]);
        } else {
            throw new Error('No valid JSON found in LLM response');
        }
    } catch (parseError) {
        console.error('Error parsing LLM response:', parseError);
        throw new Error('Failed to parse JSON from LLM response');
    }
}

// Enhanced function to get rollout context from knowledge base
async function getRolloutRelevantContext(
    designDoc: any, 
    taskDetails: any, 
    repositorySlug: string, 
    installationId: string | number
): Promise<string[]> {
    const jobNamespace = getRepositoryNamespace(Number(installationId), repositorySlug);
    const retrievedContext: string[] = [];
    
    // Generate targeted search queries for rollout strategy context
    const searchQueries = [
        // Technical risk context
        `rollout strategy ${designDoc.title} deployment risks`,
        `production deployment ${taskDetails.title} monitoring`,
        `feature flag ${designDoc.title} incremental rollout`,
        // Past incidents and lessons learned
        `incident rollback production deployment failure`,
        `monitoring alerts ${repositorySlug} performance degradation`,
        // Experimental and A/B testing context
        ...(taskDetails.isExperimental ? [
            `experiment evaluation A/B testing statistical significance`,
            `feature experiment rollout success criteria`
        ] : []),
        // Data model and security context
        ...(designDoc.high_level_design?.data_model_changes && 
            designDoc.high_level_design.data_model_changes !== 'N/A' ? [
            `database migration rollout strategy data consistency`,
            `schema changes production deployment rollback`
        ] : []),
        ...(designDoc.high_level_design?.security_considerations && 
            designDoc.high_level_design.security_considerations !== 'N/A' ? [
            `security rollout deployment authentication authorization`,
            `security incident production deployment vulnerability`
        ] : [])
    ];

    const uniqueContexts = new Set<string>();
    
    for (const query of searchQueries) {
        try {
            console.log(`[Rollout Strategy] Searching: "${query}" in namespace ${jobNamespace}`);
            const pineconeResults = await queryPinecone(query, jobNamespace, 3, 0.4);
            
            // Smart filtering: If we have decisions with 0.5+ similarity, only use those
            // Otherwise, use all decisions that meet the 0.4 threshold
            const highQualityResults = pineconeResults.filter(match => match.score && match.score >= 0.5);
            const resultsToUse = highQualityResults.length > 0 ? highQualityResults : pineconeResults;
            
            console.log(`[Rollout Strategy] Query "${query}": Found ${pineconeResults.length} results (0.4+), using ${resultsToUse.length} results (${highQualityResults.length > 0 ? '0.5+ quality filter applied' : '0.4+ threshold used'})`);
            
            for (const match of resultsToUse) {
                if (match.metadata?.text || match.metadata?.content) {
                    const contextText = match.metadata.text || match.metadata.content;
                    const contextWithMetadata = `
**Context Type:** ${match.metadata.type || 'Unknown'}
**Source:** ${match.metadata.title || match.metadata.file_path || 'Unknown source'}
**Relevance Score:** ${match.score?.toFixed(3) || 'N/A'}
**Content:** ${contextText}`;
                    
                    if (!uniqueContexts.has(contextText)) {
                        uniqueContexts.add(contextText);
                        retrievedContext.push(contextWithMetadata);
                    }
                }
            }
        } catch (error) {
            console.warn(`[Rollout Strategy] Error querying Pinecone for "${query}":`, error);
        }
    }

    console.log(`[Rollout Strategy] Retrieved ${retrievedContext.length} unique context items`);
    return retrievedContext;
}

export async function POST(req: NextRequest) {
    console.log('[Design Doc Wizard - Generate Rollout Strategy] Request received');
    
    try {
        const body = await req.json();
        const { 
            design_document,
            task_details, 
            project_constitution,
            repository_slug, 
            installation_id,
            is_experimental = false,
            // Legacy support for old format
            designDoc,
            taskDetails
        } = body;
        
        // Support both new and legacy parameter formats
        const finalDesignDoc = design_document || designDoc;
        const finalTaskDetails = task_details || taskDetails;
        const finalRepoSlug = repository_slug || finalTaskDetails?.repositorySlug;
        const finalInstallationId = installation_id || finalTaskDetails?.installationId;
        const isExperimentalFlag = is_experimental || finalTaskDetails?.isExperimental || false;
        const finalProjectConstitution = project_constitution;
        
        console.log('[Design Doc Wizard - Generate Rollout Strategy] Request parameters:', {
            design_document_title: finalDesignDoc?.title,
            task_title: finalTaskDetails?.title,
            repository_slug: finalRepoSlug,
            installation_id: finalInstallationId,
            is_experimental: isExperimentalFlag,
            project_constitution: !!finalProjectConstitution // Log if constitution is present
        });

        if (!finalDesignDoc || !finalTaskDetails) {
            console.log('[Design Doc Wizard - Generate Rollout Strategy] Missing required parameters');
            return NextResponse.json({ 
                error: 'design_document and task_details are required' 
            }, { status: 400 });
        }
        
        // Create Supabase client for auth
        const cookieStore = cookies();
        const supabase = createServerClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            {
                cookies: {
                    get(name: string) {
                        return cookieStore.get(name)?.value;
                    },
                },
            }
        );

        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session) {
            console.log('[Design Doc Wizard - Generate Rollout Strategy] Authentication required');
            return NextResponse.json(
                { error: 'Authentication required' },
                { status: 401 }
            );
        }

        console.log('[Design Doc Wizard - Generate Rollout Strategy] User authenticated:', session.user.id);

        // Get comprehensive context from knowledge base
        const retrievedContext = await getRolloutRelevantContext(
            finalDesignDoc, 
            finalTaskDetails, 
            finalRepoSlug, 
            finalInstallationId
        );

        // Generate the action-oriented rollout strategy prompt
        const prompt = generateFeatureRolloutStrategyPrompt(
            finalDesignDoc, 
            finalTaskDetails,
            finalProjectConstitution,
            isExperimentalFlag, 
            retrievedContext
        );

        console.log('[Design Doc Wizard - Generate Rollout Strategy] Calling LLM for rollout strategy generation...');
        
        // Generate rollout strategy using Claude
        const rolloutStrategyResult = await callClaudeLlmForJson(prompt);

        // Validate that we got a proper rollout strategy
        if (!rolloutStrategyResult.rollout_strategy) {
            throw new Error('Invalid response format: missing rollout_strategy object');
        }

        const rolloutStrategy = rolloutStrategyResult.rollout_strategy;

        // Enhance the rollout strategy with metadata
        const enhancedRolloutStrategy = {
            ...rolloutStrategy,
            metadata: {
                generated_at: new Date().toISOString(),
                design_document_title: finalDesignDoc.title,
                task_title: finalTaskDetails.title,
                repository_slug: finalRepoSlug,
                installation_id: finalInstallationId,
                is_experimental: isExperimentalFlag,
                context_sources_count: retrievedContext.length,
                risk_level: rolloutStrategy.risk_assessment?.change_risk_level || 'UNKNOWN',
                incremental_rollout_possible: rolloutStrategy.rollout_feasibility?.incremental_rollout_possible || false
            }
        };

        // Store the rollout strategy in Pinecone for future reference
        if (retrievedContext.length > 0) {  // Only if we have Pinecone access
            try {
                const jobNamespace = getRepositoryNamespace(Number(finalInstallationId), finalRepoSlug);
                
                // Create a summary for embedding
                const rolloutSummary = `
Rollout Strategy: ${finalDesignDoc.title}
Risk Level: ${rolloutStrategy.risk_assessment?.change_risk_level}
Incremental Rollout: ${rolloutStrategy.rollout_feasibility?.incremental_rollout_possible ? 'Yes' : 'No'}
Phases: ${rolloutStrategy.rollout_phases?.length || 0}
Key Risks: ${rolloutStrategy.risk_assessment?.critical_risk_vectors?.join(', ') || 'None specified'}
Monitoring Metrics: ${rolloutStrategy.monitoring_and_alerting?.core_system_metrics?.length || 0} system metrics
Generated: ${new Date().toISOString()}
                `.trim();

                // Store for future retrieval
                const { queryPinecone } = await import('@/lib/pineconeUtils');
                // Note: We would need a storePinecone function for this, using queryPinecone as placeholder
                console.log('[Design Doc Wizard - Generate Rollout Strategy] Would store rollout strategy in Pinecone:', rolloutSummary.substring(0, 100) + '...');
            } catch (error) {
                console.warn('[Design Doc Wizard - Generate Rollout Strategy] Failed to store in Pinecone:', error);
                // Don't fail the request if Pinecone storage fails
            }
        }

        console.log('[Design Doc Wizard - Generate Rollout Strategy] Rollout strategy generated successfully');
        
        return NextResponse.json({
            success: true,
            rolloutStrategy: enhancedRolloutStrategy,
            message: 'Action-oriented rollout strategy generated successfully',
            context_sources: retrievedContext.length
        });

    } catch (error: any) {
        console.error('[Design Doc Wizard - Generate Rollout Strategy] Error:', error);
        return NextResponse.json({ 
            error: 'Failed to generate rollout strategy', 
            details: error.message 
        }, { status: 500 });
    }
} 