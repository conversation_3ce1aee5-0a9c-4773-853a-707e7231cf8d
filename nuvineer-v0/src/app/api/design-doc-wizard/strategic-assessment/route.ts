import { NextRequest, NextResponse } from 'next/server';
import { performStrategicAssessment } from '@/lib/assessment';

export async function POST(request: NextRequest) {
  try {
    const { taskAnalysis, projectConstitution, taskDetails } = await request.json();

    if (!taskAnalysis || !projectConstitution) {
        return NextResponse.json({ error: 'Task analysis and project constitution are required' }, { status: 400 });
    }
    
    const strategicAssessment = await performStrategicAssessment(taskAnalysis, projectConstitution, taskDetails);

    return NextResponse.json({ strategicAssessment });

  } catch (error: any) {
    console.error('Error in strategic assessment route:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
} 