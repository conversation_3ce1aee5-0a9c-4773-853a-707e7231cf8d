import { NextResponse, NextRequest } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server'; // Import Supabase server client
import {
    generateApiKey as generateApiKeyService,
    listApiKeys as listApiKeysService,
    revoke<PERSON>pi<PERSON>ey as revokeApiKeyService, // Assuming revoke exists
    validateApiKeyAndGetUser
} from '@/lib/apiKeyService';

// Helper function to get user ID from either session or API key
async function getUserIdFromRequest(request: NextRequest): Promise<string | null> {
    // 1. Check for API Key in Authorization header
    const authHeader = request.headers.get('Authorization');
    if (authHeader) {
        let apiKey: string | null = null;
        if (authHeader.startsWith('Bearer ')) {
             apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix
             console.log("[API Auth Helper] Found Bearer token scheme.");
        } else if (authHeader.startsWith('ApiKey ')) {
             apiKey = authHeader.substring(7); // Remove 'ApiKey ' prefix
             console.log("[API Auth Helper] Found ApiKey token scheme.");
        }

        if (apiKey) {
             const userId = await validateApiKeyAndGetUser(apiKey);
             if (userId) {
                 console.log(`[API Auth Helper] API Key validated for user: ${userId}`);
                 return userId;
             }
             console.warn("[API Auth Helper] Invalid API Key provided in header.");
             // Do not proceed to session check if an invalid API key was explicitly provided
             return null; 
        }
    }

    // 2. Fallback to checking web session using Supabase (if no valid API key header was found)
    console.log("[API Auth Helper] No valid API Key header found, checking web session...");
    try {
        const supabase = createSupabaseServerClient();
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
            console.error("[API Auth Helper] Error fetching Supabase session:", sessionError);
            return null;
        }
        
        if (session?.user?.id) {
            return session.user.id;
        }
    } catch (error) {
        console.error("[API Auth Helper] Exception checking Supabase session:", error);
    }

    // If neither is valid, return null
    return null;
}

// Generate a new API Key (Requires a logged-in web session)
export async function POST(request: NextRequest) {
    // Generation MUST use a web session, not an API key
    let userId: string | undefined;
    try {
        const supabase = createSupabaseServerClient();
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError || !session?.user?.id) {
             console.error("[POST /keys] Session fetch error or no session:", sessionError?.message);
             throw new Error('Unauthorized: Must be logged in to generate keys');
        }
        userId = session.user.id;
    } catch (error) {
         console.error("[POST /keys] Exception checking Supabase session:", error);
         return NextResponse.json({ error: 'Authentication error' }, { status: 500 });
    }
    
    if (!userId) {
         return NextResponse.json({ error: 'Unauthorized: Must be logged in to generate keys' }, { status: 401 });
    }

    try {
        const newKey = await generateApiKeyService(userId);
        return NextResponse.json({ apiKey: newKey.key, id: newKey.id, createdAt: newKey.createdAt, prefix: newKey.prefix, name: newKey.name });
    } catch (error) {
        console.error('Error generating API key:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to generate API key';
        return NextResponse.json({ error: errorMessage }, { status: 500 });
    }
}

// List API Keys (Accessible via web session or valid API Key)
export async function GET(request: NextRequest) {
    const userId = await getUserIdFromRequest(request);

    if (!userId) {
        return NextResponse.json({ error: 'Unauthorized or Invalid API Key' }, { status: 401 });
    }

    try {
        const keys = await listApiKeysService(userId);
        return NextResponse.json(keys);
    } catch (error) {
        console.error('Error listing API keys:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to list API keys';
        return NextResponse.json({ error: errorMessage }, { status: 500 });
    }
}

// DELETE handler removed - it lives in [keyId]/route.ts 