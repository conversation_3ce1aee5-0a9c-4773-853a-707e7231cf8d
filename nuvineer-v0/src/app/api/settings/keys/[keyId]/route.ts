import { NextResponse, NextRequest } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server'; // Import Supabase server client
import { revokeApiKey as revokeApiKeyService, validateApiKeyAndGetUser } from '@/lib/apiKeyService'; // Assume this function exists

// Re-use or adapt the helper function from the other route file
// For simplicity here, we define it again. Consider placing it in a shared utils file.
async function getUserIdFromRequest(request: NextRequest): Promise<string | null> {
    // 1. Check for API Key
    const authHeader = request.headers.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
        const apiKey = authHeader.substring(7);
        const userId = await validateApiKeyAndGetUser(apiKey);
        if (userId) return userId;
        return null; // Invalid key
    }
    // 2. Fallback to session using Supabase
    try {
        const supabase = createSupabaseServerClient();
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
            console.error("[API Auth Helper /keys/[keyId]] Error fetching Supabase session:", sessionError);
            return null;
        }

        if (session?.user?.id) return session.user.id;
    } catch (error) { console.error("[API Auth Helper /keys/[keyId]] Exception checking Supabase session:", error); }
    return null;
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { keyId: string } }
) {
    const userId = await getUserIdFromRequest(request);

    if (!userId) {
        return NextResponse.json({ error: 'Unauthorized or Invalid API Key' }, { status: 401 });
    }

  const keyIdToDelete = params.keyId;
  if (!keyIdToDelete) {
      return NextResponse.json({ error: 'Key ID is missing in the URL path' }, { status: 400 });
  }

  try {
    // The service function already checks if the userId owns the keyId
    await revokeApiKeyService(userId, keyIdToDelete);
    return NextResponse.json({ success: true, message: 'API key revoked successfully.' }); // Changed message for consistency
  } catch (error: any) {
    console.error('Error revoking API key:', error);
        // Handle specific errors, e.g., key not found or permission denied
        if (error.message === 'Key not found or permission denied') { // Assuming service throws this
            return NextResponse.json({ error: 'API key not found or you do not have permission to revoke it.' }, { status: 404 });
        }
    const errorMessage = error instanceof Error ? error.message : 'Failed to revoke API key';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
} 