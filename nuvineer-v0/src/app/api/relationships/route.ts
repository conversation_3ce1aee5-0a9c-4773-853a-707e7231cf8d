import { NextRequest, NextResponse } from 'next/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client (use environment variables)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Ensure Supabase client is initialized only if credentials are provided
let supabase: SupabaseClient | null = null;
if (supabaseUrl && supabaseServiceRoleKey) {
  supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      persistSession: false // Use service role key, no user session needed
    }
  });
  console.log("[API /relationships] Supabase client initialized.");
} else {
  console.warn("[API /relationships] Supabase URL or Service Role Key not found. API will be disabled.");
}

/**
 * Fetches decision relationships from Supabase.
 * Can filter by repository slug and optionally by source or target decision IDs.
 *
 * Query Parameters:
 * - repositorySlug: string (required) - The repository slug (e.g., 'owner/repo').
 * - sourceIds?: string - Comma-separated list of source decision Pinecone IDs.
 * - targetIds?: string - Comma-separated list of target decision Pinecone IDs.
 */
export async function GET(request: NextRequest) {
  if (!supabase) {
    console.error('[API /relationships] Supabase client not available. Cannot process request.');
    return NextResponse.json({ error: 'Relationship feature not configured' }, { status: 503 });
  }

  const { searchParams } = new URL(request.url);
  const repositorySlug = searchParams.get('repositorySlug');
  const sourceIdsParam = searchParams.get('sourceIds');
  const targetIdsParam = searchParams.get('targetIds');

  console.log(`[API /relationships] Received request. Params: repoSlug=${repositorySlug}, sourceIds=${sourceIdsParam || 'N/A'}, targetIds=${targetIdsParam || 'N/A'}`);

  if (!repositorySlug) {
    console.warn('[API /relationships] Bad Request: Missing repositorySlug parameter.');
    return NextResponse.json({ error: 'Missing required query parameter: repositorySlug' }, { status: 400 });
  }

  try {
    let query = supabase
      .from('decision_relationships')
      .select('id, source_decision_pinecone_id, target_decision_pinecone_id, relationship_type, confidence_score, repository_slug, source_pr_number, created_at')
      .eq('repository_slug', repositorySlug);

    // Apply filters if provided
    let filterLog = `repoSlug='${repositorySlug}'`;
    if (sourceIdsParam) {
      const sourceIds = sourceIdsParam.split(',').map(id => id.trim()).filter(id => id);
       if (sourceIds.length > 0) {
           console.log(`[API /relationships] Filtering by sourceIds: ${sourceIds}`);
           query = query.in('source_decision_pinecone_id', sourceIds);
           filterLog += `, sourceIds=[${sourceIds.join(',')}]`;
       }
    }
     if (targetIdsParam) {
        const targetIds = targetIdsParam.split(',').map(id => id.trim()).filter(id => id);
        if (targetIds.length > 0) {
             console.log(`[API /relationships] Filtering by targetIds: ${targetIds}`);
             query = query.in('target_decision_pinecone_id', targetIds);
             filterLog += `, targetIds=[${targetIds.join(',')}]`;
         }
     }

    console.log(`[API /relationships] Executing Supabase query with filters: ${filterLog}`);
    const { data, error } = await query;

    if (error) {
      console.error(`[API /relationships] Error fetching relationships for ${repositorySlug}:`, error);
      throw error; // Let the catch block handle it
    }

    const resultCount = data?.length || 0;
    console.log(`[API /relationships] Query successful. Found ${resultCount} relationships for ${repositorySlug}.`);
    return NextResponse.json({ success: true, relationships: data || [] });

  } catch (error: any) {
    console.error(`[API /relationships] Unexpected error:`, error);
    return NextResponse.json({ error: `Failed to fetch relationships: ${error.message || 'Unknown error'}` }, { status: 500 });
  }
} 