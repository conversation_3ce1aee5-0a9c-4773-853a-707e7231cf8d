import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse, NextRequest } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { createSupabaseAdminClient } from '@/lib/supabase-server'; // Import the admin client

// --- Environment Variables ---
function getRequiredEnvVar(name: string): string {
    const value = process.env[name];
    if (!value) {
        throw new Error(`Missing required environment variable: ${name}`);
    }
    return value;
}

const pineconeIndexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';

// --- Type Definitions (align with frontend and actual data structures) ---
// Pinecone Metadata (Adjust based on orchestrator.js storeDecisionRecord)
type PineconeDecisionMetadata = {
    pr_number: number;
    pr_title: string;
    pr_url: string;
    title?: string; // Decision title
    description?: string;
    rationale?: string;
    implications?: string;
    confidence_score?: number;
    related_files?: string[] | string; // Stored as stringified JSON or array?
    risks_extracted?: string; // Stored as stringified JSON
    no_architecture_impact_reason?: string;
    follows_standard_practice?: boolean; // Assuming this field exists
    follows_standard_practice_reason?: string; // Assuming this field exists
    // Add any other fields stored in Pinecone metadata needed for UI Decision type
};

// Supabase Relationship Record
type RelationshipRecord = {
    source_decision_pinecone_id: string;
    target_decision_pinecone_id: string;
    relationship_type: 'supersedes' | 'amends' | 'conflicts_with'; // Assuming 'independent' isn't stored
    confidence_score?: number | null;
    justification?: string | null;
    // Add id, repository_slug if needed
};

// Frontend Types (copied from ArchitectureAnalysisPage for consistency)
type Decision = { id: string; title: string; description?: string; rationale?: string; implications?: string; related_files?: string[]; confidence_score?: number; follows_standard_practice?: boolean; follows_standard_practice_reason?: string; risks?: Risk[]; /* ... other fields from metadata */ };
type Relationship = { new_decision_id: string; existing_decision_id: string; relationship_type: 'supersedes' | 'amends' | 'conflicts_with' | 'independent'; confidence_score: number; justification: string; };
type PRFile = { filename: string; additions?: number; deletions?: number; status?: string }; // Additions/deletions likely not in Pinecone metadata
type PRContext = { number: number; title: string; html_url: string; body?: string; files?: PRFile[] };
type AnalysisResult = {
  prContext: PRContext;
  decisions: Decision[];
  relationships: Relationship[];
  no_architecture_impact_reason?: string;
};

// ---> ADDED Risk type matching frontend
type Risk = {
  category: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  mitigation: string;
};

// --- Helper Functions ---
let pinecone: Pinecone | null = null;
async function getPineconeClient(): Promise<Pinecone> {
    if (!pinecone) {
        const apiKey = getRequiredEnvVar('PINECONE_API_KEY');
        pinecone = new Pinecone({ apiKey });
    }
    return pinecone;
}

function safeParseJsonArray(jsonString: string | string[] | undefined | null): string[] {
    if (!jsonString) return [];
    if (Array.isArray(jsonString)) return jsonString; // Already an array
    try {
        const parsed = JSON.parse(jsonString);
        return Array.isArray(parsed) ? parsed : [];
    } catch (e) {
        console.error("Failed to parse JSON string array:", jsonString, e);
        return [];
    }
}

// ---> ADDED: Helper to parse risks
function safeParseRisks(jsonString: string | undefined | null): Risk[] {
    if (!jsonString) return [];
    try {
        const parsed = JSON.parse(jsonString);
        // Basic validation of structure could be added here
        return Array.isArray(parsed) ? parsed : [];
    } catch (e) {
        console.error("Failed to parse risks JSON string:", jsonString, e);
        return [];
    }
}

// --- API Route Handler ---
export async function GET(request: NextRequest) {
    const cookieStore = cookies(); // Get the cookie store instance

    // Client for potential user session checks (if re-enabled)
    const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
            cookies: {
                get(name: string) {
                    return cookieStore.get(name)?.value;
                },
                set(name: string, value: string, options: CookieOptions) {
                    cookieStore.set(name, value, options);
                },
                remove(name: string, options: CookieOptions) {
                    // If path or domain are specified in Supabase options, use them for deletion.
                    // Otherwise, delete with default path/domain.
                    if (options.path || options.domain) {
                        cookieStore.delete({
                            name: name,
                            path: options.path,
                            domain: options.domain,
                            // Other options like secure, httpOnly, sameSite are not typically part of Next.js cookieStore.delete options object
                        });
                    } else {
                        cookieStore.delete(name);
                    }
                },
            },
        }
    );
    
    // Admin client for RLS-bypassing queries
    const adminSupabase = createSupabaseAdminClient();

    const params = request.nextUrl.searchParams;
    const repoSlug = params.get('repo');
    const installationIdParam = params.get('installationId');
    const isPublicParam = params.get('isPublic') === 'true';

    let installationId: number;

    // Validate parameters
    if (!repoSlug) {
        return NextResponse.json({ error: 'Missing repository slug in URL parameters.' }, { status: 400 });
    }

    if (isPublicParam) {
        // Public repo, use default installation ID for namespace consistency
        installationId = 0;
        console.log(`[API ArchAnalysis] Public repository request for ${repoSlug}. Using installationId=0 for namespace.`);
    } else {
        // Private repo, installation ID is required
        if (!installationIdParam) {
            return NextResponse.json({ error: 'Missing installation ID for private repository analysis.' }, { status: 400 });
        }
        installationId = parseInt(installationIdParam, 10);
        if (isNaN(installationId) || installationId <= 0) {
            return NextResponse.json({ error: 'Invalid installation ID format.' }, { status: 400 });
        }
        console.log(`[API ArchAnalysis] Private repository request for ${repoSlug}. Using installationId=${installationId}.`);
    }

    // Construct the Pinecone namespace using the effective installationId
    const namespace = `${installationId}-${repoSlug.replace('/', '--')}`;
    console.log(`[API ArchAnalysis] Querying Pinecone namespace: ${namespace}`);

    let pineconeClient: Pinecone | null = null;

    try {
        // 1. TEMPORARILY COMMENT OUT Authentication Check for Debugging
        /*
        console.log('[API ArchAnalysis] Attempting getSession check...');
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError || !session) {
            console.error('API Auth Error (getSession check):', sessionError);
            // Log the cookies to see what's being received
            try {
                const cookieStore = cookies();
                console.log('Cookies received by API route:', JSON.stringify(cookieStore.getAll()));
            } catch (e) {
                console.error('Failed to log cookies:', e);
            }
            return NextResponse.json({ error: 'Unauthorized (getSession check failed - DEBUG BYPASSED)' }, { status: 401 });
        }
        console.log('[API ArchAnalysis] User session validated via getSession (DEBUG BYPASSED)');
        */
        // END TEMPORARY COMMENT OUT

        console.log('[API ArchAnalysis] Bypassing session check for DEBUG. Proceeding...');

        // 3. Initialize Pinecone Client & Get Index
        pineconeClient = await getPineconeClient();
        const pineconeIndex = pineconeClient.Index(pineconeIndexName);

        // 4. Fetch ALL Decision Vectors/Metadata from Pinecone for the CORRECT Namespace
        console.log(`[API ArchAnalysis] Fetching decisions from Pinecone namespace: ${namespace}`);
        const allDecisionRecords: { id: string, metadata?: PineconeDecisionMetadata }[] = [];
        let currentPaginationToken: string | undefined = undefined;
        do {
            console.log(`[API ArchAnalysis] Fetching Pinecone page with token: ${currentPaginationToken || '(first page)'}`);
            const listResult = await pineconeIndex.namespace(namespace).listPaginated({ limit: 100, paginationToken: currentPaginationToken });
            if (listResult.vectors && listResult.vectors.length > 0) {
                 const idsToFetch = listResult.vectors.map(v => v.id).filter((id): id is string => id !== undefined);
                 console.log(`[API ArchAnalysis] Fetching metadata for ${idsToFetch.length} IDs...`);
                 if (idsToFetch.length > 0) { // Ensure there are IDs to fetch after filtering
                    const fetchResult = await pineconeIndex.namespace(namespace).fetch(idsToFetch);
                    if (fetchResult.records) {
                         allDecisionRecords.push(...Object.values(fetchResult.records) as { id: string, metadata?: PineconeDecisionMetadata }[]);
                    }
                    console.log(`[API ArchAnalysis] Fetched metadata. Total records so far: ${allDecisionRecords.length}`);
                 } else {
                    console.log('[API ArchAnalysis] No valid IDs to fetch metadata for after filtering.');
                 }
            }
            currentPaginationToken = listResult.pagination?.next;
            console.log(`[API ArchAnalysis] Next pagination token: ${currentPaginationToken}`);
        } while (currentPaginationToken);
        console.log(`[API ArchAnalysis] Finished fetching all pages from Pinecone. Total records: ${allDecisionRecords.length}`);

        // 5. Fetch Relationships from Supabase for the Repository
        // Use the admin client to bypass RLS for reading relationships
        console.log(`[API ArchAnalysis] Fetching relationships from Supabase for repo: ${repoSlug} (using ADMIN client)`);
        const query = adminSupabase // Use admin client here
            .from('decision_relationships')
            .select('source_decision_pinecone_id, target_decision_pinecone_id, relationship_type, confidence_score')
            .eq('repository_slug', repoSlug);

        // Log the query details before executing
        console.log(`[API ArchAnalysis] Supabase Query Details: table=decision_relationships, select=source_decision_pinecone_id,target_decision_pinecone_id,relationship_type,confidence_score, filter=repository_slug eq ${repoSlug}`);

        const { data: relationshipRecords, error: relationshipsError } = await query; // Execute the constructed query

        if (relationshipsError) {
            console.error('Supabase relationships fetch error:', relationshipsError);
            throw new Error(`Failed to fetch relationships: ${relationshipsError.message}`);
        }
        console.log(`[API ArchAnalysis] Fetched ${relationshipRecords?.length || 0} relationship records from Supabase.`);

        // 6. Process and Aggregate Data by PR Number
        const analysisMap = new Map<number, AnalysisResult>();
        const decisionIdToPrNumberMap = new Map<string, number>(); // Helper map
        let loggedSkippedMetadata = false; // Added for debugging

        for (const record of allDecisionRecords) {
            const metadata = record.metadata as PineconeDecisionMetadata | undefined; // Type assertion

            // ---> MODIFIED CHECK: Validate core metadata, be flexible with pr_number type
            const prNumberRaw = metadata?.pr_number;
            const prUrl = metadata?.pr_url;
            let prNumber: number;

            if (!metadata || prNumberRaw === undefined || prNumberRaw === null) { // Removed prUrl check here, will handle fallback later
                 console.warn(`[API ArchAnalysis] Skipping Pinecone record ${record.id}: Missing basic metadata (metadata or pr_number). Metadata received:`, JSON.stringify(metadata));
                 continue;
            }

            // Attempt to parse pr_number if it's not already a number
            if (typeof prNumberRaw === 'number') {
                prNumber = prNumberRaw;
            } else {
                prNumber = parseInt(String(prNumberRaw), 10);
            }

            // Final check if parsing failed
            if (isNaN(prNumber)) {
                console.warn(`[API ArchAnalysis] Skipping Pinecone record ${record.id}: pr_number ('${prNumberRaw}') could not be parsed to a valid number.`);
                // Log the metadata of the first skipped record for easier debugging
                 if (!loggedSkippedMetadata) {
                     console.warn(`[API ArchAnalysis] Metadata of first skipped record with unparsable pr_number (${record.id}):`, JSON.stringify(metadata, null, 2));
                     loggedSkippedMetadata = true; // Log only once
                 }
                continue;
            }
            // <--- END MODIFIED CHECK

            // ---> Construct prUrl if missing
            let finalPrUrl = prUrl; // Use existing prUrl if available
            if (!finalPrUrl) {
                if (repoSlug) { // Ensure repoSlug is available from the outer scope
                    finalPrUrl = `https://github.com/${repoSlug}/pull/${prNumber}`;
                    console.log(`[API ArchAnalysis] Constructed default pr_url for record ${record.id}: ${finalPrUrl}`);
                } else {
                    console.warn(`[API ArchAnalysis] Cannot construct pr_url for record ${record.id}: repoSlug is missing.`);
                    // Decide if we should skip or continue with a null URL
                    // Skipping seems safer to avoid broken links downstream
                    continue; 
                }
            }
            // <--- End prUrl construction

            decisionIdToPrNumberMap.set(record.id, prNumber); // Store mapping (prNumber is now guaranteed to be a number)

            if (!analysisMap.has(prNumber)) { // Use the guaranteed number key
                 analysisMap.set(prNumber, {
                    prContext: {
                        number: prNumber, // Use the guaranteed number
                        title: `PR #${prNumber}`, // Use the guaranteed number
                        html_url: finalPrUrl, // Use the final (potentially constructed) URL
                        files: safeParseJsonArray(metadata.related_files).map(f => ({ filename: f })),
                    },
                    decisions: [],
                    relationships: [],
                    no_architecture_impact_reason: metadata.no_architecture_impact_reason,
                });
            }

            const analysis = analysisMap.get(prNumber)!;
             const decision: Decision = {
                 id: record.id,
                 title: metadata.title || 'Untitled Decision',
                 description: metadata.description,
                 rationale: metadata.rationale,
                 implications: metadata.implications,
                 confidence_score: metadata.confidence_score,
                 related_files: safeParseJsonArray(metadata.related_files),
                 // Add the new fields from metadata
                 follows_standard_practice: metadata.follows_standard_practice,
                 follows_standard_practice_reason: metadata.follows_standard_practice_reason,
                 risks: safeParseRisks(metadata.risks_extracted),
             };
             analysis.decisions.push(decision);
             if (metadata.no_architecture_impact_reason) {
                 analysis.no_architecture_impact_reason = metadata.no_architecture_impact_reason;
             }
        }

        // 7. Assign Relationships to relevant PRs
        for (const rel of (relationshipRecords || []) as RelationshipRecord[]) {
            const sourcePr = decisionIdToPrNumberMap.get(rel.source_decision_pinecone_id);
            const targetPr = decisionIdToPrNumberMap.get(rel.target_decision_pinecone_id);

            // Add relationship to the source PR's analysis result if it exists
            if (sourcePr && analysisMap.has(sourcePr)) {
                const analysis = analysisMap.get(sourcePr)!;
                 if (!analysis.relationships.some(r => r.new_decision_id === rel.source_decision_pinecone_id && r.existing_decision_id === rel.target_decision_pinecone_id)) {
                     analysis.relationships.push({
                         new_decision_id: rel.source_decision_pinecone_id,
                         existing_decision_id: rel.target_decision_pinecone_id,
                         relationship_type: rel.relationship_type,
                         confidence_score: rel.confidence_score ?? 0,
                         justification: 'N/A',
                     });
                 }
            }
            // Also add relationship to the target PR's analysis result
            if (targetPr && targetPr !== sourcePr && analysisMap.has(targetPr)) {
                 const analysis = analysisMap.get(targetPr)!;
                  if (!analysis.relationships.some(r => r.new_decision_id === rel.source_decision_pinecone_id && r.existing_decision_id === rel.target_decision_pinecone_id)) {
                     analysis.relationships.push({
                         new_decision_id: rel.source_decision_pinecone_id,
                         existing_decision_id: rel.target_decision_pinecone_id,
                         relationship_type: rel.relationship_type,
                         confidence_score: rel.confidence_score ?? 0,
                         justification: 'N/A',
                     });
                 }
            }
        }

        // Convert map values to array
        const results: AnalysisResult[] = Array.from(analysisMap.values());

        // Sort results by PR number descending
        results.sort((a, b) => b.prContext.number - a.prContext.number);

        // 8. Return JSON Response
        console.log(`[API ArchAnalysis] Returning ${results.length} aggregated PR analysis results.`);
        return NextResponse.json(results);

    } catch (error: any) {
        console.error('[API ArchAnalysis] Error:', error);
        // Ensure Pinecone client is closed if initialized (though route handlers are typically stateless)
        // Pinecone client doesn't have an explicit close method in v3
        return NextResponse.json({ error: error.message || 'Internal Server Error' }, { status: 500 });
    }
}

export const dynamic = 'force-dynamic'; // Ensure fresh data 