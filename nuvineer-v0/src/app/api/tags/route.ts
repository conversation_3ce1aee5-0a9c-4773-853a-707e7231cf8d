import { NextRequest, NextResponse } from 'next/server';
// import { cookies } from 'next/headers'; // Not used
// import { createServerClient } from '@supabase/ssr'; // Not used for admin client
import { Pinecone } from '@pinecone-database/pinecone';
import { createClient as createSupabaseClient, SupabaseClient } from '@supabase/supabase-js'; // For admin client
import { getRepositoryNamespace } from '@/lib/pinecone-utils'; // Added import
// import { getPineconeClient, getRequiredEnvVar } from '@/app/api/utils/pinecone'; // REMOVED
// import { createSupabaseAdminClient } from '@/app/api/utils/supabase'; // REMOVED

// --- Local Environment Variable Helper ---
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            console.warn(`[API Tags] Environment variable ${name} not found, using default value: ${defaultValue}`);
            return defaultValue;
        }
        console.error(`[API Tags] Missing required environment variable: ${name}`);
        throw new Error(`[API Tags] Missing required environment variable: ${name}`);
    }
    return value;
}

// --- Local Pinecone Client Initialization ---
let pinecone: Pinecone | null = null;
const pineconeIndexes: Record<string, any> = {}; // To cache index handles by namespace

async function initializePineconeClient() {
    if (!pinecone) {
        try {
            const apiKey = getEnvVar('PINECONE_API_KEY');
            pinecone = new Pinecone({ apiKey });
            console.log(`[API Tags] Pinecone client initialized.`);
        } catch (error: any) {
            console.error("[API Tags] Failed to initialize Pinecone client:", error);
            pinecone = null;
            throw error; // Re-throw to be caught by the main handler
        }
    }
}

async function getPineconeIndexForNamespace(namespace: string) {
    await initializePineconeClient();
    if (!pinecone) {
        throw new Error("[API Tags] Pinecone client is not initialized after attempt.");
    }
    if (!pineconeIndexes[namespace]) {
        const indexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions'); // Consistent default
        pineconeIndexes[namespace] = pinecone.Index<PineconeDecisionMetadata>(indexName).namespace(namespace);
        console.log(`[API Tags] Pinecone index handle initialized for namespace: ${namespace}, index: ${indexName}`);
    }
    return pineconeIndexes[namespace];
}
// --- END Local Pinecone Helpers ---

// --- Local Supabase Admin Client Initialization ---
let supabaseAdmin: SupabaseClient | null = null;

function initializeSupabaseAdminClient(): SupabaseClient {
    if (!supabaseAdmin) {
        try {
            const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
            const supabaseServiceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY');
            supabaseAdmin = createSupabaseClient(supabaseUrl, supabaseServiceRoleKey, {
                auth: { persistSession: false, autoRefreshToken: false }
            });
            console.log("[API Tags] Supabase admin client initialized.");
        } catch (error: any) {
            console.error("[API Tags] Failed to initialize Supabase admin client:", error);
            supabaseAdmin = null;
            throw error; // Re-throw
        }
    }
    return supabaseAdmin;
}
// --- END Local Supabase Admin Client Helpers ---

// Interfaces (can be shared or redefined)
type PineconeDecisionMetadata = {
    pr_number?: number; // Making optional as we might not strictly need them here
    pr_url?: string;
    domain_concepts?: string[] | string; // The field we need
    // Add other fields if needed for filtering, but keep minimal
};

// Added type for the fetched Pinecone records
type FetchedPineconeRecord = {
    id: string;
    // We don't use values here, but a typical Pinecone record has them
    // values: number[]; 
    metadata?: PineconeDecisionMetadata;
};

type RelationshipRecord = {
    source_decision_pinecone_id: string;
    target_decision_pinecone_id: string;
    relationship_type: 'supersedes' | 'amends' | 'conflicts_with';
};

// const pineconeIndexName = getRequiredEnvVar('PINECONE_INDEX'); // REMOVED - Handled in getPineconeIndexForNamespace

// Helper to parse domain concepts safely
function safeParseDomainConcepts(concepts: string | string[] | undefined | null): string[] {
    if (!concepts) return [];
    if (Array.isArray(concepts)) return concepts.filter(Boolean); // Filter out empty strings if any
    if (typeof concepts === 'string') {
        return concepts.split(',').map(d => d.trim()).filter(Boolean);
    }
    return [];
}

export async function GET(request: NextRequest) {
    console.log('[API Tags] Received request');
    let adminSupabaseClient: SupabaseClient;
    try {
        adminSupabaseClient = initializeSupabaseAdminClient();
    } catch (error: any) {
        console.error('[API Tags] Critical: Could not initialize Supabase admin client:', error.message);
        return NextResponse.json({ success: false, error: 'Server configuration error for Supabase.' }, { status: 500 });
    }
    const params = request.nextUrl.searchParams;
    const repoSlug = params.get('repositorySlug'); // Match param name from frontend call
    const installationIdParam = params.get('installationId');
    const isPublicParam = params.get('isPublic') === 'true';

    let installationId: number;

    if (!repoSlug) {
        return NextResponse.json({ success: false, error: 'Missing repositorySlug parameter.' }, { status: 400 });
    }

    if (isPublicParam) {
        installationId = 0;
        console.log(`[API Tags] Public repository: ${repoSlug}`);
    } else {
        if (!installationIdParam) {
            return NextResponse.json({ success: false, error: 'Missing installationId for private repository.' }, { status: 400 });
        }
        installationId = parseInt(installationIdParam, 10);
        // Allow 0 only if isPublicParam was true. For private, installationId > 0.
        if (isNaN(installationId) || installationId <= 0) { 
            return NextResponse.json({ success: false, error: 'Invalid installationId format for private repository. Must be a positive integer.' }, { status: 400 });
        }
        console.log(`[API Tags] Private repository: ${repoSlug}, Installation: ${installationId}`);
    }

    // Construct the Pinecone namespace using the utility function
    const namespace = getRepositoryNamespace(installationId, repoSlug);
    console.log(`[API Tags] Querying Pinecone namespace: ${namespace}`);

    // let pineconeClient: Pinecone | null = null; // REMOVED - Handled by helpers

    try {
        // pineconeClient = await getPineconeClient(); // REMOVED
        // const pineconeIndex = pineconeClient.Index(pineconeIndexName); // REMOVED
        const pineconeIndexHandle = await getPineconeIndexForNamespace(namespace);

        // 1. Fetch ALL Decision IDs and Metadata from Pinecone for the namespace
        console.log(`[API Tags] Fetching all decisions from Pinecone namespace: ${namespace}`);
        const allDecisionRecords: { id: string, metadata?: PineconeDecisionMetadata }[] = [];
        let currentPaginationToken: string | undefined = undefined;

        // Define types for Pinecone responses to avoid 'any'
        interface PineconeListVector {
            id: string;
            // listPaginated vectors might also contain 'values' and 'metadata'
            // but we only use 'id' here.
        }
        interface PineconeListResponse {
            vectors: PineconeListVector[]; // Changed from optional, as we check vectors.length
            pagination?: { next?: string; };
        }
        
        // PineconeRecord from SDK, simplified, with our metadata type
        interface SdkPineconeRecordWithMetadata {
            id: string;
            values: number[]; // Standard field, though not used in this transformation
            metadata?: PineconeDecisionMetadata;
            // sparseValues etc. could also be here
        }
        interface PineconeFetchResponse {
            records: Record<string, SdkPineconeRecordWithMetadata>;
            // namespace: string; // also present
        }

        do {
            // const listResult = await pineconeIndex.namespace(namespace).listPaginated({ limit: 100, paginationToken: currentPaginationToken }); // MODIFIED
            const listResult: PineconeListResponse = await pineconeIndexHandle.listPaginated({ limit: 100, paginationToken: currentPaginationToken });
            if (listResult.vectors && listResult.vectors.length > 0) {
                 const idsToFetch = listResult.vectors.map((v: PineconeListVector) => v.id); // Typed v
                 // const fetchResult = await pineconeIndex.namespace(namespace).fetch(idsToFetch); // MODIFIED
                 const fetchResult: PineconeFetchResponse = await pineconeIndexHandle.fetch(idsToFetch);
                 if (fetchResult.records) {
                      // Object.values(fetchResult.records) will be SdkPineconeRecordWithMetadata[]
                      // r will be SdkPineconeRecordWithMetadata. It's compatible with FetchedPineconeRecord.
                      allDecisionRecords.push(...Object.values(fetchResult.records).map((r: SdkPineconeRecordWithMetadata) => ({
                          id: r.id,
                          metadata: { // This creates a new metadata object, only with domain_concepts
                              domain_concepts: r.metadata?.domain_concepts
                          }
                      })));
                 }
            }
            currentPaginationToken = listResult.pagination?.next;
        } while (currentPaginationToken);
        console.log(`[API Tags] Fetched ${allDecisionRecords.length} total decision records from Pinecone.`);

        // 2. Fetch Relationships from Supabase to identify superseded decisions
        console.log(`[API Tags] Fetching relationships from Supabase for repo: ${repoSlug}`);
        const { data: relationshipRecords, error: relationshipsError } = await adminSupabaseClient // Use initialized client
            .from('decision_relationships')
            .select('source_decision_pinecone_id, target_decision_pinecone_id, relationship_type')
            .eq('repository_slug', repoSlug)
            .eq('relationship_type', 'supersedes'); // Only need 'supersedes' relationships

        if (relationshipsError) {
            console.error('[API Tags] Supabase relationships fetch error:', relationshipsError);
            throw new Error(`Failed to fetch relationships: ${relationshipsError.message}`);
        }
        console.log(`[API Tags] Fetched ${relationshipRecords?.length || 0} 'supersedes' relationship records.`);

        const supersededDecisionIds = new Set<string>(
            (relationshipRecords || []).map((rel: RelationshipRecord) => rel.target_decision_pinecone_id)
        );
        console.log(`[API Tags] Identified ${supersededDecisionIds.size} superseded decision IDs.`);

        // 3. Calculate Tag Frequencies for ACTIVE decisions
        const tagCounts = new Map<string, number>();
        let activeDecisionCount = 0;
        for (const record of allDecisionRecords) {
            if (!supersededDecisionIds.has(record.id)) {
                activeDecisionCount++;
                const concepts = safeParseDomainConcepts(record.metadata?.domain_concepts);
                for (const tag of concepts) {
                    tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
                }
            }
        }
        console.log(`[API Tags] Processed ${activeDecisionCount} active decisions. Found ${tagCounts.size} unique tags.`);

        // 4. Convert map to array and sort (optional, sorting by count descending)
        const sortedTags = Array.from(tagCounts.entries())
            .map(([tag, count]) => ({ tag, count }))
            .sort((a, b) => b.count - a.count); // Sort descending by count

        // 5. Return JSON Response
        return NextResponse.json({ success: true, tags: sortedTags });

    } catch (error: any) {
        console.error('[API Tags] Error:', error);
        return NextResponse.json({ success: false, error: error.message || 'Internal Server Error' }, { status: 500 });
    }
}

export const dynamic = 'force-dynamic'; // Ensure fresh data on each request 