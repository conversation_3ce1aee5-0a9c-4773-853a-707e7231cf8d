import { NextRequest, NextResponse } from 'next/server';
import { queryPinecone } from '@/lib/pineconeUtils';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';

interface SearchRequest {
  repositorySlug: string;
  searchQuery: string;
  isPublic: boolean;
  installationId: string;
  topK?: number;
  minScore?: number;
}

interface SearchResult {
  id: string;
  score: number;
  metadata: any;
}

interface SearchResponse {
  success: boolean;
  results: SearchResult[];
  namespace: string;
  query: string;
  debug?: {
    installationId: string;
    repositorySlug: string;
    isPublic: boolean;
    topK: number;
    minScore: number;
    filter: any;
  };
  error?: string;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: SearchRequest = await request.json();
    
    // Validate required fields
    if (!body.repositorySlug || !body.searchQuery) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: repositorySlug and searchQuery are required' 
        },
        { status: 400 }
      );
    }

    // Validate repository slug format
    if (!body.repositorySlug.includes('/')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid repository slug format. Expected format: owner/repo' 
        },
        { status: 400 }
      );
    }

    // Set default values
    const topK = body.topK || 10;
    const minScore = body.minScore || 0.7;
    const installationId = body.isPublic ? '0' : body.installationId;

    // Validate installation ID for private repos
    if (!body.isPublic && (!installationId || installationId === '0')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Installation ID is required for private repositories' 
        },
        { status: 400 }
      );
    }

    // Validate parameters
    if (topK < 1 || topK > 50) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'topK must be between 1 and 50' 
        },
        { status: 400 }
      );
    }

    if (minScore < 0 || minScore > 1) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'minScore must be between 0 and 1' 
        },
        { status: 400 }
      );
    }

    console.log(`[Admin Semantic Search] Starting search for repo: ${body.repositorySlug}`);
    console.log(`[Admin Semantic Search] Query: "${body.searchQuery}"`);
    console.log(`[Admin Semantic Search] IsPublic: ${body.isPublic}, InstallationId: ${installationId}`);
    console.log(`[Admin Semantic Search] TopK: ${topK}, MinScore: ${minScore}`);

    // Construct namespace using the same logic as the rest of the system
    let namespace: string;
    try {
      const numericInstallationId = parseInt(installationId, 10);
      if (isNaN(numericInstallationId) || numericInstallationId < 0) {
        throw new Error(`Invalid installation ID: ${installationId}`);
      }
      namespace = getRepositoryNamespace(numericInstallationId, body.repositorySlug);
      console.log(`[Admin Semantic Search] Constructed namespace: ${namespace}`);
    } catch (error) {
      console.error('[Admin Semantic Search] Error constructing namespace:', error);
      return NextResponse.json(
        { 
          success: false, 
          error: `Failed to construct namespace: ${error instanceof Error ? error.message : 'Unknown error'}` 
        },
        { status: 400 }
      );
    }

    // Perform semantic search using the existing utility
    // This will use the default filter of { 'is_superseded': false }
    const searchFilter = { 'is_superseded': false };
    console.log(`[Admin Semantic Search] Using filter:`, searchFilter);

    let searchResults: SearchResult[];
    try {
      searchResults = await queryPinecone(
        body.searchQuery,
        namespace,
        topK,
        minScore,
        searchFilter
      );
      console.log(`[Admin Semantic Search] Found ${searchResults.length} results`);
    } catch (error) {
      console.error('[Admin Semantic Search] Error querying Pinecone:', error);
      return NextResponse.json(
        { 
          success: false, 
          error: `Failed to search Pinecone: ${error instanceof Error ? error.message : 'Unknown error'}`,
          namespace,
          query: body.searchQuery
        },
        { status: 500 }
      );
    }

    // Return results with debug information
    const response: SearchResponse = {
      success: true,
      results: searchResults,
      namespace,
      query: body.searchQuery,
      debug: {
        installationId,
        repositorySlug: body.repositorySlug,
        isPublic: body.isPublic,
        topK,
        minScore,
        filter: searchFilter
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('[Admin Semantic Search] Unexpected error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}` 
      },
      { status: 500 }
    );
  }
}

// Add a GET endpoint for basic health check
export async function GET(request: NextRequest): Promise<NextResponse> {
  return NextResponse.json({
    success: true,
    message: 'Admin semantic search endpoint is available',
    usage: 'POST to this endpoint with { repositorySlug, searchQuery, isPublic, installationId?, topK?, minScore? }',
    timestamp: new Date().toISOString()
  });
} 