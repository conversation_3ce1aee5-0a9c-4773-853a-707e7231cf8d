import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// GET handles fetching a project constitution
export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const repositorySlug = searchParams.get('repositorySlug');

    if (!repositorySlug) {
        return NextResponse.json({ error: 'Missing repositorySlug parameter' }, { status: 400 });
    }

    const supabase = createSupabaseServerClient();

    try {
        // Fetch the constitution from the correct table and column
        const { data, error } = await supabase
            .from('project_constitutions')
            .select('constitution_data')
            .eq('repository_slug', repositorySlug)
            .limit(1)
            .single(); // .single() returns one object instead of an array and errors if not found

        if (error) {
            if (error.code === 'PGRST116') { // PostgREST error code for "not found"
                return NextResponse.json({ error: 'No constitution found for this repository.' }, { status: 404 });
            }
            throw error;
        }

        if (!data) {
            return NextResponse.json({ error: 'No constitution found for this repository.' }, { status: 404 });
        }

        // Return the nested constitution object directly
        return NextResponse.json({ success: true, constitution: data.constitution_data });

    } catch (error: any) {
        console.error(`[API Constitution] Error fetching constitution for ${repositorySlug}:`, error);
        return NextResponse.json({ error: 'Failed to fetch constitution.', details: error.message }, { status: 500 });
    }
}

// POST handles creating or updating (upserting) a project constitution
export async function POST(request: Request) {
  const supabase = createSupabaseServerClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const { repositorySlug, constitutionData } = await request.json();

  if (!repositorySlug || !constitutionData) {
    return new NextResponse(JSON.stringify({ error: 'Missing repositorySlug or constitutionData' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    const { data, error } = await supabase
      .from('project_constitutions')
      .upsert({
        repository_slug: repositorySlug,
        constitution_data: constitutionData,
        last_updated_by: session.user.id,
      })
      .select()
      .single();

    if (error) {
      console.error('Supabase upsert error:', error);
      throw error;
    }

    return NextResponse.json(data);
  } catch (error: any) {
    return new NextResponse(JSON.stringify({ error: 'Failed to save constitution', details: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
} 