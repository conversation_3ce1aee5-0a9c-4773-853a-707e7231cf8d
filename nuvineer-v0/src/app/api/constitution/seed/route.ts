import { NextRequest, NextResponse } from 'next/server';
import { generateConstitutionSeedingPrompt } from '@/analyzer/prompt';
import { callLLM } from '@/lib/llm';

// This is a placeholder for a real AI-powered seeding implementation.
// In a real-world scenario, this endpoint would:
// 1. Receive the raw text from the user.
// 2. Use a large language model (LLM) with a specific prompt to parse the text.
// 3. The prompt would instruct the LLM to extract key information and format it
//    according to the ProjectConstitution interface.
// 4. Return the structured JSON to the client.

type PriorityId =
  | 'time-to-market'
  | 'reliability'
  | 'scalability'
  | 'maintainability'
  | 'cost-of-ownership'
  | 'developer-experience'
  | 'strategic-alignment';

interface ProjectConstitution {
    companyStage: 'startup' | 'growth' | 'mature' | 'not-set';
    projectType: 'b2c-saas' | 'b2b-saas' | 'library' | 'internal-tool' | 'data-platform' | 'other' | 'not-set';
    priorities: PriorityId[];
    architecturalPrinciples: string;
    productAndBusinessContext: string;
    primaryLanguage?: string;
  }

export async function POST(req: NextRequest) {
    const { rawText } = await req.json();

    if (!rawText) {
        return new NextResponse(JSON.stringify({ error: 'Missing rawText in request body' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
        });
    }

    try {
        const prompt = generateConstitutionSeedingPrompt(rawText);
        const constitutionData = await callLLM(prompt);
        return NextResponse.json(constitutionData);
    } catch (error: any) {
        console.error("Error in constitution seeding:", error);
        return new NextResponse(JSON.stringify({ error: 'Failed to seed constitution from text.', details: error.message }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
        });
    }
} 