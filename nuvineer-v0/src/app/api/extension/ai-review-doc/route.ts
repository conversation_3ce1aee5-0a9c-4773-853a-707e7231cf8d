import { NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService'; // Assuming this is a shared service

// --- Constants for Comment Structure ---
const ARCHKNOW_COMMENT_START_TAG = "<!-- ARCHKNOW_COMMENT_START";
const ARCHKNOW_COMMENT_END_TAG = "ARCHKNOW_COMMENT_END -->";

// --- Interfaces ---

interface AiReviewDocRequest {
  documentContent: string; // Full markdown content of the design doc
  repository_slug: string; // For potential future context, consistency
  api_key: string;
}

interface AiReviewComment {
  id: string;
  type: 'blocking' | 'non-blocking' | 'nit';
  status: 'open'; // AI review comments always start as 'open'
  author: 'ai-reviewer';
  timestamp: string;
  text: string;
  suggestedSection?: string;
}

interface AiReviewDocResponse {
  reviewedDocumentContent: string; // Markdown content with AI comments embedded
  aiComments: AiReviewComment[]; // List of comments added
}

// --- Environment Variable Helper ---
function getEnvVar(name: string, defaultValue: string | null = null): string {
  const value = process.env[name];
  if (value === undefined) {
    if (defaultValue === null) {
      throw new Error(`Missing environment variable: ${name}`);
    }
    console.warn(`Missing environment variable ${name}, using default value.`);
    return defaultValue;
  }
  return value;
}

// --- Initialize SDK Clients ---
const anthropic = new Anthropic({
  apiKey: getEnvVar('ANTHROPIC_API_KEY'),
});

// --- Prompt Generation ---
function generateAiReviewPrompt(documentContent: string): string {
  // Ensure backticks within the document content are escaped for the outer template literal
  const escapedDocumentContent = documentContent.replace(/`/g, '\\`');

  return `
You are an expert software architect. Review the following design document.
The document is provided below, delimited by triple backticks.
For each significant issue or suggestion you have, provide a comment.

When categorizing feedback, use the following criteria:
- 'blocking': Critical issues that MUST be addressed before the document can be approved, such as:
  * Missing essential sections or requirements
  * Unclear or contradictory technical decisions
  * Security vulnerabilities or compliance issues
  * Scalability concerns that could lead to system failure
  * Major architectural flaws or violations of key principles
  * Unaddressed dependencies or integration risks
  * Missing error handling or resilience strategies
  * Data privacy or regulatory compliance issues

- 'non-blocking': Important suggestions that should be considered but don't block approval:
  * Optimization opportunities
  * Additional considerations or alternatives
  * Documentation improvements
  * Minor clarifications needed
  * Potential future enhancements

- 'nit': Minor stylistic or formatting suggestions

Focus on identifying BLOCKING issues first, as these are critical for the document's quality.
Be thorough in explaining why a blocking issue is critical and what specific changes are needed.
For each blocking issue, suggest a clear path to resolution.

Respond with a JSON array of comment objects. Each object should have the following structure:
{
  "type": "blocking" | "non-blocking" | "nit",
  "text": "Your detailed comment here.",
  "suggestedSection": "The section heading or a short quote from the section your comment applies to (optional)"
}

Ensure your response is ONLY the JSON array.

Design Document:
\`\`\`
${escapedDocumentContent}
\`\`\`
`;
}


// --- LLM Interaction (adapted from existing route) ---
async function callClaudeLlmForReview(prompt: string, model?: string): Promise<AiReviewComment[]> {
  const effectiveModel = model || getEnvVar('ANTHROPIC_MODEL', 'claude-sonnet-4-20250514');
  const callId = `llm-review-call-${Date.now()}`;
  console.log(`[${callId}] [LLM Review Request] Sending prompt to Claude model: ${effectiveModel}. Expecting JSON array of comments.`);
  
  let rawResponseTextForErrorLogging = ""; // Variable to store raw response for logging in case of error
  let jsonAttemptForErrorLogging = ""; // Variable to store json string for logging in case of error

  try {
    const msg = await anthropic.messages.create({
      model: effectiveModel,
      max_tokens: 4096,
      temperature: 0.2,
      messages: [
        { role: "user", content: prompt }
      ],
    });

    const responseText = msg.content[0].type === 'text' ? msg.content[0].text : '';
    rawResponseTextForErrorLogging = responseText; // Store raw response
    
    let jsonString = responseText;
    if (responseText.includes("```json")) {
        jsonString = responseText.substring(responseText.indexOf("```json") + 7, responseText.lastIndexOf("```"));
    } else if (responseText.startsWith("[") && responseText.endsWith("]")) {
        jsonString = responseText;
    } else {
        const firstBracket = responseText.indexOf('[');
        const lastBracket = responseText.lastIndexOf(']');
        if (firstBracket !== -1 && lastBracket > firstBracket) {
            jsonString = responseText.substring(firstBracket, lastBracket + 1);
        } else {
            console.error(`[${callId}] [LLM Review Response] Could not extract JSON array from response. Response:`, responseText);
            throw new Error('Failed to parse LLM response: No JSON array found.');
        }
    }
    
    jsonString = jsonString.trim();
    jsonAttemptForErrorLogging = jsonString; // Store json string before parsing

    if (!jsonString) {
        console.warn(`[${callId}] [LLM Review Response] Extracted JSON string is empty.`);
        return [];
    }

    const parsedResult = JSON.parse(jsonString) as Partial<AiReviewComment>[];

    const validatedComments: AiReviewComment[] = [];
    parsedResult.forEach((item, index) => {
        if (item.text && item.type && ['blocking', 'non-blocking', 'nit'].includes(item.type)) {
            validatedComments.push({
                id: `ai-comment-${Date.now()}-${index}`,
                type: item.type as 'blocking' | 'non-blocking' | 'nit',
                text: item.text,
                suggestedSection: item.suggestedSection,
                status: 'open',
                author: 'ai-reviewer',
                timestamp: new Date().toISOString(),
            });
        } else {
            console.warn(`[${callId}] [LLM Review Response] Skipping invalid comment item:`, item);
        }
    });

    console.log(`[${callId}] [LLM Review Response] Successfully parsed and validated ${validatedComments.length} comments.`);
    return validatedComments;

  } catch (error: any) {
    console.error(`[${callId}] Error calling Anthropic API for review: ${error.message}`, error.response?.data);
    if (error instanceof SyntaxError) {
        console.error("Failed to parse JSON response from LLM:", error.message, "Original raw response text:", rawResponseTextForErrorLogging, "Attempted JSON string:", jsonAttemptForErrorLogging);
        throw new Error(`Anthropic API call succeeded, but failed to parse JSON response: ${error.message}`);
    }
    throw new Error(`Anthropic API call for review failed: ${error.message}`);
  }
}

// --- Comment Embedding (Simplified POST handler version) ---
// The more complex embedCommentsInMarkdown function was removed for now from the active code path 
// in the POST handler, favoring a simpler append model for initial implementation.
// It can be reinstated and refined later.

// --- API Route Handler ---
export async function POST(request: Request) {
  console.log('[API ai-review-doc] Received request');
  
  // Check HTTP method
  if (request.method !== 'POST') {
    return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json() as AiReviewDocRequest;
    const { documentContent, repository_slug, api_key } = body;

    if (!documentContent || !repository_slug || !api_key) {
      return NextResponse.json({ error: 'Missing required fields: documentContent, repository_slug, api_key' }, { status: 400 });
    }

    // Enable API Key validation
    const user = await validateApiKeyAndGetUser(api_key);
    if (!user) {
      return NextResponse.json({ error: 'Invalid or unauthorized API key.' }, { status: 401 });
    }
    console.log(`[API ai-review-doc] API Key validated for user: ${user} in repo: ${repository_slug}`);

    const reviewPrompt = generateAiReviewPrompt(documentContent);
    const aiComments = await callClaudeLlmForReview(reviewPrompt);

    if (!aiComments || aiComments.length === 0) {
      console.log('[API ai-review-doc] AI returned no comments.');
      return NextResponse.json({
        reviewedDocumentContent: documentContent,
        aiComments: [],
      } as AiReviewDocResponse, { status: 200 });
    }
    
    console.log(`[API ai-review-doc] AI generated ${aiComments.length} comments.`);

    let reviewedDocumentContent = documentContent;
    aiComments.forEach(comment => {
        // Ensure TEXT and SUGGESTED_SECTION content is properly escaped for the markdown comment format
        const textContent = comment.text.replace(/\n/g, '\\n'); // Preserve newlines within the text field
        const sectionContent = comment.suggestedSection ? `SUGGESTED_SECTION: ${comment.suggestedSection.replace(/\n/g, '\\n')}` : '';

        const commentMarkdown = `
${ARCHKNOW_COMMENT_START_TAG}
ID: ${comment.id}
TYPE: ${comment.type}
STATUS: ${comment.status}
AUTHOR: ${comment.author}
TIMESTAMP: ${comment.timestamp}
TEXT: ${textContent}
${sectionContent}
${ARCHKNOW_COMMENT_END_TAG}
`;
        // Append comment to the end of the document
        reviewedDocumentContent += `\n\n${commentMarkdown.trim()}`;
    });

    return NextResponse.json({
      reviewedDocumentContent,
      aiComments,
    } as AiReviewDocResponse, { status: 200 });

  } catch (error: any) {
    console.error('[API ai-review-doc] Error processing request:', error);
    if (error.message.includes("Anthropic API call") || error.message.includes("LLM response parsing") || error.message.includes("Failed to parse LLM response")) {
        return NextResponse.json({ error: `Error during AI review processing: ${error.message}` }, { status: 500 });
    }
    return NextResponse.json({ error: `Internal server error: ${error.message || 'Unknown error'}` }, { status: 500 });
  }
}

// Basic testing (not for production deployment, just for local dev if needed)
/*
async function main() {
    const testDoc = \`
# My Design Doc

## 1. Introduction
This is the intro.

## 2. Goals
The goal is to do something cool.

## 3. Proposed Solution
We will use a new framework.
It is very fast.
\` 
    const prompt = generateAiReviewPrompt(testDoc);
    console.log("--- PROMPT ---");
    console.log(prompt);

    // Mock callClaudeLlmForReview or set up env vars to test
    // const comments = await callClaudeLlmForReview(prompt);
    // console.log("\\n--- COMMENTS ---");
    // console.log(JSON.stringify(comments, null, 2));

    // if (comments) {
    //     const reviewedDoc = embedCommentsInMarkdown(testDoc, comments);
    //     console.log("\\n--- REVIEWED DOC ---");
    //     console.log(reviewedDoc);
    // }
}

// main().catch(console.error);
*/ 