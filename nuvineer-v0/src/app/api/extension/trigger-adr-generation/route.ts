import { NextRequest, NextResponse } from 'next/server';
import { processMergedPR } from '@/orchestrator'; // Adjust path as needed
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
// ---> Use getRepositoryAccessDetails <----
import { getRepositoryAccessDetails, RepositoryAccessDetails } from '@/lib/repository-access'; // Use correct path alias
// ---> Use getInstallationOctokit directly from github.ts <----
import { getInstallationOctokit } from '@/lib/github'; // Reuse existing Octokit helper
import { Octokit } from '@octokit/rest';
import { RequestError } from "@octokit/request-error";

// Define ProcessMergedPRResult type based on usage in this file
interface ProcessMergedPRResult {
    status: 'completed_with_feedback' | 'feedback_generated' | 'processing' | 'success' | 'no_decisions' | 'error' | string; // Expanded based on usage
    reason?: string;
    error?: string;
    // decisions?: any[]; // Not explicitly used in this file's logic for result handling
}

// Helper function getEnvVar (as before)
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            // Consider adjusting log prefix if this function is centralized
            console.warn(`[EnvHelper] Environment variable ${name} not found, using default value.`);
            return defaultValue;
        } else {
            throw new Error(`Missing required environment variable: ${name}`);
        }
    }
    return value;
}

// Helper function parseGitHubDiffToCodeChanges (as before)
function parseGitHubDiffToCodeChanges(diff: string): Array<{ filename: string; patch: string; additions: number; deletions: number; status: string | undefined }> {
    console.warn("[parseGitHubDiffToCodeChanges] Minimal implementation. Input diff not actually parsed:", diff ? diff.substring(0,100) + "..." : "empty");
    // Placeholder: In a real scenario, this would parse the diff string.
    // For now, return an empty array to satisfy the type, or a basic structure if needed for downstream logic.
    return [];
}


export async function POST(request: NextRequest) {
	const logPrefix = '[API Ext TriggerADR]';
	console.log(`${logPrefix} Received ADR generation trigger request.`);

	// 1. Authentication (as before)
	const authHeader = request.headers.get('Authorization');
	const apiKey = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;
	if (!apiKey) {
        console.warn(`${logPrefix} Missing API Key.`);
        return NextResponse.json({ error: 'Missing API Key in Authorization header (Bearer token)' }, { status: 401 });
    }
	let userId: string | null = null;
	try {
        userId = await validateApiKeyAndGetUser(apiKey);
        if (!userId) {
            console.warn(`${logPrefix} Invalid API Key.`);
            return NextResponse.json({ error: 'Invalid API Key' }, { status: 401 });
        }
    } catch (error) {
        console.error(`${logPrefix} Error validating API key:`, error);
        return NextResponse.json({ error: 'API Key validation failed' }, { status: 500 });
    }
	console.log(`${logPrefix} API Key validated for User ID: ${userId}.`);

	// 2. Parse Request Body (as before)
	let body: { repository_slug: string; branch_name?: string; pr_number?: number | string };
	try {
        body = await request.json();
    } catch (error) {
        console.warn(`${logPrefix} Invalid JSON body.`);
        return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }
	const { repository_slug, branch_name, pr_number } = body;
	const targetIdentifier = pr_number ? `PR #${pr_number}` : branch_name || 'unknown target';
	if (!repository_slug || (!branch_name && !pr_number)) {
        console.warn(`${logPrefix} Missing required parameters.`);
        return NextResponse.json({ error: 'Missing required parameters: repository_slug (string) and either branch_name (string) or pr_number (number or string for commit SHA)' }, { status: 400 });
    }
	console.log(`${logPrefix} Request details - Repo: ${repository_slug}, Target: ${targetIdentifier}`);

	try {
		// 3. ---> Use getRepositoryAccessDetails to Verify Access and Get Installation ID <---
		console.log(`${logPrefix} Determining access for user ${userId} to repo ${repository_slug}...`);
		const accessDetails: RepositoryAccessDetails = await getRepositoryAccessDetails(userId, repository_slug);

		// Require PRIVATE access via installation for ADR generation
		if (accessDetails.type !== 'private' || !accessDetails.installationId || !accessDetails.namespace) {
			const errorMessage = accessDetails.message || `ADR Generation requires private repository access via an ArchKnow installation. Access type found: ${accessDetails.type}`;
			console.warn(`${logPrefix} Access denied for ADR generation. User ${userId}, repo ${repository_slug}. Details: ${JSON.stringify(accessDetails)}`);
			const status = accessDetails.type === 'forbidden' ? 403 : accessDetails.type === 'not_found' ? 404 : 400; // Use 400 if type is public/error
			return NextResponse.json({ error: errorMessage }, { status: status });
		}
		const installationId = accessDetails.installationId;
		const pineconeNamespace = accessDetails.namespace;
		console.log(`${logPrefix} Private access confirmed via Installation ID: ${installationId}. Using namespace: ${pineconeNamespace}`);
		// ---> End Access Check <---

		const installationRepositorySlug = repository_slug;
		const [owner, repo] = repository_slug.split('/');
		if (!owner || !repo) {
            return NextResponse.json({ error: 'Invalid repository_slug format (expected owner/repo)' }, { status: 400 });
        }

		// 4. Fetch Context from GitHub (using confirmed installationId)
		let prContext: any = {};
		let codeChanges: Array<{ filename: string; patch: string; additions?: number; deletions?: number; status?: string }> = [];
		let comments: Array<{ user: { login?: string } | null; body?: string | null; created_at?: string; }> = [];

		// ---> Use the confirmed installationId to get Octokit <---
		let octokit: Octokit | null = null;
		try {
			octokit = await getInstallationOctokit(installationId);
		} catch (octoError: any) {
			console.error(`${logPrefix} Failed to get Octokit instance for installation ${installationId}:`, octoError);
			// Proceed with minimal context if Octokit fails
			prContext = { title: `Manual ADR Gen - ${targetIdentifier} (GitHub Client Failed)`, body: `**Warning:** Failed to initialize GitHub client for context fetching.` };
		}
		// ---> End Octokit Init <---

		if (octokit && pr_number) { // Only fetch if Octokit is ready and PR number provided
			console.log(`${logPrefix} Fetching context for ${targetIdentifier} via GitHub API...`);
			try {
				// ... (GitHub API calls using `octokit` for PR details, diff, comments - same as before) ...
                const { data: prDetails } = await octokit.rest.pulls.get({ owner, repo, pull_number: typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10) });
                prContext = { // Map relevant fields
                    number: prDetails.number,
                    html_url: prDetails.html_url,
                    title: prDetails.title,
                    body: prDetails.body,
                    merged_at: prDetails.merged_at || new Date().toISOString(), // Use merge time or now
                    user: { login: prDetails.user?.login },
                    ref: prDetails.head.ref, // Branch name
                    base: { ref: prDetails.base.ref }, // Base branch
                    is_pseudo_pr: false // Explicitly mark as a real PR
                };
                const diffResponse = await octokit.rest.pulls.get({
                    owner, repo, pull_number: typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10),
                    mediaType: { format: 'diff' }
                });
                if (typeof diffResponse.data === 'string') codeChanges = parseGitHubDiffToCodeChanges(diffResponse.data);
                const [issueComments, reviewComments] = await Promise.all([
                    octokit.paginate(octokit.rest.issues.listComments, { owner, repo, issue_number: typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10) }),
                    octokit.paginate(octokit.rest.pulls.listReviewComments, { owner, repo, pull_number: typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10) })
                ]);
                comments = [...issueComments, ...reviewComments].map(c => ({
                    user: c.user ? { login: c.user.login } : null,
                    body: c.body,
                    created_at: c.created_at,
                }));
				console.log(`${logPrefix} Fetched full context for PR #${typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10)}.`);
			} catch (error) {
                if (error instanceof RequestError && error.status === 404) {
                    // For commit SHAs, attempt to fetch commit details if PR lookup fails
                    if (typeof pr_number === 'string' && pr_number.match(/^[0-9a-f]{7,40}$/i)) {
                        try {
                            // Try to fetch commit details using the SHA
                            const { data: commitData } = await octokit.rest.repos.getCommit({
                                owner, repo, ref: pr_number as string
                            });
                            
                            prContext = {
                                number: pr_number, // Use SHA as the number
                                html_url: commitData.html_url,
                                title: commitData.commit.message.split('\n')[0], // First line as title
                                body: commitData.commit.message.split('\n').slice(1).join('\n'), // Rest as body
                                merged_at: commitData.commit.author?.date || new Date().toISOString(),
                                user: { login: commitData.author?.login || commitData.commit.author?.name || 'unknown' },
                                is_pseudo_pr: true, // Mark as a pseudo PR
                                commit_sha: pr_number // Store the original commit SHA
                            };
                            
                            console.log(`${logPrefix} Successfully fetched commit data for SHA: ${pr_number}`);
                        } catch (commitError) {
                            console.error(`${logPrefix} Failed to fetch commit data for SHA: ${pr_number}`, commitError);
                            return NextResponse.json({ success: false, error: `Neither PR #${pr_number} nor commit with SHA ${pr_number} found.` }, { status: 404 });
                        }
                    } else {
                        console.error(`${logPrefix} GitHub Error: PR #${typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10)} not found in ${repository_slug}.`);
                        return NextResponse.json({ success: false, error: `Pull Request #${typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10)} not found.` }, { status: 404 });
                    }
                } else {
                    console.error(`${logPrefix} GitHub API Error fetching context for PR #${typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10)}:`, error);
                    prContext = { ...prContext, title: prContext.title || `Manual ADR Gen - PR ${typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10)} (Context Fetch Failed)`, body: `${prContext.body || ''}\n\n**Warning:** Failed to fetch full context from GitHub.` };
                }
            }
		} else if (octokit && branch_name) {
            // ... (Branch logic - still simplified) ...
            console.warn(`${logPrefix} Triggering by branch name (${branch_name}). Context will be limited.`);
            prContext = {
                number: 0, // Not a PR
                html_url: `local://${repository_slug}/${branch_name}`,
                title: `Manual ADR Generation Trigger for branch ${branch_name}`,
                body: `Triggered manually via VS Code extension for branch ${branch_name}. Context derived from branch state (limited).`,
                merged_at: new Date().toISOString(), // Use current time
                user: { login: `user-${userId}` },
                ref: branch_name,
            };
            codeChanges = []; comments = [];
        }
		else {
			// Handle case where Octokit failed or no target provided
			console.warn(`${logPrefix} Proceeding with minimal context due to missing target or Octokit failure.`);
            if (!prContext.title) prContext.title = `Manual ADR Gen - ${targetIdentifier} (Minimal Context)`;
            if (!prContext.body) prContext.body = `Triggered manually via VS Code extension. Full context may be missing.`;
            prContext.number = typeof pr_number === 'number' ? pr_number : parseInt(pr_number as string, 10);
            prContext.html_url = `local://${repository_slug}/${targetIdentifier}`;
            prContext.merged_at = new Date().toISOString();
		}

		// 5. Call Orchestrator (Synchronously, dryRun=false - as before)
		console.log(`${logPrefix} Calling orchestrator processMergedPR with dryRun=false. Namespace: ${pineconeNamespace}. Changes: ${codeChanges.length}, Comments: ${comments.length}`);
		const result = await processMergedPR(prContext, codeChanges, comments, pineconeNamespace, installationRepositorySlug, false, null) as ProcessMergedPRResult; // Add missing designDocContent argument
		console.log(`${logPrefix} Orchestrator finished (sync call). Status: ${result?.status}`);

		// 6. Respond to Extension (as before)
		if (result?.status?.startsWith('completed') || result?.status === 'feedback_generated' || result?.status === 'processing') {
            const message = `ADR generation process triggered successfully for ${repository_slug} (${targetIdentifier}). Results being processed.`;
            console.log(`${logPrefix} Trigger successful.`);
            return NextResponse.json({ success: true, message: message });
        } else {
            const errorMessage = result?.error || result?.reason || 'Orchestrator failed ADR generation.';
            console.error(`${logPrefix} Orchestrator call failed. Result:`, result);
            return NextResponse.json({ success: false, error: `Failed to trigger ADR generation: ${errorMessage}` }, { status: 500 });
        }

	} catch (error: any) {
        console.error(`${logPrefix} Unhandled error processing ADR trigger request for repo ${repository_slug}:`, error);
        return NextResponse.json({ success: false, error: 'Internal Server Error', details: error.message }, { status: 500 });
    }
} 