import { NextRequest, NextResponse } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { createClient, SupabaseClient } from '@supabase/supabase-js'; // For filtering superseded
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
// import { getPineconeIndex } from '@/lib/pinecone-client'; // REMOVE THIS - Implement locally
import { getRepositoryAccessDetails, RepositoryAccessDetails } from '@/lib/repository-access';
// import { getEnvVar } from '@/lib/utils'; // REMOVE THIS - Implement locally

// --- ADDED LOCAL HELPERS (Based on /api/decisions/route.ts pattern) ---

// Helper to get environment variables
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            console.warn(`[API Ext RelevantFile] Environment variable ${name} not found, using default value.`);
            return defaultValue;
        } else {
            throw new Error(`Missing required environment variable: ${name}`);
        }
    }
    return value;
}

let pinecone: Pinecone | null = null;
// Store initialized indexes per namespace to avoid re-initializing constantly
const pineconeIndexes: Record<string, any> = {}; 

// Initialize Pinecone client (only once)
async function initializePineconeClient() {
    if (!pinecone) {
        try {
            const apiKey = getEnvVar('PINECONE_API_KEY');
            pinecone = new Pinecone({ apiKey });
            console.log(`[API Ext RelevantFile] Pinecone client initialized.`);
        } catch (error: any) {
            console.error("[API Ext RelevantFile] Failed to initialize Pinecone client:", error);
            pinecone = null; 
        }
    }
}

// Get or initialize index for a specific namespace
async function getPineconeIndex(namespace: string) {
    await initializePineconeClient(); // Ensure client is ready
    if (!pinecone) {
        throw new Error("Pinecone client is not initialized.");
    }
    const indexCacheKey = namespace;
    if (!pineconeIndexes[indexCacheKey]) {
        const indexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');
        pineconeIndexes[indexCacheKey] = pinecone.Index(indexName).namespace(namespace);
        console.log(`[API Ext RelevantFile] Pinecone index handle initialized for namespace: ${namespace}`);
    }
    return pineconeIndexes[indexCacheKey];
}
// --- END ADDED LOCAL HELPERS ---

// ---> Supabase Client Initialization for filtering superseded decisions
// TODO: Consider moving this to a shared lib or injecting it
let supabase: SupabaseClient | null = null;
function initializeSupabaseClient() {
    if (!supabase) {
        try {
            const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL'); // Now uses local getEnvVar
            const supabaseServiceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY'); // Now uses local getEnvVar
            supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
                auth: { persistSession: false }
            });
            console.log("[API Ext RelevantFile] Supabase client initialized for filtering.");
        } catch (error: any) {
            console.error("[API Ext RelevantFile] Failed to initialize Supabase client:", error);
            supabase = null;
        }
    }
}
// --- END Supabase Init ---

// Define a type for the match object from Pinecone query response
interface PineconeMatch {
  id: string;
  score?: number; // score might be undefined if not requested or not present
  metadata?: any; // Keeping it flexible, but can be more specific if metadata structure is known
  // Add other properties from Pinecone's Match object if needed, like values, sparseValues
}

// Define a type for the objects in relevantDecisionsUnsorted and relevantDecisions
interface DecisionMatch {
    id: string;
    score?: number;
    metadata: {
        pr_merged_at?: number; // Based on usage in sort function
        // Add other expected metadata properties here
        [key: string]: any; // Allow other properties
    };
}

export async function POST(request: NextRequest) {
    // Initialize Supabase client needed for filtering later
    initializeSupabaseClient();

    // 1. Authenticate API Key
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null;

    if (!apiKey) {
        console.warn('[API Ext RelevantFile] Missing API Key');
        return NextResponse.json({ error: 'Missing API Key in Authorization header (ApiKey YOUR_KEY)' }, { status: 401 });
    }

    let userId: string | null;
    try {
        userId = await validateApiKeyAndGetUser(apiKey);
        if (!userId) {
            console.warn('[API Ext RelevantFile] Invalid API Key presented.');
            return NextResponse.json({ error: 'Invalid API Key' }, { status: 401 });
        }
        console.log(`[API Ext RelevantFile] API Key validated. User ID: ${userId}`);
    } catch (error: any) {
        console.error('[API Ext RelevantFile] Error validating API key:', error);
        return NextResponse.json({ error: 'Internal server error during authentication' }, { status: 500 });
    }

    // 2. Parse Request Body
    let body: { repository_slug: string; filePath: string; limit?: number };
    try {
        body = await request.json();
    } catch (error) {
        console.warn('[API Ext RelevantFile] Invalid JSON body');
        return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const { repository_slug, filePath, limit = 100 } = body; // Default limit similar to original
    if (!repository_slug || !filePath) {
        console.warn('[API Ext RelevantFile] Missing repository_slug or filePath');
        return NextResponse.json({ error: 'Missing required body parameters: repository_slug, filePath' }, { status: 400 });
    }
    console.log(`[API Ext RelevantFile] Request for repo: ${repository_slug}, file: ${filePath}, User: ${userId}`);

    // 3. Determine Repository Access & Pinecone Namespace
    let accessDetails: RepositoryAccessDetails;
    try {
        accessDetails = await getRepositoryAccessDetails(userId, repository_slug);
    } catch (error: any) {
        console.error(`[API Ext RelevantFile] Error determining repository access for ${repository_slug}, User ${userId}:`, error);
        return NextResponse.json({ error: 'Failed to determine repository access' }, { status: 500 });
    }

    // Handle different access outcomes
    switch (accessDetails.type) {
        case 'forbidden':
            console.warn(`[API Ext RelevantFile] Forbidden access for User ${userId} to repo ${repository_slug}`);
            return NextResponse.json({ error: 'Access Denied: User does not have permission for this repository via the GitHub App.' }, { status: 403 });
        case 'not_found':
            console.warn(`[API Ext RelevantFile] Repository not found: ${repository_slug}`);
            return NextResponse.json({ error: 'Repository not found or inaccessible.' }, { status: 404 });
         case 'error':
             console.error(`[API Ext RelevantFile] Access check failed for ${repository_slug}: ${accessDetails.message}`);
             return NextResponse.json({ error: `Access check failed: ${accessDetails.message}` }, { status: 500 });
        case 'public':
        case 'private':
            console.log(`[API Ext RelevantFile] Access granted (${accessDetails.type}). Namespace: ${accessDetails.namespace}`);
            break; // Continue below
        default:
            console.error(`[API Ext RelevantFile] Unexpected access details type:`, accessDetails);
            return NextResponse.json({ error: 'Internal server error during access check' }, { status: 500 });
    }

    // 4. Query Pinecone
    try {
        const pineconeNamespace = accessDetails.namespace!;
        const index = await getPineconeIndex(pineconeNamespace);

        const filterCriteria = {
            related_files: { $in: [filePath] }
        };
        console.debug(`[API Ext RelevantFile] Pinecone filter:`, JSON.stringify(filterCriteria));
        const dimension = 1536; // Assuming OpenAI embedding size
        const dummyVector = new Array(dimension).fill(0);
        
        const queryResponse = await index.query({
            vector: dummyVector,
            topK: limit, // Use limit from request or default
            filter: filterCriteria,
            includeMetadata: true,
            includeValues: false
        });

        let matches: PineconeMatch[] = queryResponse.matches || [];
        console.log(`[API Ext RelevantFile] Found ${matches.length} potential decisions referencing file '${filePath}' in namespace '${pineconeNamespace}'.`);

        // 5. Filter out superseded decisions using Supabase (if available)
        let supersededDecisionIds = new Set<string>();
        if (supabase && matches.length > 0) {
            console.log(`[API Ext RelevantFile] Fetching 'supersedes' relationships from Supabase for repo: ${repository_slug}`);
            try {
                const { data: relationshipRecords, error: relationshipsError } = await supabase
                    .from('decision_relationships')
                    .select('target_decision_pinecone_id')
                    .eq('repository_slug', repository_slug)
                    .eq('relationship_type', 'supersedes');

                if (relationshipsError) {
                    console.error('[API Ext RelevantFile] Supabase relationships fetch error:', relationshipsError);
                    // Proceed without filtering if Supabase fails
                } else if (relationshipRecords) {
                    relationshipRecords.forEach(rel => {
                        if (rel.target_decision_pinecone_id) {
                            supersededDecisionIds.add(rel.target_decision_pinecone_id);
                        }
                    });
                    console.log(`[API Ext RelevantFile] Identified ${supersededDecisionIds.size} superseded decision IDs.`);
                }
            } catch (supaError: any) {
                 console.error('[API Ext RelevantFile] Unexpected error fetching relationships from Supabase:', supaError);
                 // Proceed without filtering
            }

            const originalMatchCount = matches.length;
            matches = matches.filter((match: PineconeMatch) => !supersededDecisionIds.has(match.id));
            const filteredMatchCount = matches.length;
            if (originalMatchCount !== filteredMatchCount) {
                console.log(`[API Ext RelevantFile] Filtered out ${originalMatchCount - filteredMatchCount} superseded decisions. Returning ${filteredMatchCount}.`);
            }
        } else if (!supabase) {
             console.warn('[API Ext RelevantFile] Skipping superseded decision filtering because Supabase client is not available.');
        }

        // 6. Format and Sort Results
        const relevantDecisionsUnsorted: DecisionMatch[] = matches.map((match: PineconeMatch) => ({
            id: match.id,
            score: match.score, // Include score if needed
            metadata: match.metadata || {}, // Assign the entire metadata object
        }));

        const relevantDecisions: DecisionMatch[] = relevantDecisionsUnsorted.sort((a: DecisionMatch, b: DecisionMatch) => {
             // Access pr_merged_at through metadata now
            const timeA = (typeof a.metadata?.pr_merged_at === 'number' && a.metadata.pr_merged_at > 0) ? a.metadata.pr_merged_at : 0;
            const timeB = (typeof b.metadata?.pr_merged_at === 'number' && b.metadata.pr_merged_at > 0) ? b.metadata.pr_merged_at : 0;
            return timeB - timeA; // Descending order (newest first)
        });

        console.log(`[API Ext RelevantFile] Returning ${relevantDecisions.length} decisions.`);
        return NextResponse.json({ success: true, decisions: relevantDecisions });

    } catch (error: any) {
        console.error(`[API Ext RelevantFile] Error processing request for repo ${repository_slug}, file ${filePath}:`, error);
        const errorMessage = error.message?.includes('Namespace not found') || error.message?.includes('does not exist')
            ? 'No decision data found for this repository.'
            : 'Failed to fetch relevant decisions.';
        const statusCode = error.message?.includes('Namespace not found') || error.message?.includes('does not exist') ? 404 : 500;
        return NextResponse.json({ error: errorMessage, details: error.message }, { status: statusCode });
    }
} 