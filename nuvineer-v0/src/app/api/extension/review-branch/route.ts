import { NextRequest, NextResponse } from 'next/server';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails, RepositoryAccessDetails } from '@/lib/repository-access';
import { processMergedPR } from '@/orchestrator.js'; // Adjust path if needed
// import { Decision } from '@/types'; // Assuming a shared type definition - REMOVED

// --- Helper Functions (Can be moved to utils if shared) ---
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            console.warn(`[API BranchReview] Environment variable ${name} not found, using default value.`);
            return defaultValue;
        } else {
            throw new Error(`Missing required environment variable: ${name}`);
        }
    }
    return value;
}

// Define Decision types locally for now (consider moving to a shared @/types file)
interface DecisionMetadata {
    title: string;
    relevance_reason?: string; 
    description?: string | null;
    rationale?: string | null;
    pr_number?: number | string;
    pr_title?: string;
    pr_url?: string;
    pr_merged_at?: number | string | null; 
    relevant_files?: string[];
    created_at?: string;
    is_pseudo_pr?: boolean;
    commit_sha?: string;
    // Add other potential fields from your decision metadata
}

interface Decision {
    id: string; // Pinecone ID or similar unique identifier
    score?: number; 
    metadata: DecisionMetadata;
}

// Type for the result of processMergedPR
interface ProcessMergedPRResult {
    status: 'success' | 'no_decisions' | 'error' | string; // More specific statuses if known
    decisions?: Decision[];
    reason?: string;
    // Add any other properties returned by processMergedPR
}

// Basic type for the expected request body
interface BranchReviewRequestBody {
    repository_slug: string;
    prContext: {
        number: number | string;
        title: string;
        body?: string;
        html_url?: string;
        files?: Array<{ filename: string; additions?: number; deletions?: number }>;
        _isBranchReview?: boolean; // Optional marker
        is_pseudo_pr?: boolean;
        commit_sha?: string;
    };
    codeChanges: Array<{ filename: string; patch: string }>;
    dryRun: boolean; // Should always be true from the extension
}

// Basic type for the response
interface BranchReviewResponseBody {
    success: boolean;
    decisions?: Decision[]; // Assuming Decision type matches frontend/shared types
    error?: string;
}

export async function POST(request: NextRequest): Promise<NextResponse<BranchReviewResponseBody>> {
    const logPrefix = '[API BranchReview]';
    console.log(`${logPrefix} Received request.`);

    // 1. Authenticate API Key
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null;

    if (!apiKey) {
        console.warn(`${logPrefix} Missing API Key`);
        return NextResponse.json({ success: false, error: 'Missing API Key in Authorization header (ApiKey YOUR_KEY)' }, { status: 401 });
    }

    let userId: string | null;
    try {
        userId = await validateApiKeyAndGetUser(apiKey);
        if (!userId) {
            console.warn(`${logPrefix} Invalid API Key presented.`);
            return NextResponse.json({ success: false, error: 'Invalid API Key' }, { status: 401 });
        }
        console.log(`${logPrefix} API Key validated. User ID: ${userId}`);
    } catch (error: any) {
        console.error(`${logPrefix} Error validating API key:`, error);
        return NextResponse.json({ success: false, error: 'Internal server error during authentication' }, { status: 500 });
    }

    // 2. Parse Request Body
    let body: BranchReviewRequestBody;
    try {
        body = await request.json();
        // Validate required fields
        if (!body.repository_slug || !body.prContext || !body.codeChanges || typeof body.dryRun !== 'boolean') {
            throw new Error('Missing required fields in request body.');
        }
        // Ensure dryRun is true for this endpoint
        if (body.dryRun !== true) {
             console.warn(`${logPrefix} Received request with dryRun !== true. Forcing dryRun.`);
             body.dryRun = true;
        }

    } catch (error: any) {
        console.warn(`${logPrefix} Invalid request body:`, error.message);
        return NextResponse.json({ success: false, error: `Invalid request body: ${error.message}` }, { status: 400 });
    }

    const { repository_slug, prContext, codeChanges, dryRun } = body;
    console.log(`${logPrefix} Request for repo: ${repository_slug}, Branch: ${prContext.title}, User: ${userId}`);

    // 3. Determine Repository Access & Pinecone Namespace
    let accessDetails: RepositoryAccessDetails;
    try {
        accessDetails = await getRepositoryAccessDetails(userId, repository_slug);
    } catch (error: any) {
        console.error(`${logPrefix} Error determining repository access for ${repository_slug}, User ${userId}:`, error);
        return NextResponse.json({ success: false, error: 'Failed to determine repository access' }, { status: 500 });
    }

    // Handle access denial or errors
    switch (accessDetails.type) {
        case 'forbidden':
            console.warn(`${logPrefix} Forbidden access for User ${userId} to repo ${repository_slug}`);
            return NextResponse.json({ success: false, error: 'Access Denied: User does not have permission for this repository via the GitHub App.' }, { status: 403 });
        case 'not_found':
            console.warn(`${logPrefix} Repository not found: ${repository_slug}`);
            return NextResponse.json({ success: false, error: 'Repository not found or inaccessible.' }, { status: 404 });
         case 'error':
             console.error(`${logPrefix} Access check failed for ${repository_slug}: ${accessDetails.message}`);
             return NextResponse.json({ success: false, error: `Access check failed: ${accessDetails.message}` }, { status: 500 });
        case 'public':
        case 'private':
            console.log(`${logPrefix} Access granted (${accessDetails.type}). Namespace: ${accessDetails.namespace}`);
            break; // Proceed
        default:
            console.error(`${logPrefix} Unexpected access details type:`, accessDetails);
            return NextResponse.json({ success: false, error: 'Internal server error during access check' }, { status: 500 });
    }

    const pineconeNamespace = accessDetails.namespace!; // Namespace is guaranteed by the checks above

    // 4. Call Orchestrator in Dry Run Mode
    try {
        console.log(`${logPrefix} Calling orchestrator processMergedPR in dryRun mode...`);
        const result = await processMergedPR(
            prContext,
            codeChanges,
            [], // No real comments for a branch review
            pineconeNamespace,
            repository_slug, // Pass the validated repo slug
            true, // Explicitly pass true for dryRun
            null // Add missing designDocContent argument
        ) as ProcessMergedPRResult;

        console.log(`${logPrefix} Orchestrator returned status: ${result.status}`);

        if (result.status === 'success' || result.status === 'no_decisions') {
            console.log(`${logPrefix} Branch review processing successful. Returning ${result.decisions?.length || 0} decisions.`);
            // Ensure the decisions array exists, even if empty
            const decisionsToReturn = Array.isArray(result.decisions) ? result.decisions : [];
            return NextResponse.json({ success: true, decisions: decisionsToReturn });
        } else {
            // Handle orchestrator failure status
            console.error(`${logPrefix} Orchestrator failed: ${result.reason}`);
            return NextResponse.json({ success: false, error: `Analysis failed: ${result.reason}` }, { status: 500 });
        }

    } catch (error: any) {
        console.error(`${logPrefix} Error calling orchestrator processMergedPR:`, error);
        return NextResponse.json({ success: false, error: `Internal server error during analysis: ${error.message}` }, { status: 500 });
    }
} 