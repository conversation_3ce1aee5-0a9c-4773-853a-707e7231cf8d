import { NextResponse } from 'next/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let supabaseAdmin: SupabaseClient;

if (supabaseUrl && supabaseServiceRoleKey) {
  supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);
} else {
  console.error('Design Doc Status API: Supabase URL or Service Role Key is not defined.');
  // Allow module to load, but GET handler will fail if supabaseAdmin is not initialized.
}

// Updated Helper function to calculate progress based on current_phase and status
function calculateJobProgress(currentPhase: string, jobStatus: string): { phaseProgressMessage: string, progressPercentage: number } {
    let phaseProgressMessage = 'Processing...';
    let progressPercentage = 0;

    if (jobStatus === 'error') {
        phaseProgressMessage = `Error during: ${currentPhase}. Check error message.`;
        if (currentPhase.startsWith('phase0_')) progressPercentage = 5;
        else if (currentPhase.startsWith('phase1_')) progressPercentage = 20;
        else if (currentPhase.startsWith('phase2_')) progressPercentage = 40;
        else if (currentPhase.startsWith('phase3_doc_generation') || currentPhase === 'phase3_pending') progressPercentage = 60; // Error in initial Phase 3 draft
        else if (currentPhase.startsWith('phase3_5_')) progressPercentage = 80; // Error in Phase 3.5
        else progressPercentage = 2; 
        return { phaseProgressMessage, progressPercentage };
    }

    // Final completed state - check against the actual final state set by the worker
    if (jobStatus === 'completed' && currentPhase === 'phase3_5_datamodel_refinement_complete') {
        progressPercentage = 100;
        phaseProgressMessage = 'Design document successfully generated.';
        return { phaseProgressMessage, progressPercentage };
    }
    // Also handle the legacy 'completed' phase if some jobs might still use it, though P3.5 is the new final substantive step
    if (jobStatus === 'completed' && currentPhase === 'completed') {
        progressPercentage = 100;
        phaseProgressMessage = 'Design document successfully generated (legacy completion state).';
        return { phaseProgressMessage, progressPercentage };
    }


    // Phase 0: Initialization and Broad Context
    if (currentPhase.startsWith('phase0_')) {
        if (currentPhase === 'phase0_pending') { progressPercentage = 0; phaseProgressMessage = 'Phase 0: Pending initialization...'; }
        else if (currentPhase === 'phase0_extraction_context_inprogress') { progressPercentage = 5; phaseProgressMessage = 'Phase 0: Extracting concepts & context...'; }
        else if (currentPhase === 'phase0_extraction_context_complete') {
            progressPercentage = 15;
            phaseProgressMessage = jobStatus === 'pending' ? 'Phase 0 Complete. Waiting for Phase 1.' : 'Phase 0 Complete.';
        }
    }
    // Phase 1: Goals & Decision Points
    else if (currentPhase.startsWith('phase1_')) {
        if (currentPhase === 'phase1_pending') { progressPercentage = 15; phaseProgressMessage = 'Phase 1: Pending goals & decisions...';}
        else if (currentPhase === 'phase1_goals_decisions_inprogress') { progressPercentage = 20; phaseProgressMessage = 'Phase 1: Identifying goals & decision points...'; }
        else if (currentPhase === 'phase1_goals_decisions_complete') {
            progressPercentage = 35;
            phaseProgressMessage = jobStatus === 'pending' ? 'Phase 1 Complete. Waiting for Phase 2.' : 'Phase 1 Complete.';
        }
    }
    // Phase 2: Decision Point Analysis
    else if (currentPhase.startsWith('phase2_')) {
        if (currentPhase === 'phase2_pending') {progressPercentage = 35; phaseProgressMessage = 'Phase 2: Pending decision analysis...';} 
        else if (currentPhase === 'phase2_decision_context_inprogress') { progressPercentage = 40; phaseProgressMessage = 'Phase 2: Analyzing technical decision points...'; }
        else if (currentPhase === 'phase2_decision_context_complete') {
            progressPercentage = 55; // Adjusted as P3 and P3.5 will take more %%
            phaseProgressMessage = jobStatus === 'pending' ? 'Phase 2 Complete. Waiting for Phase 3 (Initial Draft).' : 'Phase 2 Complete.';
        }
    }
    // Phase 3: Initial Full Document Generation
    else if (currentPhase.startsWith('phase3_doc_generation') || currentPhase === 'phase3_pending') { // Matches phase3_pending, phase3_doc_generation_inprogress, phase3_doc_generation_complete
        if (currentPhase === 'phase3_pending') {progressPercentage = 55; phaseProgressMessage = 'Phase 3: Pending initial document draft...';} 
        else if (currentPhase === 'phase3_doc_generation_inprogress') { progressPercentage = 60; phaseProgressMessage = 'Phase 3: Generating initial design document draft...'; }
        else if (currentPhase === 'phase3_doc_generation_complete') { 
             progressPercentage = 75; // Completed initial draft, P3.5 is next
             phaseProgressMessage = jobStatus === 'pending' ? 'Phase 3 Draft Complete. Waiting for Phase 3.5 (Data Model Refinement).' : 'Phase 3 Draft Complete.';
        }
    }
    // Phase 3.5: Data Model Refinement
    else if (currentPhase.startsWith('phase3_5_')) {
        if (currentPhase === 'phase3_5_datamodel_refinement_pending') { // This state might be set if P3 completes and job becomes pending for P3.5
            progressPercentage = 75; 
            phaseProgressMessage = 'Phase 3.5: Pending data model refinement...';
        }
        else if (currentPhase === 'phase3_5_datamodel_refinement_inprogress') { 
            progressPercentage = 85; 
            phaseProgressMessage = 'Phase 3.5: Refining data models for compliance...'; 
        }
        // The state 'phase3_5_datamodel_refinement_complete' with jobStatus 'completed' is handled by the 100% completion check at the top.
        // If it's 'phase3_5_datamodel_refinement_complete' but jobStatus is still 'pending' (unlikely given current worker logic but for safety):
        else if (currentPhase === 'phase3_5_datamodel_refinement_complete' && jobStatus === 'pending'){
            progressPercentage = 95;
            phaseProgressMessage = 'Phase 3.5 Complete. Finalizing job status...';
        }
    }
    // Fallback for unknown phases if status is not error/completed
    else {
        phaseProgressMessage = `Processing: ${currentPhase}`;
        progressPercentage = Math.max(progressPercentage, 2); 
    }

    return { phaseProgressMessage, progressPercentage };
}

export async function GET(
  request: Request,
  { params }: { params: { jobId: string } }
) {
  if (!supabaseAdmin) {
    return NextResponse.json(
      { error: 'Server configuration error: Supabase client not initialized.' },
      { status: 500 }
    );
  }

  const jobId = params.jobId;

  if (!jobId) {
    return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
  }

  console.log(`[Status API - Job ${jobId}] Fetching status for job ID: ${jobId}`);

  try {
    const { data: job, error } = await supabaseAdmin
      .from('design_doc_jobs')
      .select('id, status, current_phase, error_message, task_title, phase0_output, phase1_output, phase2_output, phase3_output, phase3_5_refined_data_models_output, referenced_decisions, output_file_path, created_at, updated_at')
      .eq('id', jobId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { 
        console.warn(`[Status API - Job ${jobId}] Job ID ${jobId} not found.`);
        return NextResponse.json({ error: 'Job not found' }, { status: 404 });
      }
      console.error(`[Status API - Job ${jobId}] Error fetching job ${jobId}:`, error);
      return NextResponse.json({ error: `Failed to fetch job status: ${error.message}` }, { status: 500 });
    }

    if (!job) {
      console.warn(`[Status API - Job ${jobId}] Job ID ${jobId} not found (no data).`);
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    const { phaseProgressMessage, progressPercentage } = calculateJobProgress(job.current_phase, job.status);
    
    let displayMessage = phaseProgressMessage;
    if (job.status === 'error' && job.error_message) {
      displayMessage = job.error_message;
    } else if (job.status === 'completed' && (job.current_phase === 'completed' || job.current_phase === 'phase3_5_datamodel_refinement_complete')) {
      displayMessage = 'Design document generated successfully.';
    }

    // Determine if we should include phase outputs based on the current_phase and job_status
    const includePhase0 = job.phase0_output && 
                          (job.current_phase === 'phase0_extraction_context_complete' || 
                           job.current_phase.startsWith('phase1') || 
                           job.current_phase.startsWith('phase2') || 
                           job.current_phase.startsWith('phase3') || 
                           job.current_phase === 'completed');

    const includePhase1 = job.phase1_output && 
                         (job.current_phase === 'phase1_goals_decisions_complete' || 
                          job.current_phase.startsWith('phase2') || 
                          job.current_phase.startsWith('phase3') || 
                          job.current_phase === 'completed');
                         
    const includePhase2 = job.phase2_output && 
                         (job.current_phase === 'phase2_decision_context_complete' || 
                          job.current_phase.startsWith('phase3') || 
                          job.current_phase === 'completed');
                         
    const includePhase3 = job.phase3_output && 
                          job.status === 'completed' && 
                          (job.current_phase === 'completed' || job.current_phase === 'phase3_5_datamodel_refinement_complete');

    const includePhase3_5 = job.phase3_5_refined_data_models_output &&
                            job.status === 'completed' &&
                            (job.current_phase === 'completed' || job.current_phase === 'phase3_5_datamodel_refinement_complete');
                            
    const includeReferencedDecisions = job.referenced_decisions &&
                                       job.status === 'completed' &&
                                       (job.current_phase === 'completed' || job.current_phase === 'phase3_5_datamodel_refinement_complete');

    console.log(`[Status API - Job ${jobId}] Successfully fetched status for job ${jobId}: ${job.status} (${job.current_phase})`);
    console.log(`[Status API - Job ${jobId}] Raw job.phase3_5_refined_data_models_output from DB:`, job.phase3_5_refined_data_models_output);
    console.log(`[Status API - Job ${jobId}] Calculated includePhase3_5: ${includePhase3_5}`);
    console.log(`[Status API - Job ${jobId}] Value to be returned for phase3_5_refined_data_models_output:`, includePhase3_5 ? job.phase3_5_refined_data_models_output : null);
    console.log(`[Status API - Job ${jobId}] Referenced decisions included: ${includeReferencedDecisions}`);

    return NextResponse.json({
      jobId: job.id,
      status: job.status,
      currentPhase: job.current_phase,
      phaseProgress: displayMessage,
      progressPercentage,
      taskTitle: job.task_title,
      output_file_path: (job.status === 'completed' && (job.current_phase === 'completed' || job.current_phase === 'phase3_5_datamodel_refinement_complete')) ? job.output_file_path : null,
      phase0_output: includePhase0 ? job.phase0_output : null,
      phase1_output: includePhase1 ? job.phase1_output : null,
      phase2_output: includePhase2 ? job.phase2_output : null,
      phase3_output: includePhase3 ? job.phase3_output : null,
      phase3_5_refined_data_models_output: includePhase3_5 ? job.phase3_5_refined_data_models_output : null,
      referenced_decisions: includeReferencedDecisions ? job.referenced_decisions : null,
      created_at: job.created_at,
      updated_at: job.updated_at
    });

  } catch (e: any) {
    console.error(`[Status API - Job ${jobId}] Unhandled exception for job ID ${jobId}:`, e);
    return NextResponse.json({ error: `Internal server error: ${e.message}` }, { status: 500 });
  }
} 