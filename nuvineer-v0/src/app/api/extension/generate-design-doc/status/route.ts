import { NextResponse } from 'next/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let supabaseAdmin: SupabaseClient;

if (supabaseUrl && supabaseServiceRoleKey) {
  supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);
} else {
  console.error('Design Doc Status API: Supabase URL or Service Role Key is not defined.');
}

// Updated Helper function to calculate progress based on current_phase and status
function calculateJobProgress(currentPhase: string, jobStatus: string): { phaseProgressMessage: string, progressPercentage: number } {
    let phaseProgressMessage = 'Processing...';
    let progressPercentage = 0;

    if (jobStatus === 'error') {
        phaseProgressMessage = `Error during: ${currentPhase}. Check error message.`;
        if (currentPhase.startsWith('phase0_')) progressPercentage = 5;
        else if (currentPhase.startsWith('phase1_')) progressPercentage = 20;
        else if (currentPhase.startsWith('phase2_')) progressPercentage = 40;
        else if (currentPhase.startsWith('phase3_')) progressPercentage = 60; // Error in initial Phase 3 draft
        else if (currentPhase.startsWith('phase3_5_')) progressPercentage = 80; // Error in Phase 3.5
        else progressPercentage = 2; 
        return { phaseProgressMessage, progressPercentage };
    }

    // Final completed state - check against the actual final state set by the worker
    if (jobStatus === 'completed' && currentPhase === 'phase3_5_datamodel_refinement_complete') {
        progressPercentage = 100;
        phaseProgressMessage = 'Design document successfully generated.';
        return { phaseProgressMessage, progressPercentage };
    }
    // Also handle the legacy 'completed' phase if some jobs might still use it, though P3.5 is the new final substantive step
    if (jobStatus === 'completed' && currentPhase === 'completed') {
        progressPercentage = 100;
        phaseProgressMessage = 'Design document successfully generated (legacy completion state).';
        return { phaseProgressMessage, progressPercentage };
    }


    // Phase 0: Initialization and Broad Context
    if (currentPhase.startsWith('phase0_')) {
        if (currentPhase === 'phase0_pending') { progressPercentage = 0; phaseProgressMessage = 'Phase 0: Pending initialization...'; }
        else if (currentPhase === 'phase0_extraction_context_inprogress') { progressPercentage = 5; phaseProgressMessage = 'Phase 0: Extracting concepts & context...'; }
        else if (currentPhase === 'phase0_extraction_context_complete') {
            progressPercentage = 15;
            phaseProgressMessage = jobStatus === 'pending' ? 'Phase 0 Complete. Waiting for Phase 1.' : 'Phase 0 Complete.';
        }
    }
    // Phase 1: Goals & Decision Points
    else if (currentPhase.startsWith('phase1_')) {
        if (currentPhase === 'phase1_pending') { progressPercentage = 15; phaseProgressMessage = 'Phase 1: Pending goals & decisions...';}
        else if (currentPhase === 'phase1_goals_decisions_inprogress') { progressPercentage = 20; phaseProgressMessage = 'Phase 1: Identifying goals & decision points...'; }
        else if (currentPhase === 'phase1_goals_decisions_complete') {
            progressPercentage = 35;
            phaseProgressMessage = jobStatus === 'pending' ? 'Phase 1 Complete. Waiting for Phase 2.' : 'Phase 1 Complete.';
        }
    }
    // Phase 2: Decision Point Analysis
    else if (currentPhase.startsWith('phase2_')) {
        if (currentPhase === 'phase2_pending') {progressPercentage = 35; phaseProgressMessage = 'Phase 2: Pending decision analysis...';} 
        else if (currentPhase === 'phase2_decision_context_inprogress') { progressPercentage = 40; phaseProgressMessage = 'Phase 2: Analyzing technical decision points...'; }
        else if (currentPhase === 'phase2_decision_context_complete') {
            progressPercentage = 55; // Adjusted as P3 and P3.5 will take more %%
            phaseProgressMessage = jobStatus === 'pending' ? 'Phase 2 Complete. Waiting for Phase 3 (Initial Draft).' : 'Phase 2 Complete.';
        }
    }
    // Phase 3: Initial Full Document Generation
    else if (currentPhase.startsWith('phase3_doc_generation') || currentPhase === 'phase3_pending') { // Matches phase3_pending, phase3_doc_generation_inprogress, phase3_doc_generation_complete
        if (currentPhase === 'phase3_pending') {progressPercentage = 55; phaseProgressMessage = 'Phase 3: Pending initial document draft...';} 
        else if (currentPhase === 'phase3_doc_generation_inprogress') { progressPercentage = 60; phaseProgressMessage = 'Phase 3: Generating initial design document draft...'; }
        else if (currentPhase === 'phase3_doc_generation_complete') { 
             progressPercentage = 75; // Completed initial draft, P3.5 is next
             phaseProgressMessage = jobStatus === 'pending' ? 'Phase 3 Draft Complete. Waiting for Phase 3.5 (Data Model Refinement).' : 'Phase 3 Draft Complete.';
        }
    }
    // Phase 3.5: Data Model Refinement
    else if (currentPhase.startsWith('phase3_5_')) {
        if (currentPhase === 'phase3_5_datamodel_refinement_pending') { // This state might be set if P3 completes and job becomes pending for P3.5
            progressPercentage = 75; 
            phaseProgressMessage = 'Phase 3.5: Pending data model refinement...';
        }
        else if (currentPhase === 'phase3_5_datamodel_refinement_inprogress') { 
            progressPercentage = 85; 
            phaseProgressMessage = 'Phase 3.5: Refining data models for compliance...'; 
        }
        // The state 'phase3_5_datamodel_refinement_complete' with jobStatus 'completed' is handled by the 100% completion check at the top.
        // If it's 'phase3_5_datamodel_refinement_complete' but jobStatus is still 'pending' (unlikely given current worker logic but for safety):
        else if (currentPhase === 'phase3_5_datamodel_refinement_complete' && jobStatus === 'pending'){
            progressPercentage = 95;
            phaseProgressMessage = 'Phase 3.5 Complete. Finalizing job status...';
        }
    }
    // Fallback for unknown phases if status is not error/completed
    else {
        phaseProgressMessage = `Processing: ${currentPhase}`;
        progressPercentage = Math.max(progressPercentage, 2); 
    }

    return { phaseProgressMessage, progressPercentage };
}

export async function GET(request: Request) {
  if (!supabaseAdmin) {
    return NextResponse.json(
      { error: 'Server configuration error: Supabase client not initialized.' },
      { status: 500 }
    );
  }

  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('ApiKey ')) {
    return NextResponse.json({ error: 'API Key is required' }, { status: 401 });
  }
  const apiKey = authHeader.replace('ApiKey ', '');

  const url = new URL(request.url);
  const githubHandle = url.searchParams.get('github_handle');
  if (!githubHandle) {
    return NextResponse.json({ error: 'GitHub handle is required' }, { status: 400 });
  }

  const repositorySlug = url.searchParams.get('repository_slug');
  if (!repositorySlug) {
    return NextResponse.json({ error: 'Repository slug is required' }, { status: 400 });
  }

  const userId = await validateApiKeyAndGetUser(apiKey);
  if (!userId) {
    return NextResponse.json({ error: 'Invalid API Key' }, { status: 401 });
  }

  try {
    const { data: jobs, error } = await supabaseAdmin
      .from('design_doc_jobs')
      .select('id, status, current_phase, task_title, created_at, updated_at, error_message, output_file_path, phase1_output, phase2_output, phase3_output, phase3_5_refined_data_models_output, referenced_decisions')
      .eq('user_id', userId)
      .eq('github_handle', githubHandle)
      .eq('repository_slug', repositorySlug)
      .order('created_at', { ascending: false });

    if (error) {
      console.error(`[Status API] Error fetching jobs for user ${userId}, handle ${githubHandle}, repo ${repositorySlug}:`, error);
      return NextResponse.json({ error: `Failed to fetch jobs: ${error.message}` }, { status: 500 });
    }

    const transformedJobs = jobs.map(job => {
      const { phaseProgressMessage, progressPercentage } = calculateJobProgress(job.current_phase, job.status);
      
      let displayMessage = phaseProgressMessage;
      if (job.status === 'error' && job.error_message) {
        displayMessage = job.error_message;
      } else if (job.status === 'completed' && job.current_phase === 'phase3_5_datamodel_refinement_complete') { 
        displayMessage = 'Design document generated successfully.';
      } else if (job.status === 'completed' && job.current_phase === 'completed') { // Legacy completion
         displayMessage = 'Design document generated successfully.';
      }

      return {
        jobId: job.id,
        status: job.status,
        currentPhase: job.current_phase,
        phaseProgress: displayMessage,
        progressPercentage,
        taskTitle: job.task_title,
        // Ensure output_file_path is shown for the correct completed state
        output_file_path: (job.status === 'completed' && (job.current_phase === 'phase3_5_datamodel_refinement_complete' || job.current_phase === 'completed')) ? job.output_file_path : null, 
        phase3_output: (job.status === 'completed' && (job.current_phase === 'phase3_5_datamodel_refinement_complete' || job.current_phase === 'completed')) ? job.phase3_output : undefined,
        phase3_5_refined_data_models_output: (job.status === 'completed' && (job.current_phase === 'phase3_5_datamodel_refinement_complete' || job.current_phase === 'completed')) ? job.phase3_5_refined_data_models_output : undefined,
        referenced_decisions: (job.status === 'completed' && (job.current_phase === 'phase3_5_datamodel_refinement_complete' || job.current_phase === 'completed')) ? job.referenced_decisions : undefined,
        created_at: job.created_at,
        updated_at: job.updated_at
      };
    });

    return NextResponse.json(transformedJobs);
  } catch (e: any) {
    console.error(`[Status API] Unhandled exception:`, e);
    return NextResponse.json({ error: `Internal server error: ${e.message}` }, { status: 500 });
  }
} 