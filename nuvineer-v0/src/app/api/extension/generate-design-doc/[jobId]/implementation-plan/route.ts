import { NextRequest, NextResponse } from 'next/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { processPhase4, DesignDocJob } from '../../../../../../lib/designDocProcessor'; // Adjust path as needed

// Ensure these are set in your environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Supabase URL or Service Role Key not configured.');
  // Optionally throw an error or handle appropriately if running in a context where this is critical at startup
}

const supabaseAdmin: SupabaseClient = createClient(supabaseUrl!, supabaseServiceRoleKey!); 

export async function POST(
    req: NextRequest,
    { params }: { params: { jobId: string } }
) {
    const { jobId } = params;

    let requestBody;
    try {
        requestBody = await req.json();
    } catch (error) {
        console.error('Error parsing request body:', error);
        return NextResponse.json({ error: 'Invalid request body. Expecting JSON.' }, { status: 400 });
    }

    const { finalized_design_doc_markdown, ai_agent_protocol, document_title } = requestBody;

    if (!finalized_design_doc_markdown || typeof finalized_design_doc_markdown !== 'string') {
        return NextResponse.json({ 
            error: 'Missing or invalid required field in request body. Required: finalized_design_doc_markdown (string).' 
        }, { status: 400 });
    }

    // Log the AI agent protocol if provided
    if (ai_agent_protocol) {
        console.log(`[Job ${jobId}] Implementation Plan: Received AI Agent Protocol (length: ${ai_agent_protocol.length} characters)`);
    } else {
        console.log(`[Job ${jobId}] Implementation Plan: No AI Agent Protocol provided in request`);
    }

    if (document_title) {
        console.log(`[Job ${jobId}] Implementation Plan: Document title: "${document_title}"`);
    }

    let job: DesignDocJob | null = null;

    try {
        // 1. Fetch the job
        console.log(`[Job ${jobId}] Implementation Plan: Fetching job details.`);
        const { data: jobData, error: fetchError } = await supabaseAdmin
            .from('design_doc_jobs')
            .select('*')
            .eq('id', jobId)
            .single();

        if (fetchError || !jobData) {
            console.error(`[Job ${jobId}] Implementation Plan: Error fetching job - ${fetchError?.message}`);
            return NextResponse.json({ error: `Failed to fetch job ${jobId}: ${fetchError?.message || 'Job not found'}` }, { status: 404 });
        }
        job = jobData as DesignDocJob;

        // 2. Update job with markdown and set status to 'generating'
        console.log(`[Job ${jobId}] Implementation Plan: Updating with finalized markdown and status to 'generating'.`);
        const updatePayloadStep1 = {
            finalized_design_doc_markdown: finalized_design_doc_markdown,
            implementation_plan_status: 'generating' as const,
            updated_at: new Date().toISOString(),
            error_message: undefined // Clear previous errors
        };
        const { error: updateError1 } = await supabaseAdmin
            .from('design_doc_jobs')
            .update(updatePayloadStep1)
            .eq('id', jobId);

        if (updateError1) {
            console.error(`[Job ${jobId}] Implementation Plan: Error updating job with markdown - ${updateError1.message}`);
            throw new Error(`Failed to update job ${jobId} with markdown: ${updateError1.message}`);
        }
        
        // Update local job object with the changes for processPhase4
        job = { ...job, ...updatePayloadStep1 };

        // 3. Call processPhase4 with additional context
        console.log(`[Job ${jobId}] Implementation Plan: Calling processPhase4.`);
        if (!job) { // Should not happen if fetch was successful and update didn't clear it, but good for type safety
            throw new Error(`[Job ${jobId}] Critical error: Job object became null before calling processPhase4.`);
        }
        
        // Add AI agent protocol context to the job if provided
        if (ai_agent_protocol) {
            job.ai_agent_protocol = ai_agent_protocol;
        }
        if (document_title) {
            job.document_title = document_title;
        }
        
        const phase4Result = await processPhase4(job);
        console.log(`[Job ${jobId}] Implementation Plan: processPhase4 completed.`);

        // 4. Update job with phase4_output and set status to 'completed'
        console.log(`[Job ${jobId}] Implementation Plan: Updating with phase4_output and status to 'completed'.`);
        const updatePayloadStep2 = {
            phase4_output: phase4Result,
            implementation_plan_status: 'completed' as const,
            updated_at: new Date().toISOString(),
            error_message: undefined // Clear if any error occurred during processPhase4 itself and was logged in job
        };
        const { error: updateError2 } = await supabaseAdmin
            .from('design_doc_jobs')
            .update(updatePayloadStep2)
            .eq('id', jobId);

        if (updateError2) {
            console.error(`[Job ${jobId}] Implementation Plan: Error updating job with phase4_output - ${updateError2.message}`);
            throw new Error(`Failed to update job ${jobId} with Phase 4 output: ${updateError2.message}`);
        }

        console.log(`[Job ${jobId}] Implementation plan generated and saved successfully.`);
        return NextResponse.json({ 
            message: 'Implementation plan generated and saved successfully.',
            jobId: jobId,
            implementationPlan: phase4Result 
        });

    } catch (error: any) {
        console.error(`[Job ${jobId}] Implementation Plan: General error - ${error.message}`);
        if (jobId) { // Ensure jobId is available to update the error status
            try {
                await supabaseAdmin
                    .from('design_doc_jobs')
                    .update({
                        implementation_plan_status: 'error' as const,
                        error_message: `Implementation Plan Generation Failed: ${error.message}`,
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', jobId);
            } catch (dbError: any) {
                console.error(`[Job ${jobId}] Implementation Plan: CRITICAL - Failed to update job status to error after previous failure: ${dbError.message}`);
            }
        }
        return NextResponse.json({ error: error.message || 'Failed to generate and save implementation plan.', details: error.toString() }, { status: 500 });
    }
}

export async function GET(req: NextRequest, { params }: { params: { jobId: string } }) {
    const { jobId } = params;
    // Basic GET handler, can be expanded later if needed to fetch plan status or content
    return NextResponse.json({ 
        message: `Status for implementation plan generation for Job ID: ${jobId}. Use POST to initiate. POST body: { finalized_design_doc_markdown: string }`,
    });
} 