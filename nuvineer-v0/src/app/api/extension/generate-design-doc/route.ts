import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails } from '@/lib/repository-access';

// --- Interfaces (should match definitions from previous discussion) ---

interface GenerateDesignDocRequest {
  taskTitle: string;
  taskDescription: string;
  initialApproachIdeas?: string;
  repository_slug: string;
  api_key: string;
  github_handle: string;  // Add GitHub handle to the request interface
}

interface Milestone {
  name: string;
  description: string;
  priority: 'High' | 'Medium' | 'Low';
  complexity: 'High' | 'Medium' | 'Low';
  owner?: string;
  dependencies?: string[];
  successIndicators?: string[];
}

interface EvaluatedApproach {
  approachName: string;
  description: string;
  pros?: string[];
  cons?: string[];
  alignmentWithContext?: string;
  referencedDecisionIds?: string[];
}

interface AlternativesAnalysis {
  evaluatedApproaches: EvaluatedApproach[];
  recommendation?: string;
}

interface ReferencedDecision {
  id: string;
  title: string;
  summaryOfRelevance: string;
  implications?: string;
  rationale?: string;
  dev_prompt?: string;
  related_files?: string[];
}

interface TechnicalDecision {
  decision: string;
  rationale: string;
  implications: string;
}

interface SystemConstraint {
  constraint: string;
  enforcement: string;
  userCommunication: string;
}

interface ErrorScenario {
  scenario: string;
  detection: string;
  userMessage: string;
  recovery: string;
}

interface Component {
  name: string;
  responsibility: string;
}

interface TechnicalDecisions {
  criticalChoices: TechnicalDecision[];
  systemConstraints: SystemConstraint[];
}

interface ErrorHandling {
  scenarios: ErrorScenario[];
  retryStrategy: string;
  fallbackBehavior: string;
}

interface CoreFlow {
  overview: string;
  components: Component[];
  dataModelChanges: string;
}

interface Infrastructure {
  deployment: string;
  monitoring: string;
  rollback: string;
  security: string;
}

interface DeveloperNotes {
  referencedDecisions: string[];
  implementationGuidance: string;
}

interface HighLevelDesign {
  technicalDecisions: TechnicalDecisions;
  errorHandling: ErrorHandling;
  coreFlow: CoreFlow;
  processFlow: string;
  infrastructure: Infrastructure;
  developerNotes: DeveloperNotes;
}

interface GenerateDesignDocResponse {
  suggestedFilePath: string;
  title: string;
  goals: string[];
  nonGoals: string[];
  highLevelDesign: HighLevelDesign;
  alternativesAnalysis: AlternativesAnalysis;
  milestones: Milestone[];
  successMetrics: string[];
  referencedDecisions: ReferencedDecision[];
}

interface GenerateDesignDocJobResponse {
  jobId: string;
  message: string;
}

// --- Environment Variable Helper ---
function getEnvVar(name: string, defaultValue: string | null = null): string {
  const value = process.env[name];
  if (value === undefined) {
    if (defaultValue === null) {
      throw new Error(`Missing environment variable: ${name}`);
    }
    console.warn(`Missing environment variable ${name}, using default value.`);
    return defaultValue;
  }
  return value;
}

// --- Initialize Supabase Client ---
const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
const supabaseServiceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY');

let supabaseAdmin: ReturnType<typeof createClient> | null = null;
if (supabaseUrl && supabaseServiceRoleKey) {
  try {
    supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
      auth: { persistSession: false },
    });
    console.log("[Design Doc API - Job Submission] Supabase admin client initialized.");
  } catch (e:any) {
      console.error("[Design Doc API - Job Submission] Error initializing Supabase client:", e.message);
  }
} else {
  console.error("[Design Doc API - Job Submission] Supabase URL or Service Role Key missing. Supabase client not initialized.");
}

// --- API Route Handler (Refactored to enqueue job for worker) ---
export async function POST(request: Request) {
  const callId = `api-designdoc-enqueue-${Date.now()}`;
  console.log(`[${callId}] [API Request] Received request to enqueue design document job.`);
  let reqBody: GenerateDesignDocRequest;

  if (!supabaseAdmin) {
    console.error(`[${callId}] [API Request] Supabase client not initialized. Cannot create job.`);
    return NextResponse.json({ message: "Server configuration error: Database client not available." }, { status: 500 });
  }

  try {
    reqBody = await request.json();
    console.log(`[${callId}] [API Request] Parsed request body.`);
  } catch (error: any) {
    console.error(`[${callId}] [API Request] Error parsing request body:`, error.message);
    return NextResponse.json({ message: "Invalid request body", error: error.message }, { status: 400 });
  }

  const { taskTitle, taskDescription, initialApproachIdeas, repository_slug, api_key, github_handle } = reqBody;

  const missingParams = Object.entries({ taskTitle, taskDescription, repository_slug, api_key, github_handle })
    .filter(([, value]) => !value)
    .map(([key]) => key);

  if (missingParams.length > 0) {
    console.warn(`[${callId}] [API Request] Missing required parameters: ${missingParams.join(', ')}`);
    return NextResponse.json(
      { message: `Missing required parameters: ${missingParams.join(', ')}` },
      { status: 400 }
    );
  }

  let user_id: string | null;
  let installation_id: number | undefined;

  try {
    user_id = await validateApiKeyAndGetUser(api_key);
    if (!user_id) {
      console.warn(`[${callId}] [API Auth] Invalid API key presented by github_handle: ${github_handle}.`);
      return NextResponse.json({ message: "Invalid API key." }, { status: 401 });
    }
    console.log(`[${callId}] [API Auth] API key validated. User ID: ${user_id}, GitHub Handle: ${github_handle}`);

    const accessDetails = await getRepositoryAccessDetails(user_id, repository_slug);

    if (!accessDetails) { // Case: getRepositoryAccessDetails failed entirely
        console.error(`[${callId}] [Repo Access] Failed to get any repository access details for user ${user_id}, repo ${repository_slug}.`);
        return NextResponse.json({ message: "Failed to retrieve repository access details." }, { status: 500 });
    }

    // Check if installationId is undefined
    if (typeof accessDetails.installationId === 'undefined') {
        if (accessDetails.type === 'public') {
            console.log(`[${callId}] [Repo Access] Public repository ${repository_slug}. Defaulting installationId to 0 as it was undefined.`);
            installation_id = 0; // Default to 0 for public repositories if undefined
        } else {
            // For private repos, or other unexpected types without an installationId, it's an error
            console.error(`[${callId}] [Repo Access] Failed to get installationId for non-public repo ${repository_slug} (installationId was undefined). Type: ${accessDetails.type}`);
            if (accessDetails.type !== 'private') { // e.g. if type is some error indicator from getRepositoryAccessDetails
                 return NextResponse.json({ message: `Repository access issue: ${accessDetails.message || 'Unknown repository access problem for type ' + accessDetails.type}` }, { status: 403 });
            }
            return NextResponse.json({ message: "Failed to determine repository installation ID for non-public repository (installationId was undefined)." }, { status: 500 });
        }
    } else {
        // installationId is defined (this could be 0 if getRepositoryAccessDetails already sets it so for public repos, or another number)
        installation_id = accessDetails.installationId;
    }

    console.log(`[${callId}] [Repo Access] Using installation_id: ${installation_id} for repo ${repository_slug} (Type: ${accessDetails.type})`);

  } catch (error:any) {
    console.error(`[${callId}] [API Auth/Repo Access] Error during API key validation or fetching repo details:`, error.message);
    return NextResponse.json({ message: "API key validation or repository access failed.", error: error.message }, { status: 500 });
  }

  const jobId = uuidv4();
  console.log(`[${callId}] [API Logic] Generated Job ID: ${jobId}`);

  const newJobData = {
    id: jobId,
    user_id: user_id,
    github_handle: github_handle,
    repository_slug: repository_slug,
    installation_id: installation_id,
    task_title: taskTitle,
    task_description: taskDescription,
    initial_ideas: initialApproachIdeas || null,
    status: 'pending',
    current_phase: 'phase0_pending',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  try {
    const { error: insertError } = await supabaseAdmin
      .from('design_doc_jobs')
      .insert(newJobData);

    if (insertError) {
      console.error(`[${callId}] [Job Creation] Supabase error inserting new job ${jobId}:`, insertError);
      if (insertError.message.includes('installation_id')) {
          console.error(`[${callId}] [Job Creation] Possible issue with installation_id constraint for job ${jobId}. Value: ${installation_id}`);
      }
      return NextResponse.json({ message: "Failed to create job.", error: insertError.message }, { status: 500 });
    }

    console.log(`[${callId}] [Job Creation] Successfully created and enqueued job ${jobId} with installation_id: ${installation_id}.`);
    return NextResponse.json({ jobId, message: "Design document generation job successfully enqueued." }, { status: 202 });

  } catch (e: any) {
    console.error(`[${callId}] [Job Creation] Unexpected error creating job ${jobId}:`, e.message);
    return NextResponse.json({ message: "An unexpected error occurred while creating the job.", error: e.message }, { status: 500 });
  }
}