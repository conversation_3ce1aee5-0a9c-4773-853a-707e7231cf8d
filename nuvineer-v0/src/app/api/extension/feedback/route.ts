import { NextRequest, NextResponse } from 'next/server';
import { processMergedPR } from '@/orchestrator'; // Adjust path as per your project structure
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService'; // Assuming API key validation
import { getRepositoryAccessDetails, RepositoryAccessDetails } from '@/lib/repository-access'; // Use correct path alias
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import Anthropic from '@anthropic-ai/sdk';

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!,
});

// Helper to safely get env vars or use defaults
function getEnvVar(name: string, defaultValue: string | null = null) {
  const value = process.env[name];
  return value || defaultValue;
}

// Supabase setup for fetching implementation plans
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAdmin: SupabaseClient = createClient(supabaseUrl!, supabaseServiceRoleKey!);

// Define an interface for the expected result from the orchestrator
interface OrchestratorResult {
  status?: string; // e.g., 'feedback_generated', 'error_occurred'
  feedback?: string | any; // Can be string or parsed JSON object
  error?: string;
  referenced_decisions?: Array<{
    id: string;
    title: string;
    description: string;
    implications?: string;
    rationale?: string;
    dev_prompt?: string;
    related_files?: string[];
    metadata?: any;
  }>;
  milestone_context?: any;
  feedback_mode?: string;
  // Potentially other fields if the orchestrator returns more
}

interface MilestoneContext {
  milestone_id: string;
  title: string;
  description: string;
  key_tasks_and_deliverables: Array<{
    task: string;
    validation_criteria: string;
  }> | string[];
  scope_validation: {
    planned_deliverables: string[];
  };
  verification_criteria: string[];
  priority: string;
}

// Enhanced function to extract milestone from branch name and fetch related plan
async function getMilestoneContextFromJob(jobId: string, branchName: string, featurePrefix: string): Promise<MilestoneContext | null> {
  try {
    console.log(`[Feedback] Fetching implementation plan for job ${jobId} to validate milestone scope`);
    
    // Fetch the job and its implementation plan
    const { data: jobData, error: fetchError } = await supabaseAdmin
      .from('design_doc_jobs')
      .select('phase4_output, finalized_design_doc_markdown, task_title')
      .eq('id', jobId)
      .single();

    if (fetchError || !jobData) {
      console.warn(`[Feedback] Could not fetch job ${jobId} for milestone context: ${fetchError?.message}`);
      return null;
    }

    const implementationPlan = jobData.phase4_output;
    if (!implementationPlan || !implementationPlan.milestones) {
      console.warn(`[Feedback] No implementation plan found for job ${jobId}`);
      return null;
    }

    // Extract milestone number from branch name pattern: feature/design-identifier/milestone-N-description
    const milestoneMatch = branchName.match(/milestone-(\d+)/i);
    if (!milestoneMatch) {
      console.warn(`[Feedback] Could not extract milestone number from branch: ${branchName}`);
      return null;
    }

    const milestoneNumber = parseInt(milestoneMatch[1]);
    const targetMilestone = implementationPlan.milestones.find((m: any) => 
      m.milestone_id === `M${milestoneNumber}.0` || 
      m.git_must_use_milestone_branch_name?.includes(`milestone-${milestoneNumber}`)
    );

    if (!targetMilestone) {
      console.warn(`[Feedback] Could not find milestone ${milestoneNumber} in implementation plan for job ${jobId}`);
      return null;
    }

    console.log(`[Feedback] Found milestone context: ${targetMilestone.title || targetMilestone.milestone_id}`);
    return targetMilestone as MilestoneContext;

  } catch (error: any) {
    console.error(`[Feedback] Error fetching milestone context for job ${jobId}:`, error);
    return null;
  }
}

// Enhanced function to generate milestone-focused feedback prompt
function generateMilestoneFeedbackPrompt(
  codeChanges: Array<{ filename: string; patch: string; additions?: number; deletions?: number }>,
  milestoneContext: MilestoneContext,
  existingContextString: string,
  branchName: string,
  feedbackMode: 'detailed' | 'streamlined' = 'detailed'
): string {
  const totalLinesChanged = codeChanges.reduce((sum, change) => 
    sum + (change.additions || 0) + (change.deletions || 0), 0
  );

  const formattedChanges = codeChanges.map(change => `
**File: ${change.filename}**
${change.patch}
  `).join('\n');

  if (feedbackMode === 'streamlined') {
    return `You are an expert software architect performing CRITICAL milestone validation. Your goal is to identify ONLY extremely high-value issues that would block milestone integration.

**MILESTONE SCOPE:**
${milestoneContext.milestone_id}: ${milestoneContext.title}
Priority: ${milestoneContext.priority}
Branch: ${branchName}

**PLANNED DELIVERABLES:**
${Array.isArray(milestoneContext.key_tasks_and_deliverables) 
  ? milestoneContext.key_tasks_and_deliverables.map((task, i) => 
      typeof task === 'string' 
        ? `${i + 1}. ${task}`
        : `${i + 1}. ${task.task}`
    ).join('\n')
  : milestoneContext.key_tasks_and_deliverables
}

**VERIFICATION CRITERIA:**
${milestoneContext.verification_criteria.map((criteria, i) => `${i + 1}. ${criteria}`).join('\n')}

**CODE CHANGES (${totalLinesChanged} lines):**
${formattedChanges}

**EXISTING ARCHITECTURAL CONTEXT:**
${existingContextString || 'No specific existing context found.'}

**EVALUATION CRITERIA:**
Only report issues that meet ALL of these conditions:
1. Directly related to current milestone scope
2. Creates immediate security vulnerability OR critical UX failure
3. Would prevent milestone integration or cause production incidents

**OUTPUT FORMAT:**
Provide ONLY a JSON response with this exact structure:

{
  "scope_status": "COMPLIANT" | "MAJOR_DEVIATION",
  "critical_security_risks": [
    {
      "risk": "specific security vulnerability description",
      "severity": "CRITICAL", 
      "impact": "concrete impact on users/system",
      "fix": "specific action to resolve"
    }
  ],
  "critical_ux_risks": [
    {
      "risk": "specific UX failure description",
      "severity": "CRITICAL",
      "impact": "concrete user impact", 
      "fix": "specific action to resolve"
    }
  ],
  "scope_deviations": [
    {
      "deviation": "what was planned vs what was implemented",
      "impact": "why this blocks milestone goals",
      "action": "specific next step required"
    }
  ],
  "integration_blocked": true | false,
  "summary": "1-2 sentence summary of blocking issues, or 'No critical issues identified.'"
}

If no critical issues exist, return empty arrays for risks and deviations, set integration_blocked to false, and provide appropriate summary.

CRITICAL: Only include issues that would genuinely prevent safe milestone integration. Do not include general suggestions, minor improvements, or non-critical concerns.`;
  }

  // Original detailed feedback prompt
  return `You are an expert software architect providing milestone-focused feedback on staged code changes. Your goal is to validate milestone scope compliance and identify new risks introduced.

**MILESTONE CONTEXT:**
- Milestone ID: ${milestoneContext.milestone_id}
- Title: ${milestoneContext.title}
- Description: ${milestoneContext.description}
- Priority: ${milestoneContext.priority}
- Branch: ${branchName}

**PLANNED MILESTONE DELIVERABLES:**
${Array.isArray(milestoneContext.key_tasks_and_deliverables) 
  ? milestoneContext.key_tasks_and_deliverables.map((task, i) => 
      typeof task === 'string' 
        ? `${i + 1}. ${task}`
        : `${i + 1}. ${task.task} (Validation: ${task.validation_criteria})`
    ).join('\n')
  : milestoneContext.key_tasks_and_deliverables
}

**PLANNED SCOPE BOUNDARIES:**
${milestoneContext.scope_validation.planned_deliverables.map((deliverable, i) => `${i + 1}. ${deliverable}`).join('\n')}

**VERIFICATION CRITERIA:**
${milestoneContext.verification_criteria.map((criteria, i) => `${i + 1}. ${criteria}`).join('\n')}

**STAGED CODE CHANGES (${totalLinesChanged} lines changed):**
${formattedChanges}

**EXISTING ARCHITECTURAL CONTEXT:**
${existingContextString || 'No specific existing context found.'}

**FEEDBACK REQUIREMENTS:**

## 1. MILESTONE SCOPE VALIDATION
Analyze the staged changes against the planned milestone deliverables:
- **Scope Compliance**: Rate as COMPLETE | PARTIAL | EXCEEDED | DEVIATED
- **Coverage Analysis**: Which planned deliverables are addressed by these changes?
- **Scope Deviations**: List any unplanned features or missing planned features
- **Justification Assessment**: If deviated, assess if changes are justified for milestone goals

## 2. NEW RISK ASSESSMENT (CRITICAL)
Systematically identify NEW risks introduced by these specific changes:

**Security Risks (NEW):**
- Authentication/authorization vulnerabilities
- Data exposure or privacy concerns  
- Input validation gaps
- Injection attack vectors
- Session management issues

**Performance Risks (NEW):**
- Memory leaks or resource exhaustion
- Inefficient database queries or API calls
- Blocking operations or deadlocks
- Scalability bottlenecks
- Cache misuse or invalidation issues

**UX/Accessibility Risks (NEW):**
- Broken user workflows or confusing interfaces
- Missing loading states or error handling
- Accessibility violations (WCAG)
- Responsive design issues
- Data loss scenarios

**Integration Risks (NEW):**
- Breaking API changes or contract violations
- Dependency conflicts or version issues
- Migration problems or data integrity issues
- Backward compatibility breaks

**Milestone-Specific Risks (NEW):**
- Dependencies on future milestones
- Integration sequencing problems
- Feature flag implementation issues
- Independent deployment concerns

## 3. INTEGRATION READINESS ASSESSMENT
- **Independent Integration**: Can this milestone be safely integrated without future milestones?
- **Rollback Safety**: Are changes easily reversible if issues arise?
- **Feature Flag Compliance**: Are incomplete features properly gated?
- **Testing Coverage**: Are changes adequately testable in isolation?

## 4. ACTIONABLE RECOMMENDATIONS
Provide specific, immediate actions:
- Required changes before integration
- Risk mitigation strategies
- Additional testing recommendations
- Documentation or monitoring needs

**OUTPUT FORMAT:**
Provide a structured analysis covering all four sections above. Be specific and actionable. Focus on what's NEW or CHANGED, not general advice. Highlight critical issues that would block milestone integration.`;
}

export async function POST(request: NextRequest) {
    const logPrefix = '[API Ext Feedback]';
    console.log(`${logPrefix} Received request for extension feedback endpoint.`);

    // 1. Authentication
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        console.warn(`${logPrefix} Missing or malformed Authorization header.`);
        return NextResponse.json({ error: 'Unauthorized: Missing or malformed Authorization header' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);

    if (authError) {
        console.error(`${logPrefix} Supabase auth.getUser error:`, authError.message);
        const status = (authError as any).status || (authError.message.toLowerCase().includes('invalid jwt') ? 401 : 500);
        return NextResponse.json({ error: 'Authentication failed', details: authError.message }, { status });
    }

    if (!user) {
        console.warn(`${logPrefix} No user found for token.`);
        return NextResponse.json({ error: 'Unauthorized: No user found for token' }, { status: 401 });
    }

    const userId = user.id;
    console.log(`${logPrefix} Authenticated user: ${userId}`);

    // 2. Parse Request Payload
    let payload;
    try {
        payload = await request.json();
    } catch (error) {
        console.warn(`${logPrefix} Invalid JSON in request body.`);
        return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }

    const { 
        repository_slug, 
        code_changes, 
        branch_name, 
        design_doc_content, 
        job_id, 
        feature_prefix,
        feedback_mode = 'detailed' // Default to detailed mode, can be 'streamlined'
    } = payload;

    // 3. Basic Validation
    if (!repository_slug || !Array.isArray(code_changes) || code_changes.length === 0) {
        console.warn(`${logPrefix} Missing required parameters (repository_slug or code_changes).`);
        return NextResponse.json({ error: 'Missing or invalid required parameters: repository_slug (string) and code_changes (array) are mandatory.' }, { status: 400 });
    }

    console.log(`${logPrefix} Request details - Repo: ${repository_slug}, Branch: ${branch_name || 'N/A'}, Job ID: ${job_id || 'N/A'}, Feature Prefix: ${feature_prefix || 'N/A'}, Changes: ${code_changes.length} files, Feedback Mode: ${feedback_mode}.`);

    try {
        // 3. Determine Installation ID and Namespace
        console.log(`${logPrefix} Determining access for user ${userId} to repo ${repository_slug}...`);
        const accessDetails: RepositoryAccessDetails = await getRepositoryAccessDetails(userId, repository_slug);

        if (!accessDetails.namespace || accessDetails.type === 'forbidden' || accessDetails.type === 'not_found' || accessDetails.type === 'error') {
            const errorMessage = accessDetails.message || `Access denied or repository not found for ${repository_slug}. Type: ${accessDetails.type}`;
            console.warn(`${logPrefix} Access denied for user ${userId}, repo ${repository_slug}. Details: ${JSON.stringify(accessDetails)}`);
            const status = accessDetails.type === 'forbidden' ? 403 : accessDetails.type === 'not_found' ? 404 : 500;
            return NextResponse.json({ error: errorMessage }, { status: status });
        }
        console.log(`${logPrefix} Access type: ${accessDetails.type}. Using namespace: ${accessDetails.namespace}`);

        // 4. Enhanced Milestone Context Integration
        let milestoneContext: MilestoneContext | null = null;
        let enhancedDesignDocContent = design_doc_content || '';

        if (job_id && branch_name && feature_prefix) {
            console.log(`${logPrefix} Fetching milestone context for milestone-focused feedback`);
            milestoneContext = await getMilestoneContextFromJob(job_id, branch_name, feature_prefix);
            
            if (milestoneContext) {
                console.log(`${logPrefix} Using milestone-focused feedback (${feedback_mode} mode) for: ${milestoneContext.title}`);
                enhancedDesignDocContent = `
MILESTONE-FOCUSED FEEDBACK REQUEST

Implementation Plan Context:
- Milestone: ${milestoneContext.milestone_id} - ${milestoneContext.title}
- Description: ${milestoneContext.description}
- Planned Deliverables: ${JSON.stringify(milestoneContext.scope_validation.planned_deliverables)}
- Verification Criteria: ${JSON.stringify(milestoneContext.verification_criteria)}

Current Branch: ${branch_name}
Feature Prefix: ${feature_prefix}

${enhancedDesignDocContent}
                `.trim();
            } else {
                console.warn(`${logPrefix} Could not fetch milestone context, falling back to general feedback`);
            }
        }

        // 5. Construct Minimal PR Context
        const minimalPrContext = {
            number: 0,
            html_url: `local://${repository_slug}/${branch_name || 'changes'}`,
            title: milestoneContext 
                ? `Milestone ${milestoneContext.milestone_id}: ${milestoneContext.title} (${accessDetails.type})`
                : `Local Feedback for ${branch_name || 'changes'} (${accessDetails.type})`,
            body: milestoneContext
                ? `Milestone validation for: ${milestoneContext.description}`
                : 'Code changes submitted for early feedback from VS Code extension.',
            merged_at: null,
            ref: branch_name || 'refs/heads/local-changes'
        };

        // 6. Generate Feedback Based on Mode
        let result;
        
        if (feedback_mode === 'streamlined' && milestoneContext) {
            console.log(`${logPrefix} Using streamlined milestone feedback mode.`);
            
            const feedbackPrompt = generateMilestoneFeedbackPrompt(
                code_changes,
                milestoneContext,
                enhancedDesignDocContent || '',
                branch_name || 'local-changes',
                'streamlined'
            );

            const feedbackResponse = await anthropic.messages.create({
                model: getEnvVar('ANTHROPIC_MODEL', 'claude-sonnet-4-20250514') as any,
                max_tokens: 2048,
                temperature: 0.1,
                messages: [{ role: "user", content: feedbackPrompt }],
            });

            const feedbackText = feedbackResponse.content[0]?.type === 'text' 
                ? feedbackResponse.content[0].text 
                : 'Error: Could not generate streamlined feedback';

            // Try to parse as JSON for streamlined mode
            let parsedFeedback;
            try {
                parsedFeedback = JSON.parse(feedbackText);
            } catch (parseError) {
                parsedFeedback = {
                    scope_status: "UNKNOWN",
                    critical_security_risks: [],
                    critical_ux_risks: [],
                    scope_deviations: [],
                    integration_blocked: false,
                    summary: feedbackText.substring(0, 200) + "..."
                };
            }

            result = {
                status: 'feedback_generated',
                feedback: parsedFeedback,
                milestone_context: milestoneContext,
                feedback_mode: 'streamlined'
            } as OrchestratorResult;
        } else {
            // Use existing detailed feedback flow
            console.log(`${logPrefix} Calling orchestrator processMergedPR with enhanced milestone context. Namespace: ${accessDetails.namespace}`);
            result = await processMergedPR(
                minimalPrContext,
                code_changes,
                [], // No comments for local feedback
                accessDetails.namespace,
                repository_slug,
                true, // dryRun = true
                enhancedDesignDocContent || undefined,
                false // skipRelationshipAnalysis = false (we want to analyze relationships for milestone context)
            ) as OrchestratorResult;
        }

        console.log(`${logPrefix} Generated feedback with status: ${result?.status}`);

        // 7. Handle Response with Enhanced Metadata
        if (result?.status === 'feedback_generated' && result.feedback) {
            console.log(`${logPrefix} ${feedback_mode === 'streamlined' ? 'Streamlined' : 'Detailed'} feedback successfully generated.`);
            
            const responseData: any = { 
                success: true, 
                feedback: result.feedback,
                feedback_mode,
                milestone_context: milestoneContext ? {
                    milestone_id: milestoneContext.milestone_id,
                    title: milestoneContext.title,
                    branch_match: branch_name,
                    scope_validation_enabled: true
                } : null
            };

            if (result.referenced_decisions && result.referenced_decisions.length > 0) {
                console.log(`${logPrefix} Including ${result.referenced_decisions.length} referenced decisions in response.`);
                responseData.referenced_decisions = result.referenced_decisions;
            }

            return NextResponse.json(responseData);
        } else {
            console.error(`${logPrefix} Generator did not produce feedback or returned an unexpected status: ${result?.status}`);
            return NextResponse.json({ 
                success: false, 
                error: result?.error || 'Feedback generation failed.',
                milestone_context: milestoneContext ? {
                    milestone_id: milestoneContext.milestone_id,
                    title: milestoneContext.title,
                    error: 'Feedback generation failed but milestone context was found'
                } : null
            }, { status: 500 });
        }
    } catch (error: any) {
        console.error(`${logPrefix} Error in enhanced feedback endpoint:`, error);
        return NextResponse.json({ success: false, error: error.message || 'Internal Server Error' }, { status: 500 });
    }
}