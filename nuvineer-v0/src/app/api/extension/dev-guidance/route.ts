import { NextResponse } from 'next/server';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails } from '@/lib/repository-access';
import { Pinecone } from '@pinecone-database/pinecone';

// Initialize Pinecone client
const pinecone = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY || ''
});

const pineconeIndexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';
const pineconeIndexes: Record<string, any> = {};

async function getPineconeIndex(namespace: string) {
  if (!pineconeIndexes[namespace]) {
    pineconeIndexes[namespace] = pinecone.Index(pineconeIndexName).namespace(namespace);
  }
  return pineconeIndexes[namespace];
}

export interface DevGuidanceFromAPI {
  guidance: string; // Corresponds to dev_prompt
  relatedFiles: string[];
  decision_title?: string;
}

export async function POST(request: Request) {
  try {
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null;

    if (!apiKey) {
      return NextResponse.json({ error: 'Missing API Key in Authorization header' }, { status: 401 });
    }

    const body = await request.json();
    const { repository_slug } = body;

    if (!repository_slug) {
      return NextResponse.json({ error: 'Missing repository_slug in request body' }, { status: 400 });
    }

    const userId = await validateApiKeyAndGetUser(apiKey);
    if (!userId) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 });
    }

    console.log(`[Dev Guidance] Processing request for repository: ${repository_slug} from user: ${userId}`);

    const accessDetails = await getRepositoryAccessDetails(userId, repository_slug);
    if (accessDetails.type !== 'public' && accessDetails.type !== 'private') {
      console.warn(`[Dev Guidance] Access denied: ${accessDetails.type} for repo ${repository_slug}`);
      return NextResponse.json({
        error: `Repository access denied: ${accessDetails.message || accessDetails.type}`
      }, { status: 403 });
    }

    const pineconeNamespace = accessDetails.namespace!;
    console.log(`[Dev Guidance] Using Pinecone namespace: ${pineconeNamespace}`);

    const index = await getPineconeIndex(pineconeNamespace);
    const dummyVector = new Array(1536).fill(0); // Adjust dimension if your embeddings are different

    const queryResponse = await index.query({
      vector: dummyVector,
      topK: 10000, // Fetch a large number to process all decisions
      includeMetadata: true,
      filter: { 'is_superseded': false }
    });

    console.log(`[Dev Guidance] Found ${queryResponse.matches.length} active decisions in namespace ${pineconeNamespace} for dev guidance extraction`);

    const devGuidanceItems: DevGuidanceFromAPI[] = [];
    queryResponse.matches.forEach((match: {
      id: string;
      score: number;
      metadata?: {
        dev_prompt?: string;
        related_files?: string[];
        title?: string;
        [key: string]: any;
      }
    }) => {
      if (match.metadata && match.metadata.dev_prompt && typeof match.metadata.dev_prompt === 'string' && match.metadata.dev_prompt.trim() !== '') {
        devGuidanceItems.push({
          guidance: match.metadata.dev_prompt,
          relatedFiles: match.metadata.related_files || [],
          decision_title: match.metadata.title
        });
      }
    });

    console.log(`[Dev Guidance] Extracted ${devGuidanceItems.length} dev guidance items`);

    return NextResponse.json({
      success: true,
      dev_guidance: devGuidanceItems, // Ensure this key matches what ApiService expects
      repository_slug,
      installation_id: accessDetails.installationId || 0
    });

  } catch (error: any) {
    console.error('[Dev Guidance] Error fetching dev guidance:', error);
    return NextResponse.json({
      error: 'Error fetching dev guidance',
      details: error.message
    }, { status: 500 });
  }
} 