import { NextRequest, NextResponse } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails, RepositoryAccessDetails } from '@/lib/repository-access';

// --- ADDED LOCAL HELPERS (Based on /api/extension/relevant-to-file/route.ts pattern) ---

// Helper to get environment variables
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            console.warn(`[API Ext ByConcepts] Environment variable ${name} not found, using default value.`);
            return defaultValue;
        } else {
            throw new Error(`Missing required environment variable: ${name}`);
        }
    }
    return value;
}

let pinecone: Pinecone | null = null;
// Store initialized indexes per namespace to avoid re-initializing constantly
const pineconeIndexes: Record<string, any> = {};

// Initialize Pinecone client (only once)
async function initializePineconeClient() {
    if (!pinecone) {
        try {
            const apiKey = getEnvVar('PINECONE_API_KEY');
            pinecone = new Pinecone({ apiKey });
            console.log(`[API Ext ByConcepts] Pinecone client initialized.`);
        } catch (error: any) {
            console.error("[API Ext ByConcepts] Failed to initialize Pinecone client:", error);
            pinecone = null;
        }
    }
}

// Get or initialize index for a specific namespace
async function getPineconeIndex(namespace: string) {
    await initializePineconeClient(); // Ensure client is ready
    if (!pinecone) {
        throw new Error("Pinecone client is not initialized.");
    }
    const indexCacheKey = namespace;
    if (!pineconeIndexes[indexCacheKey]) {
        const indexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');
        pineconeIndexes[indexCacheKey] = pinecone.Index(indexName).namespace(namespace);
        console.log(`[API Ext ByConcepts] Pinecone index handle initialized for namespace: ${namespace}`);
    }
    return pineconeIndexes[indexCacheKey];
}
// --- END ADDED LOCAL HELPERS ---

// ---> Supabase Client Initialization for filtering superseded decisions
let supabase: SupabaseClient | null = null;
function initializeSupabaseClient() {
    if (!supabase) {
        try {
            const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
            const supabaseServiceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY');
            supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
                auth: { persistSession: false }
            });
            console.log("[API Ext ByConcepts] Supabase client initialized for filtering.");
        } catch (error: any) {
            console.error("[API Ext ByConcepts] Failed to initialize Supabase client:", error);
            supabase = null;
        }
    }
}
// --- END Supabase Init ---

type ConceptPineconeMatch = { id: string; score?: number; metadata?: Record<string, any>; [key: string]: any; }; // Assuming a similar structure to Pinecone matches

export async function POST(request: NextRequest) {
    initializeSupabaseClient();

    // 1. Authenticate API Key
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null;

    if (!apiKey) {
        console.warn('[API Ext ByConcepts] Missing API Key');
        return NextResponse.json({ error: 'Missing API Key in Authorization header (ApiKey YOUR_KEY)' }, { status: 401 });
    }

    let userId: string | null;
    try {
        userId = await validateApiKeyAndGetUser(apiKey);
        if (!userId) {
            console.warn('[API Ext ByConcepts] Invalid API Key presented.');
            return NextResponse.json({ error: 'Invalid API Key' }, { status: 401 });
        }
        console.log(`[API Ext ByConcepts] API Key validated. User ID: ${userId}`);
    } catch (error: any) {
        console.error('[API Ext ByConcepts] Error validating API key:', error);
        return NextResponse.json({ error: 'Internal server error during authentication' }, { status: 500 });
    }

    // 2. Parse Request Body
    let body: { repository_slug: string; domain_concepts: string[]; limit?: number };
    try {
        body = await request.json();
    } catch (error) {
        console.warn('[API Ext ByConcepts] Invalid JSON body');
        return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const { repository_slug, domain_concepts, limit = 50 } = body; // Default limit 
    if (!repository_slug || !Array.isArray(domain_concepts) || domain_concepts.length === 0) {
        console.warn('[API Ext ByConcepts] Missing or invalid parameters');
        return NextResponse.json({ error: 'Missing required body parameters: repository_slug (string), domain_concepts (non-empty array)' }, { status: 400 });
    }
    console.log(`[API Ext ByConcepts] Request for repo: ${repository_slug}, concepts: ${domain_concepts.join(', ')}, User: ${userId}`);

    // 3. Determine Repository Access & Pinecone Namespace
    let accessDetails: RepositoryAccessDetails;
    try {
        accessDetails = await getRepositoryAccessDetails(userId, repository_slug);
    } catch (error: any) {
        console.error(`[API Ext ByConcepts] Error determining repository access for ${repository_slug}, User ${userId}:`, error);
        return NextResponse.json({ error: 'Failed to determine repository access' }, { status: 500 });
    }

    // Handle different access outcomes
    switch (accessDetails.type) {
        case 'forbidden':
            console.warn(`[API Ext ByConcepts] Forbidden access for User ${userId} to repo ${repository_slug}`);
            return NextResponse.json({ error: 'Access Denied: User does not have permission for this repository via the GitHub App.' }, { status: 403 });
        case 'not_found':
            console.warn(`[API Ext ByConcepts] Repository not found: ${repository_slug}`);
            return NextResponse.json({ error: 'Repository not found or inaccessible.' }, { status: 404 });
         case 'error':
             console.error(`[API Ext ByConcepts] Access check failed for ${repository_slug}: ${accessDetails.message}`);
             return NextResponse.json({ error: `Access check failed: ${accessDetails.message}` }, { status: 500 });
        case 'public':
        case 'private':
            console.log(`[API Ext ByConcepts] Access granted (${accessDetails.type}). Namespace: ${accessDetails.namespace}`);
            break; // Continue below
        default:
            console.error(`[API Ext ByConcepts] Unexpected access details type:`, accessDetails);
            return NextResponse.json({ error: 'Internal server error during access check' }, { status: 500 });
    }

    // 4. Query Pinecone by Domain Concepts
    try {
        const pineconeNamespace = accessDetails.namespace!;
        const index = await getPineconeIndex(pineconeNamespace);

        // Pinecone filter to match documents where the 'domain_concepts' array
        // contains AT LEAST ONE of the concepts provided in the request.
        const filterCriteria = {
            domain_concepts: { $in: domain_concepts }
        };
        console.debug(`[API Ext ByConcepts] Pinecone filter:`, JSON.stringify(filterCriteria));
        const dimension = 1536; // Assuming OpenAI embedding size
        const dummyVector = new Array(dimension).fill(0);
        
        const queryResponse = await index.query({
            vector: dummyVector,
            topK: limit, // Use limit from request or default
            filter: filterCriteria,
            includeMetadata: true,
            includeValues: false
        });

        let matches = queryResponse.matches || [];
        console.log(`[API Ext ByConcepts] Found ${matches.length} potential decisions matching concepts '${domain_concepts.join(', ')}' in namespace '${pineconeNamespace}'.`);

        // 5. Filter out superseded decisions using Supabase (if available)
        let supersededDecisionIds = new Set<string>();
        if (supabase && matches.length > 0) {
            console.log(`[API Ext ByConcepts] Fetching 'supersedes' relationships from Supabase for repo: ${repository_slug}`);
            try {
                const { data: relationshipRecords, error: relationshipsError } = await supabase
                    .from('decision_relationships')
                    .select('target_decision_pinecone_id')
                    .eq('repository_slug', repository_slug)
                    .eq('relationship_type', 'supersedes');

                if (relationshipsError) {
                    console.error('[API Ext ByConcepts] Supabase relationships fetch error:', relationshipsError);
                } else if (relationshipRecords) {
                    relationshipRecords.forEach(rel => {
                        if (rel.target_decision_pinecone_id) {
                            supersededDecisionIds.add(rel.target_decision_pinecone_id);
                        }
                    });
                    console.log(`[API Ext ByConcepts] Identified ${supersededDecisionIds.size} superseded decision IDs.`);
                }
            } catch (supaError: any) {
                 console.error('[API Ext ByConcepts] Unexpected error fetching relationships from Supabase:', supaError);
            }

            const originalMatchCount = matches.length;
            matches = matches.filter((match: ConceptPineconeMatch) => !supersededDecisionIds.has(match.id));
            const filteredMatchCount = matches.length;
            if (originalMatchCount !== filteredMatchCount) {
                console.log(`[API Ext ByConcepts] Filtered out ${originalMatchCount - filteredMatchCount} superseded decisions. Returning ${filteredMatchCount}.`);
            }
        } else if (!supabase) {
             console.warn('[API Ext ByConcepts] Skipping superseded decision filtering because Supabase client is not available.');
        }

        // 6. Format and Sort Results (sorting by merge date like other endpoints)
        const relevantDecisionsUnsorted = matches.map((match: any) => ({
            id: match.id,
            score: match.score, // Include score if needed
            metadata: match.metadata || {}, // Assign the entire metadata object
        }));

        const relevantDecisions = relevantDecisionsUnsorted.sort((a: ConceptPineconeMatch, b: ConceptPineconeMatch) => {
            const timeA = (typeof a.metadata?.pr_merged_at === 'number' && a.metadata.pr_merged_at > 0) ? a.metadata.pr_merged_at : 0;
            const timeB = (typeof b.metadata?.pr_merged_at === 'number' && b.metadata.pr_merged_at > 0) ? b.metadata.pr_merged_at : 0;
            return timeB - timeA; // Descending order (newest first)
        });

        console.log(`[API Ext ByConcepts] Returning ${relevantDecisions.length} decisions.`);
        return NextResponse.json({ success: true, decisions: relevantDecisions });

    } catch (error: any) {
        console.error(`[API Ext ByConcepts] Error processing request for repo ${repository_slug}, concepts ${domain_concepts.join(', ')}:`, error);
        const errorMessage = error.message?.includes('Namespace not found') || error.message?.includes('does not exist')
            ? 'No decision data found for this repository.'
            : 'Failed to fetch decisions by concepts.';
        const statusCode = error.message?.includes('Namespace not found') || error.message?.includes('does not exist') ? 404 : 500;
        return NextResponse.json({ error: errorMessage, details: error.message }, { status: statusCode });
    }
} 