import { NextResponse } from 'next/server';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails } from '@/lib/repository-access';
import { Pinecone } from '@pinecone-database/pinecone';

// Initialize Pinecone client
const pinecone = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY || ''
});

const pineconeIndexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';
const pineconeIndexes: Record<string, any> = {};

async function getPineconeIndex(namespace: string) {
  if (!pineconeIndexes[namespace]) {
    pineconeIndexes[namespace] = pinecone.Index(pineconeIndexName).namespace(namespace);
  }
  return pineconeIndexes[namespace];
}

export interface BestPracticeFromAPI {
  reason: string;
  relatedFiles: string[];
  decision_title?: string;
}

export async function POST(request: Request) {
  try {
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null;
    
    if (!apiKey) {
      return NextResponse.json({ error: 'Missing API Key in Authorization header' }, { status: 401 });
    }
    
    const body = await request.json();
    const { repository_slug } = body;
    
    if (!repository_slug) {
      return NextResponse.json({ error: 'Missing repository_slug in request body' }, { status: 400 });
    }
    
    const userId = await validateApiKeyAndGetUser(apiKey);
    if (!userId) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 });
    }
    
    console.log(`[Best Practices] Processing request for repository: ${repository_slug} from user: ${userId}`);
    
    const accessDetails = await getRepositoryAccessDetails(userId, repository_slug);
    if (accessDetails.type !== 'public' && accessDetails.type !== 'private') {
      console.warn(`[Best Practices] Access denied: ${accessDetails.type} for repo ${repository_slug}`);
      return NextResponse.json({ 
        error: `Repository access denied: ${accessDetails.message || accessDetails.type}` 
      }, { status: 403 });
    }
    
    const pineconeNamespace = accessDetails.namespace!;
    console.log(`[Best Practices] Using Pinecone namespace: ${pineconeNamespace}`);
    
    const index = await getPineconeIndex(pineconeNamespace);
    const dummyVector = new Array(1536).fill(0); // Adjust dimension if your embeddings are different
    
    const queryResponse = await index.query({
      vector: dummyVector,
      topK: 10000, // Fetch a large number to process all decisions
      includeMetadata: true,
      filter: { 'is_superseded': false }
    });
    
    console.log(`[Best Practices] Found ${queryResponse.matches.length} active decisions in namespace ${pineconeNamespace} for best practice extraction`);
    
    const bestPractices: BestPracticeFromAPI[] = [];
    queryResponse.matches.forEach((match: { 
      id: string; 
      score: number; 
      metadata?: { 
        follows_standard_practice?: boolean;
        follows_standard_practice_reason?: string;
        title?: string;
        related_files?: string[];
        [key: string]: any;
      }
    }) => {
      if (match.metadata && match.metadata.follows_standard_practice_reason && typeof match.metadata.follows_standard_practice_reason === 'string' && match.metadata.follows_standard_practice_reason.trim() !== '') {
        bestPractices.push({
          reason: match.metadata.follows_standard_practice_reason,
          relatedFiles: match.metadata.related_files || [],
          decision_title: match.metadata.title
        });
      }
    });
    
    console.log(`[Best Practices] Extracted ${bestPractices.length} best practice reasons`);
    
    return NextResponse.json({ 
      success: true, 
      best_practices: bestPractices, // Ensure this key matches what ApiService expects
      repository_slug,
      installation_id: accessDetails.installationId || 0 
    });
    
  } catch (error: any) {
    console.error('[Best Practices] Error fetching best practices:', error);
    return NextResponse.json({ 
      error: 'Error fetching best practices', 
      details: error.message 
    }, { status: 500 });
  }
} 