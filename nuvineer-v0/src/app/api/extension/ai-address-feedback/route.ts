import { NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService'; // Assuming this is a shared service
import matter from 'gray-matter'; // Add gray-matter for YAML frontmatter parsing

// --- Constants for Comment Structure (consistent with ai-review-doc) ---
const ARCHKNOW_COMMENT_START_TAG = "<!-- ARCHKNOW_COMMENT_START";
const ARCHKNOW_COMMENT_END_TAG = "ARCHKNOW_COMMENT_END -->";
const ARCHKNOW_COMMENT_SEPARATOR = "\n"; // How fields are separated within a comment block

// --- Interfaces ---
interface AiAddressFeedbackRequest {
  documentContentWithComments: string; // Full markdown content with embedded comments
  repository_slug: string;
  api_key: string;
}

// This is the structure of a comment as stored in the Markdown (HTML comments)
interface EmbeddedComment {
  id: string;
  type: string; // 'blocking', 'non-blocking', 'nit'
  status: string; // 'open', 'addressed', 'wont-fix', 'pending-ai-addressal'
  author: string;
  timestamp: string;
  text: string;
  suggestedSection?: string;
  resolutionAuthor?: string;
  resolutionTimestamp?: string;
  resolutionText?: string;
  rawText: string; // The full raw text of the comment block
}

// This is the structure of a comment as stored in YAML frontmatter
interface FrontmatterComment {
  id: string;
  user: string;
  comment: string;
  timestamp: string;
  type: string;
  status: string;
  category?: string;
  selection?: {
    selectedText: string;
    startOffset: number;
    endOffset: number;
  };
}

interface AiAddressFeedbackResponse {
  updatedDocumentContent: string; // Markdown content with AI-addressed feedback and updated comments
  // We might also want to return a summary of changes or which comments were updated.
  // For now, keeping it simple.
}

// --- Environment Variable Helper ---
function getEnvVar(name: string, defaultValue: string | null = null): string {
  const value = process.env[name];
  if (value === undefined) {
    if (defaultValue === null) {
      throw new Error(`Missing environment variable: ${name}`);
    }
    console.warn(`Missing environment variable ${name}, using default value.`);
    return defaultValue;
  }
  return value;
}

// --- Initialize SDK Clients ---
const anthropic = new Anthropic({
  apiKey: getEnvVar('ANTHROPIC_API_KEY'),
});

// --- Enhanced Comment Parsing Logic ---
function parseCommentsFromMarkdown(markdownContent: string): { 
  embeddedComments: EmbeddedComment[], 
  frontmatterComments: FrontmatterComment[], 
  parsedContent: string,
  metadata: any 
} {
  const logPrefix = '[API ai-address-feedback]';
  console.log(`${logPrefix} Starting comment parsing. Content length: ${markdownContent.length}`);
  console.log(`${logPrefix} First 200 chars of content: ${markdownContent.substring(0, 200)}`);
  
  const embeddedComments: EmbeddedComment[] = [];
  const frontmatterComments: FrontmatterComment[] = [];
  
  // First, try to parse YAML frontmatter
  let parsedContent = markdownContent;
  let metadata: any = {};
  
  try {
    const parsed = matter(markdownContent);
    metadata = parsed.data;
    parsedContent = parsed.content;
    
    console.log(`${logPrefix} Successfully parsed YAML frontmatter. Keys: ${Object.keys(metadata).join(', ')}`);
    console.log(`${logPrefix} Metadata:`, JSON.stringify(metadata, null, 2));
    
    // Extract comments from frontmatter
    if (metadata.feedback && Array.isArray(metadata.feedback)) {
      console.log(`${logPrefix} Found ${metadata.feedback.length} comments in YAML frontmatter`);
      metadata.feedback.forEach((comment: any, index: number) => {
        console.log(`${logPrefix} Processing frontmatter comment ${index + 1}:`, JSON.stringify(comment, null, 2));
        
        // Explicitly skip system_action comments, as they are for history, not for the AI to address.
        if (comment.type === 'system_action') {
          console.log(`${logPrefix} Skipping system_action comment at index ${index}.`);
          return; // Using return in forEach is like 'continue'
        }

        // Validate required fields for user comments
        if (comment.user && comment.comment && comment.timestamp && comment.status) {
          const id = comment.id || `temp-id-${Date.now()}-${index}`;
          frontmatterComments.push({
            id: id,
            user: comment.user,
            comment: comment.comment,
            timestamp: comment.timestamp,
            type: comment.type || 'comment',
            status: comment.status,
            category: comment.category,
            selection: comment.selection
          });
          console.log(`${logPrefix} Successfully processed frontmatter comment: ${id}`);
        } else {
          console.warn(`${logPrefix} Skipping malformed frontmatter comment at index ${index}:`, comment);
        }
      });
    } else {
      console.log(`${logPrefix} No 'feedback' array found in frontmatter`);
    }
  } catch (error) {
    console.warn(`${logPrefix} Failed to parse YAML frontmatter, treating as plain markdown:`, error);
    parsedContent = markdownContent;
  }
  
  // Then, look for embedded HTML comments
  const commentRegex = new RegExp(
    `${ARCHKNOW_COMMENT_START_TAG}([\\s\\S]*?)${ARCHKNOW_COMMENT_END_TAG}`,
    'g'
  );
  let match;
  while ((match = commentRegex.exec(markdownContent)) !== null) {
    const rawCommentBlock = match[0];
    const commentContent = match[1].trim();
    const lines = commentContent.split(ARCHKNOW_COMMENT_SEPARATOR);
    const comment: Partial<EmbeddedComment> = { rawText: rawCommentBlock };

    console.log(`${logPrefix} Found embedded HTML comment. Raw block: ${rawCommentBlock}`);

    lines.forEach(line => {
      const [key, ...valueParts] = line.split(': ');
      const value = valueParts.join(': ').trim();
      if (!key || !value) return;

      switch (key.trim().toUpperCase()) {
        case 'ID': comment.id = value; break;
        case 'TYPE': comment.type = value; break;
        case 'STATUS': comment.status = value; break;
        case 'AUTHOR': comment.author = value; break;
        case 'TIMESTAMP': comment.timestamp = value; break;
        case 'TEXT': comment.text = (comment.text ? comment.text + '\\n' : '') + value; break; // Handle multi-line text
        case 'SUGGESTED_SECTION': comment.suggestedSection = (comment.suggestedSection ? comment.suggestedSection + '\\n' : '') + value; break;
        case 'RESOLUTION_AUTHOR': comment.resolutionAuthor = value; break;
        case 'RESOLUTION_TIMESTAMP': comment.resolutionTimestamp = value; break;
        case 'RESOLUTION_TEXT': comment.resolutionText = (comment.resolutionText ? comment.resolutionText + '\\n' : '') + value; break;
      }
    });
    
    // Basic validation
    if (comment.id && comment.type && comment.status && comment.author && comment.text) {
      embeddedComments.push(comment as EmbeddedComment);
      console.log(`${logPrefix} Successfully processed embedded comment: ${comment.id}`);
    } else {
      console.warn(`${logPrefix} Skipping malformed embedded comment block:`, rawCommentBlock, "Parsed attempt:", comment);
    }
  }
  
  console.log(`${logPrefix} Comment parsing complete. Found ${embeddedComments.length} embedded comments and ${frontmatterComments.length} frontmatter comments`);
  
  return { embeddedComments, frontmatterComments, parsedContent, metadata };
}

// --- Prompt Generation ---
function generateAiAddressFeedbackPrompt(
  documentContent: string, 
  commentsToAddress: (EmbeddedComment | FrontmatterComment)[],
  documentFormat: 'embedded' | 'frontmatter',
  metadata?: any
): string {
  const logPrefix = '[API ai-address-feedback]';
  console.log(`${logPrefix} Generating prompt for ${commentsToAddress.length} comments in ${documentFormat} format`);
  
  const escapedDocumentContent = documentContent.replace(/`/g, '\\`');
  
  const commentsText = commentsToAddress.map((c, index) => {
    console.log(`${logPrefix} Processing comment ${index + 1} for prompt:`, JSON.stringify(c, null, 2));
    
    if ('text' in c) {
      // EmbeddedComment
      let commentStr = `Comment ID: ${c.id}\nType: ${c.type}\nStatus: ${c.status}\nAuthor: ${c.author}\nTimestamp: ${c.timestamp}\nText: ${c.text}`;
      if(c.suggestedSection) commentStr += `\nSuggested Section: ${c.suggestedSection}`;
      return commentStr;
    } else {
      // FrontmatterComment
      let commentStr = `Comment ID: ${c.id}\nType: ${c.type}\nStatus: ${c.status}\nAuthor: ${c.user}\nTimestamp: ${c.timestamp}\nText: ${c.comment}`;
      if(c.category) commentStr += `\nCategory: ${c.category}`;
      if(c.selection?.selectedText) commentStr += `\nSelected Text: "${c.selection.selectedText}"`;
      return commentStr;
    }
  }).join('\n---\n');

  console.log(`${logPrefix} Generated comments text for prompt (length: ${commentsText.length})`);

  if (documentFormat === 'frontmatter') {
    return `
You are an expert software architect tasked with revising a design document based on specific feedback.

The design document is provided below in Markdown format with YAML frontmatter. The frontmatter contains metadata including a 'feedback' array with comments that need to be addressed.

You need to address the following comments:

--- START COMMENTS TO ADDRESS ---
${commentsText}
--- END COMMENTS TO ADDRESS ---

Your tasks are:
1. Carefully review each comment listed above.
2. Modify the design document content to address these comments directly and thoroughly.
3. For each comment you address, you MUST update its status in the YAML frontmatter from its current status to 'addressed'.
4. Add any necessary explanations or improvements to the document content itself to address the feedback.
5. Return the *entire, updated Markdown document content*, including the updated YAML frontmatter and the revised document content.

IMPORTANT:
- Preserve the existing Markdown structure and YAML frontmatter format.
- Only change the 'status' field of comments you address to 'addressed'.
- Ensure the output is ONLY the complete Markdown document with frontmatter.
- Do not add any extra explanations outside of the document itself.

Design Document:
\`\`\`
${escapedDocumentContent}
\`\`\`
`;
  } else {
    return `
You are an expert software architect tasked with revising a design document based on specific feedback.

The full design document is provided below, delimited by triple backticks. Some sections might already contain comments in the format: 
${ARCHKNOW_COMMENT_START_TAG}
ID: ...
TYPE: ...
...
${ARCHKNOW_COMMENT_END_TAG}

You need to address the following comments:

--- START COMMENTS TO ADDRESS ---
${commentsText}
--- END COMMENTS TO ADDRESS ---

Your tasks are:
1.  Carefully review each comment listed above.
2.  Modify the design document content to address these comments directly and thoroughly.
3.  For each comment you address, you MUST update its corresponding comment block in the Markdown. Specifically:
    *   Change its STATUS to 'addressed'.
    *   Add a RESOLUTION_AUTHOR field with the value 'ai-assistant'.
    *   Add a RESOLUTION_TIMESTAMP field with the current ISO 8601 timestamp.
    *   Add a RESOLUTION_TEXT field explaining briefly how you addressed the comment in the document.
    *   Ensure all other fields (ID, TYPE, AUTHOR, TIMESTAMP, TEXT, SUGGESTED_SECTION) of the original comment remain unchanged.
    *   The entire comment block must remain valid and parseable.
4.  If you believe a comment cannot or should not be addressed, you can leave its status as is, or optionally change it to 'wont-fix' and add a RESOLUTION_TEXT explaining why.
5.  Return the *entire, updated Markdown document content*, including your textual changes and the updated comment blocks.

IMPORTANT:
*   Preserve the existing Markdown structure and formatting as much as possible.
*   Ensure the output is ONLY the complete Markdown document. Do not add any extra explanations or summaries outside of the document itself and the RESOLUTION_TEXT fields within the comments.
*   If the original document included other comments not in the "COMMENTS TO ADDRESS" list, preserve them as they are unless they are directly related to the changes you are making for an addressed comment.

Design Document:
\`\`\`
${escapedDocumentContent}
\`\`\`
`;
  }
}

// --- Helper function to extract and preserve Referenced Architectural Decisions section ---
function extractReferencedDecisionsSection(content: string): { 
  contentWithoutReferences: string, 
  referencedSection: string 
} {
  const logPrefix = '[API ai-address-feedback]';
  
  // Look for the "Referenced Architectural Decisions" section
  const referencedDecisionsRegex = /^##\s+Referenced\s+Architectural\s+Decisions\s*$/mi;
  const match = content.match(referencedDecisionsRegex);
  
  if (!match) {
    console.log(`${logPrefix} No "Referenced Architectural Decisions" section found`);
    return { contentWithoutReferences: content, referencedSection: '' };
  }
  
  const sectionStartIndex = match.index!;
  
  // Find the next ## section or end of content
  const remainingContent = content.substring(sectionStartIndex);
  const nextSectionMatch = remainingContent.substring(1).match(/^##\s+/m);
  
  let sectionEndIndex: number;
  if (nextSectionMatch) {
    sectionEndIndex = sectionStartIndex + 1 + nextSectionMatch.index!;
  } else {
    sectionEndIndex = content.length;
  }
  
  const contentWithoutReferences = content.substring(0, sectionStartIndex).trim();
  const referencedSection = content.substring(sectionStartIndex, sectionEndIndex).trim();
  
  console.log(`${logPrefix} Extracted Referenced Decisions section (${referencedSection.length} chars)`);
  console.log(`${logPrefix} Content without references (${contentWithoutReferences.length} chars)`);
  
  return { contentWithoutReferences, referencedSection };
}

// --- Helper function to add Referenced Architectural Decisions section back ---
function addReferencedDecisionsSectionBack(content: string, referencedSection: string): string {
  if (!referencedSection) {
    return content;
  }
  
  // Add the section back at the end with proper spacing
  const trimmedContent = content.trim();
  return `${trimmedContent}\n\n${referencedSection}`;
}

// --- LLM Interaction ---
async function callClaudeLlmForAddressingFeedback(prompt: string, model?: string): Promise<string> {
  const effectiveModel = model || getEnvVar('ANTHROPIC_MODEL', 'claude-sonnet-4-20250514');
  const callId = `llm-address-feedback-call-${Date.now()}`;
  const logPrefix = '[API ai-address-feedback]';
  
  console.log(`${logPrefix} [${callId}] Sending prompt to Claude model: ${effectiveModel}`);
  console.log(`${logPrefix} [${callId}] Prompt length: ${prompt.length}`);
  console.log(`${logPrefix} [${callId}] Full prompt:\n---PROMPT START---\n${prompt}\n---PROMPT END---`);

  try {
    const msg = await anthropic.messages.create({
      model: effectiveModel,
      max_tokens: 4096, // Maximize tokens as we expect the full doc back
      temperature: 0.1, // Low temperature for factual, direct changes
      messages: [
        { role: "user", content: prompt }
      ],
    });

    const responseText = msg.content[0].type === 'text' ? msg.content[0].text : '';
    console.log(`${logPrefix} [${callId}] Received response from Claude (length: ${responseText.length})`);
    console.log(`${logPrefix} [${callId}] Full response:\n---RESPONSE START---\n${responseText}\n---RESPONSE END---`);
    
    // We expect the raw markdown directly. We might need to strip ```markdown ``` if the model adds it.
    let updatedDoc = responseText;
    if (updatedDoc.startsWith("```markdown\n")) {
        updatedDoc = updatedDoc.substring("```markdown\n".length);
        if (updatedDoc.endsWith("\n```")) {
            updatedDoc = updatedDoc.substring(0, updatedDoc.length - "\n```".length);
        }
    } else if (updatedDoc.startsWith("```")) {
        updatedDoc = updatedDoc.substring(3);
        if (updatedDoc.endsWith("\n```")) {
             updatedDoc = updatedDoc.substring(0, updatedDoc.length - "\n```".length);
        }
    }

    console.log(`${logPrefix} [${callId}] Processed response (length: ${updatedDoc.trim().length})`);
    return updatedDoc.trim();

  } catch (error: any) {
    console.error(`${logPrefix} [${callId}] Error calling Anthropic API for addressing feedback:`, error);
    console.error(`${logPrefix} [${callId}] Error details:`, error.response?.data);
    throw new Error(`Anthropic API call for addressing feedback failed: ${error.message}`);
  }
}

// --- API Route Handler ---
export async function POST(request: Request) {
  const logPrefix = '[API ai-address-feedback]';
  console.log(`${logPrefix} Received request`);
  
  try {
    const body = await request.json() as AiAddressFeedbackRequest;
    console.log(`${logPrefix} Request body keys: ${Object.keys(body).join(', ')}`);
    console.log(`${logPrefix} Full request body:`, JSON.stringify(body, null, 2));
    
    const { documentContentWithComments, repository_slug, api_key } = body;

    if (!documentContentWithComments || !repository_slug || !api_key) {
      console.error(`${logPrefix} Missing required fields`);
      return NextResponse.json({ error: 'Missing required fields: documentContentWithComments, repository_slug, api_key' }, { status: 400 });
    }

    console.log(`${logPrefix} Received API Key (validation skipped) for repo: ${repository_slug}`);
    console.log(`${logPrefix} Document content length: ${documentContentWithComments.length}`);

    // 1. Extract the Referenced Architectural Decisions section before processing
    const { contentWithoutReferences, referencedSection } = extractReferencedDecisionsSection(documentContentWithComments);
    console.log(`${logPrefix} Processing document without Referenced Decisions section`);

    // 2. Parse all comments from the document (without the referenced section)
    const { embeddedComments, frontmatterComments, parsedContent, metadata } = parseCommentsFromMarkdown(contentWithoutReferences);
    
    console.log(`${logPrefix} Parsing results: ${embeddedComments.length} embedded comments, ${frontmatterComments.length} frontmatter comments`);
    
    const allComments = [...embeddedComments, ...frontmatterComments];
    if (allComments.length === 0) {
        console.log(`${logPrefix} No comments found in the document.`);
        // Still add back the referenced section even if no comments to address
        const finalContent = addReferencedDecisionsSectionBack(contentWithoutReferences, referencedSection);
        return NextResponse.json({ updatedDocumentContent: finalContent }, { status: 200 });
    }

    // 3. Filter comments to be addressed
    const commentsToAddress = allComments.filter(c => c.status === 'open' || c.status === 'pending-ai-addressal');
    console.log(`${logPrefix} Found ${commentsToAddress.length} open/pending comments`);
    
    if (commentsToAddress.length === 0) {
      console.log(`${logPrefix} No open comments to address.`);
      // Still add back the referenced section even if no comments to address
      const finalContent = addReferencedDecisionsSectionBack(contentWithoutReferences, referencedSection);
      return NextResponse.json({ updatedDocumentContent: finalContent }, { status: 200 });
    }
    
    console.log(`${logPrefix} Attempting to address ${commentsToAddress.length} comments:`);
    commentsToAddress.forEach((comment, index) => {
      console.log(`${logPrefix} Comment ${index + 1}: ID=${comment.id}, Status=${comment.status}, Type=${'type' in comment ? comment.type : 'frontmatter'}`);
    });

    // 4. Determine document format and generate prompt (using content without references)
    const documentFormat = frontmatterComments.length > 0 ? 'frontmatter' : 'embedded';
    console.log(`${logPrefix} Using document format: ${documentFormat}`);
    
    const addressFeedbackPrompt = generateAiAddressFeedbackPrompt(
      contentWithoutReferences, // Use content without the referenced section
      commentsToAddress, 
      documentFormat,
      metadata
    );

    // 5. Call LLM to get the updated document
    console.log(`${logPrefix} Calling Claude LLM to address feedback`);
    const updatedDocumentContentFromLlm = await callClaudeLlmForAddressingFeedback(addressFeedbackPrompt);

    if (!updatedDocumentContentFromLlm || updatedDocumentContentFromLlm.trim() === '') {
        console.error(`${logPrefix} LLM returned empty content.`);
        return NextResponse.json({ error: 'AI failed to update the document. Received empty content.' }, { status: 500 });
    }

    console.log(`${logPrefix} Successfully generated updated document from LLM (length: ${updatedDocumentContentFromLlm.length})`);

    // 6. Add back the Referenced Architectural Decisions section
    const finalUpdatedContent = addReferencedDecisionsSectionBack(updatedDocumentContentFromLlm, referencedSection);
    console.log(`${logPrefix} Final document with Referenced Decisions section added back (length: ${finalUpdatedContent.length})`);

    // 7. Return response
    return NextResponse.json({
      updatedDocumentContent: finalUpdatedContent,
    } as AiAddressFeedbackResponse, { status: 200 });

  } catch (error: any) {
    console.error(`${logPrefix} Error processing request:`, error);
    console.error(`${logPrefix} Error stack:`, error.stack);
    if (error.message.includes("Anthropic API call")) {
        return NextResponse.json({ error: `Error during AI feedback addressing: ${error.message}` }, { status: 500 });
    }
    return NextResponse.json({ error: `Internal server error: ${error.message || 'Unknown error'}` }, { status: 500 });
  }
} 