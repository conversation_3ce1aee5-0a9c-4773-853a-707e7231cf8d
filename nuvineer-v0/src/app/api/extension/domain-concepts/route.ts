import { NextResponse } from 'next/server';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails } from '@/lib/repository-access';
import { Pinecone } from '@pinecone-database/pinecone';

// Initialize Pinecone client
const pinecone = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY || ''
});

const pineconeIndexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';
const pineconeIndexes: Record<string, any> = {};

async function getPineconeIndex(namespace: string) {
  if (!pineconeIndexes[namespace]) {
    pineconeIndexes[namespace] = pinecone.Index(pineconeIndexName).namespace(namespace);
  }
  return pineconeIndexes[namespace];
}

// Helper function to trim specified words from the end of a concept segment
const WORDS_TO_TRIM = ["Architecture", "Pattern"];
function trimConceptSegment(segment: string): string {
  if (typeof segment !== 'string' || !segment.trim()) {
    return segment;
  }
  const words = segment.trim().split(/\s+/);
  const lastWord = words[words.length - 1];
  
  if (WORDS_TO_TRIM.some(trimWord => lastWord.toLowerCase() === trimWord.toLowerCase())) {
    // Check if it's exactly the word or ends with it (e.g. "Architecture", not "Architectures")
    // To be more precise, we can check if the last word *is* one of the trim words, case-insensitively.
    if (WORDS_TO_TRIM.map(w => w.toLowerCase()).includes(lastWord.toLowerCase())) {
        words.pop(); // Remove the last word
        return words.join(' ').trim(); // Return the rest, or empty if it was the only word
    }
  }
  return segment.trim(); // Return original if no trim word found at the end or if it's more complex
}

export async function POST(request: Request) {
  try {
    // Extract API key from Authorization header
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null;
    
    if (!apiKey) {
      return NextResponse.json({ error: 'Missing API Key in Authorization header' }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json();
    const { repository_slug } = body;
    
    if (!repository_slug) {
      return NextResponse.json({ error: 'Missing repository_slug in request body' }, { status: 400 });
    }
    
    // Authenticate and get user ID
    const userId = await validateApiKeyAndGetUser(apiKey);
    if (!userId) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 });
    }
    
    console.log(`[Domain Concepts] Processing request for repository: ${repository_slug} from user: ${userId}`);
    
    // Check repository access
    const accessDetails = await getRepositoryAccessDetails(userId, repository_slug);
    if (accessDetails.type !== 'public' && accessDetails.type !== 'private') {
      console.warn(`[Domain Concepts] Access denied: ${accessDetails.type} for repo ${repository_slug}`);
      return NextResponse.json({ 
        error: `Repository access denied: ${accessDetails.message || accessDetails.type}` 
      }, { status: 403 });
    }
    
    // Get Pinecone namespace
    const pineconeNamespace = accessDetails.namespace!;
    console.log(`[Domain Concepts] Using Pinecone namespace: ${pineconeNamespace}`);
    
    // Query Pinecone for all decisions
    const index = await getPineconeIndex(pineconeNamespace);
    const dummyVector = new Array(1536).fill(0);
    
    const queryResponse = await index.query({
      vector: dummyVector,
      topK: 10000,
      includeMetadata: true,
      filter: { 'is_superseded': false }
    });
    
    console.log(`[Domain Concepts] Found ${queryResponse.matches.length} active decisions in namespace ${pineconeNamespace} for concept aggregation`);
    
    // Extract and count domain concepts hierarchically
    const hierarchicalConceptCounts: Record<string, number> = {}; // Key: "L1>L2>L3>L4", Value: count
    
    queryResponse.matches.forEach((match: { 
      id: string; 
      score: number; 
      metadata?: { 
        domain_concepts?: string[]; // This is expected to be a path array
        [key: string]: any;
      }
    }) => {
      if (match.metadata && Array.isArray(match.metadata.domain_concepts)) {
        let pathArray = match.metadata.domain_concepts.filter(segment => typeof segment === 'string' && segment.trim() !== '');
        
        if (pathArray.length > 0 && pathArray.length <= 4) { // Max 4 levels
          // Trim segments (all but the last one if it's a leaf of the original path)
          const originalPathLength = pathArray.length;
          pathArray = pathArray.map((segment, index) => {
            // Only trim non-leaf segments of the path
            if (index < originalPathLength -1) { 
              return trimConceptSegment(segment);
            } 
            return segment.trim(); // Don't trim the leaf node of the original path
          }).filter(segment => segment && segment.trim() !== ''); // Filter out any segments that became empty after trimming
          
          if (pathArray.length > 0) { // Ensure path is still valid after trimming
            const pathKey = pathArray.join(' > '); // Create a string key for the path
            hierarchicalConceptCounts[pathKey] = (hierarchicalConceptCounts[pathKey] || 0) + 1;
          }
        }
      }
    });
    
    // Format as array of objects { path: string[], count: number } and sort by count
    const aggregatedConcepts = Object.entries(hierarchicalConceptCounts)
      .map(([pathKey, count]) => ({
        path: pathKey.split(' > '), // Convert string key back to array path
        count
      }))
      .sort((a, b) => b.count - a.count);
    
    console.log(`[Domain Concepts] Aggregated ${aggregatedConcepts.length} unique hierarchical domain concepts`);
    
    return NextResponse.json({ 
      success: true, 
      aggregated_concepts: aggregatedConcepts,
      repository_slug,
      installation_id: accessDetails.installationId || 0
    });
    
  } catch (error: any) {
    console.error('[Domain Concepts] Error fetching domain concepts:', error);
    return NextResponse.json({ 
      error: 'Error fetching domain concepts', 
      details: error.message 
    }, { status: 500 });
  }
} 