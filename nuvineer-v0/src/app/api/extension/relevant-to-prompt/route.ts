import { NextRequest, NextResponse } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { createClient as createSupabaseClient, SupabaseClient } from '@supabase/supabase-js';
import OpenAI from 'openai'; // Assuming OpenAI for embeddings, adjust if different
import Anthropic from '@anthropic-ai/sdk'; // Import Anthropic SDK

import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails, RepositoryAccessDetails } from '@/lib/repository-access';
import { createSupabaseAdminClient } from '@/lib/supabase-server'; // Use admin client for fetching

// --- Local Helpers (Consider refactoring to shared lib if used elsewhere) ---

// Helper to get environment variables
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            console.warn(`[API Ext RelevantPrompt] Environment variable ${name} not found, using default value.`);
            return defaultValue;
        } else {
            throw new Error(`Missing required environment variable: ${name}`);
        }
    }
    return value;
}

// --- Pinecone Initialization ---
let pinecone: Pinecone | null = null;
const pineconeIndexes: Record<string, any> = {}; 

async function initializePineconeClient() {
    if (!pinecone) {
        try {
            const apiKey = getEnvVar('PINECONE_API_KEY');
            pinecone = new Pinecone({ apiKey });
            console.log(`[API Ext RelevantPrompt] Pinecone client initialized.`);
        } catch (error: any) {
            console.error("[API Ext RelevantPrompt] Failed to initialize Pinecone client:", error);
            pinecone = null; 
        }
    }
}

async function getPineconeIndex(namespace: string) {
    await initializePineconeClient(); 
    if (!pinecone) {
        throw new Error("Pinecone client is not initialized.");
    }
    const indexCacheKey = namespace; // Using namespace directly as key
    if (!pineconeIndexes[indexCacheKey]) {
        const indexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');
        pineconeIndexes[indexCacheKey] = pinecone.Index(indexName).namespace(namespace);
        console.log(`[API Ext RelevantPrompt] Pinecone index handle initialized for namespace: ${namespace}`);
    }
    return pineconeIndexes[indexCacheKey];
}
// --- End Pinecone Init ---

// --- OpenAI Initialization (for Embeddings) ---
let openai: OpenAI | null = null;

async function initializeOpenAIClient() {
    if (!openai) {
        try {
            const apiKey = getEnvVar('OPENAI_API_KEY');
            openai = new OpenAI({ apiKey });
            console.log('[API Ext RelevantPrompt] OpenAI client initialized.');
        } catch (error: any) {
             console.error('[API Ext RelevantPrompt] Failed to initialize OpenAI client:', error);
             openai = null;
        }
    }
}

async function generateEmbedding(text: string, model = "text-embedding-3-small"): Promise<number[]> {
    await initializeOpenAIClient();
    if (!openai) {
        throw new Error('OpenAI client is not initialized.');
    }
    if (!text || typeof text !== 'string') {
        throw new Error('Invalid text provided for embedding.');
    }
    // Replace newlines with spaces, as recommended by OpenAI for embeddings
    const input = text.replace(/\n/g, ' '); 
    try {
        const embedding = await openai.embeddings.create({
            model: model,
            input: input,
        });
        if (embedding?.data?.[0]?.embedding) {
            return embedding.data[0].embedding;
        } else {
            throw new Error('Invalid embedding response structure from OpenAI API.');
        }
    } catch (error) {
        console.error('[API Ext RelevantPrompt] Error generating embedding:', error);
        throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : String(error)}`);
    }
}
// --- End OpenAI Init ---

// --- Anthropic LLM Initialization ---
let anthropic: Anthropic | null = null;
const defaultClaudeModel = process.env.ANTHROPIC_MODEL || 'claude-sonnet-4-20250514'; // Use Sonnet 3.0 as default

async function initializeAnthropicClient() {
    if (!anthropic) {
        try {
            const apiKey = getEnvVar('ANTHROPIC_API_KEY');
            anthropic = new Anthropic({ apiKey });
            console.log('[API Ext RelevantPrompt] Anthropic client initialized.');
        } catch (error: any) {
            console.error('[API Ext RelevantPrompt] Failed to initialize Anthropic client:', error);
            anthropic = null;
        }
    }
}

async function callLLM(prompt: string, model = defaultClaudeModel): Promise<any> {
    await initializeAnthropicClient();
    if (!anthropic) {
        throw new Error('Anthropic client is not initialized.');
    }
    console.log(`[API Ext RelevantPrompt] Calling LLM (${model})...`);
    // Basic prompt logging (avoid logging potentially sensitive full prompt in production)
    console.log(`[API Ext RelevantPrompt] LLM Prompt (start): ${prompt.substring(0, 100)}...`); 

    try {
        const msg = await anthropic.messages.create({
            model: model,
            max_tokens: 2048, // Adjusted token limit for potentially complex ranking
            temperature: 0.2, // Lower temperature for more deterministic JSON output
            messages: [
                { role: "user", content: prompt }
            ],
        });

        // Ensure content is a text block before accessing .text
        const firstContentBlock = msg.content[0];
        let responseText = '';
        if (firstContentBlock && firstContentBlock.type === 'text') {
            responseText = firstContentBlock.text;
        }
        
        console.log(`[API Ext RelevantPrompt] LLM response received. Raw text length: ${responseText.length}`);

        // Improved JSON array extraction
        // Look for first "[" and last "]" to capture the entire array
        const firstBracket = responseText.indexOf('[');
        const lastBracket = responseText.lastIndexOf(']');
        
        if (firstBracket !== -1 && lastBracket !== -1 && lastBracket > firstBracket) {
            // Extract the JSON array text
            const jsonText = responseText.substring(firstBracket, lastBracket + 1);
            try {
                const parsedJson = JSON.parse(jsonText);
                console.log('[API Ext RelevantPrompt] Successfully parsed JSON response from LLM.');
                return parsedJson;
            } catch (parseError) {
                console.error('[API Ext RelevantPrompt] Failed to parse JSON from LLM response:', parseError);
                // Attempt fallback with relaxed parsing
                try {
                    // Try to clean the text before parsing
                    const cleanedText = jsonText
                        .replace(/(\r\n|\n|\r)/gm, ' ') // Remove line breaks
                        .replace(/\s+/g, ' ')           // Normalize whitespace
                        .replace(/,\s*]/g, ']');        // Remove trailing commas
                    
                    const parsedJson = JSON.parse(cleanedText);
                    console.log('[API Ext RelevantPrompt] Successfully parsed JSON with fallback cleaning.');
                    return parsedJson;
                } catch (fallbackError) {
                    // Avoid logging potentially huge raw text in case of error, log excerpt
                    console.error(`[API Ext RelevantPrompt] Raw LLM text excerpt (start): ${responseText.substring(0, 200)}...`);
                    throw new Error(`LLM response was not valid JSON, even after cleanup: ${parseError}`);
                }
            }
        } else {
            console.error('[API Ext RelevantPrompt] No JSON array found in LLM response.');
            console.error(`[API Ext RelevantPrompt] Raw LLM text excerpt (start): ${responseText.substring(0, 200)}...`);
            throw new Error('LLM response did not contain the expected JSON array format.');
        }
    } catch (error) {
        console.error('[API Ext RelevantPrompt] Error calling LLM:', error);
        throw new Error(`Failed to get response from LLM: ${error instanceof Error ? error.message : String(error)}`);
    }
}
// --- End Anthropic LLM Init ---

// --- Supabase Admin Client (for data fetching) ---
let supabaseAdmin: SupabaseClient | null = null;
function getSupabaseAdmin() {
    if (!supabaseAdmin) {
         try {
             supabaseAdmin = createSupabaseAdminClient();
         } catch (error: any) {
             console.error("[API Ext RelevantPrompt] Failed to initialize Supabase Admin client:", error);
             supabaseAdmin = null;
         }
    }
    if (!supabaseAdmin) {
         console.error("[API Ext RelevantPrompt] Supabase Admin client is not available.");
    }
    return supabaseAdmin;
}
// --- End Supabase Admin Init ---

// Define a type for the match object from Pinecone query response, if not already present
// This might be similar to one used in relevant-to-file/route.ts
interface PineconeMatch {
  id: string;
  score?: number; 
  metadata?: any; 
  // Potentially other fields like 'values', 'sparseValues' depending on Pinecone response
}

// Interfaces based on the file outline (ensure these match actual definitions if they exist elsewhere)
interface DecisionMetadata {
    title: string;
    relevance_reason?: string; 
    description?: string | null;
    rationale?: string | null;
    pr_number?: number;
    pr_title?: string;
    pr_url?: string;
    pr_merged_at?: number | string | null; // Allow for flexible date formats
    relevant_files?: string[];
    related_files?: string[]; // Added to match extension interface
    created_at?: string;
    dev_prompt?: string | null; // Added to match extension interface
    // Add other fields if needed for the LLM context
}

interface Decision {
    id: string; // Pinecone ID
    score?: number; // Pinecone score
    metadata: DecisionMetadata;
    // Potentially add other top-level fields from Pinecone if they are not in metadata
}

// Define Relationship interface if not already defined, based on usage
interface Relationship {
    source_decision_pinecone_id: string;
    target_decision_pinecone_id: string;
    relationship_type: string;
}

// --- Main POST Handler ---
export async function POST(request: NextRequest) {
    // 1. Authenticate API Key
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null;

    if (!apiKey) {
        console.warn('[API Ext RelevantPrompt] Missing API Key');
        return NextResponse.json({ success: false, error: 'Missing API Key in Authorization header (ApiKey YOUR_KEY)' }, { status: 401 });
    }

    let userId: string | null;
    try {
        userId = await validateApiKeyAndGetUser(apiKey);
        if (!userId) {
            console.warn('[API Ext RelevantPrompt] Invalid API Key presented.');
            return NextResponse.json({ success: false, error: 'Invalid API Key' }, { status: 401 });
        }
        console.log(`[API Ext RelevantPrompt] API Key validated. User ID: ${userId}`);
    } catch (error: any) {
        console.error('[API Ext RelevantPrompt] Error validating API key:', error);
        return NextResponse.json({ success: false, error: 'Internal server error during authentication' }, { status: 500 });
    }

    // 2. Parse Request Body
    let body: { repository_slug: string; prompt: string; limit?: number };
    try {
        body = await request.json();
    } catch (error) {
        console.warn('[API Ext RelevantPrompt] Invalid JSON body');
        return NextResponse.json({ success: false, error: 'Invalid JSON body' }, { status: 400 });
    }

    const { repository_slug, prompt, limit = 15 } = body; // Default limit for prompt-based search
    if (!repository_slug || !prompt) {
        console.warn('[API Ext RelevantPrompt] Missing repository_slug or prompt');
        return NextResponse.json({ success: false, error: 'Missing required body parameters: repository_slug, prompt' }, { status: 400 });
    }
    if (typeof prompt !== 'string' || prompt.trim().length === 0) {
         return NextResponse.json({ success: false, error: 'Prompt cannot be empty.' }, { status: 400 });
    }
    console.log(`[API Ext RelevantPrompt] Request for repo: ${repository_slug}, User: ${userId}, Prompt: "${prompt}"`);

    // 3. Determine Repository Access & Pinecone Namespace
    let accessDetails: RepositoryAccessDetails;
    try {
        accessDetails = await getRepositoryAccessDetails(userId, repository_slug);
    } catch (error: any) {
        console.error(`[API Ext RelevantPrompt] Error determining repository access for ${repository_slug}, User ${userId}:`, error);
        return NextResponse.json({ success: false, error: 'Failed to determine repository access' }, { status: 500 });
    }

    switch (accessDetails.type) {
        case 'forbidden':
            console.warn(`[API Ext RelevantPrompt] Forbidden access for User ${userId} to repo ${repository_slug}`);
            return NextResponse.json({ success: false, error: 'Access Denied: User does not have permission for this repository via the GitHub App.' }, { status: 403 });
        case 'not_found':
            console.warn(`[API Ext RelevantPrompt] Repository not found: ${repository_slug}`);
            return NextResponse.json({ success: false, error: 'Repository not found or inaccessible.' }, { status: 404 });
         case 'error':
             console.error(`[API Ext RelevantPrompt] Access check failed for ${repository_slug}: ${accessDetails.message}`);
             return NextResponse.json({ success: false, error: `Access check failed: ${accessDetails.message}` }, { status: 500 });
        case 'public':
        case 'private':
            console.log(`[API Ext RelevantPrompt] Access granted (${accessDetails.type}). Namespace: ${accessDetails.namespace}`);
            break; // Continue below
        default:
            console.error(`[API Ext RelevantPrompt] Unexpected access details type:`, accessDetails);
            return NextResponse.json({ success: false, error: 'Internal server error during access check' }, { status: 500 });
    }
    
    const pineconeNamespace = accessDetails.namespace!;
    if (!pineconeNamespace) {
         console.error(`[API Ext RelevantPrompt] Namespace is missing in access details for ${repository_slug}`);
         return NextResponse.json({ success: false, error: 'Internal configuration error: Namespace missing.' }, { status: 500 });
    }

    try {
        // 4. Generate Embedding for the Prompt
        console.log(`[API Ext RelevantPrompt] Generating embedding for prompt...`);
        const queryVector = await generateEmbedding(prompt);
        console.log(`[API Ext RelevantPrompt] Embedding generated.`);

        // 5. Initial Query to Pinecone (Keyword-based or Broad Vector Search)
        const index = await getPineconeIndex(pineconeNamespace);

        // For simplicity, this example uses a dummy vector and filter.
        // In a real scenario, you might generate an embedding for the prompt
        // or use Pinecone's sparse-dense vector search if keywords are extracted.
        const dimension = 1536; // Example dimension for OpenAI ada-002
        const dummyVector = new Array(dimension).fill(0); // Replace with actual embedding if doing vector search

        // Broad query to get a candidate set of decisions
        // Adjust topK as needed; it's a trade-off between recall and LLM processing cost
        const initialTopK = Math.max(limit * 5, 50); // Fetch more candidates for LLM to rank
        console.log(`[API Ext RelevantPrompt] Performing initial Pinecone query with topK: ${initialTopK}, filtering for active decisions.`);

        const queryResponse = await index.query({
            vector: dummyVector, // Or embedding of the prompt
            topK: initialTopK,
            filter: { 'is_superseded': false }, // <--- MODIFIED: Filter for active decisions
            includeMetadata: true,
            includeValues: false // Values not needed for re-ranking logic here
        });

        let matches: PineconeMatch[] = queryResponse.matches || [];
        console.log(`[API Ext RelevantPrompt] Pinecone returned ${matches.length} initial active candidates.`);

        if (matches.length === 0) {
            return NextResponse.json({ success: true, decisions: [], message: "No potential active decisions found in the initial search." });
        }

        // Limit to 100 after Pinecone query, before LLM
        matches = matches.slice(0, Math.min(matches.length, 100)); 
        console.log(`[API Ext RelevantPrompt] ${matches.length} active candidates remaining for LLM after slicing.`);
        
        // Map to Decision type for LLM prompt
        const candidateDecisions: Decision[] = matches.map(match => 
            mapPineconeMatchToDecision(match, match.score)
        );
        
        const candidateDecisionIds = candidateDecisions.map((d: Decision) => d.id);

        if (candidateDecisions.length === 0) {
             console.log(`[API Ext RelevantPrompt] No non-superseded decisions found after initial query and slice.`);
             return NextResponse.json({ success: true, decisions: [] }, { status: 200 });
        }

        // 7. Fetch Relationships Between Candidate Decisions
        const supabaseAdminClient = getSupabaseAdmin();
        let relationshipsBetweenCandidates: Relationship[] = [];
        console.log(`[API Ext RelevantPrompt] Fetching relationships between ${candidateDecisionIds.length} candidate decisions.`);
        
        if (supabaseAdminClient) {
            try {
                const { data: relationshipLinks, error: linksError } = await supabaseAdminClient
                    .from('decision_relationships')
                    .select('source_decision_pinecone_id, target_decision_pinecone_id, relationship_type')
                    .in('source_decision_pinecone_id', candidateDecisionIds)
                    .in('target_decision_pinecone_id', candidateDecisionIds)
                    .eq('repository_slug', repository_slug); 

                if (linksError) {
                    console.error('[API Ext RelevantPrompt] Supabase error fetching relationships between candidates:', linksError);
                } else if (relationshipLinks) {
                    relationshipsBetweenCandidates = relationshipLinks.map((r: any) => ({
                        source_decision_pinecone_id: r.source_decision_pinecone_id,
                        target_decision_pinecone_id: r.target_decision_pinecone_id,
                        relationship_type: r.relationship_type,
                    }));
                    console.log(`[API Ext RelevantPrompt] Found ${relationshipsBetweenCandidates.length} relationships between candidates.`);
                }
            } catch (supaError: any) {
                console.error('[API Ext RelevantPrompt] Unexpected error fetching relationships between candidates from Supabase:', supaError);
            }
        } else {
            console.warn('[API Ext RelevantPrompt] Supabase Admin client not available, skipping fetching relationships between candidates.');
        }

        // 8. Prepare Prompt and Call LLM for Re-ranking
        let orderedDecisionIds: string[] = candidateDecisionIds; // Default to Pinecone order
        let tempLlmReasons = new Map<string, string>(); // Declare and initialize the map here
        try {
            const llmPrompt = createRerankingPrompt(prompt, candidateDecisions, relationshipsBetweenCandidates);
            const llmResponse = await callLLM(llmPrompt);

            // Validate LLM response (should be an array of objects with id and reason)
            if (Array.isArray(llmResponse) && llmResponse.every(item => typeof item === 'object' && item !== null && typeof item.id === 'string' && typeof item.reason === 'string')) {
                console.log('[API Ext RelevantPrompt] LLM returned ordered IDs and reasons. Validating...');

                const llmRankedItems: { id: string; reason: string }[] = llmResponse;

                // Ensure LLM returned IDs that were actually candidates
                const validRankedItems = llmRankedItems.filter(item => candidateDecisionIds.includes(item.id));
                const llmReasonsMap = new Map(validRankedItems.map(item => [item.id, item.reason]));

                if (validRankedItems.length > 0) {
                    orderedDecisionIds = validRankedItems.map(item => item.id);

                    // Add any missing candidate IDs (that LLM might have dropped) back at the end, maintaining original relative order
                    const returnedIdSet = new Set(orderedDecisionIds);
                    candidateDecisionIds.forEach(id => {
                        if (!returnedIdSet.has(id)) {
                            orderedDecisionIds.push(id);
                        }
                    });
                    console.log(`[API Ext RelevantPrompt] Using LLM re-ordered list of ${orderedDecisionIds.length} IDs. Storing reasons.`);
                    // Store reasons temporarily to add them later
                    tempLlmReasons = llmReasonsMap;
                } else {
                     console.warn('[API Ext RelevantPrompt] LLM returned empty or invalid IDs. Falling back to Pinecone order.');
                     orderedDecisionIds = candidateDecisionIds; // Fallback
                }
            } else {
                console.warn('[API Ext RelevantPrompt] LLM response was not an array of {id: string, reason: string}. Falling back to Pinecone order.');
                orderedDecisionIds = candidateDecisionIds; // Fallback
            }
        } catch (llmError: any) {
            console.error(`[API Ext RelevantPrompt] LLM re-ranking failed: ${llmError.message}. Falling back to Pinecone order.`);
            orderedDecisionIds = candidateDecisionIds; // Fallback to original Pinecone order on error
            tempLlmReasons = new Map(); // Clear reasons on error
        }

        // 9. Create Final Sorted Decisions List based on LLM Order (or fallback)
        // Apply LLM reasons here
        const decisionMap = new Map(candidateDecisions.map(d => [d.id, d]));
        const finalDecisions = orderedDecisionIds
             .map(id => {
                 const decision = decisionMap.get(id);
                 if (decision) {
                     // Add the LLM relevance reason if available
                     decision.metadata.relevance_reason = tempLlmReasons.get(id);
                     return decision;
                 }
                 return undefined;
             })
             .filter(decision => decision !== undefined && decision.metadata.relevance_reason) as Decision[]; // Filter out undefined and decisions without relevance_reason
 
        console.log(`[API Ext RelevantPrompt] Returning ${finalDecisions.length} relevant, non-superseded decisions.`);
        return NextResponse.json({ success: true, decisions: finalDecisions }, { status: 200 });

    } catch (error: any) {
        console.error(`[API Ext RelevantPrompt] Error processing request:`, error);
        return NextResponse.json({ success: false, error: `Internal server error: ${error.message}` }, { status: 500 });
    }
}

// Optional: Add handler for GET or other methods if needed, otherwise they default to 405 Method Not Allowed
// export async function GET(request: NextRequest) {
//     return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
// } 

// --- Helper Function to Create LLM Prompt ---

function createRerankingPrompt(userPrompt: string, decisions: Decision[], relationships: Relationship[]): string {
    let prompt = `User Query: "${userPrompt}"\n\n`;
    prompt += `Candidate Architectural Decisions (ranked by initial similarity):
`;

    decisions.forEach((decision, index) => {
        prompt += `--- Decision ${index + 1} ---\n`;
        prompt += `ID: ${decision.id}\n`;
        prompt += `Title: ${decision.metadata.title}\n`;
        if (decision.metadata.description) prompt += `Description: ${decision.metadata.description}\n`;
        if (decision.metadata.rationale) prompt += `Rationale: ${decision.metadata.rationale}\n`;
        // Optionally include score: prompt += `Initial Score: ${decision.score?.toFixed(4)}\n`;
        prompt += `\n`;
    });

    if (relationships.length > 0) {
        prompt += `Known Relationships Between These Candidates:
`;
        relationships.forEach(rel => {
             prompt += `- ${rel.source_decision_pinecone_id} (${rel.relationship_type}) ${rel.target_decision_pinecone_id}\n`;
        });
        prompt += `\n`;
    }

    prompt += `Instructions:
`;
    prompt += `1. Analyze the User Query and the Candidate Decisions provided.
`;
    prompt += `2. Consider the relationships between decisions. Decisions that relate to each other might be relevant together.
`;
    prompt += `3. Re-rank the Candidate Decisions based *primarily* on their relevance to the User Query. Use the relationships as secondary context.
`;
    prompt += `4. Your goal is to provide the most helpful ordered list of decisions for someone asking the User Query.
`;
    prompt += `5. For each decision you include in the ranked list, provide a concise, single bullet point explaining *why* it is relevant to the User Query.
`;
    prompt += `6. Output ONLY a valid JSON array of objects, with no surrounding text, markdown code blocks, or explanation.
`;
    prompt += `7. Each object in your JSON array MUST have exactly two properties: "id" (string) and "reason" (string).
`;
    prompt += `8. CRITICAL: Your entire response must be a properly formatted JSON array that can be directly parsed with JSON.parse().
`;
    
    prompt += `Example correct JSON output:
[
  {"id": "decision_123", "reason": "* Directly implements the feature mentioned in the query"},
  {"id": "decision_456", "reason": "* Addresses the performance concerns in the query"}
]
`;

    return prompt;
}

// Helper function to map Pinecone match to our Decision structure
// This function should be defined in your actual file, possibly around line 498 from the outline.
// For the purpose of this edit, I'm providing a plausible definition.
// Ensure this matches your actual implementation.
function mapPineconeMatchToDecision(match: PineconeMatch, score: number | undefined): Decision {
    const metadata = match.metadata || {};
    
    // Get files list from relevant_files or related_files (if one exists)
    const filesList = metadata.related_files || metadata.relevant_files || [];
    
    return {
        id: match.id, // This is the Pinecone vector ID
        score: score,
        metadata: {
            title: metadata.title || 'Untitled Decision',
            description: metadata.description || null,
            rationale: metadata.rationale || null,
            pr_number: metadata.pr_number || undefined,
            pr_title: metadata.pr_title || undefined,
            pr_url: metadata.pr_url || undefined,
            pr_merged_at: metadata.pr_merged_at || null,
            relevant_files: metadata.relevant_files || filesList, // Keep for backward compatibility
            related_files: filesList, // Ensure we always have related_files for extension compatibility
            created_at: metadata.created_at || undefined,
            dev_prompt: metadata.dev_prompt || null, // Include dev_prompt
            relevance_reason: undefined, // This will be populated by the LLM if it's part of the output
            // Ensure all fields expected by the LLM prompt are here or handled
        }
    };
} 