import { NextRequest, NextResponse } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { createSupabaseAdminClient } from '@/lib/supabase-server';
import { getRepositoryAccessDetails } from '@/lib/repository-access';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';

// --- Initialize Pinecone client ---
let pinecone: Pinecone | null = null;
async function initializePineconeClient(): Promise<Pinecone | null> {
  if (pinecone) return pinecone;
  
  if (!process.env.PINECONE_API_KEY) {
    console.error('[API Ext Decision] Missing PINECONE_API_KEY');
    return null;
  }
  
  try {
    pinecone = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY
    });
    return pinecone;
  } catch (error) {
    console.error('[API Ext Decision] Error initializing Pinecone client:', error);
    return null;
  }
}

// --- Get Pinecone Index Handle ---
// Cache for index handles to avoid repeated initialization
const pineconeIndexes: Record<string, any> = {};

async function getPineconeIndex(namespace: string) {
  if (!pineconeIndexes[namespace]) {
    await initializePineconeClient();
    if (!pinecone) {
      throw new Error('Pinecone client is not initialized.');
    }
    const indexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';
    console.log(`[API Ext Decision] Getting index '${indexName}' namespace: ${namespace}`);
    pineconeIndexes[namespace] = pinecone.Index(indexName).namespace(namespace);
  }
  return pineconeIndexes[namespace];
}

// Define interfaces for our data structures
interface Decision {
  id: string;
  metadata: Record<string, any>;
  referenced_decisions?: ReferencedDecision[];
}

interface ReferencedDecision {
  id: string;
  metadata: Record<string, any>;
  relationship_type?: string;
}

/**
 * API handler for fetching a single decision by ID
 * 
 * Required parameters:
 * - repository_slug: The repository slug (owner/repo)
 * - id (from route parameter): The Pinecone decision ID
 * 
 * Headers:
 * - Authorization: ApiKey YOUR_API_KEY
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log(`[API Ext Decision] Received request for decision ID: ${params.id}`);
  
  // 1. Extract Decision ID from path parameter
  const decisionId = params.id;
  if (!decisionId) {
    return NextResponse.json(
      { success: false, error: 'Decision ID is required' },
      { status: 400 }
    );
  }
  
  // 2. Parse request for repository slug
  let body;
  try {
    body = await request.json();
  } catch (error) {
    console.error('[API Ext Decision] JSON parse error:', error);
    return NextResponse.json(
      { success: false, error: 'Invalid JSON in request body' },
      { status: 400 }
    );
  }
  
  const { repository_slug } = body;
  if (!repository_slug) {
    return NextResponse.json(
      { success: false, error: 'repository_slug is required in request body' },
      { status: 400 }
    );
  }
  
  // 3. Authenticate the API key
  const authHeader = request.headers.get('Authorization');
  const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null;
  
  if (!apiKey) {
    console.warn(`[API Ext Decision] Missing API Key for decision ID: ${decisionId}`);
    return NextResponse.json(
      { success: false, error: 'Missing API Key in Authorization header (ApiKey YOUR_KEY)' },
      { status: 401 }
    );
  }
  
  let userId: string | null;
  try {
    userId = await validateApiKeyAndGetUser(apiKey);
    if (!userId) {
      console.warn(`[API Ext Decision] Invalid API Key for decision ID: ${decisionId}`);
      return NextResponse.json({ success: false, error: 'Invalid API Key' }, { status: 401 });
    }
    console.log(`[API Ext Decision] API Key validated. User ID: ${userId}`);
  } catch (error: any) {
    console.error(`[API Ext Decision] Error validating API key:`, error);
    return NextResponse.json(
      { success: false, error: 'Internal server error during authentication' },
      { status: 500 }
    );
  }
  
  // 4. Check repository access and get namespace
  let accessDetails;
  try {
    accessDetails = await getRepositoryAccessDetails(userId, repository_slug);
  } catch (error: any) {
    console.error(`[API Ext Decision] Error checking repository access:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to determine repository access' },
      { status: 500 }
    );
  }
  
  // Handle different access outcomes
  let pineconeNamespace;
  switch (accessDetails.type) {
    case 'forbidden':
      console.warn(`[API Ext Decision] Forbidden access for User ${userId} to repo ${repository_slug}`);
      return NextResponse.json(
        { success: false, error: 'Access Denied: User does not have permission for this repository via the GitHub App.' },
        { status: 403 }
      );
    case 'not_found':
      console.warn(`[API Ext Decision] Repository not found: ${repository_slug}`);
      return NextResponse.json(
        { success: false, error: 'Repository not found or inaccessible.' },
        { status: 404 }
      );
    case 'error':
      console.error(`[API Ext Decision] Access check failed for ${repository_slug}: ${accessDetails.message}`);
      return NextResponse.json(
        { success: false, error: `Access check failed: ${accessDetails.message}` },
        { status: 500 }
      );
    case 'public':
    case 'private':
      pineconeNamespace = accessDetails.namespace;
      console.log(`[API Ext Decision] Access granted (${accessDetails.type}). Namespace: ${pineconeNamespace}`);
      break;
    default:
      console.error(`[API Ext Decision] Unexpected access details type:`, accessDetails);
      return NextResponse.json(
        { success: false, error: 'Internal server error during access check' },
        { status: 500 }
      );
  }
  
  if (!pineconeNamespace) {
    return NextResponse.json(
      { success: false, error: 'Failed to determine Pinecone namespace' },
      { status: 500 }
    );
  }
  
  // 5. Fetch the specific decision from Pinecone
  try {
    const pineconeIndex = await getPineconeIndex(pineconeNamespace);
    
    console.log(`[API Ext Decision] Fetching decision ID '${decisionId}' from namespace '${pineconeNamespace}'`);
    
    // Fetch the vector by ID
    const fetchResponse = await pineconeIndex.fetch([decisionId]);
    
    if (!fetchResponse || !fetchResponse.records || !fetchResponse.records[decisionId]) {
      console.log(`[API Ext Decision] Decision ID '${decisionId}' not found in namespace '${pineconeNamespace}'.`);
      return NextResponse.json(
        { success: false, error: 'Decision not found' },
        { status: 404 }
      );
    }
    
    const record = fetchResponse.records[decisionId];
    
    // The is_superseded status is now directly read from record.metadata
    // No need for the isDecisionSuperseded helper or to manually add the flag here,
    // assuming storeDecisionRecord and markDecisionAsSuperseded correctly set it in Pinecone.
    if (record.metadata?.is_superseded) {
      console.log(`[API Ext Decision] Decision ID '${decisionId}' is marked as superseded in its Pinecone metadata.`);
    }
    
    // Create the decision object
    const decision: Decision = {
      id: record.id,
      metadata: record.metadata || {},
      // We don't typically need the vector values
    };
    
    // 6. Check for and add referenced decisions
    // Query relationships in Supabase to get referenced decisions
    const supabase = createSupabaseAdminClient();
    let referencedDecisions: ReferencedDecision[] = [];
    
    if (supabase) {
      try {
        const { data: relationshipData, error: relError } = await supabase
          .from('decision_relationships')
          .select('target_decision_pinecone_id, relationship_type')
          .eq('repository_slug', repository_slug)
          .eq('source_decision_pinecone_id', decisionId)
          .not('relationship_type', 'eq', 'supersedes'); // Get all relationships except 'supersedes'
        
        if (relError) {
          console.error(`[API Ext Decision] Error fetching relationships:`, relError);
        } else if (relationshipData && relationshipData.length > 0) {
          console.log(`[API Ext Decision] Found ${relationshipData.length} referenced decisions.`);
          
          // Get the IDs of referenced decisions
          const referencedIds = relationshipData.map(rel => rel.target_decision_pinecone_id);
          
          // Fetch the referenced decisions from Pinecone
          if (referencedIds.length > 0) {
            const referencedResponse = await pineconeIndex.fetch(referencedIds);
            
            if (referencedResponse && referencedResponse.records) {
              referencedDecisions = referencedIds
                .filter(id => {
                  const refRecord = referencedResponse.records[id as string];
                  // Only include if the record exists AND is not superseded
                  return refRecord && refRecord.metadata?.is_superseded !== true;
                })
                .map(id => ({
                  id: id as string,
                  metadata: referencedResponse.records[id as string].metadata || {},
                  relationship_type: relationshipData.find(rel => rel.target_decision_pinecone_id === id)?.relationship_type as string | undefined
                }));
              console.log(`[API Ext Decision] After filtering for active, ${referencedDecisions.length} referenced decisions remain.`);
            }
          }
        }
      } catch (error) {
        console.error(`[API Ext Decision] Error processing referenced decisions:`, error);
      }
    }
    
    // Add referenced decisions to the main decision object if any were found
    if (referencedDecisions.length > 0) {
      decision.referenced_decisions = referencedDecisions;
    }
    
    console.log(`[API Ext Decision] Successfully fetched decision ID '${decisionId}'.`);
    return NextResponse.json({ success: true, decision });
    
  } catch (error: any) {
    console.error(`[API Ext Decision] Error fetching decision '${decisionId}':`, error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch decision from database', 
        details: error.message 
      },
      { status: 500 }
    );
  }
} 