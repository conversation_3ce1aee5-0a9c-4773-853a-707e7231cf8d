import { NextRequest, NextResponse } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { createClient, SupabaseClient } from '@supabase/supabase-js'; // Added for filtering superseded
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService'; // Assumes this service exists and works
// import { getPineconeIndex } from '@/lib/pinecone-client'; // REMOVED - Replaced with local implementation
import { getRepositoryAccessDetails, RepositoryAccessDetails } from '@/lib/repository-access'; // Placeholder for new access logic
// import { getEnvVar as getSharedEnvVar } from '@/lib/utils'; // REMOVED - Local getEnvVar is now self-contained

// --- Local Pinecone Helpers (Pattern from other extension API routes) ---

// Helper to get environment variables (local copy to ensure independence for this route)
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            console.warn(`[API Ext Decisions] Environment variable ${name} not found, using default value: ${defaultValue}`);
            return defaultValue;
        } else {
            // Critical variable missing
            console.error(`[API Ext Decisions] Missing required environment variable: ${name}`);
            throw new Error(`[API Ext Decisions] Missing required environment variable: ${name}`);
        }
    }
    return value;
}


let pinecone: Pinecone | null = null;
// Store initialized indexes per namespace to avoid re-initializing constantly
const pineconeIndexes: Record<string, any> = {};

// Initialize Pinecone client (only once)
async function initializePineconeClient() {
    if (!pinecone) {
        try {
            const apiKey = getEnvVar('PINECONE_API_KEY');
            pinecone = new Pinecone({ apiKey });
            console.log(`[API Ext Decisions] Pinecone client initialized.`);
        } catch (error: any) {
            console.error("[API Ext Decisions] Failed to initialize Pinecone client:", error);
            pinecone = null;
        }
    }
}

// Get or initialize index for a specific namespace
async function getPineconeIndex(namespace: string) {
    await initializePineconeClient(); // Ensure client is ready
    if (!pinecone) {
        throw new Error("[API Ext Decisions] Pinecone client is not initialized.");
    }
    const indexCacheKey = namespace; // Using namespace directly as key
    if (!pineconeIndexes[indexCacheKey]) {
        const indexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions'); // Default index name
        pineconeIndexes[indexCacheKey] = pinecone.Index(indexName).namespace(namespace);
        console.log(`[API Ext Decisions] Pinecone index handle initialized for namespace: ${namespace}, index: ${indexName}`);
    }
    return pineconeIndexes[indexCacheKey];
}
// --- END Local Pinecone Helpers ---

// ---> Supabase Client Initialization for filtering superseded decisions - Added
// TODO: Consider moving this to a shared lib or injecting it
let supabase: SupabaseClient | null = null;
function initializeSupabaseClient() {
    if (!supabase) {
        try {
            const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
            const supabaseServiceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY');
            supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
                auth: { persistSession: false }
            });
            console.log("[API Ext Decisions] Supabase client initialized for filtering.");
        } catch (error: any) {
            console.error("[API Ext Decisions] Failed to initialize Supabase client:", error);
            supabase = null;
        }
    }
}
// --- END Supabase Init ---

export async function POST(request: NextRequest) {
    // Initialize Supabase client needed for filtering later - Added
    initializeSupabaseClient();

    // 1. Authenticate API Key
    const authHeader = request.headers.get('Authorization');
    // Recommended: Use 'ApiKey' scheme or stick to 'Bearer' consistently
    const apiKey = authHeader?.startsWith('ApiKey ') ? authHeader.substring(7) : null; 

    if (!apiKey) {
        console.warn('[API Ext Decisions] Missing API Key');
        // Consider 'ApiKey' scheme if changing from Bearer
        return NextResponse.json({ error: 'Missing API Key in Authorization header (ApiKey YOUR_KEY)' }, { status: 401 });
    }

    let userId: string | null;
    try {
        userId = await validateApiKeyAndGetUser(apiKey);
        if (!userId) {
            console.warn('[API Ext Decisions] Invalid API Key presented.');
            return NextResponse.json({ error: 'Invalid API Key' }, { status: 401 });
        }
        console.log(`[API Ext Decisions] API Key validated. User ID: ${userId}`);
    } catch (error: any) {
        console.error('[API Ext Decisions] Error validating API key:', error);
        return NextResponse.json({ error: 'Internal server error during authentication' }, { status: 500 });
    }

    // 2. Parse Request Body
    let body: { repository_slug: string; limit?: number, pr_url?: string };
    try {
        body = await request.json();
    } catch (error) {
        console.warn('[API Ext Decisions] Invalid JSON body');
        return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const { repository_slug, limit = 20, pr_url } = body; // Default limit
    if (!repository_slug) {
        console.warn('[API Ext Decisions] Missing repository_slug');
        return NextResponse.json({ error: 'Missing required body parameter: repository_slug' }, { status: 400 });
    }
    console.log(`[API Ext Decisions] Request for repo: ${repository_slug}, User: ${userId}`);


    // 3. Determine Repository Access & Pinecone Namespace
    let accessDetails: RepositoryAccessDetails;
    try {
        // This is the core function that needs implementation:
        // It checks if public, or if private & user has access via installation.
        // Returns { type: 'public', namespace: '0-...' }
        //      or { type: 'private', namespace: 'installId-...', installationId: ... }
        //      or { type: 'forbidden' }
        //      or { type: 'not_found' }
        //      or { type: 'error', message: '...' }
        accessDetails = await getRepositoryAccessDetails(userId, repository_slug);

    } catch (error: any) {
        console.error(`[API Ext Decisions] Error determining repository access for ${repository_slug}, User ${userId}:`, error);
        return NextResponse.json({ error: 'Failed to determine repository access' }, { status: 500 });
    }

    // Handle access denial or errors before attempting to use namespace
    switch (accessDetails.type) {
        case 'forbidden':
            console.warn(`[API Ext Decisions] Forbidden access for User ${userId} to repo ${repository_slug}`);
            return NextResponse.json({ error: 'Access Denied: User does not have permission for this repository via the GitHub App.' }, { status: 403 });
        case 'not_found':
            console.warn(`[API Ext Decisions] Repository not found: ${repository_slug}`);
            return NextResponse.json({ error: 'Repository not found or inaccessible.' }, { status: 404 });
        case 'error':
            console.error(`[API Ext Decisions] Access check failed for ${repository_slug}: ${accessDetails.message}`);
            return NextResponse.json({ error: `Access check failed: ${accessDetails.message}` }, { status: 500 });
        case 'public':
        case 'private':
            console.log(`[API Ext Decisions] Access granted (${accessDetails.type}). Namespace: ${accessDetails.namespace}`);
            if (!accessDetails.namespace) {
                // This case should ideally not be reached if 'public' or 'private' always provide a namespace.
                console.error(`[API Ext Decisions] Namespace is missing for ${accessDetails.type} access to repo ${repository_slug}`);
                return NextResponse.json({ error: 'Internal configuration error: Namespace missing despite access grant.' }, { status: 500 });
            }
            break; // Continue, namespace is expected to be defined
        default:
            console.error(`[API Ext Decisions] Unexpected access details type:`, accessDetails);
            return NextResponse.json({ error: 'Internal server error during access check' }, { status: 500 });
    }

    const pineconeNamespace = accessDetails.namespace; // Now guaranteed to be a string if we proceed

    // 4. Query Pinecone
    try {
        const index = await getPineconeIndex(pineconeNamespace!); // Use non-null assertion as it's checked

        // Base filter to exclude superseded decisions
        const baseFilter = { 'is_superseded': false };
        let finalFilter: any = baseFilter;

        if (pr_url) {
            console.log(`[API Ext Decisions] Applying PR URL filter: ${pr_url}`);
            finalFilter = { $and: [baseFilter, { pr_url: pr_url }] };
        }

        console.log(`[API Ext Decisions] Querying Pinecone namespace '${pineconeNamespace}' with filter:`, JSON.stringify(finalFilter));

        // Using a dummy vector for metadata-based or namespace-wide retrieval
        const dummyVector = new Array(1536).fill(0); // Assuming OpenAI embedding size

        const queryResponse = await index.query({
            vector: dummyVector,
            topK: limit,
            filter: finalFilter,
            includeMetadata: true,
            includeValues: false, // Values usually not needed for decisions listing
        });

        // Original mapping from Pinecone results
        // Define a simple type for Pinecone matches
        type PineconeMatch = {
            id: string;
            score?: number;
            metadata?: Record<string, any>; // Adjust metadata type as needed
        };

        // Type for the objects within the decisionsFromPinecone array
        type DecisionFromPinecone = {
            id: string;
            score?: number;
            metadata?: Record<string, any>; 
        };

        let decisionsFromPinecone: DecisionFromPinecone[] = queryResponse.matches.map((match: PineconeMatch) => ({
            id: match.id,
            score: match.score, // May not be relevant with dummy vector
            metadata: match.metadata,
        }));

        console.log(`[API Ext Decisions] Found ${decisionsFromPinecone.length} decisions in namespace ${pineconeNamespace} after applying supersedes filter.`);

        return NextResponse.json({ success: true, decisions: decisionsFromPinecone });

    } catch (error: any) {
        console.error(`[API Ext Decisions] Error processing request for repo ${repository_slug}:`, error);
        const errorMessage = error.message?.includes('Namespace not found') || error.message?.includes('does not exist')
            ? 'No decision data found for this repository.'
            : 'Failed to fetch decisions from data store.';
         const statusCode = error.message?.includes('Namespace not found') || error.message?.includes('does not exist') ? 404 : 500;
        return NextResponse.json({ error: errorMessage, details: error.message }, { status: statusCode });
    }
} 