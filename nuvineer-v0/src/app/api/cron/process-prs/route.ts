import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Octokit } from '@octokit/rest';
import { createAppAuth } from '@octokit/auth-app';
import { processMergedPR } from '@/orchestrator';
import { fetchAndFormatPRData } from '@/lib/github';

// Add ProcessMergedPRResult interface for type safety
interface ProcessMergedPRResult {
    status?: string;
    decisions?: any[];
    reason?: string;
    error?: string;
    errors?: { title: string; error: string }[];
    duration?: number;
    decisions_processed?: number;
}

// Helper function to get environment variables
function getRequiredEnvVar(name: string): string {
    const value = process.env[name];
    if (!value) {
        console.error(`[Cron Job] Missing required environment variable: ${name}`);
        throw new Error(`Missing required environment variable: ${name}`);
    }
    return value;
}

// Initialize Supabase Client (similar to webhook)
let supabaseClient: import('@supabase/supabase-js').SupabaseClient | null = null;
try {
    const supabaseUrl = getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_URL');
    const supabaseServiceRoleKey = getRequiredEnvVar('SUPABASE_SERVICE_ROLE_KEY');
    supabaseClient = createClient(supabaseUrl, supabaseServiceRoleKey, {
        auth: { persistSession: false }
    });
    console.log("[Cron Job/Supabase] Client initialized.");
} catch (error: any) {
     console.error("[Cron Job/Supabase] Error initializing Supabase client:", error.message || error);
     supabaseClient = null;
}

// Main handler for the cron job (triggered via GET request)
export async function GET(request: Request) {
    console.log('[Cron Job] Received trigger request.');

    // 1. Authenticate the Cron Job Request
    const cronSecret = getRequiredEnvVar('CRON_SECRET');
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${cronSecret}`) {
        console.warn('[Cron Job] Unauthorized attempt.');
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.log('[Cron Job] Authorization successful.');

    if (!supabaseClient) {
        console.error('[Cron Job] Supabase client not available. Aborting run.');
        return NextResponse.json({ error: 'Internal Server Error: Supabase connection failed' }, { status: 500 });
    }

    // Constants for processing
    const BATCH_SIZE = 5; // Number of PRs to process per run
    const MAX_ATTEMPTS = 3; // Max times to retry a failed PR

    try {
        // 2. Fetch a batch of pending PRs from Supabase
        console.log(`[Cron Job] Fetching up to ${BATCH_SIZE} pending PRs from Supabase.`);
        const { data: pendingPRs, error: fetchError } = await supabaseClient
            .from('repository_pr_analysis_status')
            .select('*') // Select all columns needed for processing
            .eq('status', 'pending')
            // Optional: Add filtering for retry attempts
            // .lt('retry_attempts', MAX_ATTEMPTS) // Assuming you add a retry_attempts column
            .order('pr_merged_at', { ascending: true }) // Process oldest first
            .limit(BATCH_SIZE);

        if (fetchError) {
            console.error('[Cron Job] Error fetching pending PRs:', fetchError);
            throw fetchError;
        }

        if (!pendingPRs || pendingPRs.length === 0) {
            console.log('[Cron Job] No pending PRs found to process.');
            return NextResponse.json({ success: true, message: 'No pending PRs to process.' });
        }

        console.log(`[Cron Job] Found ${pendingPRs.length} pending PRs to process.`);
        console.log(`[Cron Job] ================= PENDING ITEMS SUMMARY =================`);
        pendingPRs.forEach((pr, index) => {
            console.log(`[Cron Job] ${index + 1}. ${pr.repository_slug}#${pr.pr_number} - "${pr.pr_title}" (${pr.is_pseudo_pr ? 'COMMIT' : 'PR'}) - Status: ${pr.status}`);
        });
        console.log(`[Cron Job] ========================================================`);

        // 3. Process each PR in the batch
        const results = [];
        for (const pr of pendingPRs) {
            const logPrefix = `[Cron Job - PR ${pr.repository_slug}#${pr.pr_number}]`;
            console.log(`${logPrefix} Starting processing.`);
            let prStatus: 'completed' | 'failed' | 'no_decisions' | 'analyzed' = 'failed'; // Default to failed, added 'analyzed'
            let errorMessage: string | null = null;
            let analysisResult: ProcessMergedPRResult | null = null; // Declare outside try block

            try {
                // 3a. Update status to 'processing' in Supabase
                console.log(`${logPrefix} Updating status to 'processing'.`);
                const { error: updateError } = await supabaseClient
                    .from('repository_pr_analysis_status')
                    .update({ status: 'processing', last_analyzed_at: new Date().toISOString() })
                    .eq('repository_slug', pr.repository_slug)
                    .eq('pr_number', pr.pr_number);

                if (updateError) {
                    console.error(`${logPrefix} Failed to update status to 'processing':`, updateError);
                    // Continue to next PR if update fails? Or log and stop? For now, log and throw.
                    throw new Error(`Failed to lock PR for processing: ${updateError.message}`);
                }

                // 3b. Authenticate with GitHub App for this installation
                const appId = getRequiredEnvVar('GITHUB_APP_ID');
                const privateKey = getRequiredEnvVar('GITHUB_APP_PRIVATE_KEY').replace(/\n/g, '\n');
                const installationId = pr.installation_id;

                if (!installationId) {
                     throw new Error(`Missing installation_id for PR ${pr.repository_slug}#${pr.pr_number}`);
                }

                const appAuth = createAppAuth({ appId, privateKey });
                const installationAuth = await appAuth({ type: 'installation', installationId });
                const octokit = new Octokit({ auth: installationAuth.token });
                console.log(`${logPrefix} GitHub App authentication successful.`);

                // 3c. Fetch full PR data
                const [owner, repo] = pr.repository_slug.split('/');
                console.log(`${logPrefix} Fetching PR data from GitHub...`);
                const { formattedData, codeChanges } = await fetchAndFormatPRData(
                    octokit,
                    owner,
                    repo,
                    pr.pr_number
                );
                 console.log(`${logPrefix} Successfully fetched PR data.`);

                 // Add check for critical missing data
                 if (!formattedData || !formattedData.prContext || !codeChanges) {
                     throw new Error('Formatted data is missing expected properties after fetch.');
                 }

                // 3d. Run the orchestrator (processMergedPR)
                const pineconeNamespace = `${installationId}-${pr.repository_slug.replace('/', '--')}`; // Construct simplified namespace
                console.log(`${logPrefix} Calling orchestrator (processMergedPR) with namespace ${pineconeNamespace}...`);
                
                // Ensure prContext has required fields if processMergedPR expects them
                const prContextToPass = formattedData.prContext;
                prContextToPass.merged_at = pr.pr_merged_at; // Ensure merged_at is passed
                // Add other necessary fields from `pr` object if needed by processMergedPR/fetchAndFormatPRData's context
                
                // Assign to the outer variable
                analysisResult = await processMergedPR(
                    prContextToPass,
                    codeChanges,
                    formattedData.formattedComments || [],
                    pineconeNamespace,
                    pr.repository_slug,
                    false, // Not a dry run
                    null   // No design doc
                );
                console.log(`${logPrefix} Orchestrator finished. Status: ${analysisResult.status}`);

                // Determine final status based on analysis result
                if (analysisResult.status === 'completed_no_decisions') {
                    prStatus = 'no_decisions';
                    errorMessage = analysisResult.reason || 'No architectural decisions found.';
                } else if (analysisResult.status && (analysisResult.status === 'completed_successfully' || analysisResult.status === 'success')) {
                    prStatus = 'analyzed';
                    errorMessage = analysisResult.reason || analysisResult.errors?.map((e:any) => e.error).join('; ') || null;
                } else {
                    prStatus = 'failed';
                    errorMessage = analysisResult.error || analysisResult.reason || 'Unknown analysis error';
                }

            } catch (error: any) {
                console.error(`${logPrefix} Error during processing:`, error);
                prStatus = 'failed';
                errorMessage = error.message || 'Unknown processing error';
            } finally {
                // 3e. Update final status in Supabase
                console.log(`${logPrefix} Updating final status to '${prStatus}'. Error: ${errorMessage || 'None'}`);

                // Prepare update payload
                const updatePayload: {
                    status: string;
                    analysis_error: string | null;
                    last_analyzed_at?: string; // Add last_analyzed_at
                    extracted_decision_count?: number | null; // Add decision count
                    // retry_attempts?: number; // Keep conditional logic if needed
                } = {
                    status: prStatus,
                    analysis_error: errorMessage,
                    last_analyzed_at: new Date().toISOString(), // Always update analysis time
                };

                // Add extracted_decision_count if analyzed successfully
                if (prStatus === 'analyzed' && analysisResult) { // Check if analysisResult is not null
                    // Use the same logic as analyze-repository API for count
                    updatePayload.extracted_decision_count = analysisResult.decisions_processed !== undefined
                        ? analysisResult.decisions_processed
                        : (analysisResult.decisions?.length || 0);
                } else if (prStatus === 'failed') {
                    // Optionally handle retry attempts incrementing here
                    // updatePayload.retry_attempts = (pr.retry_attempts || 0) + 1;
                    updatePayload.extracted_decision_count = null; // Ensure count is null on failure
                } else {
                     updatePayload.extracted_decision_count = null; // Set to null for other statuses like 'no_decisions'
                }

                const { error: finalUpdateError } = await supabaseClient
                    .from('repository_pr_analysis_status')
                    .update(updatePayload) // Use the prepared payload
                    .eq('repository_slug', pr.repository_slug)
                    .eq('pr_number', pr.pr_number);

                if (finalUpdateError) {
                    console.error(`${logPrefix} CRITICAL: Failed to update final status in Supabase:`, finalUpdateError);
                    // Log this critical failure, but don't let it stop processing other PRs
                    results.push({ pr_number: pr.pr_number, repo: pr.repository_slug, status: 'update_failed', error: finalUpdateError.message });
                } else {
                    results.push({ pr_number: pr.pr_number, repo: pr.repository_slug, status: prStatus, error: errorMessage });
                }
            }
        } // End for loop

        console.log('[Cron Job] Finished processing batch.', results);
        return NextResponse.json({ success: true, message: `Processed ${pendingPRs.length} PRs.`, results });

    } catch (error: any) {
        console.error('[Cron Job] Unhandled error during run:', error);
        return NextResponse.json({ error: 'Cron job failed', details: error.message || 'Unknown error' }, { status: 500 });
    }
} 