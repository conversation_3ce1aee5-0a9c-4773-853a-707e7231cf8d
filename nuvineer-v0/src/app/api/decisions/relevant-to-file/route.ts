import { NextRequest, NextResponse } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
// Supabase client might be needed later if we want to fetch relationships for the found decisions
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Define interfaces for stronger typing (consistent with by-concepts route)
interface PineconeMatch {
    id: string;
    score?: number;
    values?: number[];
    metadata?: {
        title?: string;
        description?: string;
        implications?: string;
        risks_extracted?: string;
        rationale?: string;
        dev_prompt?: string;
        pr_number?: number | string;
        pr_url?: string;
        domain_concepts?: string[];
        pr_merged_at?: number | string;
        related_files?: string[];
        [key: string]: any;
    };
}

interface DecisionSummary {
    id: string;
    title: string;
    description?: string;
    implications?: string;
    risks_extracted?: string;
    rationale?: string;
    dev_prompt?: string;
    pr_number?: number | string;
    pr_url?: string;
    domain_concepts?: string[];
    pr_merged_at?: number | string;
    related_files?: string[];
}

let pinecone: Pinecone | null = null;
// Store initialized indexes per namespace to avoid re-initializing constantly
const pineconeIndexes: Record<string, any> = {};

// ---> ADDED: Supabase Client Initialization
let supabase: SupabaseClient | null = null;

// Initialize Supabase client (only once)
function initializeSupabaseClient() {
    if (!supabase) {
        try {
            const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
            const supabaseServiceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY');
            // Use service role key for server-side operations
            supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
                auth: {
                    persistSession: false
                }
            });
            console.log("[API RelevantDecisions] Supabase client initialized.");
        } catch (error: any) {
            console.error("[API RelevantDecisions] Failed to initialize Supabase client:", error);
            supabase = null; // Ensure it remains null on error
        }
    }
}
// --- END ADDED ---

// Helper to get environment variables
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            console.warn(`Environment variable ${name} not found, using default value.`);
            return defaultValue;
        } else {
            throw new Error(`Missing required environment variable: ${name}`);
        }
    }
    return value;
}

// Initialize Pinecone client (only once)
async function initializePineconeClient() {
    if (!pinecone) {
        try {
            const apiKey = getEnvVar('PINECONE_API_KEY');
            pinecone = new Pinecone({ apiKey });
            console.log(`[API RelevantDecisions] Pinecone client initialized.`);
        } catch (error: any) {
            console.error("[API RelevantDecisions] Failed to initialize Pinecone client:", error);
            pinecone = null; // Ensure it remains null on error
        }
    }
}

// Get or initialize index for a specific namespace
async function getPineconeIndex(namespace: string) {
    await initializePineconeClient(); // Ensure client is ready
    if (!pinecone) {
        throw new Error("Pinecone client is not initialized.");
    }
    // Use a consistent index handle cache key (namespace might have special chars)
    const indexCacheKey = namespace;
    if (!pineconeIndexes[indexCacheKey]) {
        const indexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');
        // Note: This assumes the index exists. Add validation if needed.
        pineconeIndexes[indexCacheKey] = pinecone.Index(indexName).namespace(namespace);
        console.log(`[API RelevantDecisions] Pinecone index handle initialized for namespace: ${namespace}`);
    }
    return pineconeIndexes[indexCacheKey];
}

export async function POST(request: NextRequest) {
    // ---> Initialize clients if needed
    if (!pinecone) {
        await initializePineconeClient();
        if (!pinecone) {
            console.error('[API RelevantDecisions] Pinecone client failed to initialize. Cannot process request.');
            return NextResponse.json({ error: 'Pinecone client initialization failed' }, { status: 503 });
        }
    }
    if (!supabase) {
        initializeSupabaseClient(); // Initialize Supabase synchronously or handle async if needed elsewhere
        if (!supabase) {
            // Log error but don't necessarily block, maybe relationships are optional?
            // For now, we'll proceed but filtering won't happen.
            console.error('[API RelevantDecisions] Supabase client failed to initialize. Proceeding without relationship filtering.');
        }
    }
    // --- END Client Init ---

    try {
        const body = await request.json();
        const { repoSlug, filePath } = body;

        if (!repoSlug || !filePath) {
            console.warn('[API RelevantDecisions] Bad Request: Missing repoSlug or filePath.');
            return NextResponse.json({ error: 'repoSlug and filePath are required' }, { status: 400 });
        }

        // Construct namespace for public repos: 0-owner--reponame
        // Assuming all requests are for public repos for now.
        const namespace = `0-${repoSlug.replace('/', '--')}`;
        // Need the original slug for Supabase
        const repositorySlugForSupabase = repoSlug;

        console.log(`[API RelevantDecisions] Fetching decisions for repo: ${repoSlug}, file: ${filePath}, namespace: ${namespace}`);

        const index = await getPineconeIndex(namespace);

        // --- Pinecone Query based on related_files metadata ---
        const filterCriteria = {
            $and: [
                { 'is_superseded': false },
                { related_files: { $in: [filePath] } }
            ]
        };
        console.debug(`[API RelevantDecisions] Pinecone filter:`, JSON.stringify(filterCriteria));
        const dimension = 1536; // Example dimension
        const dummyVector = new Array(dimension).fill(0);
        const topK_limit = 100; // Increase limit slightly to ensure we get relevant items before filtering

        const queryResponse = await index.query({
            vector: dummyVector,
            topK: topK_limit,
            filter: filterCriteria,
            includeMetadata: true,
            includeValues: false
        });

        let matches = queryResponse.matches || [];
        console.log(`[API RelevantDecisions] Found ${matches.length} potential decisions referencing file '${filePath}' in namespace '${namespace}'.`);

        if (matches.length === 0) {
            return NextResponse.json({ decisions: [] });
        }

        const decisionSummaries: DecisionSummary[] = matches.map((match: PineconeMatch) => ({
            id: match.id,
            // Extract key metadata fields expected by the webview/UI
            title: match.metadata?.title || 'Untitled Decision',
            description: match.metadata?.description, // Include full description
            implications: match.metadata?.implications, // Include implications
            risks_extracted: match.metadata?.risks_extracted, // Include stringified risks
            rationale: match.metadata?.rationale, // Include rationale as fallback summary?
            dev_prompt: match.metadata?.dev_prompt,

            // Include other potentially useful metadata
            pr_number: match.metadata?.pr_number,
            pr_url: match.metadata?.pr_url,
            domain_concepts: match.metadata?.domain_concepts,
            pr_merged_at: match.metadata?.pr_merged_at, // Include merge time for sorting
            related_files: match.metadata?.related_files,
            // Keep the full metadata for potential future use? Or trim down?
            // metadata: match.metadata // Option to include full metadata
        }));

        // --- ADDED: Sort decisions by pr_merged_at (descending) ---
        const relevantDecisions = decisionSummaries.sort((a: DecisionSummary, b: DecisionSummary) => {
            // Treat null, undefined, or 0 as oldest
            // Also handle cases where pr_merged_at might be a string date that needs parsing, though current logic expects number or parsable string to number
            const timeA = (typeof a.pr_merged_at === 'number' && a.pr_merged_at > 0) ? a.pr_merged_at : ((typeof a.pr_merged_at === 'string' && parseInt(a.pr_merged_at, 10) > 0) ? parseInt(a.pr_merged_at, 10) : 0);
            const timeB = (typeof b.pr_merged_at === 'number' && b.pr_merged_at > 0) ? b.pr_merged_at : ((typeof b.pr_merged_at === 'string' && parseInt(b.pr_merged_at, 10) > 0) ? parseInt(b.pr_merged_at, 10) : 0);
            return timeB - timeA; // Descending order (newest first)
        });
        // --- END ADDED SORTING ---

        return NextResponse.json({ decisions: relevantDecisions });

    } catch (error: any) {
        console.error('[API RelevantDecisions] Error fetching relevant decisions:', error);
        // Distinguish Pinecone errors from others if possible
        const errorMessage = error.message?.includes('Namespace not found')
            ? 'No data found for this repository slug.'
            : 'Internal Server Error';
        return NextResponse.json({ error: errorMessage, details: error.message }, { status: 500 });
    }
} 