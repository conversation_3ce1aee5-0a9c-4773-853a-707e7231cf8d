import { NextResponse, NextRequest } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { createSupabaseServerClient } from '@/lib/supabase-server'; // For potential auth later
import { Octokit } from '@octokit/rest'; // Need Octokit for auth check
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService'; // Import the correct function
import { getRepositoryNamespace } from '@/lib/pinecone-utils'; // Added import

// Helper to get Pinecone config from env
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value && defaultValue === null) {
        console.error(`Missing required environment variable: ${name}`);
        throw new Error(`Missing required environment variable: ${name}`);
    }
    return value || defaultValue!;
}

let pinecone: Pinecone | null = null;
// Store initialized indexes per namespace to avoid re-initializing constantly
const pineconeIndexes: Record<string, any> = {}; 

// Initialize Pinecone client (only once)
async function initializePineconeClient() {
    if (!pinecone) {
        const apiKey = getEnvVar('PINECONE_API_KEY');
        pinecone = new Pinecone({ apiKey });
        console.log(`[API Decisions] Pinecone client initialized.`);
    }
}

// Get or initialize index for a specific namespace
async function getPineconeIndex(namespace: string) {
    await initializePineconeClient(); // Ensure client is ready
    if (!pineconeIndexes[namespace]) {
        const indexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');
        // Note: Add validation if index doesn't exist if needed
        pineconeIndexes[namespace] = pinecone!.Index(indexName).namespace(namespace);
        console.log(`[API Decisions] Pinecone index handle initialized for namespace: ${namespace}`);
    }
    return pineconeIndexes[namespace];
}

// Helper Function to verify user access to an installation ID
// (Can be moved to a shared lib/utils file)
async function verifyUserAccess(userToken: string, targetInstallationId: number): Promise<boolean> {
    try {
        const userOctokit = new Octokit({ auth: userToken });
        const response = await userOctokit.request('GET /user/installations', {
             headers: { 'X-GitHub-Api-Version': '2022-11-28' }
        });
        return response.data.installations.some(inst => inst.id === targetInstallationId);
    } catch (error: any) {
        console.error(`[API Decisions Auth] Error verifying access for installation ${targetInstallationId}:`, error.status, error.message);
        return false; 
    }
}

export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const installationIdStr = searchParams.get('installationId');
    const repositoryFilter = searchParams.get('repository');
    const limit = parseInt(searchParams.get('limit') || '20');
    const prUrlFilter = searchParams.get('pr_url');
    let isPublic = searchParams.get('isPublic') === 'true'; // Check for public flag

    let installationId: number | null = null;
    let pineconeNamespace: string;

    // Validate required repository filter (repositorySlug)
    if (!repositoryFilter) {
        console.error('[API Decisions GET] Missing required query parameter: repository (repositorySlug)');
        return NextResponse.json({ error: 'Missing required query parameter: repository (repositorySlug)' }, { status: 400 });
    }

    // Determine if it's a private or public request based on installationId presence
    if (installationIdStr) {
        // --- Private Repository Logic --- 
        console.log(`[API Decisions GET] Private request detected for repo: ${repositoryFilter}, installation: ${installationIdStr}`);
        isPublic = false; // Ensure isPublic is false if installationId is provided
        installationId = parseInt(installationIdStr, 10);
        if (isNaN(installationId) || installationId < 0) { // Allow 0, but it should have come without installationIdStr if public
            return NextResponse.json({ error: 'Invalid installationId format. Must be a non-negative number.' }, { status: 400 });
        }
        if (installationId === 0) {
            // If installationId=0 is passed explicitly, treat it as public, but log a warning as it's unusual for this path.
            console.warn(`[API Decisions GET] installationId=0 passed explicitly for repo: ${repositoryFilter}. Treating as public.`);
            isPublic = true;
        }

        if (!isPublic) {
            // 1. Check Authentication & Get User Token for Private Access
            const supabase = createSupabaseServerClient();
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError || !session || !session.provider_token) {
                const message = !session?.provider_token ? 'GitHub token not available for private repository access.' : 'Authentication required for private repository access.';
                const status = !session?.provider_token ? 403 : 401;
                console.warn(`[API Decisions GET Auth] Failed private access for repo ${repositoryFilter} (install ${installationId}): ${message}`);
                return NextResponse.json({ error: message }, { status });
            }
            console.log(`[API Decisions GET Auth] User ${session.user.id} authenticated for private access check.`);

            // 2. Check Authorization using User Token for Private Access
            const isAuthorized = await verifyUserAccess(session.provider_token, installationId!);
            if (!isAuthorized) {
                console.warn(`[API Decisions GET Auth] User ${session.user.id} unauthorized for installation ${installationId} via GitHub token check.`);
                return NextResponse.json({ error: 'Forbidden - User does not have access to this installation.' }, { status: 403 });
            }
            console.log(`[API Decisions GET Auth] User ${session.user.id} authorized for installation ${installationId}.`);
        }
        // 3. Set Pinecone Namespace for Private Repo (or explicitly public if installationId=0)
        pineconeNamespace = getRepositoryNamespace(installationId, repositoryFilter);

    } else {
        // --- Public Repository Logic --- 
        console.log(`[API Decisions GET] Public request detected for repo: ${repositoryFilter}`);
        isPublic = true;
        installationId = 0; 
        pineconeNamespace = getRepositoryNamespace(installationId, repositoryFilter);
    }

    // 4. Proceed with Pinecone Query using the determined namespace
    console.log(`[API Decisions] Using Pinecone namespace: ${pineconeNamespace}`);

    try {
        const index = await getPineconeIndex(pineconeNamespace);

        // Base filter to exclude superseded decisions
        const baseFilter = { 'is_superseded': false };
        let finalFilter: any = baseFilter;

        if (prUrlFilter) {
            console.log(`[API Decisions] Applying PR URL filter: ${prUrlFilter}`);
            finalFilter = { $and: [baseFilter, { pr_url: prUrlFilter }] };
        }
        
        console.log(`[API Decisions] Querying Pinecone namespace '${pineconeNamespace}' with filter:`, JSON.stringify(finalFilter));

        const dummyVector = new Array(1536).fill(0); // Assuming OpenAI embedding size

        const queryResponse = await index.query({
            vector: dummyVector,
            topK: limit,
            filter: finalFilter,
            includeMetadata: true,
            includeValues: false,
        });

        const decisions = queryResponse.matches.map((match: any) => ({
            id: match.id,
            score: match.score, // This score is relative to the dummy vector, likely not useful
            metadata: match.metadata,
        }));

        console.log(`[API Decisions] Found ${decisions.length} decisions in namespace ${pineconeNamespace}.`);
        return NextResponse.json({ success: true, decisions });

    } catch (error: any) {
        console.error(`[API Decisions] Error querying Pinecone namespace ${pineconeNamespace}:`, error);
        // Distinguish Pinecone errors from others if possible
        const errorMessage = error.message?.includes('Namespace not found') 
            ? 'No data found for this installation.' 
            : 'Failed to fetch decisions from Pinecone.';
        return NextResponse.json({ error: errorMessage, details: error.message }, { status: 500 });
    }
}

// New POST handler for aggregated domain concepts
export async function POST(request: NextRequest) {
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!apiKey) {
        return NextResponse.json({ error: 'Missing API Key in Authorization header (Bearer token)' }, { status: 401 });
    }

    let body: { repository_slug: string; installation_id: string | number }; // Added installation_id
    try {
        body = await request.json();
    } catch (error) {
        return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const { repository_slug, installation_id } = body; // Destructure installation_id
    if (!repository_slug || installation_id === undefined) { // Check repository_slug and installation_id
        return NextResponse.json({ error: 'Missing required body parameters: repository_slug (string), installation_id (string or number)' }, { status: 400 });
    }

    // Validate installation_id format (allow number 0 or string "0")
    const numericInstallationId = typeof installation_id === 'string' ? parseInt(installation_id, 10) : installation_id;
    if (isNaN(numericInstallationId) || numericInstallationId < 0) {
        return NextResponse.json({ error: 'Invalid installation_id format. Must be a non-negative integer.' }, { status: 400 });
    }

    console.log(`[API Decisions POST] Request received for aggregated concepts. Repo: ${repository_slug}, InstallID: ${numericInstallationId}`);

    try {
        // 2. Validate API Key & Get User ID
        const userId = await validateApiKeyAndGetUser(apiKey);
        if (!userId) {
            console.warn(`[API Decisions POST] Invalid API Key provided.`);
            return NextResponse.json({ error: 'Invalid API Key' }, { status: 401 });
        }
        console.log(`[API Decisions POST] API Key validated. User ID: ${userId}`);

        // 3. Determine Pinecone Namespace
        // No direct user session assumed for API key access to installations, 
        // rely on caller to manage authorization for installationId if needed elsewhere.
        // Here, we just use it to construct the namespace.
        const pineconeNamespace = getRepositoryNamespace(numericInstallationId, repository_slug);
        console.log(`[API Decisions POST] Using Pinecone namespace: ${pineconeNamespace} for aggregated concepts.`);

        // 4. Query Pinecone (Example: Fetching concepts by querying all non-superseded docs)
        // This is a placeholder for actual aggregation logic. You'll likely want to
        // fetch a sample of documents and aggregate their 'domain_concepts' metadata.
        const index = await getPineconeIndex(pineconeNamespace);
        const dummyVector = new Array(1536).fill(0); // Assuming OpenAI embedding size

        const queryResponse = await index.query({
            vector: dummyVector,
            topK: 200, // Example: sample top 200 docs for concepts
            filter: { 'is_superseded': false }, // <--- Ensure superseded are excluded
            includeMetadata: true,
            includeValues: false,
        });

        const allConcepts = new Set<string>();
        queryResponse.matches.forEach((match: any) => {
            if (match.metadata && Array.isArray(match.metadata.domain_concepts)) {
                match.metadata.domain_concepts.forEach((concept: string) => {
                    if (typeof concept === 'string' && concept.trim()) { // Ensure it's a valid string
                        allConcepts.add(concept);
                    }
                });
            }
        });

        // Convert to array format [{ concept: string, count: number }]
        const aggregatedConcepts = Array.from(allConcepts).map(concept => ({
            concept,
            count: 1, // Placeholder count, actual aggregation logic needed
        }));

        console.log(`[API Decisions POST] Aggregated ${aggregatedConcepts.length} unique domain concepts.`);

        return NextResponse.json({ success: true, aggregated_concepts: aggregatedConcepts });

    } catch (error: any) {
        console.error(`[API Decisions POST] Error processing request for repo ${repository_slug}:`, error);
        const errorMessage = error.message?.includes('Namespace not found')
            ? 'No data found for this repository/installation.'
            : 'Failed to process request.';
        return NextResponse.json({ error: errorMessage, details: error.message }, { status: 500 });
    }
} 