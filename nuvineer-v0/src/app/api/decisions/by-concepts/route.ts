import { NextRequest, NextResponse } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService'; // Import the correct function
import { getRepositoryNamespace } from '@/lib/pinecone-utils'; // Added import

// Define interfaces for stronger typing
interface PineconeMatch {
    id: string;
    score?: number; // Optional, as it's not directly used but common in Pinecone responses
    values?: number[]; // Optional
    metadata?: {
        title?: string;
        description?: string;
        implications?: string;
        risks_extracted?: string; // Assuming string array based on typical usage
        rationale?: string;
        dev_prompt?: string;
        pr_number?: number | string;
        pr_url?: string;
        domain_concepts?: string[]; // Assuming string array
        pr_merged_at?: number | string; // Could be string from metadata, then parsed
        related_files?: string[]; // Assuming string array
        [key: string]: any; // Allow other metadata properties
    };
}

interface DecisionSummary {
    id: string;
    title: string;
    description?: string;
    implications?: string;
    risks_extracted?: string;
    rationale?: string;
    dev_prompt?: string;
    pr_number?: number | string;
    pr_url?: string;
    domain_concepts?: string[];
    pr_merged_at?: number | string; // Still string here, as it's direct from metadata or default
    related_files?: string[];
}

// Environment variable helper
function getEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value) {
        if (defaultValue !== null) {
            console.warn(`Environment variable ${name} not found, using default value.`);
            return defaultValue;
        } else {
            throw new Error(`Missing required environment variable: ${name}`);
        }
    }
    return value;
}

// --- Pinecone Client Initialization ---
let pinecone: Pinecone | null = null;
const pineconeIndexes: Record<string, any> = {};

async function initializePineconeClient() {
    if (!pinecone) {
        try {
            const apiKey = getEnvVar('PINECONE_API_KEY');
            pinecone = new Pinecone({ apiKey });
            console.log(`[API ByConcepts] Pinecone client initialized.`);
        } catch (error: any) {
            console.error("[API ByConcepts] Failed to initialize Pinecone client:", error);
            pinecone = null;
        }
    }
}

async function getPineconeIndex(namespace: string) {
    await initializePineconeClient();
    if (!pinecone) {
        throw new Error("Pinecone client is not initialized.");
    }
    const indexCacheKey = namespace;
    if (!pineconeIndexes[indexCacheKey]) {
        const indexName = getEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');
        pineconeIndexes[indexCacheKey] = pinecone.Index(indexName).namespace(namespace);
        console.log(`[API ByConcepts] Pinecone index handle initialized for namespace: ${namespace}`);
    }
    return pineconeIndexes[indexCacheKey];
}

// --- Supabase Client Initialization ---
let supabase: SupabaseClient | null = null;

function initializeSupabaseClient() {
    if (!supabase) {
        try {
            const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
            const supabaseServiceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY');
            supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
                auth: { persistSession: false }
            });
            console.log("[API ByConcepts] Supabase client initialized.");
        } catch (error: any) {
            console.error("[API ByConcepts] Failed to initialize Supabase client:", error);
            supabase = null;
        }
    }
}

// --- POST Handler ---
export async function POST(request: NextRequest) {
    // Initialize clients
    await initializePineconeClient();
    initializeSupabaseClient(); // Can run sync as it's just object creation

    if (!pinecone) {
        console.error('[API ByConcepts] Pinecone client failed to initialize. Cannot process request.');
        return NextResponse.json({ error: 'Pinecone client initialization failed' }, { status: 503 });
    }
    // Supabase initialization failure is handled later (skips filtering)

    // 1. Extract API Key and Body
    const authHeader = request.headers.get('Authorization');
    const apiKey = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!apiKey) {
        return NextResponse.json({ error: 'Missing API Key in Authorization header (Bearer token)' }, { status: 401 });
    }

    let body: { repository_slug: string; domain_concepts: string[]; installation_id: string | number };
    try {
        body = await request.json();
    } catch (error) {
        return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const { repository_slug, domain_concepts, installation_id } = body;
    if (!repository_slug || !Array.isArray(domain_concepts) || domain_concepts.length === 0 || installation_id === undefined) {
        return NextResponse.json({ error: 'Missing or invalid required parameters: repository_slug (string), domain_concepts (non-empty array), installation_id (string or number)' }, { status: 400 });
    }

    // Validate installation_id format (allow number 0 or string "0")
    const numericInstallationId = typeof installation_id === 'string' ? parseInt(installation_id, 10) : installation_id;
    if (isNaN(numericInstallationId) || numericInstallationId < 0) {
        return NextResponse.json({ error: 'Invalid installation_id format. Must be a non-negative integer.' }, { status: 400 });
    }

    console.log(`[API ByConcepts] Request received. Repo: ${repository_slug}, InstallID: ${numericInstallationId}, Concepts: ${domain_concepts.join(', ')}`);

    try {
        // 2. Validate API Key & Get User ID
        const userId = await validateApiKeyAndGetUser(apiKey);
        if (!userId) {
            console.warn(`[API ByConcepts] Invalid API Key provided.`);
            return NextResponse.json({ error: 'Invalid API Key' }, { status: 401 });
        }
        console.log(`[API ByConcepts] API Key validated. User ID: ${userId}`);

        // TODO: Implement repository access validation using userId and repository_slug
        // This might involve querying a 'user_repository_access' table or similar
        // For now, we'll proceed assuming access if the key is valid
        // Replace this placeholder logic with actual access control
        const hasAccess = true; // Placeholder

        if (!hasAccess) {
             console.warn(`[API ByConcepts] User ${userId} does not have access to repo ${repository_slug}.`);
             return NextResponse.json({ error: 'User does not have access to this repository' }, { status: 403 });
        }
        console.log(`[API ByConcepts] User ${userId} validated for repo ${repository_slug}. Install ID: ${numericInstallationId}`);

        // 3. Construct Namespace & Pinecone Query
        const pineconeNamespace = getRepositoryNamespace(numericInstallationId, repository_slug);
        const index = await getPineconeIndex(pineconeNamespace);

        const filterCriteria = {
            $and: [
                { 'is_superseded': false },
                { domain_concepts: { $in: domain_concepts } }
            ]
        };
        console.debug(`[API ByConcepts] Pinecone filter:`, JSON.stringify(filterCriteria));
        const dimension = 1536; // Assuming OpenAI embedding size
        const dummyVector = new Array(dimension).fill(0);
        const topK_limit = 1000; // Fetch more to ensure concepts are found before filtering

        const queryResponse = await index.query({
            vector: dummyVector,
            topK: topK_limit,
            filter: filterCriteria,
            includeMetadata: true,
            includeValues: false
        });

        let matches = queryResponse.matches || [];
        console.log(`[API ByConcepts] Found ${matches.length} potential decisions matching concepts in namespace '${pineconeNamespace}'.`);

        if (matches.length === 0) {
            return NextResponse.json({ decisions: [] });
        }

        // Format active results for the extension
        const decisionSummaries: DecisionSummary[] = matches.map((match: PineconeMatch): DecisionSummary => ({
            id: match.id,
            title: match.metadata?.title || 'Untitled Decision',
            description: match.metadata?.description,
            implications: match.metadata?.implications,
            risks_extracted: match.metadata?.risks_extracted,
            rationale: match.metadata?.rationale,
            dev_prompt: match.metadata?.dev_prompt,
            pr_number: match.metadata?.pr_number,
            pr_url: match.metadata?.pr_url,
            domain_concepts: match.metadata?.domain_concepts,
            pr_merged_at: match.metadata?.pr_merged_at,
            related_files: match.metadata?.related_files,
            // Optional: include full metadata if needed
            // metadata: match.metadata
        }));

        const relevantDecisions = decisionSummaries.sort((a: DecisionSummary, b: DecisionSummary) => {
            const timeA = (typeof a.pr_merged_at === 'number' && a.pr_merged_at > 0) ? a.pr_merged_at : ((typeof a.pr_merged_at === 'string' && parseInt(a.pr_merged_at, 10) > 0) ? parseInt(a.pr_merged_at, 10) : 0);
            const timeB = (typeof b.pr_merged_at === 'number' && b.pr_merged_at > 0) ? b.pr_merged_at : ((typeof b.pr_merged_at === 'string' && parseInt(b.pr_merged_at, 10) > 0) ? parseInt(b.pr_merged_at, 10) : 0);
            return timeB - timeA; // Descending order (most recent first)
        });

        console.log(`[API ByConcepts] Returning ${relevantDecisions.length} decisions.`);
        return NextResponse.json({ success: true, decisions: relevantDecisions });

    } catch (error: any) {
        console.error(`[API ByConcepts] Error processing request for repo ${repository_slug}:`, error);
        const errorMessage = error.message?.includes('Namespace not found')
            ? 'No data found for this repository/installation.'
            : 'Failed to process request.';
         const statusCode = error.message?.includes('Namespace not found') ? 404 : 500;
        return NextResponse.json({ error: errorMessage, details: error.message }, { status: statusCode });
    }
} 