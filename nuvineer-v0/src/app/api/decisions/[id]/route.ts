import { NextRequest, NextResponse } from 'next/server';
import { Pinecone } from '@pinecone-database/pinecone';
import { getRepositoryNamespace } from '@/lib/pinecone-utils'; // Assuming a utility exists

// Initialize Pinecone client (Ensure environment variables are set)
// It's better to initialize this once, potentially in a separate config file or helper
const pinecone = new Pinecone({
    apiKey: process.env.PINECONE_API_KEY!,
    // environment: process.env.PINECONE_ENVIRONMENT! // Might be needed depending on client version
});

// TODO: Select the correct index name
const indexName = process.env.PINECONE_INDEX_NAME || 'your-index-name'; // Replace with your actual index name or fetch from env

export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    const decisionId = params.id;
    const { searchParams } = new URL(request.url);
    const repositorySlug = searchParams.get('repositorySlug');
    const installationIdStr = searchParams.get('installationId'); // <-- Get installationId

    if (!decisionId) {
        return NextResponse.json({ success: false, error: 'Decision ID is required' }, { status: 400 });
    }
    if (!repositorySlug) {
        return NextResponse.json({ success: false, error: 'Repository slug is required' }, { status: 400 });
    }
    if (!installationIdStr) { // <-- Check installationId
        return NextResponse.json({ success: false, error: 'Installation ID is required' }, { status: 400 });
    }
    // Optional: Validate installationId is a number
    const installationId = parseInt(installationIdStr, 10);
    if (isNaN(installationId) || installationId < 0) { // Allow 0, reject negative
         return NextResponse.json({ success: false, error: 'Invalid Installation ID format' }, { status: 400 });
    }
    if (!process.env.PINECONE_API_KEY) {
         console.error('Missing Pinecone API Key environment variable');
        return NextResponse.json({ success: false, error: 'Server configuration error' }, { status: 500 });
    }
     if (!indexName || indexName === 'your-index-name') {
         console.error('Pinecone index name is not configured');
         return NextResponse.json({ success: false, error: 'Server configuration error [index]' }, { status: 500 });
     }

    try {
        const pineconeIndex = pinecone.index(indexName);
        // Pass both installationId and repositorySlug to get the correct namespace
        const namespace = getRepositoryNamespace(installationId, repositorySlug);

        console.log(`Fetching decision ID '${decisionId}' from Pinecone index '${indexName}', namespace '${namespace}'`);

        // Fetch the vector by ID
        const fetchResponse = await pineconeIndex.namespace(namespace).fetch([decisionId]);

        if (!fetchResponse || !fetchResponse.records || !fetchResponse.records[decisionId]) {
            console.log(`Decision ID '${decisionId}' not found in namespace '${namespace}'.`);
            return NextResponse.json({ success: false, error: 'Decision not found' }, { status: 404 });
        }

        const record = fetchResponse.records[decisionId];

        // Assuming the structure stored in Pinecone includes a metadata field
        // Adjust according to your actual data structure
        const decisionData = {
            id: record.id,
            // score: record.score, // Fetch doesn't return score, only query does
            metadata: record.metadata || {}, // Extract metadata
            // values: record.values // Usually not needed for display
        };

        console.log(`Successfully fetched decision ID '${decisionId}'.`);
        return NextResponse.json({ success: true, decision: decisionData });

    } catch (error: any) {
        console.error(`Error fetching decision '${decisionId}' from Pinecone:`, error);
        // Log specific Pinecone errors if available
        if (error.response) {
             console.error('Pinecone error response:', error.response.data);
        }
        return NextResponse.json({ success: false, error: 'Failed to fetch decision from database', details: error.message }, { status: 500 });
    }
} 