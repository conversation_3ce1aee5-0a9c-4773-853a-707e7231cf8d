import { NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { listApiKeys, generateApiKey } from '@/lib/apiKeyService';

const MAX_API_KEYS_PER_USER = 5;

export async function POST() {
  const cookieStore = cookies()

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )

  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return NextResponse.json({ error: 'Failed to get session' }, { status: 500 });
    }

    if (!session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userId = session.user.id;
    const existingKeys = await listApiKeys(userId);

    if (existingKeys.length >= MAX_API_KEYS_PER_USER) {
        return NextResponse.json({ 
            error: `You have reached the maximum limit of ${MAX_API_KEYS_PER_USER} API keys. Please revoke an existing key to generate a new one.` 
        }, { status: 403 });
    }

    // If no keys, or less than max, generate one.
    const newKey = await generateApiKey(userId, 'Wizard-Generated Key');
    return NextResponse.json({ apiKey: newKey.key });

  } catch (error) {
    console.error('Error generating API key:', error);
    return NextResponse.json({ error: 'Failed to generate API key' }, { status: 500 });
  }
} 