import { NextResponse, NextRequest } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Octokit } from '@octokit/rest';
import { getInstallationOctokit, fetchAndFormatPRData } from '@/lib/github';
import { processMergedPR } from '@/orchestrator';
import { Pinecone } from '@pinecone-database/pinecone';
import { createAppAuth } from "@octokit/auth-app";
import { createSupabaseAdminClient } from '@/lib/supabase-server';
import { shouldSkipFileContent } from '@/lib/analysisUtils.js';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';

// Assume Pinecone client/index helpers exist similar to decisions route
// e.g., initializePineconeClient, getPineconeIndex

// --- Pinecone Initialization --- 
let pinecone: Pinecone | null = null;
const pineconeIndexes: Record<string, any> = {};

async function initializePineconeClient() {
    if (!pinecone) {
        try {
            const apiKey = getRequiredEnvVar('PINECONE_API_KEY');
            pinecone = new Pinecone({ apiKey });
            console.log("[API AnalyzeRepo] Pinecone client initialized.");
        } catch (error) {
            console.error("[API AnalyzeRepo] Failed to initialize Pinecone client:", error);
            pinecone = null; // Ensure it's null if initialization fails
        }
    }
    return pinecone;
}

async function getPineconeIndex(namespace: string) {
    const pineconeClient = await initializePineconeClient();
    if (!pineconeClient) { 
        throw new Error("Pinecone client is not initialized.");
    }
    // Use a simple naming convention for the index (adjust if needed)
    const indexName = getRequiredEnvVar('PINECONE_INDEX_NAME', 'archknow-decisions'); 

    if (!pineconeIndexes[namespace]) {
        try {
            console.log(`[API AnalyzeRepo] Initializing Pinecone index handle for index '${indexName}'...`);
            // Assuming the index already exists, we just get a handle
            const index = pineconeClient.index(indexName); 
            pineconeIndexes[namespace] = index; // Store handle (namespace is handled later)
            console.log(`[API AnalyzeRepo] Pinecone index handle '${indexName}' obtained.`);
        } catch (error: unknown) {
            console.error(`[API AnalyzeRepo] Failed to get Pinecone index handle '${indexName}':`, error);
            // Ensure error is an instance of Error before accessing message
            const message = error instanceof Error ? error.message : String(error);
            throw new Error(`Failed to get Pinecone index handle: ${message}`);
        }
    }
    return pineconeIndexes[namespace]; // Return the index handle
}

// --- Environment Variables ---
function getRequiredEnvVar(name: string, defaultValue: string | null = null): string {
    const value = process.env[name];
    if (!value && defaultValue === null) {
        console.error(`Missing required environment variable: ${name}`);
        throw new Error(`Missing required environment variable: ${name}`);
    }
    return value || defaultValue!;
}

const GITHUB_TOKEN = getRequiredEnvVar('GITHUB_TOKEN');

const PINECONE_INDEX_NAME = getRequiredEnvVar('PINECONE_INDEX_NAME', 'architecture-decisions');

// --- Helper to verify user access (RE-ADDED) ---
async function verifyUserAccess(userToken: string, targetInstallationId: number): Promise<boolean> {
     console.log(`[API AnalyzeRepo Auth] Verifying user token access to installation ${targetInstallationId}...`);
     try {
        const userOctokit = new Octokit({ auth: userToken });
        // Use pagination in case the user has access to more than 100 installations
        const installations = await userOctokit.paginate(userOctokit.apps.listInstallationsForAuthenticatedUser, {
             per_page: 100,
             headers: { 'X-GitHub-Api-Version': '2022-11-28' } // Ensure API version consistency
        });
        
        const hasAccess = installations.some(inst => inst.id === targetInstallationId);
        console.log(`[API AnalyzeRepo Auth] User access check result for installation ${targetInstallationId}: ${hasAccess}`);
        return hasAccess;

    } catch (error: any) {
        console.error(`[API AnalyzeRepo Auth] Error verifying access for installation ${targetInstallationId}:`, error.status, error.response?.data?.message || error.message);
        // If the token is invalid (401) or doesn't have permission (403), return false. Otherwise, it might be a temporary issue.
        if (error.status === 401 || error.status === 403) {
             return false;
        }
        // For other errors (e.g., network issues), re-throwing might be appropriate
        // depending on desired behavior, but for now, let's treat it as no access.
        console.warn(`[API AnalyzeRepo Auth] Non-401/403 error during access verification. Treating as no access.`);
        return false; 
    }
}

// Function to calculate total lines changed from codeChanges array
function getTotalLinesChanged(codeChanges: Array<{ patch?: string | null }>): number {
    let totalLines = 0;
    for (const change of codeChanges) {
        if (change.patch) {
            const lines = change.patch.split('\n');
            totalLines += lines.filter(line => (line.startsWith('+') && !line.startsWith('+++')) || (line.startsWith('-') && !line.startsWith('---'))).length;
        }
    }
    return totalLines;
}

// Define types needed (consider moving to a shared types file)
interface AnalysisStatusUpdate {
    status: 'analyzed' | 'failed' | 'skipped' | 'no_decisions' | 'pending';
    last_analyzed_at?: string | null;
    analysis_error?: string | null;
    extracted_decision_count?: number | null;
}

interface AnalysisResultDetail {
    pr_number: number;
    status: 'analyzed' | 'skipped' | 'failed' | 'no_decisions' | 'db_update_failed';
    reason?: string | null;
    error?: string | null;
    decisions?: number;
}

// Type for code changes expected by getTotalLinesChanged
interface CodeChangeForLineCount {
    patch?: string | null;
    filename?: string; // Include filename for potential future use in the function
    // Add other relevant properties if needed based on fetchAndFormatPRData return type
    additions?: number;
    deletions?: number;
}

// ADDED: Type for the return value of processMergedPR
interface ProcessMergedPRResult {
    status?: 'success' | 'failure' | 'completed_no_decisions' | 'completed_successfully' | 'no_decisions' | 'completed_with_errors';
    reason?: string;
    confidence?: number; // For no_decisions status
    decisions_processed?: number; // For success status
    decisions?: any[]; // Array of processed decisions
    decisions_found_before_error?: number; // For failure status
}

export async function POST(request: Request) {
    console.log('[API AnalyzeRepo] Received request');

    // ---- Required inputs ----
    // 1. The repository slug (owner/repo)
    // 2. Whether this is a public or private repository (isPublic flag)
    // 3. For private repos, the installation ID
    // 4. Batch size (how many PRs to analyze in one API call)
    // 5. Maximum number of historical PRs to analyze (new parameter)
    // 6. Optional: sinceDate to filter PRs

    let repositorySlug: string;
    let installationId: number | null = null;
    let batchSize: number;
    let isPublic: boolean = false;
    let sinceDate: string | null = null;
    let maxHistoricalPRs: number = 50; // New parameter with default 50 

    // --- Parse Request Body ---
    try {
        const requestBody = await request.json();
        repositorySlug = requestBody.repositorySlug;
        installationId = requestBody.installationId || null;
        isPublic = requestBody.isPublic || false;
        batchSize = Math.min(requestBody.batchSize || 5, 20); // Default to 5, cap at 20
        sinceDate = requestBody.sinceDate || null;
        
        // Parse and validate maxHistoricalPRs
        if (requestBody.maxHistoricalPRs !== undefined) {
            maxHistoricalPRs = Math.min(Math.max(1, requestBody.maxHistoricalPRs), 50); // Between 1 and 50
        }

        // Test mode flag for analyzing just one PR to validate the process
        const isTestMode = requestBody.testMode === true;
        if (isTestMode) {
            console.log("[API AnalyzeRepo] Running in TEST MODE - will process exactly one PR for validation");
            batchSize = 1; // Override batch size in test mode
        }

        console.log(`[API AnalyzeRepo] Request parameters: repo=${repositorySlug}, isPublic=${isPublic}, installationId=${installationId}, batchSize=${batchSize}, maxHistoricalPRs=${maxHistoricalPRs}, testMode=${isTestMode}`);
        
        // Validate basic inputs
        if (!repositorySlug || !repositorySlug.includes('/')) {
            return NextResponse.json({ error: 'Invalid repository slug. Must be in format "owner/repo".' }, { status: 400 });
        }

        if (isPublic === false && !installationId) {
            return NextResponse.json({ error: 'Installation ID is required for private repository analysis.' }, { status: 400 });
        }
    } catch (err: any) {
        console.error('[API AnalyzeRepo] Error parsing request:', err);
        return NextResponse.json({ error: 'Invalid request format.' }, { status: 400 });
    }

    // --- Initialize Clients ---
    // 1. Supabase client to record analysis status/results
    // 2. Pinecone client for storing extracted decisions
    // 3. GitHub client (Octokit) for accessing PRs and code

    // --- Initialize Supabase Admin Client (bypass RLS) ---
            const cookieStore = cookies();
    const supabaseAdmin = createSupabaseAdminClient();

    // --- Initialize Octokit Client ---
    let octokit: Oc
    tokit;
    try {
        if (isPublic) {
            // For public repos, use GITHUB_TOKEN (likely a PAT)
            const githubToken = getRequiredEnvVar('GITHUB_TOKEN');
            octokit = new Octokit({ auth: githubToken });
            console.log(`[API AnalyzeRepo] Initialized Octokit for public repo using GITHUB_TOKEN.`);

            /* REMOVED GitHub App Auth for public repos
            const githubAppId = getRequiredEnvVar('GITHUB_APP_ID');
            const githubAppPrivateKey = getRequiredEnvVar('GITHUB_APP_PRIVATE_KEY').replace(/\\n/g, '\n');
            octokit = new Octokit({
                authStrategy: createAppAuth,
                auth: {
                    appId: githubAppId,
                    privateKey: githubAppPrivateKey,
                },
            });
            console.log(`[API AnalyzeRepo] Initialized Octokit for public repo via GitHub App`); 
            */
        } else {
            // For private repos, we need the installation token
            if (!installationId) {
                return NextResponse.json({ error: 'Installation ID is required for private repository analysis.' }, { status: 400 });
            }
            octokit = await getInstallationOctokit(installationId);
            console.log(`[API AnalyzeRepo] Initialized Octokit for private repo via installation ${installationId}`);
        }
    } catch (err: any) {
        console.error('[API AnalyzeRepo] Failed to initialize Octokit:', err);
        return NextResponse.json({ error: `Failed to initialize GitHub client: ${err.message}` }, { status: 500 });
    }

    // --- Initialize Pinecone Client ---
    try {
        await initializePineconeClient();
        if (!pinecone) {
            throw new Error('Failed to initialize Pinecone client');
        }
    } catch (err: any) {
        console.error('[API AnalyzeRepo] Failed to initialize Pinecone:', err);
        return NextResponse.json({ error: `Failed to initialize Pinecone client: ${err.message}` }, { status: 500 });
    }

    // --- Define Pinecone Namespace for this analysis run ---
    const pineconeNsId = isPublic ? '0' : (installationId !== null ? installationId.toString() : '0'); // Default to '0' if somehow null and not public
    const pineconeNamespace = getRepositoryNamespace(pineconeNsId, repositorySlug);
    console.log(`[API AnalyzeRepo] Using Pinecone namespace: ${pineconeNamespace} for repository: ${repositorySlug}`);

    // --- Setup PR Analysis Environment ---
    // 1. Check if this is first run for the repo (populate historical PRs if needed)
    // 2. Get pending PRs to analyze based on batch size and previously analyzed status

    // ----- First Run Population Logic -----
    let isFirstRun = false;
    let populatedCount = 0;
    let populationMessage = ''; // Initialize message

    try {
        console.log(`[API AnalyzeRepo Populate Check] Checking existing PR count for ${repositorySlug} / Installation ${installationId}...`);
        
        // Build the query conditionally based on installationId
        let countQuery = supabaseAdmin
            .from('repository_pr_analysis_status')
            .select('pr_number', { count: 'exact', head: true })
            .eq('repository_slug', repositorySlug);

        if (installationId !== null) {
            countQuery = countQuery.eq('installation_id', installationId);
        } else {
            // For public repos (installationId is null), check where DB column IS 0
            countQuery = countQuery.eq('installation_id', 0);
        }

        // Execute the constructed query
        const { count: existingPrCount, error: countError } = await countQuery;

        if (countError) {
            console.error(`[API AnalyzeRepo Populate Check] Error checking existing PR count:`, countError);
            // Provide a more specific error message if possible
            const detailedMessage = countError.message || 'Unknown Supabase query error';
            throw new Error(`Database error checking repository existence: ${detailedMessage}`);
        }

        isFirstRun = (existingPrCount === null || existingPrCount === 0);
        console.log(`[API AnalyzeRepo Populate Check] Existing PR count: ${existingPrCount}. Is first run determination: ${isFirstRun}`);

        if (isFirstRun) {
            console.log(`[API AnalyzeRepo Populate] First run detected. Starting population process...`);
            populationMessage = 'Processing historical PRs...'; // Initial message

            const [owner, repo] = repositorySlug.split('/');
            const listPrParams = {
                    owner: owner,
                    repo: repo,
                    state: 'closed' as const, // Type assertion
                    sort: 'updated' as const,
                    direction: 'desc' as const,
                    per_page: 100,
                };
            console.log(`[API AnalyzeRepo Populate] Fetching closed PRs from GitHub with params:`, listPrParams);

            const allMergedPRsGenerator = octokit.paginate.iterator(
                octokit.pulls.list,
                listPrParams
            );

            const prsToInsert: any[] = [];
            let githubFetchedCount = 0;
            let skippedNotMergedCount = 0;

            for await (const { data: prs } of allMergedPRsGenerator) {
                githubFetchedCount += prs.length;
                console.log(`[API AnalyzeRepo Populate Fetch] Fetched batch of ${prs.length} closed PRs from GitHub (Total fetched so far: ${githubFetchedCount}).`);
                for (const pr of prs) {
                    if (pr.merged_at) { // Only populate merged PRs
                        prsToInsert.push({
                            // Use 0 for installation_id if it's null (public repo)
                            installation_id: installationId ?? 0, 
                            repository_slug: repositorySlug,
                            pr_number: pr.number,
                            pr_title: pr.title.substring(0, 255), // Truncate title if needed
                            pr_url: pr.html_url,
                            pr_created_at: pr.created_at,
                            pr_merged_at: pr.merged_at,
                            status: 'pending', // Start as pending
                        });
                    } else {
                         skippedNotMergedCount++;
                         // Optional: Log skipped PR numbers (can be verbose)
                         // console.log(`[API AnalyzeRepo Populate Fetch] Skipping PR #${pr.number} as it was closed but not merged.`);
                    }
                }
                
                console.log(`[API AnalyzeRepo Populate Fetch] Current batch processed. Merged PRs collected: ${prsToInsert.length}. Skipped (not merged): ${skippedNotMergedCount}.`);

                // Add a safety break if we've reached max historical PRs
                if (prsToInsert.length >= maxHistoricalPRs) {
                    console.log(`[API AnalyzeRepo Populate] Reached max historical PRs limit (${maxHistoricalPRs}). Stopping GitHub PR fetch.`);
                    // Keep only up to maxHistoricalPRs PRs, sorted by merged_at (oldest first for processing)
                    prsToInsert.sort((a, b) => new Date(a.pr_merged_at).getTime() - new Date(b.pr_merged_at).getTime());
                    // Ensure we only keep the *most recent* maxHistoricalPRs if sorting desc by updated
                    // Let's sort by merged_at DESC to keep most recent, then slice
                    prsToInsert.sort((a, b) => new Date(b.pr_merged_at).getTime() - new Date(a.pr_merged_at).getTime());
                    prsToInsert.splice(maxHistoricalPRs); // Keep only the first (most recent) maxHistoricalPRs elements
                     console.log(`[API AnalyzeRepo Populate] Sliced collected PRs to ${prsToInsert.length} (most recent).`);
                    break;
                }
            }

            console.log(`[API AnalyzeRepo Populate] GitHub fetch complete. Total closed PRs fetched: ${githubFetchedCount}. Total merged PRs collected: ${prsToInsert.length}. Total skipped (not merged): ${skippedNotMergedCount}.`);

            if (prsToInsert.length > 0) {
                console.log(`[API AnalyzeRepo Populate DB] Attempting to insert ${prsToInsert.length} merged PRs into repository_pr_analysis_status...`);
                
                // Insert collected PRs.
                const { error: insertError } = await supabaseAdmin
                    .from('repository_pr_analysis_status')
                    .insert(prsToInsert);

                if (insertError) {
                    console.error(`[API AnalyzeRepo Populate DB] Error inserting PRs:`, insertError);
                    populationMessage = `Population Error: Failed during DB insert - ${insertError.message}`;
                    // Populate count remains 0
                } else {
                    populatedCount = prsToInsert.length;
                    populationMessage = `Success: Populated ${populatedCount} historical PRs.`;
                    console.log(`[API AnalyzeRepo Populate DB] Successfully inserted ${populatedCount} PRs.`);
                }
            } else {
                 populationMessage = 'No historical merged PRs found on GitHub to populate.';
                 console.log(`[API AnalyzeRepo Populate] No merged PRs collected from GitHub fetch.`);
            }
        } else {
             console.log(`[API AnalyzeRepo Populate] Not first run. Skipping population.`);
             populationMessage = 'Repository already has analysis history. Skipping population.'; // Provide feedback
        }
    } catch (popError: any) {
        console.error('[API AnalyzeRepo Populate] Error during population phase:', popError.message || popError);
        // Capture the error message
        populationMessage = `Error during population phase check/execution: ${popError.message}`;
    }
    // --- END: First Run Population Logic ---

    // 4. Fetch Pending PRs from Database (This now runs AFTER potential population)
    console.log(`[API AnalyzeRepo] Fetching pending PRs for ${repositorySlug}...`);
    let pendingPRs: Awaited<ReturnType<typeof fetchPendingPRsFromDB>>;
    try {
        const [owner, repo] = repositorySlug.split('/');
        pendingPRs = await fetchPendingPRsFromDB(supabaseAdmin, owner, repo, batchSize * 5, sinceDate);
    } catch (dbError: any) {
         console.error('[API AnalyzeRepo] Error fetching pending PRs via function:', dbError);
         return NextResponse.json({ error: 'Database error fetching PRs', details: dbError.message }, { status: 500 });
    }

    if (!pendingPRs || pendingPRs.length === 0) {
        console.log(`[API AnalyzeRepo] No pending PRs found for ${repositorySlug}.`);
        return NextResponse.json({ message: 'No pending PRs found for analysis.', has_more: false, details: [], processed_count: 0, skipped_count: 0, failed_count: 0 });
    }

    console.log(`[API AnalyzeRepo] Found ${pendingPRs.length} candidate PRs in DB.`);

    // 5. Process PRs in Batch (Now processing in parallel batches of 5)
    const [owner, repo] = repositorySlug.split('/');
    const results: AnalysisResultDetail[] = [];
    let hasMore = false;
    let processedCount = 0;
    let skippedCount = 0;
    let failedCount = 0;
    let analyzedCount = 0;
    let noDecisionsCount = 0; // Add a counter for no_decisions PRs
    let dbUpdateFailedCount = 0;

    // Track which PRs we've processed for decision relationship analysis
    const processedPRsByMergeDate: {prNumber: number, mergeDate: string}[] = [];
    const decisionsForRelationshipAnalysis: any[] = [];
    
    // Process up to batchSize PRs
    const actualBatchSize = Math.min(batchSize, pendingPRs.length);
    const batchesToProcess = Math.min(5, Math.ceil(actualBatchSize / 5)); // Process in batches of 5, up to the requested batch size
    
    console.log(`[API AnalyzeRepo] Processing ${actualBatchSize} PRs in ${batchesToProcess} batches of up to 5 PRs each`);

    // Create batches of up to 5 PRs each
    const prBatches: typeof pendingPRs[] = [];
    for (let i = 0; i < batchesToProcess; i++) {
        const start = i * 5;
        const end = Math.min(start + 5, actualBatchSize);
        prBatches.push(pendingPRs.slice(start, end));
    }

    // Process each batch
    for (let batchIndex = 0; batchIndex < prBatches.length; batchIndex++) {
        const currentBatch = prBatches[batchIndex];
        console.log(`[API AnalyzeRepo] Processing batch ${batchIndex + 1}/${prBatches.length} with ${currentBatch.length} PRs`);
        
        // Process PRs in this batch in parallel
        const batchPromises = currentBatch.map(async (pendingPR) => {
            const currentPrNumber = pendingPR.pr_number;
            console.log(`[API AnalyzeRepo] Processing PR #${currentPrNumber}`);
            
            let currentPrDetails = null;
            let formattedPR = null;
            let prResult: any = { status: 'failure', reason: 'Unknown error occurred' };
            let processingError = null;
            
            try {
                // 1. Fetch PR details from GitHub
                currentPrDetails = await octokit.pulls.get({
                    owner,
                    repo,
                    pull_number: currentPrNumber,
                });

                if (!currentPrDetails || !currentPrDetails.data) {
                    throw new Error(`Failed to fetch PR #${currentPrNumber} details from GitHub`);
                }
                
                // 2. Format PR data for processing
                formattedPR = await fetchAndFormatPRData(octokit, owner, repo, currentPrNumber);
                
                // Skip empty PRs or those without meaningful changes
                if (!formattedPR || !formattedPR.codeChanges || formattedPR.codeChanges.length === 0) {
                    console.log(`[API AnalyzeRepo] Skipping PR #${currentPrNumber}: No files changed.`);
                    prResult = { status: 'skipped', reason: 'No files changed in PR' };
                    skippedCount++;
                    processedCount++;
                    return {
                        prNumber: currentPrNumber,
                        status: 'skipped',
                        reason: 'No files changed in PR',
                        result: prResult,
                        mergeDate: pendingPR.pr_merged_at,
                        processedDecisions: []
                    };
                }
                
                // 3. Process PR with orchestrator, passing the CORRECT namespace
                console.log(`[API AnalyzeRepo] Analyzing PR #${currentPrNumber}...`);
                prResult = await processMergedPR(
                    formattedPR.formattedData?.prContext,
                    formattedPR.codeChanges,
                    formattedPR.formattedData?.formattedComments || [],
                    pineconeNamespace, // Pass the correctly formatted namespace
                    repositorySlug,
                    false,
                    null
                );
                
                // 4. Store PR data for relationship analysis later
                if (prResult.status === 'success' && prResult.decisions && prResult.decisions.length > 0) {
                    const mergeDate = pendingPR.pr_merged_at;
                    return {
                        prNumber: currentPrNumber,
                        status: 'analyzed', 
                        reason: prResult.reason,
                        result: prResult,
                        mergeDate,
                        processedDecisions: prResult.decisions || []
                    };
                }
                
                // Handle other result types
                return {
                    prNumber: currentPrNumber,
                    status: prResult.status === 'success' ? 'analyzed' : 
                           prResult.status === 'no_decisions' || prResult.status === 'completed_no_decisions' ? 'no_decisions' :
                           prResult.status === 'completed_successfully' ? 'analyzed' : 'failed',
                    reason: prResult.reason,
                    result: prResult,
                    mergeDate: pendingPR.pr_merged_at,
                    processedDecisions: []
                };
            } catch (error: any) {
                console.error(`[API AnalyzeRepo] Error processing PR #${currentPrNumber}:`, error);
                processingError = error;
                
                return {
                    prNumber: currentPrNumber,
                    status: 'failed',
                    reason: error.message,
                    result: { status: 'failure', reason: error.message },
                    mergeDate: pendingPR.pr_merged_at,
                    processedDecisions: []
                };
            } finally {
                // Always update DB status
                try {
                    const finalStatus = processingError ? 'failed' : 
                                        prResult.status === 'success' ? 'analyzed' : 
                                        prResult.status === 'completed_successfully' ? 'analyzed' :
                                        prResult.status === 'no_decisions' || prResult.status === 'completed_no_decisions' ? 'no_decisions' : 'failed';
                    
                    await supabaseAdmin.from('repository_pr_analysis_status').upsert({
                        repository_slug: repositorySlug,
                        pr_number: currentPrNumber,
                        // Use 0 for installation_id if it's null (public repo)
                        installation_id: installationId ?? 0, 
                        status: finalStatus,
                        analysis_error: processingError?.message || (finalStatus === 'failed' ? prResult.reason : null),
                        last_analyzed_at: new Date().toISOString(),
                        extracted_decision_count: finalStatus === 'analyzed' ? (prResult.decisions_processed || 0) : null
                    }, { onConflict: 'repository_slug, pr_number, installation_id' }); // Ensure onConflict includes installation_id
                } catch (dbError) {
                    console.error(`[API AnalyzeRepo] Error updating DB status for PR #${currentPrNumber}:`, dbError);
                    // Track DB update failures if needed
                }
            }
        });
        
        // Wait for all PRs in this batch to complete
        const batchResults = await Promise.all(batchPromises);
        
        // Process batch results
        for (const result of batchResults) {
            if (result.status === 'analyzed' || result.status === 'completed_successfully') {
                // Handle both 'analyzed' and 'completed_successfully' statuses as successful analysis
                analyzedCount++;
                processedCount++;
                
                // Fix for undefined decisions count
                const decisionCount = result.result.decisions_processed !== undefined 
                    ? result.result.decisions_processed 
                    : (result.result.decisions?.length || 0);
                    
                results.push({ 
                    pr_number: result.prNumber, 
                    status: 'analyzed', 
                    reason: result.reason, 
                    decisions: decisionCount
                });
                
                // Add to list for relationship analysis
                if (result.processedDecisions.length > 0) {
                    processedPRsByMergeDate.push({
                        prNumber: result.prNumber,
                        mergeDate: result.mergeDate || new Date().toISOString()
                    });
                    decisionsForRelationshipAnalysis.push(...result.processedDecisions);
                }
            } else if (result.status === 'no_decisions' || result.status === 'completed_no_decisions') {
                // Count PRs with no decisions as successfully processed but in a separate category
                noDecisionsCount++;
                processedCount++;
                results.push({ pr_number: result.prNumber, status: 'no_decisions', reason: result.reason });
            } else if (result.status === 'skipped') {
                skippedCount++;
                processedCount++;
                results.push({ pr_number: result.prNumber, status: 'skipped', reason: result.reason });
            } else { // failed
                failedCount++;
                results.push({ pr_number: result.prNumber, status: 'failed', reason: result.reason, error: result.reason });
            }
        }
    }
    
    // After processing all PR batches, perform relationship analysis in chronological order
    if (processedPRsByMergeDate.length > 0 && decisionsForRelationshipAnalysis.length > 0) {
        console.log(`[API AnalyzeRepo] Performing relationship analysis for ${decisionsForRelationshipAnalysis.length} decisions from ${processedPRsByMergeDate.length} PRs`);
        
        // Sort PRs by merge date (oldest first)
        processedPRsByMergeDate.sort((a, b) => 
            new Date(a.mergeDate).getTime() - new Date(b.mergeDate).getTime()
        );
        
        // TODO: Implement relationship analysis in chronological order
        // This is a placeholder - actual implementation would depend on how relationships are stored and processed
        console.log(`[API AnalyzeRepo] Chronological PR order for relationship analysis: ${processedPRsByMergeDate.map(pr => pr.prNumber).join(', ')}`);
        }

        // === Step 5: Check if more pending PRs exist after this batch ===
         // Build the query conditionally based on installationId
        let remainingQuery = supabaseAdmin
             .from('repository_pr_analysis_status')
             .select('*', { count: 'exact', head: true })
             .eq('repository_slug', repositorySlug)
             .eq('status', 'pending'); // Keep the status filter

        if (installationId !== null) {
            remainingQuery = remainingQuery.eq('installation_id', installationId);
        } else {
            // For public repos (installationId is null), check where DB column IS 0
            remainingQuery = remainingQuery.eq('installation_id', 0);
        }

        const { count: remainingPendingCount, error: checkMoreError } = await remainingQuery;

        if (checkMoreError) {
             console.warn(`[API AnalyzeRepo] Error checking for more pending PRs after batch: ${checkMoreError.message}`);
             // Assume there might be more if check fails? Or assume not? Let's assume not for safety.
             hasMore = false;
        } else {
             // Use nullish coalescing to default count to 0 if null/undefined
             const currentRemainingCount = remainingPendingCount ?? 0;
             hasMore = currentRemainingCount > 0;
             console.log(`[API AnalyzeRepo] Remaining pending PRs after batch: ${currentRemainingCount}. Has more: ${hasMore}`);
        }

        // === Step 6: Return Response ===
    const totalProcessedInBatch = analyzedCount + noDecisionsCount + skippedCount + failedCount;
    console.log(`[API AnalyzeRepo] Completed batch analysis. ${totalProcessedInBatch} total PRs processed (${analyzedCount} with decisions, ${noDecisionsCount} without decisions, ${skippedCount} skipped, ${failedCount} failed).`);

    // After processing PRs, the response structure
    let decisions_created_count = 0;
    results.forEach(r => {
        if (r.status === 'analyzed' && r.decisions) {
            decisions_created_count += r.decisions;
        }
    });

    // Update response structure to include decisions_created_count
    return NextResponse.json({
        message: `Analysis batch completed successfully. Processed ${processedCount} PRs with ${analyzedCount} analyzed, ${noDecisionsCount} with no decisions, ${skippedCount} skipped, ${failedCount} failed.`,
        has_more: hasMore,
        details: results,
        processed_count: processedCount,
        analyzed_count: analyzedCount,
        no_decisions_count: noDecisionsCount,
        skipped_count: skippedCount,
        failed_count: failedCount,
        db_update_failed_count: dbUpdateFailedCount,
        decisions_created_count,
        repository_slug: repositorySlug,
        population_message: populationMessage,
        populated_count: populatedCount,
        is_first_run: isFirstRun,
    });
}

// Adjusted to accept and use sinceDate
async function fetchPendingPRsFromDB(
    supabase: any, // Type should be SupabaseClient, but using any for simplicity here
    owner: string,
    repo: string,
    limit: number,
    sinceDate: string | null // <-- ADDED parameter
    // Return type needs to match the loop's expectation
): Promise<Array<{ pr_number: number; status: string; last_analyzed_at: string | null; pr_merged_at: string | null }>> {
    const repoSlug = `${owner}/${repo}`;
    console.log(`[DB Fetch] Fetching pending PRs for ${repoSlug} with limit ${limit}` + (sinceDate ? ` and since ${sinceDate}` : ''));

    try {
        // Calculate a date threshold for retrying failed analyses (e.g., 1 day ago)
        const retryThreshold = new Date();
        retryThreshold.setDate(retryThreshold.getDate() - 1); // Retry failures older than 1 day

        let query = supabase
            .from('repository_pr_analysis_status') // <--- Corrected table name
            // Explicitly list required fields for filtering and return, using alias for pr_number
            .select('pr_number, status, last_analyzed_at, pr_merged_at') // <-- Select fields needed by loop
            .eq('repository_slug', repoSlug)
            // We filter for pending/retryable states here
            .or(`status.eq.pending,and(status.eq.failed,last_analyzed_at.lt.${retryThreshold.toISOString()})`)
            .order('pr_merged_at', { ascending: true, nullsFirst: true }) // Process oldest PRs first
            .limit(limit); // Limit the number fetched

        // ---> ADDED: Apply sinceDate filter if provided <---
        if (sinceDate) {
            try {
                 const validDate = new Date(sinceDate);
                 if (isNaN(validDate.getTime())) {
                    console.warn(`[DB Fetch] Invalid sinceDate provided: ${sinceDate}. Ignoring filter.`);
                 } else {
                    // Apply filter to pr_merged_at column
                    query = query.gte('pr_merged_at', validDate.toISOString());
                    console.log(`[DB Fetch] Applying sinceDate filter: pr_merged_at >= ${validDate.toISOString()}`);
                 }
            } catch (dateError) {
                 console.warn(`[DB Fetch] Error parsing sinceDate '${sinceDate}'. Ignoring filter.`, dateError);
            }
        }
        // ---> END ADDED <---

        const { data, error } = await query;

        if (error) {
            console.error(`[DB Fetch] Error fetching pending PRs for ${repoSlug}:`, error);
            throw error;
        }

        console.log(`[DB Fetch] Found ${data?.length || 0} raw PRs matching criteria for ${repoSlug}.`);

        // Define the type for the PR data coming from Supabase (matching the select)
        type SupabasePRStatus = {
            pr_number: number;
            status: string;
            last_analyzed_at: string | null;
            pr_merged_at: string | null;
        }

        // Filter out PRs that shouldn't be processed (e.g., null merged_at might have slipped through)
        const filteredData = data?.filter((pr: SupabasePRStatus) => {
             const isPending = pr.status === 'pending';
             const isOldFailed = pr.status === 'failed' && (!pr.last_analyzed_at || new Date(pr.last_analyzed_at) < retryThreshold);
             const hasMergedDate = pr.pr_merged_at !== null;

             // Keep if it has a merged date AND (is pending OR is an old failure)
             // This duplicates the .or filter slightly but is safer
             return hasMergedDate && (isPending || isOldFailed);
        }) || [];


        console.log(`[DB Fetch] Returning ${filteredData.length} valid pending/retryable PRs for ${repoSlug}.`);
        // Return the full object as needed by the main loop
        return filteredData;

    } catch (err) {
        console.error(`[DB Fetch] Exception during fetchPendingPRsFromDB for ${repoSlug}:`, err);
        return []; // Return empty array on error
    }
}
