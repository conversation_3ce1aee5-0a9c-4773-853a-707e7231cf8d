import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { callLLM } from '@/lib/llm';

function generateRCAInvestigationPrompt(symptoms: any, repositoryContext: any) {
  return `You are a senior site reliability engineer and software architect investigating a production incident. Your task is to first classify the issue type, then generate targeted investigation strategies.

**INCIDENT SYMPTOMS:**
- Error Pattern: ${symptoms.errorType}
- Affected Components: ${symptoms.affectedComponents?.join(', ') || 'Not specified'}
- User Impact: ${symptoms.userImpact}
- Timing Pattern: ${symptoms.timePattern}
- Environment: ${symptoms.environment}
- Error Logs: ${symptoms.errorLogs || 'Not provided'}
- Affected Files: ${symptoms.affectedFiles?.join(', ') || 'Not specified'}
- Reproduction Steps: ${symptoms.reproductionSteps || 'Not provided'}
- Business Impact: ${symptoms.businessImpact || 'Not specified'}

**REPOSITORY CONTEXT:**
- Repository: ${repositoryContext.slug}
- Recent Activity: Recent architectural decisions and changes in this repository
- Known Architecture: Based on captured architectural decisions

**STEP 1: ISSUE TYPE CLASSIFICATION**

Carefully analyze the provided symptoms to classify this issue. Pay attention to:
- The specific behavior described
- Whether functionality exists but behaves incorrectly vs. doesn't exist at all
- Timeline indicators (when did this start happening)
- Environmental context (where does it happen)

Classify as one of the following types:

1. **REGRESSION** - Something that worked before but is now broken
   - Indicators: "used to work", "worked yesterday", "after deployment", "since update", "broken after", "stopped working"
   - Investigation Focus: Recent changes, deployments, configuration updates
   - Example: "Login worked fine until yesterday's deployment"

2. **MISSING_FEATURE** - Expected functionality that was never implemented
   - Indicators: "doesn't support", "cannot do", "missing ability", "feature request", "would like to", "should be able to"
   - Investigation Focus: Feature design decisions, scope definitions, requirement gaps
   - Example: "Users cannot export data to PDF" (when PDF export was never built)

3. **DESIGN_FLAW** - Functionality exists but was implemented incorrectly from the start
   - Indicators: "always been broken", "never worked properly", "poor performance since launch", "UI doesn't fit", "layout issues", "accessibility problems"
   - Investigation Focus: Original design decisions, architecture choices, implementation patterns
   - Example: "Modal overflows on small screens" (modal exists but responsive design was flawed)

4. **INTEGRATION_ISSUE** - Components work individually but fail when combined
   - Indicators: "works in isolation", "fails when combined", "dependency conflicts", "service A + service B fails"
   - Investigation Focus: Integration patterns, interface decisions, dependency management
   - Example: "Payment works in testing but fails when user service is involved"

5. **ENVIRONMENTAL_ISSUE** - Works in some environments but not others
   - Indicators: "works locally", "only in production", "environment-specific", "different behavior in staging"
   - Investigation Focus: Environment configuration, deployment decisions, infrastructure choices
   - Example: "Database connections fail only in production"

**CRITICAL:** If symptoms are vague or missing, classify as REGRESSION by default and note the lack of information rather than assuming MISSING_FEATURE.

**STEP 2: SYMPTOM ANALYSIS**

Before generating hypotheses, analyze the specific symptoms provided:
- What exactly is the user trying to do?
- What specific behavior are they experiencing?
- Is there any functionality present that's behaving incorrectly?
- Are there any timeline clues about when this started?
- What components or systems are involved?

**STEP 3: CRITICAL INFORMATION ASSESSMENT**

Evaluate what critical information is missing to properly assess this issue. For each category, identify what's missing and why it's important:

**SEVERITY ASSESSMENT:**
- Impact Scope: How many users are affected? (all users, specific user types, percentage?)
- Business Impact: What business processes are disrupted? Revenue impact?
- Frequency: How often does this occur? (always, intermittent, under specific conditions?)
- Workarounds: Are there any existing workarounds users can employ?

**TECHNICAL CONTEXT:**
- Environment Details: Which specific environments show this behavior?
- User Context: Which user roles, permissions, or account types are affected?
- Data Context: What specific data conditions trigger this? (empty states, large datasets, specific values?)
- Browser/Device Context: Which browsers, devices, or platforms exhibit this issue?

**TIMELINE AND CAUSATION:**
- First Occurrence: When was this first reported or noticed?
- Recent Changes: What deployments, configuration changes, or updates happened recently?
- Pattern Analysis: Does this correlate with specific times, events, or user actions?
- Escalation History: How has this issue evolved? Getting worse, staying the same?

**VALIDATION QUESTIONS:**
- Is this actually a problem or expected behavior based on current design?
- Are user expectations aligned with the intended functionality?
- Is this a training/documentation issue rather than a technical issue?
- Should this behavior be considered a feature request instead of a bug?

**STEP 4: TARGETED INVESTIGATION STRATEGY**

Based on your classification and symptom analysis, generate 3-4 hypotheses with type-specific search strategies:

**For REGRESSIONS:**
- Focus on recent decisions and changes
- Look for deployment-related decisions
- Search for configuration modifications
- Examine dependency updates

**For MISSING_FEATURES:**
- Look for scope and requirement decisions
- Search for feature planning discussions
- Examine MVP and phasing decisions
- Look for intentionally deferred features

**For DESIGN_FLAWS:**
- Focus on original architecture decisions
- Look for technology choice rationale
- Search for performance and scalability decisions
- Examine design pattern selections

**For INTEGRATION_ISSUES:**
- Focus on interface and API decisions
- Look for service communication patterns
- Search for dependency management choices
- Examine data flow and protocol decisions

**For ENVIRONMENTAL_ISSUES:**
- Focus on deployment and configuration decisions
- Look for environment-specific settings
- Search for infrastructure architecture choices
- Examine environment parity decisions

**OUTPUT FORMAT:**
{
  "symptom_analysis": {
    "user_action": "What the user is trying to do",
    "observed_behavior": "What actually happens",
    "expected_behavior": "What should happen",
    "affected_functionality": "What specific features/components are involved",
    "timeline_clues": "Any indicators about when this started or how long it's existed"
  },
  "issue_classification": {
    "primary_type": "REGRESSION|MISSING_FEATURE|DESIGN_FLAW|INTEGRATION_ISSUE|ENVIRONMENTAL_ISSUE",
    "confidence": 0.0-1.0,
    "classification_reasoning": "Detailed reasoning based on the specific symptoms analyzed, not generic assumptions"
  },
  "critical_information_gaps": {
    "severity_assessment": {
      "missing_info": ["List of missing severity information"],
      "impact_on_analysis": "How the missing information affects our ability to assess severity",
      "recommended_questions": ["Specific questions to ask stakeholders"]
    },
    "technical_context": {
      "missing_info": ["List of missing technical details"],
      "impact_on_analysis": "How this affects our technical investigation approach",
      "recommended_questions": ["Specific technical questions to investigate"]
    },
    "timeline_causation": {
      "missing_info": ["List of missing timeline/causation information"],
      "impact_on_analysis": "How this affects our ability to identify root cause",
      "recommended_questions": ["Specific timeline questions to clarify"]
    },
    "validation_concerns": {
      "missing_info": ["List of validation gaps"],
      "impact_on_analysis": "How this affects whether we should investigate at all",
      "recommended_questions": ["Questions to validate if this is actually an issue"]
    },
    "overall_confidence": 0.0-1.0,
    "blocking_gaps": ["Critical gaps that prevent meaningful analysis"],
    "investigation_readiness": "READY|NEEDS_MORE_INFO|REQUIRES_VALIDATION"
  },
  "investigation_hypotheses": [
    {
      "hypothesis": "Specific theory about what might be causing the symptoms",
      "likelihood": "HIGH|MEDIUM|LOW",
      "reasoning": "Why this hypothesis fits the symptom pattern and issue type",
      "search_queries": ["query1", "query2", "query3"],
      "expected_decision_patterns": "What types of decisions would support this hypothesis",
      "investigation_approach": "How to investigate this hypothesis given the issue type",
      "confidence_with_current_info": 0.0-1.0
    }
  ],
  "investigation_strategy": "Overall approach tailored to the specific issue type and symptoms",
  "next_steps": "Recommended actions based on information gaps and readiness assessment"
}`;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user is jabubaker (RCA feature restriction)
    const githubId = session.user.user_metadata?.user_name || '';
    if (githubId !== 'jabubaker') {
      return NextResponse.json({ error: 'RCA Analysis feature is currently in beta and restricted to specific users' }, { status: 403 });
    }

    const body = await request.json();
    const { symptoms, repositorySlug, installationId, isPublic } = body;

    if (!symptoms || !repositorySlug) {
      return NextResponse.json({ error: 'Symptoms and repository are required' }, { status: 400 });
    }

    console.log('[RCA Investigation] Analyzing symptoms for repository:', repositorySlug);

    // Generate investigation plan using LLM
    const investigationPrompt = generateRCAInvestigationPrompt(symptoms, {
      slug: repositorySlug
    });

    const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20240620';
    console.log(`[RCA Investigation] Using LLM model: ${model}`);
    const investigationPlan = await callLLM(investigationPrompt, model, false);

    if (!investigationPlan || !investigationPlan.investigation_hypotheses) {
      throw new Error('Failed to generate investigation plan');
    }

    console.log('[RCA Investigation] Generated', investigationPlan.investigation_hypotheses.length, 'hypotheses');

    return NextResponse.json({
      success: true,
      investigationPlan
    });

  } catch (error) {
    console.error('RCA symptom investigation error:', error);
    return NextResponse.json({ 
      error: 'Failed to analyze symptoms',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 