import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { callLLM } from '@/lib/llm';
import { analyzeRelationships } from '../../../../orchestrator.js';

function generateDecisionSuspicionAnalysisPrompt(suspiciousDecisions: any[], symptoms: any, relationshipContext: any) {
  return `You are a forensic software architect analyzing potentially causative architectural decisions for a production incident.

**INCIDENT CONTEXT:**
${JSON.stringify(symptoms, null, 2)}

**POTENTIALLY RELEVANT DECISIONS FOUND:**
${suspiciousDecisions.map((d, i) => `
**Decision ${i+1}:**
- ID: ${d.id}
- Title: ${d.metadata?.title || 'Untitled'}
- Merged: ${d.metadata?.pr_merged_at ? new Date(d.metadata.pr_merged_at).toISOString() : 'Unknown'}
- Confidence: ${d.metadata?.confidence_score || 'Unknown'}
- Follows Standard Practice: ${d.metadata?.follows_standard_practice || 'Unknown'}
- Domain Concepts: ${d.metadata?.domain_concepts?.join(', ') || 'None'}
- Files: ${d.metadata?.related_files?.join(', ') || 'None'}
- Implications: ${d.metadata?.implications || 'None'}
- Rationale: ${d.metadata?.rationale || 'None'}
- Hypothesis Match: ${d.hypothesis_match}
- Search Query: ${d.search_query}
`).join('\n')}

**DECISION RELATIONSHIPS:**
${JSON.stringify(relationshipContext, null, 2)}

**YOUR FORENSIC ANALYSIS:**

1. **CAUSATION LIKELIHOOD**: For each decision, analyze:
   - Could this decision directly cause the observed symptoms?
   - What's the technical mechanism of failure?
   - Does the timing align with symptom onset?
   - Are there obvious failure modes in this approach?

2. **DECISION QUALITY ASSESSMENT**: 
   - Red flags in implementation approach?
   - Risky architectural choices?
   - Insufficient error handling or resilience?
   - Deviation from proven patterns?

3. **INTERACTION EFFECTS**: 
   - How do these decisions interact with each other?
   - Could the combination create unexpected failure modes?
   - Are there cascade failure scenarios?

4. **ROOT CAUSE IDENTIFICATION**:
   - Which decision is most likely the primary root cause?
   - What are contributing factors vs. root causes?
   - What's the failure chain from decision to symptoms?

**OUTPUT FORMAT:**
{
  "forensic_analysis": {
    "primary_root_cause": {
      "decision_id": "string",
      "causation_confidence": 0.0-1.0,
      "failure_mechanism": "Technical explanation of how this decision causes the symptoms",
      "evidence": ["Supporting evidence from decision metadata"],
      "timeline_fit": "How timing aligns with incident"
    },
    "contributing_factors": [
      {
        "decision_id": "string", 
        "contribution_type": "amplifier|enabler|catalyst",
        "explanation": "How this decision contributed to the failure"
      }
    ],
    "failure_chain": "Step-by-step explanation from root cause decision to observed symptoms",
    "alternative_theories": ["Other possible explanations to investigate"]
  }
}`;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user is jabubaker (RCA feature restriction)
    const githubId = session.user.user_metadata?.user_name || '';
    if (githubId !== 'jabubaker') {
      return NextResponse.json({ error: 'RCA Analysis feature is currently in beta and restricted to specific users' }, { status: 403 });
    }

    const body = await request.json();
    const { suspiciousDecisions, symptoms, repositorySlug, installationId, isPublic } = body;

    if (!suspiciousDecisions || !symptoms || !repositorySlug) {
      return NextResponse.json({ error: 'Suspicious decisions, symptoms, and repository are required' }, { status: 400 });
    }

    console.log('[RCA Forensics] Analyzing', suspiciousDecisions.length, 'suspicious decisions');

    // Get relationship context for top suspicious decisions
    let relationshipContext = {};
    if (suspiciousDecisions.length > 0) {
      try {
        const topDecision = suspiciousDecisions[0];
        if (topDecision.metadata) {
          const namespace = `${isPublic ? '0' : installationId}-${repositorySlug.replace('/', '--')}`;
          relationshipContext = await analyzeRelationships(
            {
              id: topDecision.id,
              title: topDecision.metadata.title,
              pr_merged_at: topDecision.metadata.pr_merged_at,
              domain_concepts: topDecision.metadata.domain_concepts,
              related_files: topDecision.metadata.related_files
            },
            namespace,
            { direction: 'backward' }
          );
        }
      } catch (error) {
        console.warn('[RCA Forensics] Failed to get relationship context:', error);
      }
    }

    // Generate forensic analysis using LLM
    const forensicPrompt = generateDecisionSuspicionAnalysisPrompt(
      suspiciousDecisions.slice(0, 10), // Limit to top 10 for prompt size
      symptoms,
      relationshipContext
    );

    const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20240620';
    console.log(`[RCA Forensics] Using LLM model: ${model}`);
    const forensicAnalysis = await callLLM(forensicPrompt, model, false);

    if (!forensicAnalysis || !forensicAnalysis.forensic_analysis) {
      throw new Error('Failed to generate forensic analysis');
    }

    console.log('[RCA Forensics] Analysis completed with confidence:', 
      forensicAnalysis.forensic_analysis.primary_root_cause?.causation_confidence);

    return NextResponse.json({
      success: true,
      forensicAnalysis: forensicAnalysis.forensic_analysis,
      analysisMetadata: {
        decisions_analyzed: suspiciousDecisions.length,
        relationship_context_available: Object.keys(relationshipContext).length > 0,
        analysis_timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('RCA forensic analysis error:', error);
    return NextResponse.json({ 
      error: 'Failed to perform forensic analysis',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 