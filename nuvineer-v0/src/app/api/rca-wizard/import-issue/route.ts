import { NextRequest, NextResponse } from 'next/server';
import { getOctokit } from '@/lib/github-auth';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails } from '@/lib/repository-access';
import { callLLM } from '@/lib/llm';

const GITHUB_ISSUE_URL_REGEX = /https:\/\/github\.com\/([^\/]+)\/([^\/]+)\/issues\/(\d+)/;

function parseIssueUrl(url: string) {
    const match = url.match(GITHUB_ISSUE_URL_REGEX);
    if (!match) {
        return null;
    }
    return {
        owner: match[1],
        repo: match[2],
        issue_number: parseInt(match[3], 10),
    };
}

function generateSymptomExtractionPrompt(issueTitle: string, issueBody: string) {
    return `You are a helpful assistant that extracts structured data from GitHub issues.
    
    GitHub Issue Title: ${issueTitle}
    GitHub Issue Body:
    ---
    ${issueBody}
    ---
    
    Extract the following information and format it as a JSON object:
    - errorType: A brief category for the error (e.g., "API Error", "UI Bug", "Performance Degradation").
    - affectedComponents: An array of software components, features, or services that are affected.
    - userImpact: A description of how this issue impacts users.
    - timePattern: Any information about when the error occurs (e.g., "after deployment", "intermittently", "during peak hours").
    - environment: The environment where the issue is observed (e.g., "Production", "Staging").
    
    JSON output should follow this format:
    {
      "symptoms": {
        "errorType": "string",
        "affectedComponents": ["string"],
        "userImpact": "string",
        "timePattern": "string",
        "environment": "string"
      }
    }`;
}

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const { issueUrl, apiKey, repositorySlug, githubHandle } = body;

        if (!issueUrl || !apiKey || !repositorySlug || !githubHandle) {
            return NextResponse.json({ error: 'issueUrl, apiKey, repositorySlug, and githubHandle are required' }, { status: 400 });
        }

        const user_id = await validateApiKeyAndGetUser(apiKey);
        if (!user_id) {
            return NextResponse.json({ message: "Invalid API key." }, { status: 401 });
        }

        const issueDetails = parseIssueUrl(issueUrl);
        if (!issueDetails) {
            return NextResponse.json({ error: 'Invalid GitHub issue URL' }, { status: 400 });
        }

        const { owner, repo, issue_number } = issueDetails;
        
        const accessDetails = await getRepositoryAccessDetails(user_id, repositorySlug);

        if (!accessDetails) {
            return NextResponse.json({ message: "Failed to retrieve repository access details." }, { status: 500 });
        }

        const octokit = await getOctokit(accessDetails.installationId?.toString());
        if (!octokit) {
            return NextResponse.json({ success: false, error: 'Could not get Octokit instance for the repository' }, { status: 404 });
        }

        const { data: issue } = await octokit.issues.get({
            owner,
            repo,
            issue_number,
        });

        const prompt = generateSymptomExtractionPrompt(issue.title, issue.body || '');
        const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20240620';
        
        const extractedData = await callLLM(prompt, model, false);

        return NextResponse.json(extractedData);

    } catch (error) {
        console.error('Error processing issue for RCA:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        return NextResponse.json({ error: 'Failed to process issue for RCA', details: errorMessage }, { status: 500 });
    }
} 