import { NextRequest, NextResponse } from 'next/server';
import { getRepositoryNamespace } from '@/lib/pinecone-utils';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { queryPinecone } from '@/lib/pineconeUtils';
import { Pinecone } from '@pinecone-database/pinecone';

async function getPineconeIndex(namespace: string) {
  const pinecone = new Pinecone({
    apiKey: process.env.PINECONE_API_KEY!,
  });
  const index = pinecone.index(process.env.PINECONE_INDEX_NAME || 'architecture-decisions');
  return index.namespace(namespace);
}

function generateFocusedQueries(originalQueries: string[], symptoms: any, issueClassification?: any): string[] {
  const focusedQueries: string[] = [];
  
  // Extract key terms from symptoms for context
  const errorType = symptoms?.errorType || '';
  const components = symptoms?.affectedComponents || [];
  const environment = symptoms?.environment || '';
  const timePattern = symptoms?.timePattern || '';
  const issueType = issueClassification?.primary_type || 'REGRESSION'; // Default to regression
  
  // Create issue-type-specific query patterns
  const focusPatterns = getIssueSpecificPatterns(issueType, errorType, components, environment, timePattern);
  
  // Generate focused queries for each original query
  for (const originalQuery of originalQueries) {
    // Clean up the original query - remove common words and focus on key terms
    const cleanQuery = originalQuery
      .replace(/\b(how|why|when|where|what|the|a|an|is|are|was|were|in|on|at|to|for|with|by)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim();
    
    if (cleanQuery.length < 3) continue; // Skip if too short after cleaning
    
    // Apply focus patterns
    for (const pattern of focusPatterns) {
      const focusedQuery = pattern(cleanQuery);
      if (focusedQuery && focusedQuery.length > 10) {
        focusedQueries.push(focusedQuery);
      }
    }
    
    // Also include specific technical terms if present
    const technicalTerms = extractTechnicalTerms(cleanQuery);
    for (const term of technicalTerms) {
      focusedQueries.push(`architectural decision ${term}`);
      focusedQueries.push(`implementation choice ${term}`);
      if (errorType) {
        focusedQueries.push(`${term} causing ${errorType.toLowerCase()}`);
      }
    }
  }
  
  // Remove duplicates and return unique focused queries
  return [...new Set(focusedQueries)].slice(0, 20); // Limit to 20 most focused queries
}

function extractTechnicalTerms(query: string): string[] {
  const technicalPatterns = [
    /\b(api|service|database|cache|queue|auth|payment|user|order|product|inventory)\b/gi,
    /\b(http|rest|graphql|sql|nosql|redis|kafka|rabbitmq|elasticsearch)\b/gi,
    /\b(microservice|monolith|container|kubernetes|docker|aws|gcp|azure)\b/gi,
    /\b(react|vue|angular|node|python|java|go|rust|typescript|javascript)\b/gi,
    /\b(load|performance|scaling|latency|throughput|memory|cpu)\b/gi,
    /\b(security|encryption|oauth|jwt|ssl|tls|cors|csrf)\b/gi,
  ];
  
  const terms: string[] = [];
  for (const pattern of technicalPatterns) {
    const matches = query.match(pattern);
    if (matches) {
      terms.push(...matches.map(m => m.toLowerCase()));
    }
  }
  
  return [...new Set(terms)];
}

function getIssueSpecificPatterns(issueType: string, errorType: string, components: string[], environment: string, timePattern: string) {
  const basePatterns = [
    // Technical implementation patterns
    (query: string) => `architectural decision implementing ${query.toLowerCase()}`,
    (query: string) => `design choice for ${query.toLowerCase()}`,
    (query: string) => `technical approach ${query.toLowerCase()}`,
  ];

  switch (issueType) {
    case 'REGRESSION':
      return [
        ...basePatterns,
        // Regression-specific patterns - focus on recent changes
        (query: string) => `recent change ${query.toLowerCase()}`,
        (query: string) => `deployment affecting ${query.toLowerCase()}`,
        (query: string) => `configuration change ${query.toLowerCase()}`,
        (query: string) => `update causing ${query.toLowerCase()}`,
        (query: string) => timePattern.includes('deployment') ? `deployment decision ${query.toLowerCase()}` : null,
        (query: string) => timePattern.includes('recent') || timePattern.includes('last') ? 
          `recent decision ${query.toLowerCase()}` : null,
        // Error-specific patterns for regressions
        (query: string) => errorType ? `${errorType.toLowerCase()} after ${query.toLowerCase()}` : null,
        (query: string) => errorType ? `${query.toLowerCase()} breaking ${errorType.toLowerCase()}` : null,
      ];

    case 'MISSING_FEATURE':
      return [
        ...basePatterns,
        // Missing feature patterns - focus on scope and requirements
        (query: string) => `feature scope ${query.toLowerCase()}`,
        (query: string) => `requirement decision ${query.toLowerCase()}`,
        (query: string) => `MVP scope ${query.toLowerCase()}`,
        (query: string) => `feature planning ${query.toLowerCase()}`,
        (query: string) => `intentionally excluded ${query.toLowerCase()}`,
        (query: string) => `deferred feature ${query.toLowerCase()}`,
        (query: string) => `not implemented ${query.toLowerCase()}`,
        (query: string) => `phase two ${query.toLowerCase()}`,
      ];

    case 'DESIGN_FLAW':
      return [
        ...basePatterns,
        // Design flaw patterns - focus on original decisions
        (query: string) => `original design ${query.toLowerCase()}`,
        (query: string) => `architecture choice ${query.toLowerCase()}`,
        (query: string) => `design pattern ${query.toLowerCase()}`,
        (query: string) => `technology selection ${query.toLowerCase()}`,
        (query: string) => `implementation approach ${query.toLowerCase()}`,
        (query: string) => `design decision ${query.toLowerCase()}`,
        (query: string) => `architectural pattern ${query.toLowerCase()}`,
        (query: string) => errorType ? `${query.toLowerCase()} design causing ${errorType.toLowerCase()}` : null,
      ];

    case 'INTEGRATION_ISSUE':
      return [
        ...basePatterns,
        // Integration patterns - focus on interfaces and dependencies
        (query: string) => `integration pattern ${query.toLowerCase()}`,
        (query: string) => `interface design ${query.toLowerCase()}`,
        (query: string) => `service communication ${query.toLowerCase()}`,
        (query: string) => `dependency management ${query.toLowerCase()}`,
        (query: string) => `API contract ${query.toLowerCase()}`,
        (query: string) => `data flow ${query.toLowerCase()}`,
        (query: string) => `protocol choice ${query.toLowerCase()}`,
        (query: string) => components.length > 1 ? 
          `${components[0]} ${components[1]} integration ${query.toLowerCase()}` : null,
      ];

    case 'ENVIRONMENTAL_ISSUE':
      return [
        ...basePatterns,
        // Environment patterns - focus on configuration and deployment
        (query: string) => `environment configuration ${query.toLowerCase()}`,
        (query: string) => `deployment setup ${query.toLowerCase()}`,
        (query: string) => `infrastructure decision ${query.toLowerCase()}`,
        (query: string) => `environment parity ${query.toLowerCase()}`,
        (query: string) => environment ? `${environment.toLowerCase()} specific ${query.toLowerCase()}` : null,
        (query: string) => environment ? `${environment.toLowerCase()} configuration ${query.toLowerCase()}` : null,
        (query: string) => `environment difference ${query.toLowerCase()}`,
        (query: string) => `deployment configuration ${query.toLowerCase()}`,
      ];

    default:
      return basePatterns;
  }
}

function generateFocusedComponentQueries(component: string, symptoms: any): string[] {
  const errorType = symptoms?.errorType || '';
  const environment = symptoms?.environment || '';
  const userImpact = symptoms?.userImpact || '';
  const timePattern = symptoms?.timePattern || '';
  
  const focusedQueries: string[] = [];
  
  // Extract component type/category
  const componentType = getComponentType(component);
  
  // Core architectural decision queries
  focusedQueries.push(`architectural decision ${component} ${componentType}`);
  focusedQueries.push(`design pattern ${component} implementation`);
  focusedQueries.push(`technical choice ${component} architecture`);
  
  // Error-specific queries
  if (errorType) {
    focusedQueries.push(`${component} decision causing ${errorType.toLowerCase()}`);
    focusedQueries.push(`${errorType.toLowerCase()} from ${component} architecture`);
    focusedQueries.push(`${component} implementation leading to ${errorType.toLowerCase()}`);
  }
  
  // Impact-specific queries
  if (userImpact) {
    const impactKeywords = extractImpactKeywords(userImpact);
    for (const keyword of impactKeywords) {
      focusedQueries.push(`${component} decision affecting ${keyword}`);
    }
  }
  
  // Environment-specific queries
  if (environment) {
    focusedQueries.push(`${component} ${environment.toLowerCase()} configuration decision`);
    focusedQueries.push(`${environment.toLowerCase()} deployment ${component} architecture`);
  }
  
  // Time-based queries for recent changes
  if (timePattern.includes('recent') || timePattern.includes('last') || timePattern.includes('deployment')) {
    focusedQueries.push(`recent ${component} architectural change`);
    focusedQueries.push(`latest ${component} implementation decision`);
  }
  
  // Component interaction queries
  focusedQueries.push(`${component} integration decision`);
  focusedQueries.push(`${component} interface architecture`);
  focusedQueries.push(`${component} dependency management`);
  
  // Performance and scaling queries
  if (errorType.toLowerCase().includes('performance') || errorType.toLowerCase().includes('slow') || 
      errorType.toLowerCase().includes('timeout') || errorType.toLowerCase().includes('load')) {
    focusedQueries.push(`${component} performance architecture decision`);
    focusedQueries.push(`${component} scaling implementation choice`);
    focusedQueries.push(`${component} optimization decision`);
  }
  
  // Security-related queries
  if (errorType.toLowerCase().includes('auth') || errorType.toLowerCase().includes('security') ||
      errorType.toLowerCase().includes('permission') || errorType.toLowerCase().includes('access')) {
    focusedQueries.push(`${component} security architecture decision`);
    focusedQueries.push(`${component} authentication implementation`);
    focusedQueries.push(`${component} authorization design choice`);
  }
  
  return [...new Set(focusedQueries)].slice(0, 15); // Limit to 15 focused queries per component
}

function getComponentType(component: string): string {
  const lowerComponent = component.toLowerCase();
  
  if (lowerComponent.includes('api') || lowerComponent.includes('service') || lowerComponent.includes('endpoint')) {
    return 'service';
  } else if (lowerComponent.includes('database') || lowerComponent.includes('db') || lowerComponent.includes('storage')) {
    return 'database';
  } else if (lowerComponent.includes('cache') || lowerComponent.includes('redis') || lowerComponent.includes('memcache')) {
    return 'cache';
  } else if (lowerComponent.includes('queue') || lowerComponent.includes('kafka') || lowerComponent.includes('rabbit')) {
    return 'queue';
  } else if (lowerComponent.includes('auth') || lowerComponent.includes('login') || lowerComponent.includes('user')) {
    return 'authentication';
  } else if (lowerComponent.includes('payment') || lowerComponent.includes('billing') || lowerComponent.includes('transaction')) {
    return 'payment';
  } else if (lowerComponent.includes('ui') || lowerComponent.includes('frontend') || lowerComponent.includes('client')) {
    return 'frontend';
  } else if (lowerComponent.includes('gateway') || lowerComponent.includes('proxy') || lowerComponent.includes('load')) {
    return 'gateway';
  }
  
  return 'component';
}

function extractImpactKeywords(userImpact: string): string[] {
  const keywords: string[] = [];
  const lowerImpact = userImpact.toLowerCase();
  
  // Extract action verbs and nouns that indicate user impact
  const impactPatterns = [
    /cannot\s+(\w+)/g,
    /unable\s+to\s+(\w+)/g,
    /failed\s+to\s+(\w+)/g,
    /error\s+when\s+(\w+)/g,
    /(\w+)\s+not\s+working/g,
    /(\w+)\s+broken/g,
    /(\w+)\s+failing/g,
  ];
  
  for (const pattern of impactPatterns) {
    const matches = [...lowerImpact.matchAll(pattern)];
    for (const match of matches) {
      if (match[1] && match[1].length > 2) {
        keywords.push(match[1]);
      }
    }
  }
  
  // Also extract common business process keywords
  const businessKeywords = ['purchase', 'order', 'payment', 'login', 'signup', 'checkout', 'search', 'browse', 'view', 'access', 'download', 'upload'];
  for (const keyword of businessKeywords) {
    if (lowerImpact.includes(keyword)) {
      keywords.push(keyword);
    }
  }
  
  return [...new Set(keywords)];
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user is jabubaker (RCA feature restriction)
    const githubId = session.user.user_metadata?.user_name || '';
    if (githubId !== 'jabubaker') {
      return NextResponse.json({ error: 'RCA Analysis feature is currently in beta and restricted to specific users' }, { status: 403 });
    }

    const body = await request.json();
    const { investigationPlan, symptoms, repositorySlug, installationId, isPublic } = body;

    if (!investigationPlan || !repositorySlug) {
      return NextResponse.json({ error: 'Investigation plan and repository are required' }, { status: 400 });
    }

    console.log('[RCA Discovery] Searching for decisions based on', investigationPlan.investigation_hypotheses.length, 'hypotheses');

    const namespace = getRepositoryNamespace(
      isPublic ? 0 : parseInt(installationId || '0'),
      repositorySlug
    );
    
    const pineconeIndexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';

    console.log('[RCA Discovery] Repository details:');
    console.log('  - repositorySlug:', repositorySlug);
    console.log('  - installationId:', installationId);
    console.log('  - isPublic:', isPublic);
    console.log('  - effectiveInstallationId:', isPublic ? 0 : parseInt(installationId || '0'));
    console.log('  - Pinecone index name:', pineconeIndexName);
    console.log('  - Pinecone namespace:', namespace);

    // Execute searches based on investigation hypotheses with enhanced query generation
    const allSuspiciousDecisions = new Map();
    
    console.log('[RCA Discovery] Starting hypothesis-based semantic search...');
    
    for (const hypothesis of investigationPlan.investigation_hypotheses) {
      console.log(`[RCA Discovery] Processing ${hypothesis.likelihood} likelihood hypothesis:`, hypothesis.hypothesis);
      console.log(`[RCA Discovery] Original search queries:`, hypothesis.search_queries);
      
      // Generate enhanced, focused queries from the original ones
      const enhancedQueries = generateFocusedQueries(hypothesis.search_queries, symptoms, investigationPlan.issue_classification);
      console.log(`[RCA Discovery] Enhanced focused queries:`, enhancedQueries);
      
      // Execute searches for this hypothesis using semantic search
      for (const query of enhancedQueries) {
        try {
          console.log(`[RCA Discovery] Executing semantic search for focused query: "${query}"`);
          
          const searchResults = await queryPinecone(query, namespace, 10, 0.6);
          
          console.log(`[RCA Discovery] Query "${query}" returned ${searchResults.length} matches after score filtering`);
          if (searchResults.length > 0) {
            console.log(`[RCA Discovery] Match IDs:`, searchResults.map(m => m.id));
            console.log(`[RCA Discovery] Match scores:`, searchResults.map(m => m.score));
          }
          
          searchResults.forEach((match: any) => {
            if (!allSuspiciousDecisions.has(match.id)) {
              console.log(`[RCA Discovery] Adding new suspicious decision: ${match.id} (score: ${match.score})`);
              allSuspiciousDecisions.set(match.id, {
                id: match.id,
                metadata: match.metadata,
                hypothesis_match: hypothesis.hypothesis,
                hypothesis_likelihood: hypothesis.likelihood,
                search_query: query,
                score: match.score
              });
            } else {
              console.log(`[RCA Discovery] Decision ${match.id} already found in previous query`);
            }
          });
        } catch (error) {
          console.warn(`[RCA Discovery] Semantic search for query "${query}" failed:`, error);
        }
      }
    }

    // Also search by affected components using semantic search
    if (symptoms?.affectedComponents && symptoms.affectedComponents.length > 0) {
      console.log('[RCA Discovery] Searching by affected components using semantic search:', symptoms.affectedComponents);
      
              for (const component of symptoms.affectedComponents) {
          try {
            // Create focused semantic search queries for the component
            const componentQueries = generateFocusedComponentQueries(component, symptoms);
            
            console.log(`[RCA Discovery] Component "${component}" focused semantic search queries:`, componentQueries);
          
          for (const query of componentQueries) {
            try {
              console.log(`[RCA Discovery] Executing component semantic search: "${query}"`);
              
              const searchResults = await queryPinecone(query, namespace, 5, 0.6); // Lower threshold for components
              
              console.log(`[RCA Discovery] Component query "${query}" returned ${searchResults.length} matches after score filtering`);
              if (searchResults.length > 0) {
                console.log(`[RCA Discovery] Component match IDs:`, searchResults.map(m => m.id));
                console.log(`[RCA Discovery] Component match scores:`, searchResults.map(m => m.score));
              }
              
              searchResults.forEach((match: any) => {
                if (!allSuspiciousDecisions.has(match.id)) {
                  console.log(`[RCA Discovery] Adding decision from component semantic search: ${match.id} (score: ${match.score})`);
                  allSuspiciousDecisions.set(match.id, {
                    id: match.id,
                    metadata: match.metadata,
                    hypothesis_match: `Component: ${component}`,
                    hypothesis_likelihood: 'MEDIUM',
                    search_query: query,
                    score: match.score
                  });
                } else {
                  console.log(`[RCA Discovery] Decision ${match.id} already found in previous search`);
                }
              });
            } catch (error) {
              console.warn(`[RCA Discovery] Component semantic search for query "${query}" failed:`, error);
            }
          }
        } catch (error) {
          console.warn(`[RCA Discovery] Component semantic search for "${component}" failed:`, error);
        }
      }
    }

    const suspiciousDecisions = Array.from(allSuspiciousDecisions.values());
    
    console.log('[RCA Discovery] Final results:');
    console.log(`  - Total unique suspicious decisions found: ${suspiciousDecisions.length}`);
    console.log(`  - Decision IDs:`, suspiciousDecisions.map(d => d.id));
    console.log(`  - Hypothesis matches:`, suspiciousDecisions.map(d => d.hypothesis_match));

    console.log('[RCA Discovery] Analysis completed with', suspiciousDecisions.length, 'potentially related decisions');

    return NextResponse.json({
      success: true,
      suspiciousDecisions: suspiciousDecisions,
      analysisMetadata: {
        hypotheses_analyzed: investigationPlan.investigation_hypotheses.length,
        total_queries: investigationPlan.investigation_hypotheses.reduce((acc: any, h: any) => acc + h.search_queries.length, 0),
        analysis_timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('RCA decision discovery error:', error);
    return NextResponse.json({ 
      error: 'Failed to discover decisions',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 