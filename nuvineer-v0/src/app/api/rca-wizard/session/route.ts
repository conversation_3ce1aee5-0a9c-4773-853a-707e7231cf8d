import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user is jabubaker (RCA feature restriction)
    const githubId = session.user.user_metadata?.user_name || '';
    if (githubId !== 'jabubaker') {
      return NextResponse.json({ error: 'RCA Analysis feature is currently in beta and restricted to specific users' }, { status: 403 });
    }

    const body = await request.json();
    const { repo, owner, installationId } = body;

    if (!repo || !owner) {
      return NextResponse.json({ error: 'Repository and owner are required' }, { status: 400 });
    }

    // Create RCA session in database
    const { data: rcaSession, error } = await supabase
      .from('rca_analysis_sessions')
      .insert({
        repository_slug: `${owner}/${repo}`,
        installation_id: installationId ? parseInt(installationId) : null,
        user_id: session.user.id,
        wizard_state: {
          currentStep: 'symptom-analysis',
          symptoms: {
            errorType: '',
            affectedComponents: [],
            userImpact: '',
            timePattern: '',
            environment: ''
          },
          isLoading: false
        }
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating RCA session:', error);
      return NextResponse.json({ error: 'Failed to create RCA session' }, { status: 500 });
    }

    return NextResponse.json({ 
      sessionId: rcaSession.id,
      repositorySlug: `${owner}/${repo}`,
      installationId 
    });

  } catch (error) {
    console.error('RCA session creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 