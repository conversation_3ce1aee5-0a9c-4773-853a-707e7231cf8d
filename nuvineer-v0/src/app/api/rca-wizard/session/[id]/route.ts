import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user is jabubaker (RCA feature restriction)
    const githubId = session.user.user_metadata?.user_name || '';
    if (githubId !== 'jabubaker') {
      return NextResponse.json({ error: 'RCA Analysis feature is currently in beta and restricted to specific users' }, { status: 403 });
    }

    const { data: rcaSession, error } = await supabase
      .from('rca_analysis_sessions')
      .select('*')
      .eq('id', params.id)
      .eq('user_id', session.user.id)
      .single();

    if (error) {
      console.error('Error fetching RCA session:', error);
      return NextResponse.json({ error: 'RCA session not found' }, { status: 404 });
    }

    return NextResponse.json(rcaSession);

  } catch (error) {
    console.error('RCA session fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user is jabubaker (RCA feature restriction)
    const githubId = session.user.user_metadata?.user_name || '';
    if (githubId !== 'jabubaker') {
      return NextResponse.json({ error: 'RCA Analysis feature is currently in beta and restricted to specific users' }, { status: 403 });
    }

    const body = await request.json();
    const { wizard_state } = body;

    const { data: rcaSession, error } = await supabase
      .from('rca_analysis_sessions')
      .update({
        wizard_state,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .eq('user_id', session.user.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating RCA session:', error);
      return NextResponse.json({ error: 'Failed to update RCA session' }, { status: 500 });
    }

    return NextResponse.json(rcaSession);

  } catch (error) {
    console.error('RCA session update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 