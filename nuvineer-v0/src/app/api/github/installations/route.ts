import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { Octokit } from '@octokit/rest';
// createAppAuth might not be needed here anymore if only using user token

// Helper function to get environment variables
function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    console.error(`Missing required environment variable: ${name}`);
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value;
}

// Helper function to clean the private key
function formatPrivateKey(key?: string): string {
  if (!key) return '';
  return key.replace(/\\n/g, '\n').replace(/^"(.*)"$/, '$1');
}

// Interface for the installation details we want to return
interface InstallationDetails {
    id: number;
    account: {
        login: string;
        avatar_url: string;
        type: string;
    };
    // Add other fields if needed, like app_slug
}

export async function GET(request: Request) {
    // 1. Check Authentication (Get User Session and Token)
    const supabase = createSupabaseServerClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.log('[API Installations] No active session found.');
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }
    // IMPORTANT: We need the GitHub provider token from the session
    if (!session.provider_token) {
        console.warn(`[API Installations] User ${session.user.id} authenticated but provider_token is missing.`);
        // This might happen if the token expired or scopes were insufficient.
        // Prompting re-login might be needed on the frontend.
        return NextResponse.json({ error: 'GitHub token not available. Please re-authenticate.' }, { status: 403 });
    }
    console.log(`[API Installations] User ${session.user.id} authenticated with provider token.`);

    // 2. Fetch installations accessible to the user from GitHub API
    try {
        const userOctokit = new Octokit({ auth: session.provider_token });

        console.log(`[API Installations] Fetching installations accessible to user ${session.user.id} from GitHub...`);
        
        // Use the endpoint for listing installations accessible to the authenticated user
        const installationsResponse = await userOctokit.request('GET /user/installations', {
            headers: {
                'X-GitHub-Api-Version': '2022-11-28'
            }
        });
        
        const accessibleInstallations: InstallationDetails[] = installationsResponse.data.installations.map(inst => {
            let accountLogin = 'Unknown Account';
            let accountAvatarUrl = '';
            let accountType = 'Unknown';

            if (inst.account) {
                // The account can be a User, Organization, or Enterprise according to Octokit types
                // User and Organization have 'login'. Enterprise has 'slug'.
                if ('login' in inst.account && inst.account.login) {
                    accountLogin = inst.account.login;
                } else if ('slug' in inst.account && (inst.account as any).slug) {
                    // If 'login' is not present, it might be an Enterprise account, which has 'slug'
                    accountLogin = (inst.account as any).slug; 
                }

                if (inst.account.avatar_url) {
                    accountAvatarUrl = inst.account.avatar_url;
                }

                // 'type' exists on User/Organization. For Enterprise, we might infer or use a default.
                if ('type' in inst.account && inst.account.type) {
                    accountType = inst.account.type; 
                } else if (!('login' in inst.account) && ('slug'in inst.account)) {
                    accountType = 'Enterprise'; // It's an Enterprise if it has slug but no login
                }
            }

            return {
                id: inst.id,
                account: {
                    login: accountLogin,
                    avatar_url: accountAvatarUrl,
                    type: accountType,
                },
                // inst.app_slug could be useful
            };
        });

        console.log(`[API Installations] Found ${accessibleInstallations.length} installations accessible to user ${session.user.id}.`);
        return NextResponse.json({ success: true, installations: accessibleInstallations });

    } catch (error: any) {
        console.error(`[API Installations] Error fetching user installations from GitHub API for user ${session.user.id}:`, error);
        if (error.status === 401 || error.status === 403) {
            // Token might be invalid or lack necessary scopes
             return NextResponse.json({ error: 'GitHub token invalid or expired. Please re-authenticate.' }, { status: error.status });
        }
        return NextResponse.json({ error: 'Failed to fetch installations from GitHub.', details: error.message }, { status: 500 });
    }
} 