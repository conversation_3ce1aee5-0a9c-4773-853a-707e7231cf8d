import { NextRequest, NextResponse } from 'next/server';
import { Octokit } from '@octokit/rest';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails } from '@/lib/repository-access';
import { getOctokit } from '@/lib/github-auth';

export async function POST(req: NextRequest) {
    console.log('[GitHub Issues API] Request received');
    
    try {
        const body = await req.json();
        const { repository_slug, github_handle, api_key } = body;
        
        console.log('[GitHub Issues API] Request parameters:', {
            repository_slug,
            github_handle,
            api_key: api_key ? `${api_key.substring(0, 8)}...` : 'missing'
        });

        if (!repository_slug || !github_handle || !api_key) {
            console.log('[GitHub Issues API] Missing required parameters');
            return NextResponse.json({ error: 'repository_slug, github_handle, and api_key are required' }, { status: 400 });
        }
        
        console.log('[GitHub Issues API] Validating API key...');
        const user_id = await validateApiKeyAndGetUser(api_key);
        if (!user_id) {
            console.log('[GitHub Issues API] Invalid API key provided');
            return NextResponse.json({ message: "Invalid API key." }, { status: 401 });
        }
        console.log('[GitHub Issues API] API key validated for user:', user_id);

        console.log('[GitHub Issues API] Getting repository access details...');
        const accessDetails = await getRepositoryAccessDetails(user_id, repository_slug);
        console.log('[GitHub Issues API] Repository access details:', accessDetails);

        if (!accessDetails) {
            console.log('[GitHub Issues API] Failed to retrieve repository access details');
            return NextResponse.json({ message: "Failed to retrieve repository access details." }, { status: 500 });
        }

        let installation_id: number | undefined;
        if (typeof accessDetails.installationId === 'undefined') {
            if (accessDetails.type === 'public') {
                installation_id = 0; 
                console.log('[GitHub Issues API] Using public repository access (installation_id = 0)');
            } else {
                console.log('[GitHub Issues API] No installation ID for non-public repository');
                return NextResponse.json({ message: "Failed to determine repository installation ID for non-public repository." }, { status: 500 });
            }
        } else {
            installation_id = accessDetails.installationId;
            console.log('[GitHub Issues API] Using installation ID:', installation_id);
        }

        const [owner, repo] = repository_slug.split('/');
        if (!owner || !repo) {
            console.log('[GitHub Issues API] Invalid repository_slug format:', repository_slug);
            return NextResponse.json({ error: 'Invalid repository_slug format. Expected "owner/repo"' }, { status: 400 });
        }
        console.log('[GitHub Issues API] Parsed repository:', { owner, repo });

        console.log('[GitHub Issues API] Getting Octokit instance...');
        const octokit = await getOctokit(installation_id?.toString());

        if (!octokit) {
            console.log('[GitHub Issues API] Failed to get Octokit instance for installation:', installation_id);
            return NextResponse.json({ error: 'Could not get Octokit instance for the repository' }, { status: 404 });
        }
        console.log('[GitHub Issues API] Octokit instance obtained successfully');

        // Test repository access first
        console.log('[GitHub Issues API] Testing repository access...');
        try {
            const { data: repoData } = await octokit.rest.repos.get({ owner, repo });
            console.log('[GitHub Issues API] Repository access confirmed:', {
                fullName: repoData.full_name,
                private: repoData.private,
                hasIssues: repoData.has_issues,
                openIssuesCount: repoData.open_issues_count
            });
            
            if (!repoData.has_issues) {
                console.log('[GitHub Issues API] Repository has issues disabled');
                return NextResponse.json({ 
                    error: 'Issues are disabled for this repository',
                    issues: []
                });
            }
        } catch (repoError: any) {
            console.error('[GitHub Issues API] Repository access failed:', {
                status: repoError.status,
                message: repoError.message
            });
            return NextResponse.json({ 
                error: `Repository access failed: ${repoError.message}`,
                details: `Status: ${repoError.status}`
            }, { status: repoError.status || 500 });
        }

        console.log('[GitHub Issues API] Fetching all open issues...');
        
        // Fetch all open issues for the repository
        const { data: allIssues } = await octokit.issues.listForRepo({
            owner,
            repo,
            state: 'open',
            per_page: 100, // Get up to 100 issues
        });

        console.log('[GitHub Issues API] All open issues response:', {
            total: allIssues.length,
            issueNumbers: allIssues.map(issue => issue.number)
        });

        // Filter out pull requests - GitHub's API returns both issues and PRs
        const actualIssues = allIssues.filter(issue => !issue.pull_request);

        console.log('[GitHub Issues API] Filtered out pull requests:', {
            totalFetched: allIssues.length,
            actualIssues: actualIssues.length,
            filteredOutPRs: allIssues.length - actualIssues.length
        });

        // Filter issues to separate assigned vs unassigned for logging
        const assignedToUser = actualIssues.filter(issue => 
            issue.assignee && issue.assignee.login === github_handle
        );
        const unassignedIssues = actualIssues.filter(issue => !issue.assignee);
        const otherAssignedIssues = actualIssues.filter(issue => 
            issue.assignee && issue.assignee.login !== github_handle
        );

        console.log('[GitHub Issues API] Issue breakdown:', {
            assignedToUser: assignedToUser.length,
            unassigned: unassignedIssues.length,
            assignedToOthers: otherAssignedIssues.length,
            total: actualIssues.length
        });

        // The API returns the full issue object, but the extension only needs a subset.
        // Let's map it to the GitHubIssue interface defined in the extension.
        const responseIssues = actualIssues.map(issue => ({
            number: issue.number,
            title: issue.title,
            body: issue.body,
            html_url: issue.html_url,
            state: issue.state,
            created_at: issue.created_at,
            assignee: issue.assignee ? { login: issue.assignee.login } : null,
            user: {
                login: issue.user!.login
            }
        }));

        console.log('[GitHub Issues API] Mapped response issues:', {
            count: responseIssues.length,
            issues: responseIssues.map(issue => ({
                number: issue.number,
                title: issue.title,
                assignee: issue.assignee?.login || 'unassigned'
            }))
        });

        console.log('[GitHub Issues API] Successfully returning issues');
        return NextResponse.json(responseIssues);

    } catch (error: any) {
        console.error('[GitHub Issues API] Error fetching GitHub issues:', {
            message: error.message,
            stack: error.stack,
            status: error.status,
            response: error.response?.data
        });
        return NextResponse.json({ error: 'Failed to fetch GitHub issues', details: error.message }, { status: 500 });
    }
} 