import { NextResponse } from 'next/server';
import { Octokit } from '@octokit/rest';
import { createAppAuth } from '@octokit/auth-app';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// NOTE: This file reuses helpers. These should be centralized in a real PR.
function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) throw new Error(`Missing required environment variable: ${name}`);
  return value;
}

async function verifyUserAccess(userToken: string, targetInstallationId: number): Promise<boolean> {
    try {
        const userOctokit = new Octokit({ auth: userToken });
        const response = await userOctokit.request('GET /user/installations', {
             headers: { 'X-GitHub-Api-Version': '2022-11-28' }
        });
        return response.data.installations.some(inst => inst.id === targetInstallationId);
    } catch (error: any) {
        return false; 
    }
}

async function getInstallationOctokit(installationId: number): Promise<Octokit> {
    const appId = getRequiredEnvVar('GITHUB_APP_ID');
    const privateKey = getRequiredEnvVar('GITHUB_APP_PRIVATE_KEY').replace(/\\n/g, '\n');
    const appAuth = createAppAuth({ appId, privateKey });
    const installationAuth = await appAuth({ type: 'installation', installationId });
    return new Octokit({ auth: installationAuth.token });
}


export async function POST(request: Request) {
    const { 
        repositorySlug, 
        installationId, 
        issueNumber, 
        commentBody, 
        labels 
    } = await request.json();

    // --- Validation ---
    if (!repositorySlug || !installationId || !issueNumber || !commentBody || !labels) {
        return NextResponse.json({ error: 'Missing one or more required parameters.' }, { status: 400 });
    }
    const [owner, repo] = repositorySlug.split('/');
    if (!owner || !repo) {
        return NextResponse.json({ error: 'Invalid repositorySlug format.' }, { status: 400 });
    }

    // --- Auth & Authorization ---
    const supabase = createSupabaseServerClient();
    const { data: { session } } = await supabase.auth.getSession();
    if (!session || !session.provider_token) {
        return NextResponse.json({ error: 'Not authenticated or GitHub token not available.' }, { status: 401 });
    }
    const isAuthorized = await verifyUserAccess(session.provider_token, installationId);
    if (!isAuthorized) {
        return NextResponse.json({ error: 'Forbidden - User does not have access to this installation.' }, { status: 403 });
    }

    try {
        const octokit = await getInstallationOctokit(installationId);

        const commentPromise = octokit.issues.createComment({
            owner,
            repo,
            issue_number: issueNumber,
            body: commentBody,
        });

        const labelPromise = octokit.issues.addLabels({
            owner,
            repo,
            issue_number: issueNumber,
            labels: labels,
        });
        
        await Promise.all([commentPromise, labelPromise]);

        return NextResponse.json({ success: true, message: `Issue #${issueNumber} updated successfully.` });

    } catch (error: any) {
        console.error(`[API update-issue] Error updating issue #${issueNumber} in ${repositorySlug}:`, error);
        return NextResponse.json({ error: 'Failed to update issue in GitHub.', details: error.message }, { status: 500 });
    }
} 