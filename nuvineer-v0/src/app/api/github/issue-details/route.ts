import { NextRequest, NextResponse } from 'next/server';
import { getOctokit } from '@/lib/github-auth';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails } from '@/lib/repository-access';

// A simple regex to parse GitHub issue URLs
const GITHUB_ISSUE_URL_REGEX = /https:\/\/github\.com\/([^\/]+)\/([^\/]+)\/issues\/(\d+)/;

// Regex to extract design document session ID from issue content
const DESIGN_DOC_SESSION_ID_REGEX = /https:\/\/archknow\.vercel\.app\/design-doc-wizard\/view\/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i;

/**
 * Parses a GitHub issue URL and returns the owner, repo, and issue number.
 */
function parseIssueUrl(url: string) {
    const match = url.match(GITHUB_ISSUE_URL_REGEX);
    if (!match) {
        return null;
    }
    return {
        owner: match[1],
        repo: match[2],
        issue_number: parseInt(match[3], 10),
    };
}

/**
 * Extracts design document session ID from text content.
 */
function extractDesignSessionId(text: string): string | null {
    const match = text.match(DESIGN_DOC_SESSION_ID_REGEX);
    return match ? match[1] : null;
}

export async function POST(req: NextRequest) {
    console.log('[GitHub Issue Details API] Request received');
    try {
        const body = await req.json();
        const { issueUrl, apiKey } = body;

        console.log(`[GitHub Issue Details API] Processing request for issue: ${issueUrl}`);

        if (!issueUrl || !apiKey) {
            console.log('[GitHub Issue Details API] Missing required fields - issueUrl or apiKey');
            return NextResponse.json({ success: false, error: 'issueUrl and apiKey are required' }, { status: 400 });
        }

        const parsedUrl = parseIssueUrl(issueUrl);
        if (!parsedUrl) {
            console.log(`[GitHub Issue Details API] Invalid URL format: ${issueUrl}`);
            return NextResponse.json({ success: false, error: 'Invalid GitHub issue URL format.' }, { status: 400 });
        }
        const { owner, repo, issue_number } = parsedUrl;
        const repository_slug = `${owner}/${repo}`;

        console.log(`[GitHub Issue Details API] Parsed URL - Owner: ${owner}, Repo: ${repo}, Issue: ${issue_number}`);

        const user_id = await validateApiKeyAndGetUser(apiKey);
        if (!user_id) {
            console.log('[GitHub Issue Details API] API key validation failed');
            return NextResponse.json({ success: false, error: "Invalid API key." }, { status: 401 });
        }

        console.log(`[GitHub Issue Details API] API key validated for user: ${user_id}`);

        const accessDetails = await getRepositoryAccessDetails(user_id, repository_slug);
        if (!accessDetails) {
            console.log(`[GitHub Issue Details API] Failed to get repository access for ${repository_slug}`);
            return NextResponse.json({ success: false, error: "Failed to retrieve repository access details." }, { status: 500 });
        }
        
        console.log(`[GitHub Issue Details API] Repository access confirmed - Installation ID: ${accessDetails.installationId}`);

        const octokit = await getOctokit(accessDetails.installationId?.toString());
        if (!octokit) {
            console.log(`[GitHub Issue Details API] Failed to get Octokit instance for installation: ${accessDetails.installationId}`);
            return NextResponse.json({ success: false, error: 'Could not get Octokit instance for the repository' }, { status: 404 });
        }
        
        console.log(`[GitHub Issue Details API] Fetching issue ${owner}/${repo}#${issue_number}`);
        const { data: issue } = await octokit.issues.get({
            owner,
            repo,
            issue_number,
        });

        console.log(`[GitHub Issue Details API] Issue fetched - Title: "${issue.title}"`);
        console.log(`[GitHub Issue Details API] Issue labels: ${issue.labels.map(label => typeof label === 'string' ? label : label.name).join(', ')}`);

        const isReadyForImplementation = issue.labels.some(
            (label) => typeof label === 'string' ? label === 'ready-for-implementation' : label.name === 'ready-for-implementation'
        );

        if (!isReadyForImplementation) {
            console.log('[GitHub Issue Details API] Issue is not ready for implementation - missing label');
            const responseMessage = `This issue is not marked as 'ready-for-implementation'. Please ensure the label is present. You can get started with ArchKnow to prepare your issue for implementation at https://archknow.vercel.app`;
            return NextResponse.json({ success: true, details: responseMessage });
        }

        console.log(`[GitHub Issue Details API] Issue is ready for implementation. Fetching comments...`);
        const { data: comments } = await octokit.issues.listComments({
            owner,
            repo,
            issue_number,
        });

        console.log(`[GitHub Issue Details API] Fetched ${comments.length} comments for issue ${issue_number}`);

        // Look for specific headers in comments and extract design session ID
        let designDoc = "Design document not found in comments.";
        let implementationPlan = "Implementation plan not found in comments.";
        let designSessionId: string | null = null;

        // First, check the issue body for design session ID
        if (issue.body) {
            designSessionId = extractDesignSessionId(issue.body);
            if (designSessionId) {
                console.log(`[GitHub Issue Details API] Found design session ID in issue body: ${designSessionId}`);
            }
        }

        // Updated regex patterns to match the specific headers
        const designDocHeaderRegex = /Technical Spec generat/i;  // Matches "generated" or "generation"
        const implPlanHeaderRegex = /Execution [Pp]lan generat/i;  // Matches "Plan generated" or "plan generation"

        console.log('[GitHub Issue Details API] Parsing comments for design doc and implementation plan...');

        for (let i = 0; i < comments.length; i++) {
            const comment = comments[i];
            const commentBody = comment.body || '';
            
            console.log(`[GitHub Issue Details API] Comment ${i + 1}: Author: ${comment.user?.login}, Length: ${commentBody.length} chars`);
            
            // Check for design session ID in comment if not found yet
            if (!designSessionId) {
                const sessionIdFromComment = extractDesignSessionId(commentBody);
                if (sessionIdFromComment) {
                    designSessionId = sessionIdFromComment;
                    console.log(`[GitHub Issue Details API] Found design session ID in comment ${i + 1} by ${comment.user?.login}: ${designSessionId}`);
                }
            }
            
            // Check for design document header
            if (commentBody.match(designDocHeaderRegex)) {
                console.log(`[GitHub Issue Details API] Found design document in comment ${i + 1} by ${comment.user?.login}`);
                designDoc = `## Design Document\n\nFound in comment by ${comment.user?.login}:\n\n${commentBody}`;
            }
            
            // Check for implementation plan header
            if (commentBody.match(implPlanHeaderRegex)) {
                console.log(`[GitHub Issue Details API] Found implementation plan in comment ${i + 1} by ${comment.user?.login}`);
                implementationPlan = `## Implementation Plan\n\nFound in comment by ${comment.user?.login}:\n\n${commentBody}`;
            }

            // Log if we found either pattern (for debugging)
            const hasDesignHeader = commentBody.match(designDocHeaderRegex);
            const hasImplHeader = commentBody.match(implPlanHeaderRegex);
            
            if (hasDesignHeader || hasImplHeader) {
                console.log(`[GitHub Issue Details API] Comment ${i + 1} patterns - Design doc header: ${!!hasDesignHeader}, Impl plan header: ${!!hasImplHeader}`);
                // Log a preview of the comment content for debugging
                console.log(`[GitHub Issue Details API] Comment ${i + 1} preview (first 200 chars): ${commentBody.substring(0, 200)}...`);
            }
        }
        
        console.log('[GitHub Issue Details API] Comment parsing complete');
        console.log(`[GitHub Issue Details API] Design doc found: ${designDoc !== "Design document not found in comments."}`);
        console.log(`[GitHub Issue Details API] Implementation plan found: ${implementationPlan !== "Implementation plan not found in comments."}`);
        console.log(`[GitHub Issue Details API] Design session ID found: ${designSessionId || 'None'}`);

        // Log the actual content of design doc and implementation plan
        console.log('\n=== DESIGN DOC CONTENT ===');
        console.log(designDoc);
        console.log('\n=== IMPLEMENTATION PLAN CONTENT ===');
        console.log(implementationPlan);
        console.log('\n=== END CONTENT LOGGING ===\n');

        const details = `
# ${issue.title}

## Issue Description
${issue.body || 'No description provided'}

---

${designDoc}

---

${implementationPlan}
`;

        console.log(`[GitHub Issue Details API] Returning response with details length: ${details.length} chars`);
        return NextResponse.json({ 
            success: true, 
            details,
            designSessionId: designSessionId || undefined
        });

    } catch (error: any) {
        console.error('[GitHub Issue Details API] Error fetching GitHub issue details:', {
            message: error.message,
            stack: error.stack,
            status: error.status,
            response: error.response?.data
        });
        return NextResponse.json({ success: false, error: 'Failed to fetch GitHub issue details', details: error.message }, { status: 500 });
    }
} 