import { NextRequest, NextResponse } from 'next/server';
import { getOctokit } from '@/lib/github-auth';

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const repositorySlug = searchParams.get('repositorySlug');
  const installationIdStr = searchParams.get('installationId');

  if (!repositorySlug || !installationIdStr) {
    return NextResponse.json({ error: 'Repository slug and installation ID are required' }, { status: 400 });
  }

  const [owner, repo] = repositorySlug.split('/');
  if (!owner || !repo) {
    return NextResponse.json({ error: 'Invalid repository slug format' }, { status: 400 });
  }
  
  const installationId = parseInt(installationIdStr, 10);
  if (isNaN(installationId)) {
    return NextResponse.json({ error: 'Invalid installation ID' }, { status: 400 });
  }

  try {
    const octokit = await getOctokit(installationIdStr);
    const { data: languages } = await octokit.repos.listLanguages({
      owner,
      repo,
    });

    if (Object.keys(languages).length === 0) {
      return NextResponse.json({ primaryLanguage: 'Not detected' });
    }

    // Find the language with the most bytes
    const primaryLanguage = Object.keys(languages).reduce((a, b) => languages[a] > languages[b] ? a : b);

    return NextResponse.json({ primaryLanguage });
  } catch (error: any) {
    console.error(`Error fetching languages for ${repositorySlug}:`, error);
    return NextResponse.json({ error: `Failed to fetch repository languages: ${error.message}` }, { status: 500 });
  }
} 