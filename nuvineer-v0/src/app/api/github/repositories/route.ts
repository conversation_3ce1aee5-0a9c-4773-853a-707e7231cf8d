import { NextResponse } from 'next/server';
import { createSupabaseServerClient, createSupabaseAdminClient } from '@/lib/supabase-server';
import { Octokit } from '@octokit/rest';
import { createAppAuth } from '@octokit/auth-app';

// Helper function to get environment variables
function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    console.error(`Missing required environment variable: ${name}`);
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value;
}

// Helper Function to verify user access to an installation ID
async function verifyUserAccess(userToken: string, targetInstallationId: number): Promise<boolean> {
    try {
        const userOctokit = new Octokit({ auth: userToken });
        // Fetch installations accessible to the user
        const response = await userOctokit.request('GET /user/installations', {
             headers: { 'X-GitHub-Api-Version': '2022-11-28' }
        });
        // Check if the target ID is in the user's accessible list
        return response.data.installations.some(inst => inst.id === targetInstallationId);
    } catch (error: any) {
        console.error(`[API Repositories Auth] Error verifying access for installation ${targetInstallationId}:`, error.status, error.message);
        // If token is invalid (401/403) or other error occurs, deny access
        return false; 
    }
}

export async function GET(request: Request) {
    // 1. Check Authentication & Get User Token
    const supabase = createSupabaseServerClient();
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
        return NextResponse.json({ error: 'Not authenticated.' }, { status: 401 });
    }
    console.log(`[API Repositories] User ${session.user.id} authenticated.`);

    const { searchParams } = new URL(request.url);
    const installationIdStr = searchParams.get('installationId');

    if (!installationIdStr) {
        return NextResponse.json({ error: 'Missing required query parameter: installationId' }, { status: 400 });
    }
    const installationId = parseInt(installationIdStr, 10);
     if (isNaN(installationId)) {
         return NextResponse.json({ error: 'Invalid installationId format.' }, { status: 400 });
    }

    // Special case: Handle installationId=0 for public repositories
    if (installationId === 0) {
        console.log('[API Repositories] Handling request for public repositories (installationId=0).');
        try {
            const supabaseAdmin = createSupabaseAdminClient();
            const query = `SELECT repository_slug FROM "repository_loading_jobs" WHERE "installation_id" = 0;`;
            console.log('[API Repositories] Executing Supabase query equivalent to:', query);

            const { data, error } = await supabaseAdmin
                .from('repository_loading_jobs')
                .select('repository_slug')
                .eq('installation_id', 0);

            if (error) {
                console.error('[API Repositories] Supabase error fetching public repos:', error);
                throw error;
            }

            console.log('[API Repositories] Raw data from Supabase:', data);

            // The query returns duplicates, so we need to get unique slugs
            const uniqueSlugs = [...new Set(data.map(item => item.repository_slug as string))];
            console.log('[API Repositories] Unique slugs found:', uniqueSlugs);

            // Format the response to match the expected Repository interface
            const repositories = uniqueSlugs.map((slug, index) => {
                const [owner, name] = slug.split('/');
                return {
                    id: index, // Using index as a mock ID, as we don't have a real one from this query
                    name: name,
                    full_name: slug,
                    private: false, // Public repos are not private
                    html_url: `https://github.com/${slug}`,
                };
            });
            
            console.log(`[API Repositories] Found ${repositories.length} unique public repositories. Sending to client.`);
            return NextResponse.json({ success: true, repositories });

        } catch (error: any) {
            console.error('[API Repositories] Failed to fetch public repositories from DB:', error.message);
            return NextResponse.json({ error: 'Failed to fetch public repositories from database.', details: error.message }, { status: 500 });
        }
    }


    // The rest of the logic is for private repositories (installationId > 0)
    if (!session.provider_token) {
        return NextResponse.json({ error: 'GitHub token not available for private repository access.' }, { status: 403 });
    }

    // 2. Check Authorization using User Token
    const isAuthorized = await verifyUserAccess(session.provider_token, installationId);
    if (!isAuthorized) {
        console.warn(`[API Repositories] User ${session.user.id} unauthorized for installation ${installationId} via GitHub token check.`);
        return NextResponse.json({ error: 'Forbidden - User does not have access to this installation.' }, { status: 403 });
    }
    console.log(`[API Repositories] User ${session.user.id} authorized for installation ${installationId}.`);

    // 3. Generate Installation Token (using App Credentials) & Fetch Repos
    try {
        const appId = getRequiredEnvVar('GITHUB_APP_ID');
        const privateKey = getRequiredEnvVar('GITHUB_APP_PRIVATE_KEY').replace(/\\n/g, '\n');

        const appAuth = createAppAuth({
            appId: appId,
            privateKey: privateKey,
        });

        const installationAuth = await appAuth({
            type: 'installation',
            installationId: installationId,
        });

        const octokit = new Octokit({ auth: installationAuth.token });

        console.log(`[API Repositories] Fetching accessible repositories for installation ${installationId}...`);
        // Use pagination to handle potentially large numbers of repos
        const accessibleRepos = await octokit.paginate(octokit.apps.listReposAccessibleToInstallation, {
             per_page: 100, // Fetch 100 per page
        });

        // Format the response to include only necessary info (e.g., full_name, id)
        const repositories = accessibleRepos.map(repo => ({
            id: repo.id,
            name: repo.name,
            full_name: repo.full_name, // owner/repo
            private: repo.private,
            html_url: repo.html_url,
        }));

        console.log(`[API Repositories] Found ${repositories.length} repositories for installation ${installationId}.`);
        return NextResponse.json({ success: true, repositories });

    } catch (error: any) {
        console.error(`[API Repositories] Error fetching repositories for installation ${installationId}:`, error);
        let status = 500;
        let message = 'Failed to fetch repositories from GitHub.';
        if (error.status === 401 || error.status === 403 || error.status === 404) {
            // 404 can happen if installation ID is invalid/deleted
            status = error.status;
            message = 'Could not authenticate with GitHub or installation not found.';
        }
        return NextResponse.json({ error: message, details: error.message }, { status });
    }
} 