import { NextRequest, NextResponse } from 'next/server';
import { validateApiKeyAndGetUser } from '@/lib/apiKeyService';
import { getRepositoryAccessDetails } from '@/lib/repository-access';
import { getOctokit } from '@/lib/github-auth';

export async function POST(req: NextRequest) {
    console.log('[GitHub Add Comment API] Request received');
    
    try {
        const body = await req.json();
        const { repository_slug, issue_number, comment_body, api_key, labels } = body;
        
        console.log('[GitHub Add Comment API] Request parameters:', {
            repository_slug,
            issue_number,
            comment_body_length: comment_body?.length || 0,
            api_key: api_key ? `${api_key.substring(0, 8)}...` : 'missing',
            labels: labels || 'none'
        });

        if (!repository_slug || !issue_number || !comment_body || !api_key) {
            console.log('[GitHub Add Comment API] Missing required parameters');
            return NextResponse.json({ 
                error: 'repository_slug, issue_number, comment_body, and api_key are required' 
            }, { status: 400 });
        }
        
        console.log('[GitHub Add Comment API] Validating API key...');
        const user_id = await validateApiKeyAndGetUser(api_key);
        if (!user_id) {
            console.log('[GitHub Add Comment API] Invalid API key provided');
            return NextResponse.json({ message: "Invalid API key." }, { status: 401 });
        }
        console.log('[GitHub Add Comment API] API key validated for user:', user_id);

        console.log('[GitHub Add Comment API] Getting repository access details...');
        const accessDetails = await getRepositoryAccessDetails(user_id, repository_slug);
        console.log('[GitHub Add Comment API] Repository access details:', accessDetails);

        if (!accessDetails) {
            console.log('[GitHub Add Comment API] Failed to retrieve repository access details');
            return NextResponse.json({ 
                message: "Failed to retrieve repository access details." 
            }, { status: 500 });
        }

        let installation_id: number | undefined;
        if (typeof accessDetails.installationId === 'undefined') {
            if (accessDetails.type === 'public') {
                installation_id = 0; 
                console.log('[GitHub Add Comment API] Using public repository access (installation_id = 0)');
            } else {
                console.log('[GitHub Add Comment API] No installation ID for non-public repository');
                return NextResponse.json({ 
                    message: "Failed to determine repository installation ID for non-public repository." 
                }, { status: 500 });
            }
        } else {
            installation_id = accessDetails.installationId;
            console.log('[GitHub Add Comment API] Using installation ID:', installation_id);
        }

        const [owner, repo] = repository_slug.split('/');
        if (!owner || !repo) {
            console.log('[GitHub Add Comment API] Invalid repository_slug format:', repository_slug);
            return NextResponse.json({ 
                error: 'Invalid repository_slug format. Expected "owner/repo"' 
            }, { status: 400 });
        }
        console.log('[GitHub Add Comment API] Parsed repository:', { owner, repo });

        console.log('[GitHub Add Comment API] Getting Octokit instance...');
        const octokit = await getOctokit(installation_id?.toString());

        if (!octokit) {
            console.log('[GitHub Add Comment API] Failed to get Octokit instance for installation:', installation_id);
            return NextResponse.json({ 
                error: 'Could not get Octokit instance for the repository' 
            }, { status: 404 });
        }
        console.log('[GitHub Add Comment API] Octokit instance obtained successfully');

        // Verify the issue exists and we have access
        console.log('[GitHub Add Comment API] Verifying issue exists...');
        try {
            const { data: issue } = await octokit.rest.issues.get({
                owner,
                repo,
                issue_number: parseInt(issue_number.toString(), 10)
            });
            
            console.log('[GitHub Add Comment API] Issue verified:', {
                number: issue.number,
                title: issue.title,
                state: issue.state
            });
            
            if (issue.state === 'closed') {
                console.log('[GitHub Add Comment API] Warning: Adding comment to closed issue');
            }
        } catch (issueError: any) {
            console.error('[GitHub Add Comment API] Issue verification failed:', {
                status: issueError.status,
                message: issueError.message
            });
            return NextResponse.json({ 
                error: `Issue verification failed: ${issueError.message}`,
                details: `Status: ${issueError.status}`
            }, { status: issueError.status || 500 });
        }

        // Add the comment to the issue
        console.log('[GitHub Add Comment API] Adding comment to issue...');
        let comment;
        try {
            const { data: commentData } = await octokit.rest.issues.createComment({
                owner,
                repo,
                issue_number: parseInt(issue_number.toString(), 10),
                body: comment_body
            });

            comment = commentData;
            console.log('[GitHub Add Comment API] Comment added successfully:', {
                commentId: comment.id,
                commentUrl: comment.html_url,
                issueNumber: issue_number
            });

        } catch (commentError: any) {
            console.error('[GitHub Add Comment API] Failed to add comment:', {
                status: commentError.status,
                message: commentError.message
            });
            return NextResponse.json({ 
                error: `Failed to add comment: ${commentError.message}`,
                details: `Status: ${commentError.status}`
            }, { status: commentError.status || 500 });
        }

        // Add labels if provided
        let labelsAdded: string[] = [];
        if (labels && Array.isArray(labels) && labels.length > 0) {
            console.log('[GitHub Add Comment API] Adding labels to issue:', labels);
            try {
                // Get current issue labels first
                const { data: currentIssue } = await octokit.rest.issues.get({
                    owner,
                    repo,
                    issue_number: parseInt(issue_number.toString(), 10)
                });

                const currentLabels = (currentIssue.labels as any[]).map((label: any) => 
                    typeof label === 'string' ? label : label.name
                ).filter(Boolean);

                // Only add new labels that don't already exist
                const newLabels = labels.filter((label: string) => !currentLabels.includes(label));
                
                if (newLabels.length > 0) {
                    const { data: updatedIssue } = await octokit.rest.issues.addLabels({
                        owner,
                        repo,
                        issue_number: parseInt(issue_number.toString(), 10),
                        labels: newLabels
                    });

                    labelsAdded = newLabels;
                    console.log('[GitHub Add Comment API] Labels added successfully:', {
                        labelsAdded: newLabels
                    });
                } else {
                    console.log('[GitHub Add Comment API] All requested labels already exist on issue');
                }

            } catch (labelError: any) {
                console.error('[GitHub Add Comment API] Failed to add labels (but comment was added):', {
                    status: labelError.status,
                    message: labelError.message,
                    labels: labels
                });
                // Don't fail the entire request if labels fail - comment was already added successfully
            }
        }

        return NextResponse.json({
            success: true,
            comment: {
                id: comment.id,
                html_url: comment.html_url,
                body: comment.body,
                created_at: comment.created_at,
                user: comment.user ? {
                    login: comment.user.login,
                    html_url: comment.user.html_url
                } : null
            },
            labels_added: labelsAdded,
            message: `Comment added successfully to GitHub issue${labelsAdded.length > 0 ? ` with labels: ${labelsAdded.join(', ')}` : ''}`
        });

    } catch (error: any) {
        console.error('[GitHub Add Comment API] Unexpected error:', {
            message: error.message,
            stack: error.stack,
            status: error.status
        });
        return NextResponse.json({ 
            error: 'Failed to add comment to GitHub issue', 
            details: error.message 
        }, { status: 500 });
    }
} 