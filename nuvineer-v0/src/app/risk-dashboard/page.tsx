'use client';

import React, { useState, useEffect, useCallback, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { getWeek, format } from 'date-fns'; // Added format
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart, // Use AreaChart for stacked view
  Area
} from 'recharts';

// Simplified Decision structure for this page
interface Decision {
  id: string;
  metadata: { 
    pr_number?: number;
    pr_merged_at?: string; 
    risks_extracted?: string | any[]; // Can be stringified JSON or array
    title?: string;
    pr_url?: string;
    // Add other metadata fields if needed for display
  };
}

// Structure for processed risk data
interface RiskKPIs {
  total: number;
  high: number;
  medium: number;
  low: number;
}

interface RiskMatrixEntry {
  category: string;
  High: number;
  Medium: number;
  Low: number;
}

interface RiskTrendPoint {
  date: string; // e.g., 'YYYY-WW' or 'YYYY-MM'
  High: number;
  Medium: number;
  Low: number;
  Total: number;
}

interface HighRiskEntry {
    id: string;
    title?: string;
    category: string;
    description?: string;
    prUrl?: string;
    mergedAt?: string;
}

// ---> NEW: Define Risk Status Type
type RiskStatus = 'Dismiss' | 'Accept' | 'Addressed' | 'TODO';
// <--- END NEW

// Add interface for a single processed risk
interface ProcessedRisk {
    id: string; // Unique ID for the risk instance
    decisionId: string;
    severity: 'high' | 'medium' | 'low' | 'unknown';
    category: string;
    description?: string;
    mitigation?: string;
    mergedAt?: string;
    dateKey: string; // e.g., 'YYYY-Www'
    prUrl?: string;
    prTitle?: string;
    status?: RiskStatus; // <-- ADDED
}

// Wrap the component that uses useSearchParams with Suspense
function RiskDashboardContent() {
  const searchParams = useSearchParams();
  
  // Initialize state with null or empty strings, to be set by useEffect
  const [installationIdStr, setInstallationIdStr] = useState<string | null>(null);
  const [repositorySlug, setRepositorySlug] = useState<string | null>(null);

  // ---> ADDED: Validate parameters and determine analysis type
  const [isValidParams, setIsValidParams] = useState(false);
  const [isPublicAnalysis, setIsPublicAnalysis] = useState(false);
  const [installationId, setInstallationId] = useState<number | null>(null);

  useEffect(() => {
    if (!searchParams) return; // Guard against null searchParams

    const currentInstallationIdStr = searchParams.get('installationId');
    const currentRepositorySlug = searchParams.get('repositorySlug');

    setInstallationIdStr(currentInstallationIdStr);
    setRepositorySlug(currentRepositorySlug);

    // The rest of the validation logic can now use the state variables
    // installationIdStr and repositorySlug, which are guaranteed to be 
    // string | null after being set from searchParams.get()
    console.log(`[Risk Dashboard Validation Effect] Running. Repo Slug: ${currentRepositorySlug}, Install ID Str: ${currentInstallationIdStr}`);
    if (!currentRepositorySlug || !currentInstallationIdStr) {
          setError('Missing required parameters (installationId, repositorySlug) in URL.');
          setIsLoading(false);
          setIsValidParams(false);
          console.log(`[Risk Dashboard Validation Effect] Setting error due to missing params.`);
          return;
      }
    console.log(`[Risk Dashboard Validation Effect] Params seem present. Proceeding with parsing.`);
    const parsedId = parseInt(currentInstallationIdStr, 10);
    if (isNaN(parsedId)) {
        setError('Invalid installationId format in URL.');
        setIsLoading(false);
        setIsValidParams(false);
        return;
    }

    setInstallationId(parsedId);
    setIsPublicAnalysis(parsedId === 0);
    setIsValidParams(true);
    setError(null); // Clear error if params are valid
    console.log(`[Risk Dashboard] Params Valid. Repo: ${currentRepositorySlug}, Install ID: ${parsedId}, Public: ${parsedId === 0}`);

  }, [searchParams]);
  // <--- END ADDED

  const [decisions, setDecisions] = useState<Decision[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [kpis, setKpis] = useState<RiskKPIs>({ total: 0, high: 0, medium: 0, low: 0 });
  const [riskMatrixData, setRiskMatrixData] = useState<RiskMatrixEntry[]>([]);
  const [riskTrendData, setRiskTrendData] = useState<RiskTrendPoint[]>([]);
  const [recentHighRisks, setRecentHighRisks] = useState<HighRiskEntry[]>([]);
  
  // ---> ADDED: State for drill-down
  const [allProcessedRisks, setAllProcessedRisks] = useState<ProcessedRisk[]>([]);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [detailModalTitle, setDetailModalTitle] = useState('');
  const [detailRisks, setDetailRisks] = useState<ProcessedRisk[]>([]);
  // <--- END ADDED
  
  // ---> NEW: State for managing individual risk statuses
  const [riskStatuses, setRiskStatuses] = useState<Record<string, RiskStatus>>({});
  // <--- END NEW

  // Fetch Decisions Logic
  const fetchDecisions = useCallback(async (repoSlug: string, installId: number | null) => {
      if (installId === null) { // Should not happen if isValidParams is true
           console.error('[Risk Dashboard] fetchDecisions called with null installId.');
           setError('Internal state error: Installation ID missing.');
           setIsLoading(false);
           return;
       }
    setIsLoading(true);
    setError(null);
    setDecisions([]); // Clear previous
    console.log(`Fetching decisions for ${repoSlug} (install ${installId}, public: ${installId === 0})`);

    const params = new URLSearchParams();
    // Pass parameters to API consistently
    params.append('repository', repoSlug);
    if (installId !== 0) { // Only append installationId if it's not the public indicator (0)
         params.append('installationId', String(installId));
    }
    // Let the API route infer public status from lack of installationId if needed,
    // or add isPublic explicitly if the API uses it:
    // if (installId === 0) params.append('isPublic', 'true'); 
    
    params.append('limit', '1000'); // Fetch a large number, assume this gets all relevant decisions

    try {
      const response = await fetch(`/api/decisions?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch decisions');
      }

      const fetchedDecisions = (data.decisions || []) as Decision[];
      setDecisions(fetchedDecisions);
      console.log(`Fetched ${fetchedDecisions.length} decisions.`);
      if (fetchedDecisions.length === 0) {
          // No error, but no decisions found
      }
      // Data processing will happen in another effect after decisions are set

    } catch (err: any) {
      console.error('Error fetching decisions:', err);
      setError(err.message);
      setDecisions([]); // Clear on error
    } finally {
      // setIsLoading(false); // Loading stays true until processing is also done
    }
  }, []);

  // Effect to Fetch Data
  useEffect(() => {
    // Use validated state variables or the ones directly set from searchParams
    if (isValidParams && repositorySlug && installationId !== null) {
      fetchDecisions(repositorySlug, installationId);
    }
    // Remove [repositorySlug, installationIdStr, fetchDecisions] from dependencies 
    // if they are directly from searchParams and handled in the validation effect.
    // Keep them if they are state variables updated by the validation effect.
  }, [isValidParams, repositorySlug, installationId, fetchDecisions]);

  // Effect to Process Data when decisions are fetched
  useEffect(() => {
    if (!decisions) { // Handle case where decisions might be null initially
        setIsLoading(false);
        return;
    }
    if (decisions.length === 0) {
      // If fetch finished but no decisions, stop loading
      if (!isLoading && !error) {
          // Only set loading false if fetch is not actively running
          // This check prevents flickering if fetchDecisions sets loading=true
          // and immediately after this effect runs with empty decisions
      }
      // Reset derived state if decisions are cleared
      setKpis({ total: 0, high: 0, medium: 0, low: 0 });
      setRiskMatrixData([]);
      setRiskTrendData([]);
      setRecentHighRisks([]);
      // Set loading false *only* if fetch finished with no results
      // If fetchDecisions is still running, it will set loading=false later.
      // If fetchDecisions errored, error state handles the UI.
      // Check if decisions array exists and fetch is not loading and no error exists
      if (Array.isArray(decisions) && !error) {
          setIsLoading(false);
      }
      // ---> ADDED: Clear all processed risks state
      setAllProcessedRisks([]);
      // <--- END ADDED
      return;
    }

    console.log("Processing risk data...");
    let totalRiskCount = 0;
    let highCount = 0;
    let mediumCount = 0;
    let lowCount = 0;
    const matrixAgg: { [category: string]: { High: number; Medium: number; Low: number } } = {};
    const trendAgg: { [dateKey: string]: { High: number; Medium: number; Low: number; Total: number } } = {};
    const highRiskList: HighRiskEntry[] = [];
    // ---> ADDED: Array to hold all risks for drill-down
    const processedRisksAccumulator: ProcessedRisk[] = [];
    // <--- END ADDED

    decisions.forEach(decision => {
      let risks = [];
      try {
        if (decision.metadata?.risks_extracted && typeof decision.metadata.risks_extracted === 'string') {
          risks = JSON.parse(decision.metadata.risks_extracted);
        } else if (Array.isArray(decision.metadata?.risks_extracted)) {
          risks = decision.metadata.risks_extracted;
        }
        if (!Array.isArray(risks)) risks = [];
      } catch (e) {
        console.warn(`[Risk Processing] Failed to parse risks for decision ${decision.id}:`, e);
        risks = [];
      }

      risks.forEach((risk: any, riskIndex: number) => { // Added riskIndex for unique ID
        const severity = risk.severity?.toLowerCase() || 'unknown';
        const category = risk.category || 'Uncategorized';
        const description = risk.description;
        const mitigation = risk.mitigation;
        const mergedAt = decision.metadata?.pr_merged_at;
        const dateKey = mergedAt ? `${new Date(mergedAt).getFullYear()}-W${String(getWeek(new Date(mergedAt), { weekStartsOn: 1 })).padStart(2, '0')}` : 'UnknownDate';

        // ---> ADDED: Create ProcessedRisk object
        const processedRisk: ProcessedRisk = {
            id: `${decision.id}-risk-${riskIndex}`,
            decisionId: decision.id,
            severity: severity as ProcessedRisk['severity'],
            category,
            description,
            mitigation,
            mergedAt,
            dateKey,
            prUrl: decision.metadata?.pr_url,
            prTitle: decision.metadata?.title,
            status: undefined, // Initialize status
        };
        processedRisksAccumulator.push(processedRisk);
         // <--- END ADDED

        // Skip unknown severity for aggregated stats
        if (severity !== 'high' && severity !== 'medium' && severity !== 'low') {
            return; 
        }

        totalRiskCount++;

        // KPIs & Matrix
        if (!matrixAgg[category]) matrixAgg[category] = { High: 0, Medium: 0, Low: 0 };
        if (severity === 'high') {
          highCount++;
          matrixAgg[category].High++;
          // Add to recent high list (using ProcessedRisk data)
           highRiskList.push({ 
              id: processedRisk.id,
              title: processedRisk.prTitle,
              category,
              description,
              prUrl: processedRisk.prUrl,
              mergedAt: mergedAt
           });
        } else if (severity === 'medium') {
          mediumCount++;
          matrixAgg[category].Medium++;
        } else if (severity === 'low') {
          lowCount++;
          matrixAgg[category].Low++;
        }

        // Trend
        if (!trendAgg[dateKey]) trendAgg[dateKey] = { High: 0, Medium: 0, Low: 0, Total: 0 };
        trendAgg[dateKey].Total++;
        if (severity === 'high') trendAgg[dateKey].High++;
        else if (severity === 'medium') trendAgg[dateKey].Medium++;
        else if (severity === 'low') trendAgg[dateKey].Low++;
      });
    });

    // Set KPIs
    setKpis({ total: totalRiskCount, high: highCount, medium: mediumCount, low: lowCount });

    // Set Matrix Data
    const matrixResult: RiskMatrixEntry[] = Object.entries(matrixAgg).map(([category, counts]) => ({ category, ...counts }));
    setRiskMatrixData(matrixResult);

    // Set Trend Data (sort by date)
    const trendResult: RiskTrendPoint[] = Object.entries(trendAgg)
      .map(([date, counts]) => ({ date, ...counts }))
      .filter(d => d.date !== 'UnknownDate') // Filter out unknown dates for trend
      .sort((a, b) => a.date.localeCompare(b.date)); // Sort chronologically
    setRiskTrendData(trendResult);

    // Set Recent High Risks (sort by date desc, limit)
     const sortedHighRisks = highRiskList
         .sort((a, b) => {
            const dateA = a.mergedAt ? new Date(a.mergedAt).getTime() : 0;
            const dateB = b.mergedAt ? new Date(b.mergedAt).getTime() : 0;
            return dateB - dateA; // Newest first
         })
         .slice(0, 10); // Limit to latest 10
    setRecentHighRisks(sortedHighRisks);

    // ---> ADDED: Set the accumulated processed risks
    setAllProcessedRisks(processedRisksAccumulator);
    // <--- END ADDED

    console.log("Risk data processing complete.");
    setIsLoading(false); // Processing finished

  }, [decisions, error, isLoading]);

  // ---> ADDED: Logic for Drill-down Modal
  const handleOpenDetailModal = useCallback((filterCriteria: Partial<ProcessedRisk>, title: string) => {
      console.log("--> handleOpenDetailModal called with:", filterCriteria, title); // Log entry
      console.log("--> Current allProcessedRisks count:", allProcessedRisks.length); // Log data source
      
      const filtered = allProcessedRisks.filter(risk => {
          // Handle multiple criteria if needed
          let match = true;
          if (filterCriteria.severity && risk.severity !== filterCriteria.severity) match = false;
          if (filterCriteria.category && risk.category !== filterCriteria.category) match = false;
          if (filterCriteria.dateKey && risk.dateKey !== filterCriteria.dateKey) match = false;
          // Add more criteria checks here if needed
          return match;
      });
      
      console.log(`--> Filtering complete. Found ${filtered.length} risks.`); // Log filter result

      // Sort filtered results by date, newest first
      filtered.sort((a, b) => {
        const dateA = a.mergedAt ? new Date(a.mergedAt).getTime() : 0;
        const dateB = b.mergedAt ? new Date(b.mergedAt).getTime() : 0;
        return dateB - dateA;
      });

      setDetailRisks(filtered);
      setDetailModalTitle(title);
      console.log("--> Setting isDetailModalOpen to true"); // Log before state update
      setIsDetailModalOpen(true);
  }, [allProcessedRisks]);

  const handleCloseDetailModal = () => {
      setIsDetailModalOpen(false);
      setDetailRisks([]);
      setDetailModalTitle('');
  };
  // <--- END ADDED
  
  // ---> NEW: Handler for setting risk status
  const handleSetRiskStatus = (riskId: string, status: RiskStatus) => {
      setRiskStatuses(prevStatuses => ({
          ...prevStatuses,
          [riskId]: status
      }));
      // TODO: In future, call API to persist this change
      console.log(`Set status for risk ${riskId} to ${status}`);
  };
  // <--- END NEW

  // ---> ADDED: Mitigation Parsing Helper (similar to app/page.tsx)
  const parseMitigation = (mitigationText: string | undefined | null): { shortTerm: string | null, longTerm: string | null } => {
    if (!mitigationText) return { shortTerm: 'N/A', longTerm: null };

    const shortTermPrefix = 'Short-term:';
    const longTermPrefix = 'Long-term:';
    const shortTermIndex = mitigationText.indexOf(shortTermPrefix);
    const longTermIndex = mitigationText.indexOf(longTermPrefix);

    if (shortTermIndex !== -1 && longTermIndex !== -1) {
      const shortTerm = mitigationText.substring(shortTermIndex + shortTermPrefix.length, longTermIndex).trim();
      const longTerm = mitigationText.substring(longTermIndex + longTermPrefix.length).trim();
      return { shortTerm, longTerm };
    } else if (shortTermIndex !== -1) {
      return { shortTerm: mitigationText.substring(shortTermIndex + shortTermPrefix.length).trim(), longTerm: null };
    } else if (longTermIndex !== -1) {
      return { shortTerm: null, longTerm: mitigationText.substring(longTermIndex + longTermPrefix.length).trim() };
    } else {
      // Assume the whole text is short-term if prefixes are missing
      return { shortTerm: mitigationText.trim(), longTerm: null };
    }
  };
  // <--- END ADDED

  // --- Render Logic --- 
  // Removed premature check: The error state handles missing params
  // if (!installationId || !repositorySlug) {
  //   return <p className="text-center text-red-500">Missing required parameters (installationId, repositorySlug) in URL.</p>;
  // }

  // Helper function for matrix cell background (simple version)
  const getMatrixCellBg = (count: number) => {
    if (count === 0) return 'bg-green-100 dark:bg-green-900';
    if (count <= 2) return 'bg-yellow-100 dark:bg-yellow-900';
    return 'bg-red-100 dark:bg-red-900';
  };

  // ---> MODIFIED: Wrap content in Fragment for Modal placement
  return (
    <>
      <div className="w-full">
        <h2 className="text-xl font-semibold mb-4 text-center md:text-left">Repository: {repositorySlug}</h2>
        {isLoading && <p className="text-center">Loading risk data...</p>}
        {error && (
           <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4 text-sm" role="alert">
               <strong className="font-bold">Error loading data: </strong>
               <span className="block sm:inline">{error}</span>
            </div>
        )}
        {!isLoading && !error && decisions.length === 0 && (
          <p className="text-center text-gray-500">No decisions found or no risks extracted for this repository.</p>
        )}

        {!isLoading && !error && kpis.total > 0 && ( // Only show dashboard if there are risks
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* KPIs */}
              <div className="md:col-span-2 bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
                  <h3 className="text-lg font-semibold mb-2">Key Metrics</h3>
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
                      {/* ---> MODIFIED: KPI boxes clickable with logging */}
                      <div 
                          className="p-3 bg-gray-100 dark:bg-zinc-700 rounded cursor-pointer hover:ring-2 ring-gray-400" 
                          onClick={() => { 
                              console.log("Clicked: Total Risks KPI"); // Log click event
                              handleOpenDetailModal({}, `All Risks (${kpis.total})`); 
                          }}
                      >
                          <p className="text-2xl font-bold">{kpis.total}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Total Risks</p>
                      </div>
                      <div 
                          className="p-3 bg-red-100 dark:bg-red-900 rounded cursor-pointer hover:ring-2 ring-red-400" 
                          onClick={() => { 
                              console.log("Clicked: High Severity KPI"); // Log click event
                              handleOpenDetailModal({ severity: 'high' }, `High Severity Risks (${kpis.high})`);
                           }}
                      >
                          <p className="text-2xl font-bold text-red-700 dark:text-red-200">{kpis.high}</p>
                          <p className="text-sm text-red-600 dark:text-red-300">High Severity</p>
                      </div>
                       <div 
                          className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded cursor-pointer hover:ring-2 ring-yellow-400" 
                          onClick={() => { 
                              console.log("Clicked: Medium Severity KPI"); // Log click event
                              handleOpenDetailModal({ severity: 'medium' }, `Medium Severity Risks (${kpis.medium})`); 
                          }}
                      >
                          <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-200">{kpis.medium}</p>
                          <p className="text-sm text-yellow-600 dark:text-yellow-300">Medium Severity</p>
                      </div>
                       <div 
                          className="p-3 bg-green-100 dark:bg-green-900 rounded cursor-pointer hover:ring-2 ring-green-400" 
                          onClick={() => { 
                              console.log("Clicked: Low Severity KPI"); // Log click event
                              handleOpenDetailModal({ severity: 'low' }, `Low Severity Risks (${kpis.low})`); 
                          }}
                      >
                          <p className="text-2xl font-bold text-green-700 dark:text-green-200">{kpis.low}</p>
                          <p className="text-sm text-green-600 dark:text-green-300">Low Severity</p>
                      </div>
                      {/* <--- END MODIFIED */} 
                  </div>
              </div>

              {/* Risk Matrix (Table) */}
              <div className="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow overflow-x-auto">
                  <h3 className="text-lg font-semibold mb-3">Risk Matrix (Category vs. Severity)</h3>
                  {riskMatrixData.length > 0 ? (
                      <table className="w-full text-sm text-left text-gray-700 dark:text-gray-300 border-collapse">
                           <thead className="text-xs text-gray-800 dark:text-gray-200 uppercase bg-gray-100 dark:bg-zinc-700">
                              <tr>
                                  <th scope="col" className="px-4 py-2 border dark:border-zinc-600">Category</th>
                                  <th scope="col" className="px-4 py-2 border dark:border-zinc-600 text-center">High</th>
                                  <th scope="col" className="px-4 py-2 border dark:border-zinc-600 text-center">Medium</th>
                                  <th scope="col" className="px-4 py-2 border dark:border-zinc-600 text-center">Low</th>
                              </tr>
                          </thead>
                          <tbody>
                              {riskMatrixData.map((row) => (
                                  <tr key={row.category} className="bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700">
                                      <td className="px-4 py-2 border dark:border-zinc-600 font-medium whitespace-nowrap">{row.category}</td>
                                      {/* ---> MODIFIED: Matrix cells clickable */}
                                      <td 
                                          className={`px-4 py-2 border dark:border-zinc-600 text-center font-semibold ${getMatrixCellBg(row.High)} ${row.High > 0 ? 'cursor-pointer hover:ring-2 ring-red-400' : ''}`}
                                          onClick={row.High > 0 ? () => handleOpenDetailModal({ category: row.category, severity: 'high' }, `High Risk: ${row.category} (${row.High})`) : undefined}
                                      >{row.High}</td>
                                      <td 
                                          className={`px-4 py-2 border dark:border-zinc-600 text-center font-semibold ${getMatrixCellBg(row.Medium)} ${row.Medium > 0 ? 'cursor-pointer hover:ring-2 ring-yellow-400' : ''}`}
                                          onClick={row.Medium > 0 ? () => handleOpenDetailModal({ category: row.category, severity: 'medium' }, `Medium Risk: ${row.category} (${row.Medium})`) : undefined}
                                      >{row.Medium}</td>
                                      <td 
                                          className={`px-4 py-2 border dark:border-zinc-600 text-center font-semibold ${getMatrixCellBg(row.Low)} ${row.Low > 0 ? 'cursor-pointer hover:ring-2 ring-green-400' : ''}`}
                                          onClick={row.Low > 0 ? () => handleOpenDetailModal({ category: row.category, severity: 'low' }, `Low Risk: ${row.category} (${row.Low})`) : undefined}
                                      >{row.Low}</td>
                                      {/* <--- END MODIFIED */} 
                                  </tr>
                              ))}
                          </tbody>
                      </table>
                  ) : (
                      <p className="text-gray-500 dark:text-gray-400">No risk data available for matrix.</p>
                  )}
              </div>

              {/* Risk Trend Chart */}
              <div className="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
                  {/* ---> MODIFIED: Add date range to title */}
                  <h3 className="text-lg font-semibold mb-1">Risk Trend (by Week)</h3>
                  {riskTrendData.length > 0 && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                          Showing data from {riskTrendData[0].date} to {riskTrendData[riskTrendData.length - 1].date}
                      </p>
                  )}
                  {riskTrendData.length > 1 ? ( // Need at least 2 points for a line
                    <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={riskTrendData} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                             <CartesianGrid strokeDasharray="3 3" stroke="#404040" /> {/* Adjusted grid color */} 
                             <XAxis dataKey="date" stroke="#a1a1aa" fontSize={12} /> {/* Adjusted label color */} 
                             <YAxis stroke="#a1a1aa" fontSize={12} allowDecimals={false}/> {/* Adjusted label color */} 
                             <Tooltip 
                                 contentStyle={{ backgroundColor: '#27272a', border: '1px solid #52525b' }} // Dark tooltip
                                 itemStyle={{ color: '#e4e4e7' }} // Light text in tooltip
                             />
                             <Legend wrapperStyle={{ fontSize: '12px' }}/>
                            {/* Using Area instead of Line for stacking visibility */}
                            <Area type="monotone" dataKey="High" stackId="1" stroke="#ef4444" fill="#ef4444" fillOpacity={0.6} name="High Sev." />
                            <Area type="monotone" dataKey="Medium" stackId="1" stroke="#eab308" fill="#eab308" fillOpacity={0.6} name="Medium Sev." />
                            <Area type="monotone" dataKey="Low" stackId="1" stroke="#22c55e" fill="#22c55e" fillOpacity={0.6} name="Low Sev." />
                        </AreaChart>
                    </ResponsiveContainer>
                 ) : (
                    <p className="text-gray-500 dark:text-gray-400">Not enough data points for trend chart.</p>
                 )}
              </div>

              {/* Recent High Severity Risks */}
              <div className="md:col-span-2 bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
                  <h3 className="text-lg font-semibold mb-3">Recent High Severity Risks (Max 10)</h3>
                   {recentHighRisks.length > 0 ? (
                      <ul className="space-y-3">
                          {recentHighRisks.map((risk) => (
                              <li key={risk.id} className="text-sm border-l-4 border-red-500 pl-3 py-1">
                                  <p className="font-semibold">{risk.category}: <span className="font-normal">{risk.description || 'No description'}</span></p>
                                  <p className="text-xs text-gray-500 dark:text-gray-400">
                                       From PR: {risk.title ? <span title={risk.title}>{risk.title.substring(0, 50)}{risk.title.length > 50 ? '...' : ''}</span> : 'N/A'} 
                                      {risk.prUrl && <a href={risk.prUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline ml-2">[Link]</a>}
                                      {risk.mergedAt && ` | Merged: ${format(new Date(risk.mergedAt), 'yyyy-MM-dd')}`}
                                  </p>
                              </li>
                          ))}
                      </ul>
                   ) : (
                      <p className="text-gray-500 dark:text-gray-400">No high severity risks found.</p>
                   )}
              </div>
          </div>
        )}
      </div>
      
      {/* Detail Modal */}
      {isDetailModalOpen && (
        // ... (keep existing modal structure)
        <div 
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm"
            onClick={handleCloseDetailModal} // Close on backdrop click
        >
            {/* Actual modal content container */}
            <div 
                className="bg-white dark:bg-zinc-800 rounded-lg shadow-xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto relative text-gray-900 dark:text-gray-100 border-4 border-red-500" // <-- ADDED BORDER FOR DEBUGGING
                onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside modal
            >
                <button 
                    onClick={handleCloseDetailModal} 
                    className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-2xl leading-none"
                    aria-label="Close modal"
                >
                    &times;
                </button>
                <h4 className="text-xl font-semibold mb-4">{detailModalTitle}</h4>
                {detailRisks.length > 0 ? (
                    <ul className="space-y-4">
                        {detailRisks.map((risk) => {
                            const mitigationParts = parseMitigation(risk.mitigation);
                            const severityClass = risk.severity === 'high' ? 'border-red-500 bg-red-50 dark:bg-red-900/30' 
                                                : risk.severity === 'medium' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/30' 
                                                : 'border-green-500 bg-green-50 dark:bg-green-900/30';
                            const severityBadgeClass = risk.severity === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' 
                                                : risk.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' 
                                                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';

                            // ---> NEW: Get current status for this risk
                            const currentStatus = riskStatuses[risk.id] || risk.status || 'TODO';
                            const statusTypes: RiskStatus[] = ['Dismiss', 'Accept', 'Addressed', 'TODO'];
                            // <--- END NEW

                            return (
                                <li key={risk.id} className={`text-sm border-l-4 rounded-r-md px-3 py-2 ${severityClass}`}>
                                    {/* Mitigation Section */}
                                    <div className="mb-1.5">
                                        {mitigationParts.shortTerm && (
                                            <p>
                                                <strong className="font-medium">Recommendation{mitigationParts.longTerm ? ' (Short-term)' : ''}:</strong> {mitigationParts.shortTerm}
                                            </p>
                                        )}
                                        {mitigationParts.longTerm && (
                                             <p className="mt-0.5">
                                                <strong className="font-medium">Recommendation (Long-term):</strong> {mitigationParts.longTerm}
                                            </p>
                                        )}
                                    </div>
                                    
                                    {/* Why Section */}
                                    <p className="font-semibold mt-1 pt-1 border-t border-gray-300 dark:border-zinc-600">
                                        <span className={`mr-1.5 px-1 py-0.5 rounded text-xs font-medium ${severityBadgeClass}`}>
                                            {risk.severity.toUpperCase()}
                                        </span>
                                        Why ({risk.category}): <span className="font-normal text-gray-700 dark:text-gray-300">{risk.description || 'No description provided'}</span>
                                    </p>
                                    
                                    {/* Source PR Section */}
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                         Source: {risk.prTitle ? <span title={risk.prTitle}>{risk.prTitle.substring(0, 70)}{risk.prTitle.length > 70 ? '...' : ''}</span> : 'N/A'} 
                                        {risk.prUrl && <a href={risk.prUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline ml-1.5">[PR Link]</a>}
                                        {risk.mergedAt && <span className="ml-1.5">({format(new Date(risk.mergedAt), 'yyyy-MM-dd')})</span>}
                                    </p>
                                    
                                    {/* ---> NEW: Status Display and Action Buttons */}
                                    <div className="mt-2 pt-2 border-t border-gray-300 dark:border-zinc-600 flex flex-wrap items-center gap-2 text-xs">
                                        <span className="font-medium text-gray-600 dark:text-gray-400">Status:</span>
                                        {currentStatus ? (
                                            <span className="font-semibold px-1.5 py-0.5 rounded bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                {currentStatus}
                                            </span>
                                        ) : (
                                            <span className="italic text-gray-500">Not Set</span>
                                        )}
                                        <div className="ml-auto flex gap-1.5">
                                            {statusTypes.map(status => (
                                                <button 
                                                    key={status}
                                                    onClick={() => handleSetRiskStatus(risk.id, status)}
                                                    className={`px-2 py-0.5 rounded text-xs border transition-colors duration-150 
                                                        ${currentStatus === status 
                                                            ? 'bg-indigo-600 text-white border-indigo-700' 
                                                            : 'bg-gray-100 dark:bg-zinc-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-zinc-600 hover:bg-gray-200 dark:hover:bg-zinc-600'}
                                                    `}
                                                    title={`Mark as ${status}`}
                                                >
                                                    {status}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                    {/* <--- END NEW */} 
                                </li>
                            );
                        })}
                    </ul>
                 ) : (
                    <p className="text-gray-500 dark:text-gray-400">No risks match the selected criteria.</p>
                 )}
            </div>
            {
             (() => { // Add log inside modal render
                console.log("--> Rendering Detail Modal. Title:", detailModalTitle, "Risks count:", detailRisks.length);
                return null;
             })()
            }
        </div>
      )}
    </>
  );
}

export default function RiskDashboardPage() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-start p-6 md:p-12 bg-gray-50 dark:bg-zinc-900 text-gray-900 dark:text-gray-100">
      <div className="w-full max-w-6xl">
        <h1 className="text-3xl font-bold text-indigo-700 dark:text-indigo-400 mb-8">Risk Summary Dashboard</h1>
        {/* Wrap the component using useSearchParams with Suspense */}
        <Suspense fallback={<div className="text-center">Loading dashboard...</div>}>
          <RiskDashboardContent />
        </Suspense>
      </div>
    </main>
  );
} 