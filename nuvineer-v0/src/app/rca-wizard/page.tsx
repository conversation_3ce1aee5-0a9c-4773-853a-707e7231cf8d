'use client'

import { Suspense, useState, useEffect } from 'react';
import { useRCAWizard } from '../../hooks/useRCAWizard';
import { useRCAWizardActions } from '../../hooks/useRCAWizardActions';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import InvestigationPlanView from '../../components/rca-wizard/InvestigationPlanView';
import DecisionDiscoveryView from '../../components/rca-wizard/DecisionDiscoveryView';
import ForensicAnalysisView from '../../components/rca-wizard/ForensicAnalysisView';
import CodingAgentPrompt from '../../components/rca-wizard/CodingAgentPrompt';

function RCAWizardContent() {
  const {
    session,
    isLoadingSession,
    isAuthorized,
    wizardState,
    setWizardState,
    repositorySlug,
    githubHandle,
    apiKey,
    supabase
  } = useRCAWizard();

  const actions = useRCAWizardActions({
    wizardState,
    setWizardState,
    repositorySlug,
    githubHandle,
    apiKey,
    installationId: wizardState.installationId,
    isPublic: wizardState.isPublic,
  });

  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Debug: Monitor wizard state changes
  useEffect(() => {
    console.log('[RCA Main] Wizard state symptoms changed:', wizardState.symptoms);
  }, [wizardState.symptoms]);

  // Handle import from issue - exactly like Feature Wizard
  const handleImportFromIssue = () => {
    actions.fetchIssues();
    actions.setShowIssueSelector(true);
  };

  const handleSelectIssue = (issue: any) => {
    console.log('[RCA Main] About to select issue:', issue);
    console.log('[RCA Main] Current wizard state symptoms before:', wizardState.symptoms);
    actions.handleSelectIssue(issue);
  };

  // Show loading state
  if (isLoadingSession) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading RCA Analysis Wizard...</p>
        </div>
      </div>
    );
  }

  // Show authentication required
  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access the RCA Analysis Wizard.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => window.location.href = '/auth/signin'}
              className="w-full"
            >
              Sign In with GitHub
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied for non-jabubaker users
  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Access Restricted</CardTitle>
            <CardDescription>
              The RCA Analysis Wizard is currently in beta and restricted to specific users.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-gray-600">
              <p><strong>Current User:</strong> {githubHandle}</p>
              <p>If you believe you should have access, please contact the administrator.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleSymptomAnalysis = async () => {
    setIsAnalyzing(true);
    try {
      const response = await fetch('/api/rca-wizard/investigate-symptoms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symptoms: wizardState.symptoms,
          repositorySlug: repositorySlug,
          installationId: actions.installationId,
          isPublic: actions.isPublic
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setWizardState(prev => ({
          ...prev,
          investigationPlan: data.investigationPlan,
          currentStep: 'investigation-planning'
        }));
      } else {
        console.error('Symptom analysis failed');
      }
    } catch (error) {
      console.error('Error during symptom analysis:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };


  const discoverDecisions = async () => {
    if (!wizardState.investigationPlan) {
      setWizardState(prev => ({ ...prev, error: 'No investigation plan available' }));
      return;
    }

    setWizardState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await fetch('/api/rca-wizard/discover-decisions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          investigationPlan: wizardState.investigationPlan,
          symptoms: wizardState.symptoms,
          repositorySlug,
          installationId: actions.installationId,
          isPublic: actions.isPublic
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to discover decisions');
      }

      const data = await response.json();
      
      setWizardState(prev => ({
        ...prev,
        suspiciousDecisions: data.suspiciousDecisions,
        currentStep: 'decision-discovery',
        isLoading: false
      }));

    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false
      }));
    }
  };

  const performForensicAnalysis = async () => {
    if (!wizardState.suspiciousDecisions || wizardState.suspiciousDecisions.length === 0) {
      setWizardState(prev => ({ ...prev, error: 'No suspicious decisions found' }));
      return;
    }

    setWizardState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await fetch('/api/rca-wizard/forensic-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          suspiciousDecisions: wizardState.suspiciousDecisions,
          symptoms: wizardState.symptoms,
          repositorySlug,
          installationId: actions.installationId,
          isPublic: actions.isPublic
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to perform forensic analysis');
      }

      const data = await response.json();
      
      setWizardState(prev => ({
        ...prev,
        forensicAnalysis: data.forensicAnalysis,
        currentStep: 'forensic-analysis',
        isLoading: false
      }));

    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false
      }));
    }
  };

  const renderStep = () => {
    switch (wizardState.currentStep) {
      case 'symptom-analysis':
        return (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Step 1: Symptom Analysis</CardTitle>
                <Badge variant="secondary">Current Step</Badge>
              </div>
              <CardDescription>
                Describe the symptoms of the incident. This information will be used to guide the investigation.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">Import from GitHub Issue</h3>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                      Quickly start from an existing GitHub issue in {repositorySlug}
                    </p>
                  </div>
                  <Button
                    onClick={handleImportFromIssue}
                    disabled={actions.isLoadingIssues}
                    size="sm"
                  >
                    {actions.isLoadingIssues ? 'Loading...' : 'Browse Issues'}
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="errorType" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Error Type / Failure Mode</label>
                  <Input 
                    id="errorType"
                    value={wizardState.symptoms.errorType}
                    onChange={(e) => setWizardState(prev => ({...prev, symptoms: {...prev.symptoms, errorType: e.target.value}}))}
                    placeholder="e.g. API 500 Error, UI Glitch"
                  />
                </div>
                <div>
                  <label htmlFor="environment" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Environment</label>
                  <Input 
                    id="environment"
                    value={wizardState.symptoms.environment}
                    onChange={(e) => setWizardState(prev => ({...prev, symptoms: {...prev.symptoms, environment: e.target.value}}))}
                    placeholder="e.g. Production, Staging"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="affectedComponents" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Affected Components/Services</label>
                <Input 
                  id="affectedComponents"
                  value={wizardState.symptoms.affectedComponents.join(', ')}
                  onChange={(e) => setWizardState(prev => ({...prev, symptoms: {...prev.symptoms, affectedComponents: e.target.value.split(',').map(s => s.trim())}}))}
                  placeholder="e.g. checkout-service, payment-api"
                />
              </div>
              <div>
                <label htmlFor="userImpact" className="block text-sm font-medium text-gray-700 dark:text-gray-300">User Impact</label>
                <Input 
                  id="userImpact"
                  value={wizardState.symptoms.userImpact}
                  onChange={(e) => setWizardState(prev => ({...prev, symptoms: {...prev.symptoms, userImpact: e.target.value}}))}
                  placeholder="e.g. Users cannot complete purchases"
                />
              </div>
              <div>
                <label htmlFor="timePattern" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Time Pattern / Triggers</label>
                <Input 
                  id="timePattern"
                  value={wizardState.symptoms.timePattern}
                  onChange={(e) => setWizardState(prev => ({...prev, symptoms: {...prev.symptoms, timePattern: e.target.value}}))}
                  placeholder="e.g. Intermittent, since last deployment"
                />
              </div>
              <Button onClick={handleSymptomAnalysis} disabled={isAnalyzing}>
                {isAnalyzing ? 'Analyzing...' : 'Analyze Symptoms & Investigate'}
              </Button>
            </CardContent>
          </Card>
        );

      case 'investigation-planning':
        return wizardState.investigationPlan ? (
          <InvestigationPlanView
            investigationPlan={wizardState.investigationPlan}
            onDiscoverDecisions={discoverDecisions}
            isLoading={wizardState.isLoading}
          />
        ) : (
          <Card>
            <CardContent>
              <p>No investigation plan available.</p>
            </CardContent>
          </Card>
        );

      case 'decision-discovery':
        return (
          <DecisionDiscoveryView
            suspiciousDecisions={wizardState.suspiciousDecisions || []}
            onPerformForensicAnalysis={performForensicAnalysis}
            isLoading={wizardState.isLoading}
          />
        );

      case 'forensic-analysis':
        return wizardState.forensicAnalysis ? (
          <div className="space-y-6">
            <ForensicAnalysisView forensicAnalysis={wizardState.forensicAnalysis} />
            {wizardState.suspiciousDecisions && (
              <CodingAgentPrompt
                forensicAnalysis={wizardState.forensicAnalysis}
                suspiciousDecisions={wizardState.suspiciousDecisions}
                symptoms={wizardState.symptoms}
                repositorySlug={repositorySlug}
              />
            )}
          </div>
        ) : (
          <Card>
            <CardContent>
              <p>No forensic analysis results available.</p>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  let content;

  // Main component render
  if (isAuthorized) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm">
          <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
              {/* Header */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h1 className="text-3xl font-bold text-gray-900">RCA Analysis Wizard</h1>
                  <Badge variant="outline">Beta - jabubaker only</Badge>
                </div>
                <p className="text-gray-600">
                  Investigate production incidents using architectural decision intelligence
                </p>
                {repositorySlug && (
                  <div className="mt-2">
                    <Badge variant="secondary">{repositorySlug}</Badge>
                  </div>
                )}
              </div>

              {/* Progress indicator */}
              <div className="mb-8">
                <div className="flex items-center space-x-4">
                  {[
                    { step: 'symptom-analysis', label: 'Symptoms' },
                    { step: 'investigation-planning', label: 'Investigation' },
                    { step: 'decision-discovery', label: 'Discovery' },
                    { step: 'forensic-analysis', label: 'Analysis' }
                  ].map(({ step, label }, index) => (
                    <div key={step} className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        wizardState.currentStep === step
                          ? 'bg-blue-600 text-white'
                          : index < ['symptom-analysis', 'investigation-planning', 'decision-discovery', 'forensic-analysis'].indexOf(wizardState.currentStep)
                          ? 'bg-green-600 text-white'
                          : 'bg-gray-300 text-gray-600'
                      }`}>
                        {index + 1}
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-600">{label}</span>
                      {index < 3 && <div className="w-8 h-0.5 bg-gray-300 ml-4" />}
                    </div>
                  ))}
                </div>
              </div>

              {/* Error display */}
              {wizardState.error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800">{wizardState.error}</p>
                </div>
              )}

              {/* Step content */}
              {renderStep()}

              {/* Issue Selector Modal */}
              {actions.showIssueSelector && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl">
                    <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Select a GitHub Issue</h3>
                    </div>
                    <div className="p-6 max-h-[60vh] overflow-y-auto bg-white dark:bg-gray-800">
                      {actions.issues && actions.issues.length > 0 ? (
                        <ul className="space-y-2">
                          {actions.issues.map((issue) => (
                            <li key={issue.number} 
                                onClick={() => handleSelectIssue(issue)}
                                className="p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-md cursor-pointer transition-colors border border-gray-200 dark:border-gray-600">
                              <div className="font-semibold text-gray-900 dark:text-white">{issue.title}</div>
                              <div className="text-sm text-gray-500 dark:text-gray-300">
                                #{issue.number} opened on {new Date(issue.created_at).toLocaleDateString()}
                              </div>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-gray-600 dark:text-gray-300 text-center py-8">No open issues found in this repository.</p>
                      )}
                    </div>
                    <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex justify-end">
                      <Button variant="outline" onClick={() => actions.setShowIssueSelector(false)}>Cancel</Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
    </div>
  );
}

export default function RCAWizardPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <RCAWizardContent />
    </Suspense>
  );
} 