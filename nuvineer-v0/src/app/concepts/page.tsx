'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';

interface Concept {
    name: string;
}

const LoadingSpinner = () => (
    <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-amber-500"></div>
    </div>
);

const ErrorDisplay = ({ message }: { message: string }) => (
    <div className="text-center text-red-500 bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
        <h3 className="font-bold">Error</h3>
        <p>{message}</p>
    </div>
);

// Function to determine tag size and color, can be enhanced later
const getTagStyle = (index: number, total: number) => {
    // This is a placeholder for more sophisticated styling.
    // For now, we'll use a few different colors.
    const colors = [
        'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-800/50',
        'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-800/50',
        'bg-purple-100 text-purple-800 hover:bg-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:hover:bg-purple-800/50',
        'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:hover:bg-yellow-800/50',
    ];
    return colors[index % colors.length];
};

function ConceptsExplorer() {
    const searchParams = useSearchParams();
    const router = useRouter();
    const repositorySlug = searchParams?.get('repositorySlug');
    const installationId = searchParams?.get('installationId');
    const isPublic = searchParams?.get('isPublic') === 'true';

    const [concepts, setConcepts] = useState<Concept[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!repositorySlug) {
            // setError('No repository selected. Please go back and select a repository to explore.');
            // Or redirect them back to the main page after a delay
            router.push('/');
            return;
        }

        const fetchConcepts = async () => {
            setIsLoading(true);
            setError(null);
            try {
                const params = new URLSearchParams({
                    repositorySlug: repositorySlug,
                    isPublic: String(isPublic),
                });
                if (installationId) {
                    params.append('installationId', installationId);
                }

                const response = await fetch(`/api/concepts?${params.toString()}`);
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `Failed to fetch concepts (${response.status})`);
                }
                const data: Concept[] = await response.json();
                setConcepts(data);
            } catch (err: any) {
                setError(err.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchConcepts();
    }, [repositorySlug, installationId, isPublic, router]);

    return (
        <div className="w-full max-w-4xl mx-auto p-4 md:p-6">
            <header className="mb-8">
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200">Concept Explorer</h1>
                <p className="text-md text-gray-500 dark:text-gray-400">
                    Explore architectural decisions by concept for <span className="font-semibold text-amber-600 dark:text-amber-500">{repositorySlug}</span>.
                </p>
                <div className="mt-4">
                    <Link href={`/?repositorySlug=${repositorySlug}&isPublic=${isPublic}&installationId=${installationId || ''}`} className="text-sm text-amber-600 hover:underline dark:text-amber-500">
                        &larr; Back to Main Analysis
                    </Link>
                </div>
            </header>

            <main className="bg-white dark:bg-zinc-800 p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">Architectural Concepts</h2>
                {isLoading ? (
                    <LoadingSpinner />
                ) : error ? (
                    <ErrorDisplay message={error} />
                ) : concepts.length > 0 && repositorySlug ? (
                    <div className="flex flex-wrap gap-3">
                        {concepts.map((concept, index) => (
                            <Link
                                href={`/concepts/${encodeURIComponent(concept.name)}?repositorySlug=${encodeURIComponent(repositorySlug)}&isPublic=${isPublic}&installationId=${installationId || ''}`}
                                key={concept.name}
                                className={`px-4 py-2 rounded-full text-sm font-medium transition-transform transform hover:scale-105 ${getTagStyle(index, concepts.length)}`}
                            >
                                {concept.name}
                            </Link>
                        ))}
                    </div>
                ) : (
                    <p className="text-gray-500 dark:text-gray-400">No active architectural concepts found for this repository. Analyze the repository to extract decisions and concepts.</p>
                )}
            </main>
        </div>
    );
}


export default function ConceptsPage() {
    return (
      // The Suspense boundary is important for client components that use searchParams
      <Suspense fallback={<div className="flex justify-center items-center min-h-screen"><LoadingSpinner /></div>}>
        <ConceptsExplorer />
      </Suspense>
    );
} 