'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useParams, useRouter } from 'next/navigation';
import Link from 'next/link';

// --- Reusable Components ---

const LoadingSpinner = () => (
    <div className="flex justify-center items-center h-full p-8">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-amber-500"></div>
    </div>
);

const ErrorDisplay = ({ message }: { message: string }) => (
    <div className="text-center text-red-500 bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
        <h3 className="font-bold">Error</h3>
        <p>{message}</p>
    </div>
);

// This is a simplified version of the Decision card from the main page.
// We can extract it to a shared component later.
const DecisionCard = ({ decision }: { decision: any }) => {
    return (
        <div className="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow border border-gray-200 dark:border-zinc-700">
            <h4 className="font-semibold text-lg mb-1">{decision.title || 'Untitled Decision'}</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Source: {
                    decision.pr_url ? 
                    <a href={decision.pr_url} target="_blank" rel="noopener noreferrer" className="text-amber-600 hover:underline dark:text-amber-500">
                        PR #{decision.pr_number}
                    </a> : `PR #${decision.pr_number}`
                }
            </p>
            <p className="text-sm mb-1"><strong>Description:</strong> {decision.description || 'N/A'}</p>
            <p className="text-sm mb-1"><strong>Rationale:</strong> {decision.rationale || 'N/A'}</p>
            {decision.implications && <p className="text-sm mb-1"><strong>Implications:</strong> {decision.implications}</p>}
            
            {(decision.domain_concepts?.length > 0) && (
                <div className="flex flex-wrap gap-1.5 mt-3 pt-2 border-t border-gray-200 dark:border-zinc-700">
                    {(Array.isArray(decision.domain_concepts) ? decision.domain_concepts : []).map((tag: string) => (
                        <span key={tag} className="px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700 dark:bg-zinc-700 dark:text-gray-300">
                            {tag}
                        </span>
                    ))}
                </div>
            )}
        </div>
    );
};

// --- Main Page Component ---

function ConceptDetailExplorer() {
    const searchParams = useSearchParams();
    const params = useParams();
    const router = useRouter();

    const repositorySlug = searchParams?.get('repositorySlug');
    const installationId = searchParams?.get('installationId');
    const isPublic = searchParams?.get('isPublic') === 'true';
    const conceptNameParam = params?.conceptName;
    const conceptName = Array.isArray(conceptNameParam) ? conceptNameParam[0] : conceptNameParam;

    const [details, setDetails] = useState<any | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!repositorySlug || !conceptName) {
            router.push('/');
            return;
        }

        const fetchDetails = async () => {
            setIsLoading(true);
            setError(null);
            try {
                const params = new URLSearchParams({
                    repositorySlug: repositorySlug,
                    isPublic: String(isPublic),
                });
                if (installationId) {
                    params.append('installationId', installationId);
                }
                const response = await fetch(`/api/concepts/${encodeURIComponent(conceptName)}?${params.toString()}`);
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `Failed to fetch concept details (${response.status})`);
                }
                const data = await response.json();
                setDetails(data);
            } catch (err: any) {
                setError(err.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchDetails();
    }, [repositorySlug, conceptName, installationId, isPublic, router]);

    if (isLoading) {
        return <LoadingSpinner />;
    }

    if (error) {
        return <ErrorDisplay message={error} />;
    }

    if (!details) {
        return <p>No details found for this concept.</p>;
    }

    return (
        <div className="w-full max-w-6xl mx-auto p-4 md:p-6">
            <header className="mb-8">
                <p className="text-sm text-gray-500 dark:text-gray-400">Concept</p>
                <h1 className="text-4xl font-bold text-gray-800 dark:text-gray-200">{details.concept.name}</h1>
                <p className="text-md text-gray-500 dark:text-gray-400 mt-1">
                    For repository <span className="font-semibold text-amber-600 dark:text-amber-500">{repositorySlug}</span>
                </p>
                 <div className="mt-4">
                    <Link href={`/concepts?repositorySlug=${repositorySlug}&isPublic=${isPublic}&installationId=${installationId || ''}`} className="text-sm text-amber-600 hover:underline dark:text-amber-500">
                        &larr; Back to Concept Explorer
                    </Link>
                </div>
            </header>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Main Content: Decisions */}
                <main className="lg:col-span-2">
                    <h2 className="text-2xl font-semibold mb-4 text-gray-700 dark:text-gray-300">
                        Active Decisions ({details.decisions.length})
                    </h2>
                    {details.decisions.length > 0 ? (
                        <div className="space-y-4">
                            {details.decisions.map((decision: any) => (
                                <DecisionCard key={decision.id} decision={decision} />
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500 dark:text-gray-400">No active decisions found for this concept.</p>
                    )}
                </main>

                {/* Sidebar: Related Concepts */}
                <aside>
                    <div className="sticky top-8 bg-gray-50 dark:bg-zinc-800 p-4 rounded-lg shadow-sm">
                        <h3 className="text-xl font-semibold mb-4 text-gray-700 dark:text-gray-300">Related Concepts</h3>
                        {details.related_concepts.length > 0 && repositorySlug ? (
                             <ul className="space-y-2">
                                {details.related_concepts.map((related: any) => (
                                    <li key={related.name}>
                                        <Link
                                            href={`/concepts/${encodeURIComponent(related.name)}?repositorySlug=${encodeURIComponent(repositorySlug)}&isPublic=${isPublic}&installationId=${installationId || ''}`}
                                            className="block p-2 rounded-md hover:bg-amber-100 dark:hover:bg-amber-900/50 transition-colors"
                                        >
                                            <span className="font-medium text-gray-800 dark:text-gray-200">{related.name}</span>
                                            <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">(Similarity: {(related.score * 100).toFixed(1)}%)</span>
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        ) : (
                            <p className="text-sm text-gray-500 dark:text-gray-400">{details.related_concepts.length === 0 ? "No related concepts found." : ""}</p>
                        )}
                    </div>
                </aside>
            </div>
        </div>
    );
}

export default function ConceptDetailPage() {
    return (
      <Suspense fallback={<LoadingSpinner />}>
        <ConceptDetailExplorer />
      </Suspense>
    );
} 