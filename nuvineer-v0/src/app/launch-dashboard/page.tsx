'use client';

import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  RocketLaunchIcon,
  DocumentTextIcon,
  CogIcon,
  EyeIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  ChevronRightIcon,
  PlayCircleIcon,
  PauseCircleIcon,
  StopCircleIcon,
  CloudArrowUpIcon
} from '@heroicons/react/24/outline';

// Mock data based on what the system can provide
const mockData = {
  featuresPendingLaunch: [
    {
      id: 'dd-sess-1',
      title: 'Real-time Collaboration System',
      repository: 'acme/collaboration-app',
      status: 'implementation_complete',
      riskLevel: 'medium',
      designDocGenerated: '2024-01-15',
      implementationPlanGenerated: '2024-01-16',
      rolloutStrategyGenerated: '2024-01-18',
      githubIssue: 'https://github.com/acme/collaboration-app/issues/234',
      milestones: {
        total: 4,
        completed: 4,
        inProgress: 0,
        planned: 0
      },
      lastActivity: '2024-01-20',
      owner: '<EMAIL>',
      reviewers: ['<EMAIL>', '<EMAIL>'],
      securityReviewRequired: true,
      businessImpact: 'high',
      userJourneys: 3,
      prsMerged: 12,
      decisionsExtracted: 8,
      flagsRequired: ['collab_v2_enabled', 'realtime_sync']
    },
    {
      id: 'dd-sess-2', 
      title: 'Advanced Analytics Pipeline',
      repository: 'acme/analytics-platform',
      status: 'rollout_strategy_ready',
      riskLevel: 'high',
      designDocGenerated: '2024-01-10',
      implementationPlanGenerated: '2024-01-12',
      rolloutStrategyGenerated: '2024-01-14',
      githubIssue: 'https://github.com/acme/analytics-platform/issues/156',
      milestones: {
        total: 6,
        completed: 5,
        inProgress: 1,
        planned: 0
      },
      lastActivity: '2024-01-19',
      owner: '<EMAIL>',
      reviewers: ['<EMAIL>', '<EMAIL>'],
      securityReviewRequired: true,
      businessImpact: 'critical',
      userJourneys: 2,
      prsMerged: 18,
      decisionsExtracted: 14,
      flagsRequired: ['analytics_v3', 'ml_inference_enabled']
    },
    {
      id: 'dd-sess-3',
      title: 'Mobile Push Notifications',
      repository: 'acme/mobile-app',
      status: 'implementation_in_progress', 
      riskLevel: 'low',
      designDocGenerated: '2024-01-12',
      implementationPlanGenerated: '2024-01-14',
      rolloutStrategyGenerated: null,
      githubIssue: 'https://github.com/acme/mobile-app/issues/89',
      milestones: {
        total: 3,
        completed: 2,
        inProgress: 1,
        planned: 0
      },
      lastActivity: '2024-01-21',
      owner: '<EMAIL>',
      reviewers: ['<EMAIL>'],
      securityReviewRequired: false,
      businessImpact: 'medium',
      userJourneys: 4,
      prsMerged: 6,
      decisionsExtracted: 4,
      flagsRequired: ['push_notifications_enabled']
    }
  ],
  recentLaunches: [
    {
      id: 'launch-1',
      title: 'Enhanced Search Experience',
      repository: 'acme/web-platform',
      launchedDate: '2024-01-18',
      status: 'monitoring',
      riskLevel: 'low',
      rolloutPhase: 'full_rollout',
      rolloutProgress: 100,
      metrics: {
        errorRate: 0.02,
        p99Latency: 145,
        userAdoption: 78,
        alerts: 0
      },
      owner: '<EMAIL>',
      prsMerged: 8,
      decisionsExtracted: 5
    },
    {
      id: 'launch-2', 
      title: 'API Rate Limiting v2',
      repository: 'acme/api-gateway',
      launchedDate: '2024-01-16',
      status: 'graduated',
      riskLevel: 'medium',
      rolloutPhase: 'graduated',
      rolloutProgress: 100,
      metrics: {
        errorRate: 0.01,
        p99Latency: 89,
        userAdoption: 95,
        alerts: 0
      },
      owner: '<EMAIL>',
      prsMerged: 15,
      decisionsExtracted: 9
    }
  ],
  repositoryHealth: [
    {
      repository: 'acme/collaboration-app',
      analysisProgress: 100,
      totalPRs: 145,
      decisionsExtracted: 89,
      lastAnalyzed: '2024-01-21',
      healthScore: 95
    },
    {
      repository: 'acme/analytics-platform', 
      analysisProgress: 87,
      totalPRs: 234,
      decisionsExtracted: 156,
      lastAnalyzed: '2024-01-20', 
      healthScore: 78
    },
    {
      repository: 'acme/mobile-app',
      analysisProgress: 92,
      totalPRs: 87,
      decisionsExtracted: 45,
      lastAnalyzed: '2024-01-21',
      healthScore: 88
    }
  ],
  systemMetrics: {
    totalDesignDocs: 47,
    implementationPlansGenerated: 23,
    rolloutStrategiesGenerated: 18,
    activeLaunches: 3,
    successfulLaunches: 12,
    decisionsExtractedThisMonth: 234,
    avgTimeToImplementation: 8.5,
    securityReviewsPending: 2
  }
};

const statusConfig = {
  design_doc_generated: { 
    label: 'Design Complete', 
    color: 'bg-blue-100 text-blue-800', 
    icon: DocumentTextIcon 
  },
  implementation_plan_ready: { 
    label: 'Plan Ready', 
    color: 'bg-purple-100 text-purple-800', 
    icon: CogIcon 
  },
  implementation_in_progress: { 
    label: 'In Development', 
    color: 'bg-yellow-100 text-yellow-800', 
    icon: ClockIcon 
  },
  implementation_complete: { 
    label: 'Dev Complete', 
    color: 'bg-green-100 text-green-800', 
    icon: CheckCircleIcon 
  },
  rollout_strategy_ready: { 
    label: 'Launch Ready', 
    color: 'bg-emerald-100 text-emerald-800', 
    icon: RocketLaunchIcon 
  },
  monitoring: { 
    label: 'Monitoring', 
    color: 'bg-orange-100 text-orange-800', 
    icon: EyeIcon 
  },
  graduated: { 
    label: 'Graduated', 
    color: 'bg-gray-100 text-gray-800', 
    icon: CheckCircleIcon 
  }
};

const riskConfig = {
  low: { color: 'text-green-600', bgColor: 'bg-green-50', label: 'Low Risk' },
  medium: { color: 'text-yellow-600', bgColor: 'bg-yellow-50', label: 'Medium Risk' },
  high: { color: 'text-red-600', bgColor: 'bg-red-50', label: 'High Risk' },
  critical: { color: 'text-red-800', bgColor: 'bg-red-100', label: 'Critical Risk' }
};

export default function LaunchDashboard() {
  const [selectedView, setSelectedView] = useState<'pm' | 'release' | 'security' | 'cto'>('pm');
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  const getPersonaConfig = (persona: string) => {
    const configs = {
      pm: {
        title: 'Product Manager View',
        icon: UserGroupIcon,
        color: 'text-blue-600',
        focusAreas: ['User Impact', 'Feature Readiness', 'Business Value']
      },
      release: {
        title: 'Release Manager View', 
        icon: CloudArrowUpIcon,
        color: 'text-purple-600',
        focusAreas: ['Deployment Status', 'Risk Assessment', 'Rollout Progress']
      },
      security: {
        title: 'Security Engineer View',
        icon: ShieldCheckIcon, 
        color: 'text-red-600',
        focusAreas: ['Security Reviews', 'Risk Analysis', 'Compliance']
      },
      cto: {
        title: 'CTO View',
        icon: ChartBarIcon,
        color: 'text-green-600', 
        focusAreas: ['System Health', 'Strategic Overview', 'Team Velocity']
      }
    };
    return configs[persona as keyof typeof configs];
  };

  const currentPersona = getPersonaConfig(selectedView);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">ArchKnow Launch Command Center</h1>
              <p className="mt-2 text-gray-600">
                Real-time visibility into feature development, launch readiness, and system health
              </p>
            </div>
            
            {/* Persona Switcher */}
            <div className="flex items-center space-x-4">
              <select
                value={selectedView}
                onChange={(e) => setSelectedView(e.target.value as typeof selectedView)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="pm">Product Manager</option>
                <option value="release">Release Manager</option>  
                <option value="security">Security Engineer</option>
                <option value="cto">CTO</option>
              </select>
              
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as typeof timeRange)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
            </div>
          </div>
          
          {/* Persona Header */}
          <div className="pb-6">
            <div className="flex items-center space-x-3">
              <currentPersona.icon className={`h-6 w-6 ${currentPersona.color}`} />
              <h2 className={`text-xl font-semibold ${currentPersona.color}`}>
                {currentPersona.title}
              </h2>
            </div>
            <div className="mt-2 flex flex-wrap gap-2">
              {currentPersona.focusAreas.map((area) => (
                <span key={area} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                  {area}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Ready for Launch"
            value={mockData.featuresPendingLaunch.filter(f => f.status === 'rollout_strategy_ready').length}
            subtitle="Features ready to deploy"
            icon={RocketLaunchIcon}
            color="text-green-600"
            trend="+2 this week"
          />
          <MetricCard
            title="In Development"  
            value={mockData.featuresPendingLaunch.filter(f => f.status.includes('progress')).length}
            subtitle="Active implementation"
            icon={CogIcon}
            color="text-blue-600"
            trend="On track"
          />
          <MetricCard
            title="Pending Security Review"
            value={mockData.systemMetrics.securityReviewsPending}
            subtitle="Awaiting security approval"
            icon={ShieldCheckIcon}
            color="text-yellow-600"
            trend="2 overdue"
          />
          <MetricCard
            title="Active Monitoring"
            value={mockData.systemMetrics.activeLaunches}
            subtitle="Recently launched features"
            icon={EyeIcon}
            color="text-purple-600"
            trend="All healthy"
          />
        </div>

        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          {/* Features Pending Launch */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Features Pending Launch</h3>
              <p className="text-sm text-gray-600 mt-1">
                Features with generated design docs and implementation plans
              </p>
            </div>
            <div className="divide-y divide-gray-200">
              {mockData.featuresPendingLaunch.map((feature) => (
                <FeaturePendingCard key={feature.id} feature={feature} persona={selectedView} />
              ))}
            </div>
          </div>

          {/* Recent Launches */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Recent Launches</h3>
              <p className="text-sm text-gray-600 mt-1">
                Features launched in the last {timeRange}
              </p>
            </div>
            <div className="divide-y divide-gray-200">
              {mockData.recentLaunches.map((launch) => (
                <RecentLaunchCard key={launch.id} launch={launch} persona={selectedView} />
              ))}
            </div>
          </div>
        </div>

        {/* Repository Health & System Status */}
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Repository Health */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Repository Health</h3>
              <p className="text-sm text-gray-600 mt-1">
                Analysis progress and decision extraction status
              </p>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Repository
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Analysis Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Decisions Extracted
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Health Score
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {mockData.repositoryHealth.map((repo) => (
                    <tr key={repo.repository}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{repo.repository}</div>
                        <div className="text-sm text-gray-500">Last: {repo.lastAnalyzed}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${repo.analysisProgress}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-900">{repo.analysisProgress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {repo.decisionsExtracted} / {repo.totalPRs}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          repo.healthScore >= 90 ? 'bg-green-100 text-green-800' :
                          repo.healthScore >= 70 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {repo.healthScore}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* System Overview */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">System Overview</h3>
            </div>
            <div className="p-6 space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Design Documents</span>
                <span className="text-lg font-semibold text-gray-900">{mockData.systemMetrics.totalDesignDocs}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Implementation Plans</span>
                <span className="text-lg font-semibold text-gray-900">{mockData.systemMetrics.implementationPlansGenerated}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Rollout Strategies</span>
                <span className="text-lg font-semibold text-gray-900">{mockData.systemMetrics.rolloutStrategiesGenerated}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Successful Launches</span>
                <span className="text-lg font-semibold text-green-600">{mockData.systemMetrics.successfulLaunches}</span>
              </div>
              <div className="pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Avg. Time to Implementation</span>
                  <span className="text-lg font-semibold text-blue-600">{mockData.systemMetrics.avgTimeToImplementation} days</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: number;
  subtitle: string;
  icon: React.ComponentType<any>;
  color: string;
  trend: string;
}

function MetricCard({ title, value, subtitle, icon: Icon, color, trend }: MetricCardProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className={`h-8 w-8 ${color}`} />
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-gray-900">{value}</div>
            </dd>
            <dd className="text-sm text-gray-600">{subtitle}</dd>
            <dd className="text-xs text-gray-500 mt-1">{trend}</dd>
          </dl>
        </div>
      </div>
    </div>
  );
}

interface FeaturePendingCardProps {
  feature: any;
  persona: string;
}

function FeaturePendingCard({ feature, persona }: FeaturePendingCardProps) {
  const statusInfo = statusConfig[feature.status as keyof typeof statusConfig];
  const riskInfo = riskConfig[feature.riskLevel as keyof typeof riskConfig];
  
  const getPersonaSpecificInfo = () => {
    switch (persona) {
      case 'pm':
        return (
          <div className="mt-2 text-sm text-gray-600">
            <div>User Journeys: {feature.userJourneys} • Impact: {feature.businessImpact}</div>
          </div>
        );
      case 'release':
        return (
          <div className="mt-2 text-sm text-gray-600">
            <div>Milestones: {feature.milestones.completed}/{feature.milestones.total} • PRs: {feature.prsMerged}</div>
          </div>
        );
      case 'security':
        return (
          <div className="mt-2 text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span>Security Review: {feature.securityReviewRequired ? '⚠️ Required' : '✅ Not Required'}</span>
              <span className={riskInfo.color}>Risk: {riskInfo.label}</span>
            </div>
          </div>
        );
      case 'cto':
        return (
          <div className="mt-2 text-sm text-gray-600">
            <div>Decisions: {feature.decisionsExtracted} • Technical Risk: {feature.riskLevel}</div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="px-6 py-4 hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <h4 className="text-sm font-medium text-gray-900">{feature.title}</h4>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}>
              <statusInfo.icon className="w-3 h-3 mr-1" />
              {statusInfo.label}
            </span>
          </div>
          <div className="mt-1 text-sm text-gray-500">{feature.repository}</div>
          {getPersonaSpecificInfo()}
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-right text-xs text-gray-500">
            <div>Owner: {feature.owner.split('@')[0]}</div>
            <div>Updated: {feature.lastActivity}</div>
          </div>
          <ChevronRightIcon className="h-5 w-5 text-gray-400" />
        </div>
      </div>
    </div>
  );
}

interface RecentLaunchCardProps {
  launch: any;
  persona: string;
}

function RecentLaunchCard({ launch, persona }: RecentLaunchCardProps) {
  const statusInfo = statusConfig[launch.status as keyof typeof statusConfig];
  
  const getStatusIcon = () => {
    if (launch.status === 'monitoring') return PlayCircleIcon;
    if (launch.status === 'graduated') return CheckCircleIcon;
    return ClockIcon;
  };

  const StatusIcon = getStatusIcon();

  const getPersonaSpecificMetrics = () => {
    switch (persona) {
      case 'pm':
        return (
          <div className="mt-2 text-sm text-gray-600">
            <div>User Adoption: {launch.metrics.userAdoption}% • PRs: {launch.prsMerged}</div>
          </div>
        );
      case 'release':
        return (
          <div className="mt-2 text-sm text-gray-600">
            <div>Phase: {launch.rolloutPhase.replace('_', ' ')} • Progress: {launch.rolloutProgress}%</div>
          </div>
        );
      case 'security':
        return (
          <div className="mt-2 text-sm text-gray-600">
            <div>Error Rate: {launch.metrics.errorRate}% • Alerts: {launch.metrics.alerts}</div>
          </div>
        );
      case 'cto':
        return (
          <div className="mt-2 text-sm text-gray-600">
            <div>P99: {launch.metrics.p99Latency}ms • Decisions: {launch.decisionsExtracted}</div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="px-6 py-4 hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <h4 className="text-sm font-medium text-gray-900">{launch.title}</h4>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}>
              <StatusIcon className="w-3 h-3 mr-1" />
              {statusInfo.label}
            </span>
          </div>
          <div className="mt-1 text-sm text-gray-500">{launch.repository}</div>
          {getPersonaSpecificMetrics()}
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-right text-xs text-gray-500">
            <div>Owner: {launch.owner.split('@')[0]}</div>
            <div>Launched: {launch.launchedDate}</div>
          </div>
          <ChevronRightIcon className="h-5 w-5 text-gray-400" />
        </div>
      </div>
    </div>
  );
} 