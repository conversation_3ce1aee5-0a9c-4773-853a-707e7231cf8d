'use client';

import { Suspense } from 'react';
import { BacklogGroomingFlow } from '@/components/BacklogGroomingFlow';
import Link from 'next/link';

function BacklogGroomingContent() {
    return (
        <main className="flex min-h-screen flex-col items-center p-4 md:p-8 lg:p-16 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
            {/* Header */}
            <div className="z-10 max-w-6xl w-full items-center justify-between font-mono text-sm lg:flex mb-8">
                <p className="text-lg font-semibold fixed left-0 top-0 flex w-full justify-center border-b border-gray-300 bg-gradient-to-b from-zinc-200 pb-4 pt-6 backdrop-blur-2xl dark:border-neutral-800 dark:bg-zinc-800/30 dark:from-inherit lg:static lg:w-auto lg:rounded-xl lg:border lg:bg-gray-200 lg:p-4 lg:dark:bg-zinc-800/30">
                <Link href="/">
                    <span>
                        <span className="text-zinc-900 dark:text-zinc-100">Nuv</span>
                        <span className="text-amber-500 dark:text-amber-400">ineer</span>
                        <span className="text-zinc-900 dark:text-zinc-100">&gt;_</span>
                    </span>
                </Link>
                <span className="ml-2 text-gray-500 dark:text-gray-400">// AI-Assisted Backlog Grooming</span>
                </p>
                {/* Placeholder for potential user/auth status */}
            </div>

            <div className="w-full max-w-5xl">
                <BacklogGroomingFlow />
            </div>
        </main>
    );
}


export default function BacklogGroomingPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <BacklogGroomingContent />
    </Suspense>
  );
} 