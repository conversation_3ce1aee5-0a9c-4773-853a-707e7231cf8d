'use client';

import { useState, useEffect } from 'react';
import { createBrowserClient } from '@supabase/ssr'; // Import Supabase client creator
import type { User } from '@supabase/supabase-js'; // Import User type

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

interface ApiKey {
  id: string;
  createdAt: string; // Or Date object
  prefix: string;
}

export default function SettingsPage() {
  const [user, setUser] = useState<User | null>(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [keys, setKeys] = useState<ApiKey[]>([]);
  const [newKey, setNewKey] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
      setAuthLoading(false);
    };
    fetchUser();
  }, [supabase.auth]);

  const fetchKeys = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/settings/keys');
      if (!response.ok) {
        throw new Error('Failed to fetch API keys');
      }
      const data = await response.json();
      setKeys(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const generateKey = async () => {
    setIsLoading(true);
    setError(null);
    setNewKey(null);
    try {
      const response = await fetch('/api/settings/keys', { method: 'POST' });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate API key');
      }
      const data = await response.json();
      setNewKey(data.apiKey); // Display the new key
      fetchKeys(); // Refresh the list
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const revokeKey = async (keyId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/settings/keys/${keyId}`, { method: 'DELETE' });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to revoke API key');
      }
      fetchKeys(); // Refresh the list
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchKeys();
    }
  }, [user]);

  if (authLoading) {
    return <div>Loading authentication state...</div>;
  }

  if (!user) {
    return <div>Please log in to manage API keys.</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">API Key Settings</h1>

      {error && <p className="text-red-500 mb-4">Error: {error}</p>}

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Generate New API Key</CardTitle>
          <CardDescription>
            Create a new API key to access your decision records programmatically.
            Store this key securely, it will not be shown again.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {newKey ? (
            <div>
              <p className="mb-2 font-semibold">Your new API Key:</p>
              <Input type="text" value={newKey} readOnly className="mb-2 font-mono" />
              <p className="text-sm text-muted-foreground">Copy this key and store it securely. You won't see it again.</p>
            </div>
          ) : (
            <Button onClick={generateKey} disabled={isLoading}>
              {isLoading ? 'Generating...' : 'Generate Key'}
            </Button>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Existing API Keys</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading && keys.length === 0 && <p>Loading keys...</p>}
          {!isLoading && keys.length === 0 && <p>No API keys found.</p>}
          {keys.length > 0 && (
            <ul className="space-y-2">
              {keys.map((key) => (
                <li key={key.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <span className="font-mono text-sm">{key.prefix}...</span>
                    <span className="text-xs text-muted-foreground ml-2">
                      Created: {new Date(key.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => revokeKey(key.id)}
                    disabled={isLoading}
                  >
                    Revoke
                  </Button>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 