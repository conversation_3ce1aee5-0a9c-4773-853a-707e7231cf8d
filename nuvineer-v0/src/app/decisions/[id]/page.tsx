'use client'

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation'; // Use useParams for client components
import Link from 'next/link';

// Interfaces (can be shared in a types file later)
interface DecisionData {
    id: string;
    metadata?: Record<string, any>; // Adjust based on actual API response structure
}

interface Risk {
    severity: string;
    description: string;
    mitigation?: string;
}


export default function DecisionDetailPage() {
    const params = useParams();
    const id = params?.id as string; // Get ID from dynamic route segment
    const [decision, setDecision] = useState<DecisionData | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!id) {
            setIsLoading(false);
            setError('Decision ID not found in URL.');
            return;
        }

        setIsLoading(true);
        setError(null);

        // Fetch decision data from the API endpoint
        // Assumes the API exists at /api/decisions/[id]
        fetch(`/api/decisions/${encodeURIComponent(id)}`)
            .then(async res => {
                if (!res.ok) {
                    const errorBody = await res.json().catch(() => ({ error: `HTTP error! status: ${res.status}` }));
                    throw new Error(errorBody?.error || `HTTP error! status: ${res.status}`);
                }
                return res.json();
            })
            .then(data => {
                if (data.success && data.decision) {
                    setDecision(data.decision);
                } else {
                    throw new Error(data.error || 'Decision data not found in API response.');
                }
            })
            .catch(err => {
                console.error("Error fetching decision:", err);
                setError(err.message);
            })
            .finally(() => {
                setIsLoading(false);
            });

    }, [id]); // Re-run effect if ID changes

    // Helper to parse risks (Removed filtering)
    const parseRisks = (risksString: string | undefined): Risk[] => {
        if (!risksString || typeof risksString !== 'string') return [];
        try {
            const risks = JSON.parse(risksString);
            if (Array.isArray(risks)) {
                // Return all valid risks
                return risks.filter(risk => risk && risk.severity && risk.description) as Risk[];
            }
        } catch (e) {
            console.error("Error parsing risks_extracted JSON on page:", e);
        }
        return [];
    };

    const renderMetadataField = (label: string, value: any) => {
        if (value === undefined || value === null || (typeof value === 'string' && value.trim() === '') || (Array.isArray(value) && value.length === 0)) return null;
        
        let displayValue: React.ReactNode;

        if (typeof value === 'string' && (value.startsWith('http') || value.startsWith('/'))) {
            displayValue = (
                <Link href={value} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline break-all">
                    {value}
                </Link>
            );
        } else if (Array.isArray(value)) {
            // Render arrays as a list
            displayValue = (
                <ul className="list-disc list-inside pl-1 space-y-1">
                    {value.map((item, index) => (
                        <li key={index} className="text-sm text-gray-800 dark:text-gray-200 break-words">
                            {typeof item === 'object' ? JSON.stringify(item) : String(item)}
                        </li>
                    ))}
                </ul>
            );
        } else {
            // Render other types (string, number, object) in a pre block
            displayValue = (
                <pre className="text-sm p-2 bg-gray-100 dark:bg-gray-800 rounded overflow-x-auto whitespace-pre-wrap break-words">
                    {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                </pre>
            );
        }

        return (
            <div className="mb-4">
                <h4 className="text-lg font-semibold mb-1 text-gray-700 dark:text-gray-300">{label}</h4>
                {displayValue}
            </div>
        );
    };

    const allRisks = parseRisks(decision?.metadata?.risks_extracted);

    return (
        <div className="container mx-auto p-4 md:p-8 max-w-4xl">
            <h1 className="text-3xl font-bold mb-6 border-b pb-3 text-gray-800 dark:text-gray-100">Architectural Decision Details</h1>

            {isLoading && <p className="text-center text-gray-500 dark:text-gray-400">Loading decision...</p>}
            {error && <p className="text-center text-red-600 dark:text-red-400">Error: {error}</p>}

            {decision && !isLoading && (
                <div className="bg-white dark:bg-gray-900 shadow-md rounded-lg p-6">
                    <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-50">{decision.metadata?.title || 'Untitled Decision'}</h2>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">ID: {decision.id}</p>

                    {renderMetadataField('Description', decision.metadata?.description)}
                    {renderMetadataField('Rationale', decision.metadata?.rationale)}
                    {renderMetadataField('Implications', decision.metadata?.implications)}
                    {renderMetadataField('Related Files', decision.metadata?.related_files)} 
                    {renderMetadataField('Domain Concepts', decision.metadata?.domain_concepts)}
                    {renderMetadataField('Pull Request', decision.metadata?.pr_url)}
                    {/* Render other known fields if needed */}
                    {/* {renderMetadataField('PR Number', decision.metadata?.pr_number)} */}
                    {/* {renderMetadataField('Confidence Score', decision.metadata?.confidence_score)} */}
                    {/* {renderMetadataField('PR Merged At', decision.metadata?.pr_merged_at ? new Date(decision.metadata.pr_merged_at).toLocaleString() : null)} */}


                    {/* Display All Risks */}
                    {allRisks.length > 0 && (
                        <div className="mb-4">
                            <h4 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">Risks</h4>
                            <ul className="list-none p-0 space-y-2">
                                {allRisks.map((risk, index) => {
                                    let borderColorClass = 'border-gray-400 bg-gray-50 dark:bg-gray-800/30';
                                    let textColorClass = 'text-gray-700 dark:text-gray-300';
                                    const severityLower = risk.severity?.toLowerCase();
                                    if (severityLower === 'high') {
                                        borderColorClass = 'border-red-500 bg-red-50 dark:bg-red-900/30';
                                        textColorClass = 'text-red-700 dark:text-red-300';
                                    } else if (severityLower === 'medium') {
                                        borderColorClass = 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/30';
                                        textColorClass = 'text-yellow-700 dark:text-yellow-300';
                                    } else if (severityLower === 'low') {
                                         borderColorClass = 'border-green-500 bg-green-50 dark:bg-green-900/30';
                                         textColorClass = 'text-green-700 dark:text-green-300';
                                    }

                                    return (
                                        <li key={index} className={`p-3 rounded border-l-4 ${borderColorClass}`}>
                                            <strong className={`block mb-1 ${textColorClass}`}>{risk.severity?.toUpperCase()}: {risk.description}</strong>
                                            {risk.mitigation && <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Mitigation: {risk.mitigation}</p>}
                                        </li>
                                    );
                                })}
                            </ul>
                        </div>
                    )}

                    {/* Add any other specific rendering logic here */}

                    <div className="mt-6 pt-4 border-t">
                        <Link href="/" className="text-blue-600 dark:text-blue-400 hover:underline">
                            &larr; Back to Decisions List
                        </Link>
                    </div>
                </div>
            )}
        </div>
    );
} 