'use client';

import { useEffect, useState, useRef, useMemo } from 'react';
import { useParams } from 'next/navigation';
import {
  DocumentTextIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowLeftIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  LinkIcon,
} from '@heroicons/react/24/outline';
import type { WizardState } from '../../../../types/design-doc-wizard';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import TaskAnalysisFormatter from '@/components/design-doc-wizard/TaskAnalysisFormatter';
import { DecisionLinkedText, DecisionReference } from '../../../../utils/decisionUtils';
import DecisionDetailModal from '../../../../components/DecisionDetailModal';
import { useDecisions, useProjectContextWithDebug } from '../../../../hooks/useDecisions';

interface SessionData {
  wizard_state: WizardState;
  title: string;
  github_issue_url?: string;
  created_at: string;
  updated_at: string;
  installation_id: number;
  repositories: {
    name: string;
    owner: string;
  };
}

export default function DesignDocSessionView() {
  const params = useParams();
  const sessionId = params?.id as string;
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['task-definition']));
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  // Decision linking state
  const [selectedDecisionForModal, setSelectedDecisionForModal] = useState<string | null>(null);

  // Dark mode detection
  useEffect(() => {
    const checkDarkMode = () => {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDarkMode(isDark);
      
      // Update document class for Tailwind dark mode
      if (isDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    };

    // Check initial state
    checkDarkMode();

    // Listen for changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', checkDarkMode);

    return () => mediaQuery.removeEventListener('change', checkDarkMode);
  }, []);

  // Extract repository information for decision linking
  const repositorySlug = sessionData ? `${sessionData.repositories.owner}/${sessionData.repositories.name}` : undefined;
  const installationId = sessionData ? sessionData.installation_id : undefined;
  
  // Use project context with debugging
  const { repositorySlug: finalRepoSlug, installationId: finalInstallationId } = useProjectContextWithDebug(repositorySlug, installationId);
  
  // Collect all text that might contain decision references
  const textsToSearch = useMemo(() => {
    const allTexts: (string | string[])[] = [];
    
    if (sessionData?.wizard_state) {
      const wizardState = sessionData.wizard_state;
      
      // Decision points - most likely to contain decision references
      if (wizardState.decisionPoints) {
        wizardState.decisionPoints.forEach(decision => {
          if (decision.rationale) allTexts.push(decision.rationale);
          if (decision.recommendation_rationale) allTexts.push(decision.recommendation_rationale);
          
          decision.options?.forEach(option => {
            if (option.alignmentJustification) allTexts.push(option.alignmentJustification);
            if (option.pros && option.pros.length > 0) allTexts.push(option.pros);
            if (option.cons && option.cons.length > 0) allTexts.push(option.cons);
            if (option.mitigations && option.mitigations.length > 0) allTexts.push(option.mitigations);
            if (option.alignmentReasoning) allTexts.push(option.alignmentReasoning);
            if (option.debuggingComplexity) allTexts.push(option.debuggingComplexity);
          });
        });
      }
      
      // Generated design document
      if (wizardState.generatedDoc) {
        const doc = wizardState.generatedDoc;
        if (doc.goals) allTexts.push(doc.goals);
        if (doc.non_goals) allTexts.push(doc.non_goals);
        
        if (doc.high_level_design) {
          if (doc.high_level_design.overall_system_overview) allTexts.push(doc.high_level_design.overall_system_overview);
          if (doc.high_level_design.core_architectural_choices) {
            doc.high_level_design.core_architectural_choices.forEach((choice: any) => {
              if (choice.recommended_approach_description) allTexts.push(choice.recommended_approach_description);
              if (choice.justification_and_context) allTexts.push(choice.justification_and_context);
            });
          }
          if (doc.high_level_design.data_model_changes) allTexts.push(doc.high_level_design.data_model_changes);
          if (doc.high_level_design.security_considerations) allTexts.push(doc.high_level_design.security_considerations);
        }
        
        if (doc.success_metrics) allTexts.push(doc.success_metrics);
        if (doc.referenced_decisions) {
          doc.referenced_decisions.forEach((decision: any) => {
            if (decision.summary_of_relevance_in_this_design) allTexts.push(decision.summary_of_relevance_in_this_design);
            if (decision.dev_prompt) allTexts.push(decision.dev_prompt);
            if (decision.follows_standard_practice_reason) allTexts.push(decision.follows_standard_practice_reason);
          });
        }
      }
    }
    
    return allTexts;
  }, [sessionData]);

  // Fetch decisions based on references found in the text
  const { decisions, isLoading: isLoadingDecisions, error: decisionsError } = useDecisions({
    repositorySlug: finalRepoSlug,
    installationId: finalInstallationId,
    texts: textsToSearch
  });

  const handleDecisionClick = (decisionId: string) => {
    setSelectedDecisionForModal(decisionId);
  };

  // Determine the next appropriate wizard step based on current state
  const getNextWizardStep = () => {
    if (!sessionData?.wizard_state || !sessionId) return '/design-doc-wizard';
    
    const state = sessionData.wizard_state;
    
    // If rollout strategy exists, we're done - go to review/summary
    if (state.rolloutStrategy) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=review-generation`;
    }
    
    // If implementation plan exists but no rollout strategy, go to rollout step
    if (state.implementationPlan && !state.rolloutStrategy) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=review-generation`;
    }
    
    // If design doc exists but no implementation plan, go to implementation plan step
    if (state.generatedDoc && !state.implementationPlan) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=review-generation`;
    }
    
    // If decisions are made but no design doc, go to document generation
    if (state.decisionPoints && state.decisionPoints.length > 0 && !state.generatedDoc) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=review-generation`;
    }
    
    // If decisions exist but not all are completed, go to decision review
    if (state.decisionPoints && state.decisionPoints.length > 0) {
      const incompleteDecisions = state.decisionPoints.some(d => !d.selectedOption);
      if (incompleteDecisions) {
        return `/design-doc-wizard?sessionId=${sessionId}&step=decision-making`;
      }
    }
    
    // If strategic assessment done but no decisions, go to decision discovery
    if (state.strategicAssessment && (!state.decisionPoints || state.decisionPoints.length === 0)) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=decision-discovery`;
    }
    
    // If task analysis done but no strategic assessment, go to strategic assessment
    if (state.taskAnalysis && !state.strategicAssessment) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=strategic-assessment`;
    }
    
    // If user journeys done but no task analysis, go to task analysis  
    if (state.userJourneys && state.userJourneys.length > 0 && !state.taskAnalysis) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=task-verification`;
    }
    
    // If task details done but no user journeys, go to user journeys
    if (state.taskDetails && (!state.userJourneys || state.userJourneys.length === 0)) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=user-journey-definition`;
    }
    
    // If task details incomplete, go to task definition
    if (!state.taskDetails || !state.taskDetails.title || !state.taskDetails.description) {
      return `/design-doc-wizard?sessionId=${sessionId}&step=task-definition`;
    }
    
    // Default to beginning of wizard with session loaded
    return `/design-doc-wizard?sessionId=${sessionId}`;
  };

  const getNextStepDescription = () => {
    if (!sessionData?.wizard_state) return 'Start wizard';
    
    const state = sessionData.wizard_state;
    
    if (state.rolloutStrategy) return 'Review complete journey ✅';
    if (state.implementationPlan && !state.rolloutStrategy) return 'Create rollout strategy 🚀';
    if (state.generatedDoc && !state.implementationPlan) return 'Generate implementation plan 📋';
    if (state.decisionPoints && state.decisionPoints.length > 0 && !state.generatedDoc) return 'Generate design document 📝';
    
    if (state.decisionPoints && state.decisionPoints.length > 0) {
      const incompleteDecisions = state.decisionPoints.some(d => !d.selectedOption);
      if (incompleteDecisions) return 'Complete pending decisions ⚡';
    }
    
    if (state.strategicAssessment && (!state.decisionPoints || state.decisionPoints.length === 0)) return 'Discover technical decisions 🔍';  
    if (state.taskAnalysis && !state.strategicAssessment) return 'Strategic assessment 🎯';
    if (state.userJourneys && state.userJourneys.length > 0 && !state.taskAnalysis) return 'Task analysis 🔬';
    if (state.taskDetails && (!state.userJourneys || state.userJourneys.length === 0)) return 'Define user journeys 👥';
    if (!state.taskDetails || !state.taskDetails.title || !state.taskDetails.description) return 'Complete task definition ✍️';
    
    return 'Continue wizard 🚀';
  };

  useEffect(() => {
    const fetchSession = async () => {
      try {
        const response = await fetch(`/api/design-doc-wizard/session/${sessionId}`);
        if (response.ok) {
          const data = await response.json();
          setSessionData(data);
        } else {
          setError('Design document session not found');
        }
      } catch (err) {
        setError('Failed to load design document session');
      } finally {
        setIsLoading(false);
      }
    };

    if (sessionId) {
      fetchSession();
    }
  }, [sessionId]);

  // Initialize and render Mermaid diagrams
  useEffect(() => {
    const initializeMermaid = async () => {
      try {
        // Dynamically import mermaid
        const mermaid = await import('mermaid');
        
        // Initialize mermaid with theme support
        mermaid.default.initialize({
          startOnLoad: false,
          theme: isDarkMode ? 'dark' : 'default',
          themeVariables: isDarkMode ? {
            primaryColor: '#3b82f6',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#374151',
            lineColor: '#6b7280',
            sectionBkgColor: '#1f2937',
            altSectionBkgColor: '#111827',
            gridColor: '#374151',
            secondaryColor: '#1f2937',
            tertiaryColor: '#374151',
          } : {
            primaryColor: '#3b82f6',
            primaryTextColor: '#1f2937',
            primaryBorderColor: '#e5e7eb',
            lineColor: '#6b7280',
            secondaryColor: '#f3f4f6',
            tertiaryColor: '#ffffff',
            background: '#ffffff',
            mainBkg: '#ffffff',
            secondBkg: '#f9fafb',
            tertiaryBkg: '#f3f4f6'
          },
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
          },
          sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
          }
        });

        // Find all mermaid containers and render them
        const containers = document.querySelectorAll('.mermaid-diagram');
        containers.forEach(async (container, index) => {
          const element = container as HTMLElement;
          const diagramText = element.getAttribute('data-diagram');
          
          if (diagramText) {
            try {
              // Use the correct mermaid render API
              const renderResult = await mermaid.default.render(`mermaid-${index}`, diagramText);
              // Handle both string and object return types safely
              const svgContent = typeof renderResult === 'string' 
                ? renderResult 
                : (renderResult as any)?.svg || renderResult;
              element.innerHTML = svgContent;
            } catch (error) {
              console.error('Error rendering mermaid diagram:', error);
              element.innerHTML = `
                <div class="p-4 ${isDarkMode ? 'bg-red-900/30 border-red-800' : 'bg-red-50 border-red-200'} border rounded-lg">
                  <p class="${isDarkMode ? 'text-red-200' : 'text-red-800'} font-medium">Error rendering diagram</p>
                  <pre class="text-sm ${isDarkMode ? 'text-red-400' : 'text-red-600'} mt-2 whitespace-pre-wrap">${diagramText}</pre>
                </div>
              `;
            }
          }
        });
      } catch (error) {
        console.error('Error loading mermaid:', error);
      }
    };

    if (sessionData) {
      const timer = setTimeout(() => {
        // Clear existing mermaid diagrams
        document.querySelectorAll('.mermaid-diagram').forEach(el => {
          if (el.getAttribute('data-processed')) {
            el.removeAttribute('data-processed');
            const diagramText = el.getAttribute('data-diagram');
            if (diagramText) {
              el.innerHTML = `
                <div class="text-gray-500 ${isDarkMode ? 'dark:text-gray-600' : ''}">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                  <p class="text-sm">Rendering diagram...</p>
                </div>
              `;
            }
          }
        });
        initializeMermaid();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [sessionData, isDarkMode]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      const wasExpanded = newSet.has(sectionId);
      
      if (wasExpanded) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
        
        // Only scroll when expanding (not collapsing)
        // Add a small delay to ensure the section is rendered before scrolling
        setTimeout(() => {
          const element = document.getElementById(sectionId);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }
      
      return newSet;
    });
  };

  if (isLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-500 mx-auto mb-6"></div>
          <h2 className={`text-2xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Loading Design Document
          </h2>
          <p className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>
            Fetching your design document session...
          </p>
        </div>
      </div>
    );
  }

  if (error || !sessionData) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-6" />
          <h2 className={`text-2xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Session Not Found
          </h2>
          <p className={`mb-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {error || 'The design document session you are looking for does not exist or has been removed.'}
          </p>
          <a
            href="/design-doc-wizard"
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Start New Design Document
          </a>
        </div>
      </div>
    );
  }

  const { wizard_state, title, github_issue_url, created_at, updated_at, repositories } = sessionData;

    return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header */}
      <div className={`shadow-sm border-b ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <DocumentTextIcon className={`h-8 w-8 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} />
              <div>
                <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {title || 'Design Document'}
                </h1>
                <div className={`flex items-center space-x-4 text-sm mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <span>Repository: {repositories.owner}/{repositories.name}</span>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <ClockIcon className="h-4 w-4" />
                    <span>Created {new Date(created_at).toLocaleDateString()}</span>
                  </div>
                  {github_issue_url && (
                    <>
                      <span>•</span>
                      <a
                        href={github_issue_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`hover:underline flex items-center space-x-1 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}
                      >
                        <LinkIcon className="h-4 w-4" />
                        <span>View GitHub Issue</span>
                      </a>
                    </>
                  )}
                </div>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <a
                href={getNextWizardStep()}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center space-x-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <span>Open in Wizard</span>
              </a>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {getNextStepDescription()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Progress Overview */}
          <div className="lg:col-span-1">
            <div className={`rounded-lg shadow-sm border p-6 sticky top-8 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Feature Specification
              </h3>
              <div className="space-y-4">
                <ProgressStep
                  title="Task Definition"
                  completed={!!(wizard_state.taskDetails?.title && wizard_state.taskDetails?.description)}
                  description="Initial task and requirements"
                  onClick={() => toggleSection('task-definition')}
                  isExpanded={expandedSections.has('task-definition')}
                  isDarkMode={isDarkMode}
                />
                <ProgressStep
                  title="User Journeys"
                  completed={!!(wizard_state.userJourneys && wizard_state.userJourneys.length > 0)}
                  description="User experience mapping"
                  onClick={() => toggleSection('user-journeys')}
                  isExpanded={expandedSections.has('user-journeys')}
                  isDarkMode={isDarkMode}
                />
                <ProgressStep
                  title="Task Analysis"
                  completed={!!wizard_state.taskAnalysis}
                  description="Detailed analysis and scope"
                  onClick={() => toggleSection('task-analysis')}
                  isExpanded={expandedSections.has('task-analysis')}
                  isDarkMode={isDarkMode}
                />
                <ProgressStep
                  title="Strategic Assessment"
                  completed={!!wizard_state.strategicAssessment}
                  description="Strategic recommendations"
                  onClick={() => toggleSection('strategic-assessment')}
                  isExpanded={expandedSections.has('strategic-assessment')}
                  isDarkMode={isDarkMode}
                />
                <ProgressStep
                  title="Technical Decisions"
                  completed={!!(wizard_state.decisionPoints && wizard_state.decisionPoints.length > 0)}
                  description={`${wizard_state.decisionPoints?.length || 0} decisions made`}
                  onClick={() => toggleSection('technical-decisions')}
                  isExpanded={expandedSections.has('technical-decisions')}
                  isDarkMode={isDarkMode}
                />
                <ProgressStep
                  title="Design Document"
                  completed={!!wizard_state.generatedDoc}
                  description="Final design document"
                  onClick={() => toggleSection('design-document')}
                  isExpanded={expandedSections.has('design-document')}
                  isDarkMode={isDarkMode}
                />
                {wizard_state.implementationPlan && (
                  <ProgressStep
                    title="Implementation Plan"
                    completed={true}
                    description="Detailed implementation roadmap"
                    onClick={() => toggleSection('implementation-plan')}
                    isExpanded={expandedSections.has('implementation-plan')}
                    isDarkMode={isDarkMode}
                  />
                )}
                {wizard_state.rolloutStrategy && (
                  <ProgressStep
                    title="Rollout Strategy"
                    completed={true}
                    description="Deployment and rollout plan"
                    onClick={() => toggleSection('rollout-strategy')}
                    isExpanded={expandedSections.has('rollout-strategy')}
                    isDarkMode={isDarkMode}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="space-y-8">
              {/* Task Details */}
              {wizard_state.taskDetails && (wizard_state.taskDetails.title || wizard_state.taskDetails.description) && expandedSections.has('task-definition') && (
                <div id="task-definition">
                  <Section title="Task Definition" completed={!!(wizard_state.taskDetails.title && wizard_state.taskDetails.description)} isDarkMode={isDarkMode}>
                  {wizard_state.taskDetails.title && (
                    <div className="mb-4">
                      <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Title</h4>
                      <p className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{wizard_state.taskDetails.title}</p>
                    </div>
                  )}
                  {wizard_state.taskDetails.description && (
                    <div className="mb-4">
                      <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Description</h4>
                      <div className={`prose max-w-none ${isDarkMode ? 'prose-invert' : ''}`}>
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {wizard_state.taskDetails.description}
                        </ReactMarkdown>
                      </div>
                    </div>
                  )}
                  {wizard_state.taskDetails.initialIdeas && (
                    <div>
                      <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Initial Ideas</h4>
                      <div className={`prose max-w-none ${isDarkMode ? 'prose-invert' : ''}`}>
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {wizard_state.taskDetails.initialIdeas}
                        </ReactMarkdown>
                      </div>
                    </div>
                  )}
                  </Section>
                </div>
              )}

              {/* User Journeys */}
              {wizard_state.userJourneys && wizard_state.userJourneys.length > 0 && expandedSections.has('user-journeys') && (
                <div id="user-journeys">
                  <Section title="User Journeys" completed={true} isDarkMode={isDarkMode}>
                  <div className="space-y-4">
                    {wizard_state.userJourneys.map((journey, index) => (
                      <div key={index} className={`border rounded-lg p-4 ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                        <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          Journey {index + 1}: {journey.title}
                        </h4>
                        <div className={`prose max-w-none mb-3 ${isDarkMode ? 'prose-invert' : ''}`}>
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {journey.description}
                          </ReactMarkdown>
                        </div>
                        {journey.steps && journey.steps.length > 0 && (
                          <div>
                            <h5 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Steps:</h5>
                            <ol className="list-decimal list-inside space-y-1">
                              {journey.steps.map((step, stepIndex) => (
                                <li key={stepIndex} className={`prose max-w-none ${isDarkMode ? 'prose-invert' : ''}`}>
                                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                    {step}
                                  </ReactMarkdown>
                                </li>
                              ))}
                            </ol>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  </Section>
                </div>
              )}

              {/* Task Analysis */}
              {wizard_state.taskAnalysis && expandedSections.has('task-analysis') && (
                <div id="task-analysis">
                  <Section title="Task Analysis" completed={true} isDarkMode={isDarkMode}>
                  {typeof wizard_state.taskAnalysis === 'string' ? (
                    <div className={`prose max-w-none ${isDarkMode ? 'prose-invert' : ''}`}>
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {wizard_state.taskAnalysis}
                      </ReactMarkdown>
                    </div>
                  ) : (
                    <TaskAnalysisFormatter 
                      analysis={wizard_state.taskAnalysis} 
                      isDarkMode={isDarkMode} 
                    />
                  )}
                  </Section>
                </div>
              )}

              {/* Strategic Assessment */}
              {wizard_state.strategicAssessment && expandedSections.has('strategic-assessment') && (
                <div id="strategic-assessment">
                  <Section title="Strategic Assessment" completed={true} isDarkMode={isDarkMode}>
                  {typeof wizard_state.strategicAssessment === 'string' ? (
                    <div className={`prose max-w-none ${isDarkMode ? 'prose-invert' : ''}`}>
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {wizard_state.strategicAssessment}
                      </ReactMarkdown>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* Strategic Recommendation */}
                      <div className={`border rounded-lg p-6 ${isDarkMode ? 'bg-blue-900/20 border-blue-800' : 'bg-blue-50 border-blue-200'}`}>
                        <div className="flex items-center justify-between mb-4">
                          <h4 className={`text-lg font-semibold ${isDarkMode ? 'text-blue-200' : 'text-blue-800'}`}>
                            Strategic Recommendation: {wizard_state.strategicAssessment.recommendation}
                          </h4>
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            wizard_state.strategicAssessment.confidence_level === 'high' 
                              ? isDarkMode ? 'bg-green-800 text-green-200' : 'bg-green-100 text-green-800'
                              : wizard_state.strategicAssessment.confidence_level === 'medium'
                              ? isDarkMode ? 'bg-yellow-800 text-yellow-200' : 'bg-yellow-100 text-yellow-800'
                              : isDarkMode ? 'bg-red-800 text-red-200' : 'bg-red-100 text-red-800'
                          }`}>
                            {wizard_state.strategicAssessment.confidence_level} confidence
                          </span>
                        </div>
                        <p className={isDarkMode ? 'text-blue-200' : 'text-blue-700'}>
                          {wizard_state.strategicAssessment.strategic_rationale}
                        </p>
                      </div>

                      {/* Complexity Assessment */}
                      {wizard_state.strategicAssessment.complexity_assessment && (
                        <div className={`border rounded-lg p-6 ${isDarkMode ? 'bg-orange-900/20 border-orange-800' : 'bg-orange-50 border-orange-200'}`}>
                          <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-orange-200' : 'text-orange-800'}`}>Complexity Assessment</h4>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                              <h5 className={`font-medium mb-1 ${isDarkMode ? 'text-orange-300' : 'text-orange-700'}`}>Implementation:</h5>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                wizard_state.strategicAssessment.complexity_assessment.implementation_complexity === 'high' 
                                  ? isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'
                                  : wizard_state.strategicAssessment.complexity_assessment.implementation_complexity === 'medium'
                                  ? isDarkMode ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-100 text-yellow-800'
                                  : isDarkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                              }`}>
                                {wizard_state.strategicAssessment.complexity_assessment.implementation_complexity}
                              </span>
                            </div>
                            <div>
                              <h5 className={`font-medium mb-1 ${isDarkMode ? 'text-orange-300' : 'text-orange-700'}`}>Maintenance:</h5>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                wizard_state.strategicAssessment.complexity_assessment.maintenance_burden === 'high' 
                                  ? isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'
                                  : wizard_state.strategicAssessment.complexity_assessment.maintenance_burden === 'medium'
                                  ? isDarkMode ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-100 text-yellow-800'
                                  : isDarkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                              }`}>
                                {wizard_state.strategicAssessment.complexity_assessment.maintenance_burden}
                              </span>
                            </div>
                            <div>
                              <h5 className={`font-medium mb-1 ${isDarkMode ? 'text-orange-300' : 'text-orange-700'}`}>User Value:</h5>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                wizard_state.strategicAssessment.complexity_assessment.user_value_delivered === 'high' 
                                  ? isDarkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                                  : wizard_state.strategicAssessment.complexity_assessment.user_value_delivered === 'medium'
                                  ? isDarkMode ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-100 text-yellow-800'
                                  : isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'
                              }`}>
                                {wizard_state.strategicAssessment.complexity_assessment.user_value_delivered}
                              </span>
                            </div>
                          </div>
                          <p className={`text-sm ${isDarkMode ? 'text-orange-200' : 'text-orange-700'}`}>
                            {wizard_state.strategicAssessment.complexity_assessment.complexity_justification}
                          </p>
                        </div>
                      )}

                      {/* Alignment Analysis */}
                      {wizard_state.strategicAssessment.alignment_analysis && (
                        <div className={`border rounded-lg p-6 ${isDarkMode ? 'bg-green-900/20 border-green-800' : 'bg-green-50 border-green-200'}`}>
                          <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-green-200' : 'text-green-800'}`}>Alignment Analysis</h4>
                          <div className="space-y-3">
                            <div>
                              <h5 className={`font-medium mb-1 ${isDarkMode ? 'text-green-300' : 'text-green-700'}`}>Constitutional Alignment:</h5>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                wizard_state.strategicAssessment.alignment_analysis.constitutional_alignment === 'strong' 
                                  ? isDarkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                                  : wizard_state.strategicAssessment.alignment_analysis.constitutional_alignment === 'moderate'
                                  ? isDarkMode ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-100 text-yellow-800'
                                  : isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'
                              }`}>
                                {wizard_state.strategicAssessment.alignment_analysis.constitutional_alignment}
                              </span>
                            </div>
                            {wizard_state.strategicAssessment.alignment_analysis.priority_alignment && (
                              <div>
                                <h5 className={`font-medium mb-1 ${isDarkMode ? 'text-green-300' : 'text-green-700'}`}>Priority Alignment:</h5>
                                <p className={`text-sm ${isDarkMode ? 'text-green-200' : 'text-green-600'}`}>
                                  {wizard_state.strategicAssessment.alignment_analysis.priority_alignment}
                                </p>
                              </div>
                            )}
                            {wizard_state.strategicAssessment.alignment_analysis.stage_appropriateness && (
                              <div>
                                <h5 className={`font-medium mb-1 ${isDarkMode ? 'text-green-300' : 'text-green-700'}`}>Stage Appropriateness:</h5>
                                <p className={`text-sm ${isDarkMode ? 'text-green-200' : 'text-green-600'}`}>
                                  {wizard_state.strategicAssessment.alignment_analysis.stage_appropriateness}
                                </p>
                              </div>
                            )}
                            {wizard_state.strategicAssessment.alignment_analysis.strategic_concerns && wizard_state.strategicAssessment.alignment_analysis.strategic_concerns.length > 0 && (
                              <div>
                                <h5 className={`font-medium mb-2 ${isDarkMode ? 'text-green-300' : 'text-green-700'}`}>Strategic Concerns:</h5>
                                <ul className="list-disc list-inside space-y-1">
                                  {wizard_state.strategicAssessment.alignment_analysis.strategic_concerns.map((concern: string, index: number) => (
                                    <li key={index} className={`text-sm ${isDarkMode ? 'text-green-200' : 'text-green-600'}`}>{concern}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Recommended Alternatives */}
                      {wizard_state.strategicAssessment.recommended_alternatives && wizard_state.strategicAssessment.recommended_alternatives.length > 0 && (
                        <div className={`border rounded-lg p-6 ${isDarkMode ? 'bg-purple-900/20 border-purple-800' : 'bg-purple-50 border-purple-200'}`}>
                          <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-purple-200' : 'text-purple-800'}`}>Recommended Alternatives</h4>
                          <div className="space-y-4">
                            {wizard_state.strategicAssessment.recommended_alternatives.map((alternative: any, index: number) => (
                              <div key={index} className={`border rounded-lg p-4 ${isDarkMode ? 'border-purple-700 bg-purple-900/30' : 'border-purple-200 bg-purple-50'}`}>
                                <div className="flex items-center justify-between mb-2">
                                  <h5 className={`font-semibold ${isDarkMode ? 'text-purple-100' : 'text-purple-900'}`}>
                                    {alternative.approach}
                                  </h5>
                                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                                    alternative.effort_required === 'high' 
                                      ? isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'
                                      : alternative.effort_required === 'medium'
                                      ? isDarkMode ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-100 text-yellow-800'
                                      : isDarkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                                  }`}>
                                    {alternative.effort_required} effort
                                  </span>
                                </div>
                                <p className={`text-sm mb-2 ${isDarkMode ? 'text-purple-200' : 'text-purple-700'}`}>
                                  <strong>Value:</strong> {alternative.value_delivered}
                                </p>
                                <p className={`text-sm ${isDarkMode ? 'text-purple-300' : 'text-purple-600'}`}>
                                  <strong>Implementation:</strong> {alternative.implementation_notes}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Next Steps */}
                      {wizard_state.strategicAssessment.next_steps && (
                        <div className={`border rounded-lg p-4 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'}`}>
                          <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Next Steps</h4>
                          <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            {wizard_state.strategicAssessment.next_steps}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                  </Section>
                </div>
              )}

              {/* Technical Decisions */}
              {wizard_state.decisionPoints && wizard_state.decisionPoints.length > 0 && expandedSections.has('technical-decisions') && (
                <div id="technical-decisions">
                  <Section title="Technical Decisions" completed={true} isDarkMode={isDarkMode}>
                  <div className="space-y-6">
                    {wizard_state.decisionPoints.map((decision, index) => (
                      <DecisionCard 
                        key={index} 
                        decision={decision} 
                        repositorySlug={`${repositories.owner}/${repositories.name}`}
                        isDarkMode={isDarkMode}
                        decisions={decisions}
                        onDecisionClick={handleDecisionClick}
                      />
                    ))}
                  </div>
                  </Section>
                </div>
              )}

              {/* Generated Design Document */}
              {wizard_state.generatedDoc && expandedSections.has('design-document') && (
                <div id="design-document">
                  <Section title="Design Document" completed={true} isDarkMode={isDarkMode}>
                  <div className={`border rounded-lg p-6 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
                    {renderFormattedDesignDoc(wizard_state.generatedDoc, isDarkMode)}
                  </div>
                  </Section>
                </div>
              )}

              {/* Implementation Plan */}
              {wizard_state.implementationPlan && expandedSections.has('implementation-plan') && (
                <div id="implementation-plan">
                  <Section title="Implementation Plan" completed={true} isDarkMode={isDarkMode}>
                  <div className={`border rounded-lg p-6 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
                    <div className={`prose max-w-none ${isDarkMode ? 'prose-invert' : ''}`}>
                      <ReactMarkdown 
                        remarkPlugins={[remarkGfm]}
                        components={{
                          code: ({ className, children, ...props }: any) => {
                            const match = /language-(\w+)/.exec(className || '');
                            const language = match ? match[1] : '';
                            
                            if (language === 'mermaid') {
                              return (
                                <div className="mermaid my-4">
                                  {String(children).replace(/\n$/, '')}
                                </div>
                              );
                            }
                            
                            return (
                              <code className={className} {...props}>
                                {children}
                              </code>
                            );
                          }
                        }}
                      >
                        {formatImplementationPlan(wizard_state.implementationPlan)}
                      </ReactMarkdown>
                    </div>
                  </div>
                  </Section>
                </div>
              )}

              {/* Rollout Strategy */}
              {wizard_state.rolloutStrategy && expandedSections.has('rollout-strategy') && (
                <div id="rollout-strategy">
                  <Section title="Rollout Strategy" completed={true} isDarkMode={isDarkMode}>
                  <div className={`border rounded-lg p-6 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
                    <div className={`prose max-w-none ${isDarkMode ? 'prose-invert' : ''}`}>
                      <ReactMarkdown 
                        remarkPlugins={[remarkGfm]}
                        components={{
                          code: ({ className, children, ...props }: any) => {
                            const match = /language-(\w+)/.exec(className || '');
                            const language = match ? match[1] : '';
                            
                            if (language === 'mermaid') {
                              return (
                                <div className="mermaid my-4">
                                  {String(children).replace(/\n$/, '')}
                                </div>
                              );
                            }
                            
                            return (
                              <code className={className} {...props}>
                                {children}
                              </code>
                            );
                          }
                        }}
                      >
                        {formatRolloutStrategy(wizard_state.rolloutStrategy)}
                      </ReactMarkdown>
                    </div>
                  </div>
                  </Section>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Decision Detail Modal */}
      <DecisionDetailModal
        decision={selectedDecisionForModal ? decisions[selectedDecisionForModal] : null}
        isOpen={!!selectedDecisionForModal}
        onClose={() => setSelectedDecisionForModal(null)}
        repositorySlug={repositorySlug}
      />
    </div>
  );
}

function ProgressStep({ 
  title, 
  completed, 
  description, 
  onClick, 
  isExpanded,
  isDarkMode
}: { 
  title: string; 
  completed: boolean; 
  description: string; 
  onClick: () => void;
  isExpanded: boolean;
  isDarkMode: boolean;
}) {
  return (
    <button
      onClick={onClick}
      className={`w-full flex items-start space-x-3 text-left p-2 rounded-lg transition-colors ${
        isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
      }`}
    >
      <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
        completed 
          ? isDarkMode ? 'bg-green-900/30' : 'bg-green-100'
          : isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
      }`}>
        {completed ? (
          <CheckCircleIcon className={`h-4 w-4 ${isDarkMode ? 'text-green-400' : 'text-green-600'}`} />
        ) : (
          <div className={`w-2 h-2 rounded-full ${isDarkMode ? 'bg-gray-500' : 'bg-gray-400'}`} />
        )}
      </div>
      <div className="min-w-0 flex-1">
        <div className="flex items-center justify-between">
          <h4 className={`text-sm font-medium ${
            completed 
              ? isDarkMode ? 'text-white' : 'text-gray-900'
              : isDarkMode ? 'text-gray-400' : 'text-gray-500'
          }`}>
            {title}
          </h4>
          {isExpanded ? (
            <ChevronDownIcon className="h-4 w-4 text-gray-400" />
          ) : (
            <ChevronRightIcon className="h-4 w-4 text-gray-400" />
          )}
        </div>
        <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{description}</p>
      </div>
    </button>
  );
}

function Section({ title, completed, children, isDarkMode }: { title: string; completed: boolean; children: React.ReactNode; isDarkMode: boolean }) {
  return (
    <div className={`rounded-lg shadow-sm border p-6 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
      <div className="flex items-center space-x-3 mb-6">
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          completed 
            ? isDarkMode ? 'bg-green-900/30' : 'bg-green-100'
            : isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
        }`}>
          {completed ? (
            <CheckCircleIcon className={`h-5 w-5 ${isDarkMode ? 'text-green-400' : 'text-green-600'}`} />
          ) : (
            <div className={`w-3 h-3 rounded-full ${isDarkMode ? 'bg-gray-500' : 'bg-gray-400'}`} />
          )}
        </div>
        <h3 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{title}</h3>
      </div>
      {children}
    </div>
  );
}

function DecisionCard({ 
  decision, 
  repositorySlug, 
  isDarkMode, 
  decisions = {}, 
  onDecisionClick 
}: { 
  decision: any; 
  repositorySlug: string; 
  isDarkMode: boolean; 
  decisions?: Record<string, DecisionReference>; 
  onDecisionClick?: (decisionId: string) => void; 
}) {
  const [referencedDecisions, setReferencedDecisions] = useState<any[]>([]);

  useEffect(() => {
    // Fetch referenced decisions if there are any
    if (decision.rationale) {
      fetchReferencedDecisions(decision.rationale, repositorySlug);
    }
  }, [decision.rationale, repositorySlug]);

  const fetchReferencedDecisions = async (rationale: string, repoSlug: string) => {
    try {
      // Extract decision IDs from rationale text (assuming they're in a specific format)
      const decisionIdMatches = rationale.match(/decision-(\d+)/g);
      if (decisionIdMatches) {
        const response = await fetch(`/api/extension/decisions?repositorySlug=${repoSlug}`);
        if (response.ok) {
          const allDecisions = await response.json();
          const referenced = allDecisions.filter((d: any) => 
            decisionIdMatches.some(match => match.includes(d.id))
          );
          setReferencedDecisions(referenced);
        }
      }
    } catch (error) {
      console.warn('Failed to fetch referenced decisions:', error);
    }
  };

  return (
    <div className={`border rounded-lg p-6 ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
      <h4 className={`font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
        {decision.title}
      </h4>
      <div className={`prose max-w-none mb-4 ${isDarkMode ? 'prose-invert' : ''}`}>
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {decision.description}
        </ReactMarkdown>
      </div>
      
      {decision.options && decision.options.length > 0 && (
        <div className="space-y-3 mb-4">
          <h5 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Options Considered:</h5>
          {decision.options.map((option: any, optionIndex: number) => (
            <div 
              key={optionIndex} 
              className={`p-3 rounded-lg border ${
                decision.selectedOption === option.id
                  ? isDarkMode 
                    ? 'border-green-500 bg-green-900/20' 
                    : 'border-green-500 bg-green-50'
                  : isDarkMode 
                    ? 'border-gray-600 bg-gray-700' 
                    : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h6 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {option.name}
                </h6>
                {decision.selectedOption === option.id && (
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    isDarkMode 
                      ? 'bg-green-800 text-green-100' 
                      : 'bg-green-100 text-green-800'
                  }`}>
                    <CheckCircleIcon className="h-3 w-3 mr-1" />
                    Selected
                  </span>
                )}
              </div>
              <div className={`prose max-w-none text-sm mb-2 ${isDarkMode ? 'prose-invert' : ''}`}>
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {option.description}
                </ReactMarkdown>
              </div>
              
              {/* Show pros/cons for the selected option */}
              {decision.selectedOption === option.id && (
                <div className="mt-3 space-y-2">
                  {option.pros && option.pros.length > 0 && (
                    <div>
                      <div className={`text-xs font-medium uppercase tracking-wide ${
                        isDarkMode ? 'text-green-300' : 'text-green-700'
                      }`}>Pros</div>
                      <ul className={`list-disc list-inside text-sm mt-1 ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        {option.pros.map((pro: string, proIndex: number) => (
                          <li key={proIndex}>
                            {onDecisionClick ? (
                              <DecisionLinkedText 
                                text={pro}
                                decisions={decisions}
                                onDecisionClick={onDecisionClick}
                                className=""
                              />
                            ) : (
                              pro
                            )}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {option.cons && option.cons.length > 0 && (
                    <div>
                      <div className={`text-xs font-medium uppercase tracking-wide ${
                        isDarkMode ? 'text-red-300' : 'text-red-700'
                      }`}>Cons</div>
                      <ul className={`list-disc list-inside text-sm mt-1 ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        {option.cons.map((con: string, conIndex: number) => (
                          <li key={conIndex}>
                            {onDecisionClick ? (
                              <DecisionLinkedText 
                                text={con}
                                decisions={decisions}
                                onDecisionClick={onDecisionClick}
                                className=""
                              />
                            ) : (
                              con
                            )}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Decision Rationale */}
      {decision.rationale && (
        <div className={`mt-4 p-4 rounded-lg ${
          isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'
        }`}>
          <h5 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Decision Rationale</h5>
          <div className={`prose max-w-none text-sm ${isDarkMode ? 'prose-invert' : ''}`}>
            {onDecisionClick ? (
              <DecisionLinkedText 
                text={decision.rationale}
                decisions={decisions}
                onDecisionClick={onDecisionClick}
                className=""
              />
            ) : (
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {decision.rationale}
              </ReactMarkdown>
            )}
          </div>
          
          {/* Referenced Decisions */}
          {referencedDecisions.length > 0 && (
            <div className="mt-3">
              <h6 className={`text-xs font-medium uppercase tracking-wide mb-2 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Referenced Decisions
              </h6>
              <div className="space-y-1">
                {referencedDecisions.map((refDecision, refIndex) => (
                  <a
                    key={refIndex}
                    href={`/decisions/${refDecision.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`inline-flex items-center text-sm hover:underline ${
                      isDarkMode ? 'text-blue-400' : 'text-blue-600'
                    }`}
                  >
                    <LinkIcon className="h-3 w-3 mr-1" />
                    {refDecision.title}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// Render formatted design document (same as DesignDocumentViewer)
function renderFormattedDesignDoc(doc: any, isDarkMode: boolean) {
  return (
    <div className="space-y-8">
      {/* Title */}
      <div className={`border-b pb-4 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          {doc.title || 'Design Document'}
        </h1>
      </div>

      {/* Goals */}
      {doc.goals && doc.goals.length > 0 && (
        <section>
          <h2 className={`text-2xl font-semibold mb-4 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Goals</h2>
          <ul className="space-y-2">
            {doc.goals.map((goal: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-green-500 mr-2 mt-1">✓</span>
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{goal}</span>
              </li>
            ))}
          </ul>
        </section>
      )}

      {/* Non-Goals */}
      {doc.non_goals && doc.non_goals.length > 0 && (
        <section>
          <h2 className={`text-2xl font-semibold mb-4 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Non-Goals</h2>
          <ul className="space-y-2">
            {doc.non_goals.map((nonGoal: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-red-500 mr-2 mt-1">✗</span>
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{nonGoal}</span>
              </li>
            ))}
          </ul>
        </section>
      )}

      {/* High-Level Design */}
      {doc.high_level_design && (
        <section>
          <h2 className={`text-2xl font-semibold mb-4 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>High-Level Design</h2>
          
          {/* System Overview */}
          {doc.high_level_design.overall_system_overview && (
            <div className="mb-6">
              <h3 className={`text-xl font-medium mb-3 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>System Overview</h3>
              <div className={`border rounded-lg p-4 ${isDarkMode ? 'bg-blue-900/30 border-blue-800' : 'bg-blue-50 border-blue-200'}`}>
                <p className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
                  {doc.high_level_design.overall_system_overview}
                </p>
              </div>
            </div>
          )}

          {/* Process Flow Diagram */}
          {doc.high_level_design.process_flow_diagram_mermaid && doc.high_level_design.process_flow_diagram_mermaid !== 'N/A' && (
            <div className="mb-6">
              <h3 className={`text-xl font-medium mb-3 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Process Flow</h3>
              <div className={`p-6 rounded-lg border overflow-x-auto ${isDarkMode ? 'bg-gray-100 border-gray-600' : 'bg-white border-gray-200'}`}>
                <div 
                  className="mermaid-diagram flex justify-center items-center min-h-[200px]"
                  data-diagram={doc.high_level_design.process_flow_diagram_mermaid}
                >
                  <div className={`text-gray-500 ${isDarkMode ? 'dark:text-gray-600' : ''}`}>
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <p className="text-sm">Rendering diagram...</p>
                  </div>
                </div>
                
                <details className="mt-4">
                  <summary className={`text-sm cursor-pointer hover:text-gray-800 ${isDarkMode ? 'text-gray-500 dark:hover:text-gray-300' : 'text-gray-600'}`}>
                    Show diagram source
                  </summary>
                  <pre className={`mt-2 text-xs p-3 rounded overflow-x-auto ${isDarkMode ? 'text-gray-500 bg-gray-200' : 'text-gray-600 bg-gray-50'}`}>
                    {doc.high_level_design.process_flow_diagram_mermaid}
                  </pre>
                </details>
              </div>
            </div>
          )}

          {/* Core Architectural Choices */}
          {doc.high_level_design.core_architectural_choices && doc.high_level_design.core_architectural_choices.length > 0 && (
            <div className="mb-6">
              <h3 className={`text-xl font-medium mb-3 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Key Architectural Decisions</h3>
              <div className="space-y-4">
                {doc.high_level_design.core_architectural_choices.map((choice: any, index: number) => (
                  <div key={index} className={`border rounded-lg p-4 ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>
                    <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{choice.title}</h4>
                    <p className={`mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {choice.recommended_approach_description}
                    </p>
                    {choice.justification_and_context && (
                      <div className={`text-sm p-3 rounded border-l-4 border-blue-500 ${isDarkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-white'}`}>
                        <strong>Justification:</strong> {choice.justification_and_context}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Key Components */}
          {doc.high_level_design.key_components_and_responsibilities && doc.high_level_design.key_components_and_responsibilities.length > 0 && (
            <div className="mb-6">
              <h3 className={`text-xl font-medium mb-3 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Key Components</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {doc.high_level_design.key_components_and_responsibilities.map((component: any, index: number) => (
                  <div key={index} className={`border rounded-lg p-4 ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                    <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{component.name}</h4>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{component.responsibility}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Data Model Changes */}
          {doc.high_level_design.data_model_changes && doc.high_level_design.data_model_changes !== 'N/A' && (
            <div className="mb-6">
              <h3 className={`text-xl font-medium mb-3 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Data Model Changes</h3>
              <div className={`border rounded-lg p-4 ${isDarkMode ? 'bg-purple-900/30 border-purple-800' : 'bg-purple-50 border-purple-200'}`}>
                <p className={`whitespace-pre-wrap ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {doc.high_level_design.data_model_changes}
                </p>
              </div>
            </div>
          )}

          {/* Security Considerations */}
          {doc.high_level_design.security_considerations && (
            <div className="mb-6">
              <h3 className={`text-xl font-medium mb-3 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Security Considerations</h3>
              <div className={`border rounded-lg p-4 ${isDarkMode ? 'bg-red-900/30 border-red-800' : 'bg-red-50 border-red-200'}`}>
                <p className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
                  {doc.high_level_design.security_considerations}
                </p>
              </div>
            </div>
          )}

          {/* Error Handling and Recovery */}
          {doc.high_level_design.error_handling_and_recovery && (
            <div className="mb-6">
              <h3 className={`text-xl font-medium mb-3 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Error Handling & Recovery</h3>
              <div className={`border rounded-lg p-4 ${isDarkMode ? 'bg-orange-900/30 border-orange-800' : 'bg-orange-50 border-orange-200'}`}>
                {doc.high_level_design.error_handling_and_recovery.critical_error_scenarios && (
                  <div className="mb-4">
                    <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Critical Error Scenarios:</h4>
                    <ul className="list-disc list-inside space-y-1">
                      {doc.high_level_design.error_handling_and_recovery.critical_error_scenarios.map((scenario: string, index: number) => (
                        <li key={index} className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
                          {scenario}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {doc.high_level_design.error_handling_and_recovery.overall_strategy && (
                  <div>
                    <h4 className={`font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Overall Strategy:</h4>
                    <p className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
                      {doc.high_level_design.error_handling_and_recovery.overall_strategy}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </section>
      )}

      {/* Rest of the sections would follow similar pattern... */}
      {/* For brevity, I'll add the key remaining sections */}

      {/* Success Metrics */}
      {doc.success_metrics && doc.success_metrics.length > 0 && (
        <section>
          <h2 className={`text-2xl font-semibold mb-4 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Success Metrics</h2>
          <ul className="space-y-2">
            {doc.success_metrics.map((metric: string, index: number) => (
              <li key={index} className="flex items-start">
                <span className="text-blue-500 mr-2 mt-1">📊</span>
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{metric}</span>
              </li>
            ))}
          </ul>
        </section>
      )}
    </div>
  );
}

// Helper functions to format implementation plan and rollout strategy
function formatImplementationPlan(plan: any): string {
  if (typeof plan === 'string') return plan;
  
  let markdown = '';
  
  if (plan.overview) {
    markdown += `## Overview\n\n${plan.overview}\n\n`;
  }
  
  if (plan.phases && Array.isArray(plan.phases)) {
    markdown += `## Implementation Phases\n\n`;
    plan.phases.forEach((phase: any, index: number) => {
      markdown += `### Phase ${index + 1}: ${phase.name}\n\n`;
      if (phase.description) markdown += `${phase.description}\n\n`;
      if (phase.tasks && Array.isArray(phase.tasks)) {
        markdown += `**Tasks:**\n`;
        phase.tasks.forEach((task: any) => {
          markdown += `- ${typeof task === 'string' ? task : task.description || task.name}\n`;
        });
        markdown += '\n';
      }
      if (phase.duration) markdown += `**Duration:** ${phase.duration}\n\n`;
    });
  }
  
  if (plan.milestones && Array.isArray(plan.milestones)) {
    markdown += `## Milestones\n\n`;
    plan.milestones.forEach((milestone: any, index: number) => {
      markdown += `### ${milestone.name || `Milestone ${index + 1}`}\n\n`;
      if (milestone.description) markdown += `${milestone.description}\n\n`;
      if (milestone.deliverables && Array.isArray(milestone.deliverables)) {
        markdown += `**Deliverables:**\n`;
        milestone.deliverables.forEach((deliverable: string) => {
          markdown += `- ${deliverable}\n`;
        });
        markdown += '\n';
      }
    });
  }
  
  return markdown || JSON.stringify(plan, null, 2);
}

function formatRolloutStrategy(strategy: any): string {
  if (typeof strategy === 'string') return strategy;
  
  let markdown = '';
  
  if (strategy.overview) {
    markdown += `## Rollout Overview\n\n${strategy.overview}\n\n`;
  }
  
  if (strategy.phases && Array.isArray(strategy.phases)) {
    markdown += `## Rollout Phases\n\n`;
    strategy.phases.forEach((phase: any, index: number) => {
      markdown += `### Phase ${index + 1}: ${phase.name}\n\n`;
      if (phase.description) markdown += `${phase.description}\n\n`;
      if (phase.criteria) markdown += `**Success Criteria:** ${phase.criteria}\n\n`;
      if (phase.rollback) markdown += `**Rollback Plan:** ${phase.rollback}\n\n`;
    });
  }
  
  if (strategy.risks && Array.isArray(strategy.risks)) {
    markdown += `## Risk Assessment\n\n`;
    strategy.risks.forEach((risk: any) => {
      markdown += `### ${risk.title || risk.name}\n\n`;
      if (risk.description) markdown += `${risk.description}\n\n`;
      if (risk.mitigation) markdown += `**Mitigation:** ${risk.mitigation}\n\n`;
    });
  }
  
  return markdown || JSON.stringify(strategy, null, 2);
} 