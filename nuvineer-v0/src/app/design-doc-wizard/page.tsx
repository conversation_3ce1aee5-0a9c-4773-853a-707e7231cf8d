'use client'

import { Suspense, useState, useEffect } from 'react';

// Import our extracted modules
import { useDesignDocWizard } from '../../hooks/useDesignDocWizard';
import { useDesignDocActions } from '../../hooks/useDesignDocActions';
import { useWizardEditing } from '../../hooks/useWizardEditing';
import { useWizardNavigation } from '../../hooks/useWizardNavigation';
import DecisionRemovalModal from '../../components/DecisionRemovalModal';
import DecisionOverrideModal from '../../components/design-doc-wizard/DecisionOverrideModal';

// Import the new modular components
import { WizardHeader } from '../../components/design-doc-wizard/WizardHeader';
import ConstitutionStep from '../../components/design-doc-wizard/ConstitutionStep';
import DeploymentConstitutionCheckStep from '../../components/design-doc-wizard/DeploymentConstitutionCheckStep';
import TaskDefinitionStep from '../../components/design-doc-wizard/TaskDefinitionStep';
import TaskVerificationStep from '../../components/design-doc-wizard/TaskVerificationStep';
import UserJourneyDefinitionStep from '../../components/design-doc-wizard/UserJourneyDefinitionStep';
import StrategicValueAssessmentStep from '../../components/design-doc-wizard/StrategicValueAssessmentStep';
import DecisionDiscoveryStep from '../../components/design-doc-wizard/DecisionDiscoveryStep';
import DecisionMakingStep from '../../components/design-doc-wizard/DecisionMakingStep';
import ReviewGenerationStep from '../../components/design-doc-wizard/ReviewGenerationStep';
import DesignDocumentViewer from '../../components/design-doc-wizard/DesignDocumentViewer';
import DecisionProcessingSummary from '@/components/design-doc-wizard/DecisionProcessingSummary';
import StreamlinedDecisionMakingStep from '@/components/design-doc-wizard/StreamlinedDecisionMakingStep';
import DecisionReviewPreferencesStep from '@/components/design-doc-wizard/DecisionReviewPreferencesStep';
import StrategicAssessmentPreferencesStep from '@/components/design-doc-wizard/StrategicAssessmentPreferencesStep';
import { DEFAULT_REVIEW_PREFERENCES, DEFAULT_STRATEGIC_ASSESSMENT_PREFERENCES } from '@/types/design-doc-wizard';
import WizardSettings from '@/components/design-doc-wizard/WizardSettings';
import { formatDesignDocAsMarkdown, formatImplementationPlanAsMarkdown, formatDesignDocAsMarkdownForGitHub, formatImplementationPlanAsMarkdownForGitHub } from '@/utils/design-doc-wizard';

function DesignDocWizardContent() {
  // Main wizard state and configuration
  const wizardHook = useDesignDocWizard();
    const {
    session,
    isLoadingSession,
    isAuthorized,
    wizardState,
    setWizardState,
        repositorySlug,
    installationId,
    isPublic,
    constitutionSeedText,
    setConstitutionSeedText,
    showIssueSelector,
    setShowIssueSelector,
    issues,
    setIssues,
    isLoadingIssues,
    setIsLoadingIssues,
    githubHandle,
    setGithubHandle,
    apiKey,
    dbSessionId,
    router,
    fetchConstitution,
    saveConstitution,
    handlePriorityChange,
    supabase
  } = wizardHook;

  // API actions
  const actions = useDesignDocActions({
    wizardState,
    setWizardState,
            repositorySlug,
    installationId,
    isPublic,
    supabase,
    apiKey,
    githubHandle,
    setIssues,
    setIsLoadingIssues
  });

  // Editing functions
  const editing = useWizardEditing({ wizardState, setWizardState });

  // Navigation functions
  const navigation = useWizardNavigation({ 
    wizardState, 
    setWizardState, 
    analyzeAllDecisions: actions.analyzeAllDecisions 
  });

  // State for document viewer
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [overrideDecisionId, setOverrideDecisionId] = useState<string | null>(null);

  // Show settings automatically if constitution is not set
  useEffect(() => {
    if (wizardState.currentStep === 'constitution') {
      setShowSettings(true);
    }
  }, [wizardState.currentStep]);

  // Initialize decision review and strategic assessment preferences if not set
  useEffect(() => {
    if (wizardState.projectConstitution) {
      const needsDecisionPrefs = !wizardState.projectConstitution.decisionReviewPreferences;
      const needsStrategicPrefs = !wizardState.projectConstitution.strategicAssessmentPreferences;
      
      if (needsDecisionPrefs || needsStrategicPrefs) {
        setWizardState(prev => ({
          ...prev,
          projectConstitution: {
            ...prev.projectConstitution!,
            ...(needsDecisionPrefs && { decisionReviewPreferences: DEFAULT_REVIEW_PREFERENCES }),
            ...(needsStrategicPrefs && { strategicAssessmentPreferences: DEFAULT_STRATEGIC_ASSESSMENT_PREFERENCES })
          }
        }));
      }
    }
  }, [wizardState.projectConstitution, setWizardState]);

  // Custom method to draft user journeys and navigate to the journey definition step
  const draftUserJourneysAndNavigate = async () => {
    if (!wizardState.taskDetails.title || !wizardState.taskDetails.description) return;

    // Check if user journeys already exist - if so, just navigate
    if (wizardState.userJourneys && wizardState.userJourneys.length > 0) {
      setWizardState(prev => ({ ...prev, currentStep: 'user-journey-definition' }));
      return;
    }

    setWizardState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      // Calls the API route we fixed earlier
      const response = await fetch('/api/design-doc-wizard/draft-user-journeys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskDetails: wizardState.taskDetails,
          projectConstitution: wizardState.projectConstitution,
          repositorySlug,
          installationId: installationId || undefined,
          isPublic
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to draft user journeys: ${errorData.details || response.statusText}`);
      }

      const data = await response.json();
      
      // Update state with drafted journeys and navigate to the journey definition step
      setWizardState(prev => ({
        ...prev,
        userJourneys: data.userJourneys || [],
        currentStep: 'user-journey-definition', // Navigate to the correct next step
        isLoading: false
      }));

    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false
      }));
    }
  };

  const analyzeTaskAndNavigateToVerification = async () => {
    if (!wizardState.taskDetails.title || !wizardState.userJourneys?.length) return;

    // Check if task analysis already exists - if so, just navigate
    if (wizardState.taskAnalysis) {
      setWizardState(prev => ({ ...prev, currentStep: 'task-verification' }));
      return;
    }

    setWizardState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await fetch('/api/design-doc-wizard/analyze-task', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskDetails: wizardState.taskDetails,
          userJourneys: wizardState.userJourneys,
          projectConstitution: wizardState.projectConstitution,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to analyze task: ${errorData.details || response.statusText}`);
      }

      const data = await response.json();
      
      setWizardState(prev => ({
        ...prev,
        taskAnalysis: data.taskAnalysis,
        currentStep: 'task-verification', // Navigate to verification step
        isLoading: false,
      }));
    } catch (error: any) {
      setWizardState(prev => ({ ...prev, error: error.message, isLoading: false }));
    }
  };

  const runStrategicAssessmentAndNavigate = async () => {
    if (!wizardState.taskAnalysis || !wizardState.projectConstitution) return;

    // Check if strategic assessment already exists - if so, just navigate
    if (wizardState.strategicAssessment) {
      setWizardState(prev => ({ ...prev, currentStep: 'strategic-assessment' }));
      return;
    }

    setWizardState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await fetch('/api/design-doc-wizard/strategic-assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskAnalysis: wizardState.taskAnalysis,
          projectConstitution: wizardState.projectConstitution,
          taskDetails: wizardState.taskDetails, // Pass taskDetails for experimental feature detection
          repositorySlug,
          installationId: installationId || undefined,
          isPublic,
          isMinimalScope: wizardState.isMinimalScope || false, // Pass the minimal scope flag
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to run strategic assessment: ${errorData.details || response.statusText}`);
      }

      const data = await response.json();
      
      setWizardState(prev => ({
        ...prev,
        strategicAssessment: data.strategicAssessment,
        currentStep: 'strategic-assessment',
        isLoading: false,
      }));
    } catch (error: any) {
      setWizardState(prev => ({ ...prev, error: error.message, isLoading: false }));
    }
  };

  const handleStrategicAssessmentContinue = async () => {
    // After strategic assessment, continue to decision discovery
    discoverDecisionPointsAndNavigate();
  };

  const handleStrategicAssessmentOverride = async () => {
    // Override the recommendation and continue with BUILD
    setWizardState(prev => ({
      ...prev,
      strategicAssessment: prev.strategicAssessment ? {
        ...prev.strategicAssessment,
        recommendation: 'BUILD' as const
      } : undefined
    }));
    // Continue to decision discovery
    discoverDecisionPointsAndNavigate();
  };

  const handleRetryWithMinimalScope = async (minimalScopeDescription: string) => {
    // Update task description with minimal scope and restart task analysis
    const updatedTaskDescription = `${wizardState.taskDetails.description}

--- MINIMAL SCOPE ALTERNATIVE ---
${minimalScopeDescription}`;

    setWizardState(prev => ({
      ...prev,
      taskDetails: {
        ...prev.taskDetails,
        description: updatedTaskDescription
      },
      // Reset state to restart from task analysis
      taskAnalysis: undefined,
      strategicAssessment: undefined,
      currentStep: 'task-definition',
      isLoading: false,
      isMinimalScope: true // Mark this as a minimal scope assessment
    }));
  };

  const discoverDecisionPointsAndNavigate = async () => {
    // Check if decision points already exist - if so, just navigate
    if (wizardState.decisionPoints && wizardState.decisionPoints.length > 0) {
      setWizardState(prev => ({ ...prev, currentStep: 'decision-discovery' }));
      return;
    }

    setWizardState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await fetch('/api/design-doc-wizard/discover-decisions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskDetails: wizardState.taskDetails,
          taskAnalysis: wizardState.taskAnalysis,
          projectConstitution: wizardState.projectConstitution,
          repositorySlug,
          installationId: installationId || undefined,
          isPublic,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to discover decisions: ${errorData.details || response.statusText}`);
      }

      const data = await response.json();
      
      setWizardState(prev => ({
        ...prev,
        decisionPoints: data.decisionPoints || [],
        nonGoals: data.nonGoals || [],
        strategicAnalysis: data.strategicAnalysis || undefined, // Store strategic analysis for milestone prioritization
        currentStep: 'decision-discovery',
        isLoading: false,
      }));
    } catch (error: any) {
      setWizardState(prev => ({ ...prev, error: error.message, isLoading: false }));
    }
  };

  // Smart navigation handlers that preserve state
  const handleNavigateToTaskDefinition = () => {
    setWizardState(prev => ({ ...prev, currentStep: 'task-definition' }));
  };

  const handleNavigateToUserJourneyDefinition = () => {
    setWizardState(prev => ({ ...prev, currentStep: 'user-journey-definition' }));
  };

  const handleNavigateToTaskVerification = () => {
    setWizardState(prev => ({ ...prev, currentStep: 'task-verification' }));
  };

  const handleNavigateToStrategicAssessment = () => {
    setWizardState(prev => ({ ...prev, currentStep: 'strategic-assessment' }));
  };

  const handleNavigateToDecisionDiscovery = () => {
    setWizardState(prev => ({ ...prev, currentStep: 'decision-discovery' }));
  };

  const handleNavigateToDecisionMaking = () => {
    setWizardState(prev => ({ ...prev, currentStep: 'decision-making' }));
  };

  const handleNavigateToReviewGeneration = () => {
    setWizardState(prev => ({ ...prev, currentStep: 'review-generation' }));
  };


  // Additional functions that need state access
  const handleImportFromIssue = () => {
    actions.fetchIssues();
    setShowIssueSelector(true);
  };

  const handleSelectIssue = (issue: any) => {
    // Create enhanced task description with GitHub issue link
    const enhancedDescription = `**GitHub Issue:** ${issue.html_url}

${issue.body || ''}`;

    setWizardState((prev: any) => ({
      ...prev,
      taskDetails: {
        ...prev.taskDetails,
        title: issue.title,
        description: enhancedDescription,
      }
    }));
    setShowIssueSelector(false);
  };

  const handlePriorityToggle = (priorityId: any, checked: boolean) => {
    if (!wizardState.projectConstitution) return;

    const currentPriorities = wizardState.projectConstitution.priorities || [];
    const newPriorities = checked
      ? [...currentPriorities, priorityId]
      : currentPriorities.filter(p => p !== priorityId);

    setWizardState(prev => ({
      ...prev,
      projectConstitution: {
        ...prev.projectConstitution!,
        priorities: newPriorities
      }
    }));
  };

  // Update constitution step to include decision review and strategic assessment preferences
  const handleConstitutionUpdate = (constitution: any) => {
    setWizardState(prev => ({
      ...prev,
      projectConstitution: {
        ...constitution,
        decisionReviewPreferences: constitution.decisionReviewPreferences || DEFAULT_REVIEW_PREFERENCES,
        strategicAssessmentPreferences: constitution.strategicAssessmentPreferences || DEFAULT_STRATEGIC_ASSESSMENT_PREFERENCES
      }
    }));
  };

  if (isLoadingSession || wizardState.currentStep === 'initializing') {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mr-3"></div>
        <p className="text-lg">Loading Project Context..</p>
      </div>
    );
  }

  if (!session || !isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-8">
        <h1 className="text-3xl font-bold mb-4">Design Document Wizard</h1>
        <p className="text-gray-600 mb-8">Please log in to access the interactive design document generator.</p>
        <button
          onClick={() => router.push('/')}
          className="px-6 py-3 bg-amber-500 hover:bg-amber-600 text-white rounded-lg"
        >
          Go to Login
        </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Feature Wizard
          </h1>
        </div>

        {/* Header */}
        <WizardHeader
          currentStep={wizardState.currentStep}
          sessionId={wizardState.sessionId}
          dbSessionId={dbSessionId || undefined}
          onShowSettings={() => setShowSettings(true)}
          onBackToDashboard={() => router.push('/')}
        />

        {showSettings && (
          <WizardSettings
            wizardState={wizardState}
            setWizardState={setWizardState}
            constitutionSeedText={constitutionSeedText}
            setConstitutionSeedText={setConstitutionSeedText}
            handlePriorityChange={handlePriorityToggle}
            seedConstitution={() => actions.seedConstitution(constitutionSeedText)}
            saveConstitution={wizardHook.saveConstitution}
            onClose={() => {
              setShowSettings(false);
              if (wizardState.currentStep === 'constitution') {
                setWizardState(prev => ({ ...prev, currentStep: 'task-definition' }));
              }
            }}
            handleConstitutionUpdate={handleConstitutionUpdate}
            repositorySlug={repositorySlug}
            installationId={installationId}
          />
        )}

        {wizardState.error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-800 dark:text-red-200">{wizardState.error}</p>
          </div>
        )}

        {/* Step 0: Deployment Constitution Check */}
        {wizardState.currentStep === 'deployment-constitution-check' && (
          <DeploymentConstitutionCheckStep
            hasDeploymentConstitution={wizardState.hasDeploymentConstitution || false}
            isLoading={wizardState.isCheckingDeploymentConstitution || wizardState.isGeneratingDeploymentConstitution || false}
            error={wizardState.deploymentConstitutionError || null}
            onGenerate={wizardHook.generateDeploymentConstitution}
            onSkip={wizardHook.skipDeploymentConstitution}
            onContinue={wizardHook.continueFromDeploymentConstitution}
            repositorySlug={repositorySlug}
          />
        )}

        {/* Step 1: Task Definition */}
        {wizardState.currentStep === 'task-definition' && (
          <TaskDefinitionStep
            wizardState={wizardState}
            repositorySlug={repositorySlug}
            issues={issues}
            isLoadingIssues={isLoadingIssues}
            showIssueSelector={showIssueSelector}
            setShowIssueSelector={setShowIssueSelector}
            handleImportFromIssue={handleImportFromIssue}
            handleSelectIssue={handleSelectIssue}
            editing={editing}
            onBack={() => setWizardState((prev: any) => ({ 
              ...prev, 
              currentStep: wizardState.hasDeploymentConstitution ? 'constitution' : 'deployment-constitution-check' 
            }))}
            onContinue={draftUserJourneysAndNavigate}
            isLoading={wizardState.isLoading}
          />
        )}

        {/* Step 2: Task Verification */}
        {wizardState.currentStep === 'task-verification' && (
          <TaskVerificationStep
            wizardState={wizardState}
            editing={editing}
            onBack={handleNavigateToUserJourneyDefinition}
            onContinue={runStrategicAssessmentAndNavigate}
            isLoading={wizardState.isLoading}
          />
        )}

        {/* Step 3: Strategic Value Assessment */}
        {wizardState.currentStep === 'strategic-assessment' && (
          <StrategicValueAssessmentStep
            wizardState={wizardState}
            onBack={handleNavigateToTaskVerification}
            onContinue={handleStrategicAssessmentContinue}
            onOverride={handleStrategicAssessmentOverride}
            onRetryWithMinimalScope={handleRetryWithMinimalScope}
            isLoading={wizardState.isLoading}
            dbSessionId={dbSessionId || undefined}
          />
        )}

        {/* Step 4: User Journey Definition */}
        {wizardState.currentStep === 'user-journey-definition' && (
          <UserJourneyDefinitionStep
            wizardState={wizardState}
            setWizardState={setWizardState}
            repositorySlug={repositorySlug}
            installationId={installationId ? parseInt(installationId, 10) : undefined}
            isPublic={isPublic}
            onBack={handleNavigateToTaskDefinition}
            onContinue={analyzeTaskAndNavigateToVerification}
            isLoading={wizardState.isLoading}
          />
        )}

        {/* Step 5: Decision Discovery */}
        {wizardState.currentStep === 'decision-discovery' && (
          <DecisionDiscoveryStep
            wizardState={wizardState}
            editing={editing}
            onBack={handleNavigateToStrategicAssessment}
            onContinue={() => actions.analyzeAllDecisions(wizardState.decisionPoints)}
            isLoading={wizardState.isLoading}
          />
        )}

        {/* Decision Processing Summary - Show after analysis is complete */}
        {wizardState.showDecisionProcessingSummary && wizardState.decisionProcessingResults && (
          <DecisionProcessingSummary
            wizardState={wizardState}
            onApproveAutoProcessed={actions.approveAutoProcessedDecisions}
            onInitiateOverride={(decisionId) => setOverrideDecisionId(decisionId)}
            onContinue={() => setWizardState(prev => ({ 
              ...prev, 
              showDecisionProcessingSummary: false,
              currentStep: 'decision-making' 
            }))}
          />
        )}

        {/* Step 6: Decision Making - Use streamlined version */}
        {wizardState.currentStep === 'decision-making' && !wizardState.showDecisionProcessingSummary && (
          <StreamlinedDecisionMakingStep
            wizardState={wizardState}
            actions={actions}
            repositorySlug={repositorySlug}
            installationId={installationId}
            onBack={handleNavigateToDecisionDiscovery}
            onContinue={() => actions.generateDesignDoc()}
            isLoading={wizardState.isLoading}
          />
        )}

        {/* Step 7: Review Generation */}
        {wizardState.currentStep === 'review-generation' && (
          <ReviewGenerationStep
            wizardState={wizardState}
            onBack={handleNavigateToDecisionMaking}
            onViewDocument={() => {
              setShowDocumentViewer(true);
            }}
            onStartNew={() => {
              // Reset wizard state for new document
              setWizardState(prev => ({
                ...prev,
                currentStep: 'constitution',
                taskDetails: { title: '', description: '', initialIdeas: '' },
                userJourneys: [], // Reset user journeys too
                decisionPoints: [],
                generatedDoc: undefined,
                error: undefined,
                sessionId: `wizard_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
              }));
            }}
            onAddToGitHubIssue={async () => {
              if (!wizardState.generatedDoc) {
                return { success: false, error: 'No design document to add.' };
              }

              const issueMatch = wizardState.taskDetails.description.match(/https:\/\/github\.com\/([^/]+)\/([^/]+)\/issues\/(\d+)/);
              if (!issueMatch) {
                return { success: false, error: 'No GitHub issue linked in the task description.' };
              }
              const [, owner, repo, issue_number] = issueMatch;
              const repoSlug = `${owner}/${repo}`;

              let markdown = formatDesignDocAsMarkdownForGitHub(wizardState.generatedDoc);
              
              // Add shareable link to the design document session if we have a database session ID
              if (dbSessionId) {
                const shareableLink = `https://archknow.vercel.app/design-doc-wizard/view/${dbSessionId}`;
                markdown += `\n\n---\n\n**📋 View Complete Design**: [${shareableLink}](${shareableLink})\n\n*This link provides access to the full design process including task analysis, technical decisions, and implementation details.*`;
              }

              try {
                const response = await fetch('/api/github/add-comment', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    repository_slug: repoSlug,
                    issue_number: parseInt(issue_number, 10),
                    comment_body: markdown,
                    api_key: apiKey,
                    labels: ['design-doc-complete']
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Failed to add comment to GitHub issue.');
                }

                const result = await response.json();
                return { 
                  success: true, 
                  commentUrl: result.comment.html_url,
                  issueNumber: issue_number,
                  repository: repoSlug,
                  labelsAdded: result.labelsAdded
                };
              } catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'An unknown error occurred' };
              }
            }}
            onGenerateImplementationPlan={async () => {
              try {
                if (!wizardState.generatedDoc || !wizardState.taskDetails) {
                  throw new Error('Missing design document or task details');
                }

                const response = await fetch('/api/design-doc-wizard/generate-implementation-plan', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    design_document: wizardState.generatedDoc,
                    task_details: wizardState.taskDetails,
                    repository_slug: repositorySlug || 'unknown',
                    installation_id: installationId || 0,
                    is_public: isPublic || false
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || `HTTP ${response.status}`);
                }

                const result = await response.json();
                
                if (result.success && result.implementationPlan) {
                  return {
                    success: true,
                    implementationPlan: result.implementationPlan
                  };
                } else {
                  throw new Error(result.error || 'Failed to generate implementation plan');
                }
              } catch (error) {
                console.error('Error generating implementation plan:', error);
                return {
                  success: false,
                  error: error instanceof Error ? error.message : 'Unknown error occurred'
                };
              }
            }}
            onAddImplementationPlanToGitHub={async (plan) => {
              if (!plan) {
                return { success: false, error: 'No implementation plan to add.' };
              }

              const issueMatch = wizardState.taskDetails.description.match(/https:\/\/github\.com\/([^/]+)\/([^/]+)\/issues\/(\d+)/);
              if (!issueMatch) {
                return { success: false, error: 'No GitHub issue linked in the task description.' };
              }
              const [, owner, repo, issue_number] = issueMatch;
              const repoSlug = `${owner}/${repo}`;

              // We'll need a formatter for the implementation plan similar to the design doc one.
              // For now, let's stringify it to get it working.
              const markdown = formatImplementationPlanAsMarkdownForGitHub(plan);

              try {
                const response = await fetch('/api/github/add-comment', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    repository_slug: repoSlug,
                    issue_number: parseInt(issue_number, 10),
                    comment_body: markdown,
                    api_key: apiKey,
                    labels: ['ready-for-implementation']
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Failed to add implementation plan to GitHub issue.');
                }

                const result = await response.json();
                return { 
                  success: true, 
                  commentUrl: result.comment.html_url,
                  issueNumber: issue_number,
                  repository: repoSlug,
                  labelsAdded: result.labelsAdded
                };
              } catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'An unknown error occurred' };
              }
            }}
            onGenerateRolloutStrategy={async () => {
              try {
                if (!wizardState.generatedDoc || !wizardState.taskDetails) {
                  throw new Error('Missing design document or task details');
                }

                const response = await fetch('/api/design-doc-wizard/generate-rollout-strategy', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    design_document: wizardState.generatedDoc,
                    task_details: wizardState.taskDetails,
                    project_constitution: wizardState.projectConstitution, // Add project constitution
                    repository_slug: repositorySlug || 'unknown',
                    installation_id: installationId || 0,
                    is_experimental: wizardState.taskDetails?.isExperimental || false
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || `HTTP ${response.status}`);
                }

                const result = await response.json();
                
                if (result.success && result.rolloutStrategy) {
                  return {
                    success: true,
                    rolloutStrategy: result.rolloutStrategy
                  };
                } else {
                  throw new Error(result.error || 'Failed to generate rollout strategy');
                }
              } catch (error) {
                console.error('Error generating rollout strategy:', error);
                return {
                  success: false,
                  error: error instanceof Error ? error.message : 'Unknown error occurred'
                };
              }
            }}
            isLoading={wizardState.isLoading || false}
            isPublic={isPublic}
            installationId={installationId}
            repositorySlug={repositorySlug}
            dbSessionId={dbSessionId || undefined}
          />
        )}

        {/* Decision Removal Modal */}
        <DecisionRemovalModal
          isOpen={wizardState.showRemovalModal || false}
          decision={wizardState.removalModalDecisionId ? 
            wizardState.decisionPoints?.find((d: any) => d.id === wizardState.removalModalDecisionId) || null : 
            null
          }
          onClose={editing.hideRemovalModal}
          onConfirmRemoval={editing.removeDecisionWithContext}
        />

        {/* Decision Override Modal */}
        {overrideDecisionId && (
          <DecisionOverrideModal
            isOpen={!!overrideDecisionId}
            decision={overrideDecisionId ? wizardState.decisionPoints?.find((d: any) => d.id === overrideDecisionId) || null : null}
            result={overrideDecisionId ? wizardState.decisionProcessingResults?.find((r: any) => r.decisionId === overrideDecisionId) || null : null}
            onClose={() => setOverrideDecisionId(null)}
            onConfirm={(newOptionId: string, rationale: string) => {
              actions.overrideAutoProcessedDecision(overrideDecisionId!, newOptionId, rationale);
              setOverrideDecisionId(null);
            }}
          />
        )}

        {/* Design Document Viewer Modal */}
        {showDocumentViewer && wizardState.generatedDoc && (
          <DesignDocumentViewer
            designDoc={wizardState.generatedDoc}
            onClose={() => setShowDocumentViewer(false)}
            onAddToGitHubIssue={async () => {
              if (!wizardState.generatedDoc) {
                return { success: false, error: 'No design document to add.' };
              }

              const issueMatch = wizardState.taskDetails.description.match(/https:\/\/github\.com\/([^/]+)\/([^/]+)\/issues\/(\d+)/);
              if (!issueMatch) {
                return { success: false, error: 'No GitHub issue linked in the task description.' };
              }
              const [, owner, repo, issue_number] = issueMatch;
              const repoSlug = `${owner}/${repo}`;

              let markdown = formatDesignDocAsMarkdownForGitHub(wizardState.generatedDoc);
              
              // Add shareable link to the design document session if we have a database session ID
              if (dbSessionId) {
                const shareableLink = `https://archknow.vercel.app/design-doc-wizard/view/${dbSessionId}`;
                markdown += `\n\n---\n\n**📋 View Complete Design**: [${shareableLink}](${shareableLink})\n\n*This link provides access to the full design process including task analysis, technical decisions, and implementation details.*`;
              }

              try {
                const response = await fetch('/api/github/add-comment', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    repository_slug: repoSlug,
                    issue_number: parseInt(issue_number, 10),
                    comment_body: markdown,
                    api_key: apiKey,
                    labels: ['design-doc-complete']
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Failed to add comment to GitHub issue.');
                }

                const result = await response.json();
                return { 
                  success: true, 
                  commentUrl: result.comment.html_url,
                  issueNumber: issue_number,
                  repository: repoSlug,
                  labelsAdded: result.labelsAdded
                };
              } catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'An unknown error occurred' };
              }
            }}
            onGenerateImplementationPlan={async () => {
              try {
                if (!wizardState.generatedDoc || !wizardState.taskDetails) {
                  throw new Error('Missing design document or task details');
                }

                const response = await fetch('/api/design-doc-wizard/generate-implementation-plan', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    design_document: wizardState.generatedDoc,
                    task_details: wizardState.taskDetails,
                    repository_slug: repositorySlug || 'unknown',
                    installation_id: installationId || 0,
                    is_public: isPublic || false
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || `HTTP ${response.status}`);
                }

                const result = await response.json();
                
                if (result.success && result.implementationPlan) {
                  return {
                    success: true,
                    implementationPlan: result.implementationPlan
                  };
                } else {
                  throw new Error(result.error || 'Failed to generate implementation plan');
                }
              } catch (error) {
                console.error('Error generating implementation plan:', error);
                return {
                  success: false,
                  error: error instanceof Error ? error.message : 'Unknown error occurred'
                };
              }
            }}
            onAddImplementationPlanToGitHub={async (plan) => {
              if (!plan) {
                return { success: false, error: 'No implementation plan to add.' };
              }

              const issueMatch = wizardState.taskDetails.description.match(/https:\/\/github\.com\/([^/]+)\/([^/]+)\/issues\/(\d+)/);
              if (!issueMatch) {
                return { success: false, error: 'No GitHub issue linked in the task description.' };
              }
              const [, owner, repo, issue_number] = issueMatch;
              const repoSlug = `${owner}/${repo}`;

              // We'll need a formatter for the implementation plan similar to the design doc one.
              // For now, let's stringify it to get it working.
              const markdown = formatImplementationPlanAsMarkdownForGitHub(plan);

              try {
                const response = await fetch('/api/github/add-comment', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    repository_slug: repoSlug,
                    issue_number: parseInt(issue_number, 10),
                    comment_body: markdown,
                    api_key: apiKey,
                    labels: ['ready-for-implementation']
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Failed to add implementation plan to GitHub issue.');
                }

                const result = await response.json();
                return { 
                  success: true, 
                  commentUrl: result.comment.html_url,
                  issueNumber: issue_number,
                  repository: repoSlug,
                  labelsAdded: result.labelsAdded
                };
              } catch (error) {
                return { success: false, error: error instanceof Error ? error.message : 'An unknown error occurred' };
              }
            }}
            taskDescription={wizardState.taskDetails.description}
            isPublic={isPublic}
            repositorySlug={repositorySlug}
            installationId={installationId}
          />
        )}
      </div>
    </div>
  );
}

export default function DesignDocWizardPage() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center min-h-screen"><p>Loading...</p></div>}>
      <DesignDocWizardContent />
    </Suspense>
  );
} 