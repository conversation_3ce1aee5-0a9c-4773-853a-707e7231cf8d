// Load environment variables first, before any other imports
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Get the directory of the current module and resolve path to project root
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = resolve(__dirname, '..');

// Configure dotenv to look in the project root, not the current working directory
const result = dotenv.config({ path: resolve(projectRoot, '.env') });

if (result.error) {
  console.error('Error loading .env file:', result.error);
  console.error('Looked for .env at:', resolve(projectRoot, '.env'));
  process.exit(1);
}
console.log('Loaded environment variables from .env file at:', resolve(projectRoot, '.env'));

// Now import other modules
import { processMergedPR } from './orchestrator.js';
import { Octokit } from '@octokit/rest';
import fs from 'fs';
import path from 'path';

// Double-check critical environment variables
const criticalVars = ['OPENAI_API_KEY', 'ANTHROPIC_API_KEY', 'PINECONE_API_KEY'];
for (const varName of criticalVars) {
  console.log(`${varName} set: ${process.env[varName] ? 'Yes (length: ' + process.env[varName].length + ')' : 'No'}`);
}

// Initialize Octokit with GitHub token
const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

/**
 * Fetch PR data from GitHub
 * @param {string} owner - Repository owner
 * @param {string} repo - Repository name
 * @param {number} prNumber - PR number
 * @returns {Promise<Object>} - PR context, code changes, and comments
 */
async function fetchPRData(owner, repo, prNumber) {
  console.log(`[Test] Fetching data for PR #${prNumber} from ${owner}/${repo}`);
  
  try {
    // Fetch PR details
    const { data: pr } = await octokit.pulls.get({
      owner,
      repo,
      pull_number: prNumber,
    });
    
    // Fetch PR files (changes)
    const { data: files } = await octokit.pulls.listFiles({
      owner,
      repo,
      pull_number: prNumber,
    });
    
    // Fetch PR comments
    const { data: comments } = await octokit.issues.listComments({
      owner,
      repo,
      issue_number: prNumber,
    });
    
    // Format PR context
    const prContext = {
      title: pr.title,
      body: pr.body || '',
      html_url: pr.html_url,
      number: pr.number,
      files: files.map(file => ({
        filename: file.filename,
        additions: file.additions,
        deletions: file.deletions,
        patch: file.patch || '',
      })),
    };
    
    // Format code changes
    const codeChanges = files.map(file => ({
      filename: file.filename,
      patch: file.patch || '',
    }));
    
    // Format comments
    const formattedComments = comments.map(comment => ({
      user: {
        login: comment.user.login,
      },
      body: comment.body,
    }));
    
    console.log(`[Test] Successfully fetched PR data with ${files.length} files and ${comments.length} comments`);
    
    // Optional: Save fetched data for debugging
    if (process.env.DEBUG === 'true') {
      const debugDir = path.join(process.cwd(), 'debug');
      if (!fs.existsSync(debugDir)) {
        fs.mkdirSync(debugDir);
      }
      
      fs.writeFileSync(
        path.join(debugDir, `pr-${prNumber}-data.json`),
        JSON.stringify({ prContext, codeChanges, formattedComments }, null, 2)
      );
      console.log(`[Test] Saved debug data to debug/pr-${prNumber}-data.json`);
    }
    
    return { prContext, codeChanges, formattedComments };
  } catch (error) {
    console.error(`[Test] Error fetching PR data:`, error);
    throw error;
  }
}

/**
 * Main function to test a single PR
 */
async function testSinglePR() {
  // Check for required environment variables
  const requiredEnvVars = [
    'GITHUB_TOKEN',
    'ANTHROPIC_API_KEY',
    'OPENAI_API_KEY',
    'PINECONE_API_KEY',
    'PINECONE_ENVIRONMENT',
    'PINECONE_INDEX_NAME',
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  if (missingVars.length > 0) {
    console.error(`[Test] Missing required environment variables: ${missingVars.join(', ')}`);
    process.exit(1);
  }
  
  // Get PR details from command line arguments
  const args = process.argv.slice(2);
  if (args.length !== 3) {
    console.error('[Test] Usage: node src/test-pr.js <owner> <repo> <pr-number>');
    console.error('[Test] Example: node src/test-pr.js facebook react 1000');
    process.exit(1);
  }
  
  const [owner, repo, prNumber] = args;
  console.log(`[Test] Starting test for PR #${prNumber} in ${owner}/${repo}`);
  
  try {
    // Fetch PR data
    const { prContext, codeChanges, formattedComments } = await fetchPRData(owner, repo, parseInt(prNumber));
    
    // Process PR using our orchestrator
    console.log('[Test] Starting architectural decisions extraction process...');
    await processMergedPR(prContext, codeChanges, formattedComments);
    
    console.log('[Test] Test completed successfully');
  } catch (error) {
    console.error('[Test] Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testSinglePR(); 