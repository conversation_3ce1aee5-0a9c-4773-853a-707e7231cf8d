import React, { useState, useEffect } from 'react';
import type { Repository } from '@/types';

const GlobeIcon = ({ className = "h-6 w-6" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
    </svg>
);
  
const LockClosedIcon = ({ className = "h-6 w-6" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
    </svg>
);

interface RepositoryStatus {
  checkedStatus: 'idle' | 'checking' | 'not_started' | 'in_progress' | 'completed' | 'error' | 'initial_analysis_complete';
  checkError: string | null;
}

interface RepositoryExtendedStatus extends RepositoryStatus {
  hasDeploymentConstitution?: boolean;
  isGeneratingConstitution?: boolean;
}

interface RepositoryListProps {
  repositories: Repository[];
  repositoryStatuses: Record<string, RepositoryStatus>;
  onStartAnalysis: (repoSlug: string) => void;
  isLoadingRepositories: boolean;
  selectedInstallationId: string;
  analysisType: 'public' | 'private';
}

const getStatusBadge = (status: RepositoryStatus | undefined, hasDeploymentConstitution?: boolean) => {
  if (!status) {
    return <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300">Unknown</span>;
  }

  const statusText = status.checkedStatus === 'initial_analysis_complete' ? 'completed' : status.checkedStatus;

  const statusConfig = {
    'idle': { color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300', text: 'Idle' },
    'checking': { color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300', text: 'Checking' },
    'not_started': { color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300', text: 'Not Started' },
    'in_progress': { color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300', text: 'In Progress' },
    'completed': { color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300', text: 'Completed' },
    'error': { color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300', text: 'Error' },
  };

  const config = statusConfig[statusText as keyof typeof statusConfig] || statusConfig.idle;
  
  return (
    <div className="flex items-center space-x-2">
      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
      {statusText === 'completed' && (
        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
          hasDeploymentConstitution 
            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' 
            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
        }`}>
          Deploy Context: {hasDeploymentConstitution ? 'Ready' : 'Missing'}
        </span>
      )}
    </div>
  );
};

export const RepositoryList: React.FC<RepositoryListProps> = ({
  repositories,
  repositoryStatuses,
  onStartAnalysis,
  isLoadingRepositories,
  selectedInstallationId,
  analysisType
}) => {
  const [extendedStatuses, setExtendedStatuses] = useState<Record<string, RepositoryExtendedStatus>>({});
  const [isLoadingStatuses, setIsLoadingStatuses] = useState(false);

  // Fetch deployment constitution status for completed repositories
  useEffect(() => {
    const fetchDeploymentStatuses = async () => {
      const completedRepos = repositories.filter(repo => {
        const status = repositoryStatuses[repo.full_name];
        return status && (status.checkedStatus === 'completed' || status.checkedStatus === 'initial_analysis_complete');
      });

      if (completedRepos.length === 0) return;

      setIsLoadingStatuses(true);
      const statusPromises = completedRepos.map(async (repo) => {
        try {
          // Ensure installationId is always a valid value
          const effectiveInstallationId = analysisType === 'public' ? '0' : (selectedInstallationId || '0');
          const response = await fetch(`/api/repository-status?repositorySlug=${encodeURIComponent(repo.full_name)}&installationId=${effectiveInstallationId}`);
          if (response.ok) {
            const data = await response.json();
            return { repoSlug: repo.full_name, hasDeploymentConstitution: data.hasDeploymentConstitution };
          } else {
            console.error(`Error fetching deployment status for ${repo.full_name}: ${response.status} ${response.statusText}`);
          }
        } catch (error) {
          console.error(`Error fetching deployment status for ${repo.full_name}:`, error);
        }
        return { repoSlug: repo.full_name, hasDeploymentConstitution: false };
      });

      const results = await Promise.all(statusPromises);
      const newStatuses: Record<string, RepositoryExtendedStatus> = {};
      
      results.forEach(result => {
        const existingStatus = repositoryStatuses[result.repoSlug];
        newStatuses[result.repoSlug] = {
          ...existingStatus,
          hasDeploymentConstitution: result.hasDeploymentConstitution,
          isGeneratingConstitution: false
        };
      });

      setExtendedStatuses(newStatuses);
      setIsLoadingStatuses(false);
    };

    fetchDeploymentStatuses();
  }, [repositories, repositoryStatuses, selectedInstallationId, analysisType]);

  const generateDeploymentConstitution = async (repoSlug: string) => {
    setExtendedStatuses(prev => ({
      ...prev,
      [repoSlug]: { ...prev[repoSlug], isGeneratingConstitution: true }
    }));

    try {
      // Ensure installationId is always a valid value
      const effectiveInstallationId = analysisType === 'public' ? '0' : (selectedInstallationId || '0');
      const response = await fetch('/api/repository/generate-deployment-constitution', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repositorySlug: repoSlug,
          installationId: effectiveInstallationId,
          isPublic: analysisType === 'public'
        })
      });

      if (response.ok) {
        setExtendedStatuses(prev => ({
          ...prev,
          [repoSlug]: { 
            ...prev[repoSlug], 
            hasDeploymentConstitution: true, 
            isGeneratingConstitution: false 
          }
        }));
      } else {
        throw new Error('Failed to generate deployment constitution');
      }
    } catch (error) {
      console.error('Error generating deployment constitution:', error);
      setExtendedStatuses(prev => ({
        ...prev,
        [repoSlug]: { ...prev[repoSlug], isGeneratingConstitution: false }
      }));
      alert('Failed to generate deployment constitution. Please try again.');
    }
  };

  if (isLoadingRepositories) {
    return <div className="text-center text-gray-500">Loading repositories...</div>;
  }

  // Show a message for private repos only if an installation is selected but no repos are found.
  if (analysisType === 'private' && repositories.length === 0 && selectedInstallationId) {
    return <div className="p-4 mt-2 text-sm text-center text-gray-500 bg-gray-100 rounded-lg dark:bg-zinc-700">No repositories found for this installation. Grant access to more repositories in your <a href={`https://github.com/settings/installations/${selectedInstallationId}`} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline hover:text-blue-600">GitHub installation settings</a>.</div>;
  }
  
  // For public repos, if the list is empty and not loading, we don't show the 'no repos found' message,
  // as the user is expected to add one. The parent component handles the loading state.
  if (repositories.length === 0) {
    return null;
  }

  return (
    <div className="mt-4 space-y-3">
      <ul className="divide-y divide-gray-200 rounded-md border border-gray-200 dark:divide-zinc-700 dark:border-zinc-700">
        {repositories.map((repo) => {
          const baseStatus = repositoryStatuses[repo.full_name];
          const extendedStatus = extendedStatuses[repo.full_name] || baseStatus;
          const canStartAnalysis = baseStatus && (baseStatus.checkedStatus === 'not_started' || baseStatus.checkedStatus === 'error');
          const isComplete = baseStatus && (baseStatus.checkedStatus === 'completed' || baseStatus.checkedStatus === 'initial_analysis_complete');
          const isInProgress = baseStatus && (baseStatus.checkedStatus === 'in_progress' || baseStatus.checkedStatus === 'checking');

          return (
            <li key={repo.id} className="flex items-center justify-between p-3 transition-colors hover:bg-gray-50 dark:hover:bg-zinc-700/50">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate dark:text-gray-100">{repo.full_name}</p>
                <div className="flex items-center mt-1">
                  {getStatusBadge(extendedStatus, extendedStatus?.hasDeploymentConstitution)}
                </div>
              </div>
              <div className="flex items-center ml-4 space-x-2">
                {canStartAnalysis && (
                  <button
                    onClick={() => onStartAnalysis(repo.full_name)}
                    className="px-3 py-1 text-sm text-white bg-blue-600 rounded-md shadow-sm hover:bg-blue-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                  >
                    Start Analysis
                  </button>
                )}
                {isInProgress && (
                  <button
                    disabled
                    className="px-3 py-1 text-sm text-gray-700 bg-gray-200 rounded-md cursor-not-allowed dark:bg-gray-600 dark:text-gray-300"
                  >
                    Analysis in progress
                  </button>
                )}
                {isComplete && !extendedStatus?.hasDeploymentConstitution && (
                  <button
                    onClick={() => generateDeploymentConstitution(repo.full_name)}
                    disabled={extendedStatus?.isGeneratingConstitution}
                    className="px-3 py-1 text-sm text-white bg-purple-600 rounded-md shadow-sm hover:bg-purple-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {extendedStatus?.isGeneratingConstitution ? 'Generating...' : 'Add Deploy Context'}
                  </button>
                )}
                {isComplete && (
                    <a
                        href={`/design-doc-wizard?repositorySlug=${encodeURIComponent(repo.full_name)}&isPublic=${analysisType === 'public'}&installationId=${analysisType === 'private' ? selectedInstallationId : '0'}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-3 py-1 text-sm font-semibold text-white bg-amber-600 rounded-md shadow-sm hover:bg-amber-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-amber-600"
                    >
                    Feature Wizard
                  </a>
                )}
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
}; 