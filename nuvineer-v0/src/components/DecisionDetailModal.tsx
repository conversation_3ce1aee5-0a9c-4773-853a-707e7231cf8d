import React from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import type { DecisionReference } from '../utils/decisionUtils';
import type { DecisionPoint } from '../types/design-doc-wizard';
import { getDecisionDisplayValues } from '../utils/decisionUtils';


interface DecisionDetailModalProps {
  decision?: DecisionReference | null;
  decisionPoint?: DecisionPoint | null;
  isOpen: boolean;
  onClose: () => void;
  repositorySlug?: string;  // owner/repo format for GitHub links
}

export default function DecisionDetailModal({ decision, decisionPoint, isOpen, onClose, repositorySlug }: DecisionDetailModalProps) {
  if (!isOpen || (!decision && !decisionPoint)) return null;

  // This will hold the primary data for display
  let displayData: { [key: string]: any } = {};
  let content: string = '';

  if (decision) {
    const { title, description, dev_prompt, implications, related_files, pr_url } = getDecisionDisplayValues(decision);
    displayData = {
      title,
      description,
      dev_prompt,
      implications,
      related_files,
      pr_url,
      follows_standard_practice_reason: decision.metadata?.follows_standard_practice_reason,
      rationale: decision.metadata?.rationale,
      risks_extracted: decision.metadata?.risks_extracted,
      confidence_score: decision.metadata?.confidence_score,
      design_doc_session_id: decision.metadata?.design_doc_session_id,
      id: decision.id
    };
    content = ''; // Remove the redundant content that was showing description again
  } else if (decisionPoint) {
    const selectedOption = decisionPoint.options?.find(opt => opt.id === decisionPoint.selectedOption);
    displayData = {
      title: decisionPoint.title,
      description: decisionPoint.description,
      rationale: decisionPoint.rationale,
      selectedOptionName: selectedOption?.name
    };
    content = 'This is a decision from the current design session. Detailed context is available within the wizard.';
  }

  return (
    <Dialog as="div" className="relative z-50" open={isOpen} onClose={onClose}>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <Dialog.Panel className="relative transform overflow-y-auto rounded-lg bg-white dark:bg-gray-800 px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl sm:p-6 max-h-[90vh]">
          <div>
            <div className="flex items-start justify-between mb-6">
              <div>
                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  {displayData?.title || 'Decision Details'}
                </Dialog.Title>
                {displayData.id &&
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Decision ID: {displayData.id}
                  </div>
                }
              </div>
              <button 
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {displayData.pr_url && (
              <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  <span className="text-blue-800 dark:text-blue-200 font-medium">Related Pull Request:</span>
                  <a 
                    href={displayData.pr_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                  >
                    View PR →
                  </a>
                </div>
              </div>
            )}

            {displayData.design_doc_session_id && (
              <div className="mb-6 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span className="text-purple-800 dark:text-purple-200 font-medium">Design Document:</span>
                  <a 
                    href={`https://archknow.vercel.app/design-doc-wizard/view/${displayData.design_doc_session_id}`}
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 underline"
                  >
                    View Design Journey →
                  </a>
                </div>
                <div className="mt-2 text-xs text-purple-700 dark:text-purple-300 font-mono">
                  Session ID: {displayData.design_doc_session_id}
                </div>
              </div>
            )}
            
            {displayData.description && (
              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Description</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{displayData.description}</p>
              </div>
            )}

            {displayData.implications && (
              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Implications</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{displayData.implications}</p>
              </div>
            )}

            {displayData.dev_prompt && (
              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Developer Guidance</h4>
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                  <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed">{displayData.dev_prompt}</p>
                </div>
              </div>
            )}

            {displayData.follows_standard_practice_reason && (
              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Patterns</h4>
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
                  <p className="text-sm text-green-800 dark:text-green-200 leading-relaxed">{displayData.follows_standard_practice_reason}</p>
                </div>
              </div>
            )}

            {displayData.related_files && displayData.related_files.length > 0 && (
              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">📁 Related Files</h4>
                <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <ul className="space-y-2">
                    {displayData.related_files.map((file: string, index: number) => {
                      const githubUrl = repositorySlug 
                        ? `https://github.com/${repositorySlug}/blob/main/${file.replace(/^\//, '')}`
                        : null;
                      
                      return (
                        <li key={index} className="flex items-center">
                          {githubUrl ? (
                            <a
                              href={githubUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline font-mono flex items-center"
                            >
                              <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                              </svg>
                              {file}
                              <svg className="w-3 h-3 ml-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                            </a>
                          ) : (
                            <span className="text-sm text-gray-700 dark:text-gray-300 font-mono flex items-center">
                              <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1z" clipRule="evenodd" />
                              </svg>
                              {file}
                            </span>
                          )}
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
            )}

            {displayData.selectedOptionName && (
              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Selected Option</h4>
                <div className="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 rounded-lg p-3">
                  <p className="text-sm text-emerald-800 dark:text-emerald-200 font-medium">{displayData.selectedOptionName}</p>
                </div>
              </div>
            )}
          </div>
          <div className="mt-5 sm:mt-6">
            <button
              type="button"
              className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
} 