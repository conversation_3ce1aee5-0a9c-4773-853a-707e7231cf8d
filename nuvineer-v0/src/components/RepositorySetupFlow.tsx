import React, { useState, useEffect, useCallback, useRef } from 'react';
import type { Session } from '@supabase/supabase-js';
import { useRouter, usePathname } from 'next/navigation';
import { RepositoryList } from './RepositoryList';
import { DeploymentConstitutionStep } from './RepositorySetupFlow/DeploymentConstitutionStep'; // <-- IMPORT

// Define SVG icons directly or import them if they are moved to a shared location
const GlobeIcon = ({ className = "h-6 w-6" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
  </svg>
);

const LockClosedIcon = ({ className = "h-6 w-6" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
  </svg>
);

// Define Interfaces (copy from page.tsx or import if shared)
interface Installation {
  id: number;
  account: {
    login: string;
    avatar_url: string;
    type?: string;
  };
}

interface Repository {
  id: number;
  name: string;
  full_name: string;
  private?: boolean;
}

// Define Props required by the component
interface RepositorySetupFlowProps {
  analysisType: 'public' | 'private' | null;
  setAnalysisType: (type: 'public' | 'private' | null) => void;
  session: Session | null;
  isLoadingSession: boolean;
  installations: Installation[];
  selectedInstallationId: string;
  setSelectedInstallationId: (id: string) => void;
  isLoadingInstallations: boolean;
  installationError: string | null;
  repositories: Repository[];
  selectedRepositorySlug: string;
  setSelectedRepositorySlug: (slug: string) => void;
  isLoadingRepositories: boolean;
  repositoryError: string | null;
  publicRepoInput: string;
  setPublicRepoInput: (input: string) => void;
  setSubmittedRepoInput: (input: string) => void;
  maxHistoricalPRs: number;
  setMaxHistoricalPRs: (num: number) => void;
  analysisBatchSize: number;
  setAnalysisBatchSize: (num: number) => void;
  sinceDate: string;
  setSinceDate: (date: string) => void;
  currentRepoSlug: string;
  analyzeRepoStatus: string | null;
  setAnalyzeRepoStatus: (status: string | null) => void;
  setIsSetupMode: (isSetup: boolean) => void;
  startRepositoryAnalysis: () => Promise<void>;
  logAuthStatus: () => void;
  repoCheckedStatus: 'idle' | 'checking' | 'not_started' | 'in_progress' | 'completed' | 'error' | 'initial_analysis_complete';
  repoCheckError: string | null;
  fetchDecisions?: () => Promise<void>;
  setHasAnalysisRunOrDataExists: (exists: boolean) => void;
  pendingPrCommitsCount?: number | null;
  pendingRelationshipsCount?: number | null;
  currentAnalysisStage?: string | null;
  historicalBackfillInProgress?: boolean;
  handleBuildKnowledgeGraph: () => Promise<void>;
  isBackfilling: boolean;
  backfillStatus: string | null;
}

// Define the component
const RepositorySetupFlowComponent: React.FC<RepositorySetupFlowProps> = ({
  analysisType,
  setAnalysisType,
  session,
  isLoadingSession,
  installations,
  selectedInstallationId,
  setSelectedInstallationId,
  isLoadingInstallations,
  installationError,
  repositories,
  selectedRepositorySlug,
  setSelectedRepositorySlug,
  isLoadingRepositories,
  repositoryError,
  publicRepoInput,
  setPublicRepoInput,
  setSubmittedRepoInput,
  maxHistoricalPRs,
  setMaxHistoricalPRs,
  analysisBatchSize,
  setAnalysisBatchSize,
  sinceDate,
  setSinceDate,
  currentRepoSlug,
  analyzeRepoStatus,
  setAnalyzeRepoStatus,
  setIsSetupMode,
  startRepositoryAnalysis: startRepositoryAnalysisProp,
  logAuthStatus,
  repoCheckedStatus,
  repoCheckError,
  fetchDecisions,
  setHasAnalysisRunOrDataExists,
  pendingPrCommitsCount,
  pendingRelationshipsCount,
  currentAnalysisStage,
  handleBuildKnowledgeGraph,
  isBackfilling,
  backfillStatus,
}) => {
  const [publicRepositories, setPublicRepositories] = useState<Repository[]>([]);
  const [isLoadingPublicRepos, setIsLoadingPublicRepos] = useState(false);
  const [publicReposError, setPublicReposError] = useState<string | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState<string | null>(null);
  const [isBackfillingRelationships, setIsBackfillingRelationships] = useState(false);
  const [backfillRelationshipsStatus, setBackfillRelationshipsStatus] = useState<string | null>(null);
  
  // --- NEW STATE FOR DEPLOYMENT CONSTITUTION FLOW ---
  const [currentStep, setCurrentStep] = useState('repository_selection'); // 'repository_selection' | 'deployment_constitution'
  const [deploymentConstitution, setDeploymentConstitution] = useState(null);
  const [isLoadingConstitution, setIsLoadingConstitution] = useState(false);
  const [constitutionError, setConstitutionError] = useState<string | null>(null);
  // --- END NEW STATE ---

  const [repositoryStatuses, setRepositoryStatuses] = useState<Record<string, {
    checkedStatus: 'idle' | 'checking' | 'not_started' | 'in_progress' | 'completed' | 'error' | 'initial_analysis_complete';
    checkError: string | null;
  }>>({});
  const router = useRouter();
  const currentPathname = usePathname();
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Auto-select installation if only one exists
    if (installations && installations.length === 1 && !selectedInstallationId) {
      console.log(`[Auto-select] Found only one installation (ID: ${installations[0].id}), auto-selecting it.`);
      setSelectedInstallationId(installations[0].id.toString());
    }
  }, [installations, selectedInstallationId, setSelectedInstallationId]);

  useEffect(() => {
    if (analysisType === 'public') {
      const fetchPublicRepos = async () => {
        console.log('[RepositorySetupFlow] Attempting to fetch public repositories from /api/github/repositories?installationId=0');
        setIsLoadingPublicRepos(true);
        setPublicReposError(null);
        try {
          // Using installationId=0 as a convention for fetching all public repos from our system
          const response = await fetch(`/api/github/repositories?installationId=0`);
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: 'Failed to fetch public repositories' }));
            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          if (data.repositories) {
            setPublicRepositories(data.repositories);
          }
        } catch (error) {
          console.error("Error fetching public repositories:", error);
          setPublicReposError(error instanceof Error ? error.message : String(error));
        } finally {
          setIsLoadingPublicRepos(false);
        }
      };
      fetchPublicRepos();
    } else {
      // Clear public repos if switching away from public tab
      setPublicRepositories([]);
    }
  }, [analysisType]);

  useEffect(() => {
    // Sync with props if they change from parent, but only if not actively polling
    if (!pollingIntervalRef.current) {
    }
  }, [repoCheckedStatus, currentAnalysisStage, pendingPrCommitsCount, pendingRelationshipsCount, repoCheckError]);

  // Effect to fetch statuses for all repositories when they are loaded
  useEffect(() => {
    if (analysisType === 'private' && selectedInstallationId && repositories.length > 0) {
      // Create a map to hold the new statuses
      const newStatuses: typeof repositoryStatuses = {};
  
      // Create a promise for each repository status check
      const statusPromises = repositories.map(async (repo) => {
        const repoSlug = repo.full_name;
        try {
          const response = await fetch(`/api/analysis/repository-status?repositorySlug=${repoSlug}&isPublic=false&installationId=${selectedInstallationId}`);
          const data = await response.json();
          if (data.success) {
            newStatuses[repoSlug] = {
              checkedStatus: data.analysisOverallStatus,
              checkError: null,
            };
          } else {
            newStatuses[repoSlug] = {
              checkedStatus: 'error',
              checkError: data.message || 'Failed to fetch status',
            };
          }
        } catch (error) {
          console.error(`Error fetching status for ${repoSlug}:`, error);
          newStatuses[repoSlug] = {
            checkedStatus: 'error',
            checkError: 'Network error while fetching status',
          };
        }
      });
  
      // Wait for all promises to complete
      Promise.all(statusPromises).then(() => {
        setRepositoryStatuses(newStatuses);
      });
    }
  }, [repositories, selectedInstallationId, analysisType]);

  useEffect(() => {
    if (analysisType === 'public' && publicRepositories.length > 0) {
      const newStatuses: typeof repositoryStatuses = {};
      const statusPromises = publicRepositories.map(async (repo) => {
        const repoSlug = repo.full_name;
        try {
          const response = await fetch(`/api/analysis/repository-status?repositorySlug=${repoSlug}&isPublic=true`);
          const data = await response.json();
          if (data.success) {
            newStatuses[repoSlug] = {
              checkedStatus: data.analysisOverallStatus,
              checkError: null,
            };
          } else {
            newStatuses[repoSlug] = {
              checkedStatus: 'error',
              checkError: data.message || 'Failed to fetch status',
            };
          }
        } catch (error) {
          console.error(`Error fetching status for ${repoSlug}:`, error);
          newStatuses[repoSlug] = {
            checkedStatus: 'error',
            checkError: 'Network error while fetching status',
          };
        }
      });

      Promise.all(statusPromises).then(() => {
        setRepositoryStatuses(newStatuses);
      });
    }
  }, [publicRepositories, analysisType]);

  useEffect(() => {
    const pollableRepos = Object.entries(repositoryStatuses)
        .filter(([_, s]) => s.checkedStatus === 'in_progress' || s.checkedStatus === 'checking')
        .map(([slug]) => slug);

    if (pollableRepos.length === 0) {
        if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
        }
        return;
    }

    const pollingFn = async () => {
        console.log(`[Polling] Checking status for: ${pollableRepos.join(', ')}`);
        const newStatuses = { ...repositoryStatuses };
        let hasChanged = false;

        const promises = pollableRepos.map(async (repoSlug) => {
            try {
                let url = `/api/analysis/repository-status?repositorySlug=${repoSlug}`;
                if (analysisType === 'public') {
                    url += '&isPublic=true';
                } else {
                    // This assumes that for a pollable private repo, selectedInstallationId is valid.
                    url += `&isPublic=false&installationId=${selectedInstallationId}`;
                }

                const res = await fetch(url);
                const data = await res.json();
                if (data.success && newStatuses[repoSlug].checkedStatus !== data.analysisOverallStatus) {
                    newStatuses[repoSlug] = {
                        checkedStatus: data.analysisOverallStatus,
                        checkError: null,
                    };
                    hasChanged = true;
                } else if (!data.success) {
                    newStatuses[repoSlug] = { checkedStatus: 'error', checkError: data.error || 'Polling failed' };
                    hasChanged = true;
                }
            } catch (error) {
                console.error(`[Polling] Error for ${repoSlug}:`, error);
                newStatuses[repoSlug] = { checkedStatus: 'error', checkError: 'Network error during poll' };
                hasChanged = true; // Stop polling on error
            }
        });

        await Promise.all(promises);

        if (hasChanged) {
            setRepositoryStatuses(newStatuses);
        }
    };

    if (!pollingIntervalRef.current) {
        pollingIntervalRef.current = setInterval(pollingFn, 5000);
    }

    return () => {
        if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
        }
    };
}, [repositoryStatuses, selectedInstallationId, analysisType]);


  // Add a type guard function to check status
  const hasStatus = (
    status: 'idle' | 'checking' | 'exists' | 'not_found' | 'error',
    value: string
  ): boolean => status === value;

  // Set fixed configuration values (not exposed to UI)
  useEffect(() => {
    setMaxHistoricalPRs(1000);
    setAnalysisBatchSize(1);
    setSinceDate('');
  }, [setMaxHistoricalPRs, setAnalysisBatchSize, setSinceDate]);

  // Handle publicRepoInput submission locally
  const handlePublicRepoSubmit = useCallback((input: string) => {
    // setSubmittedRepoInput(input); // This was causing navigation
    setAnalyzeRepoStatus('');
    
    if (input && input.includes('/')) {
      const newPath = `/analysis/public/${input}`;
      console.log(`[RepositorySetupFlow] Public repo submitted: ${input}. Navigation is disabled.`);
      
      // Add to list if not already present
      if (!publicRepositories.some(repo => repo.full_name === input)) {
        // We don't have a real repo object, so we create a partial one.
        // The ID can be a temporary one, as full_name is the key.
        const newRepo: Repository = {
          id: Date.now(), // Temporary ID
          name: input.split('/')[1],
          full_name: input,
          private: false,
        };
        const updatedRepos = [...publicRepositories, newRepo];
        setPublicRepositories(updatedRepos);

        // Immediately check status for the new repo
        const checkNewRepoStatus = async () => {
          try {
            const response = await fetch(`/api/analysis/repository-status?repositorySlug=${input}&isPublic=true`);
            const data = await response.json();
            setRepositoryStatuses(prev => ({
              ...prev,
              [input]: {
                checkedStatus: data.success ? data.analysisOverallStatus : 'error',
                checkError: data.success ? null : (data.message || 'Failed to fetch status'),
              }
            }));
          } catch (error) {
            console.error(`Error fetching status for new repo ${input}:`, error);
            setRepositoryStatuses(prev => ({
              ...prev,
              [input]: {
                checkedStatus: 'error',
                checkError: 'Network error while fetching status',
              }
            }));
          }
        };
        checkNewRepoStatus();
      }
    } else {
      console.warn("[RepositorySetupFlow] Invalid public repo input for navigation.");
    }
  }, [setAnalyzeRepoStatus, publicRepositories, setPublicRepositories, setRepositoryStatuses]);

  const handleStartAnalysis = async (repoSlug: string, infraRepoUrl?: string) => {
    setSelectedRepositorySlug(repoSlug);
    // Move to the constitution step
    setCurrentStep('deployment_constitution');
    setIsLoadingConstitution(true);
    setConstitutionError(null);

    try {
      const apiBody = {
        repositorySlug: repoSlug,
        isPublic: analysisType === 'public',
        installationId: analysisType === 'private' ? selectedInstallationId : undefined,
        infraRepoUrl: infraRepoUrl,
      };

      // This is a placeholder for the new API endpoint
      const response = await fetch('/api/analysis/generate-deployment-constitution', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiBody),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate deployment constitution.');
      }
      
      setDeploymentConstitution(data.constitution);
    } catch (error) {
      setConstitutionError(error instanceof Error ? error.message : String(error));
    } finally {
      setIsLoadingConstitution(false);
    }
  };

  const handleConstitutionConfirmed = () => {
    // Now that the constitution is confirmed, proceed with the original analysis
    if (selectedRepositorySlug) {
      setRepositoryStatuses(prev => ({
        ...prev,
        [selectedRepositorySlug]: { checkedStatus: 'in_progress', checkError: null }
      }));
      startRepoAnalysisInternal(false, selectedRepositorySlug);
      // Move back to the main view after starting
      setCurrentStep('repository_selection');
    }
  };

  const startRepoAnalysisInternal = useCallback(async (testMode = false, repoSlugOverride?: string) => {
    const repoToAnalyze = repoSlugOverride || currentRepoSlug;

    console.log('[RepositorySetupFlow] CALLED startRepoAnalysisInternal. testMode:', testMode, 'targetRepoSlug:', repoToAnalyze);

    let apiBody: Record<string, any> = {
      batchSize: testMode ? 1 : analysisBatchSize,
      sinceDate: sinceDate || null,
      maxHistoricalPRs: maxHistoricalPRs,
      testMode: !!testMode,
    };

    if (!repoToAnalyze || !repoToAnalyze.includes('/')) {
        setAnalyzeRepoStatus('Error: Repository not properly selected or URL is incorrect.');
        return null;
    }

    apiBody.repositorySlug = repoToAnalyze;

    if (analysisType === 'public') {
      apiBody.isPublic = true;
    } else if (analysisType === 'private') {
      if (!selectedInstallationId) {
        setAnalyzeRepoStatus('Error: Installation ID missing for private repository analysis.');
        return null;
      }
      apiBody.installationId = selectedInstallationId;
      apiBody.isPublic = false;
    } else {
      setAnalyzeRepoStatus('Error: Analysis type is not determined (public/private).');
      return null;
    }
    
    setAnalyzeRepoStatus(`Repository analysis in progress. You will be notified when complete.`);

    console.log(`Initiating repository ${testMode ? 'test PR' : ''} analysis job for ${apiBody.repositorySlug} (Type: ${analysisType})`);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 10)); 
      
      console.log('[RepositorySetupFlow] About to fetch /api/analysis/start with body:', JSON.stringify(apiBody));
      const response = await fetch('/api/analysis/start', { 
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiBody),
      });
      console.log('[RepositorySetupFlow] fetch /api/analysis/start RESPONSE STATUS:', response.status);
      
      const data = await response.json();
      console.log('[RepositorySetupFlow] fetch /api/analysis/start RESPONSE DATA:', data);

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to start analysis job');
      }
      
      console.log(`Repository population request successful for ${repoToAnalyze}.`);

      return { 
        success: true, 
        message: data.message || `Analysis initiated for ${apiBody.repositorySlug}. You will be notified upon completion.` 
      }; 
    } catch (error) {
      console.error('[RepositorySetupFlow] Error in startRepoAnalysisInternal (during/after fetch):', error);
      setAnalyzeRepoStatus(`Error starting analysis: ${error instanceof Error ? error.message : String(error)}`);
      // Revert status on error
      setRepositoryStatuses(prev => ({
        ...prev,
        [repoToAnalyze]: { checkedStatus: 'error', checkError: error instanceof Error ? error.message : String(error) }
      }));
      return null;
    }
  }, [
    analysisType,
    selectedInstallationId,
    analysisBatchSize,
    sinceDate,
    maxHistoricalPRs,
    currentRepoSlug,
    setAnalyzeRepoStatus,
    setRepositoryStatuses
  ]);

  const startTestPrAnalysis = useCallback(async () => {
    if (repoCheckedStatus === 'completed') { 
      console.log("Test PR Analysis: Repository already analyzed (status 'completed'). Not starting.");
      return;
    }
    // Prevent re-entry if local component is already analyzing OR if backend reports it as in_progress.
    if (repoCheckedStatus === 'in_progress') {
        console.log(`Test PR Analysis: Not starting. Backend repoCheckedStatus: ${repoCheckedStatus}.`);
        return;
    }
    if (!currentRepoSlug) {
      console.log("Test PR Analysis: currentRepoSlug is not set. Cannot start.");
      setAnalyzeRepoStatus("Error: Repository not selected.");
      return;
    }
        
    console.log("Requesting test PR analysis for:", currentRepoSlug);
    
    const jobStartResult = await startRepoAnalysisInternal(true);
    
    if (!jobStartResult || !jobStartResult.success) {
      console.error("Test PR analysis job failed to start.");
    } else {
      console.log(`Analysis process initiated (starting with test PR). UI will show 'in progress'.`);
    }
  }, [currentRepoSlug, startRepoAnalysisInternal, repoCheckedStatus, setAnalyzeRepoStatus]);

  const startFullAnalysis = useCallback(async () => {
    if (!currentRepoSlug) {
      setAnalyzeRepoStatus('Error: Repository not properly selected');
      return;
    }
    
    console.log("Requesting to ensure full analysis runs (e.g., activate skipped PRs and process) for:", currentRepoSlug);

    const jobStartResult = await startRepoAnalysisInternal(false); 

    if (!jobStartResult || !jobStartResult.success) {
      console.error("Full analysis job failed to start.");
    } else {
      console.log(`Full analysis process initiated. UI will show 'in progress'.`);
    }

  }, [currentRepoSlug, startRepoAnalysisInternal, setAnalyzeRepoStatus]);

  console.log('[RepositorySetupFlow] Defining handleSetupComplete. currentRepoSlug (at definition time):', currentRepoSlug);
  const handleSetupComplete = useCallback(() => {
    console.log('[RepositorySetupFlow] CALLED handleSetupComplete. currentRepoSlug (at call time):', currentRepoSlug);
    if (currentRepoSlug && currentRepoSlug.includes('/')) {
      // Ensure we only start if not already completed or in progress from backend
      if (repoCheckedStatus === 'not_started' || repoCheckedStatus === 'error') {
        console.log("Starting analysis (test PR first) for:", currentRepoSlug);
        startTestPrAnalysis(); 
      } else {
        console.log(`[RepositorySetupFlow] handleSetupComplete: Not starting analysis as repoCheckedStatus is ${repoCheckedStatus}`);
      }
    } else {
      console.log("Can't start analysis (via handleSetupComplete): no valid repo selected/submitted.");
      setAnalyzeRepoStatus("Error: No valid repository selected to start analysis.");
    }
  }, [currentRepoSlug, startTestPrAnalysis, setAnalyzeRepoStatus, repoCheckedStatus]);

  const handleFinishSetup = useCallback(() => {
    setIsSetupMode(false);
  }, [setIsSetupMode]);

  const showWizardView = repoCheckedStatus === 'initial_analysis_complete' || repoCheckedStatus === 'completed';

  // const showSetupOptions = !showInProgressMessage && repoCheckedStatus !== 'completed'; // This logic will be embedded

  const handleSyncLatestPRs = useCallback(async () => {
    if (!currentRepoSlug) {
      setSyncStatus("Error: Repository slug not available.");
      return;
    }
    setIsSyncing(true);
    setSyncStatus("Syncing latest PRs and commits...");

    try {
      const apiBody: Record<string, any> = {
        repositorySlug: currentRepoSlug,
      };
      if (analysisType === 'private' && selectedInstallationId) {
        apiBody.installationId = selectedInstallationId;
      } else if (analysisType === 'public') {
        apiBody.isPublic = true; // Explicitly indicate public
      } else if (analysisType === 'private' && !selectedInstallationId) {
        setSyncStatus("Error: Installation ID is required for private repositories but not found.");
        setIsSyncing(false);
        return;
      }


      console.log('[RepositorySetupFlow] Calling /api/analysis/sync-latest with body:', JSON.stringify(apiBody));
      const response = await fetch('/api/analysis/sync-latest', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiBody),
      });

      const data = await response.json();
      console.log('[RepositorySetupFlow] /api/analysis/sync-latest RESPONSE:', data);

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to sync latest PRs.');
      }
      setSyncStatus(data.message || 'Successfully synced latest items. New items are pending analysis.');
      // Optionally, trigger a re-fetch of decisions or update status if the sync implies new data is ready for immediate viewing
      // For now, we just show a message. The cron job will pick up 'pending' items.
      if (fetchDecisions) {
        console.log("Sync complete, re-fetching decisions if any new PRs were processed immediately.");
        // Delay slightly to allow backend processing if any immediate changes were made
        // setTimeout(() => fetchDecisions(), 1000); // This might be too soon.
      }

    } catch (error) {
      console.error('[RepositorySetupFlow] Error in handleSyncLatestPRs:', error);
      setSyncStatus(`Error syncing: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsSyncing(false);
    }
  }, [currentRepoSlug, analysisType, selectedInstallationId, fetchDecisions]);

  const handleBackfillRelationships = useCallback(async () => {
    if (!currentRepoSlug) {
      setBackfillRelationshipsStatus("Error: Repository not selected.");
      return;
    }
    setIsBackfillingRelationships(true);
    setBackfillRelationshipsStatus(`Starting relationship backfill for ${currentRepoSlug}...`);

    try {
      const apiBody = {
        repositorySlug: currentRepoSlug,
        isPublic: analysisType === 'public',
        installationId: analysisType === 'private' ? selectedInstallationId : undefined,
      };

      console.log('[RepositorySetupFlow] Calling /api/analysis/backfill-relationships with body:', JSON.stringify(apiBody));
      const response = await fetch('/api/analysis/backfill-relationships', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiBody),
      });

      const data = await response.json();
      console.log('[RepositorySetupFlow] /api/analysis/backfill-relationships RESPONSE:', data);

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to backfill relationships.');
      }
      setBackfillRelationshipsStatus(data.message || 'Backfill completed successfully.');
    } catch (error) {
      console.error('[RepositorySetupFlow] Error in handleBackfillRelationships:', error);
      setBackfillRelationshipsStatus(`Error during backfill: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsBackfillingRelationships(false);
    }
  }, [currentRepoSlug, analysisType, selectedInstallationId]);


  return (
    <div className="max-w-2xl mx-auto p-4 bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
      <h2 className="text-xl font-semibold mb-4">{currentRepoSlug ? currentRepoSlug : 'Choose codebase repository'}</h2>
      
      <div className="flex gap-2 mb-2">
        {/* Debug button in dev only */}
        {process.env.NODE_ENV === 'development' && (
          <button 
            onClick={logAuthStatus} 
            className="text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded"
          >
            Debug Auth
          </button>
        )}
        
        {/* Temporary Backfill button for jisha/grafana in production */}
        {process.env.NODE_ENV === 'production' && currentRepoSlug === 'jisha/grafana' && (
          <button
            onClick={handleBackfillRelationships}
            disabled={isBackfillingRelationships || !currentRepoSlug}
            className="text-xs bg-purple-200 dark:bg-purple-800 px-2 py-1 rounded disabled:opacity-50"
          >
            {isBackfillingRelationships ? 'Backfilling...' : 'Backfill Relationships'}
          </button>
        )}
      </div>
      
      {backfillRelationshipsStatus && (
        <div className={`mt-2 p-2 rounded text-xs ${backfillRelationshipsStatus.toLowerCase().startsWith('error:') ? 'bg-red-50 text-red-700' : 'bg-blue-50 text-blue-700'}`}>
          {backfillRelationshipsStatus}
        </div>
      )}
      
      {currentStep === 'deployment_constitution' ? (
        <DeploymentConstitutionStep
          constitution={deploymentConstitution}
          isLoading={isLoadingConstitution}
          error={constitutionError}
          onConfirm={handleConstitutionConfirmed}
          onUpdateInfraRepo={(repoUrl) => handleStartAnalysis(selectedRepositorySlug, repoUrl)}
        />
      ) : (
        <>
          {/* Analysis Type Selection */}
          <div className="mt-6 grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Option 1: Public Repository */}
            <button
              className={`${
                analysisType === 'public'
                  ? 'border-blue-500 bg-blue-50 text-blue-600 ring-2 ring-blue-600'
                  : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50 dark:border-zinc-600 dark:bg-zinc-800 dark:text-gray-100 dark:hover:bg-zinc-700'
              } flex flex-col items-center justify-center rounded-lg border p-6 text-center`}
              onClick={() => {
                console.log("Public repo button clicked");
                setAnalysisType('public');
                setAnalyzeRepoStatus('');
                setSelectedInstallationId('');
                setSelectedRepositorySlug('');
              }}
            >
              <GlobeIcon className="h-8 w-8 mb-2" />
              <h3 className="text-lg font-medium">Demo Repositories</h3>
            </button>

            {/* Option 2: Private Repository */}
            <button
              className={`${
                analysisType === 'private'
                  ? 'border-blue-500 bg-blue-50 text-blue-600 ring-2 ring-blue-600'
                  : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50 dark:border-zinc-600 dark:bg-zinc-800 dark:text-gray-100 dark:hover:bg-zinc-700'
              } flex flex-col items-center justify-center rounded-lg border p-6 text-center`}
              onClick={() => {
                console.log("Private repo button clicked");
                setAnalysisType('private');
                setAnalyzeRepoStatus('');
                setPublicRepoInput('');
              }}
            >
              <LockClosedIcon className="h-8 w-8 mb-2" />
              <h3 className="text-lg font-medium">My Repositories</h3>
            </button>
          </div>

          {/* Repository Selection (based on type) */}
          {analysisType === 'public' ? (
            <div className="mt-6">
              <label htmlFor="publicRepo" className="block text-sm font-medium mb-2">Public Repository</label>
              <div className="flex">
                <input
                  id="publicRepo"
                  type="text"
                  value={publicRepoInput}
                  onChange={(e) => setPublicRepoInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      console.log("Enter pressed for repo input:", publicRepoInput);
                      handlePublicRepoSubmit(publicRepoInput.trim());
                      e.preventDefault();
                    }
                  }}
                  placeholder="owner/repo (e.g., facebook/react)"
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-zinc-700"
                />
                <button
                  onClick={() => {
                    console.log("Submitting repo input:", publicRepoInput);
                    handlePublicRepoSubmit(publicRepoInput.trim());
                  }}
                  className="ml-2 px-3 py-2 bg-gray-200 dark:bg-gray-700 rounded-md text-sm"
                >
                  Submit
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Enter the owner/repo name and press Enter or click Submit. Below is a list of already analyzed public codebases.
              </p>
              <div className="mt-4">
                {isLoadingPublicRepos ? (
                  <div className="text-center text-gray-500">Loading public repositories...</div>
                ) : (
                  <RepositoryList
                    repositories={publicRepositories}
                    repositoryStatuses={repositoryStatuses}
                    onStartAnalysis={handleStartAnalysis}
                    isLoadingRepositories={false} // We handle loading state above
                    selectedInstallationId={"0"} // For public repos
                    analysisType={analysisType}
                  />
                )}
                {publicReposError && <p className="mt-2 text-xs text-red-500">Error: {publicReposError}</p>}
              </div>
            </div>
          ) : analysisType === 'private' ? (
            <div className="mt-6">
              <>
                <div className="mb-4">
                  <label htmlFor="installation" className="block text-sm font-medium mb-2">GitHub Installation</label>
                  <select
                    id="installation"
                    value={selectedInstallationId}
                    onChange={(e) => {
                      console.log("Installation selected:", e.target.value);
                      const newInstallId = e.target.value;
                      setSelectedInstallationId(newInstallId);
                      setSelectedRepositorySlug(''); 
                    }}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-zinc-700 disabled:opacity-50"
                    disabled={isLoadingInstallations || installations.length === 0}
                  >
                    <option value="">
                      {isLoadingInstallations ? 'Loading...' : 'Select an installation'}
                    </option>
                    {installations.map(installation => (
                      <option key={installation.id} value={installation.id.toString()}>
                        {installation.account.login} ({installation.account.type || 'User'})
                      </option>
                    ))}
                  </select>
                  {installationError && !installationError.includes("No installations found") && (
                    <p className="mt-1 text-xs text-red-500">
                      Error: {installationError}
                    </p>
                  )}
                  {(installations.length === 0 && !isLoadingInstallations && 
                    (!installationError || installationError.includes("No installations found"))) && (
                    <div className="mt-4 text-center">
                      <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                        No GitHub app installations found. Please install our app to access your private repositories.
                      </p>
                      <a
                        href="https://github.com/apps/aiguardrails"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-2 -ml-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                        Install AIGuardRails GitHub App
                      </a>
                    </div>
                  )}
                  {installations.length > 0 && selectedInstallationId && (
                    <p className="mt-1 text-xs text-blue-500">
                      Need access to more repositories? <a 
                        href={`https://github.com/settings/installations/${selectedInstallationId}`}
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        Select additional repositories
                      </a> for this installation.
                    </p>
                  )}
                </div>
                <div>
                  <RepositoryList
                    repositories={repositories}
                    repositoryStatuses={repositoryStatuses}
                    onStartAnalysis={handleStartAnalysis}
                    isLoadingRepositories={isLoadingRepositories}
                    selectedInstallationId={selectedInstallationId}
                    analysisType={analysisType}
                  />
                </div>
              </>
            </div>
          ) : (
            <div className="text-center p-6 mt-6 text-gray-500 bg-gray-50 dark:bg-zinc-800 dark:text-gray-400 rounded-lg">
              Please select a repository type to continue
            </div>
          )}

          {/* Action Buttons */} 
          <div className="mt-6 flex justify-start">
            <button
              onClick={() => setIsSetupMode(false)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700"
            >
              Cancel
            </button>
          </div>
        </>
      )}

      {/* Status/Error Messages shown by setAnalyzeRepoStatus, only if not showing global inProgress or completed state, 
          and not already handled by specific status messages within the setup UI */}
      {analyzeRepoStatus &&
       !(analysisType && currentRepoSlug && (repoCheckedStatus === 'checking' || repoCheckedStatus === 'error' || repoCheckedStatus === 'not_started')) &&
       ( 
        <div className={`mt-4 p-3 rounded text-sm ${analyzeRepoStatus.startsWith('Error:') ? 'bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400' : 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'}`}>
          {analyzeRepoStatus}
        </div>
      )}

      {/* Sync Status Message Area - shown below everything else if a message exists */}
      {syncStatus && (
        <div className={`mt-4 p-3 rounded text-sm ${syncStatus.toLowerCase().startsWith('error:') ? 'bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400' : 'bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400'}`}>
          {syncStatus}
        </div>
      )}
    </div>
  );
};

// Export the memoized component
export const RepositorySetupFlow = React.memo(RepositorySetupFlowComponent); 