import React, { useState } from 'react';
import type { DecisionRemovalReason, DecisionPoint } from '../types/design-doc-wizard';

interface DecisionRemovalModalProps {
  isOpen: boolean;
  decision: DecisionPoint | null;
  onClose: () => void;
  onConfirmRemoval: (decisionId: string, reason: DecisionRemovalReason, contextNote: string) => void;
}

const removalOptions = [
  {
    value: 'already-decided' as const,
    label: 'We\'ve already decided this',
    description: 'This decision has already been made and should be captured as a constraint',
    icon: '✅',
    placeholder: 'What decision was made? (e.g., "We\'re using PostgreSQL as decided in our DB strategy session")'
  },
  {
    value: 'out-of-scope' as const,
    label: 'This is out of scope / non-goal',
    description: 'This decision is not relevant to this specific task',
    icon: '🚫',
    placeholder: 'Why is this out of scope? (e.g., "Authentication is handled by our identity service")'
  },
  {
    value: 'duplicate' as const,
    label: 'Duplicate of another decision',
    description: 'This decision is already covered by another decision point',
    icon: '🔄',
    placeholder: 'Which other decision covers this? (e.g., "Covered by the API Gateway decision")'
  },
  {
    value: 'not-relevant' as const,
    label: 'Not relevant to this task',
    description: 'This decision doesn\'t apply to what we\'re building',
    icon: '❌',
    placeholder: 'Why isn\'t this relevant? (optional)'
  },
  {
    value: 'custom' as const,
    label: 'Other reason',
    description: 'Provide your own reason for removing this decision',
    icon: '💭',
    placeholder: 'Explain why you\'re removing this decision...'
  }
];

export default function DecisionRemovalModal({ 
  isOpen, 
  decision, 
  onClose, 
  onConfirmRemoval 
}: DecisionRemovalModalProps) {
  const [selectedReason, setSelectedReason] = useState<DecisionRemovalReason | null>(null);
  const [contextNote, setContextNote] = useState('');

  if (!isOpen || !decision) return null;

  const selectedOption = removalOptions.find(opt => opt.value === selectedReason);

  const handleConfirm = () => {
    if (!selectedReason || !decision) return;
    
    onConfirmRemoval(decision.id, selectedReason, contextNote.trim());
    
    // Reset form
    setSelectedReason(null);
    setContextNote('');
  };

  const handleCancel = () => {
    onClose();
    setSelectedReason(null);
    setContextNote('');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Remove Technical Decision
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Help us understand why you're removing this decision so we can capture the context appropriately.
            </p>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        {/* Decision being removed */}
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            {decision.title}
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {decision.description}
          </p>
        </div>

        {/* Removal reason options */}
        <div className="space-y-3 mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white">
            Why are you removing this decision?
          </h4>
          
          {removalOptions.map((option) => (
            <label
              key={option.value}
              className={`flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedReason === option.value
                  ? 'border-amber-500 bg-amber-50 dark:bg-amber-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <input
                type="radio"
                value={option.value}
                checked={selectedReason === option.value}
                onChange={(e) => setSelectedReason(e.target.value as DecisionRemovalReason)}
                className="mt-1"
              />
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{option.icon}</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {option.label}
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {option.description}
                </p>
              </div>
            </label>
          ))}
        </div>

        {/* Context note input */}
        {selectedReason && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Additional Context
              {selectedReason === 'already-decided' || selectedReason === 'duplicate' ? (
                <span className="text-red-500 ml-1">*</span>
              ) : (
                <span className="text-gray-500 ml-1">(optional)</span>
              )}
            </label>
            <textarea
              value={contextNote}
              onChange={(e) => setContextNote(e.target.value)}
              placeholder={selectedOption?.placeholder}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
            />
            
            {/* Show where the context will be captured */}
            {selectedReason && (
              <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>📍 Context will be saved as:</strong>{' '}
                  {selectedReason === 'already-decided' && 'Project Constraint'}
                  {selectedReason === 'out-of-scope' && 'Non-Goal'}
                  {selectedReason === 'duplicate' && 'Project Assumption'}
                  {(selectedReason === 'not-relevant' || selectedReason === 'custom') && 'Removal Note'}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={
              !selectedReason || 
              (selectedReason === 'already-decided' && !contextNote.trim()) ||
              (selectedReason === 'duplicate' && !contextNote.trim())
            }
            className="px-6 py-2 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded-lg font-medium"
          >
            Remove Decision
          </button>
        </div>
      </div>
    </div>
  );
} 