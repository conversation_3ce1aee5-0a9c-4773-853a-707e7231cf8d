'use client'

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardD<PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { <PERSON><PERSON>, ExternalLink, Code, FileText, <PERSON><PERSON><PERSON>riangle, CheckCircle } from 'lucide-react';

interface ForensicAnalysis {
  primary_root_cause?: {
    decision_id: string;
    causation_confidence: number;
    failure_mechanism: string;
    evidence: string[];
    timeline_fit: string;
  };
  contributing_factors?: Array<{
    decision_id: string;
    contribution_type: string;
    explanation: string;
  }>;
  failure_chain?: string;
  alternative_theories?: string[];
}

interface SuspiciousDecision {
  id: string;
  metadata?: {
    title?: string;
    description?: string;
    confidence_score?: number;
    follows_standard_practice?: boolean;
    pr_merged_at?: string;
    domain_concepts?: string[];
    related_files?: string[];
    implications?: string;
    rationale?: string;
  };
  hypothesis_match: string;
  hypothesis_likelihood: 'HIGH' | 'MEDIUM' | 'LOW';
  search_query: string;
  score?: number;
}

interface Symptoms {
  errorType: string;
  affectedComponents: string[];
  userImpact: string;
  timePattern: string;
  environment: string;
}

interface CodingAgentPromptProps {
  forensicAnalysis: ForensicAnalysis;
  suspiciousDecisions: SuspiciousDecision[];
  symptoms: Symptoms;
  repositorySlug: string;
}

function discoverRelevantFiles(
  relatedDecision: any,
  suspiciousDecisions: SuspiciousDecision[],
  symptoms: Symptoms,
  forensicAnalysis: ForensicAnalysis
): string[] {
  const discoveredFiles = new Set<string>();

  // 1. Files from the primary root cause decision
  if (relatedDecision?.metadata?.related_files) {
    relatedDecision.metadata.related_files.forEach((file: string) => discoveredFiles.add(file));
  }

  // 2. Files from all suspicious decisions (not just root cause)
  suspiciousDecisions.forEach(decision => {
    if (decision.metadata?.related_files) {
      decision.metadata.related_files.forEach(file => discoveredFiles.add(file));
    }
  });

  // 3. Only add files that we're confident exist (remove hallucinated inference)
  // We'll rely on the coding agent to identify the actual files based on symptoms

  return Array.from(discoveredFiles).filter(file => file && file.length > 0);
}



export default function CodingAgentPrompt({ 
  forensicAnalysis, 
  suspiciousDecisions, 
  symptoms, 
  repositorySlug 
}: CodingAgentPromptProps) {
  const [copied, setCopied] = useState(false);

  const generatePrompt = () => {
    const rootCause = forensicAnalysis.primary_root_cause;
    const relatedDecision = suspiciousDecisions.find(d => d.id === rootCause?.decision_id);
    
    // Comprehensive file discovery from multiple sources
    const relatedFiles = discoverRelevantFiles(
      relatedDecision,
      suspiciousDecisions,
      symptoms,
      forensicAnalysis
    );
    
    const prompt = `# RCA Verification and Fix Implementation Request

## Context
Repository: ${repositorySlug}
Analysis Date: ${new Date().toISOString()}
Confidence Level: ${rootCause ? Math.round(rootCause.causation_confidence * 100) : 0}%

## Incident Summary
**Error Type:** ${symptoms.errorType}
**User Impact:** ${symptoms.userImpact}
**Environment:** ${symptoms.environment}
**Time Pattern:** ${symptoms.timePattern}
**Affected Components:** ${symptoms.affectedComponents.join(', ')}

## Root Cause Analysis Findings

### Primary Root Cause
**Decision ID:** ${rootCause?.decision_id || 'Not identified'}
**Confidence:** ${rootCause ? Math.round(rootCause.causation_confidence * 100) : 0}%

**Failure Mechanism:**
${rootCause?.failure_mechanism || 'Not specified'}

**Timeline Analysis:**
${rootCause?.timeline_fit || 'Not specified'}

**Supporting Evidence:**
${rootCause?.evidence.map(e => `- ${e}`).join('\n') || 'No evidence provided'}

### Files to Investigate

**From Architectural Decisions:**
${relatedDecision?.metadata?.related_files && relatedDecision.metadata.related_files.length > 0 ? 
  relatedDecision.metadata.related_files.map((file: string) => `- ${file}`).join('\n') : 
  'No files found in root cause decision metadata'}

**From All Suspicious Decisions:**
${suspiciousDecisions.flatMap(d => d.metadata?.related_files || []).length > 0 ?
  [...new Set(suspiciousDecisions.flatMap(d => d.metadata?.related_files || []))].map((file: string) => `- ${file}`).join('\n') :
  'No files found in suspicious decisions metadata'}

**Key Symptoms to Investigate:**
- Error Type: ${symptoms.errorType}
- Affected Components: ${symptoms.affectedComponents.join(', ')}
- User Impact: ${symptoms.userImpact}
- Environment: ${symptoms.environment}

**All Files to Examine:**
${relatedFiles.length > 0 ? relatedFiles.map(file => `- ${file}`).join('\n') : 'Please identify relevant files based on the symptoms and failure mechanism described above.'}

### Contributing Factors
${forensicAnalysis.contributing_factors?.map(factor => 
  `**${factor.contribution_type.toUpperCase()}:** ${factor.explanation} (Decision: ${factor.decision_id})`
).join('\n\n') || 'No contributing factors identified'}

### Failure Chain
${forensicAnalysis.failure_chain || 'Not specified'}

## Verification Tasks

Please perform the following verification and analysis tasks:

### 1. Code Review and Verification
- [ ] **Examine the related files** listed above for the suspected architectural decision
- [ ] **Verify the failure mechanism** by analyzing the code paths described
- [ ] **Check recent changes** around the timeline mentioned in the analysis
- [ ] **Validate the evidence** by looking for the specific patterns or issues mentioned

### 2. Root Cause Confirmation
- [ ] **Reproduce the issue** in a test environment if possible
- [ ] **Trace the execution path** that leads to the failure
- [ ] **Identify the exact code location** where the failure occurs
- [ ] **Confirm the architectural decision** that introduced the problematic pattern

### 3. Impact Assessment
- [ ] **Assess blast radius** - what other parts of the system might be affected
- [ ] **Check for similar patterns** elsewhere in the codebase
- [ ] **Evaluate data integrity** impacts if applicable
- [ ] **Review monitoring and alerting** coverage for this failure mode

## Fix Implementation Plan

### Immediate Actions (Hot Fix)
1. **Implement minimal fix** to stop the bleeding:
   - Identify the quickest, safest change to resolve user impact
   - Add temporary monitoring/alerting if needed
   - Implement circuit breakers or fallbacks if applicable

### Short-term Fix (Within Sprint)
1. **Address the root cause** identified in the analysis:
   - Refactor the problematic architectural decision
   - Implement proper error handling
   - Add comprehensive testing for the failure scenario

2. **Prevent recurrence:**
   - Add monitoring for early detection
   - Implement safeguards against similar failures
   - Update documentation and runbooks

### Long-term Improvements (Next Quarter)
1. **Architectural improvements:**
   - Review and improve the overall design pattern
   - Consider more resilient alternatives
   - Implement better separation of concerns

2. **Process improvements:**
   - Add automated tests for this failure scenario
   - Improve deployment and rollback procedures
   - Enhance monitoring and observability

## Alternative Theories to Investigate

${forensicAnalysis.alternative_theories?.map(theory => `- ${theory}`).join('\n') || 'No alternative theories provided'}

## Specific Questions to Answer

1. **Is the identified root cause accurate?** 
   - Can you confirm the failure mechanism by examining the code?
   - Does the timeline align with recent deployments or changes?

2. **Are there additional contributing factors?**
   - What other code changes might have amplified this issue?
   - Are there environmental factors not captured in the analysis?

3. **What is the complete fix strategy?**
   - What specific code changes are needed?
   - What testing is required to validate the fix?
   - What monitoring should be added to prevent recurrence?

4. **Risk assessment:**
   - What is the risk of the proposed fix introducing new issues?
   - Are there any dependencies or side effects to consider?

## Expected Deliverables

1. **Verification Report:**
   - Confirmation or correction of the root cause analysis
   - Evidence from code examination
   - Additional findings not captured in the original analysis

2. **Detailed Fix Plan:**
   - Specific code changes required (with file paths and line numbers)
   - Testing strategy and test cases
   - Deployment and rollback plan

3. **Prevention Strategy:**
   - Code changes to prevent similar issues
   - Monitoring and alerting improvements
   - Process or architectural recommendations

## Files to Examine

${relatedFiles.length > 0 ? 
  relatedFiles.map(file => `- \`${file}\``).join('\n') : 
  'Please identify relevant files based on the symptoms and failure mechanism described above.'
}

---

**Note:** This analysis was generated by Nuvineer RCA Wizard. Please validate all findings through code examination and testing before implementing any fixes.`;

    return prompt;
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(generatePrompt());
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
    }
  };

  const handleOpenFile = (filePath: string) => {
    // This would ideally integrate with the IDE or repository viewer
    // For now, we'll construct a GitHub URL if possible
    const githubUrl = `https://github.com/${repositorySlug}/blob/main/${filePath}`;
    window.open(githubUrl, '_blank');
  };

  const rootCause = forensicAnalysis.primary_root_cause;
  const relatedDecision = suspiciousDecisions.find(d => d.id === rootCause?.decision_id);
  
  // Get files from different sources (only real files from decisions)
  const decisionFiles = relatedDecision?.metadata?.related_files || [];
  const allDecisionFiles = [...new Set(suspiciousDecisions.flatMap(d => d.metadata?.related_files || []))];
  
  const relatedFiles = allDecisionFiles;

  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Code className="h-5 w-5 text-purple-600" />
          <CardTitle>Coding Agent Verification Prompt</CardTitle>
        </div>
        <CardDescription>
          Comprehensive prompt for AI coding agents to verify RCA findings and implement fixes
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {rootCause ? Math.round(rootCause.causation_confidence * 100) : 0}%
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Confidence</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{relatedFiles.length}</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Files to Check</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {forensicAnalysis.contributing_factors?.length || 0}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Contributing Factors</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {forensicAnalysis.alternative_theories?.length || 0}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Alt. Theories</div>
          </div>
        </div>

        {/* Key Files to Investigate */}
        <div className="space-y-4">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Files to Investigate
            <Badge variant="outline">{relatedFiles.length}</Badge>
          </h3>
          
          {/* Files from Decisions */}
          {allDecisionFiles.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                📋 From Architectural Decisions ({allDecisionFiles.length})
              </h4>
              <div className="grid gap-2">
                {allDecisionFiles.slice(0, 10).map((file, index) => (
                  <div key={`decision-${index}`} className="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <code className="text-sm font-mono text-blue-800 dark:text-blue-200 flex-1">
                      {file}
                    </code>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenFile(file)}
                      className="ml-2"
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      Open
                    </Button>
                  </div>
                ))}
                {allDecisionFiles.length > 10 && (
                  <p className="text-sm text-blue-600 dark:text-blue-400 text-center">
                    +{allDecisionFiles.length - 10} more files from decisions...
                  </p>
                )}
              </div>
            </div>
          )}
          
          {/* Show message if no files found */}
          {relatedFiles.length === 0 && (
            <div className="text-center py-6 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No files found in architectural decisions</p>
              <p className="text-sm mt-1">The coding agent will need to identify relevant files based on the symptoms and failure mechanism</p>
            </div>
          )}
        </div>

        {/* Verification Checklist Preview */}
        <div className="space-y-3">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Key Verification Tasks
          </h3>
          <div className="space-y-2">
            <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <strong>Code Review:</strong> Examine the {relatedFiles.length} related files for the suspected architectural decision
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <strong>Root Cause Confirmation:</strong> Verify the failure mechanism by reproducing the issue
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <strong>Fix Implementation:</strong> Develop immediate, short-term, and long-term solutions
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button
            onClick={handleCopy}
            className="flex-1"
            variant={copied ? "default" : "outline"}
          >
            <Copy className="h-4 w-4 mr-2" />
            {copied ? 'Copied!' : 'Copy Full Prompt'}
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              const prompt = generatePrompt();
              const blob = new Blob([prompt], { type: 'text/markdown' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `rca-verification-prompt-${Date.now()}.md`;
              a.click();
              URL.revokeObjectURL(url);
            }}
          >
            <FileText className="h-4 w-4 mr-2" />
            Export as MD
          </Button>
        </div>

        {/* Prompt Preview */}
        <details className="space-y-3">
          <summary className="cursor-pointer font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100">
            Preview Full Prompt
          </summary>
          <div className="mt-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
            <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap overflow-x-auto max-h-96">
              {generatePrompt()}
            </pre>
          </div>
        </details>
      </CardContent>
    </Card>
  );
} 