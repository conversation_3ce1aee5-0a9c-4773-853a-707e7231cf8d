'use client'

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardD<PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le } from '../ui/card';
import { Badge } from '../ui/badge';
import { But<PERSON> } from '../ui/button';
import { ChevronDown, ChevronUp, Search, Brain, Target, <PERSON>ertTriangle, Lightbulb } from 'lucide-react';

interface InvestigationHypothesis {
  hypothesis: string;
  reasoning: string;
  likelihood: 'HIGH' | 'MEDIUM' | 'LOW';
  search_queries: string[];
}

interface SymptomAnalysis {
  user_action: string;
  observed_behavior: string;
  expected_behavior: string;
  affected_functionality: string;
  timeline_clues: string;
}

interface IssueClassification {
  primary_type: 'REGRESSION' | 'MISSING_FEATURE' | 'DESIGN_FLAW' | 'INTEGRATION_ISSUE' | 'ENVIRONMENTAL_ISSUE';
  confidence: number;
  classification_reasoning: string;
}

interface CriticalInformationGap {
  missing_info: string[];
  impact_on_analysis: string;
  recommended_questions: string[];
}

interface CriticalInformationGaps {
  severity_assessment: CriticalInformationGap;
  technical_context: CriticalInformationGap;
  timeline_causation: CriticalInformationGap;
  validation_concerns: CriticalInformationGap;
  overall_confidence: number;
  blocking_gaps: string[];
  investigation_readiness: 'READY' | 'NEEDS_MORE_INFO' | 'REQUIRES_VALIDATION';
}

interface InvestigationPlan {
  symptom_analysis?: SymptomAnalysis;
  issue_classification?: IssueClassification;
  critical_information_gaps?: CriticalInformationGaps;
  investigation_strategy: string;
  investigation_hypotheses: InvestigationHypothesis[];
  next_steps?: string;
}

interface InvestigationPlanViewProps {
  investigationPlan: InvestigationPlan;
  onDiscoverDecisions: () => void;
  isLoading: boolean;
}

const getLikelihoodColor = (likelihood: string) => {
  switch (likelihood) {
    case 'HIGH': return 'bg-red-100 text-red-800 border-red-200';
    case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'LOW': return 'bg-gray-100 text-gray-800 border-gray-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getLikelihoodIcon = (likelihood: string) => {
  switch (likelihood) {
    case 'HIGH': return '🔴';
    case 'MEDIUM': return '🟡';
    case 'LOW': return '⚪';
    default: return '⚪';
  }
};

const getIssueTypeColor = (type: string) => {
  switch (type) {
    case 'REGRESSION': return 'bg-red-100 text-red-800 border-red-200';
    case 'MISSING_FEATURE': return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'DESIGN_FLAW': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'INTEGRATION_ISSUE': return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'ENVIRONMENTAL_ISSUE': return 'bg-green-100 text-green-800 border-green-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getIssueTypeIcon = (type: string) => {
  switch (type) {
    case 'REGRESSION': return '🔄';
    case 'MISSING_FEATURE': return '🔍';
    case 'DESIGN_FLAW': return '🏗️';
    case 'INTEGRATION_ISSUE': return '🔗';
    case 'ENVIRONMENTAL_ISSUE': return '🌍';
    default: return '❓';
  }
};

const getIssueTypeDescription = (type: string) => {
  switch (type) {
    case 'REGRESSION': return 'Something that worked before but is now broken';
    case 'MISSING_FEATURE': return 'Expected functionality that was never implemented';
    case 'DESIGN_FLAW': return 'Functionality implemented incorrectly from the start';
    case 'INTEGRATION_ISSUE': return 'Components work individually but fail when combined';
    case 'ENVIRONMENTAL_ISSUE': return 'Works in some environments but not others';
    default: return 'Unknown issue type';
  }
};

const getReadinessColor = (readiness: string) => {
  switch (readiness) {
    case 'READY': return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
    case 'NEEDS_MORE_INFO': return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
    case 'REQUIRES_VALIDATION': return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
    default: return 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700';
  }
};

const getReadinessIcon = (readiness: string) => {
  switch (readiness) {
    case 'READY': return '✅';
    case 'NEEDS_MORE_INFO': return '⚠️';
    case 'REQUIRES_VALIDATION': return '❓';
    default: return '❓';
  }
};

const getReadinessDescription = (readiness: string) => {
  switch (readiness) {
    case 'READY': return 'Sufficient information available to proceed with investigation';
    case 'NEEDS_MORE_INFO': return 'Additional information needed before meaningful analysis';
    case 'REQUIRES_VALIDATION': return 'Need to validate if this is actually an issue worth investigating';
    default: return 'Investigation readiness unknown';
  }
};

export default function InvestigationPlanView({ 
  investigationPlan, 
  onDiscoverDecisions, 
  isLoading 
}: InvestigationPlanViewProps) {
  const [expandedHypotheses, setExpandedHypotheses] = useState<Set<number>>(new Set());

  const toggleHypothesis = (index: number) => {
    const newExpanded = new Set(expandedHypotheses);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedHypotheses(newExpanded);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-blue-600" />
          <CardTitle>Investigation Plan</CardTitle>
        </div>
        <CardDescription>
          AI-generated investigation strategy and hypotheses for your incident
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Symptom Analysis */}
        {investigationPlan.symptom_analysis && (
          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-gray-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">Symptom Analysis</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-700 dark:text-gray-300 mb-1">User Action:</p>
                    <p className="text-gray-600 dark:text-gray-400">{investigationPlan.symptom_analysis.user_action}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700 dark:text-gray-300 mb-1">Affected Functionality:</p>
                    <p className="text-gray-600 dark:text-gray-400">{investigationPlan.symptom_analysis.affected_functionality}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700 dark:text-gray-300 mb-1">Observed Behavior:</p>
                    <p className="text-gray-600 dark:text-gray-400">{investigationPlan.symptom_analysis.observed_behavior}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700 dark:text-gray-300 mb-1">Expected Behavior:</p>
                    <p className="text-gray-600 dark:text-gray-400">{investigationPlan.symptom_analysis.expected_behavior}</p>
                  </div>
                  {investigationPlan.symptom_analysis.timeline_clues && (
                    <div className="md:col-span-2">
                      <p className="font-medium text-gray-700 dark:text-gray-300 mb-1">Timeline Clues:</p>
                      <p className="text-gray-600 dark:text-gray-400">{investigationPlan.symptom_analysis.timeline_clues}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Issue Classification */}
        {investigationPlan.issue_classification && (
          <div className={`border rounded-lg p-4 ${getIssueTypeColor(investigationPlan.issue_classification.primary_type)}`}>
            <div className="flex items-start gap-3">
              <span className="text-2xl">{getIssueTypeIcon(investigationPlan.issue_classification.primary_type)}</span>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-semibold">Issue Type: {investigationPlan.issue_classification.primary_type.replace('_', ' ')}</h3>
                  <Badge variant="outline" className="text-xs">
                    {Math.round(investigationPlan.issue_classification.confidence * 100)}% confident
                  </Badge>
                </div>
                <p className="text-sm mb-2 opacity-90">
                  {getIssueTypeDescription(investigationPlan.issue_classification.primary_type)}
                </p>
                <p className="text-sm font-medium">Reasoning:</p>
                <p className="text-sm opacity-90">
                  {investigationPlan.issue_classification.classification_reasoning}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Critical Information Gaps */}
        {investigationPlan.critical_information_gaps && (
          <div className={`border rounded-lg p-4 ${getReadinessColor(investigationPlan.critical_information_gaps.investigation_readiness)}`}>
            <div className="flex items-start gap-3 mb-4">
              <span className="text-2xl">{getReadinessIcon(investigationPlan.critical_information_gaps.investigation_readiness)}</span>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-semibold">Investigation Readiness: {investigationPlan.critical_information_gaps.investigation_readiness.replace('_', ' ')}</h3>
                  <Badge variant="outline" className="text-xs">
                    {Math.round(investigationPlan.critical_information_gaps.overall_confidence * 100)}% confidence
                  </Badge>
                </div>
                <p className="text-sm mb-3 opacity-90">
                  {getReadinessDescription(investigationPlan.critical_information_gaps.investigation_readiness)}
                </p>
                
                {/* Blocking Gaps */}
                {investigationPlan.critical_information_gaps.blocking_gaps.length > 0 && (
                  <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                    <h4 className="font-medium text-red-900 dark:text-red-100 mb-2">🚫 Critical Blocking Issues:</h4>
                    <ul className="text-sm text-red-800 dark:text-red-200 space-y-1">
                      {investigationPlan.critical_information_gaps.blocking_gaps.map((gap, idx) => (
                        <li key={idx}>• {gap}</li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {/* Information Gap Categories */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Severity Assessment */}
                  {investigationPlan.critical_information_gaps.severity_assessment.missing_info.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm flex items-center gap-1">
                        📊 Severity Assessment Gaps
                      </h4>
                      <div className="text-xs space-y-1">
                        <p className="font-medium">Missing:</p>
                        <ul className="ml-2 space-y-0.5">
                          {investigationPlan.critical_information_gaps.severity_assessment.missing_info.slice(0, 3).map((info, idx) => (
                            <li key={idx}>• {info}</li>
                          ))}
                        </ul>
                        {investigationPlan.critical_information_gaps.severity_assessment.recommended_questions.length > 0 && (
                          <>
                            <p className="font-medium mt-2">Key Questions:</p>
                            <ul className="ml-2 space-y-0.5">
                              {investigationPlan.critical_information_gaps.severity_assessment.recommended_questions.slice(0, 2).map((q, idx) => (
                                <li key={idx}>• {q}</li>
                              ))}
                            </ul>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Technical Context */}
                  {investigationPlan.critical_information_gaps.technical_context.missing_info.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm flex items-center gap-1">
                        ⚙️ Technical Context Gaps
                      </h4>
                      <div className="text-xs space-y-1">
                        <p className="font-medium">Missing:</p>
                        <ul className="ml-2 space-y-0.5">
                          {investigationPlan.critical_information_gaps.technical_context.missing_info.slice(0, 3).map((info, idx) => (
                            <li key={idx}>• {info}</li>
                          ))}
                        </ul>
                        {investigationPlan.critical_information_gaps.technical_context.recommended_questions.length > 0 && (
                          <>
                            <p className="font-medium mt-2">Key Questions:</p>
                            <ul className="ml-2 space-y-0.5">
                              {investigationPlan.critical_information_gaps.technical_context.recommended_questions.slice(0, 2).map((q, idx) => (
                                <li key={idx}>• {q}</li>
                              ))}
                            </ul>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Timeline Causation */}
                  {investigationPlan.critical_information_gaps.timeline_causation.missing_info.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm flex items-center gap-1">
                        ⏰ Timeline & Causation Gaps
                      </h4>
                      <div className="text-xs space-y-1">
                        <p className="font-medium">Missing:</p>
                        <ul className="ml-2 space-y-0.5">
                          {investigationPlan.critical_information_gaps.timeline_causation.missing_info.slice(0, 3).map((info, idx) => (
                            <li key={idx}>• {info}</li>
                          ))}
                        </ul>
                        {investigationPlan.critical_information_gaps.timeline_causation.recommended_questions.length > 0 && (
                          <>
                            <p className="font-medium mt-2">Key Questions:</p>
                            <ul className="ml-2 space-y-0.5">
                              {investigationPlan.critical_information_gaps.timeline_causation.recommended_questions.slice(0, 2).map((q, idx) => (
                                <li key={idx}>• {q}</li>
                              ))}
                            </ul>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Validation Concerns */}
                  {investigationPlan.critical_information_gaps.validation_concerns.missing_info.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm flex items-center gap-1">
                        ❓ Validation Concerns
                      </h4>
                      <div className="text-xs space-y-1">
                        <p className="font-medium">Missing:</p>
                        <ul className="ml-2 space-y-0.5">
                          {investigationPlan.critical_information_gaps.validation_concerns.missing_info.slice(0, 3).map((info, idx) => (
                            <li key={idx}>• {info}</li>
                          ))}
                        </ul>
                        {investigationPlan.critical_information_gaps.validation_concerns.recommended_questions.length > 0 && (
                          <>
                            <p className="font-medium mt-2">Key Questions:</p>
                            <ul className="ml-2 space-y-0.5">
                              {investigationPlan.critical_information_gaps.validation_concerns.recommended_questions.slice(0, 2).map((q, idx) => (
                                <li key={idx}>• {q}</li>
                              ))}
                            </ul>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Next Steps */}
        {investigationPlan.next_steps && (
          <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Lightbulb className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-1">Recommended Next Steps</h3>
                <p className="text-sm text-purple-800 dark:text-purple-200">{investigationPlan.next_steps}</p>
              </div>
            </div>
          </div>
        )}

        {/* Strategy Overview */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Target className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">Investigation Strategy</h3>
              <p className="text-sm text-blue-800 dark:text-blue-200">{investigationPlan.investigation_strategy}</p>
            </div>
          </div>
        </div>

        {/* Hypotheses */}
        <div className="space-y-3">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <span>Investigation Hypotheses</span>
            <Badge variant="outline">{investigationPlan.investigation_hypotheses.length}</Badge>
          </h3>
          
          {investigationPlan.investigation_hypotheses.map((hypothesis, index) => (
            <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              {/* Hypothesis Header */}
              <div 
                className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                onClick={() => toggleHypothesis(index)}
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-lg">{getLikelihoodIcon(hypothesis.likelihood)}</span>
                      <Badge className={getLikelihoodColor(hypothesis.likelihood)}>
                        {hypothesis.likelihood} LIKELIHOOD
                      </Badge>
                    </div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 leading-relaxed">
                      {hypothesis.hypothesis}
                    </h4>
                  </div>
                  <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                    {expandedHypotheses.has(index) ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedHypotheses.has(index) && (
                <div className="px-4 pb-4 border-t border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50">
                  <div className="space-y-3 pt-3">
                    {/* Reasoning */}
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Reasoning</h5>
                      <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                        {hypothesis.reasoning}
                      </p>
                    </div>

                    {/* Search Queries */}
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
                        <Search className="h-3 w-3" />
                        Search Queries
                      </h5>
                      <div className="flex flex-wrap gap-2">
                        {hypothesis.search_queries.map((query, queryIndex) => (
                          <Badge key={queryIndex} variant="outline" className="text-xs">
                            {query}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Action Button */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button 
            onClick={onDiscoverDecisions}
            disabled={isLoading}
            className="w-full h-12 text-base font-medium"
            size="lg"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Searching Architecture Decisions...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                Discover Suspicious Decisions
              </div>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 