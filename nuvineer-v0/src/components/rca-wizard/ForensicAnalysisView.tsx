'use client'

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardDescription, Card<PERSON><PERSON><PERSON>, CardT<PERSON>le } from '../ui/card';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>Triangle, CheckCircle, XCircle, ChevronDown, ChevronUp, FileText, Clock, Target, Lightbulb } from 'lucide-react';
import CodingAgentPrompt from './CodingAgentPrompt';

interface ForensicAnalysis {
  primary_root_cause?: {
    decision_id: string;
    causation_confidence: number;
    failure_mechanism: string;
    evidence: string[];
    timeline_fit: string;
  };
  contributing_factors?: Array<{
    decision_id: string;
    contribution_type: string;
    explanation: string;
  }>;
  failure_chain?: string;
  alternative_theories?: string[];
}

interface SuspiciousDecision {
  id: string;
  metadata?: {
    title?: string;
    description?: string;
    confidence_score?: number;
    follows_standard_practice?: boolean;
    pr_merged_at?: string;
    domain_concepts?: string[];
    related_files?: string[];
    implications?: string;
    rationale?: string;
  };
  hypothesis_match: string;
  hypothesis_likelihood: 'HIGH' | 'MEDIUM' | 'LOW';
  search_query: string;
  score?: number;
}

interface Symptoms {
  errorType: string;
  affectedComponents: string[];
  userImpact: string;
  timePattern: string;
  environment: string;
}

interface ForensicAnalysisViewProps {
  forensicAnalysis: ForensicAnalysis;
  suspiciousDecisions?: SuspiciousDecision[];
  symptoms?: Symptoms;
  repositorySlug?: string;
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) return 'text-red-600 bg-red-50 border-red-200';
  if (confidence >= 0.6) return 'text-orange-600 bg-orange-50 border-orange-200';
  if (confidence >= 0.4) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
  return 'text-gray-600 bg-gray-50 border-gray-200';
};

const getConfidenceIcon = (confidence: number) => {
  if (confidence >= 0.8) return <XCircle className="h-5 w-5 text-red-600" />;
  if (confidence >= 0.6) return <AlertTriangle className="h-5 w-5 text-orange-600" />;
  if (confidence >= 0.4) return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
  return <CheckCircle className="h-5 w-5 text-gray-600" />;
};

const getContributionTypeColor = (type: string) => {
  switch (type.toLowerCase()) {
    case 'amplifier': return 'bg-red-100 text-red-800 border-red-200';
    case 'enabler': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'catalyst': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export default function ForensicAnalysisView({ 
  forensicAnalysis, 
  suspiciousDecisions, 
  symptoms, 
  repositorySlug 
}: ForensicAnalysisViewProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['root-cause']));

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const confidence = forensicAnalysis.primary_root_cause?.causation_confidence || 0;
  const confidencePercent = Math.round(confidence * 100);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Target className="h-5 w-5 text-red-600" />
          <CardTitle>Forensic Analysis Results</CardTitle>
        </div>
        <CardDescription>
          Root cause analysis completed with {confidencePercent}% confidence
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Primary Root Cause */}
        {forensicAnalysis.primary_root_cause && (
          <div className="space-y-4">
            {/* Confidence Overview */}
            <div className={`rounded-lg border p-4 ${getConfidenceColor(confidence)}`}>
              <div className="flex items-center gap-3 mb-3">
                {getConfidenceIcon(confidence)}
                <div>
                  <h3 className="font-bold text-lg">Primary Root Cause Identified</h3>
                  <p className="text-sm opacity-90">
                    Analysis confidence: {confidencePercent}%
                  </p>
                </div>
              </div>
              
              {/* Decision ID */}
              <div className="bg-white/70 dark:bg-gray-800/70 rounded-md p-3 mb-3">
                <div className="flex items-center gap-2 mb-1">
                  <FileText className="h-4 w-4" />
                  <span className="font-medium text-sm">Decision ID</span>
                </div>
                <code className="text-sm font-mono">{forensicAnalysis.primary_root_cause.decision_id}</code>
              </div>

              {/* Failure Mechanism */}
              <div className="space-y-2">
                <h4 className="font-semibold flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Failure Mechanism
                </h4>
                <p className="text-sm leading-relaxed">
                  {forensicAnalysis.primary_root_cause.failure_mechanism}
                </p>
              </div>
            </div>

            {/* Expandable Details */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
              <button
                onClick={() => toggleSection('root-cause')}
                className="w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Detailed Evidence & Timeline</h4>
                  {expandedSections.has('root-cause') ? (
                    <ChevronUp className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </button>

              {expandedSections.has('root-cause') && (
                <div className="px-4 pb-4 border-t border-gray-100 dark:border-gray-800 space-y-4">
                  {/* Timeline Fit */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Clock className="h-4 w-4 text-blue-600" />
                      <h5 className="font-medium text-sm">Timeline Analysis</h5>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 pl-6">
                      {forensicAnalysis.primary_root_cause.timeline_fit}
                    </p>
                  </div>

                  {/* Evidence */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <h5 className="font-medium text-sm">Supporting Evidence</h5>
                    </div>
                    <ul className="space-y-2 pl-6">
                      {forensicAnalysis.primary_root_cause.evidence.map((evidence, index) => (
                        <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-green-600 rounded-full mt-2 flex-shrink-0"></span>
                          {evidence}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Contributing Factors */}
        {forensicAnalysis.contributing_factors && forensicAnalysis.contributing_factors.length > 0 && (
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
            <button
              onClick={() => toggleSection('contributing')}
              className="w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <h4 className="font-medium">Contributing Factors</h4>
                  <Badge variant="outline">{forensicAnalysis.contributing_factors.length}</Badge>
                </div>
                {expandedSections.has('contributing') ? (
                  <ChevronUp className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                )}
              </div>
            </button>

            {expandedSections.has('contributing') && (
              <div className="px-4 pb-4 border-t border-gray-100 dark:border-gray-800">
                <div className="space-y-3 pt-3">
                  {forensicAnalysis.contributing_factors.map((factor, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className={getContributionTypeColor(factor.contribution_type)}>
                          {factor.contribution_type.toUpperCase()}
                        </Badge>
                        <code className="text-xs bg-white dark:bg-gray-700 px-2 py-1 rounded">
                          {factor.decision_id}
                        </code>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {factor.explanation}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Failure Chain */}
        {forensicAnalysis.failure_chain && (
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
            <button
              onClick={() => toggleSection('failure-chain')}
              className="w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <h4 className="font-medium">Failure Chain Analysis</h4>
                </div>
                {expandedSections.has('failure-chain') ? (
                  <ChevronUp className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                )}
              </div>
            </button>

            {expandedSections.has('failure-chain') && (
              <div className="px-4 pb-4 border-t border-gray-100 dark:border-gray-800">
                <div className="pt-3">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed">
                      {forensicAnalysis.failure_chain}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Alternative Theories */}
        {forensicAnalysis.alternative_theories && forensicAnalysis.alternative_theories.length > 0 && (
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
            <button
              onClick={() => toggleSection('alternatives')}
              className="w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Lightbulb className="h-4 w-4 text-purple-600" />
                  <h4 className="font-medium">Alternative Theories</h4>
                  <Badge variant="outline">{forensicAnalysis.alternative_theories.length}</Badge>
                </div>
                {expandedSections.has('alternatives') ? (
                  <ChevronUp className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                )}
              </div>
            </button>

            {expandedSections.has('alternatives') && (
              <div className="px-4 pb-4 border-t border-gray-100 dark:border-gray-800">
                <div className="space-y-2 pt-3">
                  {forensicAnalysis.alternative_theories.map((theory, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <span className="w-6 h-6 bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-300 rounded-full flex items-center justify-center text-xs font-medium">
                        {index + 1}
                      </span>
                      <p className="text-sm text-purple-800 dark:text-purple-200 leading-relaxed">
                        {theory}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
                 )}
       </CardContent>
     </Card>
   );
 } 