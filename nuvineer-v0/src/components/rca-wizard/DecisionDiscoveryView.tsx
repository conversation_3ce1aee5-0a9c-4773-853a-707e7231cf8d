'use client'

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { ChevronDown, ChevronUp, Search, FileText, Clock, AlertTriangle, CheckCircle, XCircle, ExternalLink } from 'lucide-react';

interface SuspiciousDecision {
  id: string;
  metadata?: {
    title?: string;
    description?: string;
    confidence_score?: number;
    follows_standard_practice?: boolean;
    pr_merged_at?: string;
    domain_concepts?: string[];
    related_files?: string[];
    implications?: string;
    rationale?: string;
  };
  hypothesis_match: string;
  hypothesis_likelihood: 'HIGH' | 'MEDIUM' | 'LOW';
  search_query: string;
  score?: number;
}

interface DecisionDiscoveryViewProps {
  suspiciousDecisions: SuspiciousDecision[];
  onPerformForensicAnalysis: () => void;
  isLoading: boolean;
}

const getLikelihoodColor = (likelihood: string) => {
  switch (likelihood) {
    case 'HIGH': return 'bg-red-100 text-red-800 border-red-200';
    case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'LOW': return 'bg-gray-100 text-gray-800 border-gray-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getConfidenceIcon = (score?: number) => {
  if (!score) return <AlertTriangle className="h-4 w-4 text-gray-400" />;
  if (score >= 0.8) return <CheckCircle className="h-4 w-4 text-green-600" />;
  if (score >= 0.6) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
  return <XCircle className="h-4 w-4 text-red-600" />;
};

const getStandardPracticeColor = (follows?: boolean) => {
  if (follows === undefined) return 'text-gray-600';
  return follows ? 'text-green-600' : 'text-red-600';
};

export default function DecisionDiscoveryView({ 
  suspiciousDecisions, 
  onPerformForensicAnalysis, 
  isLoading 
}: DecisionDiscoveryViewProps) {
  const [expandedDecisions, setExpandedDecisions] = useState<Set<string>>(new Set());

  const toggleDecision = (decisionId: string) => {
    const newExpanded = new Set(expandedDecisions);
    if (newExpanded.has(decisionId)) {
      newExpanded.delete(decisionId);
    } else {
      newExpanded.add(decisionId);
    }
    setExpandedDecisions(newExpanded);
  };

  // Group decisions by likelihood for better organization
  const groupedDecisions = {
    HIGH: suspiciousDecisions.filter(d => d.hypothesis_likelihood === 'HIGH'),
    MEDIUM: suspiciousDecisions.filter(d => d.hypothesis_likelihood === 'MEDIUM'),
    LOW: suspiciousDecisions.filter(d => d.hypothesis_likelihood === 'LOW')
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Search className="h-5 w-5 text-blue-600" />
          <CardTitle>Suspicious Decisions Discovered</CardTitle>
        </div>
        <CardDescription>
          Found {suspiciousDecisions.length} potentially related architectural decisions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {suspiciousDecisions.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No suspicious decisions found in the current search.</p>
            <p className="text-sm mt-2">Try adjusting your symptom analysis or investigation hypotheses.</p>
          </div>
        ) : (
          <>
            {/* Summary Stats */}
            <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{groupedDecisions.HIGH.length}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">High Risk</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{groupedDecisions.MEDIUM.length}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Medium Risk</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">{groupedDecisions.LOW.length}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Low Risk</div>
              </div>
            </div>

            {/* Decisions by Risk Level */}
            {(['HIGH', 'MEDIUM', 'LOW'] as const).map(level => {
              const decisions = groupedDecisions[level];
              if (decisions.length === 0) return null;

              return (
                <div key={level} className="space-y-3">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                    <Badge className={getLikelihoodColor(level)}>
                      {level} RISK
                    </Badge>
                    <span className="text-sm text-gray-500">({decisions.length})</span>
                  </h3>

                  {decisions.slice(0, 10).map((decision) => (
                    <div key={decision.id} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                      {/* Decision Header */}
                      <div 
                        className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                        onClick={() => toggleDecision(decision.id)}
                      >
                        <div className="flex items-start justify-between gap-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              {getConfidenceIcon(decision.metadata?.confidence_score)}
                              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                {decision.metadata?.title || 'Untitled Decision'}
                              </h4>
                              {decision.score && (
                                <Badge variant="outline" className="text-xs">
                                  {Math.round(decision.score * 100)}% match
                                </Badge>
                              )}
                            </div>
                            
                            {decision.metadata?.description && (
                              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                                {decision.metadata.description}
                              </p>
                            )}

                            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                              <span>Matched: {decision.hypothesis_match.substring(0, 50)}...</span>
                              {decision.metadata?.follows_standard_practice !== undefined && (
                                <span className={getStandardPracticeColor(decision.metadata.follows_standard_practice)}>
                                  {decision.metadata.follows_standard_practice ? '✓ Standard Practice' : '⚠ Non-Standard'}
                                </span>
                              )}
                            </div>
                          </div>
                          <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                            {expandedDecisions.has(decision.id) ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>

                      {/* Expanded Content */}
                      {expandedDecisions.has(decision.id) && (
                        <div className="px-4 pb-4 border-t border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50">
                          <div className="space-y-4 pt-4">
                            {/* Decision ID */}
                            <div>
                              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-1">
                                <FileText className="h-3 w-3" />
                                Decision ID
                              </h5>
                              <code className="text-xs bg-white dark:bg-gray-700 px-2 py-1 rounded font-mono">
                                {decision.id}
                              </code>
                            </div>

                            {/* Match Details */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Hypothesis Match</h5>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                  {decision.hypothesis_match}
                                </p>
                              </div>
                              <div>
                                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search Query</h5>
                                <Badge variant="outline" className="text-xs">
                                  {decision.search_query}
                                </Badge>
                              </div>
                            </div>

                            {/* Metadata */}
                            {decision.metadata && (
                              <div className="space-y-3">
                                {decision.metadata.pr_merged_at && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      Merged Date
                                    </h5>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                      {new Date(decision.metadata.pr_merged_at).toLocaleDateString()}
                                    </p>
                                  </div>
                                )}

                                {decision.metadata.domain_concepts && decision.metadata.domain_concepts.length > 0 && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Domain Concepts</h5>
                                    <div className="flex flex-wrap gap-1">
                                      {decision.metadata.domain_concepts.map((concept, index) => (
                                        <Badge key={index} variant="outline" className="text-xs">
                                          {concept}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {decision.metadata.related_files && decision.metadata.related_files.length > 0 && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Related Files</h5>
                                    <div className="space-y-1">
                                      {decision.metadata.related_files.slice(0, 5).map((file, index) => (
                                        <div key={index} className="flex items-center justify-between bg-white dark:bg-gray-700 px-2 py-1 rounded">
                                          <code className="text-xs font-mono text-gray-700 dark:text-gray-300 flex-1">
                                            {file}
                                          </code>
                                          <button
                                            onClick={() => {
                                              // Construct GitHub URL - this could be made configurable
                                              const githubUrl = `https://github.com/${window.location.pathname.includes('repositorySlug=') ? 
                                                new URLSearchParams(window.location.search).get('repositorySlug') || '' : 
                                                'owner/repo'}/blob/main/${file}`;
                                              window.open(githubUrl, '_blank');
                                            }}
                                            className="ml-2 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
                                            title="Open file"
                                          >
                                            <ExternalLink className="h-3 w-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300" />
                                          </button>
                                        </div>
                                      ))}
                                      {decision.metadata.related_files.length > 5 && (
                                        <p className="text-xs text-gray-500">
                                          +{decision.metadata.related_files.length - 5} more files...
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                )}

                                {decision.metadata.rationale && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Rationale</h5>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                                      {decision.metadata.rationale}
                                    </p>
                                  </div>
                                )}

                                {decision.metadata.implications && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Implications</h5>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                                      {decision.metadata.implications}
                                    </p>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              );
            })}

            {/* Action Button */}
            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button 
                onClick={onPerformForensicAnalysis}
                disabled={isLoading}
                className="w-full h-12 text-base font-medium"
                size="lg"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Performing Forensic Analysis...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Perform Forensic Analysis
                  </div>
                )}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
} 