import React, { useState, useMemo } from 'react';
import type { WizardState, DecisionPoint, DecisionOption } from '../../types/design-doc-wizard';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import { DecisionLinkedText, DecisionReference } from '../../utils/decisionUtils';
import DecisionDetailModal from '../DecisionDetailModal';
import { useDecisions, useProjectContextWithDebug } from '../../hooks/useDecisions';

interface DecisionMakingStepProps {
  wizardState: WizardState;
  actions: {
    selectDecisionOption: (decisionPointId: string, optionId: string, rationale: string) => void;
  };
  editing: {
    updateDecision: (decisionId: string, updates: Partial<DecisionPoint>) => void;
  };
  repositorySlug: string;
  installationId: string | number;
  onBack: () => void;
  onContinue: () => void;
  isLoading: boolean;
}

export default function DecisionMakingStep({
  wizardState,
  actions,
  editing,
  repositorySlug,
  installationId,
  onBack,
  onContinue,
  isLoading
}: DecisionMakingStepProps) {
  const { decisionPoints } = wizardState;
  const [expandedDecisions, setExpandedDecisions] = useState<Set<string>>(new Set(decisionPoints.map(dp => dp.id)));
  const [overrideReasons, setOverrideReasons] = useState<Record<string, string>>({});
  const [pendingSelection, setPendingSelection] = useState<Record<string, string>>({});
  const [expandedDetails, setExpandedDetails] = useState<Set<string>>(new Set());
  const [selectedDecisionForModal, setSelectedDecisionForModal] = useState<string | null>(null);

  // Use project context with debugging
  const { repositorySlug: finalRepoSlug, installationId: finalInstallationId } = useProjectContextWithDebug(repositorySlug, installationId);
  
  // Collect all text that might contain decision references
  const textsToSearch = useMemo(() => {
    const allTexts: (string | string[])[] = [];
    
    decisionPoints.forEach(decision => {
      if (decision.recommendation_rationale) {
        allTexts.push(decision.recommendation_rationale);
      }
      
      decision.options?.forEach(option => {
        if (option.alignmentJustification) {
          allTexts.push(option.alignmentJustification);
        }
      });
    });
    
    return allTexts;
  }, [decisionPoints]);

  // Fetch decisions based on references found in the text
  const { decisions, isLoading: isLoadingDecisions, error: decisionsError } = useDecisions({
    repositorySlug: finalRepoSlug,
    installationId: finalInstallationId,
    texts: textsToSearch
  });

  const handleDecisionClick = (decisionId: string) => {
    setSelectedDecisionForModal(decisionId);
  };

  const completedDecisions = decisionPoints.filter(dp => dp.selectedOption).length;
  
  const toggleDecisionExpanded = (decisionId: string) => {
    const newExpanded = new Set(expandedDecisions);
    if (newExpanded.has(decisionId)) {
      newExpanded.delete(decisionId);
    } else {
      newExpanded.add(decisionId);
    }
    setExpandedDecisions(newExpanded);
  };

  const toggleDetails = (optionId: string) => {
    setExpandedDetails(prev => {
      const newSet = new Set(prev);
      if (newSet.has(optionId)) {
        newSet.delete(optionId);
      } else {
        newSet.add(optionId);
      }
      return newSet;
    });
  };

  const getRiskColor = (risk?: string) => {
    switch (risk?.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getComplexityColor = (complexity?: string) => {
    switch (complexity?.toLowerCase()) {
      case 'high': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300';
      case 'medium': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300';
      case 'low': return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/50 dark:text-cyan-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };



  const allDecisionsMade = decisionPoints.every(dp => dp.selectedOption);

  const handleSelectOption = (decisionId: string, optionId: string) => {
    const decision = decisionPoints.find(dp => dp.id === decisionId);
    if (!decision) return;

    const recommendedOption = getRecommendedOption(decision);
    const isOverride = recommendedOption && recommendedOption.id !== optionId;

    if (isOverride) {
      setPendingSelection(prev => ({ ...prev, [decisionId]: optionId }));
      setOverrideReasons(prev => ({ ...prev, [decisionId]: '' }));
      return;
    }

    actions.selectDecisionOption(decisionId, optionId, '');
    if (pendingSelection[decisionId]) {
      setPendingSelection(prev => {
        const newPending = { ...prev };
        delete newPending[decisionId];
        return newPending;
      });
    }
    if (overrideReasons[decisionId] !== undefined) {
      setOverrideReasons(prev => {
        const newReasons = { ...prev };
        delete newReasons[decisionId];
        return newReasons;
      });
    }
  };

  const getRecommendedOption = (decision: DecisionPoint) => {
    if (!decision.options || !decision.options.length) {
      return null;
    }
    if (decision.recommended_option_id) {
      const recommended = decision.options.find(opt => opt.id === decision.recommended_option_id);
      if (recommended) return recommended;
    }
    const scoredOptions = decision.options.filter(o => typeof o.alignmentScore === 'number');
    if (scoredOptions.length > 0) {
      return scoredOptions.reduce((prev, current) => 
        (prev.alignmentScore! > current.alignmentScore!) ? prev : current
      );
    }
    return null;
  };

  const handleOverrideRationale = (decisionId: string, reason: string) => {
    setOverrideReasons(prev => ({ ...prev, [decisionId]: reason }));
  };

  const confirmOverride = (decisionId: string) => {
    const optionId = pendingSelection[decisionId];
    const reason = overrideReasons[decisionId];
    if (!optionId || !reason || !reason.trim()) return;

    actions.selectDecisionOption(decisionId, optionId, `Override: ${reason}`);
    setPendingSelection(prev => {
      const newPending = { ...prev };
      delete newPending[decisionId];
      return newPending;
    });
    setOverrideReasons(prev => {
      const newReasons = { ...prev };
      delete newReasons[decisionId];
      return newReasons;
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Make Technical Decisions
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Review AI recommendations and make your choices. You can accept recommendations or override with justification.
        </p>
      </div>

      <div className="mb-6 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Decision Progress</span>
          <span className="text-sm text-gray-600 dark:text-gray-400">{completedDecisions} of {decisionPoints.length} completed</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <div 
            className="bg-amber-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${decisionPoints.length > 0 ? (completedDecisions / decisionPoints.length) * 100 : 0}%` }}
          ></div>
        </div>
      </div>

      <div className="space-y-4 mb-6">
        {decisionPoints.map((decision, index) => {
          const isExpanded = expandedDecisions.has(decision.id);
          const hasSelection = !!decision.selectedOption;
          const selectedOption = decision.options?.find(opt => opt.id === decision.selectedOption);
          const recommendedOption = getRecommendedOption(decision);
          const isOverride = hasSelection && recommendedOption && selectedOption?.id !== recommendedOption.id;
          const needsOverrideReason = pendingSelection[decision.id] && overrideReasons[decision.id] !== undefined;

          return (
            <div key={decision.id} className={`border rounded-lg transition-all ${hasSelection ? (isOverride ? 'border-orange-300 bg-orange-50/50 dark:border-orange-600 dark:bg-orange-900/10' : 'border-green-300 bg-green-50/50 dark:border-green-600 dark:bg-green-900/10') : 'border-gray-200 dark:border-gray-600'}`}>
              <div className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50" onClick={() => toggleDecisionExpanded(decision.id)}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${hasSelection ? (isOverride ? 'bg-orange-500 border-orange-500 text-white' : 'bg-green-500 border-green-500 text-white') : 'border-gray-300 text-gray-400'}`}>
                      {hasSelection ? (isOverride ? '⚠' : '✓') : index + 1}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">{decision.title}</h3>
                      {hasSelection && selectedOption ? (
                        <div className="flex items-center space-x-2 mt-1">
                          <p className={`text-sm font-medium ${isOverride ? 'text-orange-600 dark:text-orange-400' : 'text-green-600 dark:text-green-400'}`}>
                            Selected: {selectedOption.name}
                          </p>
                          {isOverride && <span className="px-2 py-0.5 text-xs font-semibold bg-orange-100 text-orange-800 rounded-full">Override</span>}
                        </div>
                      ) : !hasSelection && recommendedOption ? (
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Recommended: <span className="font-semibold">{recommendedOption.name}</span>
                        </p>
                      ) : null}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {decision.priority === 'high' && <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded border border-red-200">High Priority</span>}
                    <span className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>▼</span>
                  </div>
                </div>
              </div>

              {isExpanded && (
                <div className="border-t border-gray-200 dark:border-gray-600 p-4">
                  <p className="text-gray-600 dark:text-gray-400 mb-4">{decision.description}</p>
                  
                  {recommendedOption && (
                    <div className="mb-6 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 border-2 border-emerald-200 dark:border-emerald-700 rounded-xl p-6 shadow-sm">
                      <div className="flex items-start space-x-3 mb-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                        <div>
                          <h5 className="text-lg font-bold text-emerald-900 dark:text-emerald-100">
                            AI Recommendation
                          </h5>
                          <p className="text-emerald-700 dark:text-emerald-300 font-medium">
                            {recommendedOption.name}
                          </p>
                        </div>
                      </div>
                      
                      {/* Structured Recommendation Rationale */}
                      {decision.recommendation_rationale && (
                        <div className="mb-5">
                          <h6 className="text-sm font-semibold text-emerald-800 dark:text-emerald-200 mb-3 flex items-center">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            Why This Works Best
                          </h6>
                          <div className="space-y-2">
                            {Array.isArray(decision.recommendation_rationale) ? (
                              decision.recommendation_rationale.map((rationale, i) => (
                                <div key={i} className="flex items-start space-x-3 p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                                  <div className="flex-shrink-0 w-2 h-2 bg-emerald-500 rounded-full mt-2"></div>
                                  <DecisionLinkedText 
                                    text={rationale}
                                    decisions={decisions}
                                    onDecisionClick={handleDecisionClick}
                                    className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                                  />
                                </div>
                              ))
                            ) : (
                              <div className="space-y-2">
                                {decision.recommendation_rationale.split('\n').filter(line => line.trim()).map((line, i) => (
                                  <div key={i} className="flex items-start space-x-3 p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                                    <div className="flex-shrink-0 w-2 h-2 bg-emerald-500 rounded-full mt-2"></div>
                                    <DecisionLinkedText 
                                      text={line.trim()}
                                      decisions={decisions}
                                      onDecisionClick={handleDecisionClick}
                                      className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                                    />
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Evaluation Dimensions Summary */}
                      <div className="grid grid-cols-2 gap-4 mb-5">
                        <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg border border-emerald-100 dark:border-emerald-800">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className={`w-3 h-3 rounded-full ${getRiskColor(recommendedOption.riskLevel).includes('red') ? 'bg-red-400' : getRiskColor(recommendedOption.riskLevel).includes('yellow') ? 'bg-yellow-400' : 'bg-green-400'}`}></div>
                            <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Risk Level</div>
                          </div>
                          <div className="text-lg font-bold text-gray-900 dark:text-white">
                            {recommendedOption.riskLevel?.toUpperCase() || 'N/A'}
                          </div>
                        </div>
                        <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg border border-emerald-100 dark:border-emerald-800">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className={`w-3 h-3 rounded-full ${getComplexityColor(recommendedOption.complexity).includes('purple') ? 'bg-purple-400' : getComplexityColor(recommendedOption.complexity).includes('blue') ? 'bg-blue-400' : 'bg-cyan-400'}`}></div>
                            <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Complexity</div>
                          </div>
                          <div className="text-lg font-bold text-gray-900 dark:text-white">
                            {recommendedOption.complexity?.toUpperCase() || 'N/A'}
                          </div>
                        </div>
                        <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg border border-emerald-100 dark:border-emerald-800">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className={`w-3 h-3 rounded-full ${getComplexityColor(recommendedOption.maintenanceBurden).includes('purple') ? 'bg-purple-400' : getComplexityColor(recommendedOption.maintenanceBurden).includes('blue') ? 'bg-blue-400' : 'bg-cyan-400'}`}></div>
                            <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Maintenance</div>
                          </div>
                          <div className="text-lg font-bold text-gray-900 dark:text-white">
                            {recommendedOption.maintenanceBurden?.toUpperCase() || 'N/A'}
                          </div>
                        </div>
                        <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg border border-emerald-100 dark:border-emerald-800">
                          <div className="flex items-center space-x-2 mb-2">
                            <div className={`w-3 h-3 rounded-full ${recommendedOption.architecturalAlignment === 'high' ? 'bg-green-400' : recommendedOption.architecturalAlignment === 'medium' ? 'bg-yellow-400' : 'bg-red-400'}`}></div>
                            <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Alignment</div>
                          </div>
                          <div className="text-lg font-bold text-gray-900 dark:text-white">
                            {recommendedOption.architecturalAlignment?.toUpperCase() || 'N/A'}
                          </div>
                        </div>
                      </div>

                      {/* Cons with Mitigation Analysis */}
                      {recommendedOption.cons && recommendedOption.cons.length > 0 && (
                        <div className="mb-5">
                          <h6 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                            <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Trade-offs & Mitigations
                          </h6>
                          <div className="space-y-2">
                            {recommendedOption.cons.map((con, i) => (
                              <div key={i} className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg border-l-3 border-gray-300 dark:border-gray-600">
                                <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">
                                  {con}
                                </p>
                                {recommendedOption.mitigations && recommendedOption.mitigations[i] && (
                                  <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                                    {recommendedOption.mitigations[i]}
                                  </p>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Key Strengths */}
                      {recommendedOption.pros && recommendedOption.pros.length > 0 && (
                        <div>
                          <h6 className="text-sm font-semibold text-emerald-800 dark:text-emerald-200 mb-3 flex items-center">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Key Strengths
                          </h6>
                          <div className="grid grid-cols-1 gap-2">
                            {recommendedOption.pros.slice(0, 3).map((pro, i) => (
                              <div key={i} className="flex items-center space-x-3 p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                                <div className="flex-shrink-0 w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                                  <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                </div>
                                <p className="text-sm text-gray-700 dark:text-gray-300 font-medium">{pro}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="space-y-3">
                    {decision.options?.slice().sort((a, b) => {
                      const isARecommended = recommendedOption?.id === a.id;
                      const isBRecommended = recommendedOption?.id === b.id;
                      if (isARecommended) return -1;
                      if (isBRecommended) return 1;
                      return 0;
                    }).map(option => {
                      const isSelected = decision.selectedOption === option.id;
                      const isRecommended = recommendedOption?.id === option.id;
                      const isPending = pendingSelection[decision.id] === option.id;
                      const isDetailsExpanded = expandedDetails.has(option.id);

                      return (
                        <div key={option.id} className={`border-2 p-4 rounded-lg transition-all ${
                          isSelected 
                            ? isRecommended ? 'border-green-500 bg-green-50 dark:bg-green-900/20' : 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                            : isPending 
                              ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                              : 'border-gray-200 dark:border-gray-600'
                        }`}>
                          <div className="flex justify-between items-start">
                            <div className="flex-grow">
                                <div className="flex items-center flex-wrap gap-2 mb-2">
                                <h4 className="font-semibold text-gray-800 dark:text-gray-200">{option.name}</h4>
                                {isRecommended && (
                                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    AI Recommended
                                    </span>
                                )}
                                {isPending && (
                                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                    Pending Override
                                    </span>
                                )}
                                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getComplexityColor(option.complexity)} border`}>
                                    Complexity: {option.complexity || 'N/A'}
                                </span>
                                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRiskColor(option.riskLevel)} border`}>
                                    Risk: {option.riskLevel || 'N/A'}
                                </span>
                                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getComplexityColor(option.maintenanceBurden)} border`}>
                                    Maintenance: {option.maintenanceBurden || 'N/A'}
                                </span>
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-400 my-2">{option.description}</p>
                            </div>
                            {isSelected && (
                              <CheckCircleIcon className={`w-6 h-6 flex-shrink-0 ml-4 ${isRecommended ? 'text-green-500' : 'text-orange-500'}`} />
                            )}
                          </div>
                          
                          {isDetailsExpanded && (
                            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                              {option.alignmentScore !== undefined && (
                                <div className="mb-4">
                                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400">Architectural Alignment</label>
                                  <div className="flex items-center space-x-2 mt-1">
                                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
                                      <div className={`h-1.5 rounded-full ${option.alignmentScore >= 0.7 ? 'bg-green-500' : option.alignmentScore >= 0.4 ? 'bg-yellow-500' : 'bg-red-500'}`} style={{ width: `${option.alignmentScore * 100}%` }}></div>
                                    </div>
                                    <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">{Math.round(option.alignmentScore * 100)}%</span>
                                  </div>
                                  {option.alignmentReasoning && <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 italic">"{option.alignmentReasoning}"</p>}
                                </div>
                              )}
                              {option.alignmentJustification && (
                                <div className="mt-3">
                                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400">Alignment Analysis</label>
                                  <DecisionLinkedText 
                                    text={option.alignmentJustification}
                                    decisions={decisions}
                                    onDecisionClick={handleDecisionClick}
                                    className="text-xs text-gray-600 dark:text-gray-400 mt-1 block"
                                  />
                                </div>
                              )}
                              {option.debuggingComplexity && (
                                <div className="mt-3">
                                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400">Debugging Complexity</label>
                                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{option.debuggingComplexity}</p>
                                </div>
                              )}
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                                <div>
                                  <h6 className="text-xs font-medium text-green-700 dark:text-green-400 mb-1">✓ Pros:</h6>
                                  <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                    {option.pros.map((pro, i) => <li key={i}>{pro}</li>)}
                                  </ul>
                                </div>
                                <div>
                                  <h6 className="text-xs font-medium text-red-700 dark:text-red-400 mb-1">✗ Cons:</h6>
                                  <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                    {option.cons.map((con, i) => <li key={i}>{con}</li>)}
                                  </ul>
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="mt-4 flex items-center justify-between">
                            <button 
                              onClick={() => toggleDetails(option.id)}
                              className="text-sm font-medium text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300"
                            >
                              {isDetailsExpanded ? 'Hide Details' : 'Show Details'}
                            </button>
                            
                            {!isSelected && !isPending && (
                               <button 
                                onClick={() => handleSelectOption(decision.id, option.id)}
                                className={`px-4 py-2 rounded-md text-sm font-medium ${isRecommended ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-white'}`}
                              >
                                {isRecommended ? 'Accept Recommendation' : 'Choose this option'}
                              </button>
                            )}
                          </div>

                          {isPending && (
                            <div className="mt-4 p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                              <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-2">Override Rationale Required</h4>
                              <p className="text-sm text-orange-800 dark:text-orange-200 mb-2">
                                You've selected an option not recommended by the AI. Please provide a brief justification for this override.
                              </p>
                              <textarea
                                value={overrideReasons[decision.id] || ''}
                                onChange={(e) => handleOverrideRationale(decision.id, e.target.value)}
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                rows={2}
                                placeholder="Explain why you chose this option..."
                              />
                              <button 
                                onClick={() => confirmOverride(decision.id)}
                                disabled={!overrideReasons[decision.id]?.trim()}
                                className="mt-2 px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded disabled:bg-gray-400"
                              >
                                Confirm Override
                              </button>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div className="flex justify-between">
        <button onClick={onBack} className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
          ← Back
        </button>
        <button onClick={onContinue} disabled={isLoading || !allDecisionsMade} className="px-6 py-3 bg-amber-500 hover:bg-amber-600 disabled:bg-gray-400 text-white rounded-lg font-medium flex items-center space-x-2">
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Generating Document...</span>
            </>
          ) : (
            <>
              <span>Generate Design Document</span>
              <span>→</span>
            </>
          )}
        </button>
      </div>

      {/* Decision Detail Modal */}
      <DecisionDetailModal
        decision={selectedDecisionForModal ? decisions[selectedDecisionForModal] : null}
        isOpen={!!selectedDecisionForModal}
        onClose={() => setSelectedDecisionForModal(null)}
      />
    </div>
  );
} 