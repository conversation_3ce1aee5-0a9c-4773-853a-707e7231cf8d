import React, { useState } from 'react';

interface DeploymentConstitutionCheckStepProps {
  hasDeploymentConstitution: boolean;
  isLoading: boolean;
  error: string | null;
  onGenerate: () => void;
  onSkip: () => void;
  onContinue: () => void;
  repositorySlug: string;
}

export default function DeploymentConstitutionCheckStep({
  hasDeploymentConstitution,
  isLoading,
  error,
  onGenerate,
  onSkip,
  onContinue,
  repositorySlug
}: DeploymentConstitutionCheckStepProps) {
  const [showDetails, setShowDetails] = useState(false);

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Checking Deployment Context
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Analyzing your repository's deployment setup...
          </p>
        </div>
      </div>
    );
  }

  if (hasDeploymentConstitution) {
    return (
      <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Deployment Context Ready
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Your repository's deployment environment has been analyzed and is ready to inform architectural decisions.
          </p>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            🚀 Deployment-Aware Decision Making
          </h3>
          <p className="text-sm text-blue-800 dark:text-blue-200">
            The wizard will now consider your existing hosting provider, data stores, deployment pipeline, and infrastructure when evaluating technical options. This ensures recommendations are practical and aligned with your operational environment.
          </p>
        </div>

        <div className="flex justify-center">
          <button
            onClick={onContinue}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium"
          >
            Continue to Feature Definition
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Add Deployment Context
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-2">
          Enhance your architectural decisions with deployment awareness
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-500">
          Repository: <span className="font-mono">{repositorySlug}</span>
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-800 dark:text-red-200 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-6 mb-8">
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
          <h3 className="text-sm font-medium text-amber-900 dark:text-amber-100 mb-2">
            ⚡ What is Deployment Context?
          </h3>
          <p className="text-sm text-amber-800 dark:text-amber-200 mb-3">
            We analyze your repository's infrastructure files (package.json, Dockerfile, CI/CD workflows, etc.) to understand your deployment environment.
          </p>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-xs text-amber-700 dark:text-amber-300 hover:text-amber-900 dark:hover:text-amber-100 underline"
          >
            {showDetails ? 'Hide' : 'Show'} technical details
          </button>
          {showDetails && (
            <div className="mt-3 text-xs text-amber-700 dark:text-amber-300 space-y-1">
              <p>• <strong>Hosting Provider:</strong> Vercel, AWS, GCP, etc.</p>
              <p>• <strong>Data Stores:</strong> PostgreSQL, Redis, MongoDB, etc.</p>
              <p>• <strong>Containerization:</strong> Docker configuration</p>
              <p>• <strong>CI/CD Pipeline:</strong> GitHub Actions, deployment targets</p>
            </div>
          )}
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            🎯 How This Helps Your Decisions
          </h3>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Recommends solutions that work with your existing stack</li>
            <li>• Avoids suggesting technologies that conflict with your deployment</li>
            <li>• Considers operational overhead and maintenance burden</li>
            <li>• Aligns rollout strategies with your deployment pipeline</li>
          </ul>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={onGenerate}
          disabled={isLoading}
          className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Analyzing...' : 'Add Deployment Context'}
        </button>
        <button
          onClick={onSkip}
          className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 font-medium"
        >
          Skip for Now
        </button>
      </div>

      <div className="mt-6 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          You can always add deployment context later from the repository list
        </p>
      </div>
    </div>
  );
} 