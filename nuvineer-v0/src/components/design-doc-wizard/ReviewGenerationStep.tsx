import React, { useEffect, useRef, useState, useMemo } from 'react';
import {
  DocumentTextIcon,
  PencilIcon,
  XMarkIcon,
  PrinterIcon,
  DocumentArrowDownIcon,
  ClipboardDocumentCheckIcon,
  ExclamationTriangleIcon,
  RocketLaunchIcon,
  ChartBarIcon,
  ArrowUturnLeftIcon,
  CheckCircleIcon,
  BeakerIcon,
} from '@heroicons/react/24/outline';
import {
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/20/solid';
import type { WizardState } from '../../types/design-doc-wizard';
import { DecisionLinkedText, DecisionReference } from '../../utils/decisionUtils';
import DecisionDetailModal from '../DecisionDetailModal';
import { useDecisions, useProjectContextWithDebug } from '../../hooks/useDecisions';
import { formatDesignDocAsMarkdown, formatImplementationPlanAsMarkdown, formatDesignDocAsMarkdownForGitHub, formatImplementationPlanAsMarkdownForGitHub } from '../../utils/design-doc-wizard';
import ShareButton from './ShareButton';

interface ReviewGenerationStepProps {
  wizardState: WizardState;
  onBack: () => void;
  onViewDocument?: () => void;
  onStartNew?: () => void;
  onAddToGitHubIssue?: () => Promise<{ success: boolean; error?: string; commentUrl?: string; issueNumber?: string; repository?: string; labelsAdded?: string[]; }>;
  onGenerateImplementationPlan?: () => Promise<{ success: boolean; error?: string; implementationPlan?: any; }>;
  onAddImplementationPlanToGitHub?: (plan: any) => Promise<{ success: boolean; error?: string; commentUrl?: string; issueNumber?: string; repository?: string; labelsAdded?: string[]; }>;
  onGenerateRolloutStrategy?: () => Promise<{ success: boolean; error?: string; rolloutStrategy?: any; }>;
  onGenerateStrategy?: () => Promise<{ success: boolean; error?: string; strategy?: any; }>;
  isLoading: boolean;
  isPublic?: boolean;
  installationId?: string | number;
  repositorySlug?: string;
  dbSessionId?: string;
}

export default function ReviewGenerationStep({
  wizardState,
  onBack,
  onViewDocument,
  onStartNew,
  onAddToGitHubIssue,
  onGenerateImplementationPlan,
  onAddImplementationPlanToGitHub,
  onGenerateRolloutStrategy,
  onGenerateStrategy,
  isLoading,
  isPublic,
  installationId,
  repositorySlug: repositorySlugFromProps,
  dbSessionId
}: ReviewGenerationStepProps) {
  const { generatedDoc, decisionPoints, taskDetails } = wizardState;
  const completedDecisions = decisionPoints.filter(dp => dp.selectedOption).length;
  
  const mermaidContainerRef = useRef<HTMLDivElement>(null);
  const successMessageRef = useRef<HTMLDivElement>(null);
  const [isAddingToGitHub, setIsAddingToGitHub] = useState(false);
  const [githubResult, setGithubResult] = useState<{ success: boolean; error?: string; commentUrl?: string; issueNumber?: string; repository?: string; labelsAdded?: string[]; } | null>(null);
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [implementationPlan, setImplementationPlan] = useState<any>(null);
  const [isAddingPlanToGitHub, setIsAddingPlanToGitHub] = useState(false);
  const [planGithubResult, setPlanGithubResult] = useState<{ success: boolean; error?: string; commentUrl?: string; issueNumber?: string; repository?: string; labelsAdded?: string[]; } | null>(null);
  const [isGeneratingStrategy, setIsGeneratingStrategy] = useState(false);
  const [rolloutStrategy, setRolloutStrategy] = useState<any>(null);
  
  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [editableMarkdown, setEditableMarkdown] = useState('');
  const [isImplementationPlanEditMode, setIsImplementationPlanEditMode] = useState(false);
  const [editableImplementationPlanMarkdown, setEditableImplementationPlanMarkdown] = useState('');

  const [activeTab, setActiveTab] = useState('doc');

  // Decision linking state
  const [selectedDecisionForModal, setSelectedDecisionForModal] = useState<string | null>(null);

  // Get repository context for decision fetching
  const repositorySlug = repositorySlugFromProps || new URLSearchParams(window.location.search).get('repositorySlug') || '';
  const finalInstallationId = installationId || new URLSearchParams(window.location.search).get('installationId') || '';
  const { repositorySlug: finalRepoSlug, installationId: finalInstallationIdFromHook } = useProjectContextWithDebug(repositorySlug, finalInstallationId);

  console.log('[ReviewGenerationStep] Debug Info', {
    repositorySlug,
    finalInstallationId,
    finalRepoSlug,
    finalInstallationIdFromHook
  });

  // Collect all text that might contain decision references
  const textsToSearch = useMemo(() => {
    const allTexts: (string | string[])[] = [];
    
    if (generatedDoc) {
      // Goals and non-goals
      if (generatedDoc.goals) allTexts.push(generatedDoc.goals);
      if (generatedDoc.non_goals) allTexts.push(generatedDoc.non_goals);
      
      // High-level design
      if (generatedDoc.high_level_design) {
        if (generatedDoc.high_level_design.overall_system_overview) {
          allTexts.push(generatedDoc.high_level_design.overall_system_overview);
        }
        if (generatedDoc.high_level_design.core_architectural_choices) {
          generatedDoc.high_level_design.core_architectural_choices.forEach((choice: any) => {
            if (choice.recommended_approach_description) allTexts.push(choice.recommended_approach_description);
            if (choice.justification_and_context) allTexts.push(choice.justification_and_context);
          });
        }
      }
      
      // Referenced decisions
      if (generatedDoc.referenced_decisions) {
        generatedDoc.referenced_decisions.forEach((decision: any) => {
          if (decision.summary_of_relevance_in_this_design) allTexts.push(decision.summary_of_relevance_in_this_design);
          if (decision.id) allTexts.push(decision.id);
        });
      }
    }
    
    // Implementation plan texts
    if (implementationPlan) {
      if (implementationPlan.summary) allTexts.push(implementationPlan.summary);
      if (implementationPlan.referenced_decisions) allTexts.push(implementationPlan.referenced_decisions);
      
      if (implementationPlan.enhanced_milestones) {
        implementationPlan.enhanced_milestones.forEach((milestone: any) => {
          if (milestone.applicable_developer_guidance_and_best_practices) {
            milestone.applicable_developer_guidance_and_best_practices.forEach((guidance: any) => {
              if (guidance.guidance_summary) allTexts.push(guidance.guidance_summary);
            });
          }
        });
      }
    }
    
    return allTexts;
  }, [generatedDoc, implementationPlan]);

  // Fetch decisions based on references found in the text
  const { decisions, isLoading: isLoadingDecisions, error: decisionsError } = useDecisions({
    repositorySlug: finalRepoSlug,
    installationId: finalInstallationIdFromHook,
    texts: textsToSearch
  });

  // Debug logging for decision fetching
  useEffect(() => {
    if (Object.keys(decisions).length > 0) {
      console.log('[ReviewGenerationStep] Fetched decisions:', Object.keys(decisions));
      console.log('[ReviewGenerationStep] Sample decision:', decisions[Object.keys(decisions)[0]]);
    }
    if (generatedDoc?.referenced_decisions) {
      console.log('[ReviewGenerationStep] Referenced decision IDs:', generatedDoc.referenced_decisions.map((d: any) => d.id));
    }
  }, [decisions, generatedDoc]);

  const handleDecisionClick = (decisionId: string) => {
    setSelectedDecisionForModal(decisionId);
  };

  // Check if task description contains a GitHub issue URL
  const taskDescription = `**Task:** ${taskDetails?.description || 'No description provided'}`;
  const hasGitHubIssue = taskDescription && taskDescription.includes('**GitHub Issue:**');
  const githubIssueMatch = taskDescription?.match(/\*\*GitHub Issue:\*\* https:\/\/github\.com\/([^\/]+)\/([^\/]+)\/issues\/(\d+)/);
  const issueInfo = githubIssueMatch ? {
    owner: githubIssueMatch[1],
    repo: githubIssueMatch[2],
    issueNumber: githubIssueMatch[3],
    url: `https://github.com/${githubIssueMatch[1]}/${githubIssueMatch[2]}/issues/${githubIssueMatch[3]}`
  } : null;

  // Auto-scroll to success message when it appears
  useEffect(() => {
    if ((githubResult?.success || planGithubResult?.success) && successMessageRef.current) {
      setTimeout(() => {
        successMessageRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      }, 100);
    }
  }, [githubResult?.success, planGithubResult?.success]);

  // Check if GitHub integration is available (has valid installation ID)
  const isGitHubAvailable = () => {
    const id = typeof installationId === 'string' ? parseInt(installationId, 10) : installationId;
    return !!(id && id !== 0);
  };

  // Sanitize Mermaid diagram content to prevent rendering errors
  const sanitizeMermaidContent = (mermaidText: string): string => {
    if (!mermaidText) return '';
    
    return mermaidText
      // Only remove characters that actually break Mermaid parsing
      // Keep valid Mermaid syntax like arrows (-->, ->, etc.), brackets, curly braces for decision nodes, etc.
      
      // Remove problematic quotes that can break string parsing
      .replace(/[""'']/g, '"') // Replace smart quotes with regular quotes
      
      // Handle parentheses carefully - they're valid in some contexts but can break node definitions
      .replace(/\(([^)]*)\)/g, (match, content) => {
        // Keep parentheses if they seem to be part of valid Mermaid syntax or simple text
        if (content.includes('-->') || content.includes('->') || content.match(/^[A-Za-z0-9_\s\-.,!?]+$/)) {
          return match;
        }
        // Otherwise, replace with brackets or remove problematic content
        return `[${content.replace(/[^\w\s\-.,!?]/g, '')}]`;
      })
      
      // Remove characters that commonly cause parsing issues (but keep valid Mermaid syntax)
      .replace(/[#%@$]/g, '') // Remove hash, percent, at, dollar (can cause parsing issues)
      .replace(/[\\]/g, '') // Remove backslashes (escape character issues)
      
      // Clean up multiple spaces but preserve line breaks and structure
      .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space
      .replace(/\n\s*\n/g, '\n') // Multiple newlines to single newline
      .trim();
  };

  const handleAddToGitHub = async () => {
    if (!onAddToGitHubIssue) return;
    
    setIsAddingToGitHub(true);
    setGithubResult(null);
    
    try {
      const result = await onAddToGitHubIssue();
      setGithubResult(result);
    } catch (error) {
      setGithubResult({ success: false, error: 'Failed to add to GitHub issue' });
    } finally {
      setIsAddingToGitHub(false);
    }
  };

  const handleGenerateImplementationPlan = async () => {
    if (!onGenerateImplementationPlan) return;
    
    setIsGeneratingPlan(true);
    setImplementationPlan(null);
    
    try {
      const result = await onGenerateImplementationPlan();
      if (result.success && result.implementationPlan) {
        setImplementationPlan(result.implementationPlan);
      } else {
        console.error('Failed to generate implementation plan:', result.error);
      }
    } catch (error) {
      console.error('Error generating implementation plan:', error);
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  const handleAddPlanToGitHub = async () => {
    if (!onAddImplementationPlanToGitHub || !implementationPlan) return;
    
    console.log('[ReviewGenerationStep] handleAddPlanToGitHub called');
    setIsAddingPlanToGitHub(true);
    setPlanGithubResult(null);
    
    try {
      console.log('[ReviewGenerationStep] Calling onAddImplementationPlanToGitHub with plan:', !!implementationPlan);
      const result = await onAddImplementationPlanToGitHub(implementationPlan);
      console.log('[ReviewGenerationStep] onAddImplementationPlanToGitHub result:', result);
      setPlanGithubResult(result);
    } catch (error) {
      console.error('[ReviewGenerationStep] Error in handleAddPlanToGitHub:', error);
      setPlanGithubResult({ success: false, error: 'Failed to add implementation plan to GitHub issue' });
    } finally {
      setIsAddingPlanToGitHub(false);
    }
  };

  const handleGenerateRolloutStrategy = async () => {
    if (!onGenerateRolloutStrategy) return;
    
    setIsGeneratingStrategy(true);
    setRolloutStrategy(null);
    
    try {
      const result = await onGenerateRolloutStrategy();
      if (result.success && result.rolloutStrategy) {
        setRolloutStrategy(result.rolloutStrategy);
      } else {
        console.error('Failed to generate rollout strategy:', result.error);
      }
    } catch (error) {
      console.error('Error generating rollout strategy:', error);
    } finally {
      setIsGeneratingStrategy(false);
    }
  };

  const handleGenerateStrategy = async () => {
    if (!onGenerateStrategy) return;
    
    setIsGeneratingStrategy(true);
    setRolloutStrategy(null);
    
    try {
      const result = await onGenerateStrategy();
      if (result.success && result.strategy) {
        setRolloutStrategy(result.strategy);
      } else {
        console.error('Failed to generate strategy:', result.error);
      }
    } catch (error) {
      console.error('Error generating strategy:', error);
    } finally {
      setIsGeneratingStrategy(false);
    }
  };

  // Initialize and render Mermaid diagrams
  useEffect(() => {
    const initializeMermaid = async () => {
      try {
        // Dynamically import mermaid
        const mermaid = await import('mermaid');
        
        // Initialize mermaid with dark theme support
        mermaid.default.initialize({
          startOnLoad: false,
          theme: 'default',
          themeVariables: {
            primaryColor: '#3b82f6',
            primaryTextColor: '#1f2937',
            primaryBorderColor: '#e5e7eb',
            lineColor: '#6b7280',
            secondaryColor: '#f3f4f6',
            tertiaryColor: '#ffffff',
            background: '#ffffff',
            mainBkg: '#ffffff',
            secondBkg: '#f9fafb',
            tertiaryBkg: '#f3f4f6'
          },
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
          },
          sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
          }
        });

        // Find all mermaid containers and render them
        const containers = document.querySelectorAll('.mermaid-diagram');
        containers.forEach(async (container, index) => {
          const element = container as HTMLElement;
          const diagramText = element.getAttribute('data-diagram');
          
          if (diagramText) {
            try {
              // Sanitize the diagram text before rendering
              const sanitizedDiagramText = sanitizeMermaidContent(diagramText);
              
              // Use the correct mermaid render API
              const renderResult = await mermaid.default.render(`mermaid-${index}`, sanitizedDiagramText);
              // Handle both string and object return types safely
              const svgContent = typeof renderResult === 'string' 
                ? renderResult 
                : (renderResult as any)?.svg || renderResult;
              element.innerHTML = svgContent;
            } catch (error) {
              console.error('Error rendering mermaid diagram:', error);
              element.innerHTML = `
                <div class="p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
                  <p class="text-red-800 dark:text-red-200 font-medium">Error rendering diagram</p>
                  <pre class="text-sm text-red-600 dark:text-red-400 mt-2 whitespace-pre-wrap">${diagramText}</pre>
                </div>
              `;
            }
          }
        });
      } catch (error) {
        console.error('Error loading mermaid:', error);
      }
    };

    if (generatedDoc && generatedDoc.high_level_design?.process_flow_diagram_mermaid) {
      initializeMermaid();
    }
  }, [generatedDoc, implementationPlan, rolloutStrategy]);

  // Format implementation plan as markdown
  const formatImplementationPlanAsMarkdown = (plan: any): string => {
    if (!plan) return '';
    
    let markdown = `# ${plan.implementation_plan_title || 'Implementation Plan'}\n\n`;
    
    // Main Feature Branch
    if (plan.main_feature_branch_name) {
      markdown += `## Project Setup\n\n`;
      markdown += `**Main Feature Branch:** \`${plan.main_feature_branch_name}\`\n\n`;
      markdown += `This branch will accumulate all milestone work and serve as the integration point for the complete feature.\n\n`;
    }

    // AI Agent Protocol Compliance
    if (plan.ai_agent_protocol_compliance) {
      markdown += `## Implementation Protocol\n\n`;
      const compliance = plan.ai_agent_protocol_compliance;
      markdown += `**Protocol Source:** ${compliance.protocol_source === 'custom' ? 'Custom AI Agent Protocol' : 'Default Protocol'}\n\n`;
      markdown += `**Quality Assurance Features:**\n`;
      if (compliance.milestone_driven_approach) markdown += `- ✅ Milestone-driven development approach\n`;
      if (compliance.branch_naming_enforced) markdown += `- ✅ Standardized branch naming conventions\n`;
      if (compliance.independent_milestone_integration) markdown += `- ✅ Independent milestone integration capability\n`;
      if (compliance.quality_control_integrated) markdown += `- ✅ Built-in quality control at each milestone\n`;
      if (compliance.scope_validation_required) markdown += `- ✅ Mandatory scope validation before integration\n`;
      if (compliance.pre_integration_validation_required) markdown += `- ✅ Pre-integration validation and risk assessment\n`;
      markdown += `\n`;
    }

    // Enhanced Milestones
    if (plan.enhanced_milestones && plan.enhanced_milestones.length > 0) {
      markdown += `## Implementation Milestones\n\n`;
      markdown += `The implementation is broken down into ${plan.enhanced_milestones.length} sequential milestones, each independently testable and deployable.\n\n`;
      
      plan.enhanced_milestones.forEach((milestone: any, index: number) => {
        markdown += `### Milestone ${index + 1}: ${milestone.title || milestone.milestone_id}\n\n`;
        
        // Priority and Branch
        if (milestone.priority) {
          markdown += `**Priority:** ${milestone.priority}\n`;
        }
        if (milestone.git_must_use_milestone_branch_name) {
          markdown += `**Milestone Branch:** \`${milestone.git_must_use_milestone_branch_name}\`\n\n`;
        }
        
        // Description
        if (milestone.description) {
          markdown += `${milestone.description}\n\n`;
        }
        
        // Key Tasks and Deliverables
        if (milestone.key_tasks_and_deliverables && milestone.key_tasks_and_deliverables.length > 0) {
          markdown += `**Key Tasks and Deliverables:**\n`;
          milestone.key_tasks_and_deliverables.forEach((task: any) => {
            const taskText = typeof task === 'string' ? task : task.task || task.description || 'Task not specified';
            markdown += `- ${taskText}\n`;
          });
          markdown += `\n`;
        }
        
        // Scope Validation
        if (milestone.scope_validation) {
          markdown += `**Scope Validation:**\n`;
          if (milestone.scope_validation.planned_deliverables && milestone.scope_validation.planned_deliverables.length > 0) {
            markdown += `- **Planned Deliverables:**\n`;
            milestone.scope_validation.planned_deliverables.forEach((deliverable: string) => {
              markdown += `  - ${deliverable}\n`;
            });
          }
          markdown += `\n`;
        }
        
        // Verification Criteria
        if (milestone.verification_criteria && milestone.verification_criteria.length > 0) {
          markdown += `**Verification Criteria:**\n`;
          milestone.verification_criteria.forEach((criteria: string) => {
            markdown += `- ${criteria}\n`;
          });
          markdown += `\n`;
        }
        
        // Developer Guidance and Best Practices
        if (milestone.applicable_developer_guidance_and_best_practices && milestone.applicable_developer_guidance_and_best_practices.length > 0) {
          markdown += `**Applicable Developer Guidance:**\n`;
          milestone.applicable_developer_guidance_and_best_practices.forEach((guidance: any) => {
            markdown += `- **${guidance.retrieved_decision_title}**\n`;
            if (guidance.guidance_summary) {
              markdown += `  - ${guidance.guidance_summary}\n`;
            }
            if (guidance.related_files && guidance.related_files.length > 0) {
              markdown += `  - Related files: ${guidance.related_files.join(', ')}\n`;
            }
          });
          markdown += `\n`;
        }
        
        markdown += `---\n\n`;
      });
    }

    // Data Model Summary
    if (plan.data_model_summary && plan.data_model_summary !== 'N/A') {
      markdown += `## Data Model Changes\n\n`;
      markdown += `${plan.data_model_summary}\n\n`;
    }

    // Referenced Decisions
    if (plan.referenced_decisions && plan.referenced_decisions.length > 0) {
      markdown += `## Referenced Architectural Decisions\n\n`;
      plan.referenced_decisions.forEach((decision: string) => {
        markdown += `- ${decision}\n`;
      });
      markdown += `\n`;
    }

    // Overall Success Metrics
    if (plan.overall_success_metrics && plan.overall_success_metrics.length > 0) {
      markdown += `## Success Metrics\n\n`;
      plan.overall_success_metrics.forEach((metric: string) => {
        markdown += `- ${metric}\n`;
      });
      markdown += `\n`;
    }

    // Implementation Notes
    if (plan.full_design_doc_analyzed) {
      markdown += `## Implementation Notes\n\n`;
      markdown += `- ✅ Complete design document analyzed for comprehensive planning\n`;
      if (plan.finalized_design_doc_content_used) {
        markdown += `- ✅ Finalized design document content incorporated\n`;
      }
      markdown += `- ✅ Knowledge base integration for developer guidance\n`;
      markdown += `- ✅ Branch naming conventions enforced for consistency\n`;
      markdown += `- ✅ Quality control checkpoints built into each milestone\n\n`;
    }
    
    return markdown;
  };

  // Render markdown content with proper styling


  // Print functionality for the document content
  const handlePrint = () => {
    const printContent = implementationPlan ? formatImplementationPlanAsMarkdown(implementationPlan) : formatDesignDocAsMarkdown(generatedDoc);
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${implementationPlan ? 'Implementation Plan' : 'Design Document'} - ${generatedDoc?.title || 'Document'}</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; color: #1f2937 !important; background: white !important; }
            h1 { color: #1a202c !important; border-bottom: 2px solid #e2e8f0; padding-bottom: 10px; }
            h2 { color: #2d3748 !important; margin-top: 30px; }
            h3 { color: #4a5568 !important; }
            h4 { color: #4a5568 !important; }
            ul, ol { padding-left: 20px; }
            li { margin-bottom: 5px; color: #1f2937 !important; }
            p { color: #1f2937 !important; }
            strong { color: #1f2937 !important; }
            code { background: #f7fafc; padding: 2px 4px; border-radius: 3px; font-family: 'SF Mono', Monaco, monospace; color: #1f2937 !important; }
            pre { background: #f7fafc; padding: 15px; border-radius: 5px; overflow-x: auto; color: #1f2937 !important; }
            .mermaid-placeholder { background: #f0f9ff; border: 1px dashed #0ea5e9; padding: 20px; text-align: center; color: #0369a1 !important; margin: 20px 0; }
            @media print { body { margin: 0; padding: 15px; color: #000 !important; } }
          </style>
        </head>
        <body>
          ${convertMarkdownToHTML(printContent)}
        </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    }
  };

  // Save functionality for the document content
  const handleSave = () => {
    const content = implementationPlan ? formatImplementationPlanAsMarkdown(implementationPlan) : formatDesignDocAsMarkdown(generatedDoc);
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${implementationPlan ? 'implementation-plan' : 'design-document'}-${generatedDoc?.title?.toLowerCase().replace(/\s+/g, '-') || 'document'}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Convert markdown to HTML for printing (fixed regex)
  const convertMarkdownToHTML = (markdown: string): string => {
    let html = markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^#### (.*$)/gm, '<h4>$1</h4>')
      .replace(/^\- (.*$)/gm, '<li>$1</li>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      .replace(/```mermaid\n([\s\S]*?)\n```/g, '<div class="mermaid-placeholder">Mermaid Diagram: $1</div>')
      .replace(/```([\s\S]*?)```/g, '<pre>$1</pre>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^(?!<[hul])/gm, '<p>')
      .replace(/(?<!>)$/gm, '</p>');
    
    // Clean up empty paragraphs
    html = html.replace(/<p><\/p>/g, '');
    
    // Wrap consecutive list items in ul tags
    html = html.replace(/(<li>.*?<\/li>\s*)+/g, (match) => `<ul>${match}</ul>`);
    
    return html;
  };

  // Toggle edit mode
  const handleEditToggle = () => {
    if (!isEditMode) {
      // Entering edit mode - populate with current markdown
      const markdown = implementationPlan ? formatImplementationPlanAsMarkdown(implementationPlan) : formatDesignDocAsMarkdown(generatedDoc);
      setEditableMarkdown(markdown);
      setIsEditMode(true);
    } else {
      // Exiting edit mode - save changes (for now, just exit)
      handleSaveEdit();
    }
  };

  const handleImplementationPlanEditToggle = () => {
    if (!isImplementationPlanEditMode) {
      const markdown = formatImplementationPlanAsMarkdown(implementationPlan);
      setEditableImplementationPlanMarkdown(markdown);
      setIsImplementationPlanEditMode(true);
    } else {
      // For now, just exit. In the future, this would save.
      setIsImplementationPlanEditMode(false);
    }
  };

  // Save edited markdown (for now, just toggle back to view mode)
  const handleSaveEdit = () => {
    // TODO: Implement actual save functionality to update the document
    setIsEditMode(false);
    // For now, we'll just exit edit mode. In the future, this could update the document
  };

  // Render formatted design document (not raw markdown)
  const renderFormattedDesignDoc = (doc: any) => {
    return (
      <div className="space-y-8">
        {/* Title */}
        <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white force-dark-text">
            {doc.title || 'Design Document'}
          </h1>
        </div>

        {/* Goals */}
        {doc.goals && doc.goals.length > 0 && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Goals</h2>
            <ul className="space-y-2">
              {doc.goals.map((goal: string, index: number) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2 mt-1">✓</span>
                  <span className="text-gray-700 dark:text-gray-300">
                    <DecisionLinkedText 
                      text={goal}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className="text-gray-700 dark:text-gray-300"
                    />
                  </span>
                </li>
              ))}
            </ul>
          </section>
        )}

        {/* Non-Goals */}
        {doc.non_goals && doc.non_goals.length > 0 && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Non-Goals</h2>
            <ul className="space-y-2">
              {doc.non_goals.map((nonGoal: string, index: number) => (
                <li key={index} className="flex items-start">
                  <span className="text-red-500 mr-2 mt-1">✗</span>
                  <span className="text-gray-700 dark:text-gray-300">
                    <DecisionLinkedText 
                      text={nonGoal}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className="text-gray-700 dark:text-gray-300"
                    />
                  </span>
                </li>
              ))}
            </ul>
          </section>
        )}

        {/* High-Level Design */}
        {doc.high_level_design && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">High-Level Design</h2>
            
            {/* System Overview */}
            {doc.high_level_design.overall_system_overview && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">System Overview</h3>
                <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300">
                    <DecisionLinkedText 
                      text={doc.high_level_design.overall_system_overview}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className="text-gray-700 dark:text-gray-300"
                    />
                  </p>
                </div>
              </div>
            )}

            {/* Process Flow Diagram */}
            {doc.high_level_design.process_flow_diagram_mermaid && doc.high_level_design.process_flow_diagram_mermaid !== 'N/A' && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Process Flow</h3>
                <div className="bg-white dark:bg-gray-100 p-6 rounded-lg border border-gray-200 dark:border-gray-600 overflow-x-auto">
                  <div 
                    className="mermaid-diagram flex justify-center items-center min-h-[200px]"
                    data-diagram={sanitizeMermaidContent(doc.high_level_design.process_flow_diagram_mermaid)}
                  >
                    <div className="text-gray-500 dark:text-gray-600">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                      <p className="text-sm">Rendering diagram...</p>
                    </div>
                  </div>
                  
                  <details className="mt-4">
                    <summary className="text-sm text-gray-600 dark:text-gray-500 cursor-pointer hover:text-gray-800 dark:hover:text-gray-300">
                      Show diagram source
                    </summary>
                    <pre className="mt-2 text-xs text-gray-600 dark:text-gray-500 bg-gray-50 dark:bg-gray-200 p-3 rounded overflow-x-auto">
                      {doc.high_level_design.process_flow_diagram_mermaid}
                    </pre>
                  </details>
                </div>
              </div>
            )}

            {/* Core Architectural Choices */}
            {doc.high_level_design.core_architectural_choices && doc.high_level_design.core_architectural_choices.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Key Architectural Decisions</h3>
                <div className="space-y-4">
                  {doc.high_level_design.core_architectural_choices.map((choice: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{choice.title}</h4>
                      <p className="text-gray-700 dark:text-gray-300 mb-2">
                        <DecisionLinkedText 
                          text={choice.recommended_approach_description}
                          decisions={decisions}
                          onDecisionClick={handleDecisionClick}
                          className="text-gray-700 dark:text-gray-300"
                        />
                      </p>
                      {choice.justification_and_context && (
                        <div className="text-sm text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-700 p-3 rounded border-l-4 border-blue-500">
                          <strong>Justification:</strong> <DecisionLinkedText 
                            text={choice.justification_and_context}
                            decisions={decisions}
                            onDecisionClick={handleDecisionClick}
                            className="text-gray-600 dark:text-gray-400"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Key Components */}
            {doc.high_level_design.key_components_and_responsibilities && doc.high_level_design.key_components_and_responsibilities.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Key Components</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {doc.high_level_design.key_components_and_responsibilities.map((component: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">{component.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{component.responsibility}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Data Model Changes */}
            {doc.high_level_design.data_model_changes && doc.high_level_design.data_model_changes !== 'N/A' && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Data Model Changes</h3>
                <div className="bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{doc.high_level_design.data_model_changes}</p>
                </div>
              </div>
            )}

            {/* Security Considerations */}
            {doc.high_level_design.security_considerations && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Security Considerations</h3>
                <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300">{doc.high_level_design.security_considerations}</p>
                </div>
              </div>
            )}

            {/* Error Handling and Recovery */}
            {doc.high_level_design.error_handling_and_recovery && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Error Handling & Recovery</h3>
                <div className="bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                  {doc.high_level_design.error_handling_and_recovery.critical_error_scenarios && (
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Critical Error Scenarios:</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {doc.high_level_design.error_handling_and_recovery.critical_error_scenarios.map((scenario: string, index: number) => (
                          <li key={index} className="text-gray-700 dark:text-gray-300">{scenario}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {doc.high_level_design.error_handling_and_recovery.overall_strategy && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Overall Strategy:</h4>
                      <p className="text-gray-700 dark:text-gray-300">{doc.high_level_design.error_handling_and_recovery.overall_strategy}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </section>
        )}

        {/* Alternatives Analysis */}
        {doc.alternatives_analysis && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Alternatives Analysis</h2>
            
            {/* Overall Solution Alternatives */}
            {doc.alternatives_analysis.overall_solution_alternatives && doc.alternatives_analysis.overall_solution_alternatives.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Alternative Approaches Considered</h3>
                <div className="space-y-4">
                  {doc.alternatives_analysis.overall_solution_alternatives.map((alt: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Alternative {index + 1}: {alt.approach_name}</h4>
                      <p className="text-gray-700 dark:text-gray-300 mb-3">{alt.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {alt.pros && alt.pros.length > 0 && (
                          <div>
                            <h5 className="font-medium text-green-700 dark:text-green-300 mb-2">Pros:</h5>
                            <ul className="list-disc list-inside space-y-1">
                              {alt.pros.map((pro: string, proIndex: number) => (
                                <li key={proIndex} className="text-gray-600 dark:text-gray-400 text-sm">{pro}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {alt.cons && alt.cons.length > 0 && (
                          <div>
                            <h5 className="font-medium text-red-700 dark:text-red-300 mb-2">Cons:</h5>
                            <ul className="list-disc list-inside space-y-1">
                              {alt.cons.map((con: string, conIndex: number) => (
                                <li key={conIndex} className="text-gray-600 dark:text-gray-400 text-sm">{con}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                      
                      {alt.alignment_with_context && (
                        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Context Alignment:</strong> {alt.alignment_with_context}
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Key Technical Decision Alternatives */}
            {doc.alternatives_analysis.key_technical_decision_alternatives && doc.alternatives_analysis.key_technical_decision_alternatives.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Key Technical Decision Alternatives</h3>
                <div className="space-y-3">
                  {doc.alternatives_analysis.key_technical_decision_alternatives.map((alt: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">{alt.decision_point_title}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                        <strong>Alternative Considered:</strong> {alt.alternative_considered}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        <strong>Reason Not Chosen:</strong> {alt.reason_not_chosen}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Overall Recommendation */}
            {doc.alternatives_analysis.overall_recommendation_and_justification && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Recommendation</h3>
                <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300">{doc.alternatives_analysis.overall_recommendation_and_justification}</p>
                </div>
              </div>
            )}
          </section>
        )}

        {/* Implementation Strategy */}
        {doc.implementation_strategy && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Implementation Strategy</h2>
            
            {/* Strategic Recommendations */}
            {doc.implementation_strategy.strategic_recommendations && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Strategic Recommendations</h3>
                <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  
                  {doc.implementation_strategy.strategic_recommendations.immediate_focus && doc.implementation_strategy.strategic_recommendations.immediate_focus.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">Immediate Focus:</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {doc.implementation_strategy.strategic_recommendations.immediate_focus.map((item: string, index: number) => (
                          <li key={index} className="text-yellow-800 dark:text-yellow-200">{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {doc.implementation_strategy.strategic_recommendations.defer_until_later && doc.implementation_strategy.strategic_recommendations.defer_until_later.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">Defer Until Later:</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {doc.implementation_strategy.strategic_recommendations.defer_until_later.map((item: string, index: number) => (
                          <li key={index} className="text-yellow-800 dark:text-yellow-200">{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {doc.implementation_strategy.strategic_recommendations.complexity_concerns && doc.implementation_strategy.strategic_recommendations.complexity_concerns.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">Complexity Concerns:</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {doc.implementation_strategy.strategic_recommendations.complexity_concerns.map((item: string, index: number) => (
                          <li key={index} className="text-yellow-800 dark:text-yellow-200">{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {doc.implementation_strategy.strategic_recommendations.constitution_alignment && (
                    <div>
                      <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">Strategic Alignment:</h4>
                      <p className="text-yellow-800 dark:text-yellow-200">{doc.implementation_strategy.strategic_recommendations.constitution_alignment}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Core Milestones */}
            {doc.implementation_strategy.core_milestones && doc.implementation_strategy.core_milestones.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Core Milestones</h3>
                <div className="space-y-4">
                  {doc.implementation_strategy.core_milestones.map((milestone: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          {milestone.milestone_id || `M${index + 1}`}: {milestone.title}
                        </h4>
                        <div className="flex space-x-2">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            milestone.priority === 'P0' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                            milestone.priority === 'P1' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                          }`}>
                            {milestone.priority}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            milestone.estimated_complexity === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                            milestone.estimated_complexity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          }`}>
                            {milestone.estimated_complexity} complexity
                          </span>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 dark:text-gray-300 mb-3">{milestone.description}</p>
                      
                      {milestone.user_value_delivered && (
                        <div className="mb-3">
                          <h5 className="font-medium text-gray-900 dark:text-white mb-1">User Value:</h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{milestone.user_value_delivered}</p>
                        </div>
                      )}
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {milestone.success_criteria && milestone.success_criteria.length > 0 && (
                          <div>
                            <h5 className="font-medium text-gray-900 dark:text-white mb-2">Success Criteria:</h5>
                            <ul className="list-disc list-inside space-y-1">
                              {milestone.success_criteria.map((criteria: string, criteriaIndex: number) => (
                                <li key={criteriaIndex} className="text-sm text-gray-600 dark:text-gray-400">{criteria}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {milestone.dependencies && milestone.dependencies.length > 0 && (
                          <div>
                            <h5 className="font-medium text-gray-900 dark:text-white mb-2">Dependencies:</h5>
                            <ul className="list-disc list-inside space-y-1">
                              {milestone.dependencies.map((dep: string, depIndex: number) => (
                                <li key={depIndex} className="text-sm text-gray-600 dark:text-gray-400">{dep}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {milestone.risks && milestone.risks.length > 0 && (
                          <div>
                            <h5 className="font-medium text-gray-900 dark:text-white mb-2">Risks:</h5>
                            <ul className="list-disc list-inside space-y-1">
                              {milestone.risks.map((risk: string, riskIndex: number) => (
                                <li key={riskIndex} className="text-sm text-red-600 dark:text-red-400">{risk}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Deferrable Milestones */}
            {doc.implementation_strategy.deferrable_milestones && doc.implementation_strategy.deferrable_milestones.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Deferrable Milestones</h3>
                <div className="space-y-3">
                  {doc.implementation_strategy.deferrable_milestones.map((milestone: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                        {milestone.milestone_id || `D${index + 1}`}: {milestone.title}
                      </h4>
                      <p className="text-gray-700 dark:text-gray-300 mb-3">{milestone.description}</p>
                      
                      {milestone.deferral_rationale && (
                        <div className="mb-2">
                          <h5 className="font-medium text-gray-900 dark:text-white mb-1">Deferral Rationale:</h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{milestone.deferral_rationale}</p>
                        </div>
                      )}
                      
                      {milestone.dependency_complexity && (
                        <div className="mb-2">
                          <h5 className="font-medium text-gray-900 dark:text-white mb-1">Dependency Complexity:</h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{milestone.dependency_complexity}</p>
                        </div>
                      )}
                      
                      {milestone.future_implementation_notes && (
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white mb-1">Future Implementation Notes:</h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{milestone.future_implementation_notes}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </section>
        )}

        {/* Legacy Implementation Milestones - Backward Compatibility */}
        {doc.milestones_sketch && doc.milestones_sketch !== 'N/A' && !doc.implementation_strategy && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Implementation Milestones</h2>
            <div className="bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
              <div className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">{doc.milestones_sketch}</div>
            </div>
          </section>
        )}

        {/* Success Metrics */}
        {doc.success_metrics && doc.success_metrics.length > 0 && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Success Metrics</h2>
            <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <ul className="space-y-2">
                {doc.success_metrics.map((metric: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 mr-2 mt-1">📊</span>
                    <span className="text-gray-700 dark:text-gray-300">{metric}</span>
                  </li>
                ))}
              </ul>
            </div>
          </section>
        )}

        {/* Referenced Decisions */}
        {doc.referenced_decisions && doc.referenced_decisions.length > 0 && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Referenced Decisions</h2>
            <div className="bg-indigo-50 dark:bg-indigo-900/30 border border-indigo-200 dark:border-indigo-800 rounded-lg p-4">
              <div className="space-y-3">
                {doc.referenced_decisions.map((decision: any, index: number) => (
                  <div key={index} className="border-l-4 border-indigo-500 pl-4">
                    <h4 className="font-medium text-indigo-900 dark:text-indigo-100">
                      {decisions[decision.id] ? (
                        <button
                          onClick={() => handleDecisionClick(decision.id)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline font-medium"
                        >
                          {decisions[decision.id].metadata?.title || decision.id}
                        </button>
                      ) : (
                        <DecisionLinkedText 
                          text={decision.id}
                          decisions={decisions}
                          onDecisionClick={handleDecisionClick}
                          className="text-indigo-900 dark:text-indigo-100"
                        />
                      )}
                    </h4>
                    <p className="text-sm text-indigo-700 dark:text-indigo-300">
                      <DecisionLinkedText 
                        text={decision.summary_of_relevance_in_this_design}
                        decisions={decisions}
                        onDecisionClick={handleDecisionClick}
                        className="text-indigo-700 dark:text-indigo-300"
                      />
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </section>
        )}
      </div>
    );
  };

  useEffect(() => {
    if (implementationPlan && !rolloutStrategy) {
      setActiveTab('plan');
    }
  }, [implementationPlan]);

  useEffect(() => {
    if (rolloutStrategy) {
      setActiveTab('rollout');
    }
  }, [rolloutStrategy]);

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-amber-500 mx-auto mb-6"></div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Generating Your Design Document
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            AI is synthesizing your decisions into a comprehensive design document...
          </p>
          
          {/* Generation progress indicators */}
          <div className="max-w-md mx-auto space-y-3">
            <div className="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Analyzing {completedDecisions} technical decisions</span>
            </div>
            <div className="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Gathering relevant architectural context</span>
            </div>
            <div className="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
              <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
              <span>Generating comprehensive design document</span>
            </div>
            <div className="flex items-center space-x-3 text-sm text-gray-400">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <span>Finalizing document structure</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!generatedDoc) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="text-center py-12">
          <div className="text-6xl mb-6">📄</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Ready to Generate
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            All decisions have been made. Ready to generate your design document.
          </p>
          
          <button
            onClick={onBack}
            className="px-6 py-3 bg-amber-500 hover:bg-amber-600 text-white rounded-lg font-medium"
          >
            ← Back to Review Decisions
          </button>
        </div>
      </div>
    );
  }

  const documentMarkdown = formatDesignDocAsMarkdown(generatedDoc);

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        {/* Header with Action Buttons */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <DocumentTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white force-dark-text">
              🎉 Design Document Generated!
            </h2>
          </div>
          
          {/* Action Buttons */}
          <div className="flex items-center space-x-3">
            <ShareButton 
              dbSessionId={dbSessionId} 
              variant="small"
              title="Share this design document with your team"
            />
            {implementationPlan ? (
              // Implementation Plan Actions
              <>
                <button
                  onClick={handlePrint}
                  className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg flex items-center space-x-2 transition-colors"
                  title="Print Implementation Plan"
                >
                  <PrinterIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={handleSave}
                  className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg flex items-center space-x-2 transition-colors"
                  title="Save Implementation Plan"
                >
                  <DocumentArrowDownIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={handleImplementationPlanEditToggle}
                  className={`px-4 py-2 ${isImplementationPlanEditMode ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} text-white rounded-lg flex items-center space-x-2 transition-colors`}
                >
                  <PencilIcon className="h-4 w-4" />
                  <span>{isImplementationPlanEditMode ? 'Save Plan' : 'Edit Plan'}</span>
                </button>
                
                {onAddImplementationPlanToGitHub && !isEditMode && (
                  <button
                    onClick={handleAddPlanToGitHub}
                    disabled={isAddingPlanToGitHub || !isGitHubAvailable()}
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    {isAddingPlanToGitHub ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                      </svg>
                    )}
                    <span>Add to GitHub</span>
                  </button>
                )}

                {onGenerateRolloutStrategy && !isEditMode && (
                  <button
                    onClick={handleGenerateRolloutStrategy}
                    disabled={isGeneratingStrategy}
                    className="px-4 py-2 bg-teal-600 hover:bg-teal-700 disabled:bg-teal-400 text-white rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    {isGeneratingStrategy ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <RocketLaunchIcon className="h-4 w-4" />
                    )}
                    <span>Rollout Strategy</span>
                  </button>
                )}
              </>
            ) : (
              // Design Document Actions
              <>
                <button
                  onClick={handlePrint}
                  className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg flex items-center space-x-2 transition-colors"
                  title="Print Design Document"
                >
                  <PrinterIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={handleSave}
                  className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg flex items-center space-x-2 transition-colors"
                  title="Save Design Document"
                >
                  <DocumentArrowDownIcon className="h-4 w-4" />
                </button>

                <button
                  onClick={handleEditToggle}
                  className={`px-4 py-2 ${isEditMode ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} text-white rounded-lg flex items-center space-x-2 transition-colors`}
                >
                  <PencilIcon className="h-4 w-4" />
                  <span>{isEditMode ? 'Save Edit' : 'Edit Document'}</span>
                </button>

                {onAddToGitHubIssue && !isEditMode && (
                  <button
                    onClick={handleAddToGitHub}
                    disabled={isAddingToGitHub || !isGitHubAvailable()}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    {isAddingToGitHub ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                      </svg>
                    )}
                    <span>Add to GitHub</span>
                  </button>
                )}

                {onGenerateImplementationPlan && !isEditMode && (
                  <button
                    onClick={handleGenerateImplementationPlan}
                    disabled={isGeneratingPlan}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white dark:text-white rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    {isGeneratingPlan ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white dark:border-white"></div>
                    ) : (
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                      </svg>
                    )}
                    <span>Coding Agent Instructions</span>
                  </button>
                )}

                {onGenerateRolloutStrategy && !isEditMode && (
                  <button
                    onClick={handleGenerateRolloutStrategy}
                    disabled={isGeneratingStrategy}
                    className="px-4 py-2 bg-teal-600 hover:bg-teal-700 disabled:bg-teal-400 text-white rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    {isGeneratingStrategy ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <RocketLaunchIcon className="h-4 w-4" />
                    )}
                    <span>Rollout Strategy</span>
                  </button>
                )}
              </>
            )}
          </div>
        </div>

        {/* Success/Error Messages */}
        {githubResult && (
          <div className={`p-6 border-b border-gray-200 dark:border-gray-700 ${
            githubResult.success 
              ? 'bg-green-50 dark:bg-green-900/30' 
              : 'bg-red-50 dark:bg-red-900/30'
          }`} ref={successMessageRef}>
            <div className="flex items-center">
              {githubResult.success ? (
                <svg className="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
              )}
              <div>
                <h3 className={`font-medium ${
                  githubResult.success 
                    ? 'text-green-800 dark:text-green-200' 
                    : 'text-red-800 dark:text-red-200'
                }`}>
                  {githubResult.success ? 'Successfully Added to GitHub!' : 'Failed to Add to GitHub'}
                </h3>
                {githubResult.success ? (
                  <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                    <p>Design document has been added as a comment to the GitHub issue.</p>
                    {githubResult.commentUrl && (
                      <p className="mt-1">
                        <a href={githubResult.commentUrl} target="_blank" rel="noopener noreferrer" className="font-medium underline hover:text-green-600 dark:hover:text-green-200">
                          View comment on GitHub →
                        </a>
                      </p>
                    )}
                    {githubResult.labelsAdded && githubResult.labelsAdded.length > 0 && (
                      <p className="mt-1">
                        Labels added: {githubResult.labelsAdded.map((label: string) => (
                          <span key={label} className="inline-block bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full mr-1">
                            {label}
                          </span>
                        ))}
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="mt-2 text-sm text-red-700 dark:text-red-300">
                    {githubResult.error || 'An unknown error occurred'}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {planGithubResult && (
          <div className={`p-6 border-b border-gray-200 dark:border-gray-700 ${
            planGithubResult.success 
              ? 'bg-green-50 dark:bg-green-900/30' 
              : 'bg-red-50 dark:bg-red-900/30'
          }`} ref={successMessageRef}>
            <div className="flex items-center">
              {planGithubResult.success ? (
                <svg className="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
              )}
              <div>
                <h3 className={`font-medium ${
                  planGithubResult.success 
                    ? 'text-green-800 dark:text-green-200' 
                    : 'text-red-800 dark:text-red-200'
                }`}>
                  {planGithubResult.success ? 'Successfully Added Implementation Plan to GitHub!' : 'Failed to Add Implementation Plan to GitHub'}
                </h3>
                {planGithubResult.success ? (
                  <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                    <p>Implementation plan has been added as a comment to the GitHub issue.</p>
                    {planGithubResult.commentUrl && (
                      <p className="mt-1">
                        <a href={planGithubResult.commentUrl} target="_blank" rel="noopener noreferrer" className="font-medium underline hover:text-green-600 dark:hover:text-green-200">
                          View comment on GitHub →
                        </a>
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="mt-2 text-sm text-red-700 dark:text-red-300">
                    {planGithubResult.error || 'An unknown error occurred'}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('doc')}
              className={`${
                activeTab === 'doc'
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'
              } flex items-center whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors`}
              aria-current={activeTab === 'doc' ? 'page' : undefined}
            >
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              Design Document
            </button>
            
            {implementationPlan && (
              <button
                onClick={() => setActiveTab('plan')}
                className={`${
                  activeTab === 'plan'
                    ? 'border-green-500 text-green-600 dark:text-green-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'
                } flex items-center whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors`}
                aria-current={activeTab === 'plan' ? 'page' : undefined}
              >
                <ClipboardDocumentCheckIcon className="h-5 w-5 mr-2" />
                Coding Agent Instructions
              </button>
            )}
            
            {rolloutStrategy && (
              <button
                onClick={() => setActiveTab('rollout')}
                className={`${
                  activeTab === 'rollout'
                    ? 'border-teal-500 text-teal-600 dark:text-teal-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'
                } flex items-center whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors`}
                aria-current={activeTab === 'rollout' ? 'page' : undefined}
              >
                <RocketLaunchIcon className="h-5 w-5 mr-2" />
                Rollout Strategy
              </button>
            )}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Design Document Tab */}
          {activeTab === 'doc' && (
            isEditMode ? (
              // Show Edit Mode
              <div className="space-y-4">
                <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                    ✏️ Editing Mode
                  </h3>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    You are now editing the raw markdown content. Click "Save Edit" to return to the formatted view.
                  </p>
                </div>
                
                <div className="relative">
                  <textarea
                    value={editableMarkdown}
                    onChange={(e) => setEditableMarkdown(e.target.value)}
                    className="w-full h-[500px] p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Edit your document in markdown format..."
                  />
                  <div className="absolute bottom-4 right-4 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 px-2 py-1 rounded">
                    {editableMarkdown.split('\n').length} lines
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setIsEditMode(false)}
                    className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSaveEdit}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                  >
                    Save Changes
                  </button>
                </div>
              </div>
            ) : (
              // Show Formatted Design Document
              <div className="max-w-none">
                {renderFormattedDesignDoc(generatedDoc)}
              </div>
            )
          )}

          {/* Implementation Plan Tab */}
          {activeTab === 'plan' && implementationPlan && (
            isImplementationPlanEditMode ? (
              <div className="space-y-4">
                <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                    ✏️ Editing Implementation Plan
                  </h3>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    You are now editing the raw markdown content of the implementation plan.
                  </p>
                </div>
                
                <div className="relative">
                  <textarea
                    value={editableImplementationPlanMarkdown}
                    onChange={(e) => setEditableImplementationPlanMarkdown(e.target.value)}
                    className="w-full h-[500px] p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Edit your implementation plan in markdown format..."
                  />
                  <div className="absolute bottom-4 right-4 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 px-2 py-1 rounded">
                    {editableImplementationPlanMarkdown.split('\n').length} lines
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setIsImplementationPlanEditMode(false)}
                    className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                                     <button
                     onClick={handleImplementationPlanEditToggle}
                     className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                   >
                     Save Changes
                   </button>
                </div>
              </div>
            ) : (
              <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-6 space-y-6">
                <div className="flex items-center space-x-3">
                  <ClipboardDocumentCheckIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  <div>
                    <h3 className="text-xl font-semibold text-blue-900 dark:text-blue-100">Implementation Plan</h3>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">Detailed step-by-step coding instructions</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Summary */}
                  {implementationPlan.summary && (
                    <div>
                      <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Summary:</h4>
                      <div className="bg-white dark:bg-gray-800 rounded p-3 text-sm text-gray-700 dark:text-gray-300">
                        {implementationPlan.summary}
                      </div>
                    </div>
                  )}

                  {/* Enhanced Milestones */}
                  {implementationPlan.enhanced_milestones && implementationPlan.enhanced_milestones.length > 0 && (
                    <div>
                      <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-3">
                        Implementation Milestones ({implementationPlan.enhanced_milestones.length}):
                      </h4>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                        The implementation is broken down into {implementationPlan.enhanced_milestones.length} sequential milestones, each independently testable and deployable.
                      </p>
                      <div className="space-y-4">
                        {implementationPlan.enhanced_milestones.map((milestone: any, index: number) => (
                          <div key={index} className="border border-blue-200 dark:border-blue-700 rounded p-4 bg-white dark:bg-gray-800">
                            <div className="flex items-center justify-between mb-3">
                              <h5 className="font-medium text-gray-900 dark:text-white">
                                Milestone {index + 1}: {milestone.title || milestone.milestone_id}
                              </h5>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                milestone.priority === 'P0' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                milestone.priority === 'P1' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                              }`}>
                                {milestone.priority}
                              </span>
                            </div>
                            
                            {/* Milestone Branch */}
                            <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                              <strong>Milestone Branch:</strong> <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded text-gray-900 dark:text-white">{milestone.git_must_use_milestone_branch_name}</code>
                            </div>
                            
                            {/* Description */}
                            {milestone.description && (
                              <div className="text-sm text-gray-700 dark:text-gray-300 mb-3">
                                {milestone.description}
                              </div>
                            )}
                            
                            {/* Key Tasks and Deliverables */}
                            {milestone.key_tasks_and_deliverables && milestone.key_tasks_and_deliverables.length > 0 && (
                              <div className="mb-3">
                                <strong className="text-sm text-gray-900 dark:text-white">Key Tasks and Deliverables:</strong>
                                <ul className="list-disc list-inside mt-1 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                                  {milestone.key_tasks_and_deliverables.map((task: any, taskIndex: number) => (
                                    <li key={taskIndex}>{typeof task === 'string' ? task : task.task || task.description || 'Task not specified'}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                            
                            {/* Scope Validation */}
                            {milestone.scope_validation && milestone.scope_validation.planned_deliverables && milestone.scope_validation.planned_deliverables.length > 0 && (
                              <div className="mb-3">
                                <strong className="text-sm text-gray-900 dark:text-white">Scope Validation - Planned Deliverables:</strong>
                                <ul className="list-disc list-inside mt-1 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                                  {milestone.scope_validation.planned_deliverables.map((deliverable: string, delIndex: number) => (
                                    <li key={delIndex}>{deliverable}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                            
                            {/* Verification Criteria */}
                            {milestone.verification_criteria && milestone.verification_criteria.length > 0 && (
                              <div className="mb-3">
                                <strong className="text-sm text-gray-900 dark:text-white">Verification Criteria:</strong>
                                <ul className="list-disc list-inside mt-1 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                                  {milestone.verification_criteria.map((criteria: string, critIndex: number) => (
                                    <li key={critIndex}>{criteria}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                            
                            {/* Developer Guidance */}
                            {milestone.applicable_developer_guidance_and_best_practices && milestone.applicable_developer_guidance_and_best_practices.length > 0 && (
                              <div className="mb-3">
                                <strong className="text-sm text-gray-900 dark:text-white">Applicable Developer Guidance:</strong>
                                <div className="mt-2 space-y-2">
                                  {milestone.applicable_developer_guidance_and_best_practices.map((guidance: any, guidanceIndex: number) => (
                                    <div key={guidanceIndex} className="bg-gray-50 dark:bg-gray-700 rounded p-2">
                                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                                        {guidance.retrieved_decision_title} ({guidance.retrieved_decision_id})
                                      </div>
                                      {guidance.guidance_summary && (
                                        <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                          {guidance.guidance_summary}
                                        </div>
                                      )}
                                      {guidance.related_files && guidance.related_files.length > 0 && (
                                        <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                          Related files: {guidance.related_files.join(', ')}
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Data Model Summary */}
                  {implementationPlan.data_model_summary && implementationPlan.data_model_summary !== 'N/A' && (
                    <div>
                      <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Data Model Changes:</h4>
                      <div className="bg-gray-50 dark:bg-gray-700 rounded p-3 text-sm text-gray-700 dark:text-gray-300">
                        {implementationPlan.data_model_summary}
                      </div>
                    </div>
                  )}

                  {/* Referenced Decisions */}
                  {implementationPlan.referenced_decisions && implementationPlan.referenced_decisions.length > 0 && (
                    <div>
                      <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Referenced Architectural Decisions:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 dark:text-gray-300">
                        {implementationPlan.referenced_decisions.map((decision: string, decIndex: number) => (
                          <li key={decIndex}>
                            {decisions[decision] ? (
                              <button
                                onClick={() => handleDecisionClick(decision)}
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline font-medium"
                              >
                                {decisions[decision].metadata?.title || decision}
                              </button>
                            ) : (
                              <DecisionLinkedText 
                                text={decision}
                                decisions={decisions}
                                onDecisionClick={handleDecisionClick}
                                className="text-gray-700 dark:text-gray-300"
                              />
                            )}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Success Metrics */}
                  {implementationPlan.overall_success_metrics && implementationPlan.overall_success_metrics.length > 0 && (
                    <div>
                      <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Success Metrics:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 dark:text-gray-300">
                        {implementationPlan.overall_success_metrics.map((metric: string, metricIndex: number) => (
                          <li key={metricIndex}>{metric}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Implementation Notes */}
                  {implementationPlan.full_design_doc_analyzed && (
                    <div>
                      <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Implementation Notes:</h4>
                      <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded p-3">
                        <div className="text-sm text-green-800 dark:text-green-200 space-y-1">
                          <div>✅ Complete design document analyzed for comprehensive planning</div>
                          {implementationPlan.finalized_design_doc_content_used && <div>✅ Finalized design document content incorporated</div>}
                          <div>✅ Knowledge base integration for developer guidance</div>
                          <div>✅ Branch naming conventions enforced for consistency</div>
                          <div>✅ Quality control checkpoints built into each milestone</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )
          )}

          {/* Rollout Strategy Tab */}
          {activeTab === 'rollout' && rolloutStrategy && (
            <RolloutStrategyViewer strategy={rolloutStrategy} />
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-gray-700 mt-auto">
          <button
            onClick={onBack}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            ← Back to Decisions
          </button>
        </div>
      </div>
      
      {/* Decision Detail Modal */}
      <DecisionDetailModal
        decision={selectedDecisionForModal ? decisions[selectedDecisionForModal] : null}
        isOpen={!!selectedDecisionForModal}
        onClose={() => setSelectedDecisionForModal(null)}
      />
    </div>
  );
}

interface TabPanelProps {
  children: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`rollout-tabpanel-${index}`}
      aria-labelledby={`rollout-tab-${index}`}
    >
      {value === index && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-b-lg">
          {children}
        </div>
      )}
    </div>
  );
}

function RolloutStrategyViewer({ strategy }: { strategy: any }) {
  const [activeTab, setActiveTab] = useState(0);

  const riskLevelColor = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'high':
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    }
  };

  const alertLevelColor = (level: string) => {
    switch (level?.toUpperCase()) {
      case 'P0':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'P1':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'P2':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    }
  };
  
  const tabs = [
    { name: 'Summary', icon: ClipboardDocumentCheckIcon },
    { name: 'Phases', icon: RocketLaunchIcon },
    { name: 'Monitoring', icon: ChartBarIcon },
    { name: 'Contingency', icon: ArrowUturnLeftIcon },
  ];

  return (
    <div className="mt-8">
      <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
        <RocketLaunchIcon className="h-6 w-6 mr-2 text-teal-500" />
        Action-Oriented Rollout Strategy
      </h3>

      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-6" aria-label="Tabs">
          {tabs.map((tab, index) => (
            <button
              key={tab.name}
              onClick={() => setActiveTab(index)}
              className={`${
                activeTab === index
                  ? 'border-teal-500 text-teal-600 dark:text-teal-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'
              } flex items-center whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors`}
              aria-current={activeTab === index ? 'page' : undefined}
            >
              <tab.icon className="h-5 w-5 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      <TabPanel value={activeTab} index={0}>
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Executive Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className={`p-3 rounded-lg ${riskLevelColor(strategy.executive_summary?.risk_level)}`}>
                <div className="text-xs font-medium uppercase opacity-75">Risk Level</div>
                <div className="text-lg font-bold">{strategy.executive_summary?.risk_level}</div>
              </div>
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <div className="text-xs font-medium uppercase text-gray-600 dark:text-gray-400">Decision</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">{strategy.executive_summary?.decision}</div>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-3">{strategy.executive_summary?.summary}</p>
          </div>

          {strategy.executive_summary?.key_next_actions?.length > 0 && (
            <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Key Next Actions</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 dark:text-gray-300">
                {strategy.executive_summary.key_next_actions.map((action: string, i: number) => <li key={i}>{action}</li>)}
              </ul>
            </div>
          )}

          {strategy.pre_flight_checklist?.length > 0 && (
            <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Pre-flight Checklist</h4>
              <ul className="space-y-2">
                {strategy.pre_flight_checklist.map((item: any, i: number) => (
                  <li key={i} className="flex items-center text-sm">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="flex-1 text-gray-700 dark:text-gray-300">{item.item}</span>
                    <span className="text-xs bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">{item.owner}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </TabPanel>

      <TabPanel value={activeTab} index={1}>
        <div className="space-y-4">
          {strategy.rollout_phases?.map((phase: any, i: number) => (
            <div key={i} className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow border-l-4 border-teal-500">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-semibold text-gray-900 dark:text-white">{phase.phase_name}</h4>
                <span className="text-sm text-gray-500 dark:text-gray-400">{phase.duration}</span>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                <strong>Target:</strong> {phase.target_population}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 className="text-xs font-semibold text-gray-500 dark:text-gray-400 mb-1">Goals</h5>
                  <ul className="list-disc list-inside text-sm text-gray-700 dark:text-gray-300">
                    {phase.goals?.map((goal: string, j: number) => <li key={j}>{goal}</li>)}
                  </ul>
                </div>
                <div>
                  <h5 className="text-xs font-semibold text-green-600 dark:text-green-400 mb-1">Graduation Criteria</h5>
                  <ul className="list-disc list-inside text-sm text-green-700 dark:text-green-300">
                    {phase.graduation_criteria?.map((criteria: string, j: number) => <li key={j}>{criteria}</li>)}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </TabPanel>

      <TabPanel value={activeTab} index={2}>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white dark:bg-gray-700 rounded-lg shadow">
            <thead className="bg-gray-100 dark:bg-gray-800">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Metric</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Type</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Threshold</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Alert</th>
                <th className="px-4 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Notes</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {strategy.monitoring?.map((metric: any, i: number) => (
                <tr key={i}>
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{metric.metric_name}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{metric.type}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{metric.threshold}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${alertLevelColor(metric.alert_level)}`}>
                      {metric.alert_level}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">{metric.notes}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </TabPanel>

      <TabPanel value={activeTab} index={3}>
        <div className="space-y-4">
          <div className="bg-red-50 dark:bg-red-900/30 p-4 rounded-lg border border-red-200 dark:border-red-700">
            <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">Key Risks</h4>
            <ul className="space-y-2">
              {strategy.risk_assessment?.key_risks?.map((risk: any, i: number) => (
                <li key={i}>
                  <strong className="text-sm text-red-700 dark:text-red-300">{risk.risk}</strong>
                  <p className="text-xs text-red-600 dark:text-red-400"><strong>Impact:</strong> {risk.impact}</p>
                  <p className="text-xs text-red-600 dark:text-red-400"><strong>Mitigation:</strong> {risk.mitigation}</p>
                </li>
              ))}
            </ul>
          </div>
          <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Rollback Plan</h4>
            <div className="text-sm text-gray-600 dark:text-gray-300 mb-2">
              <strong>Complexity:</strong> {strategy.contingency_plan?.rollback_complexity}
            </div>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-700 dark:text-gray-300 font-mono">
              {strategy.contingency_plan?.rollback_steps?.map((step: string, i: number) => (
                <li key={i}>{step}</li>
              ))}
            </ol>
          </div>
          <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Communication Plan</h4>
            <p className="text-sm text-gray-700 dark:text-gray-300">{strategy.contingency_plan?.communication_plan}</p>
          </div>
        </div>
      </TabPanel>
    </div>
  );
} 