import React, { useState, useMemo } from 'react';
import type { 
  WizardState, 
  DecisionPoint, 
  DecisionOption,
  DecisionProcessingResult,
  UseDesignDocActionsProps
} from '../../types/design-doc-wizard';
import { DecisionLinkedText, DecisionReference } from '../../utils/decisionUtils';
import DecisionDetailModal from '../DecisionDetailModal';
import { useDecisions, useProjectContextWithDebug } from '../../hooks/useDecisions';

interface StreamlinedDecisionMakingStepProps {
  wizardState: WizardState;
  actions: ReturnType<typeof import('../../hooks/useDesignDocActions').useDesignDocActions>;
  repositorySlug: string;
  installationId: string | number;
  onBack: () => void;
  onContinue: () => void;
  isLoading: boolean;
}

interface DecisionReviewModalProps {
  decision: DecisionPoint;
  result: DecisionProcessingResult;
  onSelect: (decisionId: string, optionId: string, rationale: string) => void;
  onClose: () => void;
  decisions: Record<string, DecisionReference>;
  onDecisionClick: (decisionId: string) => void;
}

interface DecisionReviewInlineProps {
  decision: DecisionPoint;
  result: DecisionProcessingResult;
  onSelect: (decisionId: string, optionId: string, rationale: string) => void;
  decisions: Record<string, DecisionReference>;
  onDecisionClick: (decisionId: string) => void;
}

function DecisionReviewModal({ decision, result, onSelect, onClose, decisions, onDecisionClick }: DecisionReviewModalProps) {
  const [selectedOption, setSelectedOption] = useState<string>(decision.selectedOption || '');
  const [rationale, setRationale] = useState(decision.rationale || '');
  
  const recommendedOption = decision.options?.find(opt => 
    opt.id === decision.recommended_option_id
  );

  const isCompletedDecision = !!decision.selectedOption;

  const handleSave = () => {
    if (selectedOption) {
      const finalRationale = selectedOption === decision.recommended_option_id
        ? (Array.isArray(decision.recommendation_rationale) 
            ? decision.recommendation_rationale.join(' ') 
            : decision.recommendation_rationale || 'Selected recommended option')
        : rationale;
      
      if (selectedOption !== decision.recommended_option_id && !rationale.trim()) {
        return; // Don't save if non-recommended option selected but no rationale provided
      }
      
      onSelect(decision.id, selectedOption, finalRationale);
      onClose();
    }
  };

  const getRiskColor = (risk?: string) => {
    switch (risk?.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getComplexityColor = (complexity?: string) => {
    switch (complexity?.toLowerCase()) {
      case 'high': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300';
      case 'medium': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300';
      case 'low': return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/50 dark:text-cyan-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                {isCompletedDecision ? '✅ Review Completed Decision' : '🔍 Review High-Impact Decision'}
              </h2>
              <h3 className="text-lg text-gray-700 dark:text-gray-300">{decision.title}</h3>
              <p className="text-gray-600 dark:text-gray-400 mt-1">{decision.description}</p>
            </div>
            <button 
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          {/* Risk factors */}
          {!isCompletedDecision && result.classification.riskFactors.length > 0 && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                Risk factors to consider:
              </h4>
              <div className="flex flex-wrap gap-2">
                {result.classification.riskFactors.map((factor, index) => (
                  <span key={index} className="px-2 py-1 bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200 text-xs rounded">
                    {factor}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Current rationale for completed decisions */}
          {isCompletedDecision && decision.rationale && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                Current Decision Rationale:
              </h4>
              <div className="text-sm text-blue-700 dark:text-blue-300">
                {Array.isArray(decision.rationale) ? (
                  <ul className="list-disc list-inside space-y-1 pl-1">
                    {decision.rationale.map((point, index) => (
                      <li key={index}>
                        <DecisionLinkedText 
                          text={point}
                          decisions={decisions}
                          onDecisionClick={onDecisionClick}
                          className="dark:text-blue-300"
                        />
                      </li>
                    ))}
                  </ul>
                ) : (
                  <ul className="list-disc list-inside space-y-1 pl-1">
                    {decision.rationale.split('.').filter(point => point.trim()).map((point, index) => (
                      <li key={index}>
                        <DecisionLinkedText 
                          text={point.trim() + '.'}
                          decisions={decisions}
                          onDecisionClick={onDecisionClick}
                          className="dark:text-blue-300"
                        />
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          )}

          {/* AI Recommendation Section */}
          {recommendedOption && (
            <div className="mb-6 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 border-2 border-emerald-200 dark:border-emerald-700 rounded-xl p-6 shadow-sm">
              <div className="flex items-start space-x-3 mb-4">
                <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h5 className="text-lg font-bold text-emerald-900 dark:text-emerald-100">
                    AI Recommendation
                  </h5>
                  <p className="text-emerald-700 dark:text-emerald-300 font-medium">
                    {recommendedOption.name}
                  </p>
                </div>
              </div>
              
              {/* Structured Recommendation Rationale */}
              {decision.recommendation_rationale && (
                <div className="mb-5">
                  <h6 className="text-sm font-semibold text-emerald-800 dark:text-emerald-200 mb-3 flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    Why This Works Best
                  </h6>
                  <div className="space-y-2">
                    {Array.isArray(decision.recommendation_rationale) ? (
                      decision.recommendation_rationale.map((rationale, i) => (
                        <div key={i} className="flex items-start space-x-3 p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                          <div className="flex-shrink-0 w-2 h-2 bg-emerald-500 rounded-full mt-2"></div>
                          <DecisionLinkedText 
                            text={rationale}
                            decisions={decisions}
                            onDecisionClick={onDecisionClick}
                            className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                          />
                        </div>
                      ))
                    ) : (
                      <div className="space-y-2">
                        {decision.recommendation_rationale.split('\n').filter(line => line.trim()).map((line, i) => (
                          <div key={i} className="flex items-start space-x-3 p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                            <div className="flex-shrink-0 w-2 h-2 bg-emerald-500 rounded-full mt-2"></div>
                                                         <DecisionLinkedText 
                               text={line.trim()}
                               decisions={decisions}
                               onDecisionClick={onDecisionClick}
                               className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                             />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Evaluation Dimensions Summary */}
              <div className="grid grid-cols-2 gap-4 mb-5">
                <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg border border-emerald-100 dark:border-emerald-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className={`w-3 h-3 rounded-full ${getRiskColor(recommendedOption.riskLevel).includes('red') ? 'bg-red-400' : getRiskColor(recommendedOption.riskLevel).includes('yellow') ? 'bg-yellow-400' : 'bg-green-400'}`}></div>
                    <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Risk Level</div>
                  </div>
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {recommendedOption.riskLevel?.toUpperCase() || 'N/A'}
                  </div>
                </div>
                <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg border border-emerald-100 dark:border-emerald-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className={`w-3 h-3 rounded-full ${getComplexityColor(recommendedOption.complexity).includes('purple') ? 'bg-purple-400' : getComplexityColor(recommendedOption.complexity).includes('blue') ? 'bg-blue-400' : 'bg-cyan-400'}`}></div>
                    <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Complexity</div>
                  </div>
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {recommendedOption.complexity?.toUpperCase() || 'N/A'}
                  </div>
                </div>
                <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg border border-emerald-100 dark:border-emerald-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className={`w-3 h-3 rounded-full ${getComplexityColor(recommendedOption.maintenanceBurden).includes('purple') ? 'bg-purple-400' : getComplexityColor(recommendedOption.maintenanceBurden).includes('blue') ? 'bg-blue-400' : 'bg-cyan-400'}`}></div>
                    <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Maintenance</div>
                  </div>
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {recommendedOption.maintenanceBurden?.toUpperCase() || 'N/A'}
                  </div>
                </div>
                <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg border border-emerald-100 dark:border-emerald-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className={`w-3 h-3 rounded-full ${recommendedOption.architecturalAlignment === 'high' ? 'bg-green-400' : recommendedOption.architecturalAlignment === 'medium' ? 'bg-yellow-400' : 'bg-red-400'}`}></div>
                    <div className="text-xs font-medium text-gray-600 dark:text-gray-400">Alignment</div>
                  </div>
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {recommendedOption.architecturalAlignment?.toUpperCase() || 'N/A'}
                  </div>
                </div>
              </div>

              {/* Cons with Mitigation Analysis */}
              {recommendedOption.cons && recommendedOption.cons.length > 0 && (
                <div className="mb-5">
                  <h6 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                    <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Trade-offs & Mitigations
                  </h6>
                  <div className="space-y-2">
                    {recommendedOption.cons.map((con, i) => (
                      <div key={i} className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg border-l-3 border-gray-300 dark:border-gray-600">
                        <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">
                          {con}
                        </p>
                        {recommendedOption.mitigations && recommendedOption.mitigations[i] && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                            {recommendedOption.mitigations[i]}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Key Strengths */}
              {recommendedOption.pros && recommendedOption.pros.length > 0 && (
                <div>
                  <h6 className="text-sm font-semibold text-emerald-800 dark:text-emerald-200 mb-3 flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Key Strengths
                  </h6>
                  <div className="grid grid-cols-1 gap-2">
                    {recommendedOption.pros.slice(0, 3).map((pro, i) => (
                      <div key={i} className="flex items-center space-x-3 p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                        <div className="flex-shrink-0 w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                          <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                        <p className="text-sm text-gray-700 dark:text-gray-300 font-medium">{pro}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* All Options with Detailed Views */}
          <div className="mb-6">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">All Available Options:</h4>
            
            <div className="space-y-4">
              {decision.options?.slice().sort((a, b) => {
                const isARecommended = recommendedOption?.id === a.id;
                const isBRecommended = recommendedOption?.id === b.id;
                if (isARecommended) return -1;
                if (isBRecommended) return 1;
                return 0;
              }).map((option) => {
                const isRecommended = option.id === decision.recommended_option_id;
                const isSelected = selectedOption === option.id;
                
                return (
                  <div key={option.id} className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                    isSelected 
                      ? isRecommended ? 'border-green-500 bg-green-50 dark:bg-green-900/20' : 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`} onClick={() => setSelectedOption(option.id)}>
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center flex-wrap gap-2 mb-2">
                          <input
                            type="radio"
                            checked={isSelected}
                            onChange={() => setSelectedOption(option.id)}
                            className="text-blue-600 mr-2"
                          />
                          <h5 className="font-semibold text-gray-800 dark:text-gray-200">{option.name}</h5>
                          {isRecommended && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              AI Recommended
                            </span>
                          )}
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getComplexityColor(option.complexity)} border`}>
                            Complexity: {option.complexity || 'N/A'}
                          </span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRiskColor(option.riskLevel)} border`}>
                            Risk: {option.riskLevel || 'N/A'}
                          </span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getComplexityColor(option.maintenanceBurden)} border`}>
                            Maintenance: {option.maintenanceBurden || 'N/A'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{option.description}</p>
                      </div>
                    </div>

                    {/* Detailed Analysis */}
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      {option.alignmentScore !== undefined && (
                        <div className="mb-4">
                          <label className="text-xs font-medium text-gray-500 dark:text-gray-400">Architectural Alignment</label>
                          <div className="flex items-center space-x-2 mt-1">
                            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
                              <div className={`h-1.5 rounded-full ${option.alignmentScore >= 0.7 ? 'bg-green-500' : option.alignmentScore >= 0.4 ? 'bg-yellow-500' : 'bg-red-500'}`} style={{ width: `${option.alignmentScore * 100}%` }}></div>
                            </div>
                            <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">{Math.round(option.alignmentScore * 100)}%</span>
                          </div>
                          {option.alignmentReasoning && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 italic">
                              "<DecisionLinkedText 
                                text={option.alignmentReasoning}
                                decisions={decisions}
                                onDecisionClick={onDecisionClick}
                                className=""
                              />"
                            </p>
                          )}
                        </div>
                      )}
                                    {option.alignmentJustification && (
                <div className="mb-3">
                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400">Alignment Analysis</label>
                  <DecisionLinkedText 
                    text={option.alignmentJustification}
                    decisions={decisions}
                    onDecisionClick={onDecisionClick}
                    className="text-xs text-gray-600 dark:text-gray-400 mt-1 block"
                  />
                </div>
              )}
                      {option.debuggingComplexity && (
                        <div className="mb-3">
                          <label className="text-xs font-medium text-gray-500 dark:text-gray-400">Debugging Complexity</label>
                          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            <DecisionLinkedText 
                              text={option.debuggingComplexity}
                              decisions={decisions}
                              onDecisionClick={onDecisionClick}
                              className=""
                            />
                          </p>
                        </div>
                      )}
                      
                      {/* Comprehensive Pros and Cons */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {option.pros.length > 0 && (
                          <div>
                            <h6 className="text-xs font-medium text-green-700 dark:text-green-400 mb-2">✓ Pros:</h6>
                            <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                              {option.pros.map((pro, i) => (
                                <li key={i}>
                                  <DecisionLinkedText 
                                    text={pro}
                                    decisions={decisions}
                                    onDecisionClick={onDecisionClick}
                                    className=""
                                  />
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {option.cons.length > 0 && (
                          <div>
                            <h6 className="text-xs font-medium text-red-700 dark:text-red-400 mb-2">✗ Cons:</h6>
                            <div className="space-y-2">
                              {option.cons.map((con, i) => (
                                <div key={i} className="text-sm">
                                  <div className="flex items-start">
                                    <span className="text-red-500 mr-1 mt-0.5">•</span>
                                    <span className="text-gray-600 dark:text-gray-400">
                                      <DecisionLinkedText 
                                        text={con}
                                        decisions={decisions}
                                        onDecisionClick={onDecisionClick}
                                        className=""
                                      />
                                    </span>
                                  </div>
                                  {option.mitigations && option.mitigations[i] && (
                                    <div className="ml-3 mt-1 text-xs text-blue-600 dark:text-blue-400 italic">
                                      🛡️ <DecisionLinkedText 
                                        text={option.mitigations[i]}
                                        decisions={decisions}
                                        onDecisionClick={onDecisionClick}
                                        className=""
                                      />
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Rationale */}
          {selectedOption && selectedOption !== decision.recommended_option_id && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Decision Rationale <span className="text-red-500">*</span>
              </label>
              <textarea
                value={rationale}
                onChange={(e) => setRationale(e.target.value)}
                placeholder="Insert decision rationale here"
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-blue-500"
                rows={4}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Please explain why you chose this option instead of the recommended one.
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-between">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Cancel
            </button>
            
            <button
              onClick={handleSave}
              disabled={!selectedOption || (selectedOption !== decision.recommended_option_id && !rationale.trim())}
              className="px-6 py-2 bg-blue-600 text-white dark:text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {isCompletedDecision ? 'Update Decision' : 'Save Decision'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function DecisionReviewInline({ decision, result, onSelect, decisions, onDecisionClick }: DecisionReviewInlineProps) {
  const [selectedOption, setSelectedOption] = useState<string>(decision.selectedOption || '');
  const [rationale, setRationale] = useState(decision.rationale || '');
  const [showRecommendedDetails, setShowRecommendedDetails] = useState(false);
  
  const recommendedOption = decision.options?.find(opt => 
    opt.id === decision.recommended_option_id
  );

  const handleSave = () => {
    if (selectedOption) {
      const finalRationale = selectedOption === decision.recommended_option_id
        ? (Array.isArray(decision.recommendation_rationale) 
            ? decision.recommendation_rationale.join(' ') 
            : decision.recommendation_rationale || 'Selected recommended option')
        : rationale;
      
      if (selectedOption !== decision.recommended_option_id && !rationale.trim()) {
        return; // Don't save if non-recommended option selected but no rationale provided
      }
      
      onSelect(decision.id, selectedOption, finalRationale);
    }
  };

  const getRiskColor = (risk?: string) => {
    switch (risk?.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getComplexityColor = (complexity?: string) => {
    switch (complexity?.toLowerCase()) {
      case 'high': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300';
      case 'medium': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300';
      case 'low': return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/50 dark:text-cyan-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              🔍 {decision.title}
            </h4>
            <p className="text-gray-600 dark:text-gray-400">{decision.description}</p>
          </div>
        </div>

        {/* Impact score and risk factors */}
        <div className="flex items-center space-x-4 mb-4">
          <span className={`px-3 py-1 text-sm rounded-full border ${
            result.classification.impactScore >= 70 
              ? 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/50 dark:text-red-300'
              : 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300'
          }`}>
            Impact Score: {result.classification.impactScore}
          </span>
          <span className="text-sm text-gray-500">
            {result.classification.reasons.length} factor{result.classification.reasons.length !== 1 ? 's' : ''} identified
          </span>
        </div>

        {/* Risk factors */}
        {result.classification.riskFactors.length > 0 && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4 mb-4">
            <h5 className="font-semibold text-red-800 dark:text-red-200 mb-2">
              Risk factors to consider:
            </h5>
            <div className="flex flex-wrap gap-2">
              {result.classification.riskFactors.map((factor, index) => (
                <span key={index} className="px-2 py-1 bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200 text-xs rounded">
                  {factor}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Why it needs review */}
        <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          <span className="font-medium">Requires attention: </span>
          <DecisionLinkedText 
            text={result.classification.reasons.join(', ')}
            decisions={decisions}
            onDecisionClick={onDecisionClick}
            className=""
          />
        </div>
      </div>

      {/* AI Recommendation Section */}
      {recommendedOption && (
        <div className="mb-6 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 border-2 border-emerald-200 dark:border-emerald-700 rounded-xl p-6 shadow-sm">
          <div className="flex items-start space-x-3 mb-4">
            <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div>
              <h5 className="text-lg font-bold text-emerald-900 dark:text-emerald-100">
                AI Recommendation
              </h5>
              <p className="text-emerald-700 dark:text-emerald-300 font-medium">
                {recommendedOption.name}
              </p>
            </div>
          </div>
          
          {/* Structured Recommendation Rationale */}
          {decision.recommendation_rationale && (
            <div className="mb-5">
              <h6 className="text-sm font-semibold text-emerald-800 dark:text-emerald-200 mb-3 flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                Why This Works Best
              </h6>
              <div className="space-y-2">
                {Array.isArray(decision.recommendation_rationale) ? (
                  decision.recommendation_rationale.map((rationale, i) => (
                    <div key={i} className="flex items-start space-x-3 p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                      <div className="flex-shrink-0 w-2 h-2 bg-emerald-500 rounded-full mt-2"></div>
                      <DecisionLinkedText 
                        text={rationale}
                        decisions={decisions}
                        onDecisionClick={onDecisionClick}
                        className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                      />
                    </div>
                  ))
                ) : (
                  <div className="space-y-2">
                    {decision.recommendation_rationale.split('\n').filter(line => line.trim()).map((line, i) => (
                      <div key={i} className="flex items-start space-x-3 p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                        <div className="flex-shrink-0 w-2 h-2 bg-emerald-500 rounded-full mt-2"></div>
                        <DecisionLinkedText 
                          text={line.trim()}
                          decisions={decisions}
                          onDecisionClick={onDecisionClick}
                          className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Collapsible Pros/Cons/Mitigations Section */}
          {recommendedOption && (
            <div className="mb-5">
              <button
                onClick={() => setShowRecommendedDetails(!showRecommendedDetails)}
                className="flex items-center justify-between w-full p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg hover:bg-white/80 dark:hover:bg-gray-800/80 transition-colors"
              >
                <span className="text-sm font-semibold text-emerald-800 dark:text-emerald-200">
                  View Detailed Analysis
                </span>
                <svg 
                  className={`w-4 h-4 text-emerald-600 dark:text-emerald-400 transition-transform ${showRecommendedDetails ? 'rotate-180' : ''}`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {showRecommendedDetails && (
                <div className="mt-3 p-4 bg-white/80 dark:bg-gray-800/80 rounded-lg border border-emerald-100 dark:border-emerald-800">
                  {/* Risk and Complexity Indicators */}
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    <div className="text-center">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">Risk</div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(recommendedOption.riskLevel)} border`}>
                        {recommendedOption.riskLevel?.toUpperCase() || 'N/A'}
                      </span>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">Complexity</div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getComplexityColor(recommendedOption.complexity)} border`}>
                        {recommendedOption.complexity?.toUpperCase() || 'N/A'}
                      </span>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">Maintenance</div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getComplexityColor(recommendedOption.maintenanceBurden)} border`}>
                        {recommendedOption.maintenanceBurden?.toUpperCase() || 'N/A'}
                      </span>
                    </div>
                  </div>

                  {/* Pros and Cons */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {recommendedOption.pros && recommendedOption.pros.length > 0 && (
                      <div>
                        <h6 className="text-xs font-medium text-green-700 dark:text-green-400 mb-2">✓ Pros:</h6>
                        <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                          {recommendedOption.pros.map((pro, i) => (
                            <li key={i}>
                              <DecisionLinkedText 
                                text={pro}
                                decisions={decisions}
                                onDecisionClick={onDecisionClick}
                                className=""
                              />
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {recommendedOption.cons && recommendedOption.cons.length > 0 && (
                      <div>
                        <h6 className="text-xs font-medium text-red-700 dark:text-red-400 mb-2">✗ Cons:</h6>
                        <div className="space-y-2">
                          {recommendedOption.cons.map((con, i) => (
                            <div key={i} className="text-sm">
                              <div className="flex items-start">
                                <span className="text-red-500 mr-1 mt-0.5">•</span>
                                <span className="text-gray-600 dark:text-gray-400">
                                  <DecisionLinkedText 
                                    text={con}
                                    decisions={decisions}
                                    onDecisionClick={onDecisionClick}
                                    className=""
                                  />
                                </span>
                              </div>
                              {recommendedOption.mitigations && recommendedOption.mitigations[i] && (
                                <div className="ml-3 mt-1 text-xs text-blue-600 dark:text-blue-400 italic">
                                  🛡️ <DecisionLinkedText 
                                    text={recommendedOption.mitigations[i]}
                                    decisions={decisions}
                                    onDecisionClick={onDecisionClick}
                                    className=""
                                  />
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Quick accept button */}
          <div className="flex justify-end">
            <button
              onClick={() => onSelect(
                decision.id, 
                recommendedOption.id, 
                Array.isArray(decision.recommendation_rationale) 
                  ? decision.recommendation_rationale.join(' ')
                  : decision.recommendation_rationale || 'Accepted recommended option'
              )}
              className="px-4 py-2 bg-emerald-600 text-white dark:text-white rounded-lg hover:bg-emerald-700 transition-colors text-sm font-medium"
            >
              ✓ Accept Recommended
            </button>
          </div>
        </div>
      )}

      {/* All Options with Detailed Views */}
      <div className="mb-6">
        <h5 className="font-semibold text-gray-900 dark:text-white mb-4">Alternative Options:</h5>
        
        <div className="space-y-3">
          {decision.options?.filter(option => option.id !== decision.recommended_option_id).map((option) => {
            const isSelected = selectedOption === option.id;
            
            return (
              <div key={option.id} className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                isSelected 
                  ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`} onClick={() => setSelectedOption(option.id)}>
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center flex-wrap gap-2 mb-2">
                      <input
                        type="radio"
                        checked={isSelected}
                        onChange={() => setSelectedOption(option.id)}
                        className="text-blue-600 mr-2"
                      />
                      <h6 className="font-semibold text-gray-800 dark:text-gray-200">{option.name}</h6>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getComplexityColor(option.complexity)} border`}>
                        Complexity: {option.complexity || 'N/A'}
                      </span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRiskColor(option.riskLevel)} border`}>
                        Risk: {option.riskLevel || 'N/A'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{option.description}</p>
                  </div>
                </div>

                {/* Detailed Analysis */}
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  {/* Comprehensive Pros and Cons */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {option.pros.length > 0 && (
                      <div>
                        <h6 className="text-xs font-medium text-green-700 dark:text-green-400 mb-2">✓ Pros:</h6>
                        <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                          {option.pros.map((pro, i) => (
                            <li key={i}>
                              <DecisionLinkedText 
                                text={pro}
                                decisions={decisions}
                                onDecisionClick={onDecisionClick}
                                className=""
                              />
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {option.cons.length > 0 && (
                      <div>
                        <h6 className="text-xs font-medium text-red-700 dark:text-red-400 mb-2">✗ Cons:</h6>
                        <div className="space-y-2">
                          {option.cons.map((con, i) => (
                            <div key={i} className="text-sm">
                              <div className="flex items-start">
                                <span className="text-red-500 mr-1 mt-0.5">•</span>
                                <span className="text-gray-600 dark:text-gray-400">
                                  <DecisionLinkedText 
                                    text={con}
                                    decisions={decisions}
                                    onDecisionClick={onDecisionClick}
                                    className=""
                                  />
                                </span>
                              </div>
                              {option.mitigations && option.mitigations[i] && (
                                <div className="ml-3 mt-1 text-xs text-blue-600 dark:text-blue-400 italic">
                                  🛡️ <DecisionLinkedText 
                                    text={option.mitigations[i]}
                                    decisions={decisions}
                                    onDecisionClick={onDecisionClick}
                                    className=""
                                  />
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Rationale */}
      {selectedOption && selectedOption !== decision.recommended_option_id && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Decision Rationale <span className="text-red-500">*</span>
          </label>
          <textarea
            value={rationale}
            onChange={(e) => setRationale(e.target.value)}
            placeholder="Please explain why you chose this option instead of the recommended one."
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-blue-500"
            rows={3}
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Required when choosing a non-recommended option.
          </p>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={!selectedOption || (selectedOption !== decision.recommended_option_id && !rationale.trim())}
          className="px-6 py-2 bg-blue-600 text-white dark:text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          Save Decision
        </button>
      </div>
    </div>
  );
}

export default function StreamlinedDecisionMakingStep({
  wizardState,
  actions,
  repositorySlug,
  installationId,
  onBack,
  onContinue,
  isLoading
}: StreamlinedDecisionMakingStepProps) {
  const [selectedDecisionForModal, setSelectedDecisionForModal] = useState<string | null>(null);
  const [selectedWizardDecisionForModal, setSelectedWizardDecisionForModal] = useState<DecisionPoint | null>(null);
  
  // Use project context with debugging
  const { repositorySlug: finalRepoSlug, installationId: finalInstallationId } = useProjectContextWithDebug(repositorySlug, installationId);
  
  // Collect all text that might contain decision references
  const textsToSearch = useMemo(() => {
    const allTexts: (string | string[])[] = [];
    
    wizardState.decisionPoints.forEach(decision => {
      if (decision.recommendation_rationale) {
        allTexts.push(decision.recommendation_rationale);
      }
      
      // Include rationale from completed decisions
      if (decision.rationale) {
        allTexts.push(decision.rationale);
      }
      
      decision.options?.forEach(option => {
        if (option.alignmentJustification) {
          allTexts.push(option.alignmentJustification);
        }
        
        // Include pros, cons, mitigations, and alignment reasoning that might contain decision references
        if (option.pros && option.pros.length > 0) {
          allTexts.push(option.pros);
        }
        if (option.cons && option.cons.length > 0) {
          allTexts.push(option.cons);
        }
        if (option.mitigations && option.mitigations.length > 0) {
          allTexts.push(option.mitigations);
        }
        if (option.alignmentReasoning) {
          allTexts.push(option.alignmentReasoning);
        }
        if (option.debuggingComplexity) {
          allTexts.push(option.debuggingComplexity);
        }
      });
    });
    
    // Include classification reasons that might contain decision references
    wizardState.decisionProcessingResults?.forEach(result => {
      if (result.classification.reasons) {
        allTexts.push(result.classification.reasons);
      }
    });
    
    return allTexts;
  }, [wizardState.decisionPoints, wizardState.decisionProcessingResults]);

  // Fetch decisions based on references found in the text
  const { decisions, isLoading: isLoadingDecisions, error: decisionsError } = useDecisions({
    repositorySlug: finalRepoSlug,
    installationId: finalInstallationId,
    texts: textsToSearch
  });

  const handleDecisionClick = (decisionId: string) => {
    setSelectedDecisionForModal(decisionId);
  };

  const handleWizardDecisionClick = (decision: DecisionPoint) => {
    setSelectedWizardDecisionForModal(decision);
  };
  
  // Get decisions that need review
  const pendingReviewDecisions = useMemo(() => {
    const results = wizardState.decisionProcessingResults || [];
    return wizardState.decisionPoints.filter((decision, index) => 
      results[index]?.classification.requiresReview && !decision.selectedOption
    );
  }, [wizardState.decisionPoints, wizardState.decisionProcessingResults]);

  // Get decisions already made (auto-processed or manually reviewed)
  const completedDecisions = useMemo(() => {
    return wizardState.decisionPoints.filter(decision => decision.selectedOption);
  }, [wizardState.decisionPoints]);

  const allDecisionsMade = pendingReviewDecisions.length === 0;

  const handleDecisionSelect = (decisionId: string, optionId: string, rationale: string) => {
    actions.selectDecisionOption(decisionId, optionId, rationale);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Decision Review
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Review and make decisions on the items that require your attention. 
          Low-impact decisions have been automatically processed.
        </p>
      </div>

      {/* Progress */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Decision Progress</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {completedDecisions.length} of {wizardState.decisionPoints.length} completed
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${(completedDecisions.length / wizardState.decisionPoints.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Decisions Needing Review */}
      {pendingReviewDecisions.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Decisions Requiring Your Review ({pendingReviewDecisions.length})
          </h3>
          
          <div className="space-y-6">
            {pendingReviewDecisions.map((decision) => {
              const result = wizardState.decisionProcessingResults?.find(r => r.decisionId === decision.id);
              if (!result) return null;
              
              const recommendedOption = decision.options?.find(opt => 
                opt.id === decision.recommended_option_id
              );

              return (
                <DecisionReviewInline
                  key={decision.id}
                  decision={decision}
                  result={result}
                  onSelect={handleDecisionSelect}
                                  decisions={decisions}
                                  onDecisionClick={handleDecisionClick}
                                />
              );
            })}
          </div>
        </div>
      )}

      {/* Completed Decisions Summary */}
      {completedDecisions.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            ✅ Completed Decisions ({completedDecisions.length})
          </h3>
          
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {completedDecisions.map((decision) => {
                const selectedOption = decision.options?.find(opt => opt.id === decision.selectedOption);
                const result = wizardState.decisionProcessingResults?.find(r => r.decisionId === decision.id);
                
                return (
                  <div 
                    key={decision.id} 
                    onClick={() => handleWizardDecisionClick(decision)}
                    className="bg-white dark:bg-gray-800 rounded-lg p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 border border-transparent hover:border-green-300 dark:hover:border-green-600 group"
                    title="Click to view decision details"
                  >
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{decision.title}</div>
                    <div className="text-green-600 dark:text-green-400 text-sm mt-1">
                      ✓ {selectedOption?.name || 'Decision made'}
                      {result?.autoProcessed && (
                        <span className="ml-2 px-1 py-0.5 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs rounded">
                          Auto
                        </span>
                      )}
                    </div>
                    {/* Show rationale with decision references parsed */}
                    {decision.rationale && (
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-2 line-clamp-2">
                        <DecisionLinkedText 
                          text={Array.isArray(decision.rationale) ? decision.rationale.join(' ') : decision.rationale}
                          decisions={decisions}
                          onDecisionClick={handleDecisionClick}
                          className=""
                        />
                    </div>
                    )}
                    {/* Visual indicator that this is clickable */}
                    <div className="text-xs text-gray-400 dark:text-gray-500 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                     Click to view details
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          ← Back
        </button>
        
        <div className="text-center">
          {!allDecisionsMade && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {pendingReviewDecisions.length} decision{pendingReviewDecisions.length !== 1 ? 's' : ''} remaining
            </p>
          )}
        </div>
        
        <button
          onClick={onContinue}
          disabled={!allDecisionsMade || isLoading}
          className="px-6 py-2 bg-green-600 text-white dark:text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {isLoading ? 'Generating...' : 'Generate Design Document →'}
        </button>
      </div>

      {/* Decision Detail Modal */}
      <DecisionDetailModal
        decision={selectedDecisionForModal ? decisions[selectedDecisionForModal] : null}
        isOpen={!!selectedDecisionForModal}
        onClose={() => setSelectedDecisionForModal(null)}
      />

      {/* Wizard Decision Detail Modal */}
      {selectedWizardDecisionForModal && (() => {
        const foundResult = wizardState.decisionProcessingResults?.find(r => r.decisionId === selectedWizardDecisionForModal.id);
        
        // Provide safe default structure for missing results
        const result = foundResult || {
          decisionId: selectedWizardDecisionForModal.id,
          autoProcessed: true,
          classification: {
            requiresReview: false,
            reviewMode: 'auto' as const,
            impactScore: 0,
            reasons: [],
            riskFactors: []
          }
        } as DecisionProcessingResult;
        
        return (
          <DecisionReviewModal
            decision={selectedWizardDecisionForModal}
            result={result}
            onSelect={handleDecisionSelect}
            onClose={() => setSelectedWizardDecisionForModal(null)}
            decisions={decisions}
            onDecisionClick={handleDecisionClick}
          />
        );
      })()}
    </div>
  );
} 