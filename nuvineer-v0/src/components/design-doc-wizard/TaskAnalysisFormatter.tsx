import React from 'react';

interface TaskAnalysisProps {
  analysis: {
    summary: string;
    functional_requirements: string[];
    implied_NFRs: Array<{
      nfr: string;
      evidence: string;
    }>;
    core_architectural_characteristics: string[];
    non_goals?: string[];
  };
  isDarkMode?: boolean;
}

export default function TaskAnalysisFormatter({ analysis, isDarkMode = false }: TaskAnalysisProps) {
  if (!analysis) {
    return <div className="text-gray-500 dark:text-gray-400">No task analysis available</div>;
  }

  return (
    <div className="space-y-6">
      {/* Summary */}
      {analysis.summary && (
        <div>
          <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            📋 Summary
          </h4>
          <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-700'} leading-relaxed`}>
            {analysis.summary}
          </p>
        </div>
      )}

      {/* Functional Requirements */}
      {analysis.functional_requirements && analysis.functional_requirements.length > 0 && (
        <div>
          <h4 className={`font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            ✅ Functional Requirements ({analysis.functional_requirements.length})
          </h4>
          <ul className="space-y-2">
            {analysis.functional_requirements.map((req, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="text-green-500 mt-1">•</span>
                <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-700'} leading-relaxed`}>
                  {req}
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Non-Goals */}
      {analysis.non_goals && analysis.non_goals.length > 0 && (
        <div>
          <h4 className={`font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            🚫 Non-Goals ({analysis.non_goals.length})
          </h4>
          <ul className="space-y-2">
            {analysis.non_goals.map((goal, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="text-red-500 mt-1">•</span>
                <span className={`${isDarkMode ? 'text-gray-300' : 'text-gray-700'} leading-relaxed`}>
                  {goal}
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Implied NFRs */}
      {analysis.implied_NFRs && analysis.implied_NFRs.length > 0 && (
        <div>
          <h4 className={`font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            ⚡ Quality Requirements ({analysis.implied_NFRs.length})
          </h4>
          <div className="space-y-3">
            {analysis.implied_NFRs.map((nfr, index) => (
              <div 
                key={index} 
                className={`p-3 rounded-lg border ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-700' 
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <div className={`font-medium mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {nfr.nfr}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} italic`}>
                  Evidence: "{nfr.evidence}"
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Core Architectural Characteristics */}
      {analysis.core_architectural_characteristics && analysis.core_architectural_characteristics.length > 0 && (
        <div>
          <h4 className={`font-semibold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            🏗️ Core Architectural Characteristics
          </h4>
          <div className="flex flex-wrap gap-2">
            {analysis.core_architectural_characteristics.map((char, index) => (
              <span 
                key={index}
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  isDarkMode 
                    ? 'bg-blue-900 text-blue-200 border border-blue-700' 
                    : 'bg-blue-100 text-blue-800 border border-blue-200'
                }`}
              >
                {char}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 