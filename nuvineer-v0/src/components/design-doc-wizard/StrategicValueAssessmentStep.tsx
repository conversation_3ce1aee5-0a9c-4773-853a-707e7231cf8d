'use client'

import React, { useState } from 'react';
import type {
  WizardState,
  StrategicAssessmentAlternative,
} from '../../types/design-doc-wizard';
import ShareButton from './ShareButton';

interface StrategicValueAssessmentStepProps {
  wizardState: WizardState;
  onBack: () => void;
  onContinue: () => void;
  onOverride: () => void;
  onRetryWithMinimalScope?: (minimalScopeDescription: string) => void;
  isLoading: boolean;
  dbSessionId?: string;
}

export default function StrategicValueAssessmentStep({
  wizardState,
  onBack,
  onContinue,
  onOverride,
  onRetryWithMinimalScope,
  isLoading,
  dbSessionId
}: StrategicValueAssessmentStepProps) {
  const { strategicAssessment } = wizardState;
  const [copied, setCopied] = useState(false);
  const [isGeneratingMinimalScope, setIsGeneratingMinimalScope] = useState(false);
  const [showMinimalScopeModal, setShowMinimalScopeModal] = useState(false);
  const [proposedMinimalScope, setProposedMinimalScope] = useState<string>('');

  const getRecommendationStyle = (recommendation?: string) => {
    switch (recommendation) {
      case 'BUILD': return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-200 dark:border-green-700';
      case 'DOCUMENT': return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-200 dark:border-yellow-700';
      case 'DEFER': return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-200 dark:border-red-700';
      default: return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600';
    }
  };

  const getRecommendationIcon = (recommendation?: string) => {
    switch (recommendation) {
      case 'BUILD': return '🟢';
      case 'DOCUMENT': return '📄';
      case 'DEFER': return '⏸️';
      default: return '❓';
    }
  };

  const getComplexityColor = (level?: string) => {
    switch (level) {
      case 'high': return 'text-red-600 dark:text-red-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-green-600 dark:text-green-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getAlignmentColor = (alignment?: string) => {
    switch (alignment) {
      case 'strong': return 'text-green-600 dark:text-green-400';
      case 'moderate': return 'text-yellow-600 dark:text-yellow-400';
      case 'weak': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const handleSkipForExperimental = () => {
    // For experimental features, we can skip strategic assessment
    // since they're designed to be easy to enable/disable/remove
    onContinue();
  };

  // Check if this is an experimental feature
  const isExperimental = wizardState.taskDetails.isExperimental;

  const handleCopyToClipboard = () => {
    if (!strategicAssessment) return;

    const markdown = `### Strategic Value Assessment: Recommendation to ${strategicAssessment.recommendation}

**Recommendation:** ${strategicAssessment.recommendation} (${strategicAssessment.confidence_level} confidence)

**Strategic Rationale:**
${strategicAssessment.strategic_rationale}

---

**Complexity Assessment:**
- **Implementation:** ${strategicAssessment.complexity_assessment?.implementation_complexity?.toUpperCase()}
- **Maintenance:** ${strategicAssessment.complexity_assessment?.maintenance_burden?.toUpperCase()}
- **User Value:** ${strategicAssessment.complexity_assessment?.user_value_delivered?.toUpperCase()}
> ${strategicAssessment.complexity_assessment?.complexity_justification}

---

**Strategic Alignment:**
- **Product Alignment:** ${strategicAssessment.alignment_analysis?.constitutional_alignment?.toUpperCase()}
- **Stage Appropriateness:** ${strategicAssessment.alignment_analysis?.stage_appropriateness}
${
  strategicAssessment.alignment_analysis?.strategic_concerns &&
  strategicAssessment.alignment_analysis.strategic_concerns.length > 0
    ? `
**Strategic Concerns:**
${strategicAssessment.alignment_analysis.strategic_concerns.map(c => `- ${c}`).join('\n')}
`
    : ''
}
---
${
  strategicAssessment.recommended_alternatives &&
  strategicAssessment.recommended_alternatives.length > 0
    ? `
**Recommended Alternatives:**
${strategicAssessment.recommended_alternatives.map(
      (alt: StrategicAssessmentAlternative) => `
- **${alt.approach}**
  - **Effort:** ${alt.effort_required}, **Value:** ${alt.value_delivered}
  - ${alt.implementation_notes}
`).join('\n')}
`
    : ''
}
---

**Override Justification:**
To proceed with building this feature, the following needs to be justified:
> ${strategicAssessment.override_justification_needed}
`;

    navigator.clipboard.writeText(markdown.trim()).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const generateMinimalScopeRecommendation = async () => {
    if (!strategicAssessment) return;

    setIsGeneratingMinimalScope(true);

    try {
      const response = await fetch('/api/design-doc-wizard/generate-minimal-scope', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskDetails: wizardState.taskDetails,
          strategicAssessment: strategicAssessment,
          projectConstitution: wizardState.projectConstitution,
          userJourneys: wizardState.userJourneys,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to generate minimal scope: ${errorData.details || response.statusText}`);
      }

      const data = await response.json();
      
      if (data.minimalScopeDescription) {
        setProposedMinimalScope(data.minimalScopeDescription);
        setShowMinimalScopeModal(true);
      }
    } catch (error: any) {
      console.error('Error generating minimal scope:', error);
      // Could add error handling UI here
    } finally {
      setIsGeneratingMinimalScope(false);
    }
  };

  const handleAcceptMinimalScope = () => {
    if (onRetryWithMinimalScope && proposedMinimalScope) {
      onRetryWithMinimalScope(proposedMinimalScope);
      setShowMinimalScopeModal(false);
      setProposedMinimalScope('');
    }
  };

  const handleDeclineMinimalScope = () => {
    setShowMinimalScopeModal(false);
    setProposedMinimalScope('');
  };

  // Check if we should show minimal scope option
  const shouldShowMinimalScopeOption = () => {
    // Don't show minimal scope option if we're already in a minimal scope assessment
    if (wizardState.isMinimalScope) return false;
    
    if (strategicAssessment?.recommendation !== 'DEFER' && strategicAssessment?.recommendation !== 'DOCUMENT') return false;
    
    const userValue = strategicAssessment?.complexity_assessment?.user_value_delivered;
    return userValue === 'low' || userValue === 'medium' || userValue === 'high';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Value Assessment</h2>
        <div className="flex items-center space-x-3">
          <ShareButton 
            dbSessionId={dbSessionId} 
            variant="small"
            title="Share strategic assessment with your team"
          />
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Step 5 of 8 {isExperimental && <span className="text-purple-600 dark:text-purple-400">(Optional for Experimental)</span>}
          </div>
        </div>
      </div>
      
      {/* Minimal Scope Modal */}
      {showMinimalScopeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Proposed Minimal Scope Alternative</h3>
              <button
                onClick={handleDeclineMinimalScope}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>

            <div className="mb-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                <div className="flex items-start space-x-3">
                  <div className="text-blue-600 dark:text-blue-400 mt-1">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                      Minimal Scope Recommendation
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      This alternative focuses on delivering core user value while avoiding the complexity concerns that led to the {strategicAssessment?.recommendation?.toLowerCase()} recommendation.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                <h4 className="font-medium mb-3 text-gray-900 dark:text-gray-100">Proposed Minimal Scope:</h4>
                <div className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {proposedMinimalScope}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={handleDeclineMinimalScope}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Decline
              </button>
              <button
                onClick={handleAcceptMinimalScope}
                className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700"
              >
                Accept & Continue with Minimal Scope
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Experimental Feature Indicator */}
      {isExperimental && (
        <div className="mb-6 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-purple-600 dark:text-purple-400 mt-1">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-purple-900 dark:text-purple-100">Experimental Feature - Strategic Assessment Optional</h3>
              <p className="text-sm text-purple-700 dark:text-purple-300 mt-1 mb-3">
                Since this is an experimental feature designed to be easily enabled, disabled, or removed, 
                you can skip the strategic assessment and proceed directly to design. Experimental features 
                are inherently low-risk and focused on rapid validation.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={handleSkipForExperimental}
                  className="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-lg hover:bg-purple-700"
                >
                  Skip to Design Document
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Minimal Scope Indicator */}
      {wizardState.isMinimalScope && (
        <div className="mb-6 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-amber-600 dark:text-amber-400 mt-1">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-amber-900 dark:text-amber-100">Minimal Scope Assessment</h3>
              <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                This assessment is evaluating a reduced scope version of your feature that focuses on core user value while avoiding complexity concerns from the original assessment.
              </p>
            </div>
          </div>
        </div>
      )}
      
      <div className="space-y-6">
        {/* Strategic Assessment Summary */}
        {/* <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-blue-600 dark:text-blue-400 mt-1">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100">Strategic Assessment Complete</h3>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Your feature request has been assessed against your project priorites and strategic context.
              </p>
            </div>
          </div>
        </div> */}

        {strategicAssessment ? (
          <>
            {/* Main Recommendation - Only show for DOCUMENT or DEFER */}
            {(strategicAssessment.recommendation === 'DOCUMENT' || strategicAssessment.recommendation === 'DEFER') && (
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <span className="text-2xl">
                    {getRecommendationIcon(strategicAssessment.recommendation)}
                  </span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Recommendation: {strategicAssessment.recommendation}
                    </h3>
                    <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getRecommendationStyle(strategicAssessment.recommendation)}`}>
                      {strategicAssessment.confidence_level} confidence
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                  <h4 className="font-medium mb-2">Strategic Rationale:</h4>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {strategicAssessment.strategic_rationale}
                  </p>
                </div>
              </div>
            )}

            {/* Product Alignment - Moved to top */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Product Alignment</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Product Alignment</div>
                  <div className={`font-semibold ${getAlignmentColor(strategicAssessment.alignment_analysis?.constitutional_alignment)}`}>
                    {strategicAssessment.alignment_analysis?.constitutional_alignment?.toUpperCase()}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Stage Appropriateness</div>
                  <div className="font-semibold text-gray-700 dark:text-gray-300">
                    {strategicAssessment.alignment_analysis?.stage_appropriateness}
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3">
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Priority Alignment:</div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {strategicAssessment.alignment_analysis?.priority_alignment}
                </p>
              </div>
              {strategicAssessment.alignment_analysis?.strategic_concerns && 
               strategicAssessment.alignment_analysis.strategic_concerns.length > 0 && (
                <div className="mt-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                  <div className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">Strategic Concerns:</div>
                  <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                    {strategicAssessment.alignment_analysis.strategic_concerns.map((concern: string, index: number) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-yellow-600 mt-0.5">•</span>
                        <span>{concern}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Complexity Assessment - Moved below Product Alignment */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Complexity Assessment</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Implementation</div>
                  <div className={`font-semibold ${getComplexityColor(strategicAssessment.complexity_assessment?.implementation_complexity)}`}>
                    {strategicAssessment.complexity_assessment?.implementation_complexity?.toUpperCase()}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Maintenance</div>
                  <div className={`font-semibold ${getComplexityColor(strategicAssessment.complexity_assessment?.maintenance_burden)}`}>
                    {strategicAssessment.complexity_assessment?.maintenance_burden?.toUpperCase()}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">User Value</div>
                  <div className={`font-semibold ${getComplexityColor(strategicAssessment.complexity_assessment?.user_value_delivered)}`}>
                    {strategicAssessment.complexity_assessment?.user_value_delivered?.toUpperCase()}
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {strategicAssessment.complexity_assessment?.complexity_justification}
                </p>
              </div>
            </div>

            {/* Recommended Alternatives */}
            {strategicAssessment.recommended_alternatives && 
             strategicAssessment.recommended_alternatives.length > 0 && 
             strategicAssessment.recommendation !== 'BUILD' && (
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Recommended Alternatives</h3>
                <div className="space-y-3">
                  {strategicAssessment.recommended_alternatives.map((alternative: StrategicAssessmentAlternative, index: number) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          {alternative.approach}
                        </h4>
                        <div className="flex items-center space-x-2 text-sm">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getComplexityColor(alternative.effort_required)} bg-gray-100 dark:bg-gray-800`}>
                            {alternative.effort_required} effort
                          </span>
                          <span className="text-gray-600 dark:text-gray-400">
                            {alternative.value_delivered} value
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {alternative.implementation_notes}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Override Section */}
            {(strategicAssessment.recommendation === 'DOCUMENT' || strategicAssessment.recommendation === 'DEFER') && (
              <div className="border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/20 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 text-orange-800 dark:text-orange-200">
                  Override This Recommendation?
                </h3>
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    To override this recommendation, you would need to justify:
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {strategicAssessment.override_justification_needed}
                  </p>
                </div>
                <button
                  onClick={onOverride}
                  className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
                >
                  Override and Continue
                </button>
              </div>
            )}

            {/* Next Steps */}
            {strategicAssessment.recommendation !== 'BUILD' && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Next Steps:</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {strategicAssessment.next_steps}
                </p>
                
                {/* Minimal Scope Alternative for DEFER with medium/high user value */}
                {shouldShowMinimalScopeOption() && (
                  <div className="mt-4 p-4 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-600 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <div className="text-blue-600 dark:text-blue-400 mt-1">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                          Consider Minimal Scope Alternative
                        </h5>
                        <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                          Since this feature has <strong>{strategicAssessment.complexity_assessment?.user_value_delivered}</strong> user value, 
                          consider if there's a minimal scope version that could deliver core value without the complexity issues causing this {strategicAssessment.recommendation.toLowerCase()}.
                          This could help maintain the application's attractiveness to users while avoiding technical risks.
                        </p>
                        <button
                          onClick={generateMinimalScopeRecommendation}
                          disabled={isGeneratingMinimalScope}
                          className="px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isGeneratingMinimalScope ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Analyzing Minimal Scope...
                            </>
                          ) : (
                            'Explore Minimal Scope Alternative'
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <div className="text-gray-500 dark:text-gray-400">
              No strategic assessment available. Please run the analysis first.
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onBack}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50"
          >
            Back
          </button>
          <div className="flex space-x-3">
            {isExperimental && !strategicAssessment && (
              <div className="text-sm text-gray-600 dark:text-gray-400 self-center">
                <span className="text-purple-600 dark:text-purple-400">
                  💡 As an experimental feature, you can skip assessment above
                </span>
              </div>
            )}
            {strategicAssessment?.recommendation === 'BUILD' && (
              <button
                onClick={onContinue}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                {isLoading ? 'Processing...' : 'Continue'}
              </button>
            )}
            {(strategicAssessment?.recommendation === 'DEFER' || strategicAssessment?.recommendation === 'DOCUMENT') && (
              <button
                onClick={handleCopyToClipboard}
                disabled={isLoading || copied}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 disabled:opacity-50"
              >
                {isLoading
                  ? 'Processing...'
                  : copied
                  ? 'Copied!'
                  : 'Copy as Markdown'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 