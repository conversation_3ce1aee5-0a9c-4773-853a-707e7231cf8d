'use client'

import React from 'react';

interface TaskDefinitionStepProps {
  wizardState: any;
  repositorySlug?: string;
  issues: any[];
  isLoadingIssues: boolean;
  showIssueSelector: boolean;
  setShowIssueSelector: (show: boolean) => void;
  handleImportFromIssue: () => void;
  handleSelectIssue: (issue: any) => void;
  editing: {
    updateTaskDetails: (updates: any) => void;
  };
  onBack: () => void;
  onContinue: () => void;
  isLoading: boolean;
}

export default function TaskDefinitionStep({
  wizardState,
  repositorySlug,
  issues,
  isLoadingIssues,
  showIssueSelector,
  setShowIssueSelector,
  handleImportFromIssue,
  handleSelectIssue,
  editing,
  onBack,
  onContinue,
  isLoading
}: TaskDefinitionStepProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Build A Feature</h2>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Step 2 of 8
        </div>
      </div>
      
      <div className="space-y-6">
        {/* GitHub Issue Import */}
        {repositorySlug && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">Import from GitHub Issue</h3>
                <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                  Quickly start from an existing GitHub issue in {repositorySlug}
                </p>
              </div>
              <button
                onClick={handleImportFromIssue}
                disabled={isLoadingIssues}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md text-sm font-medium"
              >
                {isLoadingIssues ? 'Loading...' : 'Browse Issues'}
              </button>
            </div>
          </div>
        )}

        {/* Issue Selector Modal */}
        {showIssueSelector && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Select GitHub Issue</h3>
                <button
                  onClick={() => setShowIssueSelector(false)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  ✕
                </button>
              </div>

              {isLoadingIssues ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                  <span>Loading issues...</span>
                </div>
              ) : issues.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No open issues found in this repository.
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {issues.map((issue) => (
                    <div
                      key={issue.number}
                      onClick={() => handleSelectIssue(issue)}
                      className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            #{issue.number}: {issue.title}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                            {issue.body ? issue.body.substring(0, 200) + (issue.body.length > 200 ? '...' : '') : 'No description'}
                          </p>
                        </div>
                        <div className="ml-4 text-xs text-gray-500 dark:text-gray-400">
                          {issue.assignee ? `@${issue.assignee.login}` : 'Unassigned'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Task Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feature Request *</label>
          <input
            type="text"
            value={wizardState.taskDetails.title}
            onChange={(e) => editing.updateTaskDetails({ title: e.target.value })}
            placeholder="e.g., Add user authentication system"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Task Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feature Description *</label>
          <textarea
            value={wizardState.taskDetails.description}
            onChange={(e) => editing.updateTaskDetails({ description: e.target.value })}
            placeholder="Describe what you want to build, the problem it solves, and any key requirements..."
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Initial Ideas */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Initial Ideas (Optional)</label>
          <textarea
            value={wizardState.taskDetails.initialIdeas || ''}
            onChange={(e) => editing.updateTaskDetails({ initialIdeas: e.target.value })}
            placeholder="Any initial thoughts on approach, technology choices, or implementation ideas..."
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Experimental Feature Toggle - Only show for non-library projects */}
        {wizardState.projectConstitution?.projectType !== 'library' && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="experimental-toggle"
                checked={wizardState.taskDetails.isExperimental || false}
                onChange={(e) => editing.updateTaskDetails({ isExperimental: e.target.checked })}
                className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <label htmlFor="experimental-toggle" className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                Mark as Experimental Feature
              </label>
            </div>
            <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
              Important to be easy to enable/disable/tweak/remove.
            </p>
          </div>
        )}
      </div>
      
      {/* Navigation buttons */}
      <div className="flex justify-between mt-8">
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
        >
          ← Define Project Context
        </button>
        
        <button
          onClick={onContinue}
          disabled={!wizardState.taskDetails.title || !wizardState.taskDetails.description || isLoading}
          className="px-6 py-3 bg-amber-500 hover:bg-amber-600 disabled:bg-gray-400 text-white rounded-lg font-medium"
        >
          {isLoading ? 'Analyzing Feature Request...' : 'Analyze Feature Request →'}
        </button>
      </div>
    </div>
  );
} 