import React, { useState } from 'react';
import type { WizardState, DecisionPoint } from '../../types/design-doc-wizard';

interface DecisionDiscoveryStepProps {
  wizardState: WizardState;
  editing: {
    addNewDecision: () => void;
    updateDecision: (decisionId: string, updates: Partial<DecisionPoint>) => void;
    showRemovalModal: (decisionId: string) => void;
  };
  onBack: () => void;
  onContinue: () => void;
  isLoading: boolean;
}

export default function DecisionDiscoveryStep({
  wizardState,
  editing,
  onBack,
  onContinue,
  isLoading
}: DecisionDiscoveryStepProps) {
  const { decisionPoints } = wizardState;

  // State for adding new decisions
  const [isAddingDecision, setIsAddingDecision] = useState(false);
  const [newDecisionForm, setNewDecisionForm] = useState<{
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    category: 'architecture' | 'technology' | 'data' | 'integration' | 'security' | 'performance';
  }>({
    title: '',
    description: '',
    priority: 'medium',
    category: 'architecture'
  });

  // Check if this is an experimental feature
  const isExperimental = wizardState.taskDetails.isExperimental;

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category?: string) => {
    switch (category) {
      case 'architecture': return '🏗️';
      case 'technology': return '⚙️';
      case 'data': return '💾';
      case 'integration': return '🔌';
      case 'security': return '🔒';
      case 'performance': return '⚡';
      default: return '❓';
    }
  };

  const handleStartAddingDecision = () => {
    setIsAddingDecision(true);
    setNewDecisionForm({
      title: '',
      description: '',
      priority: 'medium' as const,
      category: 'architecture' as const
    });
  };

  const handleCancelAddingDecision = () => {
    setIsAddingDecision(false);
    setNewDecisionForm({
      title: '',
      description: '',
      priority: 'medium' as const,
      category: 'architecture' as const
    });
  };

  const handleSaveNewDecision = () => {
    if (newDecisionForm.title.trim() && newDecisionForm.description.trim()) {
      // First, call addNewDecision to create the base decision
      editing.addNewDecision();
      
      // The new decision will be the last one in the array
      // We'll update it in the next render cycle
      setTimeout(() => {
        // Find the most recently added decision (should be last in array)
        const lastDecision = decisionPoints[decisionPoints.length - 1];
        if (lastDecision && lastDecision.title === 'New Technical Decision') {
          editing.updateDecision(lastDecision.id, {
            title: newDecisionForm.title.trim(),
            description: newDecisionForm.description.trim(),
            priority: newDecisionForm.priority,
            category: newDecisionForm.category
          });
        }
      }, 0);
      
      handleCancelAddingDecision();
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Review Technical Decisions
        </h2>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Step 6 of 8
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          AI has identified {decisionPoints.length} critical technical decisions for your task. 
          Review them below and remove any that aren't relevant or have already been decided.
        </p>
      </div>

      {/* Experimental Feature Indicator */}
      {isExperimental && (
        <div className="mb-6 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-purple-600 dark:text-purple-400 mt-1">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-purple-900 dark:text-purple-100">🧪 Experimental Feature Decisions</h3>
              <p className="text-sm text-purple-700 dark:text-purple-300 mt-1">
                These decisions focus on reversible, feature-flag-friendly approaches. They avoid data model changes 
                and architectural modifications to keep the experiment easy to enable, disable, and remove.
              </p>
              <div className="mt-2 text-xs text-purple-600 dark:text-purple-400">
                <span className="font-medium">Focus areas:</span> UI behavior • Feature flags • Configuration • API endpoints • Existing infrastructure
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Summary stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {decisionPoints.length}
          </div>
          <div className="text-sm text-blue-800 dark:text-blue-200">
            Decision Points
          </div>
        </div>
        <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
          <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
            {decisionPoints.filter(dp => dp.priority === 'high').length}
          </div>
          <div className="text-sm text-amber-800 dark:text-amber-200">
            High Priority
          </div>
        </div>
        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {decisionPoints.filter(dp => dp.category === 'architecture').length}
          </div>
          <div className="text-sm text-green-800 dark:text-green-200">
            Architectural
          </div>
        </div>
      </div>

      {/* Decision points list */}
      <div className="space-y-4 mb-6">
        {decisionPoints.map((decision, index) => (
          <div key={decision.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{getCategoryIcon(decision.category)}</span>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {decision.title}
                  </h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={`px-2 py-1 text-xs rounded border ${getPriorityColor(decision.priority)}`}>
                      {decision.priority || 'medium'} priority
                    </span>
                    {decision.category && (
                      <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded border">
                        {decision.category}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => editing.showRemovalModal(decision.id)}
                  className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                  title="Remove this decision"
                >
                  🗑️
                </button>
              </div>
            </div>
            
            <p className="text-gray-600 dark:text-gray-400 mb-3">
              {decision.description}
            </p>

            {decision.initial_proposal_to_evaluate && decision.initial_proposal_to_evaluate !== 'N/A' && (
              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded border border-blue-200 dark:border-blue-800">
                <div className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                  💡 Evaluating your initial idea:
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  "{decision.initial_proposal_to_evaluate}"
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Add Decision Point Section */}
      {!isAddingDecision ? (
        <div className="flex justify-center mb-6">
          <button
            onClick={handleStartAddingDecision}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center space-x-2 transition-colors cursor-pointer"
            title="Add a new technical decision point"
          >
            <span className="text-lg">➕</span>
            <span>Add Decision Point</span>
          </button>
        </div>
      ) : (
        <div className="border border-blue-200 dark:border-blue-600 rounded-lg p-4 mb-6 bg-blue-50 dark:bg-blue-900/20">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
            <span className="text-lg">➕</span>
            <span>Add New Technical Decision</span>
          </h3>
          
          <div className="space-y-4">
            {/* Title Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Decision Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newDecisionForm.title}
                onChange={(e) => setNewDecisionForm(prev => ({ ...prev, title: e.target.value }))}
                placeholder="e.g., Choose state management library for React components"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Description Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description <span className="text-red-500">*</span>
              </label>
              <textarea
                value={newDecisionForm.description}
                onChange={(e) => setNewDecisionForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what needs to be decided and why it's important..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Priority and Category Selects */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Priority
                </label>
                <select
                  value={newDecisionForm.priority}
                  onChange={(e) => setNewDecisionForm(prev => ({ ...prev, priority: e.target.value as 'high' | 'medium' | 'low' }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="high">High Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="low">Low Priority</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Category
                </label>
                <select
                  value={newDecisionForm.category}
                  onChange={(e) => setNewDecisionForm(prev => ({ ...prev, category: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="architecture">🏗️ Architecture</option>
                  <option value="technology">⚙️ Technology</option>
                  <option value="data">💾 Data</option>
                  <option value="integration">🔌 Integration</option>
                  <option value="security">🔒 Security</option>
                  <option value="performance">⚡ Performance</option>
                </select>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-2">
              <button
                onClick={handleCancelAddingDecision}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveNewDecision}
                disabled={!newDecisionForm.title.trim() || !newDecisionForm.description.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors cursor-pointer"
              >
                Add Decision
              </button>
            </div>
          </div>
        </div>
      )}

      {decisionPoints.length === 0 && (
        <div className="text-center py-8">
          <div className="text-6xl mb-4">🤔</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No decisions to review
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            It looks like there are no technical decisions identified for this task.
          </p>
          <p className="text-gray-600 dark:text-gray-400">
            You can add custom decision points using the button above.
          </p>
        </div>
      )}

      {/* Helper text */}
      <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
          💡 Review Tips
        </h4>
        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <li>• Remove decisions that have already been made in your organization</li>
          <li>• Remove decisions that are out of scope for this specific task</li>
          <li>• Add additional decision points if important ones are missing</li>
          <li>• Keep decisions that need active evaluation and choice-making</li>
          <li>• High priority decisions will be analyzed first in the next step</li>
          {isExperimental && (
            <>
              <li>• <span className="text-purple-600 dark:text-purple-400 font-medium">Experimental:</span> Focus on decisions that can be easily reversed or modified</li>
              <li>• <span className="text-purple-600 dark:text-purple-400 font-medium">Experimental:</span> Avoid decisions requiring data model or architectural changes</li>
            </>
          )}
        </ul>
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
        >
          ← Back
        </button>
        
        <button
          onClick={onContinue}
          disabled={isLoading || decisionPoints.length === 0}
          className="px-6 py-3 bg-amber-500 hover:bg-amber-600 disabled:bg-gray-400 text-white rounded-lg font-medium flex items-center space-x-2"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Analyzing Decisions...</span>
            </>
          ) : (
            <>
              <span>Analyze Decisions</span>
              <span>→</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
} 