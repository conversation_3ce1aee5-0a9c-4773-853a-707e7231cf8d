'use client'

import { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, SparklesIcon, PencilIcon, TrashIcon, PlusIcon, UserIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import type { WizardState, UserJourney } from '../../types/design-doc-wizard';

interface UserJourneyDefinitionStepProps {
  wizardState: WizardState;
  setWizardState: (updater: (prev: WizardState) => WizardState) => void;
  repositorySlug: string;
  installationId?: number;
  isPublic: boolean;
  onBack: () => void;
  onContinue: () => void;
  isLoading: boolean;
}

export default function UserJourneyDefinitionStep({
  wizardState,
  setWizardState,
  repositorySlug,
  installationId,
  isPublic,
  onBack,
  onContinue,
  isLoading
}: UserJourneyDefinitionStepProps) {
  const [isDrafting, setIsDrafting] = useState(false);
  const [editingJourneyId, setEditingJourneyId] = useState<string | null>(null);

  const userJourneys = wizardState.userJourneys || [];
  const confirmedJourneys = userJourneys.filter(j => !j.isDraft);
  const hasAnyJourneys = userJourneys.length > 0;

  // Draft user journeys from task details
  const handleDraftJourneys = async () => {
    if (!wizardState.taskDetails.title || !wizardState.taskDetails.description) {
      return;
    }

    setIsDrafting(true);
    try {
      const response = await fetch('/api/design-doc-wizard/draft-user-journeys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskDetails: wizardState.taskDetails,
          projectConstitution: wizardState.projectConstitution,
          repositorySlug,
          installationId,
          isPublic
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to draft user journeys: ${response.statusText}`);
      }

      const data = await response.json();
      
      setWizardState(prev => ({
        ...prev,
        userJourneys: data.userJourneys
      }));

    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        error: error.message
      }));
    } finally {
      setIsDrafting(false);
    }
  };

  // Confirm a draft journey
  const handleConfirmJourney = (journeyId: string) => {
    setWizardState(prev => ({
      ...prev,
      userJourneys: prev.userJourneys?.map(journey =>
        journey.id === journeyId
          ? { ...journey, isDraft: false, status: 'new' as const }
          : journey
      )
    }));
  };

  // Delete a journey
  const handleDeleteJourney = (journeyId: string) => {
    setWizardState(prev => ({
      ...prev,
      userJourneys: prev.userJourneys?.filter(journey => journey.id !== journeyId)
    }));
  };

  // Edit a journey
  const handleEditJourney = (journeyId: string, updates: Partial<UserJourney>) => {
    setWizardState(prev => ({
      ...prev,
      userJourneys: prev.userJourneys?.map(journey =>
        journey.id === journeyId
          ? { ...journey, ...updates, status: journey.isDraft ? 'draft' : 'modified' }
          : journey
      )
    }));
  };

  // Add a new journey manually
  const handleAddJourney = () => {
    const newJourney: UserJourney = {
      id: `journey_manual_${Date.now()}`,
      title: 'New User Journey',
      userRole: 'User',
      steps: ['Step 1: User action'],
      isDraft: false,
      status: 'new'
    };

    setWizardState(prev => ({
      ...prev,
      userJourneys: [...(prev.userJourneys || []), newJourney]
    }));
    setEditingJourneyId(newJourney.id);
  };

  const handleContinueClick = () => {
    if (isLoading) return;
    onContinue();
  };

  const canContinue = confirmedJourneys.length > 0;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
          Define User Journeys
        </h2>
        <p className="mt-2 text-lg text-gray-600 dark:text-gray-300">
          Clarify how users will interact with your feature to ensure technical decisions align with user needs
        </p>
      </div>

      {/* Current Task Context */}
      <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          Task: {wizardState.taskDetails.title}
        </h3>
        <p className="text-blue-800 dark:text-blue-200 text-sm">
          {wizardState.taskDetails.description}
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        {!hasAnyJourneys && (
          <button
            onClick={handleDraftJourneys}
            disabled={isDrafting}
            className="flex items-center px-4 py-2 bg-amber-600 hover:bg-amber-700 disabled:bg-amber-400 text-white rounded-lg transition-colors"
          >
            {isDrafting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <SparklesIcon className="h-4 w-4 mr-2" />
            )}
            {isDrafting ? 'Drafting...' : 'Draft User Journeys with AI'}
          </button>
        )}
        
        <button
          onClick={handleAddJourney}
          className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Journey Manually
        </button>
      </div>

      {/* User Journeys List */}
      {hasAnyJourneys && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            User Journeys ({confirmedJourneys.length} confirmed, {userJourneys.filter(j => j.isDraft).length} draft)
          </h3>
          
          <div className="space-y-4">
            {userJourneys.map((journey) => (
              <UserJourneyCard
                key={journey.id}
                journey={journey}
                isEditing={editingJourneyId === journey.id}
                onEdit={(updates) => handleEditJourney(journey.id, updates)}
                onStartEdit={() => setEditingJourneyId(journey.id)}
                onStopEdit={() => setEditingJourneyId(null)}
                onConfirm={() => handleConfirmJourney(journey.id)}
                onDelete={() => handleDeleteJourney(journey.id)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <button
          onClick={onBack}
          className="flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
        >
          <ChevronLeftIcon className="h-5 w-5 mr-2" />
          Back to Task Definition
        </button>

        <button
          onClick={handleContinueClick}
          disabled={!canContinue || isLoading}
          className="flex items-center px-6 py-3 bg-amber-600 hover:bg-amber-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
        >
          {isLoading ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
          ) : (
            <ChevronRightIcon className="h-5 w-5 mr-2" />
          )}
          Continue
        </button>
      </div>

      {!canContinue && hasAnyJourneys && (
        <p className="text-center text-sm text-gray-500 dark:text-gray-400">
          Please confirm at least one user journey to continue
        </p>
      )}
    </div>
  );
}

// Individual journey card component
interface UserJourneyCardProps {
  journey: UserJourney;
  isEditing: boolean;
  onEdit: (updates: Partial<UserJourney>) => void;
  onStartEdit: () => void;
  onStopEdit: () => void;
  onConfirm: () => void;
  onDelete: () => void;
}

function UserJourneyCard({
  journey,
  isEditing,
  onEdit,
  onStartEdit,
  onStopEdit,
  onConfirm,
  onDelete
}: UserJourneyCardProps) {
  const [editTitle, setEditTitle] = useState(journey.title);
  const [editUserRole, setEditUserRole] = useState(journey.userRole);
  const [editSteps, setEditSteps] = useState(journey.steps.join('\n'));

  const handleSave = () => {
    onEdit({
      title: editTitle,
      userRole: editUserRole,
      steps: editSteps.split('\n').filter(step => step.trim() !== '')
    });
    onStopEdit();
  };

  const handleCancel = () => {
    setEditTitle(journey.title);
    setEditUserRole(journey.userRole);
    setEditSteps(journey.steps.join('\n'));
    onStopEdit();
  };

  return (
    <div className={`border rounded-lg p-4 ${
      journey.isDraft 
        ? 'border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-900/30' 
        : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {isEditing ? (
            <div className="space-y-3">
              <input
                type="text"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Journey title"
              />
              <input
                type="text"
                value={editUserRole}
                onChange={(e) => setEditUserRole(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="User role (e.g., 'Admin', 'Guest User')"
              />
              <textarea
                value={editSteps}
                onChange={(e) => setEditSteps(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="One step per line"
              />
              <div className="flex gap-2">
                <button
                  onClick={handleSave}
                  className="flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm"
                >
                  <CheckIcon className="h-4 w-4 mr-1" />
                  Save
                </button>
                <button
                  onClick={handleCancel}
                  className="flex items-center px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm"
                >
                  <XMarkIcon className="h-4 w-4 mr-1" />
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {journey.title}
                </h4>
                {journey.isDraft && (
                  <span className="px-2 py-1 bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200 rounded text-xs font-medium">
                    DRAFT
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-1 mb-3 text-sm text-gray-600 dark:text-gray-400">
                <UserIcon className="h-4 w-4" />
                <span>{journey.userRole}</span>
              </div>

              <div className="space-y-1">
                {journey.steps.map((step, index) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <span className="text-gray-500 dark:text-gray-400 font-mono">
                      {index + 1}.
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">{step}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {!isEditing && (
          <div className="flex gap-2 ml-4">
            <button
              onClick={onStartEdit}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title="Edit journey"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            
            {journey.isDraft && (
              <button
                onClick={onConfirm}
                className="p-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                title="Confirm journey"
              >
                <CheckIcon className="h-4 w-4" />
              </button>
            )}
            
            <button
              onClick={onDelete}
              className="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
              title="Delete journey"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 