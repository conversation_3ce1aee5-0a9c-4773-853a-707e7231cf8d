import React, { useState } from 'react';
import type { 
  StrategicAssessmentPreferences, 
  StrategicAssessmentCriteria, 
  StrategicAssessmentMode
} from '../../types/design-doc-wizard';
import { DEFAULT_STRATEGIC_ASSESSMENT_PREFERENCES } from '../../types/design-doc-wizard';

interface StrategicAssessmentPreferencesStepProps {
  preferences: StrategicAssessmentPreferences;
  onUpdate: (preferences: StrategicAssessmentPreferences) => void;
  className?: string;
}

export default function StrategicAssessmentPreferencesStep({
  preferences,
  onUpdate,
  className = ''
}: StrategicAssessmentPreferencesStepProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const updateCriteria = (key: keyof StrategicAssessmentCriteria, value: any) => {
    onUpdate({
      ...preferences,
      requireReviewCriteria: {
        ...preferences.requireReviewCriteria,
        [key]: value
      }
    });
  };

  const updatePreference = (key: keyof StrategicAssessmentPreferences, value: any) => {
    onUpdate({
      ...preferences,
      [key]: value
    });
  };

  const addCustomKeyword = (keyword: string) => {
    if (keyword.trim() && !preferences.requireReviewCriteria.customKeywords.includes(keyword.trim())) {
      updateCriteria('customKeywords', [
        ...preferences.requireReviewCriteria.customKeywords,
        keyword.trim()
      ]);
    }
  };

  const removeCustomKeyword = (keyword: string) => {
    updateCriteria('customKeywords', 
      preferences.requireReviewCriteria.customKeywords.filter(k => k !== keyword)
    );
  };

  const resetToDefaults = () => {
    onUpdate(DEFAULT_STRATEGIC_ASSESSMENT_PREFERENCES);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Strategic Assessment Preferences
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Configure when to perform detailed strategic assessments vs. auto-approving features. 
          Strategic assessment evaluates if a feature should be built, documented, or deferred.
        </p>
      </div>

      {/* Assessment Mode */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-3">Assessment Approach</h4>
        
        <div className="space-y-3">
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              checked={preferences.assessmentMode === 'summary'}
              onChange={() => updatePreference('assessmentMode', 'summary')}
              className="text-blue-600 focus:ring-blue-500"
            />
            <div className="ml-3">
              <div className="font-medium text-gray-900 dark:text-white">Smart Assessment (Recommended)</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Auto-approve low-risk features, review high-impact strategic decisions
              </div>
            </div>
          </label>
          
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              checked={preferences.assessmentMode === 'detailed'}
              onChange={() => updatePreference('assessmentMode', 'detailed')}
              className="text-blue-600 focus:ring-blue-500"
            />
            <div className="ml-3">
              <div className="font-medium text-gray-900 dark:text-white">Review Everything</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Perform strategic assessment for every feature request
              </div>
            </div>
          </label>
          
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              checked={preferences.assessmentMode === 'auto-build'}
              onChange={() => updatePreference('assessmentMode', 'auto-build')}
              className="text-blue-600 focus:ring-blue-500"
            />
            <div className="ml-3">
              <div className="font-medium text-gray-900 dark:text-white">Auto-Build Most Features</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Proceed to design for most features, only review highest-risk items
              </div>
            </div>
          </label>
          
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              checked={preferences.assessmentMode === 'skip'}
              onChange={() => updatePreference('assessmentMode', 'skip')}
              className="text-blue-600 focus:ring-blue-500"
            />
            <div className="ml-3">
              <div className="font-medium text-gray-900 dark:text-white">Skip Strategic Assessment</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Skip strategic assessment entirely and go straight to design
              </div>
            </div>
          </label>
        </div>
      </div>

      {/* Strategic Review Criteria */}
      {['summary', 'auto-build'].includes(preferences.assessmentMode) && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <h4 className="font-medium text-yellow-900 dark:text-yellow-200 mb-3">
            What Requires Strategic Review?
          </h4>
          <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-4">
            Select the types of features that should require strategic assessment:
          </p>
          
          <div className="space-y-4">
            {/* Project Characteristics */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Project Characteristics</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.newProjectTypes}
                    onChange={(e) => updateCriteria('newProjectTypes', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">New project types/domains</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.highComplexityFeatures}
                    onChange={(e) => updateCriteria('highComplexityFeatures', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">High-complexity features</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.majorArchitecturalChanges}
                    onChange={(e) => updateCriteria('majorArchitecturalChanges', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Major architectural changes</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.crossTeamDependencies}
                    onChange={(e) => updateCriteria('crossTeamDependencies', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Cross-team dependencies</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.regulatoryCompliance}
                    onChange={(e) => updateCriteria('regulatoryCompliance', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Regulatory/compliance impact</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.performanceCritical}
                    onChange={(e) => updateCriteria('performanceCritical', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Performance-critical features</span>
                </label>
              </div>
            </div>

            {/* Business Impact */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Business Impact</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.highBusinessRisk}
                    onChange={(e) => updateCriteria('highBusinessRisk', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">High business risk</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.strategicInitiatives}
                    onChange={(e) => updateCriteria('strategicInitiatives', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Strategic initiatives</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.customerCommitments}
                    onChange={(e) => updateCriteria('customerCommitments', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Customer commitments</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.competitiveAdvantage}
                    onChange={(e) => updateCriteria('competitiveAdvantage', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Competitive advantage features</span>
                </label>
              </div>
            </div>

            {/* Resource & Timing */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Resource & Timing</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.longTermMaintenance}
                    onChange={(e) => updateCriteria('longTermMaintenance', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Long-term maintenance impact</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.irreversibleDecisions}
                    onChange={(e) => updateCriteria('irreversibleDecisions', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Irreversible decisions</span>
                </label>
              </div>
            </div>
            
            {/* Custom Keywords */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Custom Strategic Keywords</h5>
              <div className="flex flex-wrap gap-2 mb-2">
                {preferences.requireReviewCriteria.customKeywords.map((keyword) => (
                  <span key={keyword} className="px-2 py-1 bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 text-xs rounded flex items-center">
                    {keyword}
                    <button
                      onClick={() => removeCustomKeyword(keyword)}
                      className="ml-1 text-yellow-600 dark:text-yellow-300 hover:text-yellow-800 dark:hover:text-yellow-100"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input
                type="text"
                placeholder="Add strategic keyword..."
                className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    addCustomKeyword((e.target as HTMLInputElement).value);
                    (e.target as HTMLInputElement).value = '';
                  }
                }}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Press Enter to add keywords. Features containing these terms will require strategic review.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Advanced Settings */}
      <div>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          <span className={`mr-1 transition-transform ${showAdvanced ? 'rotate-90' : ''}`}>▶</span>
          Advanced Strategic Assessment Settings
        </button>
        
        {showAdvanced && (
          <div className="mt-3 space-y-4 pl-4 border-l-2 border-gray-200 dark:border-gray-600">
            <label className="flex items-center text-sm cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.autoBuildOnPositiveAssessment}
                onChange={(e) => updatePreference('autoBuildOnPositiveAssessment', e.target.checked)}
                className="text-blue-600 focus:ring-blue-500 rounded"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">Auto-proceed when assessment recommends BUILD</span>
            </label>
            
            <label className="flex items-center text-sm cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.skipForExperimentalFeatures}
                onChange={(e) => updatePreference('skipForExperimentalFeatures', e.target.checked)}
                className="text-blue-600 focus:ring-blue-500 rounded"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">Skip assessment for experimental features</span>
            </label>
            
            <label className="flex items-center text-sm cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.showAssessmentSummary}
                onChange={(e) => updatePreference('showAssessmentSummary', e.target.checked)}
                className="text-blue-600 focus:ring-blue-500 rounded"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">Show summary of strategic assessments</span>
            </label>
            
            {/* Confidence Threshold */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Minimum Confidence for Auto-Processing
              </label>
              <div className="space-y-2">
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="radio"
                    checked={preferences.confidenceLevelThreshold === 'any'}
                    onChange={() => updatePreference('confidenceLevelThreshold', 'any')}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Any confidence level</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="radio"
                    checked={preferences.confidenceLevelThreshold === 'medium'}
                    onChange={() => updatePreference('confidenceLevelThreshold', 'medium')}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Medium or high confidence only</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="radio"
                    checked={preferences.confidenceLevelThreshold === 'high'}
                    onChange={() => updatePreference('confidenceLevelThreshold', 'high')}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">High confidence only</span>
                </label>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Reset to Defaults */}
      <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={resetToDefaults}
          className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          Reset to Defaults
        </button>
        
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Strategic assessment preferences will be saved with your project constitution
        </div>
      </div>
    </div>
  );
} 