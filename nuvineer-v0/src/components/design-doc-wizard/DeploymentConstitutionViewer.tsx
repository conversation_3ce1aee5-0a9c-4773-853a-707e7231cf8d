'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Server, Database, Cloud, Zap, Monitor, HardDrive, Cpu, Calendar, ExternalLink, RefreshCw } from 'lucide-react';

// Simple Badge component
const Badge = ({ children, variant = "default", className = "" }: { 
  children: React.ReactNode; 
  variant?: "default" | "secondary" | "destructive" | "outline";
  className?: string;
}) => {
  const variantClasses = {
    default: "bg-blue-600 text-white hover:bg-blue-700",
    secondary: "bg-gray-100 text-gray-800 hover:bg-gray-200",
    destructive: "bg-red-600 text-white hover:bg-red-700",
    outline: "border border-gray-300 text-gray-700 hover:bg-gray-50"
  };

  return (
    <span 
      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors ${variantClasses[variant]} ${className}`}
    >
      {children}
    </span>
  );
};

interface DeploymentConstitutionData {
  provider: string | null;
  compute: Array<{ type: string }>;
  data_stores: Array<{ name: string; type: string }>;
  containerization: {
    type: string;
    base_image: string;
    exposed_ports: string[];
    build_args: string[];
    working_dir: string;
  } | null;
  deploymentPipeline: {
    deployment_steps: string[];
    deployed_to: string[];
    has_tests: boolean;
  } | null;
  hosting_services?: string[];
  deployment_tools?: string[];
  monitoring_tools?: string[];
  serverless_functions?: Array<{ name: string; provider: string }>;
  storage_services?: Array<{ name: string; type: string }>;
  compute_services?: Array<{ name: string; type: string }>;
}

interface DeploymentConstitutionViewerProps {
  deploymentConstitution: {
    constitutionData: DeploymentConstitutionData;
    createdAt: string;
    updatedAt: string;
    sourceRepoUrl?: string;
    sourceCommitHash?: string;
  } | null;
  isLoading?: boolean;
  error?: string;
  onRefresh?: () => void;
}

export default function DeploymentConstitutionViewer({
  deploymentConstitution,
  isLoading,
  error,
  onRefresh
}: DeploymentConstitutionViewerProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-600 bg-red-50 p-4 rounded-lg">
        <p className="font-medium">Error loading deployment context</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  if (!deploymentConstitution) {
    return (
      <div className="text-gray-600 bg-gray-50 p-6 rounded-lg text-center">
        <Cloud className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <p className="font-medium">No deployment context available</p>
        <p className="text-sm mt-1">Generate deployment context to see infrastructure details here.</p>
      </div>
    );
  }

  const { constitutionData, createdAt, updatedAt, sourceRepoUrl, sourceCommitHash } = deploymentConstitution;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderSection = (title: string, icon: React.ReactNode, items: string[] | Array<{ name: string; type?: string; provider?: string }> | null, emptyMessage: string) => {
    if (!items || items.length === 0) {
      return (
        <div className="text-sm text-gray-500 italic">
          {emptyMessage}
        </div>
      );
    }

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
          {icon}
          {title}
        </div>
        <div className="flex flex-wrap gap-2">
          {items.map((item, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {typeof item === 'string' ? item : `${item.name}${item.type ? ` (${item.type})` : ''}${item.provider ? ` - ${item.provider}` : ''}`}
            </Badge>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Deployment Context</h3>
          <p className="text-sm text-gray-600">
            Infrastructure and deployment configuration detected from your repository.
          </p>
        </div>
        {onRefresh && (
          <button
            onClick={onRefresh}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        )}
      </div>

      {/* Provider & Overview */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Server className="h-5 w-5 text-blue-600" />
            <h4 className="font-medium text-gray-900">Hosting Provider</h4>
          </div>
          {constitutionData.provider && (
            <Badge variant="default" className="bg-blue-100 text-blue-800">
              {constitutionData.provider}
            </Badge>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Compute */}
          {renderSection(
            'Compute Environment',
            <Cpu className="h-4 w-4" />,
            constitutionData.compute?.map(c => c.type) || [],
            'No compute environment detected'
          )}

          {/* Data Stores */}
          {renderSection(
            'Data Stores',
            <Database className="h-4 w-4" />,
            constitutionData.data_stores || [],
            'No data stores detected'
          )}
        </div>
      </Card>

      {/* Services */}
      <Card className="p-4">
        <h4 className="font-medium text-gray-900 mb-4">Cloud Services</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Serverless Functions */}
          {renderSection(
            'Serverless Functions',
            <Zap className="h-4 w-4" />,
            constitutionData.serverless_functions || [],
            'No serverless functions detected'
          )}

          {/* Storage Services */}
          {renderSection(
            'Storage Services',
            <HardDrive className="h-4 w-4" />,
            constitutionData.storage_services || [],
            'No storage services detected'
          )}

          {/* Hosting Services */}
          {renderSection(
            'Hosting Services',
            <Cloud className="h-4 w-4" />,
            constitutionData.hosting_services || [],
            'No hosting services detected'
          )}

          {/* Monitoring Tools */}
          {renderSection(
            'Monitoring & Observability',
            <Monitor className="h-4 w-4" />,
            constitutionData.monitoring_tools || [],
            'No monitoring tools detected'
          )}
        </div>
      </Card>

      {/* Containerization */}
      {constitutionData.containerization && (
        <Card className="p-4">
          <h4 className="font-medium text-gray-900 mb-4">Containerization</h4>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Type:</span>
              <Badge variant="secondary">{constitutionData.containerization.type}</Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Base Image:</span>
              <code className="text-xs bg-gray-100 px-2 py-1 rounded">{constitutionData.containerization.base_image}</code>
            </div>
            {constitutionData.containerization.exposed_ports.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Exposed Ports:</span>
                <div className="flex gap-1">
                  {constitutionData.containerization.exposed_ports.map((port, index) => (
                    <Badge key={index} variant="outline" className="text-xs">{port}</Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Deployment Pipeline */}
      {constitutionData.deploymentPipeline && (
        <Card className="p-4">
          <h4 className="font-medium text-gray-900 mb-4">Deployment Pipeline</h4>
          <div className="space-y-3">
            {constitutionData.deploymentPipeline.deployment_steps.length > 0 && (
              <div>
                <span className="text-sm font-medium text-gray-700 block mb-2">Deployment Steps:</span>
                <div className="space-y-1">
                  {constitutionData.deploymentPipeline.deployment_steps.map((step, index) => (
                    <code key={index} className="text-xs bg-gray-100 px-2 py-1 rounded block">{step}</code>
                  ))}
                </div>
              </div>
            )}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Has Tests:</span>
                <Badge variant={constitutionData.deploymentPipeline.has_tests ? "default" : "secondary"}>
                  {constitutionData.deploymentPipeline.has_tests ? "Yes" : "No"}
                </Badge>
              </div>
              {constitutionData.deploymentPipeline.deployed_to.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-700">Deployed To:</span>
                  <div className="flex gap-1">
                    {constitutionData.deploymentPipeline.deployed_to.map((target, index) => (
                      <Badge key={index} variant="outline" className="text-xs">{target}</Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Metadata */}
      <Card className="p-4 bg-gray-50">
        <h4 className="font-medium text-gray-900 mb-3">Analysis Details</h4>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <span>Generated: {formatDate(createdAt)}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <span>Updated: {formatDate(updatedAt)}</span>
          </div>
          {sourceRepoUrl && (
            <div className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              <a 
                href={sourceRepoUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline"
              >
                Source Repository
              </a>
            </div>
          )}
          {sourceCommitHash && (
            <div className="flex items-center gap-2">
              <span>Commit: </span>
              <code className="text-xs bg-gray-200 px-2 py-1 rounded">{sourceCommitHash.substring(0, 7)}</code>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
} 