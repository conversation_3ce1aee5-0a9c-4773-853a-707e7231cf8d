import React, { useState } from 'react';
import type { 
  DecisionReviewPreferences, 
  DecisionReviewCriteria, 
  DecisionReviewMode
} from '../../types/design-doc-wizard';
import { DEFAULT_REVIEW_PREFERENCES } from '../../types/design-doc-wizard';

interface DecisionReviewPreferencesStepProps {
  preferences: DecisionReviewPreferences;
  onUpdate: (preferences: DecisionReviewPreferences) => void;
  className?: string;
}

export default function DecisionReviewPreferencesStep({
  preferences,
  onUpdate,
  className = ''
}: DecisionReviewPreferencesStepProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const updateCriteria = (key: keyof DecisionReviewCriteria, value: any) => {
    onUpdate({
      ...preferences,
      requireReviewCriteria: {
        ...preferences.requireReviewCriteria,
        [key]: value
      }
    });
  };

  const updatePreference = (key: keyof DecisionReviewPreferences, value: any) => {
    onUpdate({
      ...preferences,
      [key]: value
    });
  };

  const addCustomKeyword = (keyword: string) => {
    if (keyword.trim() && !preferences.requireReviewCriteria.customKeywords.includes(keyword.trim())) {
      updateCriteria('customKeywords', [
        ...preferences.requireReviewCriteria.customKeywords,
        keyword.trim()
      ]);
    }
  };

  const removeCustomKeyword = (keyword: string) => {
    updateCriteria('customKeywords', 
      preferences.requireReviewCriteria.customKeywords.filter(k => k !== keyword)
    );
  };

  const resetToDefaults = () => {
    onUpdate(DEFAULT_REVIEW_PREFERENCES);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Decision Review Preferences
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Configure which decisions require your attention vs. which can be auto-processed. 
          This helps focus your time on the most impactful architectural decisions.
        </p>
      </div>

      {/* Review Mode */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-3">Review Approach</h4>
        
        <div className="space-y-3">
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              checked={preferences.defaultReviewMode === 'summary'}
              onChange={() => updatePreference('defaultReviewMode', 'summary')}
              className="text-blue-600 focus:ring-blue-500"
            />
            <div className="ml-3">
              <div className="font-medium text-gray-900 dark:text-white">Smart Review (Recommended)</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Auto-process low-impact decisions, review high-impact ones in detail
              </div>
            </div>
          </label>
          
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              checked={preferences.defaultReviewMode === 'detailed'}
              onChange={() => updatePreference('defaultReviewMode', 'detailed')}
              className="text-blue-600 focus:ring-blue-500"
            />
            <div className="ml-3">
              <div className="font-medium text-gray-900 dark:text-white">Review Everything</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Traditional mode - review every decision manually
              </div>
            </div>
          </label>
          
          <label className="flex items-center cursor-pointer">
            <input
              type="radio"
              checked={preferences.defaultReviewMode === 'auto'}
              onChange={() => updatePreference('defaultReviewMode', 'auto')}
              className="text-blue-600 focus:ring-blue-500"
            />
            <div className="ml-3">
              <div className="font-medium text-gray-900 dark:text-white">Auto-Process All</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Automatically select recommended options for all decisions
              </div>
            </div>
          </label>
        </div>
      </div>

      {/* High-Impact Criteria */}
      {preferences.defaultReviewMode === 'summary' && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <h4 className="font-medium text-yellow-900 dark:text-yellow-200 mb-3">
            What Requires Your Review?
          </h4>
          <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-4">
            Select the types of decisions that should require your detailed review:
          </p>
          
          <div className="space-y-3">
            {/* Technical Impact Criteria */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Technical Impact</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.newTechnologies}
                    onChange={(e) => updateCriteria('newTechnologies', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">New technologies/frameworks</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.schemaChanges}
                    onChange={(e) => updateCriteria('schemaChanges', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Database schema changes</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.architecturalChanges}
                    onChange={(e) => updateCriteria('architecturalChanges', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Architectural changes</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.securityChanges}
                    onChange={(e) => updateCriteria('securityChanges', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Security model changes</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.externalDependencies}
                    onChange={(e) => updateCriteria('externalDependencies', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">External dependencies</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.performanceImpact}
                    onChange={(e) => updateCriteria('performanceImpact', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Performance impact</span>
                </label>
              </div>
            </div>

            {/* Risk & Complexity Criteria */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Risk & Complexity</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.highRiskDecisions}
                    onChange={(e) => updateCriteria('highRiskDecisions', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">High risk decisions</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.highComplexityDecisions}
                    onChange={(e) => updateCriteria('highComplexityDecisions', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">High complexity decisions</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.irreversibleChanges}
                    onChange={(e) => updateCriteria('irreversibleChanges', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Irreversible changes</span>
                </label>
                
                <label className="flex items-center text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.requireReviewCriteria.deviatesFromStandards}
                    onChange={(e) => updateCriteria('deviatesFromStandards', e.target.checked)}
                    className="text-yellow-600 focus:ring-yellow-500 rounded"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Deviates from standards</span>
                </label>
              </div>
            </div>
            
            {/* Custom Keywords */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Custom Keywords</h5>
              <div className="flex flex-wrap gap-2 mb-2">
                {preferences.requireReviewCriteria.customKeywords.map((keyword) => (
                  <span key={keyword} className="px-2 py-1 bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 text-xs rounded flex items-center">
                    {keyword}
                    <button
                      onClick={() => removeCustomKeyword(keyword)}
                      className="ml-1 text-yellow-600 dark:text-yellow-300 hover:text-yellow-800 dark:hover:text-yellow-100"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input
                type="text"
                placeholder="Add custom keyword..."
                className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    addCustomKeyword((e.target as HTMLInputElement).value);
                    (e.target as HTMLInputElement).value = '';
                  }
                }}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Press Enter to add keywords. Decisions containing these terms will require review.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Advanced Settings */}
      <div>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          <span className={`mr-1 transition-transform ${showAdvanced ? 'rotate-90' : ''}`}>▶</span>
          Advanced Settings
        </button>
        
        {showAdvanced && (
          <div className="mt-3 space-y-4 pl-4 border-l-2 border-gray-200 dark:border-gray-600">
            <label className="flex items-center text-sm cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.reviewStepByStep}
                onChange={(e) => updatePreference('reviewStepByStep', e.target.checked)}
                className="text-blue-600 focus:ring-blue-500 rounded"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">Review each step manually</span>
            </label>
            
            <label className="flex items-center text-sm cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.showAutoProcessingSummary}
                onChange={(e) => updatePreference('showAutoProcessingSummary', e.target.checked)}
                className="text-blue-600 focus:ring-blue-500 rounded"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">Show summary of auto-processed decisions</span>
            </label>
            
            <label className="flex items-center text-sm cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.showProgressIndicators}
                onChange={(e) => updatePreference('showProgressIndicators', e.target.checked)}
                className="text-blue-600 focus:ring-blue-500 rounded"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">Show progress indicators</span>
            </label>
          </div>
        )}
      </div>

      {/* Reset to Defaults */}
      <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={resetToDefaults}
          className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          Reset to Defaults
        </button>
        
        <div className="text-xs text-gray-500 dark:text-gray-400">
          These preferences will be saved with your project constitution
        </div>
      </div>
    </div>
  );
} 