import React, { useState, useEffect } from 'react';
import type { DecisionPoint, DecisionOption, DecisionProcessingResult } from '../../types/design-doc-wizard';

interface DecisionOverrideModalProps {
  isOpen: boolean;
  decision: DecisionPoint | null;
  result: DecisionProcessingResult | null;
  onClose: () => void;
  onConfirm: (newOptionId: string, rationale: string) => void;
}

export default function DecisionOverrideModal({ isOpen, decision, result, onClose, onConfirm }: DecisionOverrideModalProps) {
  const [selectedOptionId, setSelectedOptionId] = useState<string | null>(null);
  const [rationale, setRationale] = useState('');

  useEffect(() => {
    if (decision) {
      // Don't set a default selected, force user to choose.
      setSelectedOptionId(null);
      setRationale('');
    }
  }, [decision]);

  if (!isOpen || !decision) {
    return null;
  }

  const handleConfirm = () => {
    if (selectedOptionId && rationale.trim()) {
      onConfirm(selectedOptionId, rationale);
    }
  };

  const recommendedOption = decision.options?.find(opt => opt.id === decision.recommended_option_id);
  const currentlySelectedOption = decision.options?.find(opt => opt.id === decision.selectedOption);

  const getRiskColor = (risk?: string) => {
    switch (risk?.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                🔄 Override Decision
              </h2>
              <h3 className="text-lg text-gray-700 dark:text-gray-300">{decision.title}</h3>
              <p className="text-gray-600 dark:text-gray-400 mt-1">{decision.description}</p>
            </div>
            <button 
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          {/* Why it was auto-processed (if we have classification data) */}
          {result?.classification && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                Why this decision was auto-processed:
              </h4>
              <div className="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
                <strong>Impact Score:</strong> {result.classification.impactScore}/100 (below review threshold)
              </div>
              {result.classification.reasons.length > 0 ? (
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  {result.classification.reasons.map((reason, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-1.5 h-1.5 bg-yellow-500 rounded-full mr-2 mt-1.5 flex-shrink-0"></span>
                      {reason}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  Low complexity decision suitable for automatic processing
                </p>
              )}
            </div>
          )}

          {/* Risk factors (if any) */}
          {result?.classification?.riskFactors && result.classification.riskFactors.length > 0 && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                Risk factors to consider:
              </h4>
              <div className="flex flex-wrap gap-2">
                {result.classification.riskFactors.map((factor, index) => (
                  <span key={index} className="px-2 py-1 bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200 text-xs rounded">
                    {factor}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Current Selection Info */}
          {currentlySelectedOption && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                Current Selection (Auto-Processed):
              </h4>
              <div className="flex items-center space-x-2">
                <span className="font-medium text-blue-900 dark:text-blue-100">{currentlySelectedOption.name}</span>
                {currentlySelectedOption.id === decision.recommended_option_id && (
                  <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300 text-xs rounded font-medium">
                    AI Recommended
                  </span>
                )}
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">{currentlySelectedOption.description}</p>
            </div>
          )}

          {/* Options */}
          <div className="mb-6">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Select a Different Option:</h4>
            
            <div className="space-y-4">
              {decision.options?.map((option) => {
                const isRecommended = option.id === decision.recommended_option_id;
                const isSelected = selectedOptionId === option.id;
                const isCurrent = option.id === decision.selectedOption;
                
                return (
                  <div key={option.id} className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    isSelected 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                      : isCurrent
                        ? 'border-gray-300 bg-gray-100 dark:bg-gray-700 opacity-60 cursor-not-allowed'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`} onClick={() => !isCurrent && setSelectedOptionId(option.id)}>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <input
                            type="radio"
                            checked={isSelected}
                            onChange={() => !isCurrent && setSelectedOptionId(option.id)}
                            disabled={isCurrent}
                            className="text-blue-600 disabled:opacity-50"
                          />
                          <h5 className="font-medium text-gray-900 dark:text-white">{option.name}</h5>
                          {isRecommended && (
                            <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300 text-xs rounded font-medium">
                              AI Recommended
                            </span>
                          )}
                          {isCurrent && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 text-xs rounded font-medium">
                              Current Selection
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{option.description}</p>
                        
                        {/* Pros and Cons */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                          {option.pros && option.pros.length > 0 && (
                            <div>
                              <h6 className="text-sm font-medium text-green-700 dark:text-green-300 mb-1">✓ Pros:</h6>
                              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                {option.pros.map((pro, index) => (
                                  <li key={index} className="flex items-start">
                                    <span className="text-green-500 mr-1 mt-0.5">+</span>
                                    {pro}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                          
                          {option.cons && option.cons.length > 0 && (
                            <div>
                              <h6 className="text-sm font-medium text-red-700 dark:text-red-300 mb-1">✗ Cons:</h6>
                              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                {option.cons.map((con, index) => (
                                  <li key={index} className="flex items-start">
                                    <span className="text-red-500 mr-1 mt-0.5">-</span>
                                    {con}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>

                        {/* Mitigations for recommended option */}
                        {isRecommended && option.mitigations && option.mitigations.length > 0 && (
                          <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                            <h6 className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">🛡️ Mitigations:</h6>
                            <ul className="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                              {option.mitigations.map((mitigation, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="text-blue-500 mr-1 mt-0.5">→</span>
                                  {mitigation}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {/* Technical Details */}
                        <div className="space-y-2">
                          {option.alignmentJustification && (
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              <strong>Alignment:</strong> {option.alignmentJustification}
                            </div>
                          )}
                          {option.debuggingComplexity && (
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              <strong>Debugging:</strong> {option.debuggingComplexity}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Risk and Complexity Badges */}
                      <div className="flex flex-col space-y-1 ml-4">
                        <span className={`px-2 py-1 text-xs rounded ${getRiskColor(option.riskLevel)}`}>
                          {option.riskLevel} risk
                        </span>
                        <span className={`px-2 py-1 text-xs rounded ${getRiskColor(option.complexity)}`}>
                          {option.complexity} complexity
                        </span>
                        {option.architecturalAlignment && (
                          <span className={`px-2 py-1 text-xs rounded ${
                            option.architecturalAlignment === 'high' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' :
                            option.architecturalAlignment === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300' :
                            'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300'
                          }`}>
                            {option.architecturalAlignment} alignment
                          </span>
                        )}
                        {option.maintenanceBurden && (
                          <span className={`px-2 py-1 text-xs rounded ${getRiskColor(option.maintenanceBurden)}`}>
                            {option.maintenanceBurden} maintenance
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Rationale */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Override Rationale <span className="text-red-500">*</span>
            </label>
            <textarea
              value={rationale}
              onChange={(e) => setRationale(e.target.value)}
              placeholder="Explain why you are overriding the AI's selection and choosing this alternative..."
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-blue-500"
              rows={4}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Provide context for your decision to help future team members understand the reasoning.
            </p>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={!selectedOptionId || !rationale.trim()}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 dark:disabled:bg-blue-800 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
            >
              Override Decision
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 