'use client';

import React, { useState, useEffect } from 'react';
import ConstitutionStep from './ConstitutionStep';
import DecisionReviewPreferencesStep from './DecisionReviewPreferencesStep';
import StrategicAssessmentPreferencesStep from './StrategicAssessmentPreferencesStep';
import DeploymentConstitutionViewer from './DeploymentConstitutionViewer';
import { Button } from '@/components/ui/button';
import { X, AlertTriangle } from 'lucide-react';
import { WizardState, ProjectConstitution, PriorityId, DEFAULT_REVIEW_PREFERENCES, DEFAULT_STRATEGIC_ASSESSMENT_PREFERENCES } from '@/types/design-doc-wizard';
import { isEqual } from 'lodash';

interface WizardSettingsProps {
  wizardState: WizardState;
  setWizardState: React.Dispatch<React.SetStateAction<WizardState>>;
  constitutionSeedText: string;
  setConstitutionSeedText: React.Dispatch<React.SetStateAction<string>>;
  handlePriorityChange: (priorityId: PriorityId, checked: boolean) => void;
  seedConstitution: () => void;
  saveConstitution: () => void;
  onClose: () => void;
  handleConstitutionUpdate: (constitution: ProjectConstitution) => void;
  repositorySlug?: string;
  installationId?: string;
}

type SettingsTab = 'priorities' | 'decision' | 'strategic' | 'personas' | 'deployment';

export default function WizardSettings({
  wizardState,
  setWizardState,
  constitutionSeedText,
  setConstitutionSeedText,
  handlePriorityChange,
  seedConstitution,
  saveConstitution,
  onClose,
  handleConstitutionUpdate,
  repositorySlug,
  installationId
}: WizardSettingsProps) {
  const [activeTab, setActiveTab] = useState<SettingsTab>('priorities');
  const [initialState, setInitialState] = useState<WizardState>(wizardState);
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] = useState(false);
  const [deploymentConstitution, setDeploymentConstitution] = useState<any>(null);
  const [isLoadingDeployment, setIsLoadingDeployment] = useState(false);
  const [deploymentError, setDeploymentError] = useState<string | undefined>();

  useEffect(() => {
    setInitialState(wizardState);
  }, []);

  // Fetch deployment constitution when deployment tab is accessed
  useEffect(() => {
    if (activeTab === 'deployment' && repositorySlug && !deploymentConstitution && !isLoadingDeployment) {
      fetchDeploymentConstitution();
    }
  }, [activeTab, repositorySlug, deploymentConstitution, isLoadingDeployment]);

  const fetchDeploymentConstitution = async () => {
    if (!repositorySlug) return;
    
    setIsLoadingDeployment(true);
    setDeploymentError(undefined);
    
    try {
      const response = await fetch(`/api/design-doc-wizard/deployment-constitution?repositorySlug=${repositorySlug}&installationId=${installationId || '0'}`);
      if (!response.ok) {
        throw new Error('Failed to fetch deployment constitution');
      }
      
      const data = await response.json();
      setDeploymentConstitution(data.hasDeploymentConstitution ? data.deploymentConstitution : null);
    } catch (error: any) {
      setDeploymentError(error.message);
    } finally {
      setIsLoadingDeployment(false);
    }
  };

  const hasUnsavedChanges = !isEqual(initialState.projectConstitution, wizardState.projectConstitution);

  const handleClose = () => {
    if (hasUnsavedChanges) {
      setShowUnsavedChangesDialog(true);
    } else {
      onClose();
    }
  };
  
  const handleSaveAndClose = () => {
    saveConstitution();
    onClose();
  };
  
  const handleDiscardAndClose = () => {
    setWizardState(initialState);
    setShowUnsavedChangesDialog(false);
    onClose();
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'priorities':
        return (
          <ConstitutionStep
            wizardState={wizardState}
            setWizardState={setWizardState as any}
            constitutionSeedText={constitutionSeedText}
            setConstitutionSeedText={setConstitutionSeedText}
            handlePriorityChange={handlePriorityChange as any}
            seedConstitution={seedConstitution}
            saveConstitution={saveConstitution}
          />
        );
      case 'decision':
        return wizardState.projectConstitution && (
          <DecisionReviewPreferencesStep
            preferences={wizardState.projectConstitution.decisionReviewPreferences || DEFAULT_REVIEW_PREFERENCES}
            onUpdate={(preferences) => {
              setWizardState(prev => ({
                ...prev,
                projectConstitution: {
                  ...prev.projectConstitution!,
                  decisionReviewPreferences: preferences
                }
              }));
            }}
          />
        );
      case 'strategic':
        return wizardState.projectConstitution && (
          <StrategicAssessmentPreferencesStep
            preferences={wizardState.projectConstitution.strategicAssessmentPreferences || DEFAULT_STRATEGIC_ASSESSMENT_PREFERENCES}
            onUpdate={(preferences) => {
              setWizardState(prev => ({
                ...prev,
                projectConstitution: {
                  ...prev.projectConstitution!,
                  strategicAssessmentPreferences: preferences
                }
              }));
            }}
          />
        );
      case 'personas':
        return (
          <div>
            <h3 className="text-lg font-medium text-gray-900">User Personas</h3>
            <p className="mt-1 text-sm text-gray-600">Define the key user personas for this project. This will help in generating more relevant user journeys.</p>
            <div className="mt-4">
              {wizardState.projectConstitution?.userPersonas?.map((persona, index) => (
                <div key={index} className="flex items-center gap-2 mb-2">
                  <input
                    type="text"
                    value={persona}
                    onChange={(e) => {
                      const newPersonas = [...(wizardState.projectConstitution?.userPersonas || [])];
                      newPersonas[index] = e.target.value;
                      setWizardState(prev => ({
                        ...prev,
                        projectConstitution: {
                          ...prev.projectConstitution!,
                          userPersonas: newPersonas
                        }
                      }));
                    }}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                  <button
                    onClick={() => {
                      const newPersonas = wizardState.projectConstitution?.userPersonas?.filter((_, i) => i !== index);
                      setWizardState(prev => ({
                        ...prev,
                        projectConstitution: {
                          ...prev.projectConstitution!,
                          userPersonas: newPersonas
                        }
                      }));
                    }}
                    className="p-2 text-red-500 hover:text-red-700"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              ))}
              <button
                onClick={() => {
                  const newPersonas = [...(wizardState.projectConstitution?.userPersonas || []), ''];
                  setWizardState(prev => ({
                    ...prev,
                    projectConstitution: {
                      ...prev.projectConstitution!,
                      userPersonas: newPersonas
                    }
                  }));
                }}
                className="mt-2 flex items-center px-3 py-1.5 border border-dashed border-gray-400 text-sm rounded-md hover:bg-gray-50"
              >
                Add Persona
              </button>
            </div>
          </div>
        );
      case 'deployment':
        return (
          <DeploymentConstitutionViewer
            deploymentConstitution={deploymentConstitution}
            isLoading={isLoadingDeployment}
            error={deploymentError}
            onRefresh={() => {
              setDeploymentConstitution(null);
              fetchDeploymentConstitution();
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-4xl w-full max-h-[90vh] flex flex-col relative">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-800"
        >
          <X className="h-6 w-6" />
        </Button>
        <div className="flex justify-between items-center mb-6 flex-shrink-0">
          <h2 className="text-2xl font-bold">Feature Wizard Settings</h2>
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleSaveAndClose}
              disabled={!hasUnsavedChanges || wizardState.isSavingConstitution}
              className="px-6 py-2 bg-amber-500 hover:bg-amber-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
            >
              {wizardState.isSavingConstitution ? 'Saving...' : 'Save & Close'}
            </Button>
            
          </div>
        </div>
        
        <div className="border-b border-gray-200 mb-6 flex-shrink-0">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('priorities')}
              className={`${
                activeTab === 'priorities'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Product Priorities
            </button>
            <button
              onClick={() => setActiveTab('personas')}
              className={`${
                activeTab === 'personas'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              User Personas
            </button>
            <button
              onClick={() => setActiveTab('decision')}
              className={`${
                activeTab === 'decision'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Decision Preferences
            </button>
            <button
              onClick={() => setActiveTab('strategic')}
              className={`${
                activeTab === 'strategic'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Strategic Assessment Preferences
            </button>
            <button
              onClick={() => setActiveTab('deployment')}
              className={`${
                activeTab === 'deployment'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Deployment
            </button>
          </nav>
        </div>

        <div className="overflow-y-auto">
          {renderContent()}
        </div>
        <div className="flex justify-end mt-4">
            <Button
              onClick={saveConstitution}
              disabled={!hasUnsavedChanges || wizardState.isSavingConstitution}
              className="px-6 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
            >
              {wizardState.isSavingConstitution ? 'Saving...' : 'Save'}
            </Button>
          </div>

        {showUnsavedChangesDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex justify-center items-center">
            <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full">
              <div className="flex items-start">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                  <AlertTriangle className="h-6 w-6 text-red-600" aria-hidden="true" />
                </div>
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Unsaved Changes
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      You have unsaved changes. Are you sure you want to discard them?
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <Button
                  onClick={handleDiscardAndClose}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Discard
                </Button>
                <Button
                  onClick={() => setShowUnsavedChangesDialog(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 