'use client'

import React, { useState } from 'react';
import { Share2, Check, Copy } from 'lucide-react';

interface ShareButtonProps {
  dbSessionId?: string;
  className?: string;
  variant?: 'default' | 'icon-only' | 'small';
  title?: string;
}

export default function ShareButton({ 
  dbSessionId, 
  className = '', 
  variant = 'default',
  title = 'Share this design document session'
}: ShareButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleShare = async () => {
    if (!dbSessionId) return;
    
    const shareUrl = `${window.location.origin}/design-doc-wizard/view/${dbSessionId}`;
    
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = shareUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  if (!dbSessionId) {
    return null;
  }

  const getButtonClass = () => {
    const baseClass = 'transition-colors';
    
    switch (variant) {
      case 'icon-only':
        return `${baseClass} p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700`;
      case 'small':
        return `${baseClass} inline-flex items-center space-x-1 px-2 py-1 text-xs font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded`;
      default:
        return `${baseClass} inline-flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md`;
    }
  };

  return (
    <button 
      onClick={handleShare}
      className={`${getButtonClass()} ${className}`}
      title={title}
    >
      {copied ? (
        <>
          <Check className={`${variant === 'small' ? 'h-3 w-3' : 'h-4 w-4'} text-green-600 dark:text-green-400`} />
          {variant !== 'icon-only' && (
            <span className="text-green-600 dark:text-green-400">
              {variant === 'small' ? 'Copied!' : 'Link Copied!'}
            </span>
          )}
        </>
      ) : (
        <>
          <Share2 className={`${variant === 'small' ? 'h-3 w-3' : 'h-4 w-4'}`} />
          {variant !== 'icon-only' && (
            <span>{variant === 'small' ? 'Share' : 'Share Session'}</span>
          )}
        </>
      )}
    </button>
  );
} 