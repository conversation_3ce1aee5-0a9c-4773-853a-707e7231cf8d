import React, { useEffect, useRef, useState, useMemo } from 'react';
import { DocumentTextIcon, PencilIcon, XMarkIcon, PrinterIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import { DecisionLinkedText, DecisionReference } from '../../utils/decisionUtils';
import DecisionDetailModal from '../DecisionDetailModal';
import { useDecisions, useProjectContextWithDebug } from '../../hooks/useDecisions';
import { formatDesignDocAsMarkdown, formatImplementationPlanAsMarkdown, formatDesignDocAsMarkdownForGitHub, formatImplementationPlanAsMarkdownForGitHub } from '../../utils/design-doc-wizard';

interface DesignDocumentViewerProps {
  designDoc: any;
  onClose: () => void;
  onAddToGitHubIssue?: () => Promise<{ success: boolean; error?: string; commentUrl?: string; issueNumber?: string; repository?: string; labelsAdded?: string[]; }>;
  onGenerateImplementationPlan?: () => Promise<{ success: boolean; error?: string; implementationPlan?: any; }>;
  onAddImplementationPlanToGitHub?: (plan: any) => Promise<{ success: boolean; error?: string; commentUrl?: string; issueNumber?: string; repository?: string; labelsAdded?: string[]; }>;
  taskDescription?: string;
  isPublic?: boolean;
  repositorySlug?: string;
  installationId?: string | number;
}

export default function DesignDocumentViewer({ 
  designDoc, 
  onClose, 
  onAddToGitHubIssue, 
  onGenerateImplementationPlan, 
  onAddImplementationPlanToGitHub, 
  taskDescription, 
  isPublic,
  repositorySlug,
  installationId
}: DesignDocumentViewerProps) {
  const mermaidContainerRef = useRef<HTMLDivElement>(null);
  const successMessageRef = useRef<HTMLDivElement>(null);
  const [isAddingToGitHub, setIsAddingToGitHub] = useState(false);
  const [githubResult, setGithubResult] = useState<{ success: boolean; error?: string; commentUrl?: string; issueNumber?: string; repository?: string; labelsAdded?: string[]; } | null>(null);
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [implementationPlan, setImplementationPlan] = useState<any>(null);
  const [isAddingPlanToGitHub, setIsAddingPlanToGitHub] = useState(false);
  const [planGithubResult, setPlanGithubResult] = useState<{ success: boolean; error?: string; commentUrl?: string; issueNumber?: string; repository?: string; labelsAdded?: string[]; } | null>(null);
  
  // New state for edit mode
  const [isEditMode, setIsEditMode] = useState(false);
  const [editableMarkdown, setEditableMarkdown] = useState('');
  const [isImplementationPlanEditMode, setIsImplementationPlanEditMode] = useState(false);
  const [editableImplementationPlanMarkdown, setEditableImplementationPlanMarkdown] = useState('');
  
  // Decision linking state
  const [selectedDecisionForModal, setSelectedDecisionForModal] = useState<string | null>(null);

  // Log received props for debugging
  useEffect(() => {
    console.log('[DesignDocumentViewer] Props received:', {
      repositorySlug,
      installationId
    });
  }, [repositorySlug, installationId]);

  // Check if task description contains a GitHub issue URL
  const hasGitHubIssue = taskDescription && taskDescription.includes('**GitHub Issue:**');
  const githubIssueMatch = taskDescription?.match(/\*\*GitHub Issue:\*\* https:\/\/github\.com\/([^\/]+)\/([^\/]+)\/issues\/(\d+)/);
  const issueInfo = githubIssueMatch ? {
    owner: githubIssueMatch[1],
    repo: githubIssueMatch[2],
    issueNumber: githubIssueMatch[3],
    url: `https://github.com/${githubIssueMatch[1]}/${githubIssueMatch[2]}/issues/${githubIssueMatch[3]}`
  } : null;

  // Auto-scroll to success message when it appears
  useEffect(() => {
    if ((githubResult?.success || planGithubResult?.success) && successMessageRef.current) {
      setTimeout(() => {
        successMessageRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      }, 100);
    }
  }, [githubResult?.success, planGithubResult?.success]);

  // Use project context with debugging
  const { repositorySlug: finalRepoSlug, installationId: finalInstallationId } = useProjectContextWithDebug(repositorySlug, installationId);
  
  // Collect all text that might contain decision references
  const textsToSearch = useMemo(() => {
    const allTexts: (string | string[])[] = [];
    
    if (designDoc) {
      // Goals and non-goals
      if (designDoc.goals) allTexts.push(designDoc.goals);
      if (designDoc.non_goals) allTexts.push(designDoc.non_goals);
      
      // High-level design
      if (designDoc.high_level_design) {
        if (designDoc.high_level_design.overall_system_overview) {
          allTexts.push(designDoc.high_level_design.overall_system_overview);
        }
        if (designDoc.high_level_design.core_architectural_choices) {
          designDoc.high_level_design.core_architectural_choices.forEach((choice: any) => {
            if (choice.recommended_approach_description) allTexts.push(choice.recommended_approach_description);
            if (choice.justification_and_context) allTexts.push(choice.justification_and_context);
          });
        }
        if (designDoc.high_level_design.data_model_changes) allTexts.push(designDoc.high_level_design.data_model_changes);
        if (designDoc.high_level_design.security_considerations) allTexts.push(designDoc.high_level_design.security_considerations);
        if (designDoc.high_level_design.error_handling_and_recovery) {
          if (designDoc.high_level_design.error_handling_and_recovery.critical_error_scenarios) {
            allTexts.push(designDoc.high_level_design.error_handling_and_recovery.critical_error_scenarios);
          }
          if (designDoc.high_level_design.error_handling_and_recovery.overall_strategy) {
            allTexts.push(designDoc.high_level_design.error_handling_and_recovery.overall_strategy);
          }
        }
      }
      
      // Alternatives analysis
      if (designDoc.alternatives_analysis) {
        if (designDoc.alternatives_analysis.key_technical_decision_alternatives) {
          designDoc.alternatives_analysis.key_technical_decision_alternatives.forEach((alt: any) => {
            if (alt.alternative_considered) allTexts.push(alt.alternative_considered);
            if (alt.reason_not_chosen) allTexts.push(alt.reason_not_chosen);
          });
        }
        if (designDoc.alternatives_analysis.overall_recommendation_and_justification) {
          allTexts.push(designDoc.alternatives_analysis.overall_recommendation_and_justification);
        }
      }
      
      // Implementation strategy
      if (designDoc.implementation_strategy) {
        if (designDoc.implementation_strategy.strategic_recommendations) {
          const rec = designDoc.implementation_strategy.strategic_recommendations;
          if (rec.immediate_focus) allTexts.push(rec.immediate_focus);
          if (rec.defer_until_later) allTexts.push(rec.defer_until_later);
          if (rec.complexity_concerns) allTexts.push(rec.complexity_concerns);
          if (rec.constitution_alignment) allTexts.push(rec.constitution_alignment);
        }
        if (designDoc.implementation_strategy.core_milestones) {
          designDoc.implementation_strategy.core_milestones.forEach((milestone: any) => {
            if (milestone.description) allTexts.push(milestone.description);
            if (milestone.user_value_delivered) allTexts.push(milestone.user_value_delivered);
            if (milestone.success_criteria) allTexts.push(milestone.success_criteria);
            if (milestone.dependencies) allTexts.push(milestone.dependencies);
            if (milestone.risks) allTexts.push(milestone.risks);
          });
        }
      }
      
      // Legacy milestones
      if (designDoc.milestones_sketch) allTexts.push(designDoc.milestones_sketch);
      
      // Success metrics
      if (designDoc.success_metrics) allTexts.push(designDoc.success_metrics);
      
      // Referenced decisions
      if (designDoc.referenced_decisions) {
        designDoc.referenced_decisions.forEach((decision: any) => {
          if (decision.summary_of_relevance_in_this_design) allTexts.push(decision.summary_of_relevance_in_this_design);
          if (decision.dev_prompt) allTexts.push(decision.dev_prompt);
          if (decision.follows_standard_practice_reason) allTexts.push(decision.follows_standard_practice_reason);
        });
      }
    }
    
    // Implementation plan texts
    if (implementationPlan) {
      if (implementationPlan.summary) allTexts.push(implementationPlan.summary);
      if (implementationPlan.data_model_summary) allTexts.push(implementationPlan.data_model_summary);
      if (implementationPlan.referenced_decisions) allTexts.push(implementationPlan.referenced_decisions);
      if (implementationPlan.overall_success_metrics) allTexts.push(implementationPlan.overall_success_metrics);
      
      if (implementationPlan.enhanced_milestones) {
        implementationPlan.enhanced_milestones.forEach((milestone: any) => {
          if (milestone.description) allTexts.push(milestone.description);
          if (milestone.key_tasks_and_deliverables) {
            allTexts.push(milestone.key_tasks_and_deliverables.map((task: any) => 
              typeof task === 'string' ? task : task.task || task.description || ''
            ));
          }
          if (milestone.scope_validation?.planned_deliverables) allTexts.push(milestone.scope_validation.planned_deliverables);
          if (milestone.verification_criteria) allTexts.push(milestone.verification_criteria);
          if (milestone.applicable_developer_guidance_and_best_practices) {
            milestone.applicable_developer_guidance_and_best_practices.forEach((guidance: any) => {
              if (guidance.guidance_summary) allTexts.push(guidance.guidance_summary);
            });
          }
        });
      }
    }
    
    return allTexts;
  }, [designDoc, implementationPlan]);

  // Fetch decisions based on references found in the text
  const { decisions, isLoading: isLoadingDecisions, error: decisionsError } = useDecisions({
    repositorySlug: finalRepoSlug,
    installationId: finalInstallationId,
    texts: textsToSearch
  });

  const handleDecisionClick = (decisionId: string) => {
    setSelectedDecisionForModal(decisionId);
  };

  const handleAddToGitHub = async () => {
    if (!onAddToGitHubIssue) return;
    
    setIsAddingToGitHub(true);
    setGithubResult(null);
    
    try {
      const result = await onAddToGitHubIssue();
      setGithubResult(result);
    } catch (error) {
      setGithubResult({ success: false, error: 'Failed to add to GitHub issue' });
    } finally {
      setIsAddingToGitHub(false);
    }
  };

  const handleGenerateImplementationPlan = async () => {
    if (!onGenerateImplementationPlan) return;
    
    setIsGeneratingPlan(true);
    setImplementationPlan(null);
    
    try {
      const result = await onGenerateImplementationPlan();
      if (result.success && result.implementationPlan) {
        setImplementationPlan(result.implementationPlan);
      } else {
        console.error('Failed to generate implementation plan:', result.error);
      }
    } catch (error) {
      console.error('Error generating implementation plan:', error);
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  const handleAddPlanToGitHub = async () => {
    if (!onAddImplementationPlanToGitHub || !implementationPlan) return;
    
    console.log('[DesignDocumentViewer] handleAddPlanToGitHub called');
    setIsAddingPlanToGitHub(true);
    setPlanGithubResult(null);
    
    try {
      console.log('[DesignDocumentViewer] Calling onAddImplementationPlanToGitHub with plan:', !!implementationPlan);
      const result = await onAddImplementationPlanToGitHub(implementationPlan);
      console.log('[DesignDocumentViewer] onAddImplementationPlanToGitHub result:', result);
      setPlanGithubResult(result);
    } catch (error) {
      console.error('[DesignDocumentViewer] Error in handleAddPlanToGitHub:', error);
      setPlanGithubResult({ success: false, error: 'Failed to add implementation plan to GitHub issue' });
    } finally {
      setIsAddingPlanToGitHub(false);
    }
  };

  // Initialize and render Mermaid diagrams
  useEffect(() => {
    const initializeMermaid = async () => {
      try {
        // Dynamically import mermaid
        const mermaid = await import('mermaid');
        
        // Initialize mermaid with dark theme support
        mermaid.default.initialize({
          startOnLoad: false,
          theme: 'default',
          themeVariables: {
            primaryColor: '#3b82f6',
            primaryTextColor: '#1f2937',
            primaryBorderColor: '#e5e7eb',
            lineColor: '#6b7280',
            secondaryColor: '#f3f4f6',
            tertiaryColor: '#ffffff',
            background: '#ffffff',
            mainBkg: '#ffffff',
            secondBkg: '#f9fafb',
            tertiaryBkg: '#f3f4f6'
          },
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
          },
          sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
          }
        });

        // Find all mermaid containers and render them
        const containers = document.querySelectorAll('.mermaid-diagram');
        containers.forEach(async (container, index) => {
          const element = container as HTMLElement;
          const diagramText = element.getAttribute('data-diagram');
          
          if (diagramText) {
            try {
              // Use the correct mermaid render API
              const renderResult = await mermaid.default.render(`mermaid-${index}`, diagramText);
              // Handle both string and object return types safely
              const svgContent = typeof renderResult === 'string' 
                ? renderResult 
                : (renderResult as any)?.svg || renderResult;
              element.innerHTML = svgContent;
            } catch (error) {
              console.error('Error rendering mermaid diagram:', error);
              element.innerHTML = `
                <div class="p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
                  <p class="text-red-800 dark:text-red-200 font-medium">Error rendering diagram</p>
                  <pre class="text-sm text-red-600 dark:text-red-400 mt-2 whitespace-pre-wrap">${diagramText}</pre>
                </div>
              `;
            }
          }
        });
      } catch (error) {
        console.error('Error loading mermaid:', error);
      }
    };

    if (designDoc && designDoc.high_level_design?.process_flow_diagram_mermaid) {
      initializeMermaid();
    }
  }, [designDoc, implementationPlan]);

  // Format design document content as proper markdown
  const formatDesignDocAsMarkdown = (doc: any): string => {
    if (!doc) return '';
    
    let markdown = `# ${doc.title || 'Design Document'}\n\n`;
    
    if (doc.goals && doc.goals.length > 0) {
      markdown += `## Goals\n\n`;
      doc.goals.forEach((goal: string) => {
        markdown += `- ${goal}\n`;
      });
      markdown += '\n';
    }
    
    if (doc.non_goals && doc.non_goals.length > 0) {
      markdown += `## Non-Goals\n\n`;
      doc.non_goals.forEach((nonGoal: string) => {
        markdown += `- ${nonGoal}\n`;
      });
      markdown += '\n';
    }

    if (doc.high_level_design) {
      markdown += `## High-Level Design\n\n`;
      
      if (doc.high_level_design.overall_system_overview) {
        markdown += `### System Overview\n\n${doc.high_level_design.overall_system_overview}\n\n`;
      }

      if (doc.high_level_design.core_architectural_choices && doc.high_level_design.core_architectural_choices.length > 0) {
        markdown += `### Core Architectural Decisions\n\n`;
        doc.high_level_design.core_architectural_choices.forEach((choice: any, index: number) => {
          markdown += `#### ${choice.title}\n\n`;
          markdown += `**Approach:** ${choice.recommended_approach_description}\n\n`;
          if (choice.justification_and_context) {
            markdown += `**Justification:** ${choice.justification_and_context}\n\n`;
          }
        });
      }

      if (doc.high_level_design.process_flow_diagram_mermaid && doc.high_level_design.process_flow_diagram_mermaid !== 'N/A') {
        markdown += `### Process Flow\n\n`;
        markdown += `\`\`\`mermaid\n${doc.high_level_design.process_flow_diagram_mermaid}\n\`\`\`\n\n`;
      }

      if (doc.high_level_design.key_components_and_responsibilities && doc.high_level_design.key_components_and_responsibilities.length > 0) {
        markdown += `### Key Components\n\n`;
        doc.high_level_design.key_components_and_responsibilities.forEach((component: any, index: number) => {
          markdown += `- **${component.name}:** ${component.responsibility}\n`;
        });
        markdown += '\n';
      }

      if (doc.high_level_design.data_model_changes && doc.high_level_design.data_model_changes !== 'N/A') {
        markdown += `### Data Model Changes\n\n${doc.high_level_design.data_model_changes}\n\n`;
      }

      if (doc.high_level_design.security_considerations) {
        markdown += `### Security Considerations\n\n${doc.high_level_design.security_considerations}\n\n`;
      }

      if (doc.high_level_design.error_handling_and_recovery) {
        markdown += `### Error Handling & Recovery\n\n`;
        if (doc.high_level_design.error_handling_and_recovery.critical_error_scenarios && doc.high_level_design.error_handling_and_recovery.critical_error_scenarios.length > 0) {
          markdown += `**Critical Error Scenarios:**\n\n`;
          doc.high_level_design.error_handling_and_recovery.critical_error_scenarios.forEach((scenario: string) => {
            markdown += `- ${scenario}\n`;
          });
          markdown += '\n';
        }
        if (doc.high_level_design.error_handling_and_recovery.overall_strategy) {
          markdown += `**Overall Strategy:**\n\n${doc.high_level_design.error_handling_and_recovery.overall_strategy}\n\n`;
        }
      }
    }

    if (doc.alternatives_analysis) {
      markdown += `## Alternatives Analysis\n\n`;
      


      if (doc.alternatives_analysis.key_technical_decision_alternatives && doc.alternatives_analysis.key_technical_decision_alternatives.length > 0) {
        markdown += `### Key Technical Decision Alternatives\n\n`;
        doc.alternatives_analysis.key_technical_decision_alternatives.forEach((alt: any, index: number) => {
          markdown += `#### ${alt.decision_point_title}\n\n`;
          markdown += `**Alternative Considered:** ${alt.alternative_considered}\n\n`;
          markdown += `**Reason Not Chosen:** ${alt.reason_not_chosen}\n\n`;
        });
      }

      if (doc.alternatives_analysis.overall_recommendation_and_justification) {
        markdown += `### Recommendation\n\n${doc.alternatives_analysis.overall_recommendation_and_justification}\n\n`;
      }
    }

    // Implementation Strategy - New Structured Format
    if (doc.implementation_strategy) {
      markdown += `## Implementation Strategy\n\n`;
      
      if (doc.implementation_strategy.strategic_recommendations) {
        markdown += `### Strategic Recommendations\n\n`;
        if (doc.implementation_strategy.strategic_recommendations.immediate_focus && doc.implementation_strategy.strategic_recommendations.immediate_focus.length > 0) {
          markdown += `**Immediate Focus:**\n\n`;
          doc.implementation_strategy.strategic_recommendations.immediate_focus.forEach((item: string) => {
            markdown += `- ${item}\n`;
          });
          markdown += '\n';
        }
        if (doc.implementation_strategy.strategic_recommendations.defer_until_later && doc.implementation_strategy.strategic_recommendations.defer_until_later.length > 0) {
          markdown += `**Defer Until Later:**\n\n`;
          doc.implementation_strategy.strategic_recommendations.defer_until_later.forEach((item: string) => {
            markdown += `- ${item}\n`;
          });
          markdown += '\n';
        }
        if (doc.implementation_strategy.strategic_recommendations.complexity_concerns && doc.implementation_strategy.strategic_recommendations.complexity_concerns.length > 0) {
          markdown += `**Complexity Concerns:**\n\n`;
          doc.implementation_strategy.strategic_recommendations.complexity_concerns.forEach((item: string) => {
            markdown += `- ${item}\n`;
          });
          markdown += '\n';
        }
        if (doc.implementation_strategy.strategic_recommendations.constitution_alignment) {
          markdown += `**Strategic Alignment:**\n\n${doc.implementation_strategy.strategic_recommendations.constitution_alignment}\n\n`;
        }
      }

      if (doc.implementation_strategy.core_milestones && doc.implementation_strategy.core_milestones.length > 0) {
        markdown += `### Core Milestones\n\n`;
        doc.implementation_strategy.core_milestones.forEach((milestone: any, index: number) => {
          markdown += `#### ${milestone.milestone_id || `M${index + 1}`}: ${milestone.title}\n\n`;
          markdown += `**Priority:** ${milestone.priority} | **Complexity:** ${milestone.estimated_complexity}\n\n`;
          markdown += `${milestone.description}\n\n`;
          
          if (milestone.user_value_delivered) {
            markdown += `**User Value:** ${milestone.user_value_delivered}\n\n`;
          }
          
          if (milestone.success_criteria && milestone.success_criteria.length > 0) {
            markdown += `**Success Criteria:**\n`;
            milestone.success_criteria.forEach((criteria: string) => {
              markdown += `- ${criteria}\n`;
            });
            markdown += '\n';
          }
          
          if (milestone.dependencies && milestone.dependencies.length > 0) {
            markdown += `**Dependencies:**\n`;
            milestone.dependencies.forEach((dep: string) => {
              markdown += `- ${dep}\n`;
            });
            markdown += '\n';
          }
          
          if (milestone.risks && milestone.risks.length > 0) {
            markdown += `**Risks:**\n`;
            milestone.risks.forEach((risk: string) => {
              markdown += `- ${risk}\n`;
            });
            markdown += '\n';
          }
        });
      }


    }

    // Legacy Implementation Milestones - Backward Compatibility
    if (doc.milestones_sketch && doc.milestones_sketch !== 'N/A' && !doc.implementation_strategy) {
      markdown += `## Implementation Milestones\n\n${doc.milestones_sketch}\n\n`;
    }

    if (doc.success_metrics && doc.success_metrics.length > 0) {
      markdown += `## Success Metrics\n\n`;
      doc.success_metrics.forEach((metric: string) => {
        markdown += `- ${metric}\n`;
      });
      markdown += '\n';
    }

    if (doc.referenced_decisions && doc.referenced_decisions.length > 0) {
      markdown += `## Referenced Decisions\n\n`;
      doc.referenced_decisions.forEach((decision: any) => {
        markdown += `### ${decision.title || 'Untitled Decision'}\n\n`;
        
        if (decision.id) {
          markdown += `**ID:** ${decision.id}\n\n`;
        }
        
        if (decision.summary_of_relevance_in_this_design) {
          markdown += `**Relevance to This Design:**\n${decision.summary_of_relevance_in_this_design}\n\n`;
        }
        
        if (decision.dev_prompt && decision.dev_prompt !== 'N/A') {
          markdown += `**🎯 Developer Guidance:**\n${decision.dev_prompt}\n\n`;
        }
        
        if (decision.follows_standard_practice_reason) {
          markdown += `**✅ Standard Practice:**\n${decision.follows_standard_practice_reason}\n\n`;
        }
        
        if (decision.related_files && decision.related_files.length > 0) {
          markdown += `**📁 Related Files:**\n`;
          decision.related_files.forEach((file: any, fileIndex: number) => {
            markdown += `- \`${typeof file === 'string' ? file : file.file_path}\`\n`;
          });
          markdown += '\n';
        }
        
        if (decision.domain_concepts && decision.domain_concepts.length > 0) {
          markdown += `**🏷️ Domain Concepts:** ${decision.domain_concepts.join(', ')}\n\n`;
        }
        
        markdown += '---\n\n';
      });
    }

    return markdown;
  };

  // Format implementation plan as markdown
  const formatImplementationPlanAsMarkdown = (plan: any): string => {
    if (!plan) return '';
    
    let markdown = `# ${plan.implementation_plan_title || 'Implementation Plan'}\n\n`;
    
    // Main Feature Branch
    if (plan.main_feature_branch_name) {
      markdown += `## Project Setup\n\n`;
      markdown += `**Main Feature Branch:** \`${plan.main_feature_branch_name}\`\n\n`;
      markdown += `This branch will accumulate all milestone work and serve as the integration point for the complete feature.\n\n`;
    }

    // AI Agent Protocol Compliance
    if (plan.ai_agent_protocol_compliance) {
      markdown += `## Implementation Protocol\n\n`;
      const compliance = plan.ai_agent_protocol_compliance;
      markdown += `**Protocol Source:** ${compliance.protocol_source === 'custom' ? 'Custom AI Agent Protocol' : 'Default Protocol'}\n\n`;
      markdown += `**Quality Assurance Features:**\n`;
      if (compliance.milestone_driven_approach) markdown += `- ✅ Milestone-driven development approach\n`;
      if (compliance.branch_naming_enforced) markdown += `- ✅ Standardized branch naming conventions\n`;
      if (compliance.independent_milestone_integration) markdown += `- ✅ Independent milestone integration capability\n`;
      if (compliance.quality_control_integrated) markdown += `- ✅ Built-in quality control at each milestone\n`;
      if (compliance.scope_validation_required) markdown += `- ✅ Mandatory scope validation before integration\n`;
      if (compliance.pre_integration_validation_required) markdown += `- ✅ Pre-integration validation and risk assessment\n`;
      markdown += `\n`;
    }

    // Enhanced Milestones
    if (plan.enhanced_milestones && plan.enhanced_milestones.length > 0) {
      markdown += `## Implementation Milestones\n\n`;
      markdown += `The implementation is broken down into ${plan.enhanced_milestones.length} sequential milestones, each independently testable and deployable.\n\n`;
      
      plan.enhanced_milestones.forEach((milestone: any, index: number) => {
        markdown += `### Milestone ${index + 1}: ${milestone.title || milestone.milestone_id}\n\n`;
        
        // Priority and Branch
        if (milestone.priority) {
          markdown += `**Priority:** ${milestone.priority}\n`;
        }
        if (milestone.git_must_use_milestone_branch_name) {
          markdown += `**Milestone Branch:** \`${milestone.git_must_use_milestone_branch_name}\`\n\n`;
        }
        
        // Description
        if (milestone.description) {
          markdown += `${milestone.description}\n\n`;
        }
        
        // Key Tasks and Deliverables
        if (milestone.key_tasks_and_deliverables && milestone.key_tasks_and_deliverables.length > 0) {
          markdown += `**Key Tasks and Deliverables:**\n`;
          milestone.key_tasks_and_deliverables.forEach((task: any) => {
            const taskText = typeof task === 'string' ? task : task.task || task.description || 'Task not specified';
            markdown += `- ${taskText}\n`;
          });
          markdown += `\n`;
        }
        
        // Scope Validation
        if (milestone.scope_validation) {
          markdown += `**Scope Validation:**\n`;
          if (milestone.scope_validation.planned_deliverables && milestone.scope_validation.planned_deliverables.length > 0) {
            markdown += `- **Planned Deliverables:**\n`;
            milestone.scope_validation.planned_deliverables.forEach((deliverable: string) => {
              markdown += `  - ${deliverable}\n`;
            });
          }
          markdown += `\n`;
        }
        
        // Verification Criteria
        if (milestone.verification_criteria && milestone.verification_criteria.length > 0) {
          markdown += `**Verification Criteria:**\n`;
          milestone.verification_criteria.forEach((criteria: string) => {
            markdown += `- ${criteria}\n`;
          });
          markdown += `\n`;
        }
        
        // Developer Guidance and Best Practices
        if (milestone.applicable_developer_guidance_and_best_practices && milestone.applicable_developer_guidance_and_best_practices.length > 0) {
          markdown += `**Applicable Developer Guidance:**\n`;
          milestone.applicable_developer_guidance_and_best_practices.forEach((guidance: any) => {
            markdown += `- **${guidance.retrieved_decision_title}**\n`;
            if (guidance.guidance_summary) {
              markdown += `  - ${guidance.guidance_summary}\n`;
            }
            if (guidance.related_files && guidance.related_files.length > 0) {
              markdown += `  - Related files: ${guidance.related_files.join(', ')}\n`;
            }
          });
          markdown += `\n`;
        }
        
        markdown += `---\n\n`;
      });
    }

    // Data Model Summary
    if (plan.data_model_summary && plan.data_model_summary !== 'N/A') {
      markdown += `## Data Model Changes\n\n`;
      markdown += `${plan.data_model_summary}\n\n`;
    }

    // Referenced Decisions
    if (plan.referenced_decisions && plan.referenced_decisions.length > 0) {
      markdown += `## Referenced Architectural Decisions\n\n`;
      plan.referenced_decisions.forEach((decision: string) => {
        markdown += `- ${decision}\n`;
      });
      markdown += `\n`;
    }

    // Overall Success Metrics
    if (plan.overall_success_metrics && plan.overall_success_metrics.length > 0) {
      markdown += `## Success Metrics\n\n`;
      plan.overall_success_metrics.forEach((metric: string) => {
        markdown += `- ${metric}\n`;
      });
      markdown += `\n`;
    }

    // Implementation Notes
    if (plan.full_design_doc_analyzed) {
      markdown += `## Implementation Notes\n\n`;
      markdown += `- ✅ Complete design document analyzed for comprehensive planning\n`;
      if (plan.finalized_design_doc_content_used) {
        markdown += `- ✅ Finalized design document content incorporated\n`;
      }
      markdown += `- ✅ Knowledge base integration for developer guidance\n`;
      markdown += `- ✅ Branch naming conventions enforced for consistency\n`;
      markdown += `- ✅ Quality control checkpoints built into each milestone\n\n`;
    }
    
    return markdown;
  };



  // Print functionality for the document content
  const handlePrint = () => {
    const printContent = implementationPlan ? formatImplementationPlanAsMarkdown(implementationPlan) : formatDesignDocAsMarkdown(designDoc);
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${implementationPlan ? 'Implementation Plan' : 'Design Document'} - ${designDoc?.title || 'Document'}</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; color: #1f2937 !important; background: white !important; }
            h1 { color: #1a202c !important; border-bottom: 2px solid #e2e8f0; padding-bottom: 10px; }
            h2 { color: #2d3748 !important; margin-top: 30px; }
            h3 { color: #4a5568 !important; }
            h4 { color: #4a5568 !important; }
            ul, ol { padding-left: 20px; }
            li { margin-bottom: 5px; color: #1f2937 !important; }
            p { color: #1f2937 !important; }
            strong { color: #1f2937 !important; }
            code { background: #f7fafc; padding: 2px 4px; border-radius: 3px; font-family: 'SF Mono', Monaco, monospace; color: #1f2937 !important; }
            pre { background: #f7fafc; padding: 15px; border-radius: 5px; overflow-x: auto; color: #1f2937 !important; }
            .mermaid-placeholder { background: #f0f9ff; border: 1px dashed #0ea5e9; padding: 20px; text-align: center; color: #0369a1 !important; margin: 20px 0; }
            @media print { body { margin: 0; padding: 15px; color: #000 !important; } }
          </style>
        </head>
        <body>
          ${convertMarkdownToHTML(printContent)}
        </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    }
  };

  // Save functionality for the document content
  const handleSave = () => {
    const content = implementationPlan ? formatImplementationPlanAsMarkdown(implementationPlan) : formatDesignDocAsMarkdown(designDoc);
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${implementationPlan ? 'implementation-plan' : 'design-document'}-${designDoc?.title?.toLowerCase().replace(/\s+/g, '-') || 'document'}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Convert markdown to HTML for printing (fixed regex)
  const convertMarkdownToHTML = (markdown: string): string => {
    let html = markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^#### (.*$)/gm, '<h4>$1</h4>')
      .replace(/^\- (.*$)/gm, '<li>$1</li>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      .replace(/```mermaid\n([\s\S]*?)\n```/g, '<div class="mermaid-placeholder">Mermaid Diagram: $1</div>')
      .replace(/```([\s\S]*?)```/g, '<pre>$1</pre>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^(?!<[hul])/gm, '<p>')
      .replace(/(?<!>)$/gm, '</p>');
    
    // Clean up empty paragraphs
    html = html.replace(/<p><\/p>/g, '');
    
    // Wrap consecutive list items in ul tags
    html = html.replace(/(<li>.*?<\/li>\s*)+/g, (match) => `<ul>${match}</ul>`);
    
    return html;
  };

  // Toggle edit mode
  const handleEditToggle = () => {
    if (!isEditMode) {
      // Entering edit mode - populate with current markdown
      const markdown = implementationPlan ? formatImplementationPlanAsMarkdown(implementationPlan) : formatDesignDocAsMarkdown(designDoc);
      setEditableMarkdown(markdown);
      setIsEditMode(true);
    } else {
      // Exiting edit mode - save changes (for now, just exit)
      handleSaveEdit();
    }
  };

  const handleImplementationPlanEditToggle = () => {
    if (!isImplementationPlanEditMode) {
      const markdown = formatImplementationPlanAsMarkdown(implementationPlan);
      setEditableImplementationPlanMarkdown(markdown);
      setIsImplementationPlanEditMode(true);
    } else {
      // For now, just exit. In the future, this would save.
      setIsImplementationPlanEditMode(false);
    }
  };

  // Save edited markdown (for now, just toggle back to view mode)
  const handleSaveEdit = () => {
    // TODO: Implement actual save functionality to update the document
    setIsEditMode(false);
    // For now, we'll just exit edit mode. In the future, this could update the document
  };

  // Render formatted design document (not raw markdown)
  const renderFormattedDesignDoc = (doc: any) => {
    return (
      <div className="space-y-8">
        {/* Title */}
        <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white force-dark-text">
            {doc.title || 'Design Document'}
          </h1>
        </div>

        {/* Goals */}
        {doc.goals && doc.goals.length > 0 && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Goals</h2>
            <ul className="space-y-2">
              {doc.goals.map((goal: string, index: number) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2 mt-1">✓</span>
                  <span className="text-gray-700 dark:text-gray-300">
                    <DecisionLinkedText 
                      text={goal}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className=""
                    />
                  </span>
                </li>
              ))}
            </ul>
          </section>
        )}

        {/* Non-Goals */}
        {doc.non_goals && doc.non_goals.length > 0 && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Non-Goals</h2>
            <ul className="space-y-2">
              {doc.non_goals.map((nonGoal: string, index: number) => (
                <li key={index} className="flex items-start">
                  <span className="text-red-500 mr-2 mt-1">✗</span>
                  <span className="text-gray-700 dark:text-gray-300">
                    <DecisionLinkedText 
                      text={nonGoal}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className=""
                    />
                  </span>
                </li>
              ))}
            </ul>
          </section>
        )}

        {/* High-Level Design */}
        {doc.high_level_design && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">High-Level Design</h2>
            
            {/* System Overview */}
            {doc.high_level_design.overall_system_overview && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">System Overview</h3>
                <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300">
                    <DecisionLinkedText 
                      text={doc.high_level_design.overall_system_overview}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className=""
                    />
                  </p>
                </div>
              </div>
            )}

            {/* Process Flow Diagram */}
            {doc.high_level_design.process_flow_diagram_mermaid && doc.high_level_design.process_flow_diagram_mermaid !== 'N/A' && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Process Flow</h3>
                <div className="bg-white dark:bg-gray-100 p-6 rounded-lg border border-gray-200 dark:border-gray-600 overflow-x-auto">
                  <div 
                    className="mermaid-diagram flex justify-center items-center min-h-[200px]"
                    data-diagram={doc.high_level_design.process_flow_diagram_mermaid}
                  >
                    <div className="text-gray-500 dark:text-gray-600">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                      <p className="text-sm">Rendering diagram...</p>
                    </div>
                  </div>
                  
                  <details className="mt-4">
                    <summary className="text-sm text-gray-600 dark:text-gray-500 cursor-pointer hover:text-gray-800 dark:hover:text-gray-300">
                      Show diagram source
                    </summary>
                    <pre className="mt-2 text-xs text-gray-600 dark:text-gray-500 bg-gray-50 dark:bg-gray-200 p-3 rounded overflow-x-auto">
                      {doc.high_level_design.process_flow_diagram_mermaid}
                    </pre>
                  </details>
                </div>
              </div>
            )}

            {/* Core Architectural Choices */}
            {doc.high_level_design.core_architectural_choices && doc.high_level_design.core_architectural_choices.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Key Architectural Decisions</h3>
                <div className="space-y-4">
                  {doc.high_level_design.core_architectural_choices.map((choice: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{choice.title}</h4>
                      <p className="text-gray-700 dark:text-gray-300 mb-2">
                        <DecisionLinkedText 
                          text={choice.recommended_approach_description}
                          decisions={decisions}
                          onDecisionClick={handleDecisionClick}
                          className=""
                        />
                      </p>
                      {choice.justification_and_context && (
                        <div className="text-sm text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-700 p-3 rounded border-l-4 border-blue-500">
                          <strong>Justification:</strong> <DecisionLinkedText 
                            text={choice.justification_and_context}
                            decisions={decisions}
                            onDecisionClick={handleDecisionClick}
                            className=""
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Key Components */}
            {doc.high_level_design.key_components_and_responsibilities && doc.high_level_design.key_components_and_responsibilities.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Key Components</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {doc.high_level_design.key_components_and_responsibilities.map((component: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">{component.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{component.responsibility}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Data Model Changes */}
            {doc.high_level_design.data_model_changes && doc.high_level_design.data_model_changes !== 'N/A' && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Data Model Changes</h3>
                <div className="bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                    <DecisionLinkedText 
                      text={doc.high_level_design.data_model_changes}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className=""
                    />
                  </p>
                </div>
              </div>
            )}

            {/* Security Considerations */}
            {doc.high_level_design.security_considerations && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Security Considerations</h3>
                <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300">
                    <DecisionLinkedText 
                      text={doc.high_level_design.security_considerations}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className=""
                    />
                  </p>
                </div>
              </div>
            )}

            {/* Error Handling and Recovery */}
            {doc.high_level_design.error_handling_and_recovery && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Error Handling & Recovery</h3>
                <div className="bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                                      {doc.high_level_design.error_handling_and_recovery.critical_error_scenarios && (
                      <div className="mb-4">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Critical Error Scenarios:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {doc.high_level_design.error_handling_and_recovery.critical_error_scenarios.map((scenario: string, index: number) => (
                            <li key={index} className="text-gray-700 dark:text-gray-300">
                              <DecisionLinkedText 
                                text={scenario}
                                decisions={decisions}
                                onDecisionClick={handleDecisionClick}
                                className=""
                              />
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {doc.high_level_design.error_handling_and_recovery.overall_strategy && (
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Overall Strategy:</h4>
                        <p className="text-gray-700 dark:text-gray-300">
                          <DecisionLinkedText 
                            text={doc.high_level_design.error_handling_and_recovery.overall_strategy}
                            decisions={decisions}
                            onDecisionClick={handleDecisionClick}
                            className=""
                          />
                        </p>
                      </div>
                    )}
                </div>
              </div>
            )}
          </section>
        )}

        {/* Alternatives Analysis */}
        {doc.alternatives_analysis && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Alternatives Analysis</h2>
            


            {/* Key Technical Decision Alternatives */}
            {doc.alternatives_analysis.key_technical_decision_alternatives && doc.alternatives_analysis.key_technical_decision_alternatives.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Key Technical Decision Alternatives</h3>
                <div className="space-y-3">
                  {doc.alternatives_analysis.key_technical_decision_alternatives.map((alt: any, index: number) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-yellow-50 dark:bg-yellow-900/30">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">{alt.decision_point_title}</h4>
                      <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                        <strong>Alternative Considered:</strong> {alt.alternative_considered}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        <strong>Reason Not Chosen:</strong> {alt.reason_not_chosen}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Overall Recommendation */}
            {doc.alternatives_analysis.overall_recommendation_and_justification && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Recommendation</h3>
                <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300">{doc.alternatives_analysis.overall_recommendation_and_justification}</p>
                </div>
              </div>
            )}
          </section>
        )}

        {/* Implementation Strategy - New Structured Format */}
        {doc.implementation_strategy && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Implementation Strategy</h2>
            
            {/* Strategic Recommendations */}
            {doc.implementation_strategy.strategic_recommendations && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Strategic Recommendations</h3>
                <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="space-y-3">
                    {doc.implementation_strategy.strategic_recommendations.immediate_focus && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Immediate Focus:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {doc.implementation_strategy.strategic_recommendations.immediate_focus.map((item: string, index: number) => (
                            <li key={index} className="text-blue-700 dark:text-blue-300">{item}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {doc.implementation_strategy.strategic_recommendations.defer_until_later && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Defer Until Later:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {doc.implementation_strategy.strategic_recommendations.defer_until_later.map((item: string, index: number) => (
                            <li key={index} className="text-blue-700 dark:text-blue-300">{item}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {doc.implementation_strategy.strategic_recommendations.complexity_concerns && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Complexity Concerns:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {doc.implementation_strategy.strategic_recommendations.complexity_concerns.map((item: string, index: number) => (
                            <li key={index} className="text-blue-700 dark:text-blue-300">{item}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {doc.implementation_strategy.strategic_recommendations.constitution_alignment && (
                      <div className="mt-3 p-3 bg-blue-100 dark:bg-blue-800 rounded">
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Product Alignment:</h4>
                        <p className="text-blue-700 dark:text-blue-300">{doc.implementation_strategy.strategic_recommendations.constitution_alignment}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Core Milestones */}
            {doc.implementation_strategy.core_milestones && doc.implementation_strategy.core_milestones.length > 0 && (
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200 mb-3">Core Milestones</h3>
                <div className="space-y-4">
                  {doc.implementation_strategy.core_milestones.map((milestone: any, index: number) => (
                    <div key={index} className="border border-green-200 dark:border-green-700 rounded-lg p-4 bg-green-50 dark:bg-green-900/30">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-green-900 dark:text-green-100">
                          {milestone.milestone_id || `M${index + 1}`}: {milestone.title}
                        </h4>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            milestone.priority === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                            milestone.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                            milestone.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          }`}>
                            {milestone.priority}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            milestone.estimated_complexity === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                            milestone.estimated_complexity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          }`}>
                            {milestone.estimated_complexity} complexity
                          </span>
                        </div>
                      </div>
                      <p className="text-green-700 dark:text-green-300 mb-3">{milestone.description}</p>
                      
                      {milestone.user_value_delivered && (
                        <div className="mb-3 p-3 bg-green-100 dark:bg-green-800 rounded">
                          <h5 className="font-medium text-green-800 dark:text-green-200 mb-1">User Value:</h5>
                          <p className="text-green-700 dark:text-green-300 text-sm">{milestone.user_value_delivered}</p>
                        </div>
                      )}
                      
                      {milestone.success_criteria && milestone.success_criteria.length > 0 && (
                        <div className="mb-3">
                          <h5 className="font-medium text-green-800 dark:text-green-200 mb-1">Success Criteria:</h5>
                          <ul className="list-disc list-inside space-y-1">
                            {milestone.success_criteria.map((criteria: string, criteriaIndex: number) => (
                              <li key={criteriaIndex} className="text-green-700 dark:text-green-300 text-sm">{criteria}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {milestone.dependencies && milestone.dependencies.length > 0 && (
                        <div className="mb-3">
                          <h5 className="font-medium text-green-800 dark:text-green-200 mb-1">Dependencies:</h5>
                          <ul className="list-disc list-inside space-y-1">
                            {milestone.dependencies.map((dep: string, depIndex: number) => (
                              <li key={depIndex} className="text-green-700 dark:text-green-300 text-sm">{dep}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {milestone.risks && milestone.risks.length > 0 && (
                        <div>
                          <h5 className="font-medium text-green-800 dark:text-green-200 mb-1">Risks:</h5>
                          <ul className="list-disc list-inside space-y-1">
                            {milestone.risks.map((risk: string, riskIndex: number) => (
                              <li key={riskIndex} className="text-green-700 dark:text-green-300 text-sm">{risk}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}


          </section>
        )}

        {/* Legacy Implementation Milestones - Backward Compatibility */}
        {doc.milestones_sketch && doc.milestones_sketch !== 'N/A' && !doc.implementation_strategy && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Implementation Milestones</h2>
            <div className="bg-indigo-50 dark:bg-indigo-900/30 border border-indigo-200 dark:border-indigo-800 rounded-lg p-4">
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                <DecisionLinkedText 
                  text={doc.milestones_sketch}
                  decisions={decisions}
                  onDecisionClick={handleDecisionClick}
                  className=""
                />
              </p>
            </div>
          </section>
        )}

        {/* Success Metrics */}
        {doc.success_metrics && doc.success_metrics.length > 0 && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Success Metrics</h2>
            <ul className="space-y-2">
              {doc.success_metrics.map((metric: string, index: number) => (
                <li key={index} className="flex items-start">
                  <span className="text-blue-500 mr-2 mt-1">📊</span>
                  <span className="text-gray-700 dark:text-gray-300">
                    <DecisionLinkedText 
                      text={metric}
                      decisions={decisions}
                      onDecisionClick={handleDecisionClick}
                      className=""
                    />
                  </span>
                </li>
              ))}
            </ul>
          </section>
        )}

        {/* Referenced Decisions */}
        {doc.referenced_decisions && doc.referenced_decisions.length > 0 && (
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Referenced Decisions</h2>
            <div className="space-y-4">
              {doc.referenced_decisions.map((decision: any, index: number) => (
                <div key={index} className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <div className="flex items-start">
                    <span className="text-yellow-600 dark:text-yellow-400 mr-3 mt-1">📋</span>
                    <div className="flex-1">
                      {/* Decision Title and ID */}
                      <div className="mb-3">
                        {decision.id && decisions[decision.id] ? (
                          <button
                            onClick={() => handleDecisionClick(decision.id)}
                            className="font-semibold text-gray-900 dark:text-white text-lg hover:text-blue-600 dark:hover:text-blue-400 underline cursor-pointer text-left"
                          >
                            {decision.title || 'Untitled Decision'}
                          </button>
                        ) : (
                          <h4 className="font-semibold text-gray-900 dark:text-white text-lg">
                            {decision.title || 'Untitled Decision'} <span className="text-gray-500 italic text-sm">(details not available)</span>
                          </h4>
                        )}
                        {decision.id && (
                          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            <strong>ID:</strong> {decision.id}
                          </div>
                        )}
                      </div>

                      {/* Summary of Relevance */}
                      {decision.summary_of_relevance_in_this_design && (
                        <div className="mb-3">
                          <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-1">Relevance to This Design:</h5>
                          <p className="text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 p-3 rounded border-l-4 border-yellow-500">
                            <DecisionLinkedText 
                              text={decision.summary_of_relevance_in_this_design}
                              decisions={decisions}
                              onDecisionClick={handleDecisionClick}
                              className=""
                            />
                          </p>
                        </div>
                      )}

                      {/* Developer Guidance */}
                      {decision.dev_prompt && decision.dev_prompt !== 'N/A' && (
                        <div className="mb-3">
                          <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-2">🎯 Developer Guidance:</h5>
                          <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded p-3">
                            <p className="text-sm text-blue-800 dark:text-blue-200">
                              <DecisionLinkedText 
                                text={decision.dev_prompt}
                                decisions={decisions}
                                onDecisionClick={handleDecisionClick}
                                className=""
                              />
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Best Practices Pattern */}
                      {decision.follows_standard_practice_reason && (
                        <div className="mb-3">
                          <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-2">✅ Standard Practice:</h5>
                          <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded p-3">
                            <p className="text-sm text-green-800 dark:text-green-200">
                              <DecisionLinkedText 
                                text={decision.follows_standard_practice_reason}
                                decisions={decisions}
                                onDecisionClick={handleDecisionClick}
                                className=""
                              />
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Related Files */}
                      {decision.related_files && decision.related_files.length > 0 && (
                        <div className="mb-3">
                          <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-2">📁 Related Files:</h5>
                          <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
                            <div className="flex flex-wrap gap-2">
                              {decision.related_files.map((file: any, fileIndex: number) => (
                                <code key={fileIndex} className="inline-block px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded text-xs font-mono">
                                  {typeof file === 'string' ? file : file.file_path}
                                </code>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    );
  };

  if (!designDoc) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Design Document</h2>
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>
            <p className="text-gray-600 dark:text-gray-400">No design document available to display.</p>
          </div>
        </div>
      </div>
    );
  }

  const documentMarkdown = formatDesignDocAsMarkdown(designDoc);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header with Action Buttons */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <DocumentTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white force-dark-text">
              {implementationPlan ? 'Implementation Plan' : 'Design Document'}
            </h2>
          </div>
          
          {/* Action Buttons - Now in Header */}
          <div className="flex items-center space-x-3">
            {implementationPlan ? (
              // Implementation Plan Actions
              <>
                <button
                  onClick={handlePrint}
                  className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white dark:bg-gray-600 dark:hover:bg-gray-700 rounded-lg flex items-center space-x-2 transition-colors cursor-pointer"
                  title="Print Implementation Plan"
                >
                  <PrinterIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={handleSave}
                  className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white dark:bg-gray-600 dark:hover:bg-gray-700 rounded-lg flex items-center space-x-2 transition-colors cursor-pointer"
                  title="Save Implementation Plan"
                >
                  <DocumentArrowDownIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={handleImplementationPlanEditToggle}
                  className={`px-4 py-2 ${isImplementationPlanEditMode ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} text-white dark:text-white rounded-lg flex items-center space-x-2 transition-colors cursor-pointer`}
                >
                  <PencilIcon className="h-4 w-4" />
                  <span>{isImplementationPlanEditMode ? 'Save Plan' : 'Edit Plan'}</span>
                </button>
                
                {onAddImplementationPlanToGitHub && !isEditMode && (
                  <button
                    onClick={handleAddPlanToGitHub}
                    disabled={isAddingPlanToGitHub || isPublic}
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 disabled:cursor-not-allowed cursor-pointer text-white dark:text-white rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    {isAddingPlanToGitHub ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white dark:border-white"></div>
                    ) : (
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                      </svg>
                    )}
                    <span>Add to GitHub</span>
                  </button>
                )}
              </>
            ) : (
              // Design Document Actions
              <>
                <button
                  onClick={handlePrint}
                  className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white dark:bg-gray-600 dark:hover:bg-gray-700 rounded-lg flex items-center space-x-2 transition-colors cursor-pointer"
                  title="Print Design Document"
                >
                  <PrinterIcon className="h-4 w-4" />
                </button>
                
                <button
                  onClick={handleSave}
                  className="px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white dark:bg-gray-600 dark:hover:bg-gray-700 rounded-lg flex items-center space-x-2 transition-colors cursor-pointer"
                  title="Save Design Document"
                >
                  <DocumentArrowDownIcon className="h-4 w-4" />
                </button>

                <button
                  onClick={handleEditToggle}
                  className={`px-4 py-2 ${isEditMode ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} text-white dark:text-white rounded-lg flex items-center space-x-2 transition-colors cursor-pointer`}
                >
                  <PencilIcon className="h-4 w-4" />
                  <span>{isEditMode ? 'Save Edit' : 'Edit Document'}</span>
                </button>

                {onAddToGitHubIssue && !isEditMode && (
                  <button
                    onClick={handleAddToGitHub}
                    disabled={isAddingToGitHub || isPublic}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed cursor-pointer text-white dark:text-white rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    {isAddingToGitHub ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white dark:border-white"></div>
                    ) : (
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                      </svg>
                    )}
                    <span>Add to GitHub</span>
                  </button>
                )}

                {onGenerateImplementationPlan && !isEditMode && (
                  <button
                    onClick={handleGenerateImplementationPlan}
                    disabled={isGeneratingPlan}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed cursor-pointer text-white dark:text-white rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    {isGeneratingPlan ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white dark:border-white"></div>
                    ) : (
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                      </svg>
                    )}
                    <span>Coding Agent Instructions</span>
                  </button>
                )}
              </>
            )}
            
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white dark:bg-gray-700 dark:hover:bg-gray-800 rounded-lg flex items-center space-x-2 transition-colors cursor-pointer"
            >
              <XMarkIcon className="h-4 w-4" />
              <span>Close</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 bg-white dark:bg-gray-800" id="design-doc-content">
          {/* Success/Error Messages */}
          {githubResult && (
            <div className={`mb-6 p-6 rounded-lg border-2 ${
              githubResult.success 
                ? 'bg-green-50 dark:bg-green-900/30 border-green-300 dark:border-green-700' 
                : 'bg-red-50 dark:bg-red-900/30 border-red-300 dark:border-red-700'
            }`} ref={githubResult.success ? successMessageRef : undefined}>
              <div className="flex items-start space-x-4">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  githubResult.success 
                    ? 'bg-green-500 text-white dark:bg-green-600' 
                    : 'bg-red-500 text-white dark:bg-red-600'
                }`}>
                  {githubResult.success ? '✓' : '✗'}
                </div>
                <div className="flex-1">
                  <h4 className={`text-lg font-semibold mb-2 ${
                    githubResult.success 
                      ? 'text-green-900 dark:text-green-100' 
                      : 'text-red-900 dark:text-red-100'
                  }`}>
                    {githubResult.success ? 'Design Document Added Successfully!' : 'Failed to Add Design Document'}
                  </h4>
                  <p className={`mb-3 ${
                    githubResult.success 
                      ? 'text-green-800 dark:text-green-200' 
                      : 'text-red-800 dark:text-red-200'
                  }`}>
                    {githubResult.success 
                      ? `Your design document has been added as a comment to GitHub issue #${githubResult.issueNumber} in the ${githubResult.repository} repository.${githubResult.labelsAdded && githubResult.labelsAdded.length > 0 ? ` Labels added: ${githubResult.labelsAdded.join(', ')}` : ''}` 
                      : githubResult.error
                    }
                  </p>
                  {githubResult.success && githubResult.commentUrl && (
                    <div className="flex flex-col sm:flex-row gap-3">
                      <a 
                        href={githubResult.commentUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white dark:bg-green-700 dark:hover:bg-green-800 rounded-lg font-medium transition-colors"
                      >
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                        </svg>
                        View Comment on GitHub
                      </a>
                      <a 
                        href={`https://github.com/${githubResult.repository}/issues/${githubResult.issueNumber}`}
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white dark:bg-gray-700 dark:hover:bg-gray-800 rounded-lg font-medium transition-colors"
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                        View Full Issue
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {planGithubResult && (
            <div className={`mb-6 p-6 rounded-lg border-2 ${
              planGithubResult.success 
                ? 'bg-purple-50 dark:bg-purple-900/30 border-purple-300 dark:border-purple-700' 
                : 'bg-red-50 dark:bg-red-900/30 border-red-300 dark:border-red-700'
            }`} ref={planGithubResult.success && !githubResult?.success ? successMessageRef : undefined}>
              <div className="flex items-start space-x-4">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  planGithubResult.success 
                    ? 'bg-purple-500 text-white dark:bg-purple-600' 
                    : 'bg-red-500 text-white dark:bg-red-600'
                }`}>
                  {planGithubResult.success ? '✓' : '✗'}
                </div>
                <div className="flex-1">
                  <h4 className={`text-lg font-semibold mb-2 ${
                    planGithubResult.success 
                      ? 'text-purple-900 dark:text-purple-100' 
                      : 'text-red-900 dark:text-red-100'
                  }`}>
                    {planGithubResult.success ? 'Implementation Plan Added Successfully!' : 'Failed to Add Implementation Plan'}
                  </h4>
                  <p className={`mb-3 ${
                    planGithubResult.success 
                      ? 'text-purple-800 dark:text-purple-200' 
                      : 'text-red-800 dark:text-red-200'
                  }`}>
                    {planGithubResult.success 
                      ? `Your implementation plan has been added as a comment to GitHub issue #${planGithubResult.issueNumber} in the ${planGithubResult.repository} repository.${planGithubResult.labelsAdded && planGithubResult.labelsAdded.length > 0 ? ` Labels added: ${planGithubResult.labelsAdded.join(', ')}` : ''}` 
                      : planGithubResult.error
                    }
                  </p>
                  {planGithubResult.success && planGithubResult.commentUrl && (
                    <div className="flex flex-col sm:flex-row gap-3">
                      <a 
                        href={planGithubResult.commentUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white dark:bg-purple-700 dark:hover:bg-purple-800 rounded-lg font-medium transition-colors"
                      >
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                        </svg>
                        View Comment on GitHub
                      </a>
                      <a 
                        href={`https://github.com/${planGithubResult.repository}/issues/${planGithubResult.issueNumber}`}
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white dark:bg-gray-700 dark:hover:bg-gray-800 rounded-lg font-medium transition-colors"
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                        View Full Issue
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {implementationPlan ? (
            isImplementationPlanEditMode ? (
              // Implementation Plan Edit Mode
              <div className="space-y-4">
                <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                    ✏️ Editing Implementation Plan
                  </h3>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    You are now editing the implementation plan markdown.
                  </p>
                </div>
                <textarea
                  value={editableImplementationPlanMarkdown}
                  onChange={(e) => setEditableImplementationPlanMarkdown(e.target.value)}
                  className="w-full h-[500px] p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Edit your implementation plan in markdown format..."
                />
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setIsImplementationPlanEditMode(false)}
                    className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white dark:bg-gray-600 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleImplementationPlanEditToggle}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white dark:text-white rounded-lg transition-colors cursor-pointer"
                  >
                    Save Plan
                  </button>
                </div>
              </div>
            ) : (
              // Show Implementation Plan Read-only
              <div className="space-y-6">
                <div className="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
                    🚀 {implementationPlan.implementation_plan_title || 'Enhanced Implementation Plan Generated'}
                  </h3>
                  
                  <div className="space-y-4">
                    {/* Main Feature Branch */}
                    <div>
                      <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Main Feature Branch:</h4>
                      <code className="px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded text-sm">
                        {implementationPlan.main_feature_branch_name}
                      </code>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        This branch will accumulate all milestone work and serve as the integration point for the complete feature.
                      </p>
                    </div>

                    {/* AI Agent Protocol Compliance */}
                    {implementationPlan.ai_agent_protocol_compliance && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Implementation Protocol:</h4>
                        <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded p-3">
                          <div className="text-sm text-green-800 dark:text-green-200 space-y-1">
                            <div>📋 Protocol: {implementationPlan.ai_agent_protocol_compliance.protocol_source === 'custom' ? 'Custom AI Agent Protocol' : 'Default Protocol'}</div>
                            {implementationPlan.ai_agent_protocol_compliance.milestone_driven_approach && <div>✅ Milestone-driven development approach</div>}
                            {implementationPlan.ai_agent_protocol_compliance.branch_naming_enforced && <div>✅ Standardized branch naming conventions</div>}
                            {implementationPlan.ai_agent_protocol_compliance.independent_milestone_integration && <div>✅ Independent milestone integration capability</div>}
                            {implementationPlan.ai_agent_protocol_compliance.quality_control_integrated && <div>✅ Built-in quality control at each milestone</div>}
                            {implementationPlan.ai_agent_protocol_compliance.scope_validation_required && <div>✅ Mandatory scope validation before integration</div>}
                            {implementationPlan.ai_agent_protocol_compliance.pre_integration_validation_required && <div>✅ Pre-integration validation and risk assessment</div>}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Enhanced Milestones */}
                    {implementationPlan.enhanced_milestones && implementationPlan.enhanced_milestones.length > 0 && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-3">
                          Implementation Milestones ({implementationPlan.enhanced_milestones.length}):
                        </h4>
                        <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                          The implementation is broken down into {implementationPlan.enhanced_milestones.length} sequential milestones, each independently testable and deployable.
                        </p>
                        <div className="space-y-4">
                          {implementationPlan.enhanced_milestones.map((milestone: any, index: number) => (
                            <div key={index} className="border border-blue-200 dark:border-blue-700 rounded p-4 bg-white dark:bg-gray-800">
                              <div className="flex items-center justify-between mb-3">
                                <h5 className="font-medium text-gray-900 dark:text-white">
                                  Milestone {index + 1}: {milestone.title || milestone.milestone_id}
                                </h5>
                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                  milestone.priority === 'P0' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                  milestone.priority === 'P1' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                }`}>
                                  {milestone.priority}
                                </span>
                              </div>
                              
                              {/* Milestone Branch */}
                              <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                <strong>Milestone Branch:</strong> <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded text-gray-900 dark:text-white">{milestone.git_must_use_milestone_branch_name}</code>
                              </div>
                              
                              {/* Description */}
                              {milestone.description && (
                                <div className="text-sm text-gray-700 dark:text-gray-300 mb-3">
                                  {milestone.description}
                                </div>
                              )}
                              
                              {/* Key Tasks and Deliverables */}
                              {milestone.key_tasks_and_deliverables && milestone.key_tasks_and_deliverables.length > 0 && (
                                <div className="mb-3">
                                  <strong className="text-sm text-gray-900 dark:text-white">Key Tasks and Deliverables:</strong>
                                  <ul className="list-disc list-inside mt-1 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                                    {milestone.key_tasks_and_deliverables.map((task: any, taskIndex: number) => (
                                      <li key={taskIndex}>{typeof task === 'string' ? task : task.task || task.description || 'Task not specified'}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                              
                              {/* Scope Validation */}
                              {milestone.scope_validation && milestone.scope_validation.planned_deliverables && milestone.scope_validation.planned_deliverables.length > 0 && (
                                <div className="mb-3">
                                  <strong className="text-sm text-gray-900 dark:text-white">Scope Validation - Planned Deliverables:</strong>
                                  <ul className="list-disc list-inside mt-1 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                                    {milestone.scope_validation.planned_deliverables.map((deliverable: string, delIndex: number) => (
                                      <li key={delIndex}>{deliverable}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                              
                              {/* Verification Criteria */}
                              {milestone.verification_criteria && milestone.verification_criteria.length > 0 && (
                                <div className="mb-3">
                                  <strong className="text-sm text-gray-900 dark:text-white">Verification Criteria:</strong>
                                  <ul className="list-disc list-inside mt-1 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                                    {milestone.verification_criteria.map((criteria: string, critIndex: number) => (
                                      <li key={critIndex}>{criteria}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                              
                              {/* Developer Guidance */}
                              {milestone.applicable_developer_guidance_and_best_practices && milestone.applicable_developer_guidance_and_best_practices.length > 0 && (
                                <div className="mb-3">
                                  <strong className="text-sm text-gray-900 dark:text-white">Applicable Developer Guidance:</strong>
                                  <div className="mt-2 space-y-2">
                                    {milestone.applicable_developer_guidance_and_best_practices.map((guidance: any, guidanceIndex: number) => (
                                      <div key={guidanceIndex} className="bg-gray-50 dark:bg-gray-700 rounded p-2">
                                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                                          {guidance.retrieved_decision_title}
                                        </div>
                                        {guidance.guidance_summary && (
                                          <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                            {guidance.guidance_summary}
                                          </div>
                                        )}
                                        {guidance.related_files && guidance.related_files.length > 0 && (
                                          <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                            Related files: {guidance.related_files.join(', ')}
                                          </div>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Data Model Summary */}
                    {implementationPlan.data_model_summary && implementationPlan.data_model_summary !== 'N/A' && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Data Model Changes:</h4>
                        <div className="bg-gray-50 dark:bg-gray-700 rounded p-3 text-sm text-gray-700 dark:text-gray-300">
                          {implementationPlan.data_model_summary}
                        </div>
                      </div>
                    )}

                    {/* Referenced Decisions */}
                    {implementationPlan.referenced_decisions && implementationPlan.referenced_decisions.length > 0 && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Referenced Architectural Decisions:</h4>
                        <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 dark:text-gray-300">
                          {implementationPlan.referenced_decisions.map((decision: string, decIndex: number) => (
                            <li key={decIndex}>{decision}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Success Metrics */}
                    {implementationPlan.overall_success_metrics && implementationPlan.overall_success_metrics.length > 0 && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Success Metrics:</h4>
                        <ul className="list-disc list-inside space-y-1 text-sm text-gray-700 dark:text-gray-300">
                          {implementationPlan.overall_success_metrics.map((metric: string, metricIndex: number) => (
                            <li key={metricIndex}>{metric}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Implementation Notes */}
                    {implementationPlan.full_design_doc_analyzed && (
                      <div>
                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Implementation Notes:</h4>
                        <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded p-3">
                          <div className="text-sm text-green-800 dark:text-green-200 space-y-1">
                            <div>✅ Complete design document analyzed for comprehensive planning</div>
                            {implementationPlan.finalized_design_doc_content_used && <div>✅ Finalized design document content incorporated</div>}
                            <div>✅ Knowledge base integration for developer guidance</div>
                            <div>✅ Branch naming conventions enforced for consistency</div>
                            <div>✅ Quality control checkpoints built into each milestone</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          ) : isEditMode ? (
            // Show Edit Mode
            <div className="space-y-4">
              <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
                  ✏️ Editing Mode
                </h3>
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  You are now editing the raw markdown content. Click "Save Edit" to return to the formatted view.
                </p>
              </div>
              
              <div className="relative">
                <textarea
                  value={editableMarkdown}
                  onChange={(e) => setEditableMarkdown(e.target.value)}
                  className="w-full h-[500px] p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Edit your document in markdown format..."
                />
                <div className="absolute bottom-4 right-4 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 px-2 py-1 rounded">
                  {editableMarkdown.split('\n').length} lines
                </div>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setIsEditMode(false)}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white dark:bg-gray-600 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveEdit}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white dark:text-white rounded-lg transition-colors cursor-pointer"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            // Show Formatted Design Document
            <div className="max-w-none">
              {renderFormattedDesignDoc(designDoc)}
            </div>
          )}
        </div>
      </div>
      
      {/* Decision Detail Modal */}
      <DecisionDetailModal
        decision={selectedDecisionForModal ? decisions[selectedDecisionForModal] : null}
        isOpen={!!selectedDecisionForModal}
        onClose={() => setSelectedDecisionForModal(null)}
      />
    </div>
  );
} 