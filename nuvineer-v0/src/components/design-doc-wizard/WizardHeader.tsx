'use client'

import React from 'react';
import { ChevronR<PERSON>, Settings, ArrowLeft } from 'lucide-react';
import ShareButton from './ShareButton';

interface WizardHeaderProps {
  sessionId?: string;
  dbSessionId?: string;
  currentStep: string;
  onShowSettings: () => void;
  onBackToDashboard: () => void;
}

export function WizardHeader({
  sessionId,
  dbSessionId,
  currentStep,
  onShowSettings,
  onBackToDashboard,
}: WizardHeaderProps) {
  const getStepClass = (stepName: string) => {
    const baseClass = 'text-sm font-medium transition-colors';
    const activeClass = 'text-gray-900';
    const inactiveClass = 'text-gray-500 hover:text-gray-700';

    const stepOrder = [
      'constitution',
      'task-definition',
      'user-journey-definition',
      'requirements-review',
      'task-verification',
      'strategic-assessment',
      'decision-discovery',
      'decision-making',
      'review-generation',
    ];
    
    const currentIndex = stepOrder.indexOf(currentStep);
    const stepIndex = stepOrder.indexOf(stepName);

    if (stepIndex < currentIndex) {
      return `${baseClass} ${activeClass}`; // Completed
    }
    if (stepIndex === currentIndex) {
      return `${baseClass} ${activeClass}`; // Active
    }
    return `${baseClass} ${inactiveClass}`; // Inactive
  };

  return (
    <header className="bg-white shadow-sm">
      <div className="mx-auto max-w-7xl px-4 py-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          <div className="flex-shrink-0">
            <h1 className="text-lg font-bold text-gray-900">
              Feature Wizard
            </h1>
          </div>
          <div className="flex items-center space-x-4">
            <ShareButton 
              dbSessionId={dbSessionId} 
              variant="default"
              title="Share this design document session with your team"
            />
            {sessionId && (
              <button onClick={onShowSettings} className="inline-flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100">
                <Settings className="h-5 w-5 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">Settings</span>
              </button>
            )}
            <button onClick={onBackToDashboard} className="inline-flex items-center space-x-2 text-sm font-medium text-gray-600 hover:text-gray-800">
              <ArrowLeft className="h-5 w-5 text-gray-600" />
              <span>Home</span>
            </button>
          </div>
        </div>
        {sessionId && (
          <div className="hidden md:flex items-center space-x-2 border-t border-gray-200 mt-4 pt-4">
            <span className={getStepClass('constitution')}>Context</span>
            <ChevronRight className="h-5 w-5 text-gray-400" />
            <span className={getStepClass('task-definition')}>Define</span>
            <ChevronRight className="h-5 w-5 text-gray-400" />
            <span className={getStepClass('user-journey-definition')}>Journeys</span>
            <ChevronRight className="h-5 w-5 text-gray-400" />
            <span className={getStepClass('requirements-review')}>Review</span>
            <ChevronRight className="h-5 w-5 text-gray-400" />
            <span className={getStepClass('task-verification')}>Verify</span>
            <ChevronRight className="h-5 w-5 text-gray-400" />
            <span className={getStepClass('strategic-assessment')}>Assess</span>
            <ChevronRight className="h-5 w-5 text-gray-400" />
            <span className={getStepClass('decision-discovery')}>Discover</span>
            <ChevronRight className="h-5 w-5 text-gray-400" />
            <span className={getStepClass('decision-making')}>Decide</span>
            <ChevronRight className="h-5 w-5 text-gray-400" />
            <span className={getStepClass('review-generation')}>Generate</span>
          </div>
        )}
      </div>
    </header>
  );
} 