'use client'

import React, { useState } from 'react';

interface TaskVerificationStepProps {
  wizardState: any;
  editing: {
    toggleRequirementsEditMode: () => void;
    updateFunctionalRequirement: (index: number, value: string) => void;
    removeFunctionalRequirement: (index: number) => void;
    addFunctionalRequirement: () => void;
    updateFunctionalRequirementsDiagram: (value: string) => void;
    updateNFR: (index: number, field: "nfr" | "evidence", value: string) => void;
    removeNFR: (index: number) => void;
    addNFR: () => void;
    updateArchCharacteristic: (index: number, value: string) => void;
    removeArchCharacteristic: (index: number) => void;
    addArchCharacteristic: () => void;
    toggleNonGoalsEditMode: () => void;
    updateNonGoalAtIndex: (index: number, value: string) => void;
    removeNonGoal: (index: number) => void;
    addNonGoal: () => void;
  };
  onBack: () => void;
  onContinue: () => void;
  isLoading: boolean;
}

export default function TaskVerificationStep({
  wizardState,
  editing,
  onBack,
  onContinue,
  isLoading
}: TaskVerificationStepProps) {
  const [expandedSections, setExpandedSections] = useState({
    summary: true,
    functional: true,
    nfrs: false,
    architecture: true,
    nonGoals: false
  });




  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const functionalReqs = wizardState.taskAnalysis?.functional_requirements || [];
  const nfrs = wizardState.taskAnalysis?.implied_NFRs || [];
  const archChars = wizardState.taskAnalysis?.core_architectural_characteristics || [];
  const nonGoals = wizardState.nonGoals || [];




  
  // Calculate total requirements count for summary
  const totalFunctionalReqs = functionalReqs.length;

  const isExperimental = wizardState.taskDetails?.isExperimental || false;

  // Categorize NFRs for better organization
  const categorizeNFRs = (nfrList: any[]) => {
    type NFRItem = { nfr: string; evidence: string; index: number };
    type Category = { icon: string; color: string; items: NFRItem[] };
    
    const categories: Record<string, Category> = {
      performance: { icon: '⚡', color: 'bg-green-50 border-green-200', items: [] },
      security: { icon: '🔒', color: 'bg-red-50 border-red-200', items: [] },
      usability: { icon: '👤', color: 'bg-blue-50 border-blue-200', items: [] },
      reliability: { icon: '🛡️', color: 'bg-purple-50 border-purple-200', items: [] },
      scalability: { icon: '📈', color: 'bg-orange-50 border-orange-200', items: [] },
      other: { icon: '⚙️', color: 'bg-gray-50 border-gray-200', items: [] }
    };

    nfrList.forEach((nfr, index) => {
      const text = nfr.nfr.toLowerCase();
      const item: NFRItem = { ...nfr, index };
      
      if (text.includes('performance') || text.includes('speed') || text.includes('latency')) {
        categories.performance.items.push(item);
      } else if (text.includes('security') || text.includes('auth') || text.includes('encrypt')) {
        categories.security.items.push(item);
      } else if (text.includes('usability') || text.includes('user') || text.includes('interface')) {
        categories.usability.items.push(item);
      } else if (text.includes('reliability') || text.includes('uptime') || text.includes('available')) {
        categories.reliability.items.push(item);
      } else if (text.includes('scalability') || text.includes('scale') || text.includes('growth')) {
        categories.scalability.items.push(item);
      } else {
        categories.other.items.push(item);
      }
    });

    return categories;
  };

  const nfrCategories = categorizeNFRs(nfrs);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold">Task Analysis Review</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {isExperimental ? 'Experimental feature analysis' : 'Production feature analysis'}
          </p>
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Step 4 of 8
        </div>
      </div>

      {/* Quick Summary Card */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-blue-600 dark:text-blue-400 font-medium">
                📋 Analysis Summary
              </span>
              {isExperimental && (
                <span className="bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 px-2 py-1 rounded-full text-xs font-medium">
                  Experimental
                </span>
              )}
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
              {wizardState.taskAnalysis?.summary || 'No summary available'}
            </p>
            <div className="flex items-center space-x-6 text-xs">
              <div className="flex items-center space-x-1">
                <span className="text-green-600">✓</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {totalFunctionalReqs} functional requirements
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <span className="text-blue-600">⚡</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {nfrs.length} quality requirements
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <span className="text-purple-600">🏗️</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {archChars.length} architectural characteristics
                </span>
              </div>
            </div>
          </div>
          <button
            onClick={() => toggleSection('summary')}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 p-1"
          >
            {expandedSections.summary ? '🔽' : '▶️'}
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {/* Functional Requirements */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
          <div 
            className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 cursor-pointer"
            onClick={() => toggleSection('functional')}
          >
            <div className="flex items-center space-x-3">
              <span className="text-xl">{expandedSections.functional ? '🔽' : '▶️'}</span>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Functional Requirements
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {totalFunctionalReqs} requirements • What the system must do
                </p>
              </div>
            </div>
            <div className="flex items-center">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  editing.toggleRequirementsEditMode();
                }}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                {wizardState.isEditingRequirements ? 'Done' : 'Edit'}
              </button>
            </div>
          </div>
          
          {expandedSections.functional && (
            <div className="p-4">
              <div className="space-y-2">
                {functionalReqs.map((req: string, index: number) => (
                  <div key={index} className="flex items-start space-x-3 p-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-4 h-4 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                        <span className="text-green-600 dark:text-green-400 text-xs">✓</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      {wizardState.isEditingRequirements ? (
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={req}
                            onChange={(e) => editing.updateFunctionalRequirement(index, e.target.value)}
                            className="flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                          <button
                            onClick={() => editing.removeFunctionalRequirement(index)}
                            className="text-red-500 hover:text-red-700 text-sm px-2 py-1"
                          >
                            Remove
                          </button>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-700 dark:text-gray-300">{req}</p>
                      )}
                    </div>
                  </div>
                ))}
                {wizardState.isEditingRequirements && (
                  <button
                    onClick={editing.addFunctionalRequirement}
                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 flex items-center space-x-1"
                  >
                    <span>+</span>
                    <span>Add Requirement</span>
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Non-Functional Requirements */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
          <div 
            className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 cursor-pointer"
            onClick={() => toggleSection('nfrs')}
          >
            <div className="flex items-center space-x-3">
              <span className="text-xl">{expandedSections.nfrs ? '🔽' : '▶️'}</span>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Quality Requirements
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {nfrs.length} requirements • How well the system must perform
                </p>
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                editing.toggleRequirementsEditMode();
              }}
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              {wizardState.isEditingRequirements ? 'Done' : 'Edit'}
            </button>
          </div>
          
          {expandedSections.nfrs && (
            <div className="p-4">
              {Object.entries(nfrCategories).map(([category, { icon, color, items }]) => {
                if (items.length === 0) return null;
                
                return (
                  <div key={category} className="mb-4 last:mb-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm">{icon}</span>
                      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                        {category} ({items.length})
                      </h4>
                    </div>
                    <div className="space-y-2">
                      {items.map((nfr: any) => (
                        <div key={nfr.index} className={`p-3 rounded-lg border ${color} dark:bg-gray-700/50 dark:border-gray-600`}>
                          {wizardState.isEditingRequirements ? (
                            <div className="space-y-2">
                              <input
                                type="text"
                                value={nfr.nfr}
                                onChange={(e) => editing.updateNFR(nfr.index, 'nfr', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm font-medium bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              />
                              <textarea
                                value={nfr.evidence}
                                onChange={(e) => editing.updateNFR(nfr.index, 'evidence', e.target.value)}
                                rows={2}
                                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                placeholder="Evidence/justification"
                              />
                              <button
                                onClick={() => editing.removeNFR(nfr.index)}
                                className="text-red-500 hover:text-red-700 text-sm"
                              >
                                Remove
                              </button>
                            </div>
                          ) : (
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white text-sm mb-1">
                                {nfr.nfr}
                              </p>
                              <p className="text-xs text-gray-600 dark:text-gray-400">
                                <span className="font-medium">Evidence:</span> {nfr.evidence}
                              </p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
              {wizardState.isEditingRequirements && (
                <button
                  onClick={editing.addNFR}
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 flex items-center space-x-1"
                >
                  <span>+</span>
                  <span>Add Quality Requirement</span>
                </button>
              )}
            </div>
          )}
        </div>

        {/* Architectural Characteristics */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
          <div 
            className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 cursor-pointer"
            onClick={() => toggleSection('architecture')}
          >
            <div className="flex items-center space-x-3">
              <span className="text-xl">{expandedSections.architecture ? '🔽' : '▶️'}</span>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Core Architectural Characteristics
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {archChars.length} characteristics • Key architectural priorities
                </p>
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                editing.toggleRequirementsEditMode();
              }}
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              {wizardState.isEditingRequirements ? 'Done' : 'Edit'}
            </button>
          </div>
          
          {expandedSections.architecture && (
            <div className="p-4">
              <div className="flex flex-wrap gap-2">
                {archChars.map((char: string, index: number) => (
                  <div key={index} className="bg-purple-100 dark:bg-purple-900/30 px-3 py-2 rounded-full border border-purple-200 dark:border-purple-800">
                    {wizardState.isEditingRequirements ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={char}
                          onChange={(e) => editing.updateArchCharacteristic(index, e.target.value)}
                          className="bg-transparent border-none p-0 text-sm focus:outline-none min-w-0 text-gray-900 dark:text-white"
                        />
                        <button
                          onClick={() => editing.removeArchCharacteristic(index)}
                          className="text-red-500 hover:text-red-700 text-xs"
                        >
                          ×
                        </button>
                      </div>
                    ) : (
                      <span className="text-purple-800 dark:text-purple-200 text-sm font-medium">
                        {char}
                      </span>
                    )}
                  </div>
                ))}
                {wizardState.isEditingRequirements && (
                  <button
                    onClick={editing.addArchCharacteristic}
                    className="bg-gray-200 dark:bg-gray-600 px-3 py-2 rounded-full text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500"
                  >
                    + Add
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Non-Goals */}
        {nonGoals.length > 0 && (
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
            <div 
              className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 cursor-pointer"
              onClick={() => toggleSection('nonGoals')}
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{expandedSections.nonGoals ? '🔽' : '▶️'}</span>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Non-Goals
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {nonGoals.length} items • What we're explicitly NOT building
                  </p>
                </div>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  editing.toggleNonGoalsEditMode();
                }}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                {wizardState.isEditingNonGoals ? 'Done' : 'Edit'}
              </button>
            </div>
            
            {expandedSections.nonGoals && (
              <div className="p-4 space-y-3">
                {nonGoals.map((nonGoal: string, index: number) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-6 h-6 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                        <span className="text-red-600 dark:text-red-400 text-sm">✗</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      {wizardState.isEditingNonGoals ? (
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={nonGoal}
                            onChange={(e) => editing.updateNonGoalAtIndex(index, e.target.value)}
                            className="flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                          <button
                            onClick={() => editing.removeNonGoal(index)}
                            className="text-red-500 hover:text-red-700 text-sm px-2 py-1"
                          >
                            Remove
                          </button>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-700 dark:text-gray-300">{nonGoal}</p>
                      )}
                    </div>
                  </div>
                ))}
                {wizardState.isEditingNonGoals && (
                  <button
                    onClick={editing.addNonGoal}
                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 flex items-center space-x-1"
                  >
                    <span>+</span>
                    <span>Add Non-Goal</span>
                  </button>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between mt-8">
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
        >
          ← Back to Task Definition
        </button>
        
        <button
          onClick={onContinue}
          disabled={isLoading}
          className="px-6 py-3 bg-amber-500 hover:bg-amber-600 disabled:bg-gray-400 text-white rounded-lg font-medium"
        >
          {isLoading ? 'Processing...' : 'Continue to Strategic Assessment →'}
        </button>
      </div>
    </div>
  );
} 