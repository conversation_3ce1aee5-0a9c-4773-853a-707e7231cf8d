import React, { useState, useMemo } from 'react';
import type { 
  DecisionProcessingResult, 
  DecisionPoint,
  WizardState
} from '../../types/design-doc-wizard';
import { DecisionClassifier } from '../../utils/decisionClassification';
import DecisionDetailModal from '../DecisionDetailModal';
import { useDecisions, useProjectContextWithDebug } from '../../hooks/useDecisions';
import { DecisionLinkedText } from '../../utils/decisionUtils';

interface DecisionProcessingSummaryProps {
  wizardState: WizardState;
  onApproveAutoProcessed: () => void;
  onInitiateOverride: (decisionId: string) => void;
  onContinue: () => void;
}

export default function DecisionProcessingSummary({
  wizardState,
  onApproveAutoProcessed,
  onInitiateOverride,
  onContinue
}: DecisionProcessingSummaryProps) {
  const [selectedDecisionForModal, setSelectedDecisionForModal] = useState<string | null>(null);
  const [selectedDecisionPointForModal, setSelectedDecisionPointForModal] = useState<DecisionPoint | null>(null);
  const processingResults = wizardState.decisionProcessingResults || [];
  const processingStats = DecisionClassifier.getProcessingSummary(processingResults);

  // Use project context - we need repository info for decision lookup
  const repositorySlug = new URLSearchParams(window.location.search).get('repositorySlug') || '';
  const installationId = new URLSearchParams(window.location.search).get('installationId') || '';
  const { repositorySlug: finalRepoSlug, installationId: finalInstallationId } = useProjectContextWithDebug(repositorySlug, installationId);

  // Collect all text that might contain decision references
  const textsToSearch = useMemo(() => {
    const allTexts: (string | string[])[] = [];
    
    wizardState.decisionPoints.forEach(decision => {
      if (decision.recommendation_rationale) {
        allTexts.push(decision.recommendation_rationale);
      }
      
      decision.options?.forEach(option => {
        if (option.alignmentJustification) {
          allTexts.push(option.alignmentJustification);
        }
      });
    });
    
    return allTexts;
  }, [wizardState.decisionPoints]);

  // Fetch decisions based on references found in the text
  const { decisions, isLoading: isLoadingDecisions, error: decisionsError } = useDecisions({
    repositorySlug: finalRepoSlug,
    installationId: finalInstallationId,
    texts: textsToSearch
  });

  const handleDecisionClick = (decisionId: string) => {
    console.log('[DecisionProcessingSummary] Decision clicked:', decisionId);
    const decisionPoint = wizardState.decisionPoints.find(dp => dp.id === decisionId);
    console.log('[DecisionProcessingSummary] Found decision point:', !!decisionPoint);
    if (decisionPoint) {
      console.log('[DecisionProcessingSummary] Setting selectedDecisionPointForModal');
      setSelectedDecisionPointForModal(decisionPoint);
    } else {
      console.log('[DecisionProcessingSummary] Setting selectedDecisionForModal');
      setSelectedDecisionForModal(decisionId);
    }
  };

  // Debug modal state
  console.log('[DecisionProcessingSummary] Modal state:', {
    selectedDecisionForModal,
    selectedDecisionPointForModal: !!selectedDecisionPointForModal,
    isOpen: !!selectedDecisionForModal || !!selectedDecisionPointForModal
  });

  const autoProcessedDecisions = wizardState.decisionPoints.filter((decision, index) => 
    processingResults[index]?.autoProcessed
  );
  
  const pendingReviewDecisions = wizardState.decisionPoints.filter((decision, index) => 
    processingResults[index]?.classification.requiresReview
  );

  const getImpactColor = (score: number) => {
    if (score >= 70) return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/50 dark:text-red-300';
    if (score >= 30) return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300';
    return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/50 dark:text-green-300';
  };

  const getImpactLabel = (score: number) => {
    if (score >= 70) return 'High Impact';
    if (score >= 30) return 'Medium Impact';
    return 'Low Impact';
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Intelligent Decision Processing Complete
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Our AI has analyzed your decisions and automatically handled the low-impact ones. 
          Review the summary below and approve or modify as needed.
        </p>
      </div>

      {/* Processing Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{processingStats.total}</div>
          <div className="text-sm text-blue-800 dark:text-blue-300">Total Decisions</div>
        </div>
        
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">{processingStats.autoProcessed}</div>
          <div className="text-sm text-green-800 dark:text-green-300">Auto-Processed</div>
        </div>
        
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{processingStats.requireReview}</div>
          <div className="text-sm text-yellow-800 dark:text-yellow-300">Need Review</div>
        </div>
        
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">{processingStats.highImpact}</div>
          <div className="text-sm text-red-800 dark:text-red-300">High Impact</div>
        </div>
      </div>

      {/* Risk Factors Summary */}
      {processingStats.riskFactors.length > 0 && (
        <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4 mb-8">
          <h3 className="font-semibold text-orange-800 dark:text-orange-200 mb-2">⚠️ Key Risk Factors Identified</h3>
          <div className="flex flex-wrap gap-2">
            {processingStats.riskFactors.map((factor, index) => (
              <span key={index} className="px-2 py-1 bg-orange-100 dark:bg-orange-800 text-orange-800 dark:text-orange-200 text-xs rounded-full">
                {factor}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Auto-Processed Decisions */}
      {autoProcessedDecisions.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              ✅ Auto-Processed Decisions ({autoProcessedDecisions.length})
            </h3>
            <button
              onClick={onApproveAutoProcessed}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
            >
              Approve All
            </button>
          </div>
          
          <div className="space-y-4">
            {autoProcessedDecisions.map((decision, index) => {
              const result = processingResults.find(r => r.decisionId === decision.id);
              if (!result) return null;
              
              const selectedOption = decision.options?.find(opt => opt.id === decision.selectedOption);
              
              return (
                <div 
                  key={decision.id} 
                  onClick={(e) => {
                    console.log('[DecisionProcessingSummary] Click event triggered for:', decision.id);
                    e.preventDefault();
                    e.stopPropagation();
                    handleDecisionClick(decision.id);
                  }}
                  className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4 cursor-pointer hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-200 hover:border-green-300 dark:hover:border-green-600 group"
                  title="Click to view decision details"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">{decision.title}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{decision.description}</p>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <span className={`px-2 py-1 text-xs rounded border ${getImpactColor(result.classification.impactScore)}`}>
                        {getImpactLabel(result.classification.impactScore)}
                      </span>
                      <span className="text-sm text-gray-500">Score: {result.classification.impactScore}</span>
                    </div>
                  </div>
                  
                  {/* Selected Option */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 mb-3">
                    <div className="font-medium mb-1 flex items-baseline">
                      <span className="text-green-600 dark:text-green-400">✓ Selected:</span>
                      <span className="ml-2 text-gray-900 dark:text-white">{selectedOption?.name || 'Unknown Option'}</span>
                    </div>
                    
                    {(decision.rationale || result.rationale) && (
                      <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1 my-2 pl-1">
                        {(decision.rationale || result.rationale || '')
                          .split('.')
                          .filter(point => point.trim())
                          .map((point, index) => (
                            <li key={index}>
                              <DecisionLinkedText
                                text={point.trim() + '.'}
                                decisions={decisions}
                                onDecisionClick={handleDecisionClick}
                                className=""
                              />
                            </li>
                        ))}
                      </ul>
                    )}

                    {selectedOption && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        Risk: {selectedOption.riskLevel} • Complexity: {selectedOption.complexity}
                      </div>
                    )}
                  </div>
                  
                  {/* Visual indicator that this is clickable */}
                  <div className="text-xs text-gray-400 dark:text-gray-500 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                   Click to view details
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Decisions Needing Review */}
      {pendingReviewDecisions.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              🔍 Decisions Requiring Your Review ({pendingReviewDecisions.length})
            </h3>
            <button
              onClick={onContinue}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Review {pendingReviewDecisions.length} Decision{pendingReviewDecisions.length !== 1 ? 's' : ''} →
            </button>
          </div>
          
          <div className="space-y-4">
            {pendingReviewDecisions.map((decision, index) => {
              const result = processingResults.find(r => r.decisionId === decision.id);
              if (!result) return null;
              
              return (
                <div key={decision.id} className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">{decision.title}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{decision.description}</p>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <span className={`px-2 py-1 text-xs rounded border ${getImpactColor(result.classification.impactScore)}`}>
                        {getImpactLabel(result.classification.impactScore)}
                      </span>
                      <span className="text-sm text-gray-500">Score: {result.classification.impactScore}</span>
                    </div>
                  </div>
                  
                  {/* Why it needs review */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 mb-3">
                    <div className="font-medium text-yellow-600 dark:text-yellow-400 mb-2">
                      Why this needs your attention:
                    </div>
                    <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      {result.classification.reasons.map((reason, reasonIndex) => (
                        <li key={reasonIndex} className="flex items-start">
                          <span className="w-1.5 h-1.5 bg-yellow-400 rounded-full mr-2 mt-1.5 flex-shrink-0"></span>
                          {reason}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {/* Risk factors */}
                  {result.classification.riskFactors.length > 0 && (
                    <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3">
                      <div className="font-medium text-red-600 dark:text-red-400 mb-2">
                        Risk factors to consider:
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {result.classification.riskFactors.map((factor, factorIndex) => (
                          <span key={factorIndex} className="px-2 py-1 bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200 text-xs rounded">
                            {factor}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {processingStats.requireReview === 0 && (
        <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {processingStats.autoProcessed > 0 && (
              <span>
                {processingStats.autoProcessed} decisions auto-processed based on your preferences
              </span>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onContinue}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
            >
              All Decisions Processed - Continue →
            </button>
          </div>
        </div>
      )}

      {/* Decision Detail Modal */}
      <DecisionDetailModal
        decision={selectedDecisionForModal ? decisions[selectedDecisionForModal] : null}
        decisionPoint={selectedDecisionPointForModal}
        isOpen={!!selectedDecisionForModal || !!selectedDecisionPointForModal}
        onClose={() => {
          console.log('[DecisionProcessingSummary] Closing modal');
          setSelectedDecisionForModal(null);
          setSelectedDecisionPointForModal(null);
        }}
      />
    </div>
  );
} 