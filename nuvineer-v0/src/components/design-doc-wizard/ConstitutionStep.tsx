'use client'

import React from 'react';
import { TECHNICAL_PRIORITIES } from '../../types/design-doc-wizard';

interface ConstitutionStepProps {
  wizardState: any;
  setWizardState: (updater: (prev: any) => any) => void;
  constitutionSeedText: string;
  setConstitutionSeedText: (text: string) => void;
  handlePriorityChange: (index: number, direction: 'up' | 'down') => void;
  seedConstitution: () => void;
  saveConstitution: () => void;
}

export default function ConstitutionStep({
  wizardState,
  setWizardState,
  constitutionSeedText,
  setConstitutionSeedText,
  handlePriorityChange,
  seedConstitution,
  saveConstitution
}: ConstitutionStepProps) {

  const hasExistingConstitution =
  wizardState.projectConstitution &&
  (wizardState.projectConstitution.companyStage !== 'not-set' ||
    wizardState.projectConstitution.projectType !== 'not-set' ||
    wizardState.projectConstitution.architecturalPrinciples ||
    wizardState.projectConstitution.productAndBusinessContext);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-2">Product Priorities</h2>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">Define these core principles once for your company/product. We will use this context to guide our decisions.</p>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-6">
        {/* Left Column: AI Seeding */}
        {!hasExistingConstitution && (
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600 h-fit">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">⚡️ Seed with Existing Context</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Have an existing `README.md`, project brief, or mission statement? Paste it all here.</p>
          {wizardState.projectConstitution?.primaryLanguage && (
            <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Detected Primary Language:</strong> {wizardState.projectConstitution.primaryLanguage}
              </p>
            </div>
          )}
          <textarea
            value={constitutionSeedText}
            onChange={(e) => setConstitutionSeedText(e.target.value)}
            rows={10}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
            placeholder="Paste your content here..."
          />
          <button
            onClick={seedConstitution}
            disabled={!constitutionSeedText || wizardState.isSeedingConstitution}
            className="mt-3 w-full px-6 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center justify-center"
          >
            {wizardState.isSeedingConstitution ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-3"></div>
                Analyzing...
              </>
            ) : (
              '🤖 Analyze and Populate'
            )}
          </button>
        </div>
        )}

        {/* Right Column: Constitution Form */}
        <div className={`space-y-6 ${hasExistingConstitution ? 'lg:col-span-2' : ''}`}>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Company Stage</label>
            <select
              value={wizardState.projectConstitution?.companyStage || 'not-set'}
              onChange={(e) => setWizardState((prev: any) => ({ ...prev, projectConstitution: { ...prev.projectConstitution!, companyStage: e.target.value as any } }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
            >
              <option value="not-set" disabled>Select stage...</option>
              <option value="startup">Startup (Focus on speed & learning)</option>
              <option value="growth">Growth (Focus on scaling & new features)</option>
              <option value="mature">Mature (Focus on stability & efficiency)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Project Type</label>
            <select
              value={wizardState.projectConstitution?.projectType || 'not-set'}
              onChange={(e) => setWizardState((prev: any) => ({ ...prev, projectConstitution: { ...prev.projectConstitution!, projectType: e.target.value as any } }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
            >
              <option value="not-set" disabled>Select type...</option>
              <option value="b2c-saas">B2C SaaS</option>
              <option value="b2b-saas">B2B SaaS</option>
              <option value="library">Library</option>
              <option value="internal-tool">Internal Tool</option>
              <option value="data-platform">Data Platform</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rank Architectural Priorities</label>
            <div className="space-y-2 rounded-md border border-gray-300 dark:border-gray-600 p-3 bg-gray-50 dark:bg-gray-700/50">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Order these architectural characteristics from most to least important for your project.</p>
              {wizardState.projectConstitution?.priorities?.map((priorityId: any, index: any) => {
                const priority = TECHNICAL_PRIORITIES.find((p: any) => p.id === priorityId);
                if (!priority) return null;

                return (
                  <div key={priority.id} className="flex items-center justify-between bg-white dark:bg-gray-800 p-2 rounded-md shadow-sm group">
                    <div className="flex items-center">
                      <span className="text-lg font-bold text-amber-500 mr-4 w-4 text-center">{index + 1}</span>
                      <div>
                        <span className="font-medium text-gray-900 dark:text-white">{priority.name}</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{priority.description}</p>
                      </div>
                    </div>
                    <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={() => handlePriorityChange(index, 'up')}
                        disabled={index === 0}
                        className="p-1 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 dark:hover:bg-gray-600"
                        title="Move up"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l5 5a1 1 0 01-1.414 1.414L11 6.414V16a1 1 0 11-2 0V6.414L5.707 9.707a1 1 0 01-1.414-1.414l5-5A1 1 0 0110 3z" clipRule="evenodd" /></svg>
                      </button>
                      <button
                        onClick={() => handlePriorityChange(index, 'down')}
                        disabled={index === (wizardState.projectConstitution?.priorities?.length || 0) - 1}
                        className="p-1 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 dark:hover:bg-gray-600"
                        title="Move down"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 17a1 1 0 00.707-.293l5-5a1 1 0 00-1.414-1.414L11 13.586V4a1 1 0 10-2 0v9.586L5.707 10.293a1 1 0 00-1.414 1.414l5 5A1 1 0 0010 17z" clipRule="evenodd" /></svg>
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Architectural Principles</label>
            <textarea
              value={wizardState.projectConstitution?.architecturalPrinciples || ''}
              onChange={(e) => setWizardState((prev: any) => ({ ...prev, projectConstitution: { ...prev.projectConstitution!, architecturalPrinciples: e.target.value } }))}
              rows={5}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
              placeholder="e.g., Use microservices for new domains, all endpoints must be authenticated, prefer managed services..."
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Product & Business Context</label>
            <textarea
              value={wizardState.projectConstitution?.productAndBusinessContext || ''}
              onChange={(e) => setWizardState((prev: any) => ({ ...prev, projectConstitution: { ...prev.projectConstitution!, productAndBusinessContext: e.target.value } }))}
              rows={5}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 text-gray-900 bg-white dark:bg-gray-700 dark:text-white"
              placeholder="e.g., We are building a B2B SaaS for enterprise customers. Target users are non-technical. Key goal for Q3 is user retention..."
            />
          </div>
        </div>
      </div>

       
    </div>
  );
} 