import React from 'react';

// Define the structure of the constitution data we expect to display.
interface ConstitutionData {
  provider?: string;
  compute?: { type: string; region?: string }[];
  data_stores?: { name: string; type:string; tier?: string }[];
  observability?: { provider: string; logging?: string };
  sourceFiles?: string[];
}

// Define the props for the component.
interface DeploymentConstitutionStepProps {
  constitution: ConstitutionData | null;
  isLoading: boolean;
  error: string | null;
  onConfirm: () => void;
  onUpdateInfraRepo: (repoUrl: string) => void;
}

export const DeploymentConstitutionStep: React.FC<DeploymentConstitutionStepProps> = ({
  constitution,
  isLoading,
  error,
  onConfirm,
  onUpdateInfraRepo,
}) => {
  const [infraRepoUrl, setInfraRepoUrl] = React.useState('');
  const [isEditingRepo, setIsEditingRepo] = React.useState(false);

  const handleUpdateClick = () => {
    if (infraRepoUrl.trim()) {
      onUpdateInfraRepo(infraRepoUrl.trim());
      setIsEditingRepo(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600 dark:text-gray-400">
          Analyzing repository for deployment context...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center text-red-600 bg-red-50 dark:bg-red-900/30 rounded-lg">
        <p>Error generating deployment context: {error}</p>
      </div>
    );
  }

  if (!constitution) {
    return (
      <div className="p-6 text-center text-gray-500">
        <p>Could not determine deployment context from the repository.</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 dark:bg-zinc-900/50 border border-gray-200 dark:border-zinc-700 rounded-lg">
      <h3 className="text-lg font-semibold mb-3">Deployment Constitution</h3>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
        We've analyzed your repository to understand its hosting environment. This helps us provide more context-aware architectural feedback. Please review the summary below.
      </p>

      {/* Displaying the constitution data will go here */}
      <div className="space-y-2 text-sm p-4 bg-white dark:bg-zinc-800 rounded-md">
        <p><strong>Provider:</strong> {constitution.provider || 'Not detected'}</p>
        <p><strong>Compute:</strong> {constitution.compute?.map(c => c.type).join(', ') || 'Not detected'}</p>
        <p><strong>Data Stores:</strong> {constitution.data_stores?.map(d => d.name).join(', ') || 'Not detected'}</p>
        <p className="text-xs text-gray-500 pt-2">
            Sourced from: {constitution.sourceFiles?.join(', ') || 'analysis of repository contents.'}
        </p>
      </div>

      <div className="mt-4 p-4 border-t border-gray-200 dark:border-zinc-700">
        <p className="text-sm font-medium mb-2">Is your infrastructure code in a different repository?</p>
        {isEditingRepo ? (
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={infraRepoUrl}
              onChange={(e) => setInfraRepoUrl(e.target.value)}
              placeholder="https://github.com/owner/infra-repo"
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-md shadow-sm focus:outline-none sm:text-sm bg-white dark:bg-zinc-700"
            />
            <button onClick={handleUpdateClick} className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
              Update
            </button>
            <button onClick={() => setIsEditingRepo(false)} className="px-3 py-2 text-sm">
              Cancel
            </button>
          </div>
        ) : (
          <button onClick={() => setIsEditingRepo(true)} className="text-sm text-blue-600 hover:underline">
            Specify infrastructure repository
          </button>
        )}
      </div>

      <div className="mt-6 flex justify-end gap-3">
        <button
          onClick={onConfirm}
          className="px-4 py-2 bg-green-600 text-white font-semibold rounded-md shadow-sm hover:bg-green-700"
        >
          Confirm and Continue Analysis
        </button>
      </div>
    </div>
  );
};
