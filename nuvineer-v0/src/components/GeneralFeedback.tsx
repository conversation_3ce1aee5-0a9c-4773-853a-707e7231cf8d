'use client'

import React, { useState } from 'react';

interface GeneralFeedbackProps {
  contextData?: {
    prNumber: number;
    decisionIds?: string[];
  };
  onSubmitGeneralFeedback: (
    feedbackType: string,
    comment: string,
    context?: GeneralFeedbackProps['contextData']
  ) => Promise<void>;
}

const GeneralFeedback: React.FC<GeneralFeedbackProps> = ({ contextData, onSubmitGeneralFeedback }) => {
  const [feedbackType, setFeedbackType] = useState<string>('suggestion');
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const feedbackTypes = [
    { value: 'suggestion', label: 'Suggestion for this Analysis' },
    { value: 'bug_report', label: 'Issue with this Analysis' },
    { value: 'question', label: 'Question about this Analysis' },
    { value: 'compliment', label: 'Analysis is Helpful' },
    { value: 'other', label: 'Other Feedback' },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!comment.trim()) {
      setError('Please enter your feedback before submitting.');
      return;
    }
    setError(null);
    setIsSubmitting(true);

    try {
      await onSubmitGeneralFeedback(feedbackType, comment.trim(), contextData);
      console.log('General feedback submitted:', { type: feedbackType, comment, context: contextData });
      setSubmitted(true);
      setTimeout(() => {
        setFeedbackType('suggestion');
        setComment('');
        setSubmitted(false);
      }, 3000);
    } catch (err) {
      console.error("Error submitting general feedback:", err);
      setError('Failed to submit feedback. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="mt-8 p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
        {contextData?.prNumber ? `Feedback on Analysis for PR #${contextData.prNumber}` : 'Feedback'}
      </h3>

      {submitted ? (
        <div className="text-center text-green-700 dark:text-green-400 p-4 bg-green-50 dark:bg-green-900/50 rounded-md">
          Thank you for your feedback!
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="feedbackType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Feedback Type:</label>
            <select
              id="feedbackType"
              value={feedbackType}
              onChange={(e) => setFeedbackType(e.target.value)}
              className="w-full p-2 border rounded-md bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:text-gray-200"
              disabled={isSubmitting}
            >
              {feedbackTypes.map((type) => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="generalComment" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Your Feedback:</label>
            <textarea
              id="generalComment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Share your thoughts, suggestions, or report issues"
              rows={4}
              className="w-full p-2 border rounded-md text-sm bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:text-gray-200"
              disabled={isSubmitting}
              required
            />
          </div>

          {error && (
            <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
          )}

          <button
            type="submit"
            disabled={isSubmitting || !comment.trim()}
            className="w-full px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-60 disabled:cursor-not-allowed dark:bg-indigo-500 dark:hover:bg-indigo-600 dark:focus:ring-offset-gray-900"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Analysis Feedback'}
          </button>
        </form>
      )}
    </div>
  );
};

export default GeneralFeedback; 