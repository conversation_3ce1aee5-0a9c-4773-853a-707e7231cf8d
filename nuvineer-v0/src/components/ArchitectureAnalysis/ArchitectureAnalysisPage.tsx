'use client';

import React, { useState, useEffect } from 'react';
import PRAnalysisList from './PRAnalysisList';
import PRAnalysisDetail from './PRAnalysisDetail';
import { useSearchParams } from 'next/navigation';

// Define the expected structure for a single analysis result from the API
// (Ensure these types match your actual backend response)
type Risk = {
  category: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  mitigation: string;
};
type PRFile = { filename: string; /* additions?: number; deletions?: number; */ }; // Adjust as needed
type PRContext = { number: number; title: string; html_url: string; files?: PRFile[]; };
type Relationship = {
  new_decision_id: string;
  existing_decision_id: string;
  relationship_type: 'supersedes' | 'amends' | 'conflicts_with' | 'independent'; // Updated to be specific
  confidence_score: number; // Now required
  justification: string;    // Now required
};
type Decision = {
  id: string;
  title: string;
  description?: string;
  rationale?: string;
  implications?: string;
  related_files?: string[];
  confidence_score?: number;
  follows_standard_practice?: boolean;
  follows_standard_practice_reason?: string;
  risks?: Risk[]; // Uses the updated Risk type
};


type AnalysisResult = {
  prContext: PRContext;
  decisions: Decision[];
  relationships: Relationship[];
  no_architecture_impact_reason?: string;
  // filesChanged?: PRFile[]; // Potentially redundant if prContext.files is comprehensive
};

export default function ArchitectureAnalysisPage() {
  const [analyses, setAnalyses] = useState<AnalysisResult[]>([]);
  const [selectedPrNumber, setSelectedPrNumber] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use useSearchParams hook to access query parameters
  const searchParams = useSearchParams();
  
  // State for URL parameters, initialized to null or appropriate defaults
  const [repositorySlug, setRepositorySlug] = useState<string | null>(null);
  const [installationIdParam, setInstallationIdParam] = useState<string | null>(null);
  const [isPublicParam, setIsPublicParam] = useState<boolean>(false);

  // No longer need state for repositorySlug, derived directly from searchParams
  // const [repositorySlug, setRepositorySlug] = useState<string | null>(null);

  useEffect(() => {
    if (!searchParams) { // Guard against null searchParams
      setError("Failed to load URL parameters.");
      setIsLoading(false);
      return;
    }

    const currentRepositorySlug = searchParams.get('repositorySlug');
    const currentInstallationIdParam = searchParams.get('installationId');
    const currentIsPublicParam = searchParams.get('isPublic') === 'true';

    setRepositorySlug(currentRepositorySlug);
    setInstallationIdParam(currentInstallationIdParam);
    setIsPublicParam(currentIsPublicParam);

    // Directly check if repositorySlug exists from the hook
    if (!currentRepositorySlug) {
      setError("Repository context is missing. Please select a repository first.");
      setIsLoading(false);
      return; // Stop further execution if slug is missing
    }

    // Fetch data since we have the repository slug
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Validate parameters (use state variables which are now set)
        if (!isPublicParam && !installationIdParam) {
            throw new Error("Missing installation ID in URL parameters for private repository analysis.");
        }

        // Construct API URL using correct parameter names (use state variables)
        let apiUrl = `/api/architecture-analysis?repo=${encodeURIComponent(repositorySlug!)}`; // Added non-null assertion as it's checked before
        if (!isPublicParam && installationIdParam) {
            apiUrl += `&installationId=${encodeURIComponent(installationIdParam)}`; // API expects 'installationId'
        }
        if (isPublicParam) {
             apiUrl += `&isPublic=true`; // Pass isPublic to API
        }

        // Use the constructed API URL
        console.log(`[ArchAnalysisPage] Fetching data from: ${apiUrl}`);
        const response = await fetch(apiUrl);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to fetch analyses: ${response.statusText}`);
        }
        const data: AnalysisResult[] = await response.json();

        if (!Array.isArray(data)) {
            throw new Error('Invalid data format received from API.');
        }

        setAnalyses(data);
      } catch (err: any) {
        console.error("Failed to load architecture analyses:", err);
        setError(err.message || "Failed to load data. Please ensure the API endpoint is running and returning the correct format.");
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
    // Dependencies: re-run if any relevant query param changes
    // The state variables (repositorySlug, installationIdParam, isPublicParam) will trigger this effect when they change.
  }, [searchParams, repositorySlug, installationIdParam, isPublicParam]);

  const selectedAnalysis = analyses.find(a => a.prContext.number === selectedPrNumber);

  // Check derived repositorySlug directly
  if (!isLoading && !repositorySlug && !error) {
     return <div className="text-center p-8 text-orange-600">Please navigate to this page via the main dashboard after selecting a repository.</div>;
  }

  if (isLoading) {
    // Use repositorySlug directly from hook
    return <div className="text-center p-8">Loading architecture analyses for {repositorySlug}...</div>;
  }

  if (error) {
    return <div className="text-center p-8 text-red-600">Error: {error}</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-1 text-center">Architecture Analysis Results</h1>
      {/* Use repositorySlug directly from hook */}
      <h2 className="text-xl font-medium mb-6 text-center text-gray-500 dark:text-gray-400">{repositorySlug}</h2>

      {selectedAnalysis ? (
        <div>
          <button
            onClick={() => setSelectedPrNumber(null)}
            className="mb-4 px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded"
          >
            &larr; Back to PR List
          </button>
          <PRAnalysisDetail
            prData={selectedAnalysis.prContext}
            decisions={selectedAnalysis.decisions}
            relationships={selectedAnalysis.relationships}
            filesChanged={selectedAnalysis.prContext.files || []} // Derive from prContext
            no_architecture_impact_reason={selectedAnalysis.no_architecture_impact_reason}
            // Note: Pass your actual feedback component if needed, currently using GeneralFeedback inside PRAnalysisDetail
          />
        </div>
      ) : (
        <PRAnalysisList
          analyses={analyses}
          onSelectPr={setSelectedPrNumber}
        />
      )}
    </div>
  );
} 