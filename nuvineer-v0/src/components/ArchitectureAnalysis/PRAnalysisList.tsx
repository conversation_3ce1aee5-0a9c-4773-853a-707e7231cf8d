'use client';

import React from 'react';

// Match types from ArchitectureAnalysisPage
type Risk = {  category: string; description: string; severity: 'low' | 'medium' | 'high'; mitigation: string; [key: string]: any }; // Making it more flexible based on typical use
type PRFile = { filename: string; [key: string]: any }; // Making it more flexible
type PRContext = { number: number; title: string; html_url: string; files?: PRFile[]; };
type Decision = {
  id: string;
  title: string;
  description?: string;
  rationale?: string;
  implications?: string;
  related_files?: string[];
  confidence_score?: number;
  follows_standard_practice?: boolean;
  follows_standard_practice_reason?: string;
  risks?: Risk[];
  [key: string]: any; // Allow other properties
};
type Relationship = {
  new_decision_id: string;
  existing_decision_id: string; // Added from ArchitectureAnalysisPage
  relationship_type: string; // Changed from 'conflicts_with' to string
  confidence_score?: number; // Added from ArchitectureAnalysisPage
  justification?: string; // Added from ArchitectureAnalysisPage
  [key: string]: any; // Allow other properties
};

type AnalysisResult = {
  prContext: PRContext;
  decisions: Decision[];
  relationships: Relationship[];
  no_architecture_impact_reason?: string;
};

interface PRAnalysisListProps {
  analyses: AnalysisResult[];
  onSelectPr: (prNumber: number) => void;
}

export default function PRAnalysisList({ analyses, onSelectPr }: PRAnalysisListProps) {

  const getStatus = (analysis: AnalysisResult): { text: string; className: string } => {
    const prDecisionIds = new Set(analysis.decisions.map(d => d.id));
    const hasConflict = analysis.relationships.some(
      rel => rel.relationship_type === 'conflicts_with' && prDecisionIds.has(rel.new_decision_id)
    );

    if (hasConflict) {
      return { text: "Conflicts Detected", className: "text-red-600 font-semibold" };
    }
    if (analysis.decisions.length > 0) {
      return { text: "Decisions Found", className: "text-blue-600" };
    }
    return { text: "No Significant Decisions", className: "text-gray-500" };
  };

  if (analyses.length === 0) {
      return <div className="text-center p-8 text-gray-600">No architecture analyses found.</div>;
  }

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-4">Analyzed Pull Requests</h2>
      <ul className="space-y-3">
        {analyses.map((analysis) => {
          const status = getStatus(analysis);
          return (
            <li
              key={analysis.prContext.number}
              onClick={() => onSelectPr(analysis.prContext.number)}
              className="p-4 border rounded-md hover:bg-gray-100 cursor-pointer transition duration-150 ease-in-out"
            >
              <div className="flex justify-between items-center">
                <div>
                  <span className="font-medium">PR #{analysis.prContext.number}:</span> {analysis.prContext.title}
                </div>
                <span className={`text-sm ${status.className}`}>{status.text}</span>
              </div>
              <a href={analysis.prContext.html_url} target="_blank" rel="noopener noreferrer" className="text-xs text-blue-500 hover:underline" onClick={(e) => e.stopPropagation()}>
                View on GitHub
              </a>
            </li>
          );
        })}
      </ul>
    </div>
  );
} 