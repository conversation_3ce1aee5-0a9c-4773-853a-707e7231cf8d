'use client';

import React from 'react';
import GeneralFeedback from '@/components/GeneralFeedback'; // Import the feedback component
import DecisionDetailItem from './DecisionDetailItem'; // Import the new component

// Define Risk structure (matching DecisionDetailItem)
type Risk = {
  category: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  mitigation: string;
};

// Updated Decision type (matching DecisionDetailItem)
type Decision = {
    id: string;
    title: string;
    description?: string;
    rationale?: string;
    implications?: string; // General implications
    related_files?: string[];
    confidence_score?: number;
    follows_standard_practice?: boolean;
    follows_standard_practice_reason?: string;
    risks?: Risk[];
    // Include other potential fields if necessary
};

type Relationship = { new_decision_id: string; existing_decision_id: string; relationship_type: 'supersedes' | 'amends' | 'conflicts_with' | 'independent'; confidence_score: number; justification: string; };
type PRFile = { filename: string; additions?: number; deletions?: number; status?: string }; // Ensure status is available if used
type PRContext = { number: number; title: string; html_url: string; body?: string; files?: PRFile[] }; // Added files here for consistency

interface PRAnalysisDetailProps {
  prData: PRContext;
  decisions: Decision[];
  relationships: Relationship[];
  filesChanged: PRFile[]; // List of files from PR context
  // Optional: Pass no_architecture_impact_reason if needed
  no_architecture_impact_reason?: string;
}

// Placeholder function for feedback submission - replace with your actual API call
const handleFeedbackSubmit = async (feedbackType: string, comment: string, context?: any) => {
  console.log("Submitting feedback:", { feedbackType, comment, context });
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  // Replace with actual API call: await api.submitAnalysisFeedback({ feedbackType, comment, context });
  console.log("Feedback submission successful (simulated).");
};

export default function PRAnalysisDetail({ prData, decisions, relationships, filesChanged, no_architecture_impact_reason }: PRAnalysisDetailProps) {

  // --- Data Processing for Display ---
  const prDecisionsMap = new Map(decisions.map(d => [d.id, d]));

  const conflicts = relationships.filter(rel =>
    prDecisionsMap.has(rel.new_decision_id) && rel.relationship_type === 'conflicts_with'
  );

  // Explicitly type the map variable first
  const fileDecisionMap: Map<string, string[]> = new Map();
  decisions.forEach(decision => {
    (decision.related_files || []).forEach(file => {
      if (!fileDecisionMap.has(file)) {
        fileDecisionMap.set(file, []);
      }
      // Use non-null assertion (if sure 'get' won't return undefined after the check) or keep optional chaining
      // Let's keep optional chaining as it's safer
      fileDecisionMap.get(file)?.push(decision.title);
    });
  });
  // --- End Data Processing ---

  return (
    <div className="p-6 bg-white dark:bg-gray-900 rounded-lg shadow border border-gray-200 dark:border-gray-700">
      {/* PR Header */}
      <h2 className="text-2xl font-semibold mb-1 text-gray-800 dark:text-gray-200">
        Analysis for PR #{prData.number}: {prData.title}
      </h2>
      <a
        href={prData.html_url}
        target="_blank"
        rel="noopener noreferrer"
        className="text-sm text-blue-600 dark:text-blue-400 hover:underline mb-6 inline-block"
      >
        View PR on GitHub &rarr;
      </a>

      {/* Architecture Summary Section */}
      <div className="mb-6 p-4 border border-dashed border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-800/50">
        <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">Architecture Summary</h3>
        {decisions.length === 0 ? (
          <p className="text-gray-600 dark:text-gray-400">
            No significant architectural decisions detected.
            {no_architecture_impact_reason && <span className="text-sm italic"> Reason: {no_architecture_impact_reason}</span>}
          </p>
        ) : (
          <ul className="list-none space-y-0 p-0 m-0">
            {decisions.map(d => (
              <DecisionDetailItem key={d.id} decision={d} prNumber={prData.number} onFeedbackSubmit={handleFeedbackSubmit} />
            ))}
          </ul>
        )}
        {/* Display Conflicts */}
        {conflicts.length > 0 && (
          <div className="mt-4 p-3 border border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/50 rounded-md">
            <h4 className="text-md font-semibold text-red-800 dark:text-red-200 mb-2">⚠️ Conflicts Detected:</h4>
            <ul className="list-none space-y-2 p-0 m-0 text-red-700 dark:text-red-300 text-sm">
              {conflicts.map(c => {
                const conflictingNewDecision = prDecisionsMap.get(c.new_decision_id);
                // TODO: Need a way to fetch/get details of the existing decision (c.existing_decision_id)
                // For now, just show the ID

                return (
                  <li key={`${c.new_decision_id}-${c.existing_decision_id}`} className="border-t border-red-200 dark:border-red-800 pt-2 mt-2 first:mt-0 first:pt-0 first:border-t-0">
                     <p className="mb-2">
                         The following decision conflicts with existing decision ID "<span className="font-medium">{c.existing_decision_id}</span>"
                         <br />
                         <small className="text-red-600 dark:text-red-400">Justification: {c.justification}</small>
                     </p>
                     {conflictingNewDecision ? (
                        <DecisionDetailItem decision={conflictingNewDecision} prNumber={prData.number} onFeedbackSubmit={handleFeedbackSubmit} />
                     ) : (
                        <p>Details for decision {c.new_decision_id} not found.</p>
                     )}
                  </li>
                )
              })}
            </ul>
          </div>
        )}
        {/* Placeholder for link to full analysis/knowledge base */}
        {/* <a href="#" className="text-sm text-blue-600 dark:text-blue-400 hover:underline mt-3 inline-block">View Full Analysis Details &rarr;</a> */}
      </div>

      {/* File Annotations Simulation Section */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">Analyzed Files & Related Decisions</h3>
        <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
          {filesChanged.map(file => (
            <li key={file.filename} className="p-2 border-b border-gray-100 dark:border-gray-700/50 flex justify-between items-center">
              <span>{file.filename}</span>
              {
                fileDecisionMap.has(file.filename) ? (
                  <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300 rounded-full">
                    Relates to: {fileDecisionMap.get(file.filename)?.join(', ')}
                  </span>
                ) : file.status === 'skipped' ? (
                  <span className="ml-2 px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300 rounded-full">
                    Content Skipped
                  </span>
                ) : null // Show nothing if analyzed but no related decisions
              }
            </li>
          ))}
        </ul>
      </div>

      {/* Feedback Section - Keep the general feedback for the overall PR analysis? Or remove? */}
      {/* Let's keep it for now, but maybe hide it if individual feedback is preferred */}
      <div className="mt-8">
         <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">Overall Analysis Feedback</h3>
        <GeneralFeedback
           contextData={{
             prNumber: prData.number,
             decisionIds: decisions.map(d => d.id) // Pass decision IDs for context
           }}
           onSubmitGeneralFeedback={handleFeedbackSubmit} // Pass the handler function
         />
      </div>

    </div>
  );
} 