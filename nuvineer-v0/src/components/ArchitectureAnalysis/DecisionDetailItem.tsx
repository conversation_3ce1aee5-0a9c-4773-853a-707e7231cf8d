'use client';

import React, { useState } from 'react';
import GeneralFeedback from '@/components/GeneralFeedback'; // Assuming path is correct

// Define Risk structure
type Risk = {
  category: string; // ux|security|performance|maintenance|scalability|reliability|testability
  description: string;
  severity: 'low' | 'medium' | 'high';
  mitigation: string;
};

// Updated Decision type
type Decision = {
  id: string;
  title: string;
  description?: string;
  rationale?: string;
  implications?: string; // General implications
  related_files?: string[];
  confidence_score?: number; // Kept in type, but won't display
  follows_standard_practice?: boolean;
  follows_standard_practice_reason?: string;
  risks?: Risk[];
  // Include other potential fields if necessary
};

interface DecisionDetailItemProps {
  decision: Decision;
  prNumber: number;
  onFeedbackSubmit: (feedbackType: string, comment: string, context?: any) => Promise<void>;
}

// Helper for severity colors
const severityClasses = {
  low: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',
  medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300',
  high: 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300',
};

export default function DecisionDetailItem({ decision, prNumber, onFeedbackSubmit }: DecisionDetailItemProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => setIsExpanded(!isExpanded);

  const feedbackContext = {
    prNumber: prNumber,
    decisionId: decision.id,
    decisionTitle: decision.title
  };

  return (
    <li className="mb-2 border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden bg-white dark:bg-gray-900">
      <button
        onClick={toggleExpand}
        className="w-full flex justify-between items-center p-3 text-left bg-gray-50 dark:bg-gray-800/80 hover:bg-gray-100 dark:hover:bg-gray-700/80 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 dark:focus:ring-blue-400"
        aria-expanded={isExpanded}
        aria-controls={`decision-details-${decision.id}`}
      >
        <div className="flex-grow mr-2">
            <span className="font-medium text-gray-800 dark:text-gray-200">{decision.title}</span>
             {/* Standard Practice Indicator */}
             {decision.follows_standard_practice && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300" title={decision.follows_standard_practice_reason || 'Follows standard practice'}>
                    Follows Standard Practice: {decision.follows_standard_practice_reason || 'Reason not specified'}
                </span>
            )}
        </div>
        <span className={`transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : 'rotate-0'}`}>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </span>
      </button>
      {isExpanded && (
        <div id={`decision-details-${decision.id}`} className="p-4 border-t border-gray-200 dark:border-gray-700">
          {/* Description */}
          {decision.description && (
            <div className="mb-3">
              <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Description:</h4>
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{decision.description}</p>
            </div>
          )}
          {/* Rationale */}
          {decision.rationale && (
            <div className="mb-3">
              <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Rationale:</h4>
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{decision.rationale}</p>
            </div>
          )}
          {/* General Implications */}
          {decision.implications && (
            <div className="mb-3">
              <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Implications:</h4>
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{decision.implications}</p>
            </div>
          )}
          {/* Risks Section */}
          {decision.risks && decision.risks.length > 0 && (
            <div className="mb-3">
              <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Risks:</h4>
              <ul className="space-y-2">
                {decision.risks.map((risk, index) => (
                  <li key={index} className="p-2 border border-gray-100 dark:border-gray-700/50 rounded-md text-sm">
                     <div className="flex items-center mb-1">
                         <span className={`inline-block px-2 py-0.5 rounded text-xs font-medium mr-2 ${severityClasses[risk.severity] || 'bg-gray-100 text-gray-800'}`}>
                            {risk.severity.toUpperCase()} Risk
                         </span>
                         <span className="font-medium text-gray-700 dark:text-gray-300 capitalize">{risk.category}</span>
                     </div>
                    <p className="text-gray-500 dark:text-gray-400 mb-1"><span className="font-medium">Action:</span> {risk.mitigation}</p>
                    <p className="text-gray-700 dark:text-gray-300"><span className="font-medium">Why:</span> {risk.description}</p>
                  </li>
                ))}
              </ul>
            </div>
          )}
          {/* Related Files */}
          {decision.related_files && decision.related_files.length > 0 && (
            <div className="mb-3">
              <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1">Related Files:</h4>
              <ul className="list-disc list-inside text-sm text-gray-700 dark:text-gray-300">
                {decision.related_files.map(file => <li key={file}>{file}</li>)}
              </ul>
            </div>
          )}
          {/* Feedback specific to this decision */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
             <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Feedback on this Decision:</h4>
             <GeneralFeedback
               contextData={feedbackContext}
               onSubmitGeneralFeedback={onFeedbackSubmit}
             />
          </div>
        </div>
      )}
    </li>
  );
} 