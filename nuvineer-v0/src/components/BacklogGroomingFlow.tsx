'use client';

import { useState, useEffect, useCallback } from 'react';
import { RepositorySelector } from './RepositorySelector';


// TODO: Refactor repository selection logic from page.tsx into a reusable component
// For now, we'll include a simplified version here.

// Mock interfaces for now
interface Installation {
  id: number;
  account: { login: string };
}
interface Repository {
  id: number;
  full_name: string;
}
interface FeatureRequest {
    id: number;
    number: number;
    title: string;
    author: string;
    url: string;
}

// Assessment state
interface Assessment {
    issueId: number;
    assessment: any; // Define a proper type for this later
    status: 'success' | 'failed';
    error?: string;
}

const isRepoAnalyzed = (status: any) => status?.checkedStatus === 'completed' || status?.checkedStatus === 'initial_analysis_complete';


type GroomingStep = 'configure' | 'select_features' | 'assess' | 'review';

export function BacklogGroomingFlow() {
    const [step, setStep] = useState<GroomingStep>('configure');
    
    // Configuration state
    const [selectedInstallationId, setSelectedInstallationId] = useState<string>('');
    const [selectedRepositorySlug, setSelectedRepositorySlug] = useState<string>('');
    const [issueCount, setIssueCount] = useState<number>(50);
    const [assessmentCount, setAssessmentCount] = useState<number>(5);

    // New state for enhanced repository selection
    const [analysisType, setAnalysisType] = useState<'public' | 'private' | null>(null);
    const [installations, setInstallations] = useState<Installation[]>([]);
    const [isLoadingInstallations, setIsLoadingInstallations] = useState(false);
    const [repositories, setRepositories] = useState<Repository[]>([]);
    const [isLoadingRepositories, setIsLoadingRepositories] = useState(false);
    const [publicRepositories, setPublicRepositories] = useState<Repository[]>([]);
    const [isLoadingPublicRepos, setIsLoadingPublicRepos] = useState(false);
    const [repositoryStatuses, setRepositoryStatuses] = useState<Record<string, { checkedStatus: string; checkError: string | null }>>({});
    const [selectionError, setSelectionError] = useState<string | null>(null);


    // Triage state
    const [isTriaging, setIsTriaging] = useState<boolean>(false);
    const [featureRequests, setFeatureRequests] = useState<FeatureRequest[]>([]);
    const [triageError, setTriageError] = useState<string | null>(null);

    // Selection state
    const [selectedFeatures, setSelectedFeatures] = useState<Set<number>>(new Set());

    // Assessment state
    const [isAssessing, setIsAssessing] = useState<boolean>(false);
    const [assessments, setAssessments] = useState<Assessment[]>([]);
    const [assessmentError, setAssessmentError] = useState<string | null>(null);

    // Constitution state
    const [projectConstitution, setProjectConstitution] = useState<any | null>(null);


    // Sync state
    const [isSyncing, setIsSyncing] = useState<boolean>(false);
    const [syncStatus, setSyncStatus] = useState<Record<number, 'pending' | 'success' | 'failed'>>({});
    const [syncError, setSyncError] = useState<string | null>(null);

    // Effect to fetch installations for private repos
    useEffect(() => {
        if (analysisType === 'private') {
            const fetchInstallations = async () => {
                setIsLoadingInstallations(true);
                setSelectionError(null);
                try {
                    const res = await fetch('/api/github/installations');
                    const data = await res.json();
                    if (!res.ok) throw new Error(data.error || 'Failed to fetch installations.');
                    setInstallations(data.installations || []);
                } catch (err: any) {
                    setSelectionError(err.message);
                } finally {
                    setIsLoadingInstallations(false);
                }
            };
            fetchInstallations();
        }
    }, [analysisType]);

    // Effect to fetch repositories based on selection
    useEffect(() => {
        const fetchRepos = async () => {
            if (analysisType === 'private' && selectedInstallationId) {
                setIsLoadingRepositories(true);
                setSelectionError(null);
                try {
                    const res = await fetch(`/api/github/repositories?installationId=${selectedInstallationId}`);
                    const data = await res.json();
                    if (!res.ok) throw new Error(data.error || 'Failed to fetch repositories.');
                    setRepositories(data.repositories || []);
                } catch (err: any) {
                    setSelectionError(err.message);
                    setRepositories([]);
                } finally {
                    setIsLoadingRepositories(false);
                }
            } else if (analysisType === 'public') {
                setIsLoadingPublicRepos(true);
                setSelectionError(null);
                try {
                    const res = await fetch('/api/github/repositories?installationId=0');
                    const data = await res.json();
                    if (!res.ok) throw new Error(data.error || 'Failed to fetch public repositories.');
                    setPublicRepositories(data.repositories || []);
                } catch (err: any) {
                    setSelectionError(err.message);
                    setPublicRepositories([]);
                } finally {
                    setIsLoadingPublicRepos(false);
                }
            } else {
                setRepositories([]);
                setPublicRepositories([]);
            }
        };
        fetchRepos();
    }, [analysisType, selectedInstallationId]);

    // Effect to fetch repository statuses
    useEffect(() => {
        const allRepos = [...repositories, ...publicRepositories];
        if (allRepos.length > 0) {
            const fetchStatuses = async () => {
                const newStatuses: Record<string, { checkedStatus: string; checkError: string | null }> = {};
                const statusPromises = allRepos.map(async (repo) => {
                    const repoSlug = repo.full_name;
                    const isPublic = analysisType === 'public';
                    let url = `/api/analysis/repository-status?repositorySlug=${repoSlug}&isPublic=${isPublic}`;
                    if (!isPublic && selectedInstallationId) {
                        url += `&installationId=${selectedInstallationId}`;
                    }

                    try {
                        const res = await fetch(url);
                        const data = await res.json();
                        if (data.success) {
                            newStatuses[repoSlug] = {
                                checkedStatus: data.analysisOverallStatus,
                                checkError: null
                            };
                        } else {
                            newStatuses[repoSlug] = { checkedStatus: 'error', checkError: data.message };
                        }
                    } catch (error) {
                        newStatuses[repoSlug] = { checkedStatus: 'error', checkError: 'Failed to fetch status' };
                    }
                });
                await Promise.all(statusPromises);
                setRepositoryStatuses(newStatuses);
            };
            fetchStatuses();
        }
    }, [repositories, publicRepositories, analysisType, selectedInstallationId]);


    const handleRepoSelectionChange = useCallback((selection: { installationId: string; repositorySlug: string; }) => {
        setSelectedInstallationId(selection.installationId);
        setSelectedRepositorySlug(selection.repositorySlug);
    }, []);


    const handleTriage = useCallback(async () => {
        if (!selectedRepositorySlug) {
            setTriageError('Please select a repository first.');
            return;
        }
        setIsTriaging(true);
        setTriageError(null);

        try {
            const response = await fetch('/api/backlog-grooming/triage', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    repositorySlug: selectedRepositorySlug,
                    installationId: selectedInstallationId ? parseInt(selectedInstallationId, 10) : null,
                    issueCount,
                }),
            });

            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || 'Failed to triage issues.');
            }

            setFeatureRequests(data.feature_requests || []);
            setStep('select_features');

        } catch (error: any) {
            setTriageError(error.message);
        } finally {
            setIsTriaging(false);
        }
    }, [selectedRepositorySlug, selectedInstallationId, issueCount]);

    const handleFeatureSelection = (issueId: number) => {
        setSelectedFeatures(prevSelected => {
            const newSelected = new Set(prevSelected);
            if (newSelected.has(issueId)) {
                newSelected.delete(issueId);
            } else {
                if (newSelected.size < assessmentCount) {
                    newSelected.add(issueId);
                } else {
                    // Optional: show a notification that the limit is reached
                    alert(`You can only select up to ${assessmentCount} features.`);
                }
            }
            return newSelected;
        });
    };

    const handleAssess = useCallback(async () => {
        setIsAssessing(true);
        setAssessmentError(null);
        setStep('assess');

        const issuesToAssess = featureRequests.filter(f => selectedFeatures.has(f.id));

        try {
            // Fetch constitution before assessing
            const constitutionRes = await fetch(`/api/constitution?repositorySlug=${selectedRepositorySlug}`);
            const constitutionData = await constitutionRes.json();
            if (!constitutionRes.ok) {
                // If no constitution is found, we can proceed with a default or show an error.
                // For now, we'll proceed but log a warning.
                console.warn(constitutionData.error || 'Could not fetch project constitution. Proceeding without it.');
                setProjectConstitution(null);
            } else {
                // Fix: Extract the constitution data from the nested response
                const constitution = constitutionData.constitution || constitutionData;
                setProjectConstitution(constitution);
            }

            const response = await fetch('/api/backlog-grooming/assess', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    issues: issuesToAssess,
                    repositorySlug: selectedRepositorySlug,
                    installationId: selectedInstallationId ? parseInt(selectedInstallationId, 10) : null,
                    projectConstitution: constitutionData.constitution || {}, // Fix: Use the correct nested property
                }),
            });

            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || 'Failed to assess features.');
            }

            setAssessments(data.assessments);
            setStep('review');

        } catch (error: any) {
            setAssessmentError(error.message);
        } finally {
            setIsAssessing(false);
        }
    }, [selectedFeatures, featureRequests, selectedRepositorySlug, selectedInstallationId]);

    const handleSyncToGitHub = useCallback(async () => {
        setIsSyncing(true);
        setSyncError(null);
        
        const successfulAssessments = assessments.filter(a => a.status === 'success');
        const initialSyncStatus: Record<number, 'pending' | 'success' | 'failed'> = {};
        successfulAssessments.forEach(a => {
            initialSyncStatus[a.issueId] = 'pending';
        });
        setSyncStatus(initialSyncStatus);

        for (const assessment of successfulAssessments) {
            try {
                const issue = featureRequests.find(f => f.id === assessment.issueId);
                if (!issue) continue;

                // Do not update issues for public repos without an installation ID
                if (!selectedInstallationId || selectedInstallationId === '0') {
                    console.warn(`Skipping issue update for ${issue.number} in ${selectedRepositorySlug} because it's a public repository without an installation.`);
                    // We can either skip silently or update the status to show it was skipped.
                    // For now, let's treat it as a success from the user's perspective, but log it.
                    setSyncStatus(prev => ({ ...prev, [assessment.issueId]: 'success' }));
                    continue;
                }

                // Simple label logic based on assessment
                const recommendation = assessment.assessment.recommendation.toLowerCase();
                const labels = ['triage: complete'];
                if (recommendation === 'build') {
                    labels.push('priority: P1');
                } else {
                    labels.push('priority: P3');
                }
                
                const response = await fetch('/api/github/update-issue', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        repositorySlug: selectedRepositorySlug,
                        installationId: parseInt(selectedInstallationId, 10),
                        issueNumber: issue.number,
                        commentBody: `### AI Strategic Assessment:\n\n**Recommendation:** ${assessment.assessment.recommendation}\n\n**Rationale:** ${assessment.assessment.strategic_rationale}`,
                        labels,
                    }),
                });

                if (!response.ok) throw new Error(`Failed to update issue #${issue.number}`);

                setSyncStatus(prev => ({ ...prev, [assessment.issueId]: 'success' }));
            } catch (error) {
                 setSyncStatus(prev => ({ ...prev, [assessment.issueId]: 'failed' }));
            }
        }

        setIsSyncing(false);

    }, [assessments, selectedRepositorySlug, selectedInstallationId, featureRequests]);


    return (
        <div className="bg-white dark:bg-zinc-800 p-8 rounded-lg shadow-md">
            <h1 className="text-3xl font-bold mb-2">AI-Assisted Backlog Grooming</h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
                Let AI help you triage your backlog, identify high-value features, and assess their complexity.
            </p>

            {step === 'configure' && (
                <div>
                    <h2 className="text-xl font-semibold mb-4">Step 1: Configure Triage</h2>
                    
                    <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                             <button
                                className={`p-4 rounded-lg border-2 ${analysisType === 'public' ? 'border-amber-500 bg-amber-50' : 'border-gray-300'}`}
                                onClick={() => {
                                    setAnalysisType('public');
                                    setSelectedInstallationId('0');
                                    setSelectedRepositorySlug('');
                                }}
                            >
                                Public Repositories
                            </button>
                             <button
                                className={`p-4 rounded-lg border-2 ${analysisType === 'private' ? 'border-amber-500 bg-amber-50' : 'border-gray-300'}`}
                                onClick={() => {
                                    setAnalysisType('private');
                                    setSelectedInstallationId('');
                                    setSelectedRepositorySlug('');
                                }}
                            >
                                Private Repositories
                            </button>
                        </div>

                        {analysisType === 'private' && (
                            <div>
                                <label htmlFor="installation-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    GitHub Installation
                                </label>
                                <select
                                    id="installation-select"
                                    value={selectedInstallationId}
                                    onChange={(e) => setSelectedInstallationId(e.target.value)}
                                    className="mt-1 block w-full px-3 py-2 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 rounded-md shadow-sm"
                                    disabled={isLoadingInstallations}
                                >
                                    <option value="">{isLoadingInstallations ? "Loading..." : "Select an installation"}</option>
                                    {installations.map(inst => (
                                        <option key={inst.id} value={inst.id.toString()}>
                                            {inst.account.login}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        )}

                        {selectionError && <p className="text-red-500 text-sm">{selectionError}</p>}
                        
                        {(analysisType === 'public' || (analysisType === 'private' && selectedInstallationId)) && (
                            <div className="space-y-2">
                                {(isLoadingRepositories || isLoadingPublicRepos) ? <p>Loading repositories...</p> :
                                 (analysisType === 'public' ? publicRepositories : repositories)
                                 .filter(repo => isRepoAnalyzed(repositoryStatuses[repo.full_name]))
                                 .map(repo => (
                                     <div
                                        key={repo.id}
                                        onClick={() => setSelectedRepositorySlug(repo.full_name)}
                                        className={`p-3 rounded-md cursor-pointer border ${selectedRepositorySlug === repo.full_name ? 'bg-amber-100 border-amber-500' : 'bg-gray-50 hover:bg-gray-100'}`}
                                     >
                                         {repo.full_name}
                                     </div>
                                 ))
                                }
                            </div>
                        )}
                    </div>


                    <div className="flex gap-4 mt-4">
                        <div className="flex-1">
                            <label htmlFor="issue-count" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Issues to Scan
                            </label>
                            <input
                                type="number"
                                id="issue-count"
                                value={issueCount}
                                onChange={(e) => setIssueCount(Number(e.target.value))}
                                className="mt-1 block w-full px-3 py-2 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 rounded-md shadow-sm"
                            />
                        </div>
                        <div className="flex-1">
                            <label htmlFor="assessment-count" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Features to Assess
                            </label>
                            <input
                                type="number"
                                id="assessment-count"
                                value={assessmentCount}
                                onChange={(e) => setAssessmentCount(Number(e.target.value))}
                                className="mt-1 block w-full px-3 py-2 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-zinc-600 rounded-md shadow-sm"
                            />
                        </div>
                    </div>
                    <div className="mt-6">
                        <button
                            onClick={handleTriage}
                            disabled={isTriaging || !selectedRepositorySlug}
                            className="w-full px-6 py-3 bg-amber-600 hover:bg-amber-700 text-white rounded-md shadow-sm text-base font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                        >
                            {isTriaging ? 'Finding Features...' : 'Find Feature Requests'}
                        </button>
                    </div>
                    {triageError && <p className="text-red-500 mt-4">{triageError}</p>}
                </div>
            )}
            
            {step === 'select_features' && (
                 <div>
                    <h2 className="text-xl font-semibold mb-4">Step 2: Select Features to Assess</h2>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Found {featureRequests.length} potential feature requests. Please select up to <strong>{assessmentCount}</strong> to assess.
                    </p>
                    <div className="space-y-3">
                        {featureRequests.map(issue => (
                            <div key={issue.id} className="flex items-center p-3 border border-gray-200 dark:border-zinc-700 rounded-lg">
                                <input
                                    type="checkbox"
                                    id={`issue-${issue.id}`}
                                    checked={selectedFeatures.has(issue.id)}
                                    onChange={() => handleFeatureSelection(issue.id)}
                                    className="h-5 w-5 rounded border-gray-300 text-amber-600 focus:ring-amber-500"
                                />
                                <div className="ml-4">
                                    <label htmlFor={`issue-${issue.id}`} className="font-medium text-gray-900 dark:text-gray-100 cursor-pointer">
                                        {issue.title}
                                    </label>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        #{issue.number} by {issue.author}
                                        <a href={issue.url} target="_blank" rel="noopener noreferrer" className="ml-2 text-amber-600 hover:underline">
                                            View on GitHub
                                        </a>
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                     <div className="mt-6">
                        <button
                            onClick={handleAssess}
                            disabled={selectedFeatures.size === 0}
                            className="w-full px-6 py-3 bg-amber-600 hover:bg-amber-700 text-white rounded-md shadow-sm text-base font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                        >
                            Assess {selectedFeatures.size} Selected Feature(s)
                        </button>
                    </div>
                </div>
            )}
            
            {step === 'assess' && (
                 <div>
                    <h2 className="text-xl font-semibold mb-4">Step 3: Assessing Features</h2>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        The AI is performing a strategic assessment on the selected features. This may take a moment.
                    </p>
                    {/* TODO: Build out assessment progress UI */}
                    {isAssessing && (
                        <div className="mt-4 text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
                            <p className="mt-2">Assessing features...</p>
                        </div>
                    )}
                </div>
            )}

            {step === 'review' && (
                <div>
                    <h2 className="text-xl font-semibold mb-4">Step 4: Review & Sync Assessments</h2>
                     <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Here are the strategic assessments for the selected features. Review the recommendations and then sync the results back to GitHub.
                    </p>
                    <div className="space-y-4">
                        {assessments.map(assessment => {
                            const issue = featureRequests.find(f => f.id === assessment.issueId);
                            return (
                                <div key={assessment.issueId} className="p-4 border border-gray-200 dark:border-zinc-700 rounded-lg">
                                    <h3 className="font-bold text-lg">{issue?.title || `Issue #${issue?.number}`}</h3>
                                    {assessment.status === 'success' ? (
                                        <div>
                                            <p className={`font-semibold text-lg ${assessment.assessment.recommendation === 'BUILD' ? 'text-green-500' : 'text-yellow-500'}`}>
                                                Recommendation: {assessment.assessment.recommendation}
                                            </p>
                                            <p className="text-sm mt-1"><strong>Rationale:</strong> {assessment.assessment.strategic_rationale}</p>
                                            <p className="text-xs mt-2"><strong>Complexity:</strong> {assessment.assessment.complexity_assessment.implementation_complexity} | <strong>User Value:</strong> {assessment.assessment.complexity_assessment.user_value_delivered}</p>
                                        </div>
                                    ) : (
                                        <p className="text-red-500">Assessment failed: {assessment.error}</p>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                    <div className="mt-6">
                        <button
                            onClick={handleSyncToGitHub}
                            disabled={isSyncing || !selectedInstallationId || selectedInstallationId === '0'}
                            className="w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm text-base font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                        >
                           {isSyncing ? 'Syncing...' : 'Sync Assessments to GitHub'}
                        </button>
                         {(!selectedInstallationId || selectedInstallationId === '0') && <p className="text-xs text-center mt-2 text-gray-500">Sync to GitHub is disabled for public repositories without an app installation.</p>}
                    </div>
                </div>
            )}

        </div>
    );
} 