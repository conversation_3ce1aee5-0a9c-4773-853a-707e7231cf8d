'use client';

import { useState, useEffect } from 'react';

interface Installation {
  id: number;
  account: { login: string };
}
interface Repository {
  id: number;
  full_name: string;
}

interface RepositorySelectorProps {
    onSelectionChange: (selection: { installationId: string; repositorySlug: string; }) => void;
}

export function RepositorySelector({ onSelectionChange }: RepositorySelectorProps) {
    const [installations, setInstallations] = useState<Installation[]>([]);
    const [selectedInstallationId, setSelectedInstallationId] = useState<string>('');
    const [isLoadingInstallations, setIsLoadingInstallations] = useState<boolean>(true);
    const [installationError, setInstallationError] = useState<string | null>(null);

    const [repositories, setRepositories] = useState<Repository[]>([]);
    const [selectedRepositorySlug, setSelectedRepositorySlug] = useState<string>('');
    const [isLoadingRepositories, setIsLoadingRepositories] = useState<boolean>(false);
    const [repositoryError, setRepositoryError] = useState<string | null>(null);

    // Fetch installations on component mount
    useEffect(() => {
        setIsLoadingInstallations(true);
        fetch('/api/github/installations')
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    setInstallations(data.installations);
                } else {
                    throw new Error(data.error || 'Failed to fetch installations.');
                }
            })
            .catch(err => setInstallationError(err.message))
            .finally(() => setIsLoadingInstallations(false));
    }, []);

    // Fetch repositories when an installation is selected
    useEffect(() => {
        if (!selectedInstallationId) {
            setRepositories([]);
            setSelectedRepositorySlug('');
            return;
        }
        setIsLoadingRepositories(true);
        setRepositoryError(null);
        fetch(`/api/github/repositories?installationId=${selectedInstallationId}`)
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    setRepositories(data.repositories);
                } else {
                    throw new Error(data.error || 'Failed to fetch repositories.');
                }
            })
            .catch(err => setRepositoryError(err.message))
            .finally(() => setIsLoadingRepositories(false));
    }, [selectedInstallationId]);

    // Notify parent component of selection change
    useEffect(() => {
        onSelectionChange({ installationId: selectedInstallationId, repositorySlug: selectedRepositorySlug });
    }, [selectedInstallationId, selectedRepositorySlug, onSelectionChange]);


    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border border-gray-200 dark:border-zinc-700 rounded-lg">
            <div>
                <label htmlFor="installation" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    GitHub Installation
                </label>
                <select
                    id="installation"
                    value={selectedInstallationId}
                    onChange={(e) => setSelectedInstallationId(e.target.value)}
                    disabled={isLoadingInstallations}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm rounded-md"
                >
                    <option value="">{isLoadingInstallations ? 'Loading...' : 'Select Installation'}</option>
                    {installations.map(inst => (
                        <option key={inst.id} value={inst.id}>
                            {inst.account.login}
                        </option>
                    ))}
                </select>
                {installationError && <p className="text-red-500 text-xs mt-1">{installationError}</p>}
            </div>
            <div>
                <label htmlFor="repository" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Repository
                </label>
                <select
                    id="repository"
                    value={selectedRepositorySlug}
                    onChange={(e) => setSelectedRepositorySlug(e.target.value)}
                    disabled={!selectedInstallationId || isLoadingRepositories}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm rounded-md"
                >
                    <option value="">{isLoadingRepositories ? 'Loading...' : 'Select Repository'}</option>
                    {repositories.map(repo => (
                        <option key={repo.id} value={repo.full_name}>
                            {repo.full_name}
                        </option>
                    ))}
                </select>
                 {repositoryError && <p className="text-red-500 text-xs mt-1">{repositoryError}</p>}
            </div>
        </div>
    );
} 