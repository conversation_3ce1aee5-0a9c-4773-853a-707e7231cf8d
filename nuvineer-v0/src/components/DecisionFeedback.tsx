'use client'

import React, { useState } from 'react';

interface DecisionFeedbackProps {
  decisionId: string;
  onSubmitFeedback: (decisionId: string, reaction: string | null, comment: string) => void; // Callback for submission
}

const DecisionFeedback: React.FC<DecisionFeedbackProps> = ({ decisionId, onSubmitFeedback }) => {
  const [selectedReaction, setSelectedReaction] = useState<string | null>(null);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const reactions = ['👍', '👎', '🤔', '💡', '❓']; // Example reactions

  const handleReactionClick = (reaction: string) => {
    setSelectedReaction(reaction === selectedReaction ? null : reaction);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedReaction && !comment.trim()) {
      // Optional: Add some validation feedback to the user
      return;
    }
    setIsSubmitting(true);
    try {
      // In a real app, call the onSubmitFeedback prop which would likely trigger an API call
      await onSubmitFeedback(decisionId, selectedReaction, comment.trim());
      console.log(`Feedback submitted for ${decisionId}:`, { reaction: selectedReaction, comment });
      setSubmitted(true);
      // Reset form after a short delay
      setTimeout(() => {
         setSelectedReaction(null);
         setComment('');
         setSubmitted(false);
      }, 2000);
    } catch (error) {
      console.error("Error submitting feedback:", error);
      // Handle error state, show message to user
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitted) {
    return <div className="text-green-600 dark:text-green-400 p-2">Feedback submitted! Thank you.</div>;
  }

  return (
    <div className="mt-4 p-3 border rounded-md bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Provide feedback on this decision:</p>
      <div className="flex space-x-2 mb-3">
        {reactions.map((reaction) => (
          <button
            key={reaction}
            type="button"
            onClick={() => handleReactionClick(reaction)}
            className={`p-1.5 rounded-full text-xl transition-transform duration-150 ease-in-out ${
              selectedReaction === reaction
                ? 'bg-blue-100 dark:bg-blue-800 ring-2 ring-blue-500 scale-110'
                : 'hover:bg-gray-200 dark:hover:bg-gray-700 scale-100'
            }`}
            aria-pressed={selectedReaction === reaction}
            aria-label={`React with ${reaction}`}
          >
            {reaction}
          </button>
        ))}
      </div>
      <form onSubmit={handleSubmit}>
        <textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder="Add an optional comment... (e.g., Why this reaction? Is something unclear?)"
          rows={2}
          className="w-full p-2 border rounded-md text-sm bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:text-gray-200"
          disabled={isSubmitting}
        />
        <button
          type="submit"
          disabled={isSubmitting || (!selectedReaction && !comment.trim())}
          className="mt-2 px-4 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-offset-gray-900"
        >
          {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
        </button>
      </form>
    </div>
  );
};

export default DecisionFeedback; 