import { getPR, getRecentPRs, getPRFiles, getPRComments } from './analyzer/github.js';
import { extractKnowledge } from './analyzer/index.js';
import { saveKnowledge, createADR } from './knowledge/repository.js';
import { logger } from './utils/logger.js';

/**
 * Analyze a specific PR and extract architectural knowledge
 * @param {string} owner - GitHub repository owner
 * @param {string} repo - GitHub repository name
 * @param {number} prNumber - Pull request number
 * @param {string} outputDir - Output directory
 * @returns {Object} - Result of the analysis
 */
export async function analyzePR(owner, repo, prNumber, outputDir = './knowledge') {
  try {
    // Fetch PR details
    const pr = await getPR(owner, repo, prNumber);
    
    // Fetch PR files
    const files = await getPRFiles(owner, repo, prNumber);
    
    // Fetch PR comments
    const comments = await getPRComments(owner, repo, prNumber);
    
    // Extract knowledge from PR
    const knowledge = await extractKnowledge(pr, files, comments);
    
    // Save knowledge to repository
    const result = await saveKnowledge(knowledge, prNumber, outputDir);
    
    return result;
  } catch (error) {
    logger.error(`Error analyzing PR #${prNumber}:`, error);
    throw error;
  }
}

/**
 * Analyze recent PRs and extract architectural knowledge
 * @param {string} owner - GitHub repository owner
 * @param {string} repo - GitHub repository name
 * @param {Object} options - Analysis options
 * @param {number} options.count - Number of recent PRs to analyze (default: 5)
 * @param {string} options.olderThan - Analyze PRs merged before this date (ISO string)
 * @param {string} options.newerThan - Analyze PRs merged after this date (ISO string)
 * @param {string} options.outputDir - Output directory
 * @returns {Object} - Results of the analysis
 */
export async function analyzeRecentPRs(owner, repo, options = {}) {
  try {
    const count = options.count || 5;
    const outputDir = options.outputDir || './knowledge';

    // Fetch recent PRs with date filtering
    const prs = await getRecentPRs(owner, repo, {
      count,
      olderThan: options.olderThan,
      newerThan: options.newerThan
    });
    
    logger.info(`Found ${prs.length} PRs to analyze`);
    
    let analyzed = 0;
    let withKnowledge = 0;
    
    // Analyze each PR
    for (const pr of prs) {
      try {
        logger.info(`Analyzing PR #${pr.number}: ${pr.title}`);
        
        const result = await analyzePR(owner, repo, pr.number, outputDir);
        analyzed++;
        
        if (result.categories.length > 0) {
          withKnowledge++;
          logger.success(`Found architectural knowledge in PR #${pr.number}`);
        } else {
          logger.info(`No significant architectural knowledge found in PR #${pr.number}`);
        }
      } catch (error) {
        logger.error(`Error analyzing PR #${pr.number}:`, error);
      }
    }
    
    return {
      analyzed,
      withKnowledge
    };
  } catch (error) {
    logger.error('Error analyzing recent PRs:', error);
    throw error;
  }
}

/**
 * Generate an Architecture Decision Record (ADR) from a PR
 * @param {string} owner - GitHub repository owner
 * @param {string} repo - GitHub repository name
 * @param {number} prNumber - Pull request number
 * @param {string} title - Title for the ADR
 * @param {string} outputDir - Output directory
 * @returns {string} - Path to the generated ADR
 */
export async function generateADR(owner, repo, prNumber, title, outputDir = './adrs') {
  try {
    // Fetch PR details
    const pr = await getPR(owner, repo, prNumber);
    
    // Fetch PR files
    const files = await getPRFiles(owner, repo, prNumber);
    
    // Fetch PR comments
    const comments = await getPRComments(owner, repo, prNumber);
    
    // Extract knowledge from PR
    const knowledge = await extractKnowledge(pr, files, comments);
    
    // Create ADR from knowledge
    const adrPath = await createADR(knowledge, title, prNumber, outputDir);
    
    return adrPath;
  } catch (error) {
    logger.error(`Error generating ADR from PR #${prNumber}:`, error);
    throw error;
  }
}
