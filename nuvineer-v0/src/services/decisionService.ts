import { DecisionReference } from '../utils/decisionUtils';

interface FetchDecisionsParams {
  repositorySlug: string;
  installationId: number;
  limit?: number;
}

interface FetchDecisionByIdParams {
  id: string;
  repositorySlug: string;
  installationId: number;
}

/**
 * Fetches a list of decisions from the API
 */
export async function fetchDecisions({
  repositorySlug,
  installationId,
  limit = 50
}: FetchDecisionsParams): Promise<DecisionReference[]> {
  const params = new URLSearchParams({
    repository: repositorySlug,
    installationId: installationId.toString(),
    limit: limit.toString()
  });

  const response = await fetch(`/api/decisions?${params}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch decisions: ${response.statusText}`);
  }

  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Failed to fetch decisions');
  }

  return data.decisions || [];
}

/**
 * Fetches a specific decision by ID
 */
export async function fetchDecisionById({
  id,
  repositorySlug,
  installationId
}: FetchDecisionByIdParams): Promise<DecisionReference | null> {
  console.log(`[fetchDecisionById] Fetching decision: ${id} for repo ${repositorySlug}, installation ${installationId}`);
  
  const params = new URLSearchParams({
    repositorySlug,
    installationId: installationId.toString()
  });

  const url = `/api/decisions/${encodeURIComponent(id)}?${params}`;
  console.log(`[fetchDecisionById] Request URL: ${url}`);

  const response = await fetch(url);
  
  if (!response.ok) {
    console.error(`[fetchDecisionById] HTTP error: ${response.status} ${response.statusText}`);
    if (response.status === 404) {
      console.log(`[fetchDecisionById] Decision ${id} not found`);
      return null; // Decision not found
    }
    throw new Error(`Failed to fetch decision: ${response.statusText}`);
  }

  const data = await response.json();
  console.log(`[fetchDecisionById] Response for ${id}:`, data);
  
  if (!data.success) {
    console.error(`[fetchDecisionById] API error for ${id}:`, data.error);
    throw new Error(data.error || 'Failed to fetch decision');
  }

  console.log(`[fetchDecisionById] Successfully fetched decision ${id}`);
  return data.decision || null;
}

/**
 * Fetches multiple decisions by their IDs
 */
export async function fetchDecisionsByIds({
  ids,
  repositorySlug,
  installationId
}: {
  ids: string[];
  repositorySlug: string;
  installationId: number;
}): Promise<Record<string, DecisionReference>> {
  console.log(`[fetchDecisionsByIds] Fetching ${ids.length} decisions for repo ${repositorySlug}, installation ${installationId}`);
  console.log(`[fetchDecisionsByIds] Decision IDs to fetch:`, ids);
  
  // Since there's no batch API, we'll fetch them individually
  // In a production app, you might want to implement a batch API
  const decisions: Record<string, DecisionReference> = {};
  
  const fetchPromises = ids.map(async (id) => {
    try {
      const decision = await fetchDecisionById({ id, repositorySlug, installationId });
      if (decision) {
        decisions[id] = decision;
        console.log(`[fetchDecisionsByIds] Successfully fetched decision ${id}`);
      } else {
        console.log(`[fetchDecisionsByIds] Decision ${id} returned null (not found)`);
      }
    } catch (error) {
      console.warn(`[fetchDecisionsByIds] Failed to fetch decision ${id}:`, error);
      // Continue with other decisions even if one fails
    }
  });

  await Promise.all(fetchPromises);
  
  console.log(`[fetchDecisionsByIds] Final results: ${Object.keys(decisions).length} decisions fetched out of ${ids.length} requested`);
  console.log(`[fetchDecisionsByIds] Successfully fetched decisions:`, Object.keys(decisions));
  
  return decisions;
}

/**
 * Extracts decision IDs from text containing decision references
 */
export function extractDecisionIds(text: string): string[] {
  // Updated regex to handle multiple decision ID formats:
  // - "Decision decision_commit_231281ad_1693247798000_1752571762511_25161dd9" (commit-based format) - PRIMARY
  // - "Decision decision_1342_1752529271439_e9fb892b" (numeric-based format) - PRIMARY  
  // - "Decision ID-123" (legacy simple format) - FALLBACK ONLY
  const decisionIdRegex = /Decision (ID-[a-zA-Z0-9_-]+|decision_(?:commit_)?[a-zA-Z0-9_-]+)/g;
  const ids: string[] = [];
  let match;

  console.log(`[extractDecisionIds] Parsing text: "${text.substring(0, 200)}..."`);

  // Test the regex against known formats (for debugging) - Full decision IDs are now primary
  const testFormats = [
    'Decision decision_commit_231281ad_1693247798000_1752571762511_25161dd9',
    'Decision decision_1342_1752529271439_e9fb892b',
    'Decision ID-123' // Legacy format for backward compatibility
  ];
  console.log(`[extractDecisionIds] Testing regex against known formats:`);
  const testRegex = /Decision (ID-[a-zA-Z0-9_-]+|decision_(?:commit_)?[a-zA-Z0-9_-]+)/;
  testFormats.forEach(testText => {
    const testMatch = testText.match(testRegex);
    console.log(`  "${testText}" → ${testMatch ? testMatch[1] : 'NO MATCH'}`);
  });

  while ((match = decisionIdRegex.exec(text)) !== null) {
    let id = match[1];
    // Remove "ID-" prefix if present, since the API expects the raw ID
    if (id.startsWith('ID-')) {
      id = id.substring(3);
    }
    if (!ids.includes(id)) {
      ids.push(id);
      console.log(`[extractDecisionIds] Found decision ID: ${id}`);
    }
  }

  console.log(`[extractDecisionIds] Extracted ${ids.length} decision IDs:`, ids);
  return ids;
}

/**
 * Extracts all decision IDs from an array of text strings
 */
export function extractDecisionIdsFromTexts(texts: (string | string[])[]): string[] {
  const allIds: string[] = [];
  
  texts.forEach(textOrArray => {
    if (Array.isArray(textOrArray)) {
      textOrArray.forEach(text => {
        if (typeof text === 'string') {
          const ids = extractDecisionIds(text);
          ids.forEach(id => {
            if (!allIds.includes(id)) {
              allIds.push(id);
            }
          });
        }
      });
    } else if (typeof textOrArray === 'string') {
      const ids = extractDecisionIds(textOrArray);
      ids.forEach(id => {
        if (!allIds.includes(id)) {
          allIds.push(id);
        }
      });
    }
  });

  return allIds;
} 