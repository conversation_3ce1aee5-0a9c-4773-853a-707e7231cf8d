import { generatePrompt, generateAlternativesPrompt, generateAlignmentPrompt, generateRelationshipAnalysisPrompt, generateDryRunFeedbackPrompt, generateDesignDocConceptExtractionPrompt, generateKnowledgeGraphTriplesPrompt, generateUserJourneysDraftPrompt, generateJourneyImpactAnalysisPrompt, generateTaskAnalysisPrompt } from './analyzer/prompt.js';
import { getRepositoryNamespace } from './lib/pinecone-utils';
import Anthropic from '@anthropic-ai/sdk';
import OpenAI from 'openai';
import { Pinecone } from '@pinecone-database/pinecone';
import { resolve, dirname, extname, basename } from 'path';
import { fileURLToPath } from 'url';
import { promises as fs } from 'fs';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = resolve(__dirname, '..');
// --- Initialize SDK Clients ---

// Helper to safely get env vars or use defaults
function getEnvVar(name, defaultValue = null) {
  const value = process.env[name];
  // Log whether we found the variable for debugging (consider reducing verbosity later)
  console.log(`Environment variable ${name}: ${value ? 'Found' : 'Not found'}${defaultValue && !value ? ' (using default)' : ''}`);
  return value || defaultValue;
}

/**
 * Extracts the repository slug (owner/repo) from a GitHub PR URL.
 * @param {string} url - The GitHub PR URL.
 * @returns {string|null} - The repository slug or null if parsing fails.
 */
const getRepoSlug = (url) => {
    if (!url) return null;
    try {
        const parsedUrl = new URL(url);
        if (parsedUrl.hostname !== 'github.com') {
            return null; // Not a github.com URL
        }
        const pathParts = parsedUrl.pathname.split('/').filter(part => part.length > 0);
        // Expected format: /owner/repo/pull/number
        if (pathParts.length >= 2) {
            return `${pathParts[0]}/${pathParts[1]}`;
        }
        return null;
    } catch (error) {
        console.error(`[getRepoSlug] Error parsing URL ${url}:`, error);
        return null;
    }
};

// Initialize clients lazily to avoid build-time errors
let anthropic = null;
let openai = null;
let pinecone = null;
let pineconeIndex = null;
let conceptsIndex = null;

const pineconeIndexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';
const pineconeNamespace = process.env.PINECONE_NAMESPACE || 'default';
const conceptsIndexName = process.env.PINECONE_CONCEPTS_INDEX_NAME || 'architecture-concepts';

function initializeClients() {
  if (!anthropic) {
    const apiKey = getEnvVar('ANTHROPIC_API_KEY');
    if (apiKey) {
      anthropic = new Anthropic({ apiKey });
    }
  }

  if (!openai) {
    const apiKey = getEnvVar('OPENAI_API_KEY');
    if (apiKey) {
      openai = new OpenAI({ apiKey });
    }
  }

  if (!pinecone) {
    const apiKey = getEnvVar('PINECONE_API_KEY');
    if (apiKey) {
      pinecone = new Pinecone({ apiKey });
      pineconeIndex = pinecone.Index(pineconeIndexName);
      conceptsIndex = pinecone.Index(conceptsIndexName);
    }
  }
}
// --- END ADDED

// ---> ADDED: Supabase Client Initialization
/** @type {import('@supabase/supabase-js').SupabaseClient | null} */
let supabase = null; // Initialize to null explicitly

const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
const supabaseServiceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY');

if (supabaseUrl && supabaseServiceRoleKey) {
  try {
      supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
        auth: {
          persistSession: false // Use service role key, no user session needed
        }
      });
      console.log("[Supabase] Client initialized successfully.");
  } catch (error) {
       console.error("[Supabase] Error initializing Supabase client:", error);
       supabase = null; // Ensure it's null on error
  }
} else {
  console.warn("[Supabase] URL or Service Role Key not found in environment variables. Relationship storage will be disabled.");
  // supabase is already null from initialization
}
// --- END ADDED

/**
 * Updates the status of a PR in the repository_pr_analysis_status table.
 * @param {string} repositorySlug - The slug of the repository.
 * @param {number} prNumber - The PR number (or a commit hash-based ID).
 * @param {string} newStatus - The new status to set (e.g., 'skipped_llm_light').
 * @param {number} installationId - The GitHub App installation ID.
 */
async function updatePrStatus(repositorySlug, prNumber, newStatus, installationId) {
  const logPrefix = `[Supabase Update - ${repositorySlug}#${prNumber}]`;
  if (!supabase) {
    console.warn(`${logPrefix} Supabase client not available. Cannot update status to '${newStatus}'.`);
    return;
  }
  try {
    console.log(`${logPrefix} Updating status to '${newStatus}'.`);
    const { error } = await supabase
      .from('repository_pr_analysis_status')
      .update({ status: newStatus })
      .eq('repository_slug', repositorySlug)
      .eq('pr_number', prNumber)
      .eq('installation_id', installationId);

    if (error) {
      console.error(`${logPrefix} Error updating status:`, error);
    } else {
      console.log(`${logPrefix} Successfully updated status.`);
    }
  } catch (error) {
    console.error(`${logPrefix} Unexpected error during status update:`, error);
  }
}

// --- END ADDED

// Validate index existence and configuration
async function validatePineconeIndex() {
  try {
    const indexStats = await pineconeIndex.describeIndexStats();
    console.log(`[Pinecone] Index stats: ${JSON.stringify(indexStats)}`);
    
    // Validate dimension matches our embedding model
    const expectedDimension = 1536; // OpenAI text-embedding-3-small dimension
    if (indexStats.dimension !== expectedDimension) {
      console.warn(`[Pinecone] Warning: Index dimension (${indexStats.dimension}) does not match expected dimension (${expectedDimension})`);
    }
    
    return true;
  } catch (error) {
    console.error("[Pinecone] Error validating index:", error);
    throw new Error(`Pinecone index validation failed: ${error.message || error}`);
  }
}

// Initialize Pinecone with validation
console.log(`[Pinecone] Initializing for index: ${pineconeIndexName} (namespace: ${pineconeNamespace})`);
validatePineconeIndex().catch(error => {
  console.error("[Pinecone] Critical: Failed to validate Pinecone index:", error);
});

// --- Implementation Functions ---

/**
 * Calls the Anthropic Claude API.
 * @param {string} prompt - The prompt to send to the LLM.
 * @param {string} model - The Claude model to use (e.g., 'claude-3-sonnet-20240229').
 * @returns {Promise<object>} - The parsed JSON response from the LLM.
 */
// Use model from env, fallback to a reasonable default
const defaultClaudeModel = process.env.ANTHROPIC_MODEL || 'claude-sonnet-4-20250514';
async function callLLM(prompt, model = defaultClaudeModel, returnRawText = false) {
  const callId = `llm-call-${Date.now()}`;
  console.log(`[${callId}] [LLM Request] Sending prompt to Claude model: ${model}. Expecting ${returnRawText ? 'raw text' : 'JSON'}.`);

  // --> ADDED: Log the full prompt
  console.log(`[${callId}] [LLM Request] Full Prompt:\n--- START PROMPT ---\n${prompt}\n--- END PROMPT ---`);
  // <-- END ADDED

  let retries = 0;
  const maxRetries = 5;
  const initialDelay = 2000; // Start with 2 seconds

  while (retries < maxRetries) {
  try {
    // Claude requires the prompt within the 'messages' structure.
    // It works best if the final instruction for JSON output is clear.
    const msg = await anthropic.messages.create({
      model: model,
      max_tokens: 4096, // Adjust as needed
      temperature: 0.1,
      messages: [
          { role: "user", content: prompt }
        ],
      // Although Claude doesn't have a dedicated JSON mode like OpenAI,
      // prompting it clearly to *only* output JSON is usually effective.
      // Ensure the prompt itself explicitly asks for JSON output.
    });

    console.log(`[${callId}] [LLM Response] Received response from Claude.`);

    const responseText = msg.content[0].type === 'text' ? msg.content[0].text : '';
    // --> ADDED: Log the full raw response text
    console.log(`[${callId}] [LLM Response] Raw Response Text:\n--- START RESPONSE ---\n${responseText}\n--- END RESPONSE ---`);
    // <-- END ADDED

    if (returnRawText) {
      console.log(`[${callId}] [LLM Response] Returning raw text as requested.`);
      return responseText; // Return raw text for feedback
    }

    // Attempt to extract JSON object/array from the response text
    let jsonString = responseText;
    const firstBrace = responseText.indexOf('{');
    const firstBracket = responseText.indexOf('[');
    const lastBrace = responseText.lastIndexOf('}');
    const lastBracket = responseText.lastIndexOf(']');

    let startIndex = -1;
    let endIndex = -1;

    // Determine the start index (first '{' or '[')
    if (firstBrace !== -1 && firstBracket !== -1) {
        startIndex = Math.min(firstBrace, firstBracket);
    } else if (firstBrace !== -1) {
        startIndex = firstBrace;
    } else if (firstBracket !== -1) {
        startIndex = firstBracket;
    }

    // Determine the end index (corresponding last '}' or ']')
    // This logic prioritizes matching brackets/braces but includes fallbacks.
    if (startIndex === firstBrace && lastBrace > startIndex) {
        endIndex = lastBrace;
    } else if (startIndex === firstBracket && lastBracket > startIndex) {
        endIndex = lastBracket;
    } else if (startIndex !== -1) { // If we found a start but couldn't match the type
        if (lastBrace > startIndex && (lastBracket === -1 || lastBrace > lastBracket)) {
             // Prefer last brace if it's after the start and there's no bracket or the brace is later
            endIndex = lastBrace;
        } else if (lastBracket > startIndex) {
            // Otherwise use the last bracket if it's after the start
            endIndex = lastBracket;
        }
    }


    if (startIndex !== -1 && endIndex !== -1 && endIndex >= startIndex) {
      jsonString = responseText.substring(startIndex, endIndex + 1);
      console.log("[LLM Response] Extracted potential JSON string.");
    } else {
       console.warn("[LLM Response] Could not reliably identify JSON block start/end markers. Attempting to parse the whole cleaned text.");
       // Fallback to old cleaning method if extraction fails
       jsonString = responseText.replace(/```json\n|\n```/g, '').trim();
    }

    // Try parsing the extracted or cleaned string
    const parsedResult = JSON.parse(jsonString);
    console.log(`[${callId}] [LLM Response] Successfully parsed JSON response.`);
    return parsedResult;

  } catch (error) {
      if (error instanceof Anthropic.APIError && error.status === 429) {
        retries++;
        if (retries >= maxRetries) {
          console.error(`[${callId}] Max retries (${maxRetries}) reached for rate-limited request. Aborting.`);
          throw error;
        }
        // Exponential backoff with jitter
        const delay = initialDelay * Math.pow(2, retries - 1) + Math.random() * 1000;
        console.warn(`[${callId}] Rate limit exceeded. Retrying in ${Math.round(delay / 1000)}s... (Attempt ${retries}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
    console.error(`[${callId}] Error calling Anthropic API:`, error);
    // Consider more robust error handling, e.g., retries or specific error types
    throw new Error(`Anthropic API call failed: ${error.message}`);
  }
    }
  }
  // This should not be reachable if the loop is correct, but provides a safeguard.
  throw new Error(`Exceeded max retries for LLM call to model ${model}.`);
}

/**
 * Generates an embedding using OpenAI API.
 * @param {string} text - The text to embed.
 * @param {string} model - The OpenAI embedding model to use.
 * @returns {Promise<number[]>} - The embedding vector.
 */
async function generateEmbedding(text, model="text-embedding-3-small") {
    const callId = `embedding-call-${Date.now()}`;
    console.log(`[${callId}] [Embedding] Generating OpenAI embedding using ${model}`);
    try {
        const response = await openai.embeddings.create({
            model: model,
            input: text.replace(/\n/g, ' '), // API recommends replacing newlines
            encoding_format: "float",
        });
        const embedding = response.data[0].embedding;
        console.log(`[${callId}] [Embedding] Successfully generated embedding vector (length: ${embedding?.length})`);
        return embedding;
    } catch (error) {
        console.error(`[${callId}] [Embedding] Error generating OpenAI embedding:`, error);
        throw new Error(`OpenAI embedding generation failed: ${error.message}`);
    }
}

/**
 * Queries the Pinecone index for relevant past decisions using OpenAI embeddings.
 * @param {object} decision - The extracted decision object (used to generate query text).
 * @param {number} k - The number of relevant documents to retrieve.
 * @param {object} [options] - Additional query options.
 * @param {number} [options.minScore] - Minimum similarity score threshold (0-1).
 * @param {string} [options.filter] - Optional metadata filter expression.
 * @returns {Promise<string>} - A string containing the formatted context from relevant documents, or an empty string if none found/error.
 */
export async function queryRAG(decision, k = 3, options = {}, targetNamespace) {
  initializeClients();
  if (!targetNamespace) {
    console.error(`[RAG Query - Decision: "${decision.title}"] CRITICAL: targetNamespace not provided. Aborting query.`);
    return { contextString: "", fullMatches: [] };
  }
  const currentActiveNamespace = targetNamespace;
  console.log(`Targeting namespace: ${currentActiveNamespace}`);
  
  try {
      const stats = await pineconeIndex.describeIndexStats();
      if (!stats.namespaces || !stats.namespaces[currentActiveNamespace]) {
          console.warn(`[RAG Query - Decision: "${decision.title}"] Namespace '${currentActiveNamespace}' does not appear to exist in Pinecone index stats. Querying may fail or return no results.`);
      }
  } catch (statsError) {
      console.error(`[RAG Query - Decision: "${decision.title}"] Error fetching index stats for namespace check:`, statsError);
      // Potentially return or throw if stats check is critical
  }

  const { minScore = 0.7, filter = null } = options;
  const queryText = `${decision.title}\n${decision.description}`;
  const logPrefix = `[RAG Query - Dec: "${decision.title}" Ns: ${currentActiveNamespace}]`;

  // Default filter to exclude superseded decisions
  const baseFilter = { 'is_superseded': false };
  let combinedFilter = baseFilter;

  if (filter) {
    try {
      const userFilter = JSON.parse(filter);
      // Combine user's filter with the base filter
      combinedFilter = { $and: [baseFilter, userFilter] };
      console.log(`${logPrefix} Combined user filter with base filter (exclude superseded).`);
    } catch (e) {
      console.error(`${logPrefix} Invalid user-provided filter (must be JSON parsable string): ${filter}. Using base filter only.`, e);
      // If user filter is invalid, proceed with base filter only
    }
  }

  console.log(`${logPrefix} Starting query. Params: k=${k}, minScore=${minScore}, finalFilter=${JSON.stringify(combinedFilter)}`);

  try {
    // 1. Generate embedding for the query text
    console.log(`${logPrefix} Generating embedding...`);
    const queryEmbedding = await generateEmbedding(queryText);
    if (!queryEmbedding) {
        console.warn(`${logPrefix} Failed to generate query embedding. Skipping RAG query.`);
        return { contextString: "", fullMatches: [] };
    }
    console.log(`${logPrefix} Embedding generated successfully.`);

    // 2. Prepare query parameters
    const queryParams = {
      topK: k,
      vector: queryEmbedding,
      includeMetadata: true,
      includeValues: false, // Keep false unless scores needed
      filter: combinedFilter // Use the combined or base filter
    };
    console.log(`${logPrefix} Prepared Pinecone query parameters.`);
    if (process.env.DEBUG === 'true') {
        console.debug(`${logPrefix} Pinecone query params (excluding vector):`, { ...queryParams, vector: '[vector hidden]' });
    }

    // 3. Query Pinecone index
    console.log(`${logPrefix} Executing Pinecone query...`);
    const queryResponse = await pineconeIndex.namespace(currentActiveNamespace).query(queryParams);
    const rawMatchCount = queryResponse.matches?.length || 0;
    console.log(`${logPrefix} Pinecone query returned ${rawMatchCount} raw matches.`);
    if (process.env.DEBUG === 'true' && rawMatchCount > 0) {
        console.debug(`${logPrefix} Raw matches (IDs and Scores):`, queryResponse.matches.map(m => ({ id: m.id, score: m.score, title: m.metadata?.title || 'N/A' })));
    }

    // Log discarded matches if DEBUG is true
    if (process.env.DEBUG === 'true' && queryResponse.matches) {
        const discardedMatches = queryResponse.matches.filter(match => match.score < minScore);
        if (discardedMatches.length > 0) {
            console.debug(`${logPrefix} ${discardedMatches.length} matches DISCARDED due to score < ${minScore}:`,
                discardedMatches.map(m => ({ id: m.id, score: m.score, title: m.metadata?.title || 'N/A' }))
            );
        }
    }

    // 4. Filter results by similarity score
    const filteredMatches = queryResponse.matches?.filter(match => match.score >= minScore) || [];
    const filteredMatchCount = filteredMatches.length;
    console.log(`${logPrefix} Filtered matches by minScore >= ${minScore}. Kept ${filteredMatchCount} of ${rawMatchCount}.`);
     if (process.env.DEBUG === 'true' && filteredMatchCount > 0) {
        console.debug(`${logPrefix} Filtered matches kept (IDs and Scores):`, filteredMatches.map(m => ({ id: m.id, score: m.score, title: m.metadata?.title || 'N/A' })));
    }

    if (filteredMatchCount === 0) {
        console.log(`${logPrefix} No matches found above similarity threshold.`);
        return { contextString: "", fullMatches: [] };
    }

    // 5. Format the results into a string block for the LLM prompt (IDs only)
    //    and return full matches for API response augmentation.
    const idOnlyContext = filteredMatches
      .map(match => `Previously seen Decision ID: ${match.id}`)
      .join('. ');
    const contextString = idOnlyContext ? `${idOnlyContext}.` : "";

    console.log(`${logPrefix} Successfully formatted ID-only context string from ${filteredMatchCount} matches.`);
    if (process.env.DEBUG === 'true') {
        const contextSnippet = contextString.length > 500 ? contextString.substring(0, 500) + '...' : contextString;
        console.debug(`${logPrefix} Formatted ID-only RAG Context (snippet):\n${contextSnippet}`);
    }
    return { contextString: contextString, fullMatches: filteredMatches };

  } catch (error) {
      // Ensure the prefix is included in error logs
      console.error(`${logPrefix} Error during RAG query:`, error);
      return { contextString: "", fullMatches: [] };
  }
}

/**
 * Stores a single decision record in the Pinecone index, generating a unique ID first.
 * @param {object} decisionRecord - The decision object extracted by the LLM, augmented with PR context.
 * @param {string} namespace - The Pinecone namespace (repository slug) to store the record in.
 * @returns {Promise<string|null>} - The generated Pinecone ID if successful, otherwise null.
 */
async function storeDecisionRecord(decisionRecord, namespace) {
  const logPrefix = `[Pinecone Store - Namespace: ${namespace}]`;

  if (!namespace) {
      console.error(`${logPrefix} Critical Error: Namespace not provided.`);
      return null;
  }
   // Check namespace validity if needed (optional)
   /*
   try {
       const stats = await pineconeIndex.describeIndexStats();
       if (!stats.namespaces || !stats.namespaces[namespace]) {
           console.warn(`${logPrefix} Namespace '${namespace}' does not exist in Pinecone index. Creating or proceeding cautiously.`);
           // Depending on policy, you might want to ensure the namespace exists or handle this case
       }
   } catch (statsError) {
       console.error(`${logPrefix} Error checking Pinecone namespace stats:`, statsError);
       return null; // Fail if stats check fails
   }
   */

  // --- Generate Unique Pinecone ID ---
  const prNumber = decisionRecord.pr_number || 'unknownPR';
  const timestamp = Date.now();
  // Create a hash based on title/description for uniqueness within the PR/timestamp
  const contentToHash = `${decisionRecord.title || ''}-${decisionRecord.description || ''}`;
  const shortHash = crypto.createHash('sha256').update(contentToHash).digest('hex').substring(0, 8);
  const pineconeId = `decision_${prNumber}_${timestamp}_${shortHash}`;
  console.log(`${logPrefix} Generated Pinecone ID: ${pineconeId}`);

  // --- Prepare Metadata ---
  const metadata = {
      // Include all fields from the decisionRecord EXCEPT the embedding values
      // Ensure nested objects like 'risks' are correctly handled (Pinecone supports nested metadata)
      ...decisionRecord,
      risks_extracted: decisionRecord.risks, // Rename for Pinecone/UI alignment
      pinecone_id: pineconeId, // Store the ID in metadata as well for filtering
      repository_slug: namespace, // Store namespace/repo slug
      is_superseded: false // Default new decisions to not superseded
  };
  delete metadata.risks; // Remove the original risks field after renaming
  delete metadata.alignment_analysis; // Remove alignment_analysis
  delete metadata.relationships_analysis; // ---> ADDED: Remove relationship_analysis
  delete metadata.pinecone_id; // Remove redundant pinecone_id from metadata

  // ---> ADDED: Normalize Domain Concepts before further processing
  if (metadata.domain_concepts && Array.isArray(metadata.domain_concepts) && metadata.domain_concepts.length > 0) {
      console.log(`${logPrefix} Normalizing ${metadata.domain_concepts.length} domain concepts...`);
      try {
          // The 'namespace' for pinecone is the 'repository_slug' for concepts
          const normalizedConcepts = await normalizeAndStoreConcepts(metadata.domain_concepts, namespace);
          metadata.domain_concepts = normalizedConcepts;
          console.log(`${logPrefix} Concepts successfully normalized to: [${normalizedConcepts.join(', ')}]`);
      } catch (normError) {
          console.error(`${logPrefix} Critical error during concept normalization. Using original concepts as fallback.`, normError);
          // The original concepts are already in `metadata.domain_concepts`, so we can just log and continue.
      }
  }
  // <--- END ADDED

  // ---> ADDED: Convert pr_merged_at to numerical timestamp
  if (metadata.pr_merged_at) {
    try {
      const date = new Date(metadata.pr_merged_at);
      // Check if the date is valid before converting
      if (!isNaN(date.getTime())) {
          metadata.pr_merged_at = date.getTime(); // Store as Unix timestamp (ms)
          console.debug(`${logPrefix} Converted pr_merged_at to numerical timestamp: ${metadata.pr_merged_at}`);
      } else {
          console.warn(`${logPrefix} Invalid date format for pr_merged_at: ${metadata.pr_merged_at}. Storing as 0.`);
          metadata.pr_merged_at = 0; // Use 0 instead of null
      }
    } catch (dateError) {
        console.error(`${logPrefix} Error converting pr_merged_at to Date object: ${metadata.pr_merged_at}`, dateError);
        metadata.pr_merged_at = 0; // Use 0 instead of null
    }
  } else {
      // Use 0 if not provided or already null
      console.warn(`${logPrefix} pr_merged_at not provided or null. Storing as 0.`);
      metadata.pr_merged_at = 0; 
  }
  // <--- END ADDED

  // --- Sanitize Metadata for Pinecone --- 
  // Pinecone metadata values must be string, number, boolean, or list of strings.
  // Stringify complex objects/arrays.

  const fieldsToStringify = ['risks_extracted', 'alternatives_analysis']; // Update to use the new name
  fieldsToStringify.forEach(field => {
      if (metadata[field] !== null && typeof metadata[field] === 'object') {
          try {
              metadata[field] = JSON.stringify(metadata[field]);
              console.debug(`${logPrefix} Stringified metadata field: ${field}`);
          } catch (stringifyError) {
              console.error(`${logPrefix} Failed to stringify metadata field ${field}:`, stringifyError);
              // Decide how to handle: store an error string, or remove the field?
              metadata[field] = '[Error stringifying data]'; 
          }
      }
  });

  // Ensure related_files is an array of strings (it should be, but double-check)
  if (metadata.related_files && !Array.isArray(metadata.related_files)) {
      console.warn(`${logPrefix} Metadata field 'related_files' was not an array. Attempting to stringify.`);
       try {
           // If it wasn't an array but some other object/primitive, stringify it
           metadata.related_files = JSON.stringify(metadata.related_files);
       } catch (stringifyError) {
           console.error(`${logPrefix} Failed to stringify non-array metadata field related_files:`, stringifyError);
           metadata.related_files = '[Error stringifying data]';
       }
  } else if (metadata.related_files && metadata.related_files.some(item => typeof item !== 'string')) {
       console.warn(`${logPrefix} Metadata field 'related_files' contained non-string elements. Stringifying entire array.`);
       try {
           metadata.related_files = JSON.stringify(metadata.related_files);
       } catch (stringifyError) {
           console.error(`${logPrefix} Failed to stringify related_files array with non-string elements:`, stringifyError);
           metadata.related_files = '[Error stringifying data]';
       }
  }

  // ---> ADDED: Ensure domain_concepts is an array of strings
  if (metadata.domain_concepts && !Array.isArray(metadata.domain_concepts)) {
      console.warn(`${logPrefix} Metadata field 'domain_concepts' was not an array. Setting to empty array.`);
      metadata.domain_concepts = []; // Default to empty array if not an array
  } else if (metadata.domain_concepts && metadata.domain_concepts.some(item => typeof item !== 'string')) {
       console.warn(`${logPrefix} Metadata field 'domain_concepts' contained non-string elements. Filtering out non-strings.`);
       // Filter the array to keep only string elements, preserving filterability
       metadata.domain_concepts = metadata.domain_concepts.filter(item => typeof item === 'string');
  }
  // <--- END ADDED

  // Ensure 'risks' and 'related_files' are stored correctly (should be fine if they are arrays/objects)


  // --- Generate Embedding ---
  // Original: const textToEmbed = `${decisionRecord.title}\n${decisionRecord.description || ''}\n${decisionRecord.rationale || ''}`;
  
  // Updated text for embedding generation:
  const domainConceptsString = (Array.isArray(metadata.domain_concepts) && metadata.domain_concepts.length > 0) ? metadata.domain_concepts.join(' ') : '';
  const textToEmbed = [
    decisionRecord.title || '',
    domainConceptsString,
    decisionRecord.dev_prompt || '',
    decisionRecord.follows_best_practices_reason || '',
    decisionRecord.implications || ''
  ].filter(Boolean).join('\n'); // Filter out empty strings and join with newline

  console.log(`${logPrefix} Generating embedding for decision ID: ${pineconeId}...`);
  console.debug(`${logPrefix} Text for embedding (ID: ${pineconeId}):\n---\n${textToEmbed}\n---`); // Log the text being embedded

  let embedding;
  try {
    embedding = await generateEmbedding(textToEmbed);
    if (!embedding) {
      throw new Error('Embedding generation returned undefined.');
    }
  } catch (embedError) {
    console.error(`${logPrefix} Failed to generate embedding for ID ${pineconeId}:`, embedError);
    return null; // Cannot store without embedding
  }
  console.log(`${logPrefix} Embedding generated for ID ${pineconeId}.`);


  // --- Upsert to Pinecone ---
  const vectorToUpsert = {
      id: pineconeId,
      values: embedding,
      metadata: metadata
  };

  try {
      console.log(`${logPrefix} Upserting vector ID: ${pineconeId} into namespace: ${namespace}...`);
      // ---> ADDED: Debug log for metadata just before upsert
      console.debug(`${logPrefix} Metadata for ID ${pineconeId} before upsert:`, JSON.stringify(metadata));
      if (typeof metadata.pr_number !== 'number') {
          console.warn(`${logPrefix} WARNING: pr_number is missing or not a number in metadata just before upsert for ID ${pineconeId}! Value:`, metadata.pr_number);
      }
      // <--- END ADDED
      await pineconeIndex.namespace(namespace).upsert([vectorToUpsert]);
      console.log(`${logPrefix} Successfully upserted vector ID: ${pineconeId}.`);
      return pineconeId; // Return the generated ID on success
  } catch (error) {
      console.error(`${logPrefix} Error upserting vector ID ${pineconeId} to Pinecone:`, error);
      // Log the vector details that failed (excluding potentially large embedding values)
      console.error(`${logPrefix} Failed vector data (metadata):`, JSON.stringify(metadata));
      return null; // Return null on failure
  }
}

// --- Orchestration Logic ---

// Helper function to decide if alternatives analysis is warranted
// Takes the decision object and the full list of code changes for the PR
async function shouldAnalyzeAlternatives(decision, prCodeChanges) {
    // --- Calculate complexity/size for THIS decision's related files first ---
    let totalLinesChanged = 0;
    const relatedFiles = decision.related_files || [];
    if (relatedFiles.length > 0 && prCodeChanges) {
        for (const change of prCodeChanges) {
            // Basic path normalization/check (might need refinement based on OS/path formats)
            if (relatedFiles.some(relatedFile => change.filename.endsWith(relatedFile))) {
                // Prioritize additions/deletions count if available
                if (typeof change.additions === 'number' && typeof change.deletions === 'number') {
                    totalLinesChanged += change.additions + change.deletions;
                } else if (change.patch) {
                    // Basic estimate from patch lines (crude but better than nothing)
                    const patchLines = change.patch.split('\n');
                    totalLinesChanged += patchLines.filter(line => (line.startsWith('+') && !line.startsWith('+++')) || (line.startsWith('-') && !line.startsWith('---'))).length;
                }
            }
        }
    }
    console.log(`[Orchestrator] Decision \"${decision.title}\" related file changes: ~${totalLinesChanged} lines.`);

    // Now make decisions with the line count information available
    // Check if decision follows standard practices that don't need alternatives analysis
    if (decision.follows_standard_practice === true) {
        console.log(`[Orchestrator] Skipping alternatives for \"${decision.title}\": Decision follows well-established standard practices.`);
        return { shouldAnalyze: false, linesChanged: totalLinesChanged, reason: 'standard_practice' };
    }

    if (decision.is_extension === false) {
        // It's considered a new pattern/approach, definitely analyze.
        console.log(`[Orchestrator] Analyzing alternatives for \"${decision.title}\": Decision is marked as introducing a new pattern/approach.`);
        return { shouldAnalyze: true, linesChanged: totalLinesChanged, reason: 'new_pattern' }; 
    }

    // --- Significance Checks (incorporating size) ---

    // Define thresholds (these need tuning based on your project/context)
    const HIGH_IMPACT_LINE_THRESHOLD = 75; // e.g., Extension affecting >75 lines might be significant
    const TRIVIAL_LINE_THRESHOLD = 10;     // e.g., Extension affecting <=10 lines likely isn't worth deep analysis

    // Priority 1: If change is trivial, likely skip regardless of keywords
    if (totalLinesChanged <= TRIVIAL_LINE_THRESHOLD) {
         console.log(`[Orchestrator] Skipping alternatives for \"${decision.title}\": Extension implemented with minimal code change (${totalLinesChanged} lines).`);
         return { shouldAnalyze: false, linesChanged: totalLinesChanged, reason: 'trivial_change' };
    }

    // Priority 2: Check for explicit high-impact signals from LLM
    const highImpactKeywords = ['scalability', 'security critical', 'performance bottleneck', 'major refactor', 'core component', 'significant maintainability', 'violates', 'breaks compatibility'];
    const implicationsText = (decision.implications || '').toLowerCase();
    if (highImpactKeywords.some(keyword => implicationsText.includes(keyword))) {
         console.log(`[Orchestrator] Analyzing alternatives for \"${decision.title}\": High-impact keywords found in implications.`);
         return { shouldAnalyze: true, linesChanged: totalLinesChanged, reason: 'high_impact_implications' };
    }
    const adaptationKeywords = ['adapt', 'modify', 'fundamentally change', 'rework', 'extend beyond original scope', 'new context requires changes'];
    const rationaleText = (decision.rationale || '').toLowerCase();
     if (adaptationKeywords.some(keyword => rationaleText.includes(keyword))) {
         console.log(`[Orchestrator] Analyzing alternatives for \"${decision.title}\": Rationale suggests significant adaptation.`);
         return { shouldAnalyze: true, linesChanged: totalLinesChanged, reason: 'significant_adaptation' };
     }

    // Priority 3: Check for standard practice keywords that might indicate this follows best practices
    const standardPracticeKeywords = ['standard practice', 'best practice', 'recommended pattern', 'common pattern', 'industry standard', 'conventional approach'];
    if (standardPracticeKeywords.some(keyword => 
        (decision.rationale || '').toLowerCase().includes(keyword) || 
        (decision.description || '').toLowerCase().includes(keyword))) {
        console.log(`[Orchestrator] Skipping alternatives for \"${decision.title}\": Decision appears to follow standard practices based on rationale/description.`);
        return { shouldAnalyze: false, linesChanged: totalLinesChanged, reason: 'implied_standard_practice' };
    }

    // Priority 4: If change size is large, analyze even without specific keywords
    if (totalLinesChanged > HIGH_IMPACT_LINE_THRESHOLD) {
        console.log(`[Orchestrator] Analyzing alternatives for \"${decision.title}\": Extension involves significant code changes (${totalLinesChanged} lines), warrants review despite lack of keywords.`);
        return { shouldAnalyze: true, linesChanged: totalLinesChanged, reason: 'large_changes' };
    }

    // Default: Extension with moderate changes and no strong keywords -> Skip
    console.log(`[Orchestrator] Skipping alternatives for \"${decision.title}\": Appears to be a routine extension with moderate changes (${totalLinesChanged} lines) and no high-impact indicators.`);
    return { shouldAnalyze: false, linesChanged: totalLinesChanged, reason: 'moderate_extension' };
}

/**
 * Performs automated triage based on alternatives analysis.
 * @param {object} alternativesAnalysis - The analysis object from the LLM.
 * @param {object} decision - The decision object from the extraction.
 * @returns {Array<string>} - A list of review flags.
 */
function performAutomatedTriage(alternativesAnalysis, decision) {
    console.log("[Triage] Performing automated triage...");
    const flags = [];
    
    // Process alternatives analysis
    if (alternativesAnalysis && alternativesAnalysis.alternatives) {
        alternativesAnalysis.alternatives.forEach(alt => {
            // Flag if an alternative has significantly more pros or fewer cons (example threshold)
            if (alt.pros_compared_to_chosen?.length > (alt.cons_compared_to_chosen?.length || 0) + 1) {
                flags.push('potential_strong_alternative');
            }
            // Flag if context alignment suggests conflict
            if (alt.contextual_alignment?.toLowerCase().includes('conflict')) {
                flags.push('potential_conflict_with_past');
            }
        });

        // Check for supersedence based on RAG context
        if (JSON.stringify(alternativesAnalysis).toLowerCase().includes('supersede')) {
            flags.push('potential_supersedence');
        }
    }
    
    // Process structured risks from decision
    if (decision.risks && Array.isArray(decision.risks)) {
        // Add flags based on risk categories and severity
        decision.risks.forEach(risk => {
            if (risk.category && risk.severity) {
                flags.push(`risk_${risk.category}_${risk.severity}`);
            }
        });
        
        // Add specific flag for high severity risks
        const highSeverityRisks = decision.risks.filter(risk => risk.severity === 'high');
        if (highSeverityRisks.length > 0) {
            flags.push('contains_high_severity_risks');
        }
    }

    const uniqueFlags = [...new Set(flags)];
    console.log(`[Triage] Generated flags: ${uniqueFlags.length > 0 ? uniqueFlags.join(', ') : 'None'}`);
    return uniqueFlags;
}

async function analyzeAlternatives(decision, namespace) {
  const logPrefix = `[Alternatives Analysis - Dec: "${decision.title}" Ns: ${namespace}]`;

  if (!namespace) {
      console.error(`${logPrefix} CRITICAL: Namespace not provided to analyzeAlternatives. Cannot proceed.`);
      return { status: "error_missing_namespace", alternatives: [], refined_tradeoff_summary: "Error: Namespace was not provided for RAG query."};
  }

  try {
      console.log(`${logPrefix} Querying RAG for historical context...`);
      // Ensure `namespace` is passed as `targetNamespace` to queryRAG
      const { contextString: ragContext } = await queryRAG(decision, 3, { minScore: 0.7 }, namespace); // Use the contextString part
      console.log(`${logPrefix} RAG context length: ${ragContext.length}`);

      const prompt = generateAlternativesPrompt(decision, ragContext);
      console.log(`${logPrefix} Generated alternatives analysis prompt.`);

      const analysisResult = await callLLM(prompt);
      console.log(`${logPrefix} Received alternatives analysis response.`);

      // --- CORRECTED CHECK --- 
      // Check for the nested structure returned by the LLM
      if (analysisResult && analysisResult.alternatives_analysis && analysisResult.alternatives_analysis.alternatives) {
          return {
              status: "success", 
              alternatives: analysisResult.alternatives_analysis.alternatives, 
              refined_tradeoff_summary: analysisResult.alternatives_analysis.refined_tradeoff_summary 
          };
      } else {
          console.error(`${logPrefix} Error: Invalid alternatives analysis response format. Expected structure {"alternatives_analysis": {"alternatives": [...]}}. Received:`, JSON.stringify(analysisResult));
          return { status: "error_invalid_response", alternatives: [], refined_tradeoff_summary: "Error: Invalid alternatives analysis response format." };
      }
      // --- END CORRECTION ---
  } catch (error) {
      console.error(`${logPrefix} Error:`, error);
      return { status: "error", alternatives: [], refined_tradeoff_summary: "Error: Failed to analyze alternatives." };
  }
}

/**
 * Logs the outcome of processing a PR (simplified to console logging).
 * @param {object} prContext - Context of the PR.
 * @param {string} status - 'success' or 'failure'.
 * @param {number} decisionsCount - Number of decisions extracted.
 * @param {string} [errorMessage=''] - Error message if status is 'failure'.
 * @param {Array} [decisions=[]] - Array of extracted decisions (optional, for detailed logging).
 */
async function logProcessedPR(prContext, status, decisionsCount, errorMessage = '', decisions = []) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    pr_url: prContext.html_url,
    pr_title: prContext.title,
    status: status,
    decisions_extracted: decisionsCount,
    error_message: errorMessage,
    // Optionally log decision titles for quick reference
    decision_titles: decisions.map(d => d.title), 
  };

  console.log(`[PR Processing Log] ${status.toUpperCase()}: PR ${prContext.html_url}`, logEntry);
}

/**
 * Processes a merged Pull Request to extract and analyze architectural decisions.
 * @param {object} prContext - Context of the PR (title, body, url, files involved).
 * @param {Array} codeChanges - Detailed code changes (diff patches).
 * @param {Array} comments - Comments from the PR.
 * @param {string} namespace - The Pinecone namespace for this installation/repo.
 * @param {string} installationRepositorySlug - The definitive repository slug (owner/repo) associated with the GitHub App installation.
 * @returns {Promise<object>} - Analysis results summary.
 */
export async function processMergedPR(prContext, codeChanges, comments, namespace, installationRepositorySlug, dryRun = false, designDocContent, skipRelationshipAnalysis = false) {
  initializeClients();
  const startTime = Date.now();
  const logPrefix = `[ProcessMergedPR - ${prContext?.number || 'unknown'} - ${installationRepositorySlug}]`;

  console.log(`[Orchestrator] Starting analysis for PR #${prContext.number} in ${installationRepositorySlug} (isPseudo: ${prContext.is_pseudo_pr}).`);
  console.log(`${logPrefix} Starting deep analysis for ${prContext.title}`);
  console.log(`${logPrefix} Repository: ${installationRepositorySlug}, Namespace: ${namespace}`);
  console.log(`${logPrefix} Code changes: ${codeChanges.length} files, Comments: ${comments.length}`);
  console.log(`${logPrefix} DryRun: ${dryRun}, SkipRelationshipAnalysis: ${skipRelationshipAnalysis}`);
  console.log(`${logPrefix} NOTE: This item has already passed heuristic and lightweight LLM filters`);

  // --- ADDED: Fetch Deployment Constitution ---
  let deploymentConstitution = null;
  try {
    if (supabase) {
      const { data: constitutionData, error: constitutionError } = await supabase
        .from('deployment_constitutions')
        .select('constitution_data')
        .eq('repository_slug', installationRepositorySlug)
        .single();
      
      if (constitutionError) {
        // It's okay if it's not found, but log other errors.
        if (constitutionError.code !== 'PGRST116') { // PGRST116 = "No rows found"
          console.warn(`${logPrefix} Error fetching deployment constitution:`, constitutionError.message);
        }
      } else if (constitutionData) {
        deploymentConstitution = constitutionData.constitution_data;
        console.log(`${logPrefix} Successfully loaded Deployment Constitution.`);
      }
    }
  } catch (err) {
    console.error(`${logPrefix} Unexpected error fetching deployment constitution:`, err);
  }
  // --- END ADDED ---

  // **DEEP ANALYSIS** - Items reaching this function have already been filtered
  console.log(`${logPrefix} ==================== DEEP ANALYSIS ====================`);
  console.log(`${logPrefix} Beginning comprehensive architectural decision analysis...`);
  
  let decisions = [];
  let decisionsCount = 0;
  let status = 'processing'; // Default status
  let errorMessage = '';

  // Log all incoming parameters, especially dryRun and designDocContent
  console.log(`[Orchestrator processMergedPR Entry] Parameters received:`, {
    prContextUrl: prContext?.html_url,
    codeChangesCount: codeChanges?.length,
    commentsCount: comments?.length,
    namespace,
    installationRepositorySlug,
    dryRun,
    designDocContentLength: designDocContent?.length, // Log length to check if content is present
    isDesignDocContentTruthy: !!designDocContent // Explicitly check if it's truthy
  });

  console.log(`[Orchestrator] Starting processing for PR: ${prContext.html_url}, RepoSlug: ${installationRepositorySlug}, DryRun: ${dryRun}${designDocContent ? ', DesignDocMode: true' : ''}`);

  try {
    if (dryRun) {
      if (designDocContent) {
        // --- Dry Run for Design Document ---
        console.log(`[Orchestrator] Dry Run for Design Document.`);
        const pseudoDecisionId = `designDoc-${prContext.number || 'standalone'}-${Date.now()}`;
        
        let extractedTitle = "Untitled Design Document";
        const titleMatch = designDocContent.match(/^#\\s*(.*)/m);
        if (titleMatch && titleMatch[1] && titleMatch[1].trim() !== "") {
            extractedTitle = titleMatch[1].trim();
        }
        
        const extractedDescription = designDocContent.length > 1000 ? designDocContent.substring(0, 1000) + "..." : designDocContent;
        
        // --- LLM-based Concept Extraction ---
        let llmExtractedConcepts = [];
        try {
            console.log(`[Orchestrator DesignDoc] Generating concept extraction prompt for design doc title: "${extractedTitle}"`);
            const conceptPrompt = generateDesignDocConceptExtractionPrompt(designDocContent); 

            if (conceptPrompt) { 
                console.log(`[Orchestrator DesignDoc] Calling LLM for concept extraction.`);
                const conceptResult = await callLLM(conceptPrompt); 
                
                if (conceptResult && conceptResult.domain_concepts && Array.isArray(conceptResult.domain_concepts)) {
                    llmExtractedConcepts = conceptResult.domain_concepts
                        .filter(c => typeof c === 'string' && c.trim() !== '')
                        .map(c => c.trim());
                    console.log(`[Orchestrator DesignDoc] LLM extracted concepts: ${llmExtractedConcepts.join(', ')}`);
                } else {
                    console.warn(`[Orchestrator DesignDoc] LLM concept extraction did not return valid 'domain_concepts' array. Response:`, conceptResult);
                }
            } else {
                console.log(`[Orchestrator DesignDoc] Concept extraction prompt was not generated (e.g., empty design doc).`);
            }
        } catch (conceptError) {
            console.error(`[Orchestrator DesignDoc] Error during LLM concept extraction:`, conceptError);
        }

        // Fallback if LLM extraction yields no concepts
        if (llmExtractedConcepts.length === 0) {
            console.log(`[Orchestrator DesignDoc] No concepts from LLM extraction, falling back to title-based concepts for pseudo-decision.`);
            const conceptsFromTitle = extractedTitle.toLowerCase().split(/\\s+/).filter(w => w.length > 3 && w.length < 20 && !/\\d/.test(w)).slice(0,5);
            llmExtractedConcepts = conceptsFromTitle.length > 0 ? conceptsFromTitle : ["design review"]; // Assign to llmExtractedConcepts for consistency
        }
        // --- End LLM-based Concept Extraction ---

        const pseudoDecision = {
            id: pseudoDecisionId,
            title: (extractedTitle === "Untitled Design Document" || extractedTitle === "") ? `Design Document Review (${new Date().toISOString().split('T')[0]})` : `Design Doc: ${extractedTitle}`,
            description: extractedDescription, // The main content of the design doc (truncated)
            rationale: "N/A for design doc preliminary analysis.",
            implications: "N/A for design doc preliminary analysis.",
            related_files: [], 
            domain_concepts: llmExtractedConcepts, // Use LLM extracted concepts here
            pr_number: prContext.number 
        };
        console.log(`[Orchestrator Dry Run] Created pseudo-decision from design doc: "${pseudoDecision.title}"`, { concepts: pseudoDecision.domain_concepts });

        let existingContextString = '';
        let referencedDecisionData = []; // To store full data of referenced decisions

        if (llmExtractedConcepts.length > 0) {
            console.log(`[Orchestrator Dry Run DesignDoc] Performing semantic search for context using ${llmExtractedConcepts.length} extracted concepts in namespace ${namespace}.`);
            const conceptQueryText = llmExtractedConcepts.join(', '); 
            const ragQueryObject = {
                title: `Context search for Design Doc: ${extractedTitle}`,
                description: `Relevant concepts: ${conceptQueryText}`
            };
            try {
                const ragResult = await queryRAG(ragQueryObject, 7, { minScore: 0.4 }, namespace); 
                existingContextString = ragResult.contextString;
                referencedDecisionData = ragResult.fullMatches.map(match => ({
                    id: match.id,
                    title: match.metadata?.title || 'N/A',
                    description: match.metadata?.description || 'N/A',
                    implications: match.metadata?.implications || 'N/A',
                    rationale: match.metadata?.rationale || 'N/A',
                    dev_prompt: match.metadata?.dev_prompt || 'N/A',
                    related_files: match.metadata?.related_files || [],
                    // Include formatted metadata object so all fields are available
                    metadata: {
                        title: match.metadata?.title || 'N/A',
                        description: match.metadata?.description || 'N/A',
                        implications: match.metadata?.implications || 'N/A',
                        rationale: match.metadata?.rationale || 'N/A',
                        dev_prompt: match.metadata?.dev_prompt || 'N/A',
                        related_files: match.metadata?.related_files || [],
                        domain_concepts: match.metadata?.domain_concepts || []
                    }
                }));

                if (existingContextString && existingContextString.trim() !== '') {
                    console.log(`[Orchestrator Dry Run DesignDoc] Successfully retrieved semantic search context (length: ${existingContextString.length}) with ${referencedDecisionData.length} referenced decisions.`);
                } else {
                    console.log(`[Orchestrator Dry Run DesignDoc] No relevant context found via semantic search with extracted concepts.`);
                    existingContextString = "No specific existing context was automatically retrieved based on the document's key concepts.";
                }
            } catch (ragError) {
                console.error(`[Orchestrator Dry Run DesignDoc] Error during semantic search for context:`, ragError);
                existingContextString = "Note: Automated analysis of related decisions encountered an error during semantic search.";
            }
        } else {
            console.log(`[Orchestrator Dry Run DesignDoc] No concepts extracted from design document. Skipping semantic search for context.`);
            existingContextString = "No key concepts were extracted from the design document to perform a contextual search.";
        }

        const designDocPromptContext = {
            ...prContext, 
            title: extractedTitle, 
            body: designDocContent.substring(0, 200) 
        };

        // Instead of passing just the IDs, pass the full referenced decision data
        const feedbackPrompt = generateDryRunFeedbackPrompt(
            designDocContent, 
            'design_doc', 
            designDocPromptContext, 
            JSON.stringify(referencedDecisionData)
        );

        if (!feedbackPrompt) {
            throw new Error("Failed to generate dry run feedback prompt for design document.");
        }
        
        console.log(`[Orchestrator DesignDoc] Calling LLM for dry run feedback.`);
        console.log(`[Orchestrator DesignDoc] Dry Run Prompt:\n---\n${feedbackPrompt}\n---`);

        const feedbackResponse = await anthropic.messages.create({
            model: getEnvVar('ANTHROPIC_MODEL', 'claude-sonnet-4-20250514'),
            max_tokens: 4096,
            temperature: 0.1,
            messages: [{ role: "user", content: feedbackPrompt }],
        });
        const feedbackText = feedbackResponse.content[0]?.type === 'text' ? feedbackResponse.content[0].text : 'Error: Could not generate feedback text.';
        console.log(`[Orchestrator DesignDoc] Dry Run Response:\n---\n${feedbackText}\n---`);
        console.log(`[Orchestrator DesignDoc] Dry Run feedback generated for Design Document.`);
        return {
            status: 'feedback_generated',
            feedback: feedbackText,
            referenced_decisions: referencedDecisionData, // Add referenced decision data to the response
            duration: Date.now() - startTime
        };

      } else {
        // --- Dry Run: Generate Feedback for PR Code Changes---
        console.log(`[Orchestrator] Dry Run enabled for PR. Generating feedback prompt.`);
        
        // Check if this is milestone-focused feedback by looking for milestone context in designDocContent
        const isMilestoneFeedback = designDocContent && designDocContent.includes('MILESTONE-FOCUSED FEEDBACK REQUEST');
        
        if (isMilestoneFeedback) {
          console.log(`[Orchestrator] Detected milestone-focused feedback request.`);
          
          // Extract milestone context from the enhanced design doc content
          let milestoneContext = null;
          try {
            const milestoneMatch = designDocContent.match(/- Milestone: (M\d+\.\d+) - (.+)/);
            const descriptionMatch = designDocContent.match(/- Description: (.+)/);
            const deliverablesMatch = designDocContent.match(/- Planned Deliverables: (\[.+?\])/s);
            const verificationMatch = designDocContent.match(/- Verification Criteria: (\[.+?\])/s);
            
            if (milestoneMatch && descriptionMatch) {
              milestoneContext = {
                milestone_id: milestoneMatch[1],
                title: milestoneMatch[2],
                description: descriptionMatch[1],
                planned_deliverables: deliverablesMatch ? JSON.parse(deliverablesMatch[1]) : [],
                verification_criteria: verificationMatch ? JSON.parse(verificationMatch[1]) : [],
                priority: 'High' // Default priority
              };
              console.log(`[Orchestrator] Extracted milestone context: ${milestoneContext.milestone_id} - ${milestoneContext.title}`);
            }
          } catch (extractError) {
            console.warn(`[Orchestrator] Error extracting milestone context from design doc content:`, extractError);
          }
          
          if (milestoneContext) {
            // Use enhanced milestone-focused feedback prompt
            const totalLinesChanged = codeChanges.reduce((sum, change) => 
              sum + (change.additions || 0) + (change.deletions || 0), 0
            );

            const formattedChanges = codeChanges.map(change => `
**File: ${change.filename}**
${change.patch}
            `).join('\n');

            const milestoneFeedbackPrompt = `You are an expert software architect providing milestone-focused feedback on staged code changes. Your goal is to validate milestone scope compliance and identify new risks introduced.

**MILESTONE CONTEXT:**
- Milestone ID: ${milestoneContext.milestone_id}
- Title: ${milestoneContext.title}
- Description: ${milestoneContext.description}
- Priority: ${milestoneContext.priority}
- Branch: ${prContext?.ref || prContext?.branch || 'N/A'}

**PLANNED MILESTONE DELIVERABLES:**
${milestoneContext.planned_deliverables.map((deliverable, i) => `${i + 1}. ${deliverable}`).join('\n')}

**VERIFICATION CRITERIA:**
${milestoneContext.verification_criteria.map((criteria, i) => `${i + 1}. ${criteria}`).join('\n')}

**STAGED CODE CHANGES (${totalLinesChanged} lines changed):**
${formattedChanges}

**EXISTING ARCHITECTURAL CONTEXT:**
${designDocContent.split('Current Branch:')[0].replace('MILESTONE-FOCUSED FEEDBACK REQUEST', '').trim() || 'No specific existing context found.'}

**FEEDBACK REQUIREMENTS:**

## 1. MILESTONE SCOPE VALIDATION
Analyze the staged changes against the planned milestone deliverables:
- **Scope Compliance**: Rate as COMPLETE | PARTIAL | EXCEEDED | DEVIATED
- **Coverage Analysis**: Which planned deliverables are addressed by these changes?
- **Scope Deviations**: List any unplanned features or missing planned features
- **Justification Assessment**: If deviated, assess if changes are justified for milestone goals

## 2. NEW RISK ASSESSMENT (CRITICAL)
Systematically identify NEW risks introduced by these specific changes:

**Security Risks (NEW):**
- Authentication/authorization vulnerabilities
- Data exposure or privacy concerns  
- Input validation gaps
- Injection attack vectors
- Session management issues

**Performance Risks (NEW):**
- Memory leaks or resource exhaustion
- Inefficient database queries or API calls
- Blocking operations or deadlocks
- Scalability bottlenecks
- Cache misuse or invalidation issues

**UX/Accessibility Risks (NEW):**
- Broken user workflows or confusing interfaces
- Missing loading states or error handling
- Accessibility violations (WCAG)
- Responsive design issues
- Data loss scenarios

**Integration Risks (NEW):**
- Breaking API changes or contract violations
- Dependency conflicts or version issues
- Migration problems or data integrity issues
- Backward compatibility breaks

**Milestone-Specific Risks (NEW):**
- Dependencies on future milestones
- Integration sequencing problems
- Feature flag implementation issues
- Independent deployment concerns

## 3. INTEGRATION READINESS ASSESSMENT
- **Independent Integration**: Can this milestone be safely integrated without future milestones?
- **Rollback Safety**: Are changes easily reversible if issues arise?
- **Feature Flag Compliance**: Are incomplete features properly gated?
- **Testing Coverage**: Are changes adequately testable in isolation?

## 4. ACTIONABLE RECOMMENDATIONS
Provide specific, immediate actions:
- Required changes before integration
- Risk mitigation strategies
- Additional testing recommendations
- Documentation or monitoring needs

**OUTPUT FORMAT:**
Provide a structured analysis covering all four sections above. Be specific and actionable. Focus on what's NEW or CHANGED, not general advice. Highlight critical issues that would block milestone integration.`;

            console.log(`[Orchestrator] Using milestone-focused feedback prompt.`);
            console.log(`[Orchestrator] Milestone Feedback Prompt:\n---\n${milestoneFeedbackPrompt}\n---`);

            const feedbackResponse = await anthropic.messages.create({
              model: getEnvVar('ANTHROPIC_MODEL', 'claude-sonnet-4-20250514'),
              max_tokens: 4096,
              temperature: 0.1,
              messages: [{ role: "user", content: milestoneFeedbackPrompt }],
            });

            const feedbackText = feedbackResponse.content[0]?.type === 'text' ? feedbackResponse.content[0].text : 'Error: Could not generate milestone feedback text.';
            console.log(`[Orchestrator] Milestone Feedback Response:\n---\n${feedbackText}\n---`);
            console.log(`[Orchestrator] Milestone-focused feedback generated.`);

            return {
              status: 'feedback_generated',
              feedback: feedbackText,
              milestone_context: milestoneContext,
              duration: Date.now() - startTime
            };
          }
        }
        
      const pseudoDecisionId = `dryRun-${prContext.number}-${Date.now()}`;
      const pseudoDecision = {
        id: pseudoDecisionId,
        title: prContext.title || "Work in Progress",
        description: prContext.body || "No PR description provided.",
        rationale: "N/A for dry run preliminary analysis.",
        implications: "N/A for dry run preliminary analysis.",
        related_files: codeChanges.map(c => c.filename),
        // Attempt to extract some basic domain concepts from title/description for better RAG.
        // This is a very simple heuristic. More advanced NLP could be used if needed.
        domain_concepts: (prContext.title + " " + (prContext.body || ""))
            .toLowerCase()
            .match(/\\b(?!is|a|an|the|to|for|in|on|of|and|or|with|by|as|at|from|if|new|update|fix|refactor|feat|chore|docs|test|style|perf|ci|build|revert|merge|pull|request|branch|commit|github|issue|task|user|data|api|service|client|server|config|util|helper|script|component|module|package|library|framework|database|query|event|message|queue|cache|log|error|warning|info|debug|trace|success|failure|test|spec|story|docs|readme|contributing|license|gitignore|dockerfile|docker-compose|package.json|yarn.lock|package-lock.json)\\w{3,}\\b/g) 
            ?.slice(0, 10) || [], // Max 10 simple keywords
        pr_number: prContext.number
      };
      console.log(`[Orchestrator Dry Run] Created pseudo-decision: ${pseudoDecision.title}`, { files: pseudoDecision.related_files, concepts: pseudoDecision.domain_concepts});

      let existingContextString = '';
      try {
        console.log(`[Orchestrator Dry Run] Relationship analysis DISABLED - skipping for pseudo-decision in namespace ${namespace}.`);
        existingContextString = "Note: Automated analysis of related decisions is currently disabled.";
        
        // ---> DISABLED: Relationship analysis is currently disabled due to critical issues
        /*
        console.log(`[Orchestrator Dry Run] Performing relationship analysis for pseudo-decision in namespace ${namespace}.`);
        // Use a simplified version of analyzeRelationships logic or call it directly
        // Note: analyzeRelationships itself calls findDecisionsBy... which use the pseudoDecision's fields.
        const relationshipAnalysis = await analyzeRelationships(pseudoDecision, namespace);

        if (relationshipAnalysis && relationshipAnalysis.relationships && relationshipAnalysis.relationships.length > 0) {
          let contextParts = ["Relevant Existing Architectural Context (from automated analysis):"];
          relationshipAnalysis.relationships.forEach(rel => {
            const existingDecision = relationshipAnalysis.potential_matches_for_analysis?.find(m => m.id === rel.existing_decision_id); // Assumes analyzeRelationships is modified to pass this
            const existingTitle = existingDecision?.metadata?.title || rel.existing_decision_id;
            const existingPrNumber = existingDecision?.metadata?.pr_number;
            const existingDevPrompt = existingDecision?.metadata?.dev_prompt;
            const existingRationale = existingDecision?.metadata?.rationale;
            const existingImplications = existingDecision?.metadata?.implications;
            const existingRisksString = existingDecision?.metadata?.risks_extracted;

            let part = `- Potentially related to: "${existingTitle}" (PR #${existingPrNumber || 'N/A'})`;
            if (rel.relationship_type && rel.relationship_type !== 'independent') {
              part += ` - New change might **${rel.relationship_type.toUpperCase()}** it.`;
            }
            if (existingRationale) {
              part += `\n  - Rationale: "${existingRationale.substring(0, 200)}${existingRationale.length > 200 ? '...' : ''}"`;
            }
            if (existingImplications) {
              part += `\n  - Implications: "${existingImplications.substring(0, 200)}${existingImplications.length > 200 ? '...' : ''}"`;
            }
            if (existingDevPrompt) {
              part += `\n  - Developer Guidance from existing decision: "${existingDevPrompt}"`;
            }

            // Add Risks Summary
            if (existingRisksString && typeof existingRisksString === 'string') {
              try {
                const risks = JSON.parse(existingRisksString);
                if (Array.isArray(risks)) {
                  const highRisks = risks.filter(r => r && r.severity && r.severity.toLowerCase() === 'high');
                  const mediumRisks = risks.filter(r => r && r.severity && r.severity.toLowerCase() === 'medium');
                  let riskSummary = '';
                  if (highRisks.length > 0) {
                    riskSummary += ` High Risks: ${highRisks.map(r => r.description).join(', ')}.`;
                  }
                  if (mediumRisks.length > 0) {
                    riskSummary += ` Medium Risks: ${mediumRisks.map(r => r.description).join(', ')}.`;
                  }
                  if (riskSummary) {
                    part += `\n  - Key Risks: ${riskSummary.substring(0, 250)}${riskSummary.length > 250 ? '...' : ''}`;
                  }
                }
              } catch (e) {
                console.warn(`[Orchestrator Dry Run] Could not parse risks for decision ${existingTitle}:`, e);
              }
            }

            contextParts.push(part);
          });
          existingContextString = contextParts.join('\n');
          console.log(`[Orchestrator Dry Run] Generated relationship context string:\n${existingContextString}`);
        } else {
          console.log(`[Orchestrator Dry Run] No significant relationships found or analysis empty.`);
        }
        */
      } catch (relError) {
        console.error(`[Orchestrator Dry Run] Error during relationship analysis for feedback context:`, relError);
        existingContextString = "Note: Automated analysis of related decisions encountered an error.";
      }

        const feedbackPrompt = generateDryRunFeedbackPrompt(codeChanges, 'code', prContext, existingContextString);

      if (!feedbackPrompt) {
         throw new Error("Failed to generate dry run feedback prompt.");
      }

      console.log(`[Orchestrator] Calling LLM for dry run feedback.`);
        
      console.log(`[Orchestrator] Dry Run Prompt:\n---\n${feedbackPrompt}\n---`);
        

      const feedbackResponse = await anthropic.messages.create({
           model: getEnvVar('ANTHROPIC_MODEL', 'claude-sonnet-4-20250514'), 
           max_tokens: 4096, 
           temperature: 0.1, 
         messages: [{ role: "user", content: feedbackPrompt }],
      });

       const feedbackText = feedbackResponse.content[0]?.type === 'text' ? feedbackResponse.content[0].text : 'Error: Could not generate feedback text.';

         
       console.log(`[Orchestrator] Dry Run Response:\n---\n${feedbackText}\n---`);
         

       console.log(`[Orchestrator] Dry Run feedback generated.`);
         
       return {
         status: 'feedback_generated',
           feedback: feedbackText, 
         duration: Date.now() - startTime
       };
      }
    } else {
      // --- Standard Run: Generate ADR ---
      console.log(`[Orchestrator] Standard run. Generating ADR extraction prompt.`);
    const prompt = generatePrompt(prContext, codeChanges, comments, deploymentConstitution); // <-- PASS CONSTITUTION
      const result = await callLLM(prompt); // callLLM expects JSON here

      // Validate the primary response structure
      if (!result || !Array.isArray(result.architectural_decisions)) {
          console.error(`[Orchestrator] Invalid LLM response structure: ${JSON.stringify(result)}`);
          throw new Error(`Invalid LLM response structure. Expected 'architectural_decisions' array. Received: ${JSON.stringify(result)}`);
    }

      decisions = result.architectural_decisions || [];
      decisionsCount = decisions.length;
      const noDecisionReason = result.no_architecture_impact_reason || null;
      const noDecisionConfidence = result.no_decision_confidence_score || null;

      if (decisionsCount === 0) {
        console.log(`[Orchestrator] No significant architectural decisions identified. Reason: ${noDecisionReason || 'None provided'}`);
        status = 'completed_no_decisions';
        errorMessage = noDecisionReason; // Store the reason
        // Log completion without decisions
        await logProcessedPR(prContext, status, decisionsCount, errorMessage);
        return { status, decisionsCount, reason: errorMessage, duration: Date.now() - startTime };
      }

      console.log(`[Orchestrator] Extracted ${decisionsCount} potential architectural decision(s).`);

      // Process each decision: enrich, analyze alternatives, store
    const processedDecisions = [];
      const processingErrors = []; // Collect errors during individual decision processing

      for (let i = 0; i < decisions.length; i++) {
        let decision = decisions[i];
        const decisionNumber = i + 1;
        console.log(`[Orchestrator] Processing decision ${decisionNumber}/${decisionsCount}: "${decision.title}"`);

        try {
          // Add metadata before storage/analysis
        decision.pr_number = prContext.number;
        decision.pr_url = prContext.html_url;
          decision.pr_merged_at = prContext.merged_at ? Date.parse(prContext.merged_at) : Date.now(); // Use merge time or current time
          decision.repository_slug = installationRepositorySlug; // Added repo slug
        decision.extracted_at = new Date().toISOString();

          // TODO: Clean the decision object just before storage?
          // decision = await cleanDecisionForStorage(decision);

          // --- Alternatives Analysis ---
          let alternativesAnalysisResult = { status: "not_run" }; // Default
          
          // Determine if alternatives analysis should be run, skipped, or recommended.
          const analysisDetermination = await shouldAnalyzeAlternatives(decision, codeChanges);

          if (decision.follows_standard_practice === true) {
            // Explicitly follows standard practice, so skip analysis.
            console.log(`[Orchestrator] Skipping alternatives analysis for "${decision.title}" as it explicitly follows standard practice. Reason: ${analysisDetermination.reason}`);
            alternativesAnalysisResult = { status: "skipped_standard_practice", reason: analysisDetermination.reason };
          } else {
            // Does not explicitly follow standard practice (or 'follows_standard_practice' is false/undefined).
            if (analysisDetermination.shouldAnalyze) {
              // Conditions met that would normally trigger analysis (new pattern, high impact, etc.).
              // Instead of running, now we recommend it.
              console.log(`[Orchestrator] Alternatives analysis is RECOMMENDED for "${decision.title}" (but not run automatically). Reason: ${analysisDetermination.reason}`);
              alternativesAnalysisResult = { status: "recommended", reason: analysisDetermination.reason };
              // DO NOT CALL: await analyzeAlternatives(decision, namespace);
            } else {
              // Conditions met to skip analysis for other reasons (e.g., trivial, moderate extension, implied standard practice).
              console.log(`[Orchestrator] Skipping alternatives analysis for "${decision.title}" based on other criteria: ${analysisDetermination.reason}`);
              alternativesAnalysisResult = { status: `skipped_${analysisDetermination.reason}`, reason: analysisDetermination.reason };
            }
          }
          decision.alternatives_analysis = JSON.stringify(alternativesAnalysisResult); // Store result stringified

          // --- Alignment Analysis ---
          // TODO: Implement or call alignment analysis logic if needed
          // decision.alignment_analysis = JSON.stringify(await analyzeAlignment(decision, namespace));

           // --- Relationship Analysis (Forward-Looking) ---
           console.log(`[Orchestrator] Performing forward-looking relationship analysis for "${decision.title}".`);
           const relationshipAnalysis = await analyzeRelationships(decision, namespace, { direction: 'forward' });
           const supersedingRelationship = relationshipAnalysis.relationships?.find(rel => rel.relationship_type === 'supersedes');
 
           if (supersedingRelationship) {
             decision.is_superseded = true;
             decision.superseded_by_decision_id = supersedingRelationship.existing_decision_id;
             console.log(`[Orchestrator] Decision "${decision.title}" marked as superseded by ${supersedingRelationship.existing_decision_id}.`);
           } else {
             decision.is_superseded = false;
           }
           decision.relationships_analysis = JSON.stringify(relationshipAnalysis); // Store for debugging/future use


          // --- Store Decision ---
          console.log(`[Orchestrator] Storing decision "${decision.title}" in namespace ${namespace}.`);
          const pineconeId = await storeDecisionRecord(decision, namespace);
          console.log(`[Orchestrator] Decision "${decision.title}" stored with Pinecone ID: ${pineconeId}`);
          decision.pinecone_id = pineconeId; // Add ID to the object in memory

          // --- Store Relationships (if any and Supabase is configured) ---
          // Relationships are now analyzed in a separate phase, but if a decision *is* the source of a supersedes,
          // we need to mark the target as superseded in Pinecone.
          // This relies on `decision.relationships_analysis` potentially being populated by an earlier step
          // or being available if this function is re-used in a multi-phase processor.
          // For now, we assume relationship analysis (identifying what THIS decision supersedes) happens before this point
          // or that `storeDecisionRelationships` (which is called elsewhere) handles this interaction.
          // Let's refine this: after storing relationships in Supabase (which should happen after this decision is stored),
          // we then update Pinecone status for any decisions this one supersedes.

          // The actual relationship storage and subsequent marking will be handled by `storeDecisionRelationships` 
          // and the logic that calls it. Here we just ensure the decision itself is stored.

          // If this `processMergedPR` is part of a multi-step flow where relationship analysis
          // has ALREADY run and populated `decision.relationships_analysis` (e.g. from an LLM call in this function scope):
          if (decision.relationships_analysis && decision.relationships_analysis.relationships) {
            const currentDecisionSupersedes = decision.relationships_analysis.relationships.filter(
              (rel) => rel.relationship_type === 'supersedes'
            );
            if (currentDecisionSupersedes.length > 0) {
              console.log(`[Orchestrator] Decision "${decision.title}" (${pineconeId}) supersedes ${currentDecisionSupersedes.length} other decision(s). Marking them in Pinecone.`);
              for (const rel of currentDecisionSupersedes) {
                await markDecisionAsSuperseded(rel.existing_decision_id, pineconeId, namespace);
              }
            }
          }

          processedDecisions.push(decision); // Keep track of successfully processed decisions

        } catch (decisionError) {
            console.error(`[Orchestrator] Error processing decision "${decision.title}":`, decisionError);
            processingErrors.push({ title: decision.title, error: decisionError.message });
            // Continue processing other decisions
        }
      } // End loop through decisions

      // Determine final status based on processing errors
      if (processingErrors.length > 0) {
          if (processingErrors.length === decisionsCount) {
              status = 'failed'; // All decisions failed
              errorMessage = `All ${decisionsCount} decisions failed processing. See logs for details. First error: ${processingErrors[0].error}`;
          } else {
              status = 'completed_with_errors'; // Some decisions failed
              errorMessage = `${processingErrors.length} out of ${decisionsCount} decisions failed processing. See logs. Errors: ${JSON.stringify(processingErrors)}`;
          }
                 } else {
          status = 'completed_successfully'; // All decisions processed ok
          errorMessage = `Successfully processed ${processedDecisions.length} decision(s).`;
      }

      console.log(`[Orchestrator] Finished processing PR ${prContext.html_url}. Status: ${status}`);
      // Log final status
      await logProcessedPR(prContext, status, processedDecisions.length, errorMessage, processedDecisions); // Log processed decisions

      return { status, decisions: processedDecisions, duration: Date.now() - startTime, errors: processingErrors };
    } // End else (standard run)

  } catch (error) {
    console.error(`[Orchestrator] Unhandled error during PR processing for ${prContext.html_url}:`, error);
    status = 'failed';
    errorMessage = error.message || 'An unknown error occurred during processing.';
    // Log failure
    await logProcessedPR(prContext, status, decisionsCount, errorMessage, decisions); // Log decisions extracted before failure
    // Ensure a consistent error return format
    return { status, error: errorMessage, decisions: decisions, duration: Date.now() - startTime };
  }
}

// --- Add the new similarity functions here ---

/**
 * Utility to test semantic similarity between two architectural decisions in Pinecone
 * 
 * @param {string} decisionId1 - First decision ID (e.g., "decision_1745257931137_c82217262732f")
 * @param {string} decisionId2 - Second decision ID
 * @returns {Promise<object>} Similarity results with detailed information
 */
export async function testDecisionSimilarity(decisionId1, decisionId2, targetNamespace) { // No longer optional
  initializeClients();
  if (!targetNamespace) {
    console.error(`[Decision Similarity Test] CRITICAL: targetNamespace not provided. Aborting similarity test.`);
    throw new Error("targetNamespace is required for testDecisionSimilarity.");
  }
  const currentActiveNamespace = targetNamespace;
  const logPrefix = `[Decision Similarity Test - Ns: ${currentActiveNamespace}]`;
  console.log(`${logPrefix} Testing similarity between ${decisionId1} and ${decisionId2}`);
  
  try {
    // 1. Fetch both decisions from Pinecone
    console.log(`${logPrefix} Fetching decisions from Pinecone...`);
    
    const fetchDecision = async (id) => {
      console.log(`Fetching ID: ${id} from namespace: ${currentActiveNamespace}`);
      const result = await pineconeIndex.namespace(currentActiveNamespace).fetch([id]);
      
      if (!result.records || !result.records[id]) { 
        throw new Error(`Decision ${id} not found in Pinecone index (namespace: ${currentActiveNamespace})`);
      }
      
      // Check if values are present
      if (!result.records[id].values) {
        console.warn(`${logPrefix} Vector values not found for decision ${id}. Regenerating embedding.`);
        const text = `${result.records[id].metadata.title || ''}\n${result.records[id].metadata.description || ''}`;
        if (!text.trim()) {
             throw new Error(`Cannot generate embedding for ${id}: Missing title and description.`);
        }
        result.records[id].values = await generateEmbedding(text);
        if (!result.records[id].values) {
          throw new Error(`Failed to generate embedding for ${id}.`);
        }
      }
      
      return {
        id,
        title: result.records[id].metadata?.title || 'No title',
        description: result.records[id].metadata?.description || 'No description',
        metadata: result.records[id].metadata,
        vector: result.records[id].values
      };
    };
    
    const decision1 = await fetchDecision(decisionId1);
    const decision2 = await fetchDecision(decisionId2);
    
    console.log(`${logPrefix} Successfully retrieved vectors for both decisions`);
    
    // 2. Calculate cosine similarity (Vectors are guaranteed by fetchDecision)
    const vector1 = decision1.vector;
    const vector2 = decision2.vector;
    
    const dotProduct = vector1.reduce((sum, val, i) => sum + val * vector2[i], 0);
    const mag1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
    const mag2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));
    
    let similarity = 0; // Default to 0 if magnitudes are zero
    if (mag1 !== 0 && mag2 !== 0) {
        similarity = dotProduct / (mag1 * mag2);
    }
    
    // 3. Return detailed results
    const results = {
      similarity,
      similarityPercentage: (similarity * 100).toFixed(2) + '%',
      aboveThreshold: {
        // Define reasonable thresholds or make them parameters?
        high: similarity >= 0.8,
        medium: similarity >= 0.65 && similarity < 0.8,
        low: similarity < 0.65
      },
      decisions: {
        first: {
          id: decisionId1,
          title: decision1.title,
          description: typeof decision1.description === 'string' 
              ? decision1.description.substring(0, 100) + '...' 
              : 'N/A' // Handle non-string descriptions
        },
        second: {
          id: decisionId2, 
          title: decision2.title,
          description: typeof decision2.description === 'string' 
              ? decision2.description.substring(0, 100) + '...' 
              : 'N/A' // Handle non-string descriptions
        }
      }
    };
    
    console.log(`${logPrefix} Similarity results:`, {
      similarity: results.similarityPercentage,
      rating: results.aboveThreshold.high ? 'High' : (results.aboveThreshold.medium ? 'Medium' : 'Low')
    });
    
    return results;
    
  } catch (error) {
    console.error(`${logPrefix} Error:`, error);
    // Add more specific error information if possible
    throw new Error(`Failed to test similarity: ${error.message}`);
  }
}

/**
 * Queries the Pinecone index specifically to retrieve potential candidate decisions
 * for relationship analysis, returning the full match objects.
 *
 * @param {object} decision - The extracted decision object (used to generate query text).
 * @param {number} k - The number of relevant documents to retrieve.
 * @param {object} [options] - Additional query options.
 * @param {number} [options.minScore=0.75] - Minimum similarity score threshold (0-1).
 * @param {string} [options.filter] - Optional metadata filter expression (JSON string format).
 * @param {string} namespace - The Pinecone namespace to query within.
 * @returns {Promise<Array<object>>} - An array of matching Pinecone record objects (including id and metadata), or an empty array if none found/error.
 */
async function queryRAGForRelationship(decision, k = 5, options = {}, namespace) {
  const { minScore = 0.75, filter = null } = options;
  const queryText = `${decision.title}\n${decision.description}`;
  const logPrefix = `[RAG Query (Rel) - Dec: ${decision.id} - Ns: ${namespace}]`; // Shorter prefix

  // Default filter to exclude superseded decisions
  const baseFilter = { 'is_superseded': false };
  let combinedFilter = baseFilter;

  if (filter) {
    try {
      const userFilter = JSON.parse(filter);
      combinedFilter = { $and: [baseFilter, userFilter] };
      console.log(`${logPrefix} Combined user filter with base filter (exclude superseded).`);
    } catch (e) {
      console.error(`${logPrefix} Invalid user-provided filter (must be JSON parsable string): ${filter}. Using base filter only.`, e);
    }
  }
  
  console.log(`${logPrefix} Starting query. Params: k=${k}, minScore=${minScore}, finalFilter=${JSON.stringify(combinedFilter)}`);

  if (!namespace) {
      console.error(`${logPrefix} Critical Error: Namespace not provided.`);
      return []; // Return empty array on critical error
  }

  if (!pineconeIndex) {
    console.error(`${logPrefix} Pinecone index not initialized.`);
    return [];
  }

  if (!queryText.trim()) {
    console.warn(`${logPrefix} Cannot query RAG: Decision has no title or description.`);
    return [];
  }

  try {
    // 1. Generate embedding for the query text
    const queryEmbedding = await generateEmbedding(queryText);
    if (!queryEmbedding) {
      console.error(`${logPrefix} Failed to generate query embedding.`);
      return [];
    }

    // 2. Prepare Pinecone query options
    const queryOptions = {
      vector: queryEmbedding,
      topK: k,
      includeMetadata: true,
      includeValues: false, // Don't need vectors in the response for relationship analysis
      filter: combinedFilter // Use the combined or base filter
    };

    // 3. Add filter if provided
    // This section is now handled by the combinedFilter logic above
    /*
    if (filter) {
      try {
        // Basic validation: Ensure it's a parsable object string
        JSON.parse(filter);
        queryOptions.filter = JSON.parse(filter); // Pinecone expects object, not string
         console.debug(`${logPrefix} Applying filter:`, queryOptions.filter);
      } catch (e) {
        console.error(`${logPrefix} Invalid filter provided (must be JSON parsable string): ${filter}`, e);
        return []; // Stop if filter is invalid
      }
    }
    */

    // 4. Execute Pinecone query
    console.log(`${logPrefix} Executing Pinecone query...`);
    const queryResponse = await pineconeIndex.namespace(namespace).query(queryOptions);
    const rawMatchCount = queryResponse.matches?.length || 0;
    console.log(`${logPrefix} Pinecone query returned ${rawMatchCount} raw matches.`);

    // 5. Filter results by similarity score
    const filteredMatches = queryResponse.matches?.filter(match => match.score >= minScore) || [];
    const filteredMatchCount = filteredMatches.length;
    console.log(`${logPrefix} Filtered matches by minScore >= ${minScore}. Kept ${filteredMatchCount} of ${rawMatchCount}.`);

    if (filteredMatchCount === 0) {
        console.log(`${logPrefix} No matches found above similarity threshold.`);
        return [];
    }

    // 6. Return the array of filtered matches (full objects)
    console.log(`${logPrefix} Returning ${filteredMatchCount} matches for relationship analysis.`);
     if (process.env.DEBUG === 'true') {
        // Log more details for debugging relationships
        console.debug(`${logPrefix} Matches returned (IDs, Scores, Titles):`, filteredMatches.map(m => ({ id: m.id, score: m.score, title: m.metadata?.title || 'N/A' })));
     }
    return filteredMatches;

  } catch (error) {
    // Handle potential JSON parsing errors for the filter
    if (error instanceof SyntaxError && filter) {
        console.error(`${logPrefix} Error parsing filter JSON: ${filter}`, error);
    } else {
        console.error(`${logPrefix} Error during RAG query for relationships:`, error);
    }
    return []; // Return empty array on error
  }
}

// ---> ADDED: Function stub for file overlap query
/**
 * Finds existing decisions that mention the same files as the new decision.
 * TODO: Implement the actual query logic (against Supabase or Pinecone metadata).
 *
 * @param {object} newDecision - The new decision object, expecting `newDecision.related_files` and `newDecision.id`.
 * @param {string} namespace - The Pinecone namespace (may be needed if querying Pinecone metadata).
 * @returns {Promise<Array<object>>} - An array of matching Pinecone-like record objects { id, metadata }, or an empty array.
 */
async function findDecisionsByFileOverlap(newDecision, targetNamespace) { // No longer optional
    if (!targetNamespace) {
        console.error(`[File Overlap Query - Dec: ${newDecision.title}] CRITICAL: targetNamespace not provided. Aborting query.`);
        return [];
    }
    const currentActiveNamespace = targetNamespace;
    const logPrefix = `[File Overlap Query - Dec: ${newDecision.title} - Ns: ${currentActiveNamespace}]`;
    const relatedFiles = newDecision.related_files || [];

    if (relatedFiles.length === 0) {
        console.log(`${logPrefix} No related files found in the new decision. Skipping file overlap query.`);
        return [];
    }
    if (!pineconeIndex) {
        console.error(`${logPrefix} Pinecone index not initialized. Cannot perform file overlap query.`);
        return [];
    }
    try {
        const stats = await pineconeIndex.describeIndexStats();
        if (!stats.namespaces || !stats.namespaces[currentActiveNamespace]) {
            console.warn(`${logPrefix} Namespace '${currentActiveNamespace}' does not appear to exist in Pinecone index stats. Querying may fail or return no results.`);
        }
    } catch (statsError) {
        console.error(`${logPrefix} Error fetching index stats for namespace check:`, statsError);
        // Decide if to proceed or return empty array
    }

    console.log(`${logPrefix} Searching for decisions related to files: ${relatedFiles.join(', ')}`);

    try {
        // ---> MODIFIED: Implement Pinecone metadata query
        // Construct the filter to find documents where 'related_files' list
        // contains any of the files from the new decision, excluding the decision itself.
        const filterCriteria = {
            $and: [
                { 'is_superseded': false }, // <--- ADDED: Exclude superseded decisions
                { related_files: { $in: relatedFiles } } // Check if the metadata field (list) contains any of these files
            ]
        };

        console.debug(`${logPrefix} Pinecone filter for file overlap:`, JSON.stringify(filterCriteria));

        // Pinecone query typically requires a vector. Since we're filtering only,
        // we can provide a dummy vector (e.g., zeros) and rely on the filter.
        // We need to retrieve enough candidates potentially matching the filter.
        const dimension = 1536; // Hardcode dimension to bypass describeIndexStats
        const dummyVector = new Array(dimension).fill(0);
        const topK_limit = 50; // Retrieve up to 50 potential matches via filter

        const queryResponse = await pineconeIndex.namespace(currentActiveNamespace).query({
            vector: dummyVector,
            topK: topK_limit,
            filter: filterCriteria,
            includeMetadata: true,
            includeValues: false // We only need metadata
        });

        const matches = queryResponse.matches || [];
        console.log(`${logPrefix} Found ${matches.length} potential matches via file overlap query (pre-filtering, limit ${topK_limit}).`);

        // Although the filter is applied server-side, we already got the results.
        // The matches returned *should* already satisfy the filter.
        // Format results to match expected structure { id, metadata }
        // (Pinecone response already aligns well)
        const formattedMatches = matches.map(match => ({ 
            id: match.id, 
            metadata: match.metadata 
            // Optional: Add score if needed, but it's based on dummy vector so not meaningful here
        }));

        if (process.env.DEBUG === 'true' && formattedMatches.length > 0) {
             console.debug(`${logPrefix} File Overlap Matches (IDs, Titles):`, formattedMatches.map(m => ({ id: m.id, title: m.metadata?.title || 'N/A' })));
        }

        return formattedMatches;
        // <--- END MODIFIED

    } catch (error) {
        console.error(`${logPrefix} Error querying Pinecone for file overlap:`, error);
        return [];
    }
}
// <--- END ADDED

/**
 * Finds decisions similar to a given decision ID within the same namespace.
 * @param {string} decisionId - The ID of the decision to find similar items for.
 * @param {number} k - The number of similar decisions to retrieve.
 * @param {number} minScore - The minimum similarity score threshold.
 * @returns {Promise<Array<object>>} - An array of similar decision objects with metadata and score.
 */
export async function findSimilarDecisions(decisionId, k = 5, minScore = 0.7, targetNamespace) { // No longer optional
  initializeClients();
  if (!targetNamespace) {
    console.error(`[Find Similar Decisions] CRITICAL: targetNamespace not provided. Aborting search.`);
    throw new Error("targetNamespace is required for findSimilarDecisions.");
  }
  const currentActiveNamespace = targetNamespace;
  const logPrefix = `[Find Similar Decisions - Ns: ${currentActiveNamespace}]`;
  console.log(`${logPrefix} Finding top ${k} decisions similar to ${decisionId} (minScore: ${minScore})`);

  try {
    // 1. Fetch the vector for the source decision
    console.log(`${logPrefix} Fetching source decision ${decisionId}...`);
    const fetchResult = await pineconeIndex.namespace(currentActiveNamespace).fetch([decisionId]);

    if (!fetchResult.records || !fetchResult.records[decisionId]) {
      throw new Error(`Source decision ${decisionId} not found in Pinecone index (namespace: ${currentActiveNamespace})`);
    }
    
    let sourceVector = fetchResult.records[decisionId].values;
    const sourceMetadata = fetchResult.records[decisionId].metadata;

    // Regenerate embedding if values are missing
    if (!sourceVector) {
        console.warn(`${logPrefix} Vector values not found for source decision ${decisionId}. Regenerating embedding.`);
        const text = `${sourceMetadata?.title || ''}\n${sourceMetadata?.description || ''}`;
        if (!text.trim()) {
             throw new Error(`Cannot generate embedding for ${decisionId}: Missing title and description.`);
        }
        sourceVector = await generateEmbedding(text);
        if (!sourceVector) {
          throw new Error(`Failed to generate embedding for ${decisionId}.`);
        }
        console.log(`${logPrefix} Embedding regenerated for ${decisionId}.`);
    }

    console.log(`${logPrefix} Source decision vector retrieved.`);

    // 2. Query Pinecone for similar vectors
    // We need to query for k+1 because the result might include the source decision itself.
    console.log(`${logPrefix} Querying Pinecone for top ${k + 1} similar vectors...`);
    const queryResponse = await pineconeIndex.namespace(currentActiveNamespace).query({
      vector: sourceVector,
      topK: k + 1, // Fetch k+1 to potentially exclude the source decision itself
      includeMetadata: true,
      includeValues: false, // Don't need vectors in the response
      // ---> ADDED: Default filter to exclude superseded decisions
      filter: { 'is_superseded': false }
    });

    const rawMatchCount = queryResponse.matches?.length || 0;
    console.log(`${logPrefix} Pinecone query returned ${rawMatchCount} raw matches.`);

    // 3. Filter results
    const filteredMatches = (queryResponse.matches || [])
      .filter(match => match.id !== decisionId && match.score >= minScore) // Exclude self and filter by score
      .slice(0, k); // Take the top k results after filtering

    const finalMatchCount = filteredMatches.length;
    console.log(`${logPrefix} Filtered matches (excluding self, score >= ${minScore}). Found ${finalMatchCount} similar decisions.`);

    if (process.env.DEBUG === 'true' && finalMatchCount > 0) {
        console.debug(`${logPrefix} Similar Matches (IDs and Scores):`, filteredMatches.map(m => ({ id: m.id, score: m.score, title: m.metadata?.title || 'N/A' })));
    }

    return filteredMatches;

  } catch (error) {
    console.error(`${logPrefix} Error:`, error);
    throw new Error(`Failed to find similar decisions for ${decisionId}: ${error.message}`);
  }
}

/**
 * Stores identified decision relationships in the Supabase database.
 *
 * @param {string} sourceDecisionId - The Pinecone ID of the new decision.
 * @param {string} repositorySlug - The slug of the repository (e.g., 'owner/repo') - THIS SHOULD BE THE INSTALLATION REPO SLUG.
 * @param {number} sourcePrNumber - The PR number where the source decision was identified.
 * @param {Array<object>} relationships - An array of relationship objects from the LLM analysis.
 *                                       Expected format: [{ existing_decision_id, relationship_type, confidence_score }, ...]
 * @returns {Promise<void>}
 */
async function storeDecisionRelationships(sourceDecisionId, repositorySlug, sourcePrNumber, relationships) {
  // Define logPrefix at the beginning of the function scope
  const logPrefix = `[Rel Store - SrcDec: ${sourceDecisionId} Repo: ${repositorySlug}]`;

  if (!supabase) {
    // Use logPrefix safely here if needed, or keep specific log message
    console.warn(`[Rel Store - SrcDec: ${sourceDecisionId}] Supabase client not available. Skipping relationship storage.`);
    return;
  }
  if (!relationships || relationships.length === 0) {
    // Now logPrefix is accessible here
    console.log(`${logPrefix} No relationships provided to store. Skipping.`);
    return;
  }

  // logPrefix was moved up

  const relationshipsToInsert = relationships
    .filter(rel => rel.relationship_type !== 'independent') // Only store meaningful relationships
    .map(rel => ({
      source_decision_pinecone_id: sourceDecisionId,
      target_decision_pinecone_id: rel.existing_decision_id,
      relationship_type: rel.relationship_type,
      confidence_score: rel.confidence_score,
      justification: rel.justification,
      repository_slug: repositorySlug,
      source_pr_number: sourcePrNumber,
    }));

  if (relationshipsToInsert.length === 0) {
    console.log(`${logPrefix} No non-independent relationships found in LLM response. Nothing to insert.`);
    // Log the original relationships received if debugging
    if (process.env.DEBUG === 'true') {
        console.debug(`${logPrefix} Original relationships received from LLM:`, JSON.stringify(relationships));
    }
    return;
  }

  console.log(`${logPrefix} Attempting to insert ${relationshipsToInsert.length} relationships into Supabase...`);
  console.debug(`${logPrefix} Relationships to insert:`, JSON.stringify(relationshipsToInsert)); // Log data being inserted

  try {
    const { data, error } = await supabase
      .from('decision_relationships')
      .insert(relationshipsToInsert)
      .select(); // Optionally select to confirm insertion

    if (error) {
      console.error(`${logPrefix} Error inserting relationships for ${sourceDecisionId}:`, error);
      // Consider more specific error handling or retry logic if needed
      throw error; // Re-throw to indicate failure
    }

    console.log(`${logPrefix} Successfully inserted ${data?.length || 0} relationships.`);

  } catch (error) {
    console.error(`${logPrefix} Unexpected error during Supabase insert:`, error);
    // Handle unexpected errors (e.g., network issues)
  }
}

// ---> ADDED: Function stub for Domain Concept overlap query
/**
 * Finds existing decisions that share common domain concepts with the new decision.
 * TODO: Implement the actual query logic. This might involve:
 *  1. Extracting domain concepts (keywords, topics) from decision text (title, desc, rationale) - perhaps using an LLM or NLP library during storage.
 *  2. Storing these concepts as a list (e.g., `domain_concepts: ['auth', 'database', 'caching']`) in Pinecone metadata.
 *  3. Querying Pinecone metadata using a filter like `{ domain_concepts: { $in: newDecisionConcepts } }`.
 *
 * @param {object} newDecision - The new decision object (potentially with pre-extracted concepts).
 * @param {string} targetNamespace - The Pinecone namespace.
 * @returns {Promise<Array<object>>} - An array of matching Pinecone-like record objects { id, metadata }, or an empty array.
 */
async function findDecisionsByDomainConcepts(newDecision, targetNamespace) { // No longer optional
    if (!targetNamespace) {
        console.error(`[Domain Concept Query - Dec: ${newDecision.title}] CRITICAL: targetNamespace not provided. Aborting query.`);
        return [];
    }
    const currentActiveNamespace = targetNamespace;
    const logPrefix = `[Domain Concept Query - Dec: ${newDecision.title} - Ns: ${currentActiveNamespace}]`;
    const ownId = newDecision.id;

    const newDecisionConcepts = newDecision.domain_concepts;

    // Check if it's a valid array of strings
    if (!Array.isArray(newDecisionConcepts) || newDecisionConcepts.length === 0 || newDecisionConcepts.some(c => typeof c !== 'string')) {
        console.log(`${logPrefix} No valid domain concepts found in metadata for the new decision (field: 'domain_concepts'). Skipping concept overlap query.`);
        return [];
    }

    if (!pineconeIndex) {
        console.error(`${logPrefix} Pinecone index not initialized. Cannot perform domain concept query.`);
        return [];
    }
    try {
        const stats = await pineconeIndex.describeIndexStats();
        if (!stats.namespaces || !stats.namespaces[currentActiveNamespace]) {
            console.warn(`${logPrefix} Namespace '${currentActiveNamespace}' does not appear to exist in Pinecone index stats. Querying may fail or return no results.`);
        }
    } catch (statsError) {
        console.error(`${logPrefix} Error fetching index stats for namespace check:`, statsError);
    }

    console.log(`${logPrefix} Searching for decisions related to concepts: ${newDecisionConcepts.join(', ')}`);

    try {
        // Implement Pinecone query using metadata filter for concept overlap
        const filterCriteria = {
            $and: [
                { 'is_superseded': false }, // <--- ADDED: Exclude superseded decisions
                { domain_concepts: { $in: newDecisionConcepts } }, // Find docs where concepts overlap
                { pinecone_id: { $ne: ownId } }                 // Exclude self
            ]
        };
        console.debug(`${logPrefix} Pinecone filter for domain concepts:`, JSON.stringify(filterCriteria));

        // Use dummy vector approach as we are filtering on metadata
        const dimension = 1536; // Hardcode dimension to bypass describeIndexStats
        const dummyVector = new Array(dimension).fill(0);
        const topK_limit = 50; // Retrieve up to 50 potential matches via filter

        const queryResponse = await pineconeIndex.namespace(currentActiveNamespace).query({
            vector: dummyVector,
            topK: topK_limit,
            filter: filterCriteria,
            includeMetadata: true,
            includeValues: false
        });

        const matches = queryResponse.matches || [];
        console.log(`${logPrefix} Found ${matches.length} potential matches via domain concept overlap query (limit ${topK_limit}).`);

        const formattedMatches = matches.map(match => ({ 
            id: match.id, 
            metadata: match.metadata 
        }));

        if (process.env.DEBUG === 'true' && formattedMatches.length > 0) {
             console.debug(`${logPrefix} Domain Concept Overlap Matches (IDs, Titles):`, formattedMatches.map(m => ({ id: m.id, title: m.metadata?.title || 'N/A' })));
        }

        return formattedMatches;
        // <--- END MODIFIED

    } catch (error) {
        console.error(`${logPrefix} Error querying Pinecone for domain concept overlap:`, error);
        return [];
    }
}
// <--- END ADDED

// ---> ADDED: Function stub for Impact Overlap query
/**
 * Finds existing decisions whose implications might overlap with the new decision.
 * TODO: Implement the actual query logic. This could involve:
 *  1. Comparing `implications` text semantically (e.g., generate embedding for implications, query Pinecone for similar implication vectors).
 *  2. Extracting concepts/keywords specifically from `implications` and storing/querying them via metadata filters.
 *  3. A simpler approach: just reuse the main RAG query but focus it on the implications text.
 *
 * @param {object} newDecision - The new decision object, expecting `newDecision.implications` and `newDecision.id`.
 * @param {string} targetNamespace - The Pinecone namespace.
 * @returns {Promise<Array<object>>} - An array of matching Pinecone-like record objects { id, metadata }, or an empty array.
 */
async function findDecisionsByImpactOverlap(newDecision, targetNamespace) { // No longer optional
    if (!targetNamespace) {
        console.error(`[Impact Overlap Query - Dec: ${newDecision.title}] CRITICAL: targetNamespace not provided. Aborting query.`);
        return [];
    }
    const currentActiveNamespace = targetNamespace;
    const logPrefix = `[Impact Overlap Query - Dec: ${newDecision.title} - Ns: ${currentActiveNamespace}]`;
    // ---> MODIFIED: Use the actual implications text from the decision object
    const implicationsText = newDecision.implications || ''; // Get text from the processed decision
    const minScore = 0.4; // Similarity threshold for impact
    const k = 5; // Max number of candidates to find based on impact
    // <--- END MODIFIED

    if (!implicationsText.trim()) {
        console.log(`${logPrefix} No implications text found in the new decision. Skipping impact overlap query.`);
        return [];
    }

    if (!pineconeIndex) {
        console.error(`${logPrefix} Pinecone index not initialized. Cannot perform impact overlap query.`);
        return [];
    }
    try {
        const stats = await pineconeIndex.describeIndexStats();
        if (!stats.namespaces || !stats.namespaces[currentActiveNamespace]) {
            console.warn(`${logPrefix} Namespace '${currentActiveNamespace}' does not appear to exist in Pinecone index stats. Querying may fail or return no results.`);
        }
    } catch (statsError) {
        console.error(`${logPrefix} Error fetching index stats for namespace check:`, statsError);
    }


    console.log(`${logPrefix} Searching for decisions with overlapping implications (minScore: ${minScore}, k: ${k})...`);

    try {
        // ---> MODIFIED: Implement semantic search on implications text
        console.debug(`${logPrefix} Implications text to embed: "${implicationsText.substring(0, 100)}..."`);
        const implicationsEmbedding = await generateEmbedding(implicationsText);
        if (!implicationsEmbedding) {
            console.error(`${logPrefix} Failed to generate embedding for implications text.`);
            return [];
        }

        const queryResponse = await pineconeIndex.namespace(currentActiveNamespace).query({
            vector: implicationsEmbedding,
            topK: k,
            includeMetadata: true,
            includeValues: false, // Values not strictly needed, scores are
            // ---> ADDED: Default filter to exclude superseded decisions
            filter: { 'is_superseded': false }
        });

        const rawMatchCount = queryResponse.matches?.length || 0;
        console.log(`${logPrefix} Pinecone query returned ${rawMatchCount} raw matches based on implications.`);

        // Filter by score
        const filteredMatches = queryResponse.matches?.filter(match => match.score >= minScore) || [];
        const filteredMatchCount = filteredMatches.length;
        console.log(`${logPrefix} Filtered implications matches by minScore >= ${minScore}. Kept ${filteredMatchCount} of ${rawMatchCount}.`);

        if (filteredMatchCount === 0) {
            console.log(`${logPrefix} No matches found above similarity threshold for implications.`);
            return [];
        }

        // Format results
        const formattedMatches = filteredMatches.map(match => ({ 
            id: match.id, 
            metadata: match.metadata,
            score: match.score // Include score as it indicates similarity of impact
        }));

        if (process.env.DEBUG === 'true') {
            console.debug(`${logPrefix} Impact Overlap Matches (IDs, Scores, Titles):`, formattedMatches.map(m => ({ id: m.id, score: m.score, title: m.metadata?.title || 'N/A' })));
        }

        return formattedMatches;
        // <--- END MODIFIED

    } catch (error) {
        console.error(`${logPrefix} Error querying Pinecone for impact overlap:`, error);
        return [];
    }
}
// <--- END ADDED

/**
 * Cleans up the decision object before storing it in Pinecone.
 */
async function cleanDecisionForStorage(decision) {
    // Implement any necessary cleaning logic here
    return decision;
}

// --- End of new similarity functions --- 

/**
 * Normalizes concept strings against a canonical list in Supabase.
 * If a concept doesn't exist, it's created. This ensures that concepts like
 * 'Auth' and 'Authentication' are resolved to a single canonical term.
 * @param {string[]} concepts - An array of concept strings from the LLM.
 * @param {string} repositorySlug - The repository slug for scoping.
 * @returns {Promise<string[]>} - A promise that resolves to an array of normalized, canonical concept names.
 */
async function normalizeAndStoreConcepts(concepts, repositorySlug) {
    const logPrefix = `[Concept Normalization - Repo: ${repositorySlug}]`;
    const SIMILARITY_THRESHOLD = 0.95; // Confidence threshold for treating concepts as synonyms

    if (!concepts || concepts.length === 0) {
        return [];
    }

    const normalizedConcepts = new Set();
    const conceptVectorMap = new Map();

    for (const concept of concepts) {
        const trimmedConcept = concept.trim();
        if (!trimmedConcept) continue;

        let embedding;
        try {
            embedding = await generateEmbedding(trimmedConcept);
        } catch (e) {
            console.error(`${logPrefix} Failed to generate embedding for concept '${trimmedConcept}'. Skipping normalization for it.`, e);
            normalizedConcepts.add(trimmedConcept); // Add original concept as fallback
            continue;
        }

        // Query the concepts index to find semantically similar concepts
        const queryResponse = await conceptsIndex.namespace(repositorySlug).query({
            vector: embedding,
            topK: 1,
            includeMetadata: true,
        });

        const topMatch = queryResponse.matches?.[0];

        if (topMatch && topMatch.score >= SIMILARITY_THRESHOLD) {
            // Found a sufficiently similar concept, use its ID (the canonical name)
            console.log(`${logPrefix} Normalizing '${trimmedConcept}' to existing concept '${topMatch.id}' (Score: ${topMatch.score})`);
            normalizedConcepts.add(topMatch.id);
        } else {
            // No similar concept found, this is a new canonical concept.
            // Add it to our set of normalized concepts for this run.
            console.log(`${logPrefix} No similar concept found for '${trimmedConcept}'. Treating as new canonical concept. (Top match: ${topMatch ? `'${topMatch.id}' at score ${topMatch.score}` : 'None'})`);
            normalizedConcepts.add(trimmedConcept);
            // Store the vector to avoid re-calculating if it appears again in the same PR.
            conceptVectorMap.set(trimmedConcept, embedding);
        }
    }

    // Identify which of the newly-deemed canonical concepts are not yet in the index
    const finalConcepts = Array.from(normalizedConcepts);
    const conceptsToUpsert = [];

    for (const conceptName of finalConcepts) {
        if (conceptVectorMap.has(conceptName)) {
            conceptsToUpsert.push({
                id: conceptName,
                values: conceptVectorMap.get(conceptName),
            });
        }
    }
    
    // Batch-upsert any new canonical concepts to the concepts index
    if (conceptsToUpsert.length > 0) {
        console.log(`${logPrefix} Adding ${conceptsToUpsert.length} new canonical concepts to Pinecone index '${conceptsIndexName}' in namespace '${repositorySlug}'.`);
        try {
            await conceptsIndex.namespace(repositorySlug).upsert(conceptsToUpsert);
        } catch (upsertError) {
            console.error(`${logPrefix} Failed to upsert new concepts to Pinecone:`, upsertError);
            // Don't block the main flow, the concepts will be attempted again next time.
        }
    }

    return finalConcepts;
}

// Add analyzeRelationships function stub if it doesn't exist, or ensure it's imported/defined
export async function analyzeRelationships(decision, targetNamespace, options = {}) { // No longer optional // MODIFIED: Added export
  const { direction = 'backward' } = options;
  if (!targetNamespace) {
    console.error(`[analyzeRelationships - Dec: "${decision.title}"] CRITICAL: targetNamespace not provided. Aborting analysis.`);
    return { relationships: [], potential_matches_for_analysis: [], error: "targetNamespace is required for analyzeRelationships." };
  }
  const currentActiveNamespace = targetNamespace;
  console.log(`[Orchestrator] Analyzing relationships for decision "${decision.title}" in namespace ${currentActiveNamespace}.`);
  
  try {
    // CRITICAL: Get the current decision's merge time to enforce chronological constraint
    const currentDecisionMergeTime = decision.pr_merged_at;
    if (!currentDecisionMergeTime) {
      console.error(`[Relationship Analysis] CRITICAL: Decision "${decision.title}" has no pr_merged_at timestamp. Cannot enforce chronological constraint.`);
      return { relationships: [], potential_matches_for_analysis: [], error: "Missing pr_merged_at timestamp for chronological constraint." };
    }
    
    // Convert to timestamp if it's a string
    const currentTimestamp = typeof currentDecisionMergeTime === 'string' 
      ? new Date(currentDecisionMergeTime).getTime() 
      : currentDecisionMergeTime;
    
    console.log(`[Relationship Analysis] Current decision "${decision.title}" merge timestamp: ${currentTimestamp} (${new Date(currentTimestamp).toISOString()})`);

    // 1. Find potentially related decisions (by concepts, file overlap, etc.)
    // Pass the currentActiveNamespace to these functions
    const relatedByConcept = await findDecisionsByDomainConcepts(decision, currentActiveNamespace);
    const relatedByFile = await findDecisionsByFileOverlap(decision, currentActiveNamespace);
    const relatedByImpact = await findDecisionsByImpactOverlap(decision, currentActiveNamespace);
    
    // Combine and deduplicate potential matches based on ID, prioritizing by relevance
    const potentialMatchesMap = new Map();
    const decisionIdToExclude = decision.id || decision.pinecone_id;

    const addMatchesToMap = (matches) => {
      matches.forEach(match => {
        if (match.id !== decisionIdToExclude && !potentialMatchesMap.has(match.id)) {
           potentialMatchesMap.set(match.id, match);
     }
    });
    };
    
    // Prioritize by impact similarity (highest signal), then file overlap, then concepts
    addMatchesToMap(relatedByImpact);
    addMatchesToMap(relatedByFile);
    addMatchesToMap(relatedByConcept);

    const allPotentialMatches = Array.from(potentialMatchesMap.values());

    // CRITICAL: Filter to only include decisions that are chronologically relevant based on direction
    const chronologicallyValidCandidates = allPotentialMatches.filter(match => {
      const matchMergeTime = match.metadata?.pr_merged_at;
      if (!matchMergeTime) {
        console.warn(`[Relationship Analysis] Skipping decision ${match.id} - no pr_merged_at timestamp.`);
        return false;
      }
      
      const matchTimestamp = typeof matchMergeTime === 'string' 
        ? new Date(matchMergeTime).getTime() 
        : matchMergeTime;
      
      const isChronologicallyValid = direction === 'backward'
        ? matchTimestamp < currentTimestamp
        : matchTimestamp > currentTimestamp;
      
      if (!isChronologicallyValid) {
        console.debug(`[Relationship Analysis] Excluding decision ${match.id} - not chronologically valid for ${direction} analysis.`);
      }
      return isChronologicallyValid;
    });

    const filteredCount = allPotentialMatches.length - chronologicallyValidCandidates.length;
    if (filteredCount > 0) {
      console.log(`[Relationship Analysis] Chronological constraint: Filtered out ${filteredCount} decisions for direction '${direction}'.`);
    }
    
    // Limit the number of candidates to avoid exceeding context window
    const MAX_RELATIONSHIP_CANDIDATES = 15;
    let finalCandidates = chronologicallyValidCandidates;
    if (chronologicallyValidCandidates.length > MAX_RELATIONSHIP_CANDIDATES) {
      console.log(`[Relationship Analysis] Too many candidates (${chronologicallyValidCandidates.length}). Capping to the top ${MAX_RELATIONSHIP_CANDIDATES} most relevant ones.`);
      finalCandidates = chronologicallyValidCandidates.slice(0, MAX_RELATIONSHIP_CANDIDATES);
    }

    if (finalCandidates.length === 0) {
       console.log(`[Relationship Analysis] No chronologically valid decisions found for "${decision.title}" (ID: ${decisionIdToExclude}) for direction '${direction}'.`);
       return { relationships: [], potential_matches_for_analysis: [] };
    }

    console.log(`[Relationship Analysis] Found ${finalCandidates.length} potentially related decisions for "${decision.title}" (ID: ${decisionIdToExclude}) for direction '${direction}'.`);

    // 2. Generate prompt for relationship analysis
    const relationshipPrompt = generateRelationshipAnalysisPrompt(decision, finalCandidates, { direction });
    if (!relationshipPrompt) {
        console.warn(`[Relationship Analysis] Skipping LLM call as no potential relationships were found or prompt generation failed for "${decision.title}".`);
        return { relationships: [], potential_matches_for_analysis: finalCandidates }; 
    }

    // 3. Call LLM
     console.log(`[Relationship Analysis] Calling LLM to analyze relationships for "${decision.title}" against ${finalCandidates.length} candidate decisions.`);
    const analysisResult = await callLLM(relationshipPrompt); // Expects JSON

    // 4. Validate and return result
    if (analysisResult && analysisResult.relationship_analysis && Array.isArray(analysisResult.relationship_analysis.relationships)) {
      console.log(`[Relationship Analysis] LLM analysis successful for "${decision.title}".`);
      
      // --- ADDED FILTERING --- 
      const allIdentifiedRelationships = analysisResult.relationship_analysis.relationships;
      const supersedesRelationships = allIdentifiedRelationships.filter(
        rel => rel.relationship_type === 'supersedes'
      );

      if (supersedesRelationships.length < allIdentifiedRelationships.length) {
        console.log(`[Relationship Analysis] Filtered LLM results for "${decision.title}": Kept ${supersedesRelationships.length} 'supersedes' relationships out of ${allIdentifiedRelationships.length} total identified.`);
      }
      // --- END ADDED FILTERING ---

      // Pass along potentialMatches so the caller can access metadata like dev_prompt
      // Return the filtered list of relationships
      return { 
        relationships: supersedesRelationships, 
        potential_matches_for_analysis: finalCandidates 
      };
    } else {
      console.error(`[Relationship Analysis] Invalid LLM response structure for "${decision.title}":`, analysisResult);
      // Still return potential matches even if LLM fails, so caller knows what was considered
      return { relationships: [], potential_matches_for_analysis: finalCandidates, error: "Invalid LLM response structure" };
    }
  } catch (error) {
      console.error(`[Relationship Analysis] Error during relationship analysis for "${decision.title}":`, error);
      // Return empty on error to avoid blocking the whole process, but include error info
      return { relationships: [], potential_matches_for_analysis: [], error: error.message };
  }
}

// Helper function to fetch a sample of unique domain concepts from Pinecone
async function fetchSampledUniqueDomainConcepts(namespace, sampleSize = 200) {
  const logPrefix = `[Concept Sampling - Ns: ${namespace}]`;
  console.log(`${logPrefix} Fetching up to ${sampleSize} vectors to sample unique domain concepts.`);

  if (!pineconeIndex) {
    console.error(`${logPrefix} Pinecone index not initialized.`);
    return [];
  }

  try {
    const indexStats = await pineconeIndex.describeIndexStats();
    const dimension = indexStats.dimension;
    if (!dimension) {
       console.error(`${logPrefix} Could not determine index dimension for dummy vector.`);
       return [];
    }
    // Using a zero vector as a generic query vector
    const dummyVector = new Array(dimension).fill(0);

    const queryResponse = await pineconeIndex.namespace(namespace).query({
      vector: dummyVector,
      topK: sampleSize,
      includeMetadata: true,
      includeValues: false, // We only need metadata for concepts
      filter: { 'is_superseded': false } // ---> ADDED: Default filter
    });

    const allConcepts = new Set();
    if (queryResponse.matches && queryResponse.matches.length > 0) {
      queryResponse.matches.forEach(match => {
        if (match.metadata && Array.isArray(match.metadata.domain_concepts)) {
          match.metadata.domain_concepts.forEach(concept => {
            if (typeof concept === 'string' && concept.trim() !== '') {
              allConcepts.add(concept.trim().toLowerCase()); // Store lowercase to ensure uniqueness
            }
          });
        }
      });
    }
    const uniqueConceptsArray = Array.from(allConcepts);
    console.log(`${logPrefix} Found ${uniqueConceptsArray.length} unique domain concepts from ${queryResponse.matches?.length || 0} sampled vectors.`);
    return uniqueConceptsArray;

  } catch (error) {
    console.error(`${logPrefix} Error fetching or processing vectors for unique domain concepts:`, error);
    return []; // Return empty array on error
  }
}

// Helper function to fetch all decision metadata for a given namespace from Pinecone
export async function getAllDecisionMetadataForRepo(targetNamespace, options = {}) {
  initializeClients();
  const { includeSuperseded = false } = options;
  const logPrefix = `[Orchestrator getAllDecisionMetadata - Ns: ${targetNamespace}]`;
  console.log(`${logPrefix} Fetching all decisions. Include Superseded: ${includeSuperseded}`);

  if (!pineconeIndex) {
    console.error(`${logPrefix} Pinecone index not initialized.`);
    return [];
  }
  if (!targetNamespace) {
    console.error(`${logPrefix} Target namespace not provided.`);
    return [];
  }

  try {
    const dimension = 1536; // Hardcode dimension to bypass describeIndexStats
    const dummyVector = new Array(dimension).fill(0);

    const queryResponse = await pineconeIndex.namespace(targetNamespace).query({
      vector: dummyVector,
      topK: 10000,
      includeMetadata: true,
      includeValues: false,
      filter: includeSuperseded ? undefined : { 'is_superseded': false }
    });

    console.log(`${logPrefix} Query response:`, queryResponse);
    
    const decisions = queryResponse.matches?.map(match => ({
        id: match.id,
        metadata: match.metadata || {}
    })) || [];

    console.log(`${logPrefix} Retrieved ${decisions.length} decisions.`);
    
    return decisions;

  } catch (error) {
    console.error(`${logPrefix} Error fetching sample decision metadata:`, error);
    return []; // Return empty array on error
  }
}

// --- ADDED FUNCTION ---
/**
 * This should be run as a batch process.
 * @param {string} repositorySlug - The repository slug (e.g., 'owner/repo').
 * @param {number | string} installationId - The GitHub App installation ID.
 * @returns {Promise<void>}
 */
export async function buildKnowledgeGraphForRepo(repositorySlug, installationId) {
  const logPrefix = `[KG Build - Repo: ${repositorySlug}]`;
  console.log(`${logPrefix} Starting retroactive knowledge graph build process.`);
  
  if (installationId === undefined) {
      console.error(`${logPrefix} Critical error: installationId is undefined. Cannot determine correct namespace.`);
      return;
  }

  // 1. Get the correct namespace
  const namespace = getRepositoryNamespace(installationId, repositorySlug);
  console.log(`${logPrefix} Determined target namespace: ${namespace}`);


  // 1. Fetch all *active* (non-superseded) decisions for the repo.
  console.log(`${logPrefix} Fetching all active decision metadata...`);
  const activeDecisions = await getAllDecisionMetadataForRepo(namespace, { includeSuperseded: false });

  console.log(`${logPrefix} Call to getAllDecisionMetadataForRepo completed. Result:`, activeDecisions ? `${activeDecisions.length} decisions found.` : 'null/undefined');


  if (!activeDecisions || activeDecisions.length === 0) {
    console.log(`${logPrefix} No active decisions found. Nothing to process. Exiting.`);
    return;
  }

  console.log(`${logPrefix} Found ${activeDecisions.length} active decisions to process. Starting loop...`);

  // 2. Iterate through each decision, extract graph triples, and store them.
  let processedCount = 0;
  for (const decision of activeDecisions) {
    const decisionLogPrefix = `[KG Build - Dec: ${decision.id}]`;
    console.log(`${decisionLogPrefix} Analyzing decision ${processedCount + 1}/${activeDecisions.length}: "${decision.metadata?.title}"`);

    try {
      // Generate the prompt to extract graph triples from the decision's text.
      const prompt = generateKnowledgeGraphTriplesPrompt(decision);
      
      // Call the LLM to get the structured relationships.
      const llmResult = await callLLM(prompt);

      if (llmResult && llmResult.relationships && Array.isArray(llmResult.relationships) && llmResult.relationships.length > 0) {
        // Store the extracted triples in Supabase.
        await storeKnowledgeGraphTriples(decision.id, repositorySlug, llmResult.relationships);
        console.log(`${decisionLogPrefix} Successfully extracted and stored ${llmResult.relationships.length} relationships.`);
      } else {
        console.log(`${decisionLogPrefix} No relationships were extracted by the LLM.`);
      }

    } catch (error) {
      console.error(`${decisionLogPrefix} An error occurred while processing this decision:`, error);
      // Continue to the next decision even if one fails.
    }
    processedCount++;
  }

  console.log(`${logPrefix} Knowledge graph build process completed. Total decisions processed: ${processedCount}.`);
}
// --- END ADDED FUNCTION ---

/**
 * Stores extracted knowledge graph triples into the Supabase database.
 * @param {string} decisionId - The Pinecone ID of the decision from which the triples were extracted.
 * @param {string} repositorySlug - The slug of the repository.
 * @param {Array<object>} triples - An array of relationship objects from the LLM analysis.
 *                                  Expected format: [{ subject, predicate, object }, ...]
 * @returns {Promise<void>}
 */
export async function storeKnowledgeGraphTriples(decisionId, repositorySlug, triples) {
  const logPrefix = `[KG Store - Dec: ${decisionId} Repo: ${repositorySlug}]`;

  if (!supabase) {
    console.warn(`${logPrefix} Supabase client not available. Skipping knowledge graph storage.`);
    return;
  }
  if (!triples || triples.length === 0) {
    console.log(`${logPrefix} No triples provided to store. Skipping.`);
    return;
  }

  const triplesToInsert = triples.map(triple => ({
    source_decision_id: decisionId,
    repository_slug: repositorySlug,
    subject: triple.subject,
    predicate: triple.predicate,
    object: triple.object,
  }));

  console.log(`${logPrefix} Attempting to upsert ${triplesToInsert.length} knowledge graph triples into Supabase...`);
  console.log(`${logPrefix} Data to be upserted:`, JSON.stringify(triplesToInsert, null, 2));


  try {
    // We will use upsert to avoid creating duplicate entries if the process is run multiple times.
    const { data, error } = await supabase
      .from('knowledge_graph_edges')
      .upsert(triplesToInsert, { onConflict: 'source_decision_id,subject,predicate,object' });

    if (error) {
      console.error(`${logPrefix} Error upserting triples:`, error);
      throw error;
    }

    console.log(`${logPrefix} Successfully stored/updated ${triplesToInsert.length} triples.`);

  } catch (error) {
    console.error(`${logPrefix} Unexpected error during Supabase upsert:`, error);
  }
}

/**
 * --- ADDED FUNCTION ---
 * Marks a decision as superseded in Pinecone by updating its metadata.
 * @param {string} supersededDecisionId - The Pinecone ID of the decision to be marked as superseded.
 * @param {string} supersedingDecisionId - The Pinecone ID of the decision that is superseding it.
 * @param {string} namespace - The Pinecone namespace where the decision resides.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
async function markDecisionAsSuperseded(supersededDecisionId, supersedingDecisionId, namespace) {
  const logPrefix = `[Mark Superseded - Ns: ${namespace}]`;
  console.log(`${logPrefix} Attempting to mark decision ${supersededDecisionId} as superseded by ${supersedingDecisionId}.`);

  if (!pineconeIndex) {
    console.error(`${logPrefix} Pinecone index not initialized. Cannot mark decision.`);
    return false;
  }
  if (!supersededDecisionId || !supersedingDecisionId || !namespace) {
    console.error(`${logPrefix} Missing required IDs or namespace.`);
    return false;
  }

  try {
    // 1. Fetch the existing decision to get its current vector and metadata
    console.log(`${logPrefix} Fetching vector for decision ${supersededDecisionId} to preserve its embedding.`);
    const fetchResult = await pineconeIndex.namespace(namespace).fetch([supersededDecisionId]);

    if (!fetchResult.records || !fetchResult.records[supersededDecisionId]) {
      console.error(`${logPrefix} Decision ${supersededDecisionId} not found in Pinecone. Cannot mark as superseded.`);
      return false;
    }

    const existingRecord = fetchResult.records[supersededDecisionId];
    const existingVector = existingRecord.values; // The original embedding
    const existingMetadata = existingRecord.metadata || {};

    if (!existingVector) {
      // This case should be rare if decisions are always stored with embeddings,
      // but good to handle. We might opt to regenerate or fail.
      // For now, we'll log an error and fail, as updating metadata without the vector is tricky.
      console.error(`${logPrefix} CRITICAL: Embedding (values) not found for decision ${supersededDecisionId}. Cannot reliably update metadata without vector. Please investigate.`);
      return false;
    }

    // 2. Update metadata
    const updatedMetadata = {
      ...existingMetadata,
      is_superseded: true,
      superseded_by_decision_id: supersedingDecisionId,
      // Optionally, add a timestamp for when it was superseded
      // superseded_at: new Date().toISOString(), 
    };
    console.debug(`${logPrefix} Updated metadata for ${supersededDecisionId}:`, JSON.stringify(updatedMetadata));

    // 3. Upsert the record with the original vector and updated metadata
    const vectorToUpsert = {
      id: supersededDecisionId,
      values: existingVector, // Use the original vector
      metadata: updatedMetadata,
    };

    console.log(`${logPrefix} Upserting updated metadata for decision ${supersededDecisionId}.`);
    await pineconeIndex.namespace(namespace).upsert([vectorToUpsert]);
    console.log(`${logPrefix} Successfully marked decision ${supersededDecisionId} as superseded by ${supersedingDecisionId}.`);
    return true;

  } catch (error) {
    console.error(`${logPrefix} Error marking decision ${supersededDecisionId} as superseded:`, error);
    return false;
  }
}
// --- END ADDED FUNCTION ---

/**
 * --- ADDED FUNCTION for Design Doc Wizard ---
 * Takes a task's details and uses an LLM to draft potential user journeys.
 * This is designed to be called by an interactive UI to reduce user burden.
 * @param {import('./types/design-doc-wizard').TaskDetails} taskDetails - The user-provided task details.
 * @returns {Promise<import('./types/design-doc-wizard').UserJourney[]>} - A promise that resolves to an array of AI-drafted user journeys.
 */
export async function draftUserJourneysFromTask(taskDetails, projectConstitution) {
  const logPrefix = `[User Journey Draft - Task: "${taskDetails.title}"]`;
  console.log(`${logPrefix} Starting AI-assisted user journey drafting.`);

  if (!taskDetails || !taskDetails.title || !taskDetails.description) {
    console.error(`${logPrefix} Task details are incomplete. Cannot generate journeys.`);
    throw new Error("Incomplete task details provided.");
  }

  try {
    // 1. Generate the specialized prompt for this task
    const prompt = generateUserJourneysDraftPrompt(taskDetails, projectConstitution);
    console.log(`${logPrefix} Generated prompt for LLM.`);

    // 2. Call the LLM to get the structured journey data
    const result = await callLLM(prompt);

    // 3. Validate the response structure
    if (!result || !Array.isArray(result.user_journeys)) {
      console.error(`${logPrefix} Invalid LLM response structure. Expected 'user_journeys' array. Received:`, result);
      throw new Error("Failed to get valid user journey drafts from the AI.");
    }

    console.log(`${logPrefix} Successfully drafted ${result.user_journeys.length} user journeys.`);
    
    // 4. Return the drafted journeys
    // The UI will be responsible for setting isDraft = true and handling confirmation
    return result.user_journeys;

  } catch (error) {
    console.error(`${logPrefix} An error occurred during user journey drafting:`, error);
    // Re-throw to be handled by the calling UI/service
    throw error;
  }
}

/**
 * --- ADDED FUNCTION for Storing User Journeys ---
 * Stores or updates a batch of user journeys in a dedicated Pinecone index,
 * managing their lifecycle status (active/deprecated).
 *
 * @param {import('./types/design-doc-wizard').UserJourney[]} journeys - An array of user journeys from a finalized design doc.
 * @param {string} namespace - The Pinecone namespace for namespacing.
 * @param {string} repositorySlug - The repository slug for metadata storage.
 * @returns {Promise<void>}
 */
export async function storeOrUpdateUserJourneys(journeys, namespace, repositorySlug) {
  const logPrefix = `[User Journey Store - Ns: ${namespace}]`;
  const journeyIndexName = getEnvVar('PINECONE_JOURNEYS_INDEX_NAME', 'product-user-journeys');
  const journeyIndex = pinecone.Index(journeyIndexName);

  console.log(`${logPrefix} Storing/updating ${journeys.length} user journeys in index '${journeyIndexName}'.`);

  for (const journey of journeys) {
    const journeyLogPrefix = `${logPrefix} [Journey: "${journey.title}"]`;

    if (journey.status === 'unchanged' || journey.status === 'draft') {
      console.log(`${journeyLogPrefix} Skipping journey with status '${journey.status}'.`);
      continue;
    }

    try {
      if (journey.status === 'deprecated') {
        // --- Deprecate an existing journey ---
        console.log(`${journeyLogPrefix} Deprecating journey.`);
        // This requires fetching the vector to update metadata, similar to markDecisionAsSuperseded
        const fetchResult = await journeyIndex.namespace(namespace).fetch([journey.id]);
        const existingRecord = fetchResult.records[journey.id];

        if (!existingRecord) {
          console.warn(`${journeyLogPrefix} Cannot deprecate journey with ID ${journey.id} as it was not found in the index.`);
          continue;
        }

        const vectorToUpsert = {
          id: journey.id,
          values: existingRecord.values, // Preserve the original embedding
          metadata: {
            ...existingRecord.metadata,
            lifecycle_status: 'deprecated',
            deprecated_at: new Date().toISOString(),
          },
        };
        await journeyIndex.namespace(namespace).upsert([vectorToUpsert]);
        console.log(`${journeyLogPrefix} Successfully marked journey as deprecated.`);

      } else {
        // --- Create or Update an active journey ---
        console.log(`${journeyLogPrefix} Upserting journey with status '${journey.status}'.`);
        const textToEmbed = `${journey.title}\\n${journey.steps.join('\\n')}`;
        const embedding = await generateEmbedding(textToEmbed);

        const vectorToUpsert = {
          id: journey.id,
          values: embedding,
          metadata: {
            title: journey.title,
            userRole: journey.userRole,
            steps: JSON.stringify(journey.steps), // Store steps as a stringified JSON array
            lifecycle_status: 'active', // Mark as active
            // You would also add links to the design doc, PRs, etc. here
            repository_slug: repositorySlug,
            updated_at: new Date().toISOString(),
          },
        };

        await journeyIndex.namespace(namespace).upsert([vectorToUpsert]);
        console.log(`${journeyLogPrefix} Successfully upserted journey as active.`);
      }
    } catch (error) {
      console.error(`${journeyLogPrefix} An error occurred while processing journey:`, error);
      // Continue to the next journey
    }
  }
}

/**
 * --- ADDED FUNCTION for finding relevant journeys ---
 * Queries the user journeys index to find journeys semantically similar to the new ones.
 * @param {import('./types/design-doc-wizard').UserJourney[]} newJourneys - The new journeys being proposed.
 * @param {string} namespace - The Pinecone namespace for the repository.
 * @param {number} [k=5] - The number of similar journeys to retrieve.
 * @param {number} [minScore=0.5] - The minimum similarity score to be considered relevant.
 * @returns {Promise<import('./types/design-doc-wizard').UserJourney[]>} - A promise that resolves to an array of relevant existing journeys.
 */
export async function findRelevantJourneys(newJourneys, namespace, k = 5, minScore = 0.5) {
  const logPrefix = `[Find Relevant Journeys - Ns: ${namespace}]`;
  const journeyIndexName = getEnvVar('PINECONE_JOURNEYS_INDEX_NAME', 'product-user-journeys');
  const journeyIndex = pinecone.Index(journeyIndexName);

  if (!newJourneys || newJourneys.length === 0) {
    console.log(`${logPrefix} No new journeys provided. Skipping search.`);
    return [];
  }

  console.log(`${logPrefix} Finding up to ${k} relevant journeys for ${newJourneys.length} new journey(s).`);

  try {
    // Combine titles and steps from all new journeys to create a single query text
    const queryText = newJourneys
      .map(journey => `${journey.title}\\n${journey.steps.join('\\n')}`)
      .join('\\n\\n');

    const queryEmbedding = await generateEmbedding(queryText);

    if (!queryEmbedding) {
      console.warn(`${logPrefix} Failed to generate query embedding. Skipping search.`);
      return [];
    }

    const queryResponse = await journeyIndex.namespace(namespace).query({
      vector: queryEmbedding,
      topK: k,
      includeMetadata: true,
      filter: { 'lifecycle_status': 'active' }, // CRITICAL: Only search against active journeys
    });

    const matches = queryResponse.matches || [];
    const relevantJourneys = matches
      .filter(match => match.score >= minScore)
      .map(match => ({
        id: match.id,
        title: match.metadata.title,
        userRole: match.metadata.userRole,
        steps: JSON.parse(match.metadata.steps || '[]'),
        status: 'unchanged', // Since it's an existing journey
        isDraft: false,
        ...match.metadata, // include all metadata
      }));
    
    console.log(`${logPrefix} Found ${relevantJourneys.length} relevant journeys with score >= ${minScore}.`);
    return relevantJourneys;

  } catch (error) {
    console.error(`${logPrefix} An error occurred while finding relevant journeys:`, error);
    return []; // Return empty on error to avoid blocking the caller
  }
}

/**
 * --- ADDED FUNCTION for journey impact analysis ---
 * Orchestrates the analysis of new journeys against existing ones to provide feedback.
 * @param {import('./types/design-doc-wizard').UserJourney[]} newJourneys - The user-confirmed journeys for the new task.
 * @param {string} namespace - The Pinecone namespace for the repository.
 * @returns {Promise<object>} - A promise that resolves to the structured analysis from the LLM.
 */
export async function analyzeJourneyImpact(newJourneys, namespace) {
  const logPrefix = `[Journey Impact Analysis - Ns: ${namespace}]`;
  console.log(`${logPrefix} Starting impact analysis for ${newJourneys.length} new journey(s).`);

  try {
    // 1. Find relevant existing journeys
    const existingJourneys = await findRelevantJourneys(newJourneys, namespace);

    if (existingJourneys.length === 0) {
      console.log(`${logPrefix} No relevant existing journeys found. Returning summary.`);
      return {
        summary: "No similar existing journeys were found for comparison. Please review the proposed journeys for internal consistency.",
        clarification_points: [],
      };
    }
    
    // 2. Generate the analysis prompt
    const prompt = generateJourneyImpactAnalysisPrompt(newJourneys, existingJourneys);

    // 3. Call the LLM
    console.log(`${logPrefix} Calling LLM for journey impact analysis against ${existingJourneys.length} existing journeys.`);
    const result = await callLLM(prompt);

    // 4. Validate and return the result
    if (!result || !result.journey_impact_analysis) {
      console.error(`${logPrefix} Invalid LLM response structure. Expected 'journey_impact_analysis'. Received:`, result);
      throw new Error("Failed to get a valid journey impact analysis from the AI.");
    }

    console.log(`${logPrefix} Successfully generated journey impact analysis.`);
    return result.journey_impact_analysis;

  } catch (error) {
    console.error(`${logPrefix} An error occurred during journey impact analysis:`, error);
    // Return a graceful error structure
    return {
      summary: "An error occurred during impact analysis.",
      clarification_points: [{
        type: 'error',
        question: "Could not complete analysis due to an internal error.",
        context: error.message,
      }],
    };
  }
}

/**
 * --- ADDED FUNCTION for Task Analysis ---
 * Takes task details and verified user journeys to generate a structured analysis of requirements.
 * @param {import('./types/design-doc-wizard').TaskDetails} taskDetails - The user-provided task details.
 * @param {import('./types/design-doc-wizard').UserJourney[]} userJourneys - The user-verified journeys.
 * @returns {Promise<import('./types/design-doc-wizard').TaskAnalysis>} - A promise that resolves to the structured task analysis.
 */
export async function generateTaskAnalysis(taskDetails, userJourneys, projectConstitution) {
  const logPrefix = `[Task Analysis - Task: "${taskDetails.title}"]`;
  console.log(`${logPrefix} Starting task analysis with ${userJourneys.length} user journeys.`);

  if (!taskDetails || !taskDetails.title) {
    console.error(`${logPrefix} Task details are incomplete. Cannot generate task analysis.`);
    throw new Error("Incomplete task details provided.");
  }

  try {
    // 1. Generate the specialized prompt for this task
    const prompt = generateTaskAnalysisPrompt(
      taskDetails,
      userJourneys,
      projectConstitution
    );
    console.log(`${logPrefix} Generated prompt for LLM.`);

    // 2. Call the LLM to get the structured task analysis
    const result = await callLLM(prompt);

    // 3. Validate the response structure
    if (!result || !result.summary || !result.functional_requirements) {
      console.error(`${logPrefix} Invalid LLM response structure for task analysis. Received:`, result);
      throw new Error("Failed to get a valid task analysis from the AI.");
    }
    
    // Validate the functional requirements structure (simple array format only)
    const hasValidFunctionalRequirements = Array.isArray(result.functional_requirements);
    
    if (!hasValidFunctionalRequirements) {
      console.error(`${logPrefix} Invalid functional requirements structure. Expected simple array. Received:`, result.functional_requirements);
      throw new Error("Failed to get valid functional requirements from the AI.");
    }
    
    console.log(`${logPrefix} Successfully generated task analysis.`);
    return result;

  } catch (error) {
    console.error(`${logPrefix} An error occurred during task analysis:`, error);
    // Return a graceful error structure
    return {
      summary: "An error occurred during task analysis.",
      clarification_points: [{
        type: 'error',
        question: "Could not complete analysis due to an internal error.",
        context: error.message,
      }],
    };
  }
}

