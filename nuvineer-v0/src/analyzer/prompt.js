import { shouldSkipFileContent } from '../lib/analysisUtils.js'; // Correct relative path

/**
 * Generate a prompt for <PERSON> to extract architectural knowledge
 * @param {Object} prContext - PR context
 * @param {Array} codeChanges - Code changes extracted from diff
 * @param {Array} comments - PR comments
 * @param {Object} [deploymentConstitution] - Optional: The deployment constitution for the repository.
 * @returns {string} - Prompt for Claude
 */
export function generatePrompt(prContext, codeChanges, comments, deploymentConstitution) {
  // --- Filter Files --- 
  const allFiles = prContext?.files || [];
  const relevantFiles = [];
  const skippedFileInfos = []; // Store info about skipped files

  allFiles.forEach(file => {
      if (shouldSkipFileContent(file.filename)) {
          skippedFileInfos.push(`- ${file.filename} (${file.additions} additions, ${file.deletions} deletions) [Content Skipped]`);
      } else {
          relevantFiles.push(file);
      }
  });

  const relevantFileDetails = relevantFiles.map(file => `- ${file.filename} (${file.additions} additions, ${file.deletions} deletions)`).join('\n');
  const fileCount = allFiles.length;
  const relevantFileCount = relevantFiles.length;
  const skippedFileCount = skippedFileInfos.length;

  // Combine file details, putting relevant first
  let fileDetails = `Relevant Files Analyzed (${relevantFileCount}):\n${relevantFileDetails || 'None'}\n`;
  if (skippedFileCount > 0) {
      fileDetails += `\nFiles Skipped for Content Analysis (${skippedFileCount}):\n${skippedFileInfos.join('\n')}`;
  }
  // --- End Filter Files ---

  // Ensure comments is an array
  const validComments = Array.isArray(comments) ? comments : [];
  const commentDetails = validComments.length > 0 ? validComments.map(comment => `${comment.user?.login || 'Unknown User'}: ${comment.body}`).join('\n\n') : 'No relevant comments found.';

  // --- ADDED: Format Deployment Constitution ---
  let deploymentContextSection = '';
  if (deploymentConstitution) {
    const formattedContext = formatDeploymentConstitution(deploymentConstitution);
    if (formattedContext) {
      deploymentContextSection = `\n**🚀 DEPLOYMENT CONTEXT:**\n${formattedContext}\n`;
    }
  }
  // --- END ADDED ---

  return `You are an expert software architect extracting SIGNIFICANT ARCHITECTURAL DECISIONS from code changes. Your task is to identify **only** architectural decisions that have long-term implications for the system's structure, behavior, evolution, and quality attributes.

**DO NOT simply summarize what the changes do functionally.** Your focus MUST be on the **architectural significance** and **long-term impact**. Avoid detailing routine refactorings or minor implementation details unless they represent a shift in architectural approach.

Based on the code changes below, critically evaluate how these changes impact the system's architecture. Focus *exclusively* on identifying and explaining:

1.  **STRUCTURAL IMPACTS:** How the changes modify components, services, layers, or their relationships in a non-trivial way. (e.g., introducing a new service, changing communication patterns between layers).
2.  **ARCHITECTURAL PATTERNS:** What significant patterns (e.g., CQRS, Event Sourcing, Microservices, Repository, Strategy) are being introduced, modified, or potentially violated?
3.  **QUALITY ATTRIBUTES:** How the changes materially affect key quality attributes like scalability, maintainability, security, performance, reliability, testability? (Be specific - *how* is scalability improved/hindered?).
4.  **TECHNICAL CONSTRAINTS:** What new architectural constraints are introduced or removed? (e.g., requiring use of a specific message queue, enforcing a specific data validation library).
5.  **CROSS-CUTTING CONCERNS:** How the changes significantly alter system-wide aspects like logging, authentication, monitoring, configuration management, etc.?
6.  **TECHNICAL DEBT:** Does the change introduce significant architectural debt (e.g., shortcuts impacting maintainability), or pay down existing structural debt?
7.  **FUTURE FLEXIBILITY:** How do the changes enable or materially hinder future architectural evolution or adoption of new technologies/approaches?
8.  **RATIONALE (Why this path?):** Explain the apparent reasons for choosing this specific architectural approach *now*, based *directly* on the PR context, code changes, and stated goals. Focus on the concrete advantages, constraints, or requirements addressed by this choice. Avoid speculation about underlying intent. (Detailed comparison with alternatives will be handled separately).

**PR Information:**
Title: ${prContext.title}
Description: ${prContext.body || 'No description provided'}
URL: ${prContext.html_url}

${deploymentContextSection}

**Files Changed (${fileCount}):**
${fileDetails}

**Code Changes (from relevant files):**
${formatCodeChanges(codeChanges.filter(change => !shouldSkipFileContent(change.filename)))}

**Key Comments:**
${commentDetails}

---
**CRITICAL REMINDER: Do NOT merely describe the features added or bugs fixed in this PR. Focus exclusively on the *architectural* choices and their long-term consequences.**
**Note:** Content/diffs for documentation, configuration, build files, lock files, and image files have been omitted from the 'Code Changes' section to focus on potential architectural impacts (unless it was package.json).
---
**Output Format:**

**General Writing Style:** For all descriptive fields ('title', 'description', 'rationale', 'implications', 'no_architecture_impact_reason'), write clear, concise, and high-signal content suitable for our knowledge base (used by humans of all levels and AI agents). Prioritize conveying the essential architectural insights without lengthy prose/excessive jargon/long sentences. Maintain analytical depth but use accessible language and minimal words/bullet points. Output *must* be easily understandable and parseable.

Format your response as a JSON object adhering strictly to this structure:
{
  "architectural_decisions": [
    {
      "title": "Concise, architecture-focused title capturing the core decision (e.g., 'Introduce Command Pattern for User Actions', 'Decouple Billing Service via Event Bus', 'Adopt Repository Pattern for Data Access')",
      "description": "Detailed analysis of the architectural change itself, focusing ONLY on structure, patterns, and principles. Avoid implementation details.",
      "rationale": "State the observable architectural reasoning for this path based on the PR context (code, description, comments). Explain the specific requirements, constraints, or goals addressed. Avoid speculative language like 'likely' or 'suggests'; focus on evidence present in the context.",
      "implications": "Analysis of long-term architectural impact: effects on specific quality attributes (scalability, maintainability, etc.), technical debt (introduced/reduced), and future flexibility (opportunities created/constraints imposed).",
      "confidence_score": "float (0.0 to 1.0) - Your confidence that this specific item truly represents a *significant* architectural decision based on the criteria provided. 1.0 = High confidence, 0.0 = Low confidence.",
      "related_files": [
        "src/path/to/relevant/file1.js",
        "src/path/to/another/file.ts"
      ],
     "domain_concepts": [
      "Category",
      "Subcategory", 
      "Pattern",
      "Specific"
      ], // Hierarchical array (max 4 levels) from broad architectural categories to specific implementation patterns (e.g., ['UI Patterns', 'Navigation', 'Space-Constrained Display', 'Hierarchical Data'] or ['Data Patterns', 'State Management', 'SWR Integration']). Enables concept drill-down from high-level to specific.
      "dev_prompt": "Concise, action-oriented guidance for developers working in this area (e.g., 'Creation flows: auto-navigate to newly created resource', 'Deep hierarchies: use ellipsis dropdown for space constraints'). Focus on what to do, not what was done.",
      "is_extension": "boolean - Set to true if this decision primarily extends or applies a previously established architectural pattern or principle to a new scope/context. Set to false if it introduces a fundamentally new pattern or approach.",
      "follows_standard_practice": "boolean - Set to true if this decision is implementing a well-established industry standard practice, design pattern, or architectural approach that is widely recognized as a best practice. For these decisions, alternatives analysis may be unnecessary as the approach is already validated by industry consensus.",
      "follows_standard_practice_reason": "string - **ONLY include this field IF \`follows_standard_practice\` is true.** Pattern name and key implementing file (e.g., 'Breadcrumb navigation with overflow: components/datarooms/dataroom-breadcrumb.tsx', 'SWR data fetching: components/visitors/dataroom-visitor-custom-fields.tsx'). Keep concise.",
      "data_model_changes": [
        "string - OPTIONAL. Include ONLY IF the PR introduces significant data model/schema changes. Describe each change concisely. Format for entities: 'entity_name (field1:type, field2:type, ...) - created | modified | deleted'. Format for enums: 'enum_name (enum: VALUE1, VALUE2) - created | modified | deleted'. Ensure 100% coverage. Examples: 'Users (email:string, preferences:json) - modified', 'NewTable (id:uuid, name:string) - created', 'OrderStatus (enum: PENDING, SHIPPED, DELIVERED) - created'."
      ], // This field is OPTIONAL. Include it ONLY IF the PR introduces significant data model/schema changes.
      "risks": [
        {
          "category": "ux|security|performance|maintenance|scalability|reliability|testability",
          "description": "Detailed description of the risk",
          "severity": "low|medium|high",
          "mitigation": "Actionable steps to address the risk. Provide a specific starting point or separate short-term/long-term actions (e.g., 'Short-term: Add specific monitoring for X. Long-term: Refactor Y to use Z pattern', or 'Implement stricter input validation on endpoint A'). Avoid generic advice and keep concise."
        }
      ]
    }
  ],
  "no_architecture_impact_reason": "string (max 5 words) - **ONLY if 'architectural_decisions' is empty.** Provide a concise, specific reason for no architectural impact (e.g., 'Minor dependency version update').",
  "no_decision_confidence_score": "float (0.0 to 1.0) - Your confidence that there are *no* significant architectural decisions in this PR. Provide this *only* if 'architectural_decisions' is empty. 1.0 = High confidence no decisions exist, 0.0 = Low confidence (suggests uncertainty)."
}

**Important Considerations:**

*   **Data Model Changes:** If the PR involves creating new database tables, collections, enums, or significantly altering existing ones (e.g., adding multiple indexed fields, changing relationships, adding/removing enum values), detail these in the \`data_model_changes\` array for the relevant architectural decision. Each string in the array should concisely describe one change. 
    *   For entities/tables: Use format \`"entity_name (field1:type, field2:type, ...) - created | modified | deleted"\`. Example: \`"Users (email:string, new_preference_field:jsonb, age:int) - modified"\` or \`"AuditLogs (id:uuid, action:text, user_id:uuid, timestamp:timestamptz) - created"\`.
    *   For enums: Use format \`"enum_name (enum: VALUE1, VALUE2, VALUE3) - created | modified | deleted"\`. Example: \`"OrderStatus (enum: PENDING, PROCESSING, SHIPPED, DELIVERED) - created"\` or \`"UserRole (enum: ADMIN, EDITOR, VIEWER) - modified"\`.
    *   Focus on architecturally significant schema changes. Ensure all significant data model modifications in the PR are listed to provide a complete picture.
*   **Confidence Score:** Assign the \`confidence_score\` based on how well the change aligns with the definition of a *significant* architectural decision (impact on structure, patterns, quality attributes, constraints, etc.). Routine changes or minor fixes should have low confidence, while major structural changes should have high confidence. Use the \`no_decision_confidence_score\` to express certainty when *no* decisions are found.
*   **Significance Threshold:** Be highly selective. Only include decisions that fundamentally alter structure, introduce/change major patterns, significantly impact quality attributes, or establish new system-wide constraints/conventions. Exclude routine updates, minor refactors confined to a single component, dependency bumps, or simple feature additions without broader architectural implications.
*   **Standard Practice Flag & Reason:** When marking a decision as \`follows_standard_practice\`, be highly confident that it represents a well-established industry norm (e.g., using JWT for authentication, implementing the Repository pattern for data access, using immutable data patterns). This should be based on clear evidence in the code or context that the approach follows widely-accepted best practices. **If you set \`follows_standard_practice\` to true, you MUST provide a concise pattern name and key file in the \`follows_standard_practice_reason\` field.**
*   **Rationale Quality:** Focus on the observable 'why' behind *this* decision based *only* on the available context (code, description, comments). State the connection between the change and the apparent goals or requirements directly.
*   **JSON Strictness:** Adhere precisely to the requested JSON format.
*   **Pattern Extensions:** If a change *extends* an existing architectural pattern to a new area, it *may* be significant if it broadens the pattern's application domain considerably or impacts critical quality attributes. Evaluate its significance based on the impact, not just the novelty. Clearly indicate if it's an extension using the 'is_extension' flag. Routine applications of established patterns are generally not significant unless they meet the broader impact criteria.
*   **Risk Identification:** Always explicitly identify risks in the structured "risks" array. Pay special attention to:
    *   **Vendor Lock-in:** When a decision introduces or deepens dependency on specific vendor services, explicitly add this as a "maintenance" category risk with appropriate severity.
    *   **Technology Lock-in:** Similar to vendor lock-in, but for specific technologies or frameworks that may limit future flexibility.
    *   **UX Risks:** When architectural decisions might negatively impact user experience (e.g., disabled but visible UI elements).
    *   Each risk must include a category, description, severity, and potential mitigation strategy. Mitigation should be specific and actionable (e.g., detail *what* to monitor, *which* component to refactor, or *what kind* of validation to add), not generic statements.
*   **Domain Concepts Hierarchy:** Structure domain concepts as a hierarchical array (max 4 levels) progressing from broad architectural categories to specific implementation details. Examples: ['UI Patterns', 'Navigation', 'Breadcrumb Management'] or ['Data Patterns', 'State Management', 'SWR Integration']. This enables conceptual drill-down from high-level architecture to specific patterns.
*   **Dev Prompt Format:** Keep dev prompts action-oriented and minimal. Focus on what developers should do when working in related areas, not explanations of what was implemented. Use format: "Context: specific action" (e.g., 'Creation flows: auto-navigate to newly created resource').
If no significant architectural decisions are identified in the provided diff based on the criteria above, return an empty \`architectural_decisions\` array and provide a clear, specific justification in the \`no_architecture_impact_reason\` field (max 5 words).`;
}

/**
 * Format code changes for the prompt
 * @param {Array} codeChanges - Code changes from PR files (expected to be objects with filename, additions, deletions, patch properties)
 * @returns {string} - Formatted code changes
 */
function formatCodeChanges(codeChanges) {
  if (!codeChanges || codeChanges.length === 0) {
    // Provide a clearer message if the input is empty or invalid
    return 'No code changes provided or format is invalid.';
  }

  // Limit the number of files and patch size to avoid excessive prompt length
  const MAX_FILES_TO_SHOW = 5;
  const MAX_PATCH_LINES = 50; // Limit lines per patch

  const relevantChanges = codeChanges.slice(0, MAX_FILES_TO_SHOW);
  let result = 'Key code changes:\\n\\n';

  for (const file of relevantChanges) {
    // Defensive check for filename
    result += `File: ${file.filename || 'Unknown File'}\\n`;

    if (file.patch) {
      // Truncate patch if it's too long
      const patchLines = file.patch.split('\\n');
      const truncatedPatch = patchLines.slice(0, MAX_PATCH_LINES).join('\\n');
      result += 'Diff (potentially truncated):\\n```diff\\n';
      result += truncatedPatch;
      if (patchLines.length > MAX_PATCH_LINES) {
        result += '\\n... (patch truncated)';
      }
      result += '\\n```\\n';
    } else {
      result += '(No diff available for this file)\\n'; // Indicate if patch is missing
    }
    result += '\\n';
  }

  if (codeChanges.length > relevantChanges.length) {
    result += `...and ${codeChanges.length - relevantChanges.length} more file(s) with changes.\\n`;
  }

  return result;
}

/**
 * Generates a prompt for analyzing alternatives to a given architectural decision,
 * using context retrieved from a RAG store.
 *
 * @param {object} decision - The architectural decision object extracted from the first stage.
 *                            Expected format: { title, description, rationale, implications, related_files }
 * @param {string} retrievedRagContext - Relevant architectural context (e.g., summaries of past related decisions) retrieved from the RAG store. Can be empty/null if no context found.
 * @returns {string} - The prompt for the LLM.
 */
export function generateAlternativesPrompt(decision, retrievedRagContext) {
  // Basic validation
  if (!decision || !decision.title || !decision.description || !decision.rationale) {
    // Consider throwing an error or returning a more descriptive error string
    console.error('Error: Invalid decision object provided for alternatives analysis:', decision);
    return 'Error: Invalid decision object provided for alternatives analysis.';
  }

  // Ensure related_files is an array before joining
  const relatedFilesString = Array.isArray(decision.related_files)
    ? decision.related_files.join(', ')
    : 'N/A';

  const decisionDetails = `
**Architectural Decision Under Analysis:**
*   **Title:** ${decision.title}
*   **Description:** ${decision.description}
*   **Original Rationale:** ${decision.rationale}
*   **Identified Implications:** ${decision.implications || 'N/A'}
*   **Related Files:** ${relatedFilesString}
  `;

  const ragContextBlock = retrievedRagContext && retrievedRagContext.trim() !== ''
    ? `
**Relevant Past Architectural Context (from RAG):**
---
${retrievedRagContext}
---
    `
    : `
**Relevant Past Architectural Context (from RAG):**
---
No relevant context found or provided.
---
    `;

  // Using template literals for the main prompt string
  return `You are an expert software architect performing a critical analysis of architectural alternatives. You have been provided with a specific architectural decision that was recently implemented and relevant historical context retrieved from our architectural knowledge base (RAG).

Your task is to:
1.  Thoroughly understand the provided architectural decision and its original rationale.
2.  Analyze the relevant past architectural context (if provided).
3.  Brainstorm and evaluate distinct **architectural alternatives** that could have addressed the same underlying problem or achieved similar goals.
4.  Perform a detailed **trade-off analysis** comparing each alternative against the **originally chosen approach**.

${decisionDetails}

${ragContextBlock}

**Instructions for Analysis:**

1.  **Identify Alternatives:** Generate at least 2-3 **distinct architectural alternatives**. These should represent fundamentally different approaches (e.g., different patterns, different component structures, alternative technologies/protocols), not just minor implementation variations.
2.  **Describe Alternatives:** Clearly describe each alternative approach.
3.  **Analyze Trade-offs (Pros/Cons):** For **each** alternative, critically evaluate its potential **advantages (Pros)** and **disadvantages (Cons)** specifically *in comparison to the chosen approach* detailed above. Consider factors like:
    *   Impact on quality attributes (scalability, maintainability, performance, security, reliability, testability, etc.)
    *   Development complexity and cost
    *   Operational overhead
    *   Alignment with existing architecture (use RAG context if available)
    *   Future flexibility and extensibility
    *   Team skills and familiarity
4.  **Contextual Relevance:** Briefly comment on how each alternative aligns with or deviates from the provided RAG context (past decisions). Would it have been a natural evolution, a significant shift, or potentially conflicting?
5.  **Refined Rationale Summary (Optional but Recommended):** Based on your analysis of alternatives, provide a brief summary refining the understanding of *why* the original decision was likely made, potentially highlighting the key trade-offs more explicitly, or noting if an alternative seems strong in hindsight.

**Output Format:**

**General Writing Style:** For all descriptive fields in the output ('title', 'description', 'pros_compared_to_chosen', 'cons_compared_to_chosen', 'contextual_alignment', and 'refined_tradeoff_summary'), adhere to the principles of clear, concise, high-signal content suitable for our knowledge base (used by humans of all levels and AI agents). Prioritize essential insights, maintain analytical depth, use accessible language, and avoid unnecessary jargon. Ensure the output is easily understandable and parseable. The specific instructions for 'refined_tradeoff_summary' further elaborate on these principles for that field.

Format your response as a JSON object adhering strictly to this structure:
{
  "alternatives_analysis": {
    "analyzed_decision_title": "${decision.title}", // Include the title for reference
    "alternatives": [
      {
        "title": "Concise title for Alternative 1 (e.g., 'Utilize Serverless Functions instead of Long-Running Service')",
        "description": "Clear description of this alternative architectural approach.",
        "pros_compared_to_chosen": [
          "Specific advantage 1 (e.g., 'Reduced operational cost for low-traffic periods')",
          "Specific advantage 2 (e.g., 'Automatic scaling handled by cloud provider')"
          // List specific advantages
        ],
        "cons_compared_to_chosen": [
          "Specific disadvantage 1 (e.g., 'Potential for cold starts impacting latency')",
          "Specific disadvantage 2 (e.g., 'Increased complexity in state management across functions')"
          // List specific disadvantages
        ],
        "contextual_alignment": "Brief analysis of how this alternative aligns/conflicts with past decisions mentioned in RAG context (e.g., 'Aligns with existing use of serverless for background tasks', 'Conflicts with preference for stateful services in core domain')."
      },
      {
        "title": "Concise title for Alternative 2",
        "description": "Description of Alternative 2.",
        "pros_compared_to_chosen": [ /* ... */ ],
        "cons_compared_to_chosen": [ /* ... */ ],
        "contextual_alignment": "Alignment analysis..."
      }
      // Add more alternative objects as identified (minimum 2-3 recommended)
    ],
    "refined_tradeoff_summary": "Optional: Generate a concise summary for our knowledge base, intended for both human team members (all levels) and AI agents. Prioritize **high signal context over lengthy sentences** to improve cognitive load and clarity. **Translate the deep technical trade-offs into clear, accessible language**, focusing on the most critical factors (key benefits/drawbacks) that influenced the chosen path vs. alternatives. **Maintain the analytical depth** but express it concisely. Briefly list any significant risks or open questions in a direct, easy-to-parse format (e.g., bullet points)."
  }
}

Focus on providing deep, insightful architectural comparisons, not superficial commentary. Ensure the trade-offs are specific and directly compare the alternative to the decision that was actually implemented. Leverage the provided RAG context to inform your analysis of alignment and potential historical influences.`;
}

/**
 * Generates a prompt for alignment analysis against historical context,
 * including an assessment of deviation quality.
 * @param {object} candidateDecision - The decision extracted from the current PR.
 * @param {string} historicalContext - Formatted string of relevant historical decisions from RAG.
 * @returns {string} - The prompt for the LLM.
 */
export function generateAlignmentPrompt(candidateDecision, historicalContext) {
    if (!candidateDecision || !candidateDecision.title) {
        console.error("Error: Invalid candidate decision object for alignment prompt.");
        return "Error: Invalid candidate decision."; // Or throw
    }

    // Ensure description and rationale are strings, defaulting to 'N/A' if null/undefined
    const description = candidateDecision.description || 'N/A';
    const rationale = candidateDecision.rationale || 'N/A';

    const decisionDetails = `
**Candidate Decision:**
*   **Title:** ${candidateDecision.title}
*   **Description:** ${description}
*   **Rationale:** ${rationale}
    `;

    const historyBlock = historicalContext && historicalContext.trim() !== ''
        ? `
**Historical Context (from RAG):**
---
${historicalContext}
---
    ` : `
**Historical Context (from RAG):**
---
No relevant historical context found or provided.
---
    `;

    // Updated prompt for alignment check including deviation justification
    return `You are an AI assistant analyzing how a new technical decision relates to past decisions.
${decisionDetails}
${historyBlock}

**Analysis Tasks:**

1.  **Assess Alignment:** Based *only* on the provided Historical Context, classify the Candidate Decision's relationship to past approaches:
    *   \`aligned\`: Generally follows patterns or choices seen in the history.
    *   \`deviating\`: Introduces a new pattern, contradicts history, or is significantly different.
    *   \`unclear\`: History is insufficient or irrelevant to make a judgment.

2.  **Explain Reasoning:** Briefly explain your alignment assessment (1-2 sentences), referencing specific historical decision titles if relevant. For 'unclear', explain why.

3.  **Analyze Deviation (If Applicable):** If the status is \`deviating\`, perform the following:
    *   **Describe Deviation:** Briefly explain *how* the Candidate Decision deviates from the historical context.
    *   **Assess Justification:** Based *only* on the Candidate Decision's Description and Rationale provided above, assess whether the deviation seems justified. Consider if the rationale explains *why* this different approach is needed now (e.g., addresses new requirements, leverages new tech, improves on past limitations). Classify the justification as:
        *   \`justified\`: The rationale suggests a deliberate, positive evolution or addresses shortcomings of past approaches. (Potentially superior)
        *   \`unjustified\`: The rationale is weak, missing, or doesn't adequately explain the need for deviation from historical patterns. (Potentially inferior)
        *   \`unclear\`: Insufficient information in the rationale/description to assess justification.

**Output Format:** Respond *only* with a JSON object adhering strictly to this structure. Do NOT include any explanatory text outside the JSON.
{
  "alignment_status": "aligned | deviating | unclear",
  "explanation": "Your reasoning for the alignment status (1-2 sentences).",
  "most_relevant_historical_decision_title": "Title of the most relevant historical decision cited, or null.",
  "deviation_analysis": { // Include this object ONLY if alignment_status is 'deviating'
    "description": "How the decision deviates from history.",
    "justification_assessment": "justified | unjustified | unclear",
    "justification_explanation": "Brief reasoning for the justification assessment based *only* on the candidate decision's provided rationale/description."
  }
}

Provide *only* the JSON object in your response.`;
}

/**
 * Generates a prompt to analyze the relationship between a new architectural decision
 * and potentially related existing decisions retrieved via RAG.
 *
 * @param {object} newDecision - The new architectural decision object (with metadata and Pinecone ID). Expected: { id, title, description, rationale, implications, ... }
 * @param {Array<object>} existingDecisions - An array of existing decision objects retrieved from RAG (including metadata and Pinecone ID). Expected: [{ id, metadata: { title, description, rationale, ... } }, ...]
 * @returns {string} - The prompt for the LLM.
 */
export function generateRelationshipAnalysisPrompt(newDecision, existingDecisions) {
  if (!newDecision || !existingDecisions || existingDecisions.length === 0) {
    console.warn("[Relationship Prompt] Missing new decision or existing decisions for analysis.");
    return null; // Or a default message indicating insufficient input
  }

  const newDecisionDetails = `
**New Architectural Decision (Under Analysis):**
- **ID:** ${newDecision.id || newDecision.pinecone_id || 'N/A'}
- **Title:** ${newDecision.title}
- **Description:** ${newDecision.description ? newDecision.description.substring(0, 500) + (newDecision.description.length > 500 ? '...' : '') : 'N/A'}
- **Related Files:** ${newDecision.related_files ? newDecision.related_files.join(', ') : 'N/A'}
- **Domain Concepts:** ${newDecision.domain_concepts ? newDecision.domain_concepts.join(', ') : 'N/A'}
  `;

  const existingDecisionsContext = existingDecisions.map((d, index) => `
ID: ${d.id || d.metadata?.pinecone_id || 'N/A'}
Title: ${d.metadata?.title || 'N/A'}
Related Files: ${Array.isArray(d.metadata?.related_files) ? d.metadata.related_files.join(', ') : (d.metadata?.related_files || 'N/A')}
Domain Concepts: ${Array.isArray(d.metadata?.domain_concepts) ? d.metadata.domain_concepts.join(', ') : (d.metadata?.domain_concepts || 'N/A')}
PR Number: ${d.metadata?.pr_number || 'N/A'}
PR Merged At: ${d.metadata?.pr_merged_at ? new Date(d.metadata.pr_merged_at).toISOString().split('T')[0] : 'N/A'} 
Implications (Summary): ${d.metadata?.implications ? d.metadata.implications.substring(0,300) + (d.metadata.implications.length > 300 ? '...' : '') : 'N/A'}
  `).join('\\n');

  return `You are an expert software architect specializing in identifying how new architectural decisions relate to existing ones, with a strong focus on detecting when a new decision **supersedes** (i.e., replaces or makes obsolete) an older one.

**Task:**

Analyze the provided "New Architectural Decision" and determine if it **SUPERSEDES** any of the "Potential Existing Decisions" listed.

${newDecisionDetails}

**Potential Existing Decisions (from knowledge base):**
${existingDecisionsContext}

**Analysis Instructions:**

1.  **Primary Goal: Identify Supersession.** For each "Potential Existing Decision", carefully evaluate if the "New Architectural Decision" renders it obsolete, invalidates its core principles, or provides a clearly superior and replacing approach to the same problem or architectural aspect.
2.  **High Confidence for Supersedes:** Only flag a relationship as 'supersedes' if you are highly confident (e.g., > 0.85 confidence) that the new decision is intended to, and effectively does, replace the old one. Consider factors like:
    *   Does the new decision address the exact same problem or component in a fundamentally different or updated way?
    *   Does the rationale of the new decision implicitly or explicitly state or imply that an older approach is no longer valid or preferred?
    *   Do the implications of the new decision make the old decision's implications irrelevant or incorrect?
    *   Is there significant overlap in related files or domain concepts, where the new decision introduces a clear evolution or replacement?
3.  **Justification for Supersedes:** If you determine a 'supersedes' relationship, provide a concise justification (1-2 sentences) explaining *why* the new decision supersedes the existing one, referencing specific aspects of both decisions.
4.  **No Other Relationship Types:** For this task, **DO NOT** identify other relationship types like 'amends', 'conflicts_with', 'related_to', etc. If the new decision does not clearly supersede an existing one, classify it as 'independent' relative to that specific existing decision but do not include in output.
5.  **Independence:** If the "New Architectural Decision" is distinct and does not supersede a "Potential Existing Decision", mark its relationship_type as 'independent' but do not include in output.

**Output Format:**

Provide your analysis as a JSON object with the following structure:
{
  "relationship_analysis": {
    "new_decision_id": "${newDecision.id || newDecision.pinecone_id || 'NEW_DECISION_ID_PLACEHOLDER'}",
    "relationships": [
      {
        "existing_decision_id": "ID_OF_EXISTING_DECISION_1",
        "relationship_type": "supersedes", // STRICTLY 'supersedes' (otherwise do not include in output)
        "confidence_score": "float (0.0 to 1.0) - Your confidence in this specific relationship assessment. For 'supersedes', this score should be high (e.g., >0.85). For 'independent', it can reflect certainty of independence.",
        "justification": "5 words at the max" (REQUIRED if relationship_type is 'supersedes'"
      }
      // ... more relationship objects for each existing decision analyzed
    ]
  }
}

**Important Considerations:**

*   **Accuracy of 'supersedes':** It is critical that 'supersedes' relationships are identified accurately. Erroneously marking a decision as superseded can hide valuable current information
*   **Focus:** Your sole focus is to identify clear supersession. Do not infer other relationships.
*   **Concise Justifications:** Keep justifications for 'supersedes' brief and to the point, highlighting the core reason for replacement.
*   **JSON Strictness:** Adhere precisely to the requested JSON format. Ensure 'relationship_type' is ONLY "supersedes" or "independent".
`;
}

// Define thresholds (these need tuning based on your project/context)
const HIGH_IMPACT_LINE_THRESHOLD = 75; // e.g., Extension affecting >75 lines might be significant
const TRIVIAL_LINE_THRESHOLD = 10;     // e.g., Extension affecting <=10 lines likely isn't worth deep analysis

/**
 * Generates a prompt for providing early, formative architectural feedback
 * on staged code changes or a design document, focusing on guidance and Socratic questioning.
 * @param {Array|string} inputContent - Staged code changes (Array) or design document text (string).
 * @param {'code'|'design_doc'} contentType - Type of input content.
 * @param {Object} prContext - Partial context (e.g., branch name, draft title/description).
 * @param {string} [existingContext=''] - Optional: String summary of relevant existing patterns/ADRs.
 * @returns {string} - Prompt for the LLM.
 */
export function generateDryRunFeedbackPrompt(inputContent, contentType, prContext, existingContext = '') {
  let formattedContentSection;
  let contextPreamble;
  const inputTypeDisplay = contentType === 'design_doc' ? 'design document' : 'code changes';
  let llmTaskInstructions = '';
  let exampleFeedback = ''; // To provide a more tailored example

  if (contentType === 'design_doc') {
    const designDocText = typeof inputContent === 'string' ? inputContent : JSON.stringify(inputContent);
    formattedContentSection = `**Design Document Under Review:**\\\\n---\\\\n${designDocText}\\\\n---`;
    contextPreamble = `**Context for Design Document Review:**\nTitle: ${prContext?.title || 'N/A (No title provided for design)'}\\nAssociated Task/Branch: ${prContext?.ref || prContext?.branch || 'N/A'}\\n`;
    
    // Check if existingContext is in the legacy ID-only format
    if (existingContext && existingContext.trim() !== '' && existingContext.includes('Previously seen Decision ID:')) {
        contextPreamble += `\\n**Relevant Existing Patterns/Decisions (from knowledge base):**\\n---\\n${existingContext}\\n---\\n`;
    } else if (existingContext && existingContext.trim() !== '') {
        // Parse the JSON or use the existingContext directly if it's already a string
        try {
            // If it's a stringified array of decisions, parse it first
            const decisions = typeof existingContext === 'string' && existingContext.startsWith('[') 
                ? JSON.parse(existingContext) 
                : null;
            
            if (Array.isArray(decisions)) {
                // Format decisions properly
                const formattedDecisions = decisions.map(decision => {
                    let formatted = `### ${decision.title || 'Untitled Decision'}\n`;
                    if (decision.implications && decision.implications !== 'N/A') {
                        formatted += `**Implications:** ${decision.implications}\n\n`;
                    }
                    if (decision.rationale && decision.rationale !== 'N/A') {
                        formatted += `**Rationale:** ${decision.rationale}\n\n`;
                    }
                    if (decision.dev_prompt && decision.dev_prompt !== 'N/A') {
                        formatted += `**Developer Guidance:** ${decision.dev_prompt}\n\n`;
                    }
                    return formatted;
                }).join('\n');
                
                contextPreamble += `\\n**Relevant Existing Patterns/Decisions (from knowledge base):**\\n---\\n${formattedDecisions}\\n---\\n`;
            } else {
                // Fallback to using the string directly
                contextPreamble += `\\n**Relevant Existing Patterns/Decisions (from knowledge base):**\\n---\\n${existingContext}\\n---\\n`;
            }
        } catch (e) {
            // If any error occurs, just use the string directly
            contextPreamble += `\\n**Relevant Existing Patterns/Decisions (from knowledge base):**\\n---\\n${existingContext}\\n---\\n`;
        }
    } else {
        contextPreamble += `\\n**Relevant Existing Patterns/Decisions (from knowledge base):**\\n---\\nNo specific existing context was automatically retrieved for this review. Please consider general best practices and any known project-specific patterns.\\n---\\n`;
    }

    llmTaskInstructions = `\\nYour primary goal is to act as a smart experienced technical lead reviewer who is Socratic towards their team. Provide **concise, thought-provoking questions and observations** that challenge assumptions, encourage deeper reflection, and help the author refine their design for architectural soundness and alignment with long-term technical strategy but be pragmatic. Prioritize actionable insights, short term / long term paths over lengthy explanations. Refer to the **Relevant Existing Patterns/Decisions** (if provided) to frame your questions and highlight potential alignments or divergences.\\n\\nFocus on questioning and probing the following areas of the design document:\\n\\n1.  **🎯 Problem-Solution Fit & Clarity:**\\n    *   How effectively does the design articulate the core problem? Is the proposed solution demonstrably the best fit, or are there unstated assumptions or alternative problem framings to consider?\\n    *   How clear and unambiguous is the document? Are diagrams, assumptions, and open questions sufficiently detailed to avoid misinterpretation? What key information might be missing for a comprehensive understanding?\\n\\n2.  **🏛️ Architectural Soundness & Principles:**\\n    *   **Component Design:** How clear are the responsibilities of each component? Does the proposed separation of concerns truly minimize coupling and maximize cohesion, or could alternative groupings be more effective? \\n    *   **Interactions & Dependencies:** What are the potential failure points or bottlenecks in component interactions? Are dependencies well-managed and justified, or do they introduce unnecessary complexity or risk? How does this compare to interaction patterns in our **Relevant Existing Patterns/Decisions**?\\n    *   **Patterns & Anti-Patterns:** Which architectural patterns are being leveraged, and are they applied appropriately for this context? Are there any subtle signs of emerging anti-patterns that need to be questioned?\\n\\n3.  **📊 Non-Functional Requirements (NFRs):**\\n    *   **Scalability & Performance:** What specific scalability targets does the design aim for, and how does it propose to meet them? What are the primary performance assumptions, and what if those assumptions are incorrect? Are there potential bottlenecks not explicitly addressed?\\n    *   **Reliability & Resilience:** Beyond stated failure modes, what other potential failures could occur? How robust are the proposed resilience strategies (e.g., retries, fallbacks, fault isolation)? Could insights from **Relevant Existing Patterns/Decisions** on resilience be applied here?\\n    *   **Security:** What are the primary security threats this design needs to defend against? Are the proposed security measures comprehensive and integrated from the start, or are there potential gaps (e.g., specific OWASP Top 10 risks)?\\n    *   **Maintainability & Testability:** How might this design impact long-term maintainability? What aspects could become complex to modify or extend? How does the design facilitate effective and efficient testing at various levels?\\n\\n4.  **💾 Data Modeling & API Design (if applicable):**\\n    *   Do the data models clearly and accurately represent the domain entities and their relationships? Are there alternative modeling choices that might offer better flexibility or performance? \\n    *   How intuitive and robust are the API contracts? Do they consider versioning, comprehensive error handling, and security best practices effectively? What questions would a new consumer of this API likely have?\\n\\n5.  **🔄 Alignment with Existing Context & Past Decisions:**\\n    *   **Leverage Provided Context:** For each key aspect of the new design, how does it align with or diverge from the **Relevant Existing Patterns/Decisions**? Explicitly use the provided context to formulate your questions.\\n    *   **Consistency & Justification:** If the design deviates from established patterns (per the context), what is the stated rationale? Is this rationale compelling enough to justify the deviation, or does it warrant further discussion? For instance, if a past decision advocated for X, and this proposes Y, why is Y superior in this specific scenario?\\n    *   **Learning & Evolution:** Does the design demonstrate learning from past efforts (as suggested by the context), or does it risk repeating previous missteps? How does it position the system for future evolution in light of existing architectural trajectory?\\n\\n6.  **💡 Alternatives & Trade-offs:**\\n    *   If alternatives are discussed, how rigorous is the trade-off analysis, especially when viewed against the **Relevant Existing Patterns/Decisions**? Are key criteria missing from the evaluation?\\n    *   If alternatives are not discussed, what significant alternatives (e.g., those suggested by common best practices or successful past decisions in the context) should the author be prompted to consider and evaluate? Frame these as questions about why the proposed path was chosen over these other potential routes.\\n\\n**Feedback Categories & Output Order (Strictly Adhere):**\\n\nProvide your feedback in Markdown. Within the \\\'🔴 Critical\\\' and \\\'🟡 Key\\\' sections, ensure most points are framed as precise questions or lead to critical inquiries that stimulate further thought and discussion while being pragmatic. Avoid making definitive statements where a guiding question would be more effective. Ensure your output strictly follows the prescribed Markdown format and the section order: Critical, Key, Clarification, Positive. Use the exact headings provided.\\n\\n*   **🔴 Critical Architectural Concerns & Questions:** Fundamental design issues, major NFR risks, significant misalignment with context without clear and compelling justification. These are points that likely need resolution before proceeding.\\n*   **🟡 Key Design Considerations & Probing Questions:** Significant design choices to explore further, areas needing more detail or justification, potential improvements to robustness or NFRs, or deeper questions about alignment with existing context.\\n*   **🔵 Points for Clarification & Minor Enhancements:** Ambiguities in the document, minor suggestions for improving clarity, consistency, or completeness. These are typically less structural.\\n*   **🟢 Positive Aspects:** Briefly acknowledge well-thought-out parts of the design, effective use of patterns, or clear alignment with beneficial existing context. Keep this section concise.\\n`;

    exampleFeedback = `\\nHi there! I\\\'ve reviewed your design document for \\\'[Feature Name]\\\'**🔴 Critical Architectural Concerns & Questions**\\n*   **Scalability of \\\'XYZ\\\' Component:** The document proposes a single-instance \\\'XYZ\\\' component. Given our NFR for 1000 TPS and learnings from scaling the \\\'ABC\\\' service (Decision: XXXX from existing context), how will this single instance meet the projected load? What are the specific assumptions about request processing time that underpin this choice?\\\\n*   **Authentication Flow Security:** The described authentication flow doesn\\\'t detail refresh token handling. Considering the \\\'AuthService v2\\\' (Decision: YYYY) addressed [specific vulnerability] via [specific mechanism], how does the current proposal ensure similar protection against [specific threat]?\\\\n\\\\n**🟡 Key Design Considerations & Probing Questions**\\\\n*   **Data Model for User Preferences:** The design suggests a flexible schemaless model for user preferences. Our \\\'UserProfile Service\\\' (Decision: ZZZZ) opted for a structured JSONB to ensure query consistency. What are the anticipated benefits of the schemaless approach here that outweigh the potential for data inconsistency or complex querying down the line?\\\\n*   **API Versioning Strategy:** An API versioning strategy isn\\'t explicitly mentioned. For long-term maintainability and client compatibility, what are your thoughts on adopting [e.g., URI path versioning / header-based versioning], similar to other core services, and what might be the trigger for a new version?\\\\n\\\\n**🔵 Points for Clarification & Minor Enhancements (Refine and Polish)**\\\\n*   **Component Interaction Diagram:** The interaction flow between Service A and Service B is described, but could a sequence diagram further clarify the exact call chain and error handling paths?\\\\n*   **Definition of \\\'High Availability\\\':** The term \\\'High Availability\\\' is used. Could you quantify the specific uptime target (e.g., 99.9%, 99.99%) and briefly outline how the design achieves it for key components?\\\\n\\\\n**🟢 Positive Aspects & Well-Considered Elements **\\\\n*   The problem statement is very clear and effectively frames the need for this feature.\\\\n*   The proposed use of a message queue for [specific task] strongly aligns with our successful pattern in the \\\'OrderProcessing Pipeline\\\' (Decision: WWWW) for enhancing resilience.`;

  } else { // 'code' - Existing instructions for code review
    const codeChangesArray = Array.isArray(inputContent) ? inputContent : [];
    formattedContentSection = `**Staged Code Changes:**\\n${formatCodeChanges(codeChangesArray)}`; 
    contextPreamble = `**Context for Code Review:**\nBranch/Task: ${prContext?.ref || prContext?.branch || 'N/A'}\nDraft PR Title: ${prContext?.title || 'N/A'}\n${existingContext && existingContext.trim() !== '' ? `\\n**Relevant Existing Patterns/Decisions (from knowledge base):**\\n---\\n${existingContext}\\n---\\n` : ''}`;
    llmTaskInstructions = `\\nReview the ${inputTypeDisplay} above and provide feedback. Categorize your feedback to clearly indicate its importance and required action. Ensure your output strictly follows the prescribed Markdown format and the section order detailed below, including the new "🚨 Identified High & Medium Severity Risks" section. Focus on the following:\\n\\n1.  **🛑 Blocking Concerns (Requires Urgent Attention):**\\n    *   Identify critical architectural issues that could lead to significant problems (e.g., security vulnerabilities, major scalability bottlenecks, severe performance degradation, fundamental design flaws). \\n    *   **Crucially, if \\'Relevant Existing Patterns/Decisions\\\' are provided, treat any direct contradiction or violation of established patterns, rationales, or developer guidance (e.g., from a \\'dev_prompt\\\') as a BLOCKING CONCERN unless the current changes provide an exceptionally strong and explicit justification for the deviation.**\\n    *   These are items the engineer MUST address before proceeding or merging. Clearly explain the concern, reference the specific existing decision/pattern being violated (if applicable), detail its potential impact, and ask precise questions to guide resolution.\\n\\n2.  **🚨 Identified High & Medium Severity Risks:**\\n    *   Based on your review, identify potential high or medium severity risks introduced by the changes or inherent in the current design.\\n    *   For each risk, provide:\\n        *   **Risk:** A clear description of the risk.\\n        *   **Category:** The area affected (e.g., Security, Performance, Scalability, Maintainability, Reliability, UX, Testability).\\n        *   **Severity:** Must be either High or Medium.\\n        *   **Suggested Mitigation:** Actionable steps to address or mitigate the risk.\\n    *   These risks should be clearly articulated and distinct from general blocking concerns unless a blocking concern is also a specific, categorizable risk.\\n\\n3.  **🤔 Major Design Questions & Suggestions (Discussion Recommended):**\\n    *   Pose questions about significant design choices, architectural patterns used, or potential alternative approaches that might be more suitable, especially when compared against the provided existing context.\\n    *   Highlight areas where the architectural impact isn\\'t clear or where a discussion could lead to a better outcome (e.g., choices affecting maintainability, testability, or long-term evolution of the system). Consider if the changes align with or diverge from patterns mentioned in the existing context.\\n    *   **Best Practices & Alternatives:** Specifically assess if the proposed changes align with established architectural best practices and principles. If the solution is complex, introduces significant new patterns, or deviates from best practices (or relevant existing context) without clear, strong justification, indicate that exploring alternative solutions could be highly beneficial. Clearly explain why and what aspects should be reconsidered or compared against alternatives. Some of these suggestions might imply necessary changes.\\n\\n4.  **💡 Minor Suggestions & Polish (Consider for Improvement):**\\n    *   Offer suggestions for smaller improvements, code clarity (if architecturally relevant), minor refactoring opportunities that don\\'t block progress but could enhance the quality.\\n    *   These are non-critical "nice-to-have" improvements.\\n\\n5.  **✅ Positive Feedback (Keep Up the Good Work!):**\\n    *   Acknowledge and reinforce good design choices, clever solutions, adherence to best practices, or well-implemented patterns, especially if they align well with the existing context provided.\\n\\n6.  **🤖 Agent Instructions (for actionable changes):**\\n    *   **This section is for an LLM agent and MUST ALWAYS be included in your output.** This is a comprehensive list of ALL code changes that should be made based on your feedback - including items from every section (Blocking Concerns, Major Design Questions & Suggestions, AND Minor Suggestions) that involve actual code modifications. \\n    *   Your goal is to provide a precise, unambiguous roadmap for an LLM agent to implement ALL necessary changes. Be specific and thorough - the agent should be able to execute these changes with minimal additional context.\\n    *   For each item, use this structured format:\\n      * **File:** Full path to the file that needs changes\\n      * **Issue:** Concise description of the problem\\n      * **Action:** Detailed, specific instructions on what code changes to make, including:\\n        * Exactly where in the file to make changes (function names, line descriptions)\\n        * What code to add, modify, or remove\\n        * Any patterns, imports, or specific approaches to follow\\n        * If relevant, explicitly reference existing patterns/architectural decisions that should be followed\\n    *   Include implementation details where possible. For example, instead of just saying \\'Implement a click-outside handler\\', specify \\'Add a useEffect hook that registers a document-level click listener to check if the click target is outside the emoji picker ref, and if so, calls the closeEmojiPicker function\\'.\\n    *   **Cover ALL actionable feedback** - not just Blocking Concerns. If a Major Design Question or even Minor Suggestion involves a concrete code change that would improve the implementation, include detailed instructions for it.\\n    *   **If there are genuinely no actionable code changes needed** (which should be rare), you MUST still include this section heading and write: \\'No specific agent actions identified that require direct code changes at this stage.\\' under it.\\n`;
    exampleFeedback = `\\nHi there! I\\'ve taken a look at your ${inputTypeDisplay === 'design document' ? 'design document' : 'recent changes for ' + (prContext?.ref || prContext?.branch || '[Feature/Branch Name]')} and have some thoughts and questions${existingContext && existingContext.trim() !== '' ? ', keeping in mind our relevant past decisions' : ''}:\\n\\n**🛑 Blocking Concerns (Requires Urgent Attention)**\\n*   **Security Risk in \`authService.js\`:** The new endpoint for user data retrieval doesn\\'t seem to have authorization checks. This could expose sensitive user information.\\n    *   *Question:* What is the intended authorization mechanism for this endpoint? How can we ensure only permitted users can access this data?\\n    *   *Impact:* Potential data breach.\\n*   **Violation of Established Pattern: \`PaymentProcessingService.js\`** The changes introduce direct synchronous calls to \`ExternalAnalyticsCollector\`. Our established pattern for analytics, detailed in \\'Decision-001: Decoupled Analytics via Event Queue\\\\' (see provided context), mandates asynchronous event emission to avoid performance impact on core flows. The \\'dev_prompt\\\\' for that decision specifically states: \\'All new services interacting with analytics MUST publish events to the \\'analytics-events\\\\' topic and MUST NOT make direct synchronous calls to the collector service.\\'\\n    *   *Question:* What is the rationale for deviating from this established and critical architectural pattern? How will the risk of performance degradation in payment processing be mitigated with this synchronous call?\\n    *   *Impact:* Potential performance degradation in critical payment processing, violation of architectural standards, increased coupling.\\n    *   *Required Action:* Revert to using asynchronous event emission for analytics data as per Decision-001, or provide an exceptionally strong, documented justification for this deviation and secure architectural approval.\\n\\n**🚨 Identified High & Medium Severity Risks**\\n*   **Risk:** The new caching mechanism in \`UserDataService.js\` has no eviction strategy defined, which could lead to unbounded memory growth.\\n    *   **Category:** Performance\\n    *   **Severity:** High\\n    *   **Suggested Mitigation:** Implement an LRU (Least Recently Used) eviction policy for the cache with a configurable size limit. Monitor cache hit/miss rates and memory usage.\\n*   **Risk:** User-provided input for search queries in \`SearchController.java\` is directly concatenated into a SQL query, posing a SQL injection vulnerability.\\n    *   **Category:** Security\\n    *   **Severity:** High\\n    *   **Suggested Mitigation:** Use parameterized queries or a dedicated query builder library that sanitizes inputs to prevent SQL injection. Review all data access points for similar vulnerabilities.\\n*   **Risk:** The retry logic for calls to the external \`InventoryService\` does not use exponential backoff, potentially overwhelming the service during outages.\\n    *   **Category:** Reliability\\n    *   **Severity:** Medium\\n    *   **Suggested Mitigation:** Implement exponential backoff with jitter for retries to the \`InventoryService\`. Consider adding a circuit breaker pattern.\\n\\n**🤔 Major Design Questions & Suggestions (Discussion Recommended)**\\n*   **Data Flow in \`OrderProcessor.js\`:** The direct dependency from \`OrderProcessor\` to the \`LegacyPaymentGateway\` might make it hard to introduce new payment providers later. ${existingContext && existingContext.includes('NotificationService') ? "Our existing \`NotificationService\` uses events effectively for similar decoupling (see context provided)." : ""} It is strongly recommended to decouple this for future flexibility.\\n    *   *Question:* Have you considered using an adapter pattern or an event-based approach here to decouple these components? This could improve flexibility for future payment integrations.\\n*   **Choice of \`ultra-cache-lib\`:** I see you\\'ve introduced \`ultra-cache-lib\`. ${ existingContext && existingContext.includes('fast-cache') ? "Our standard is currently \`fast-cache\` (see context)." : ""}\\n    *   *Question:* Could you share the main reasons for choosing this library over our current standard? Are there specific features that were essential for this use case? If not compelling, alignment with team standards is preferred.\\n\\n**💡 Minor Suggestions & Polish (Consider for Improvement)**\\n*   **Readability in \`utils.js\`:** The \`calculateMetrics\` function is a bit long.\\n    *   *Suggestion:* Perhaps extracting some of the conditional logic into smaller helper functions could improve its readability.\\n\\n**✅ Positive Feedback (Keep Up the Good Work!)**\\n*   **Strategy Pattern in \`ReportGenerator.js\`:** The use of the Strategy pattern for different report formats is excellent! It\\'s very clear and will make it easy to add new formats in the future.\\n\\n**🤖 Agent Instructions (for actionable changes)**\\n*   **File:** \`src/services/authService.js\`\\n    *   **Issue:** New user data endpoint lacks authorization checks\\n    *   **Action:** Implement authorization checks in the getUserData endpoint function:\\n        * Import the authorization utility: \`import { checkUserPermission } from '../utils/authUtils';\`\\n        * Add a permission check before retrieving data: \`if (!checkUserPermission(req.user, 'READ_USER_DATA')) { return res.status(403).json({ error: 'Unauthorized access' }); }\`\\n        * Ensure req.user exists by adding this check at the top of the function: \`if (!req.user) { return res.status(401).json({ error: 'Authentication required' }); }\`\\n*   **File:** \`src/services/PaymentProcessingService.js\`\\n    *   **Issue:** Direct synchronous calls to ExternalAnalyticsCollector violate Decision-001 (Decoupled Analytics via Event Queue)\\n    *   **Action:** Refactor analytics data handling to use asynchronous event emission:\\n        * Replace the direct collector import with event emitter: \`import { emitEvent } from '../events/eventBus';\`\\n        * Replace all instances of \`ExternalAnalyticsCollector.collect(data)\` with \`emitEvent('analytics-events', { type: 'PAYMENT_ANALYTICS', data })\`\\n        * Remove the import for ExternalAnalyticsCollector as it's no longer needed\\n*   **File:** \`src/services/OrderProcessor.js\`\\n    *   **Issue:** Tight coupling with LegacyPaymentGateway\\n    *   **Action:** Implement the adapter pattern to decouple from LegacyPaymentGateway:\\n        * Create a new file \`src/adapters/PaymentGatewayAdapter.js\` that exports a standard interface for payment operations\\n        * In the adapter file, implement a class that internally uses LegacyPaymentGateway but exposes a cleaner, more abstract API\\n        * In OrderProcessor.js, replace the direct import and usage of LegacyPaymentGateway with the new adapter\\n        * Use dependency injection in the OrderProcessor constructor to accept any payment gateway that implements the adapter interface\\n*   **File:** \`src/utils/utils.js\`\\n    *   **Issue:** The calculateMetrics function is too long and complex\\n    *   **Action:** Extract logical sections into helper functions:\\n        * Identify the main segments of the function (e.g., data validation, calculation steps, formatting)\\n        * Create new helper functions for each segment with descriptive names\\n        * Modify calculateMetrics to call these helper functions in sequence\\n        * Keep all functions in the same file but consider moving them to a dedicated metrics utility file if there are many\\n\\nLet me know your thoughts on these points, especially the blocking concerns and any areas where discussing alternatives might be fruitful. I\\'m happy to discuss these further!\\n`;
  }

  return `You are an experienced Technical Lead and Software Architect providing supportive, early feedback on work-in-progress ${inputTypeDisplay}. Your goal is NOT to generate a formal architectural decision record (ADR), but to help the engineer think critically about their choices, identify potential issues early, and align with best practices and existing patterns. If relevant past decisions are provided as **Relevant Existing Patterns/Decisions**, you MUST integrate them into your feedback and analysis, particularly for alignment, justification of deviations, and suggesting alternatives.\\\\n\\\\nAdopt a guiding, Socratic approach. Instead of just stating problems, ask clarifying questions that prompt the engineer to reflect on their design. Your feedback should be actionable and clearly prioritized and pragmatic.\\\\n\\\\n${contextPreamble}\\\\n\\\\n${formattedContentSection}\\\\n\\\\n---\\\\n**Your Task:**\\\\n${llmTaskInstructions}\\\\n\\\\n**Output Format:**\\\\n\\\\nProvide your feedback as a *markdown-formatted* text response, suitable for display in an IDE or a web UI. Use bullet points for clarity within each category. Start with a brief introductory sentence appropriate for reviewing a ${inputTypeDisplay}. You MUST always include all specified feedback categories in your output, including the new '🚨 Identified High & Medium Severity Risks' section in the correct order (after '🛑 Blocking Concerns' and before '🤔 Major Design Questions & Suggestions'), as demonstrated in the example. If there are no actionable changes for the agent, state this explicitly within that section as per the instructions in point 5 for code reviews (or point 6 if numbering changes). \\\\n\\\\n**CRITICAL:** Do NOT output JSON. Do NOT try to extract a formal \\\'architectural decision\\\'. Focus *entirely* on providing constructive, categorized, and question-based feedback to guide the engineer\\\'s thinking process. Be supportive and collaborative in tone. Refer to the example below for structure, but tailor your actual content to the ${inputTypeDisplay} provided.\\\\n\\\\nExample Feedback Snippet (adapt the introduction and content based on the specific ${inputTypeDisplay} and context provided):\\\\n\\\`\\\`\\\`markdown\\\n${exampleFeedback}\\\n\\\`\\\`\\\`\\\\n`;
}

/**
 * Generates a prompt for an LLM to extract key domain concepts from a design document.
 * @param {string} designDocContent - The full text content of the design document.
 * @returns {string} - Prompt for the LLM.
 */
export function generateDesignDocConceptExtractionPrompt(designDocContent) {
  // Basic check for empty content
  if (!designDocContent || designDocContent.trim() === '') {
    console.warn('[GenConceptExtractionPrompt] Design document content is empty.');
    return ''; // Return empty string or handle error as appropriate
  }

  // Take a sizable snippet or the full content if short
  const contentSnippet = designDocContent.length > 8000 ? designDocContent.substring(0, 8000) + '... (truncated for concept extraction)' : designDocContent;

  return `You are an expert system architect. Your task is to analyze the provided Design Document snippet and extract the most salient and discriminative domain concepts. These concepts will be used to perform a semantic search for related past architectural decisions in a knowledge base.

**Design Document Content (potentially truncated):**
---
${contentSnippet}
---

**Instructions:**

1.  Read the Design Document Content carefully to understand its main themes, technologies, and architectural focus.
2.  Identify and extract between **three (3) and seven (7)** key domain concepts. These should be nouns or short noun phrases (2-4 words max) that precisely represent the core architectural topics, critical components, key technologies, or significant problem areas discussed.
3.  Prioritize concepts that are specific and discriminative. Avoid overly generic terms (e.g., "system", "service", "database", "API") unless they are part of a highly specific and crucial phrase (e.g., "real-time notification system", "user identity service", "graph database for social network", "public REST API versioning").
4.  The goal is to identify terms that, when used for a semantic search, would effectively retrieve prior decisions relevant to the *specific architectural challenges or solutions* presented in this document.

**Output Format:**

Provide your response as a JSON object with a single key "domain_concepts". The value should be an array of strings, where each string is a single extracted concept.

Example:
{
  "domain_concepts": [
    "secure real-time collaboration",
    "PDF annotation services",
    "WebSocket session management",
    "role-based access control model",
    "data encryption at rest for user content",
    "microservices for document processing"
  ]
}

If you genuinely believe no specific, discriminative domain concepts (as per the criteria) can be extracted that would be useful for semantic search, return an empty array for "domain_concepts":
{
  "domain_concepts": []
}

Please provide ONLY the JSON object in your response.`;
}


/**
 * Generates a prompt for an LLM to create a lightweight design document.
 * @param {object} taskDetails - Details of the task.
 * @param {string} taskDetails.taskTitle - The title of the task.
 * @param {string} taskDetails.taskDescription - The description of the task.
 * @param {string} [taskDetails.initialApproachIdeas] - Optional initial approach ideas.
 * @param {string} retrievedContext - Formatted string of relevant historical/existing architectural decisions.
 * @returns {string} - Prompt for the LLM.
 */
export function generateDesignDocPrompt(taskDetails, retrievedContext) {
  const { taskTitle, taskDescription, initialApproachIdeas } = taskDetails;

  const initialIdeasSection = initialApproachIdeas && initialApproachIdeas.trim() !== ''
    ? `**Initial Approach Ideas from Developer:**
${initialApproachIdeas}`
    : '';

  const contextBlock = retrievedContext && retrievedContext.trim() !== ''
    ? `
**Relevant Existing Architectural Decisions & Context (from Knowledge Base):**
---
${retrievedContext}
---
This context provides insights into established patterns, past challenges, and approved solutions that might be relevant to the current task. Refer to these when formulating the design, especially for "Alternatives Considered" and "High-Level Design".
    `
    : `
**Relevant Existing Architectural Decisions & Context (from Knowledge Base):**
---
No specific existing architectural context was automatically retrieved. Base your design on general best practices and the provided task details.
---
    `;

  return `You are an expert software architect tasked with helping a developer draft a **concise, to-the-point, and easy-to-review lightweight design document**. The goal is to provide a solid starting point, not an exhaustive treatise. Leverage any provided existing architectural decisions to ensure consistency and learn from past experiences.

**Task Information:**
*   **Title:** ${taskTitle}
*   **Description:** ${taskDescription}
${initialIdeasSection}

${contextBlock}

**Design Document Structure and Instructions:**

Throughout your design, actively consider the implications for key quality attributes such as scalability, security (including data privacy and threat modeling if relevant), performance, maintainability, testability, and observability. Highlight any significant aspects related to these attributes in the relevant sections (especially High-Level Design and Alternatives Analysis). Your role as an expert architect is to ensure these are thoughtfully addressed in a practical manner suitable for the task's scope.

Please generate a design document with the following sections. Be accurate and focused.

1.  **Goals:**
    *   List 2-4 primary, specific, and measurable goals for this task/feature. What should it achieve?
2.  **Non-Goals:**
    *   List 1-3 specific things that are out of scope for this task to clarify boundaries.
3.  **High-Level Design:**
    *   **Technical Decisions & Constraints (REQUIRED):**
        - List ALL critical technical decisions
        - List ALL system constraints & limits
        - For each constraint: How will it be enforced? How will users be informed?
        - For each external service/library: Why this choice? What are the licensing, cost, and operational implications?
    *   **Error Handling & Recovery (REQUIRED):**
        - List ALL critical error scenarios
        - For each: Detection method, user communication, recovery action
        - Retry strategies, fallback behaviors, circuit breakers
    *   **Core Flow:**
        - Concise overview (1-2 paragraphs max)
        - Key components & responsibilities
        - Data model changes (if any)
    *   **Process Flow Diagram (if applicable):**
        - Use Mermaid diagram to show state transitions/data flow
        - Example:
        \`\`\`mermaid
        stateDiagram-v2
            [*] --> InitialState
            InitialState --> Processing: Event A
            Processing --> FinalState: Event B
            FinalState --> [*]
        \`\`\`
    *   **Infrastructure & Ops:**
        - Deployment strategy
        - Monitoring approach (metrics, alerts)
        - Rollback plan
        - Security considerations
    *   **Developer Notes:**
        - Reference relevant decisions limited to context provided (e.g., "Using Event-Driven Pattern (ADR-042)")
        - Implementation guidance
4.  **Alternatives Analysis:**
    *   As a Technical Lead, objectively evaluate the primary proposed solution (derived from the task description or initial ideas) alongside at least 1-2 distinct alternative architectural approaches. The goal is to identify the most robust and suitable path, which may differ from the initial proposal.
    *   For the **primary proposed approach** AND **each alternative**:
        *   **Approach Name/Description:** Clearly name and describe the architectural approach (e.g., 'Proposed: Real-time Sync via WebSockets', 'Alternative: Long Polling for Updates', 'Alternative: Batch Processing with Daily Sync').
        *   **Pros:** List 2-3 key advantages of this approach in the context of the task goals, non-goals, and relevant existing decisions (from the knowledge base).
        *   **Cons:** List 2-3 key disadvantages. Specifically address **potential technical or operational risks, and any negative impacts on quality attributes** (e.g., performance bottlenecks, increased security exposure, maintainability challenges, deployment complexity).
        *   **Alignment with Context:** Briefly assess how this approach aligns with or diverges from established patterns or decisions found in the "Relevant Existing Architectural Decisions & Context". Note any potential conflicts or synergies.
        *   **Overall Recommendation & Justification (Crucial):** As the Technical Lead, provide a clear recommendation for the most suitable approach. Clearly justify this recommendation by summarizing the key trade-offs and explaining why it appears to be the best path forward, even if it means suggesting a different direction than initially conceived.
5.  **Milestones:**
    *   Break down the work into 3-5 major milestones.
    *   **Early milestones (the first 1-2) should focus on de-risking the project by tackling the most complex or uncertain aspects first and establishing a "steel thread" – a minimal but functional end-to-end slice of the core functionality.**
    *   For each milestone:
        - Provide a clear, descriptive name.
        - Include a detailed description outlining specific deliverables and completion criteria. For early/steel-thread milestones, specify what core path will be proven.
        - Define observable success indicators that show when the milestone is complete (e.g., "core API endpoint X is deployed and returns Y for Z input," "basic user registration and login flow operational").
        - List any dependencies on other milestones or external factors.
        - Assign priority (High, Medium, Low) based on importance to project success and its role in de-risking.
        - Indicate complexity (High, Medium, Low) to highlight technical challenges.
    *   Order milestones in a logical sequence of implementation, with de-risking milestones first.
6.  **How to Measure Success:**
    *   List 2-3 key metrics or observable outcomes that will indicate the successful completion and adoption of this feature/task.
7.  **Referenced Decisions:**
    *   This section will be populated based on your references in other sections. Ensure any decision from the provided context that you explicitly mention (e.g., by ID like ADR-XXX) is listed here with its title and a brief note on its relevance to this design.

**Output Format:**

Respond *only* with a JSON object adhering strictly to this structure. Do NOT include any explanatory text outside the JSON. Ensure all string content within the JSON is valid Markdown where appropriate (especially for High-Level Design).

\`\`\`json
{
  "title": "${taskTitle}",
  "goals": [
    "string - Goal 1..."
  ],
  "nonGoals": [
    "string - Non-goal 1..."
  ],
  "highLevelDesign": {
    "technicalDecisions": {
      "criticalChoices": [
        {
          "decision": "string - e.g., 'TTS Library: AWS Polly v2.5'",
          "rationale": "string - Why this choice? e.g., 'Best quality/cost ratio, supports 95% of our required languages'",
          "implications": "string - e.g., 'AWS dependency, $X/million chars, requires IAM setup'"
        }
      ],
      "systemConstraints": [
        {
          "constraint": "string - e.g., 'Voice memo max duration: 30s'",
          "enforcement": "string - e.g., 'Frontend validation + server-side check'",
          "userCommunication": "string - e.g., 'Warning at 25s, error at 30s'"
        }
      ]
    },
    "errorHandling": {
      "scenarios": [
        {
          "scenario": "string - e.g., 'TTS conversion failure'",
          "detection": "string - e.g., 'HTTP 5xx from AWS Polly'",
          "userMessage": "string - e.g., 'Voice conversion failed. Please try again.'",
          "recovery": "string - e.g., 'Retry 3x with exponential backoff'"
        }
      ],
      "retryStrategy": "string - e.g., 'Global circuit breaker pattern with 60s timeout'",
      "fallbackBehavior": "string - e.g., 'Queue for retry, notify user via email'"
    },
    "coreFlow": {
      "overview": "string - Brief description of the solution",
      "components": [
        {
          "name": "string - e.g., 'TTS Processor'",
          "responsibility": "string - e.g., 'Handles voice conversion requests'"
        }
      ],
      "dataModelChanges": "string - Any schema/model updates, or 'N/A'"
    },
    "processFlow": "string - Mermaid diagram if applicable, or 'N/A'",
    "infrastructure": {
      "deployment": "string - e.g., 'Rolling update via k8s'",
      "monitoring": "string - e.g., 'Datadog metrics + PagerDuty alerts'",
      "rollback": "string - e.g., 'k8s rollback to previous version'",
      "security": "string - e.g., 'TLS 1.3, JWT auth required'"
    },
    "developerNotes": {
      "referencedDecisions": ["string - e.g., 'ADR-042: Event Pattern'"],
      "implementationGuidance": "string - e.g., 'Use shared retry util from core lib'"
    }
  },
  "alternativesAnalysis": {
    "evaluatedApproaches": [
      {
        "approachName": "string - Name & brief description (e.g., 'Proposed: Real-time Sync via WebSockets')",
        "description": "string - Detailed description of this architectural approach, its key mechanisms, and how it addresses the problem.",
        "pros": ["string - Pro 1: e.g., 'Low latency updates'", "string - Pro 2"],
        "cons": ["string - Con 1: e.g., 'Higher server resource consumption'", "string - Con 2"],
        "alignmentWithContext": "string - Assessment of alignment (e.g., 'Aligns with ADR-003 for real-time features but requires careful scaling considerations noted in ADR-005.')",
        "referencedDecisionIds": ["string - Optional: e.g., ADR-003", "string - ADR-005"]
      }
      // more approaches (including the primary proposed one and alternatives)
    ],
    "recommendation": "string - Optional: Your preliminary recommendation (e.g., 'Alternative B seems more robust due to...') and justification."
  },
  "milestones": [
    {
      "name": "string - Milestone Name (e.g., 'Steel Thread: Core User Authentication API')",
      "description": "string - Detailed description outlining specific deliverables and completion criteria. Specify what core path will be proven and what is being de-risked.",
      "successIndicators": ["string - Observable success indicator 1 (e.g., 'API endpoint X returns Y for Z input')", "string - Observable success indicator 2"],
      "dependencies": ["string - Dependency 1 (e.g., 'Completion of Milestone X', 'External API Y available')"],
      "priority": "High",
      "complexity": "High"
    },
    {
      "name": "string - Milestone Name (e.g., 'Extend Steel Thread: Basic Profile Management UI')",
      "description": "string - Build upon the steel thread, further de-risking UI/UX assumptions or key integrations. Detail specific deliverables and completion criteria.",
      "successIndicators": ["string - Observable success indicator 1", "string - Observable success indicator 2"],
      "dependencies": ["string - Dependency 1"],
      "priority": "High",
      "complexity": "Medium"
    }
    // Add more milestones as needed, following the detailed structure.
  ],
  "successMetrics": [
    "string - Metric 1: e.g., Reduce API latency for X by Y%",
    "string - Metric 2: e.g., Achieve Z daily active users for the new feature"
  ],
  "referencedDecisions": [
    {
      "id": "string - e.g., ADR-007",
      "title": "string - Title of the referenced decision",
      "summaryOfRelevance": "string - How this decision is relevant to this design document (e.g., 'Pattern Z adopted for data processing as per this ADR.')"
    }
  ]
}
\`\`\`

Strive for clarity and conciseness. The developer will use this as a starting point. Ensure all referenced decision IDs (like 'ADR-XXX') accurately reflect IDs from the provided context.
The "referencedDecisions" array in the output JSON must be a consolidated list of all unique decisions you referred to in "High-Level Design" or within the "Alternatives Analysis" section (specifically from the \\\`referencedDecisionIds\\\` fields).
`;
}

// Add helper function to format complete constitution
function formatCompleteConstitution(projectConstitution) {
  if (!projectConstitution || Object.keys(projectConstitution).length === 0) {
    return "No constitution provided.";
  }

  const { companyStage, projectType, priorities, architecturalPrinciples, productAndBusinessContext, primaryLanguage } = projectConstitution;
  const parts = [];

  if (companyStage && companyStage !== 'not-set') {
    parts.push(`- **Company Stage:** ${companyStage}`);
  }
  if (projectType && projectType !== 'not-set') {
    parts.push(`- **Project Type:** ${projectType}`);
  }
  if (priorities && priorities.length > 0) {
    const priorityList = priorities.map((p, i) => `  ${i + 1}. ${p.replace(/-/g, ' ')}`).join('\n');
    parts.push(`- **Top 3 Architectural Priorities:**\n${priorityList.slice(0, 3)}`);
  }
  if (primaryLanguage) {
    parts.push(`- **Primary Language:** ${primaryLanguage}`);
  }
  if (architecturalPrinciples) {
    parts.push(`- **Top 5 Architectural Principles:**\n${architecturalPrinciples}`);
  }
  if (productAndBusinessContext) {
    parts.push(`- **Product & Business Context:**\n${productAndBusinessContext}`);
  }

  return parts.join('\n\n');
}

// --- PHASE 1 PROMPT ---
/**
 * Generates a prompt for Phase 1 of design document generation:
 * Identifying Goals, Non-Goals, and Critical Technical Decision Points.
 *
 * @param {object} taskDetails - Details of the task.
 * @param {string} taskDetails.taskTitle - The title of the task.
 * @param {string} taskDetails.taskDescription - The description of the task.
 * @param {string} [taskDetails.initialApproachIdeas] - Optional initial approach ideas from the user.
 * @param {string[]} [taskDetails.initialDomainConcepts] - Optional: Initial domain concepts extracted from the task.
 * @returns {string} - Prompt for the LLM.
 */
export function generatePhase1GoalsAndDecisionsPrompt(taskDetails, taskAnalysis, projectConstitution) {
  const { taskTitle, taskDescription, initialIdeas, isExperimental } = taskDetails;
  const formattedRequirements = taskAnalysis.functional_requirements?.map(req => `- ${req}`).join('\n') || '';
  const formattedNFRs = taskAnalysis.implied_NFRs.map(nfr => `- ${nfr.nfr} (Evidence: "${nfr.evidence}")`).join('\n');
  const constitutionContext = formatCompleteConstitution(projectConstitution);

  // Extract key constitution elements for strategic guidance
  const companyStage = projectConstitution?.companyStage || 'not-set';
  const projectType = projectConstitution?.projectType || 'not-set';
  const topPriority = projectConstitution?.priorities?.[0] || 'time-to-market';
  
  // Generate stage-specific strategic guidance
  const stageGuidance = {
    'startup': 'Focus on minimal viable features that prove core value proposition. Defer complex features that don\'t directly impact user validation or market fit. Question every technical decision: does this help us learn faster or just add complexity?',
    'growth': 'Balance feature completeness with scalability needs. Defer features that require significant new infrastructure or complex maintenance unless they\'re blocking growth.',
    'mature': 'Prioritize reliability and maintainability. Defer features that introduce technological complexity or operational overhead unless clearly justified by business value.'
  };

  const projectTypeGuidance = {
    'b2c-saas': 'Focus on user experience and rapid iteration. Defer features that require complex integrations or administrative overhead that don\'t directly improve user experience.',
    'b2b-saas': 'Focus on enterprise needs and integration capabilities. Defer features that don\'t align with business user workflows or add unnecessary complexity to core business processes.',
    'library': 'Focus on core API stability and developer experience. Strongly question: should this be solved in the library or left to the application? Defer features that increase dependency complexity, maintenance burden, or make the library harder to understand.',
    'internal-tool': 'Focus on operational efficiency and team productivity. Defer features that don\'t directly impact internal workflows or would be complex to maintain.',
    'data-platform': 'Focus on data integrity and processing reliability. Defer features that introduce data consistency risks, performance overhead, or operational complexity without clear value.'
  };

  const currentStageGuidance = stageGuidance[companyStage] || 'Consider the balance between feature completeness and delivery speed, always questioning if complexity is justified.';
  const currentProjectTypeGuidance = projectTypeGuidance[projectType] || 'Align features with project goals and user needs, avoiding unnecessary complexity.';

  // Special library guidance
  const librarySpecificGuidance = projectType === 'library' ? `

**🔬 CRITICAL LIBRARY-SPECIFIC DECISION FRAMEWORK:**
- **Boundary Question**: For every decision, ask: "Should this be the library's responsibility or the application's?"
- **Maintenance Reality**: Every feature in a library affects ALL users. Complex features become technical debt for your entire user base.
- **Simplicity Principle**: Libraries should solve ONE thing exceptionally well rather than many things adequately.
- **User Responsibility**: Often it's better to require library users to handle edge cases than to build complex internal logic.
- **Documentation vs Implementation**: Sometimes documenting limitations is far better than implementing complex solutions.
` : '';

  // Add experimental feature constraints that override other constraints
  const experimentalConstraints = isExperimental ? `

🧪 **EXPERIMENTAL FEATURE CONSTRAINTS (OVERRIDES ALL OTHER CONSTRAINTS):**
- **NO DATA MODEL CHANGES**: Experimental features must NOT require database schema changes, new tables, or data migrations
- **NO ARCHITECTURAL CHANGES**: Avoid changes to core system architecture, service boundaries, or fundamental patterns
- **FEATURE FLAG FRIENDLY**: All decisions must be easily reversible through feature flags or configuration
- **UI/CONFIGURATION FOCUSED**: Prefer decisions about UI behavior, display logic, configuration options, or API endpoints
- **EXISTING INFRASTRUCTURE**: Use existing services, databases, and infrastructure - do not introduce new dependencies
- **EASY REMOVAL**: Every decision should consider: "How easily can this be completely removed if the experiment fails?"
- **MINIMAL SCOPE**: Focus on the smallest possible set of changes that can validate the core hypothesis
- **RAPID ITERATION**: Prioritize decisions that enable quick changes based on user feedback

**EXPERIMENTAL DECISION CATEGORIES TO FOCUS ON:**
- **User Interface**: Display logic, component behavior, layout decisions
- **Feature Flags**: Configuration and toggling mechanisms
- **API Endpoints**: Simple endpoints that don't require new data models
- **Business Logic**: Processing rules that can be easily modified or removed
- **Integration Points**: How to connect to existing services without architectural changes
- **Monitoring**: How to measure experiment success without complex analytics infrastructure

**EXPERIMENTAL DECISION CATEGORIES TO AVOID:**
- **Data Storage**: Database schema, new tables, data model changes
- **Core Architecture**: Service boundaries, fundamental patterns, system structure
- **Infrastructure**: New services, databases, message queues, or external dependencies
- **Security Models**: Authentication, authorization, or permission changes
- **Performance Optimization**: Caching strategies, database optimization, or system-wide performance changes
` : '';

  // Modify the philosophy section for experimental features
  const philosophySection = isExperimental ? `
**EXPERIMENTAL FEATURE PHILOSOPHY: VALIDATE QUICKLY, ITERATE SAFELY**
- **Validation Focus**: Every decision should help validate user hypotheses or business assumptions
- **Reversibility First**: Can this be completely turned off or removed without breaking anything?
- **Speed Over Perfection**: Prefer quick, simple solutions over robust, complex ones
- **Learning Optimization**: Optimize for learning rate, not system performance or scalability
- **Configuration Over Code**: Use configuration and feature flags instead of complex logic when possible
` : `
**CRITICAL PHILOSOPHY: QUESTION COMPLEXITY BEFORE CREATING IT**
- **Maintenance Cost Reality**: Every technical decision creates long-term maintenance burden. Complex solutions need complex debugging, testing, and documentation.
- **Early Optimization Trap**: Are we solving problems that don't exist yet? Sometimes documenting limitations is better than building complex solutions.
- **Simplicity First**: The best technical decision is often the simplest one that meets the actual need, not the most clever or comprehensive.
- **Future-Proofing vs Present-Focus**: Future flexibility is good, but not at the cost of present complexity that may never be needed.
`;

  return `
You are a Principal Engineer and Solutions Architect responsible for planning the initial phase of a new project. 
Your task is to define the primary goals, explicit non-goals, and identify the most critical technical decision points that need to be addressed to successfully deliver the feature.

${philosophySection}

**CONTEXT:**

**1. Overall Task:**
   - **Title:** ${taskTitle}
   - **Description:** ${taskDescription}
   - **Initial Ideas from Requester:** ${initialIdeas || "None"}
   - **Experimental Feature:** ${isExperimental ? 'YES - Focus on reversible, feature-flag-friendly decisions' : 'NO - Standard feature development'}

**2. Verified Core Requirements:**
   - **Functional Requirements:**
${formattedRequirements}
   - **Implied Non-Functional Requirements (NFRs):**
${formattedNFRs}
   - **Key Architectural Characteristics:** ${taskAnalysis.core_architectural_characteristics.join(', ')}

**3. Complete Project Constitution:**
${constitutionContext}

**4. Strategic Guidance for ${companyStage.toUpperCase()} ${projectType.toUpperCase()}:**
   - **Company Stage Guidance:** ${currentStageGuidance}
   - **Project Type Guidance:** ${currentProjectTypeGuidance}
   - **Top Priority:** ${topPriority.replace(/-/g, ' ')}${librarySpecificGuidance}${experimentalConstraints}

**CRITICAL GUIDANCE:** Use the complete constitution above to ensure you don't over-engineer or under-engineer the technical decisions. Consider the company stage, project type, priorities, architectural principles, and business context to make appropriate technical decisions that align with the project's needs and constraints.

**STRATEGIC PRIORITIZATION REQUIREMENTS:**
- **Complexity Audit**: For every decision point, ask: "What's the maintenance and debugging cost of this complexity?"
- **Problem Validation**: Are we solving a real problem or creating unnecessary complexity? Could we document limitations instead?
- **Dependency Assessment**: Highlight decisions that would introduce new technologies, external services, or significant maintenance overhead
- **Risk vs. Value Trade-offs**: Consider whether complex features are essential for ${isExperimental ? 'experiment validation' : 'initial user value'} or add complexity without proportional benefit
- **Constitution Alignment**: Ensure decisions support the primary architectural priority (${topPriority.replace(/-/g, ' ')}) and company stage needs
- **Simplicity Bias**: When in doubt, choose the simpler approach that can be evolved later rather than the complex approach that handles all cases upfront
${isExperimental ? '- **Reversibility Check**: For experimental features, every decision must pass the test: "Can this be completely removed if the experiment fails?"' : ''}

---

**YOUR TASK:**

Based *only* on the context provided above, generate a JSON object with the following keys: "critical_technical_decision_points", "core_vs_deferrable_analysis", and "strategic_recommendations".

1.  **critical_technical_decision_points**: Identify 3-5 of the most important, contentious, or foundational technical decisions that must be made. ${isExperimental ? 'For experimental features, focus on UI behavior, feature flags, configuration, and API decisions - avoid data model or architectural decisions.' : 'These are not the solutions themselves, but the *topics* that need a decision. Focus on decisions that significantly impact maintenance burden, complexity, or long-term technical debt.'}

2.  **core_vs_deferrable_analysis**: Analyze the functional requirements to identify which are core (${isExperimental ? 'experiment validation' : 'initial user value'}) vs. deferrable (can be implemented later without blocking ${isExperimental ? 'the experiment' : 'core functionality'}). ${isExperimental ? 'For experimental features, strongly question any requirement that needs data model changes or architectural modifications.' : 'Strongly question complex requirements that might be edge cases.'}

3.  **strategic_recommendations**: Provide specific guidance on what should be prioritized or deferred based on complexity, maintenance burden, and constitution context. ${isExperimental ? 'For experimental features, focus on what can be implemented quickly and reversed easily.' : ''}

**JSON Schema:**
{
  "critical_technical_decision_points": [
    {
      "id": "A unique identifier, e.g., 'tdp_001'.",
      "decision_point_title": "A short, focused title for the core decision, not a question (e.g., '${isExperimental ? 'Feature Flag Implementation Strategy' : 'Real-time Client Update Strategy'}'). Keep it concise and under 10 words.",
      "decision_point_description": "A concise, single-sentence explanation of *why* this decision is critical for THIS specific task. Focus on the core challenge or trade-off. Use direct, concrete language. Example: '${isExperimental ? 'Feature flag implementation affects iteration speed and experiment reversibility.' : 'Client update strategy directly impacts scalability for millions of users.'}'",
      "category": "A category from the list: '${isExperimental ? 'technology, integration, security, performance' : 'architecture, technology, data, integration, security, performance'}'. ${isExperimental ? 'Note: avoid architecture and data categories for experimental features.' : ''}",
      "priority": "A priority from the list: 'high', 'medium', 'low'.",
      "complexity_risk": "A risk level from the list: 'low', 'medium', 'high' - based on implementation complexity, maintenance burden, debugging difficulty, and potential for introducing new dependencies. ${isExperimental ? 'For experimental features, bias toward low complexity.' : ''}",
      "user_value_impact": "A value level from the list: 'critical', 'important', 'nice-to-have' - based on direct impact on ${isExperimental ? 'experiment validation and user learning' : 'core user value proposition'}.",
      "maintenance_burden_notes": "Brief assessment of what makes this decision easy or hard to maintain long-term ${isExperimental ? 'and how easily it can be reversed if the experiment fails ' : ''}(e.g., '${isExperimental ? 'Simple feature flag toggle with no data dependencies' : 'Adds new service dependency requiring monitoring'}' or 'Standard pattern with established tooling')"
    }
  ],
  "core_vs_deferrable_analysis": {
    "core_requirements": [
      {
        "requirement": "string - A functional requirement that is essential for ${isExperimental ? 'experiment validation' : 'initial user value'}",
        "justification": "string - Why this cannot be deferred without blocking ${isExperimental ? 'the experiment' : 'core functionality'}",
        "simplicity_assessment": "string - Whether this can be implemented simply${isExperimental ? ' with existing infrastructure and no data model changes' : ' or requires complex solutions'}"
      }
    ],
    "deferrable_requirements": [
      {
        "requirement": "string - A functional requirement that could be implemented in a later phase",
        "deferral_rationale": "string - Why this can be safely deferred and what it depends on",
        "complexity_concerns": "string - Assessment of maintenance burden, new technologies, or edge cases this would introduce${isExperimental ? ', especially data model or architectural changes' : ''}",
        "limitation_alternative": "string - Whether documenting limitations might be better than implementing this complexity${isExperimental ? ' or if feature flags could handle this gracefully' : ''}"
      }
    ]
  },
  "strategic_recommendations": {
    "prioritize_immediately": [
      "string - Specific recommendations for what should be prioritized based on company stage, project type, and low complexity${isExperimental ? ', focusing on reversible UI and configuration changes' : ''}"
    ],
    "consider_deferring": [
      "string - Specific recommendations for what could be deferred to reduce complexity, maintenance burden, or accelerate initial delivery${isExperimental ? ', especially anything requiring data model or architectural changes' : ''}"
    ],
    "complexity_concerns": [
      "string - Specific areas where new dependencies, technologies, or edge case handling might introduce unnecessary complexity for the current stage${isExperimental ? ', with special attention to irreversible changes' : ''}"
    ],
    "simplicity_opportunities": [
      "string - Areas where documenting limitations${isExperimental ? ', using feature flags,' : ''} or using simpler approaches might be better than building complex solutions"
    ]
  }
}

**Instructions:**
- Focus on the *most critical* decisions that significantly impact system complexity and maintenance burden. Avoid trivial choices.
- **Complexity Skepticism:** For each decision point, question whether the complexity is truly necessary or if simpler approaches could work.
- **Maintenance Reality Check:** Consider the long-term cost of maintaining, debugging, and evolving each decision.
- **Clarity and Conciseness:** Keep titles as short, focused statements and descriptions as single, high-impact sentences. The goal is to make them easy to grasp instantly.
- **Direct Language:** Avoid vague phrases like "this choice", "this decision", or "this approach". Use concrete subjects like "authentication method", "data storage strategy", or "API design" instead.
- **Constitution Alignment:** Ensure the decision points align with the company stage, project type, priorities, and architectural principles. For example, if the company is in early-stage, don't focus on enterprise-scale decisions unless warranted.
- **Strategic Lens:** Always consider whether complexity or new dependencies are justified by immediate ${isExperimental ? 'experimental validation' : 'user value'} needs, or if they're premature optimization.
${isExperimental ? '- **Experimental Focus:** Heavily bias toward decisions that can be implemented quickly, tested easily, and reversed completely if needed.' : ''}
- Base your entire output strictly on the provided context. Do not invent requirements or decisions.

Begin the JSON output now.
`;
}

// --- NEW PROMPT FOR SEARCH QUERY GENERATION ---
/**
 * Generates a prompt to create specific search queries for a given technical decision point.
 * These queries are intended for retrieving relevant knowledge from an architectural decision store (e.g., Pinecone).
 *
 * @param {object} decisionPointDetails - Details of the specific decision point.
 * @param {string} decisionPointDetails.title - Title of the decision point from Phase 1.
 * @param {string} decisionPointDetails.description - Description of the decision point from Phase 1.
 * @param {string[]} decisionPointDetails.potential_impact_areas - Impact areas from Phase 1.
 * @param {object} overallTaskContext - Context of the overall task.
 * @param {string} overallTaskContext.taskTitle - The title of the overall task.
 * @param {string} overallTaskContext.taskDescription - The description of the overall task (a snippet might be sufficient).
 * @returns {string} - Prompt for the LLM to generate search queries.
 */
export function generateSearchQueriesForDecisionPointPrompt(decisionPointDetails, overallTaskContext) {
  console.log(`[generateSearchQueriesForDecisionPointPrompt] Generating for decision: "${decisionPointDetails.title}"`);
  if (!decisionPointDetails || !decisionPointDetails.title || !decisionPointDetails.description || !overallTaskContext || !overallTaskContext.taskTitle) {
    console.error("generateSearchQueriesForDecisionPointPrompt: Missing required parameters.");
    return "Error: Missing required parameters for generating search queries prompt.";
  }

  // Use a snippet of the task description if it's very long
  const taskDescriptionSnippet = overallTaskContext.taskDescription?.substring(0, 500) +
                               (overallTaskContext.taskDescription?.length > 500 ? '...' : '');

  return `You are an expert software architect. Your task is to generate specific search queries for a given technical decision point to retrieve relevant knowledge from an architectural decision store.

**Overall Task Context:**
*   Task Title: ${overallTaskContext.taskTitle} 
*   Task Description: ${taskDescriptionSnippet}

**Critical Technical Decision Point to Generate Queries For:**
*   Title: ${decisionPointDetails.title}
*   Description: ${decisionPointDetails.description}
*   Potential Impact Areas: ${(Array.isArray(decisionPointDetails.potential_impact_areas) ? decisionPointDetails.potential_impact_areas.join(', ') : 'N/A')}

**Instructions:**

1.  Analyze the **Critical Technical Decision Point** details above.
2.  Generate 3-5 distinct search query strings (each typically 2-5 words long).
3.  These queries should be highly specific and designed to find past architectural decisions, established patterns, technology choices, or developer guidance directly relevant to solving THIS decision point.
4.  Aim for queries that reflect a level of detail similar to hierarchical domain concepts (e.g., "Category Pattern SpecificTechnology", "ProblemArea SolutionPattern"). For example, instead of a generic query like "database", aim for "user profile data storage NoSQL" or "real-time leaderboard implementation Redis".
5.  Prioritize terms that highlight architectural significance, trade-offs, or specific technologies/patterns.

**Output Format:**

Provide your response as a JSON object with a single key "search_queries". The value should be an array of strings, where each string is a search query.

Example:
{
  "search_queries": [
    "microservice authentication JWT OIDC",
    "asynchronous task processing RabbitMQ",
    "data sharding strategy user accounts",
    "API gateway selection Kong vs Apigee",
    "client-side state management Redux performance"
  ]
}

Please provide ONLY the JSON object in your response.`;
}

// --- PHASE 2 PROMPT ---
/**
 * Generates a prompt for Phase 2 of design document generation:
 * Analyzing a specific technical decision point with context from past decisions.
 *
 * @param {object} currentDecisionPoint - The technical decision point from Phase 1 to be analyzed.
 * @param {string} currentDecisionPoint.id - ID of the decision point.
 * @param {string} currentDecisionPoint.decision_point_title - Title of the decision point.
 * @param {string} currentDecisionPoint.decision_point_description - Description of the decision point.
 * @param {object[]} pastDecisionsContext - Array of relevant past decision summaries.
 * @param {string} pastDecisionsContext[].id - Pinecone ID of the past decision.
 * @param {string} pastDecisionsContext[].title - Title of the past decision.
 * @param {string} pastDecisionsContext[].description - Short description of the past decision.
 * @param {number} pastDecisionsContext[].score - Relevance score from semantic search.
 * @param {string} taskTitle - The title of the overall task.
 * @param {string} taskDescription - The description of the overall task.
 * @returns {string} - Prompt for the LLM.
 */
export function generatePhase2DecisionContextPrompt(currentDecisionPoint, pastDecisionsContext, taskAnalysis, projectConstitution, isExperimental = false, deploymentConstitution = null) {

  // Format the context from past decisions for the prompt
  const formattedPastDecisions = (pastDecisionsContext && pastDecisionsContext.length > 0)
    ? pastDecisionsContext.map(d => 
`- **Past Decision:** "${d.title}" (ID: ${d.id})
  - **Guidance:** ${d.dev_prompt || "N/A"}
  - **Implications:** ${d.implications || "N/A"}
  - **Patterns:** ${d.follows_standard_practice_reason || "N/A"}`
      ).join('\n')
    : "No directly relevant past decisions were found.";

  // Format requirements from task analysis
  const formattedRequirements = taskAnalysis.functional_requirements?.map(req => `- ${req}`).join('\n') || '';
  const formattedNFRs = taskAnalysis.implied_NFRs.map(nfr => `- ${nfr.nfr}`).join('\n');
  const constitutionContext = formatCompleteConstitution(projectConstitution);
  const primaryLanguage = projectConstitution?.primaryLanguage || 'Not specified';
  const projectType = projectConstitution?.projectType || 'not-set';
  const companyStage = projectConstitution?.companyStage || 'not-set';

  // --- ADDED: Format Deployment Constitution for Decision Context ---
  let deploymentContextSection = '';
  if (deploymentConstitution) {
    const formattedContext = formatDeploymentConstitution(deploymentConstitution, true);
    if (formattedContext) {
      deploymentContextSection = `\n**🚀 DEPLOYMENT ENVIRONMENT CONTEXT (CRITICAL FOR TECHNICAL DECISIONS):**\n${formattedContext}`;
    }
  } else {
    deploymentContextSection = '\n**🚀 DEPLOYMENT ENVIRONMENT:** No deployment context available - evaluate options using general best practices.\n';
  }
  // --- END ADDED ---

  // Special library guidance
  const libraryAnalysisGuidance = projectType === 'library' ? `

**🔬 LIBRARY-SPECIFIC ANALYSIS REQUIREMENTS:**
For every option you analyze, you MUST address:
- **Boundary Responsibility**: Should this complexity live in the library or be the application's responsibility?
- **User Impact**: How does this option affect ALL library users, not just the immediate use case?
- **Maintenance Burden**: Who maintains this complexity when edge cases arise? Library maintainers or application developers?
- **Documentation vs Implementation**: Could we document limitations instead of implementing complex handling?
- **API Surface**: Does this option unnecessarily expand the library's API surface area?
` : '';

  // Add experimental feature constraints that override other constraints
  const experimentalConstraints = isExperimental ? `

🧪 **EXPERIMENTAL FEATURE CONSTRAINTS (OVERRIDES ALL OTHER CONSTRAINTS):**
- **NO DATA MODEL CHANGES**: Options should minimize database schema changes, new tables, or data migrations but not avoid them if necessary
- **NO ARCHITECTURAL CHANGES**: Avoid options that change core system architecture, service boundaries, or fundamental patterns
- **FEATURE FLAG FRIENDLY**: All options must be easily reversible through feature flags or configuration
- **UI/CONFIGURATION FOCUSED**: Prefer options about UI behavior, display logic, configuration, or API endpoints
- **EXISTING INFRASTRUCTURE**: Use existing services, databases, and infrastructure - do not introduce new dependencies
- **EASY REMOVAL**: Every option should consider: "How easily can this be completely removed if the experiment fails?"
- **MINIMAL SCOPE**: Focus on the smallest possible set of changes that can validate the core hypothesis
- **RAPID ITERATION**: Prioritize options that enable quick changes based on user feedback

**EXPERIMENTAL OPTION CATEGORIES TO FOCUS ON:**
- **User Interface**: Display logic, component behavior, layout decisions
- **Feature Flags**: Configuration and toggling mechanisms
- **API Endpoints**: Simple endpoints that don't require new data models
- **Business Logic**: Processing rules that can be easily modified or removed
- **Integration Points**: How to connect to existing services without architectural changes
- **Monitoring**: How to measure experiment success without complex analytics infrastructure

**EXPERIMENTAL OPTION CATEGORIES TO AVOID:**
- **Core Architecture**: Service boundaries, fundamental patterns, system structure
- **Infrastructure**: New services, databases, message queues
- **Security Models**: Authentication, authorization, or permission changes
- **Performance Optimization**: Caching strategies, database optimization, or system-wide performance changes
` : '';

  // Build the complete prompt - avoiding template literals within template literals
  let prompt = `
You are a pragmatic, diligent, and deeply experienced Staff-level Software Architect. Your task is to analyze a specific, isolated technical decision point. Your recommendations carry significant weight and must be technically sound, rigorously justified, and free of unexamined assumptions.

`;

  // Add analysis principles based on experimental status
  if (isExperimental) {
    prompt += `
**EXPERIMENTAL FEATURE ANALYSIS PRINCIPLES:**
1.  **Validation-First Thinking**: Every option should help validate user hypotheses or business assumptions quickly. Ask: "What can this teach us about user behavior or product-market fit?"
2.  **Reversibility Bias**: The best option is often the one that can be completely turned off or removed without breaking anything. Prefer approaches that are easy to disable and iterate on.
3.  **Speed Over Perfection**: Prioritize options that can be implemented quickly for rapid validation. Perfect solutions are less important than fast learning.
4.  **Configuration Over Code**: Prefer options that use configuration, feature flags, and existing infrastructure rather than complex new code.
5.  **Learning Optimization**: Optimize for learning rate and user feedback quality, not system performance or scalability.

** EXPERIMENTAL ARCHITECTURAL ALIGNMENT PRINCIPLES:**
1.  **Minimal Change Bias**: STRONGLY favor options that require the fewest changes to existing systems. Experimental features should sit "on top" of existing infrastructure whenever possible.
2.  **Configuration Extension**: Prioritize options that extend existing configuration systems, feature flags, or UI components rather than creating new architectural patterns.
3.  **Existing Pattern Reuse**: Reuse established patterns, libraries, and conventions. Experimental features should leverage existing capabilities rather than introducing new ones.
4.  **Reversible Integration**: Every option should integrate with existing systems in a way that can be easily undone. Avoid deep integration that would be hard to unwind.
5.  **Feature Flag Compatibility**: All options must be compatible with feature flag systems and gradual rollouts. Options that require "all or nothing" deployments are not suitable for experiments.
`;
  } else {
    prompt += `
**CRITICAL ANALYSIS PRINCIPLES (Follow these religiously):**

1.  **Maintenance-First Thinking**: Every technical choice creates a long-term maintenance burden. Complex solutions require complex debugging, monitoring, documentation, and team knowledge. Always ask: "What's the total cost of ownership for this approach over 2-3 years?"

2.  **Simplicity Bias**: The best solution is often the simplest one that meets the actual need. Prefer approaches that are easy to understand, debug, and modify. Question whether sophisticated solutions are solving real problems or imagined ones.

3.  **Problem Validation**: Are we solving a problem that actually exists, or are we building complexity for theoretical future needs? Sometimes documenting limitations is far better than implementing complex edge case handling.

4.  **Debugging Reality**: Complex solutions are hard to debug when they break. Factor in the cost of troubleshooting, testing, and supporting each approach. Simple solutions break in simple ways.

**🏗️ ARCHITECTURAL ALIGNMENT PRINCIPLES (CRITICAL - Follow these first):**

1.  **Incremental Architecture Bias**: STRONGLY favor options that build upon existing patterns, extend current systems, or make minimal architectural changes. Revolutionary approaches should only be considered if incremental options are demonstrably inadequate.

2.  **Pattern Leverage**: Prioritize options that reuse established patterns, libraries, conventions, and architectural decisions already proven in this codebase. Every deviation from established patterns must be explicitly justified.

3.  **Minimal Change Principle**: The best architectural decision is often the one that achieves the goal with the least disruption to existing systems. Evaluate options based on how much they change vs. how much they leverage existing infrastructure.

4.  **Guidance Adherence**: Past decisions contain explicit developer guidance (dev_prompt) and established patterns. Options that align with this guidance should be strongly preferred unless there's compelling evidence they're inadequate.

5.  **Architectural Consistency**: Maintain consistency with existing architectural principles, data models, naming conventions, and system boundaries. Flag any option that would create architectural drift or inconsistency.
`;
  }

  // Continue building the prompt
  prompt += `

**FULL PROJECT CONTEXT:**
- **Task Title:** ${taskAnalysis.summary}
- **Experimental Feature:** ${isExperimental ? 'YES - Focus on reversible, feature-flag-friendly options' : 'NO - Standard feature development'}
- **Core Functional Requirements:**
${formattedRequirements}
- **Core Non-Functional Requirements (NFRs):**
${formattedNFRs}${deploymentContextSection}

**ARCHITECTURAL REVIEW PRINCIPLES (Adhere to these strictly):**

1.  **Diligence and Accuracy:** Vet all technical claims. If an option claims to improve performance, maintain backward compatibility, or enhance security, you must critically question and validate this claim based on the provided context and first principles. State any unverified assumptions clearly in your analysis. Avoid making false or inaccurate technical statements.

2.  **Constitutional Alignment:** Every pro and con for every option must be weighed against the **Project Constitution**. Explicitly connect your reasoning to the project's ranked priorities (e.g., time-to-market vs. reliability), architectural principles, company stage, and project type. The **Primary Language** (${primaryLanguage}) should inform your evaluation of options, especially regarding language-specific idioms, libraries, or ecosystem conventions.

3.  **Pragmatism and Context:** The "best" solution is highly contextual. Your recommendation must be the most appropriate choice given the project's unique constraints. The most technically elegant solution is not always the right one. Acknowledge trade-offs directly and justify why the chosen path is the most prudent.

4.  **Maintenance Burden Assessment**: For every option, explicitly evaluate:
   - How hard will this be to debug when it breaks?
   - What knowledge must the team maintain to support this?
   - How will this complexity affect future changes?
   - What dependencies or external services does this introduce?`;

  if (isExperimental) {
    prompt += `
   - How easily can this be completely removed if the experiment fails?`;
  }

  prompt += `

5.  **Deployment Context Alignment**: ${deploymentConstitution ? 'Given the deployment environment context above,' : 'Without specific deployment context,'} for every option explicitly evaluate:
   - How does this option align with or deviate from the existing deployment infrastructure?
   - What operational complexity does this introduce (monitoring, scaling, debugging in production)?
   - ${deploymentConstitution ? 'Does this leverage existing data stores, compute environments, and deployment pipelines?' : 'What new infrastructure dependencies does this create?'}
   - What is the total cost of ownership considering deployment, monitoring, and operational overhead?

**CRITICAL GUIDANCE:** Use the complete constitution${deploymentConstitution ? ', deployment context,' : ''} and the review principles above to ensure you don't over-engineer or under-engineer this decision.
`;

  if (isExperimental) {
    prompt += `For experimental features, question whether complex solutions are solving real validation needs or creating unnecessary complexity that makes the experiment harder to manage.`;
  } else {
    prompt += `Question whether complex solutions are solving real problems or creating unnecessary maintenance burden.`;
  }

  prompt += libraryAnalysisGuidance + experimentalConstraints + `

---

**CURRENT DECISION POINT TO ANALYZE:**
- **Title:** "${currentDecisionPoint.title}"
- **Description:** ${currentDecisionPoint.description}
- **Initial Proposal from Team (if any):** ${currentDecisionPoint.initial_proposal_to_evaluate || "None provided. You must generate options."}

**RELEVANT PAST DECISIONS (from knowledge base):**
${formattedPastDecisions}

---

**YOUR TASK:**

Generate a structured JSON analysis for the **CURRENT DECISION POINT ONLY**, adhering to the principles above.

1.  **Evaluate the Initial Proposal:**
    - If an initial proposal exists, assess its strengths and weaknesses with extreme diligence, following the **Architectural Review Principles**.
    - Determine how it aligns with or conflicts with the project context, constitution, and past decisions.
    - Critically evaluate its maintenance burden and debugging complexity.`;

  if (isExperimental) {
    prompt += `
    - For experimental features, assess how easily the proposal can be reversed if the experiment fails.`;
  }

  prompt += `

2.  **Generate Potential Approaches (Options):**
    - Generate 2-3 distinct, viable technical approaches to solve the problem described in the decision point. Aim for 3 when there are genuinely different architectural approaches; use 2 when the solution space is more constrained.
    - **PRIORITIZE ${isExperimental ? 'REVERSIBLE' : 'INCREMENTAL'} OPTIONS**: Start with approaches that ${isExperimental ? 'can be easily enabled/disabled via feature flags and require minimal architectural changes' : 'extend/build upon existing patterns and guidance from past decisions'}.
    - One of these options MUST be the "Initial Proposal", if one was provided.
    - For each approach, provide a brief description, a list of pros, a list of cons, and assess its risk and complexity.
    - **${isExperimental ? 'Reversibility' : 'Architectural Alignment'} Required**: For each option, explicitly assess ${isExperimental ? 'how easily it can be reversed and how it avoids architectural changes' : 'how it aligns with or deviates from established patterns and past decisions'}.
    - **Maintenance Analysis Required**: For each option, explicitly address ${isExperimental ? 'short-term maintenance burden and reversibility complexity' : 'long-term maintenance burden, debugging complexity, and team knowledge requirements'}.
    - **Mitigation Analysis Required**: For the recommended option ONLY, provide brief, actionable mitigations for each con. Each mitigation should explain how the concern can be addressed or why it's acceptable given the project context.
    - Ensure your analysis for each option is consistent with the **Architectural Review Principles**.

**CRITICAL: SCANNABLE PROS/CONS FORMATTING**

For all pros and cons, use CLEAR, SCANNABLE language that gives developers sufficient context for architectural decisions:

**✅ GOOD Pros Examples:**
- "Reduces system complexity by eliminating state management"
- "Leverages existing auth system with proven reliability"
- "Zero deployment risk - can be feature-flagged"
- "Team familiar with this pattern from past projects"
- "Debuggable with standard tools and established patterns"
- "Performance improvement: 40% faster query times"

**✅ GOOD Cons Examples:**
- "Requires new Redis dependency for caching"
- "Increases memory usage by ~20% for session storage"
- "Breaking API change requires client migration"
- "Complex edge case handling for offline scenarios"
- "No rollback mechanism without data loss risk"
- "Higher maintenance burden due to distributed state"

**❌ BAD Examples (too verbose):**
- "This approach would significantly reduce the overall system complexity by eliminating the need for complex state management and reducing the number of moving parts that could potentially fail"
- "The solution leverages our existing authentication infrastructure which has been battle-tested and proven reliable in production environments over the past several years"

**FORMATTING RULES:**
1. **Max 12 words per bullet**: Provide clear context while staying concise
2. **Specific metrics**: Include numbers when possible (40% faster, 3 new dependencies)
3. **Clear impact**: State the direct effect and brief reasoning
4. **Active voice**: Use "Reduces complexity by eliminating X" not "Complexity would be reduced"
5. **Developer-focused**: Address debugging, deployment, maintenance, and team concerns
6. **Context-rich**: Provide enough information for informed architectural decisions

3.  **Provide a Defensible Recommendation:**
    - Recommend one of the generated approaches.
    - Your recommendation rationale must be a comparative argument that explicitly explains why this option is superior to the rejected alternatives. Address the specific advantages over other options and why their benefits don't outweigh the chosen approach's strengths.
    - **${isExperimental ? 'Reversibility' : 'Architectural Alignment'} Priority**: Strongly weight options that ${isExperimental ? 'can be easily reversed and require minimal architectural changes' : 'build upon existing patterns and require minimal architectural changes'}.
    - **Maintenance Reality**: Your recommendation should explicitly factor in ${isExperimental ? 'experiment management and reversal costs' : 'long-term maintenance costs'}, not just implementation speed.
    - **Past Decision References**: Your recommendation rationale MUST reference specific past decisions by their complete ID when they influenced your choice. Use the format "Decision decision_commit_xyz..." or "Decision decision_123_xyz..." with the full decision ID exactly as it appears in the knowledge base. Do not use shortened formats or add prefixes. Explain how the recommended option aligns with or builds upon these established patterns.
    - **Direct Comparison Required**: Each rationale bullet should compare against specific alternatives (e.g., "Unlike Option A which requires X, this approach leverages Y" or "Provides better Z than Option B while maintaining same W").

**JSON OUTPUT SCHEMA:**
{
  "options": [
    {
      "id": "A unique identifier for the option (e.g., 'option-1').",
      "name": "A short, descriptive name for the approach.",
      "description": "A 1-2 sentence technical summary of the approach.",
      "pros": ["A list of 2-4 concise benefits specific to this project's context and constitution. Use max 6 words per bullet, include metrics when possible (e.g., 'Reduces complexity by 50%', 'Leverages existing auth system', 'Zero deployment risk')."],
      "cons": ["A list of 2-4 concise drawbacks specific to this project's context and constitution. Use max 6 words per bullet, include metrics when possible (e.g., 'Requires new dependency', 'Increases memory usage 20%', 'Breaking API change')."],
      "mitigations": ["ONLY for the recommended option: Brief, actionable mitigation for each con. Each mitigation should be 1-2 sentences explaining how the con can be addressed or why it's acceptable (e.g., 'Dependency risk mitigated by using battle-tested library with strong community support', 'Memory increase acceptable given performance gains and current resource headroom')."],
      "riskLevel": "low | medium | high",
      "complexity": "low | medium | high", 
      "maintenanceBurden": "low | medium | high",
      "debuggingComplexity": "Brief assessment of troubleshooting difficulty",
      "architecturalAlignment": "high | medium | low",
      "alignmentJustification": "REQUIRED: Specific explanation citing past decisions by their complete ID. Explain how this option aligns with or deviates from established patterns. Example: 'Extends the hashToken pattern from Decision decision_commit_a1b2c3d4_1693247798000_1752571762511_xyz789 for user authentication, reusing proven security infrastructure established in that decision.'",
      "changeScope": "minimal | moderate | significant",
      "isBasedOnInitialProposal": "boolean"
    }
  ],
  "recommended_option_id": "The ID of the recommended option.",
  "recommendation_rationale": [
    "REQUIRED: Comparative justification bullets explaining why this choice is superior to the other options. Each bullet must directly compare against alternatives and reference past decisions by their complete ID when applicable. Example: 'Builds upon the established hashToken security pattern from Decision decision_commit_a1b2c3d4_1693247798000_1752571762511_xyz789 unlike Option B which introduces new authentication patterns', 'Provides 40% better performance than Option A while maintaining same reliability as Option C', 'Offers easier debugging than alternatives due to established monitoring patterns from Decision decision_1342_1752529271439_e9fb892b.' Always explain what makes this option better than the specific rejected alternatives."
  ],
  "maintenance_analysis_summary": "Summary of maintenance implications",
  "architectural_alignment_summary": "REQUIRED: Comparative analysis of alignment levels citing specific past decisions by their complete ID. Explain why one option has higher alignment than another by referencing specific decisions by their full ID. Example: 'Option A has high alignment because it extends the database token pattern from Decision decision_commit_a1b2c3d4_1693247798000_1752571762511_xyz789, while Option B has medium alignment as it introduces new concepts not seen in past decisions.'",
  "initial_proposal_assessment": "Assessment of initial proposal or N/A"
}

**CRITICAL INSTRUCTIONS FOR ${isExperimental ? 'EXPERIMENTAL FEATURE' : 'ARCHITECTURAL'} ALIGNMENT:**

1. **2-3 OPTIONS:** You MUST generate 2-3 options. Use 3 when there are genuinely different architectural approaches (e.g., different patterns, technologies, or design philosophies). Use 2 when the solution space is more constrained. This ensures thorough analysis without overwhelming developers.

2. **DIFFERENTIATE RATINGS:** You MUST NOT give all options the same alignment rating.

3. **ALIGNMENT CRITERIA (be specific):**`;

  if (isExperimental) {
    prompt += `
   - **HIGH (use sparingly):** Option uses existing infrastructure with minimal changes, easy to reverse via feature flags, follows established patterns without architectural modifications.
   - **MEDIUM (most common):** Option introduces some new concepts but can be reversed, builds on familiar patterns but may require some new configuration or minor code changes.
   - **LOW (when applicable):** Option requires architectural changes, introduces new dependencies, or would be difficult to reverse without significant system modifications.`;
  } else {
    prompt += `
   - **HIGH (use sparingly):** Option directly extends existing patterns found in past decisions, reuses established libraries/tools mentioned in context, follows explicit dev_prompt guidance from past decisions, requires minimal new learning.
   - **MEDIUM (most common):** Option introduces some new concepts but doesn't conflict with established practices, builds on familiar patterns but in new ways, requires moderate new learning but leverages existing knowledge.
   - **LOW (when applicable):** Option introduces entirely new patterns not seen in past decisions, conflicts with established practices or explicit guidance, requires significant new tooling/skills/concepts that deviate from established patterns.`;
  }

  prompt += `

4. **JUSTIFICATION REQUIREMENTS:** Your alignmentJustification MUST reference specific past decisions by their complete ID when available. Use the full decision ID format like "decision_commit_xyz..." or "decision_123_xyz..." `;

  if (isExperimental) {
    prompt += `For experimental features, also explain how the option maintains reversibility and avoids architectural changes.`;
  } else {
    prompt += `Don't just say "aligns with existing patterns" - cite which specific decisions and explain how.`;
  }

  prompt += `

5. **COMPARATIVE ANALYSIS:** In your architectural_alignment_summary, explicitly compare the alignment levels of different options and explain the architectural trade-offs between choosing higher vs lower alignment approaches.

Begin the JSON output now.
`;

  return prompt;
}

// --- PHASE 3 PROMPT ---
/**
 * Generates a prompt for Phase 3 of design document generation:
 * Creating the full design document incorporating all prior analysis.
 * This is an evolution of the original generateDesignDocPrompt.
 *
 * @param {object} taskDetails - Original task details.
 * @param {string} taskDetails.taskTitle
 * @param {string} taskDetails.taskDescription
 * @param {string} [taskDetails.initialApproachIdeas]
 * @param {object} taskAnalysis - The full task analysis object from the initial step.
 * @param {object} phase1Data - Output from Phase 1.
 * @param {object[]} phase1Data.critical_technical_decision_points
 * @param {object[]} phase2Data - Array of outputs from Phase 2 (one for each decision point). Each element contains analysis for a TDP.
 * @param {object[]} fullPastDecisionsDetails - Array of full decision objects (id, title, description, rationale, implications, etc.) for all unique past decisions referenced in Phase 2.
 * @param {object[]} projectConstraints - Array of project constraints including "already decided" context from removed decisions.
 * @param {object} projectConstitution - The complete project constitution for strategic guidance.
 * @returns {string} - Prompt for the LLM.
 */
export function generatePhase3FullDesignDocPrompt(taskDetails, taskAnalysis, phase1Data, phase2Data, fullPastDecisionsDetails, projectConstraints = [], projectConstitution = null, deploymentConstitution = null) {
  const { taskTitle, taskDescription } = taskDetails;
  
  const summarizedPastDecisions = fullPastDecisionsDetails.map(decision => {
    const metadata = decision.metadata || decision; // Accommodate different structures
    const summary = {
      id: decision.id || metadata.pinecone_id,
      title: metadata.title,
      dev_prompt: metadata.dev_prompt,
      follows_standard_practice_reason: metadata.follows_standard_practice_reason,
      data_model_changes: metadata.data_model_changes,
    };
    // Remove undefined/null fields to keep it clean
    Object.keys(summary).forEach(key => (summary[key] === undefined || summary[key] === null) && delete summary[key]);
    return summary;
  });

  // Format project constraints for the prompt
  const constraintsContext = projectConstraints && projectConstraints.length > 0
    ? `\nProject Constraints (including "already decided" context):\n${JSON.stringify(projectConstraints, null, 2)}\n`
    : '';

  // Format constitution context for strategic guidance
  const constitutionContext = projectConstitution ? formatCompleteConstitution(projectConstitution) : 'No constitution provided.';
  
  // --- ADDED: Format Deployment Constitution for Technical Guidance ---
  let deploymentContextSection = '';
  if (deploymentConstitution) {
    const formattedContext = formatDeploymentConstitution(deploymentConstitution);
    if (formattedContext) {
      deploymentContextSection = `\n**Current Deployment Environment (for technical decision guidance):**\n${formattedContext}\n\n**CRITICAL: Use this deployment context to make practical technical decisions.**\n- When suggesting infrastructure choices, prefer extending existing stack over introducing new technologies\n- When recommending data storage, consider existing data stores first\n- When planning rollout, align with existing deployment pipeline patterns\n- When assessing complexity, factor in operational overhead of new vs. existing technologies\n`;
    }
  } else {
    deploymentContextSection = '\n**Deployment Environment:** No deployment context available - recommendations will use general best practices.\n';
  }
  // --- END ADDED ---
  
  // Extract strategic guidance elements
  const companyStage = projectConstitution?.companyStage || 'not-set';
  const projectType = projectConstitution?.projectType || 'not-set';
  const topPriority = projectConstitution?.priorities?.[0] || 'time-to-market';
  
  // Generate stage-specific milestone guidance
  const stageGuidance = {
    'startup': 'Prioritize milestones that prove core value proposition fastest. Defer complex features that don\'t directly impact user validation.',
    'growth': 'Balance feature completeness with scalability needs. Structure milestones to allow for early user feedback and iteration.',
    'mature': 'Prioritize reliability and maintainability. Ensure milestones include proper testing and monitoring from the start.'
  };
  
  const projectTypeGuidance = {
    'b2c-saas': 'Focus on user experience milestones. Defer administrative or complex integration features.',
    'b2b-saas': 'Focus on core business workflow milestones. Defer advanced customization or edge case features.',
    'library': 'Focus on core API stability milestones. Defer complex integrations or advanced features.',
    'internal-tool': 'Focus on operational efficiency milestones. Defer features that don\'t directly impact team productivity.',
    'data-platform': 'Focus on data integrity and processing milestones. Defer complex analytics or visualization features.'
  };

  const currentStageGuidance = stageGuidance[companyStage] || 'Structure milestones to deliver value incrementally.';
  const currentProjectTypeGuidance = projectTypeGuidance[projectType] || 'Align milestones with project goals and user needs.';

  return `Generate a comprehensive but concise design document in JSON format for the following task. Focus on high-value information and avoid redundancy across sections.

Task Title: ${taskTitle}
Task Description: ${taskDescription}

Task Analysis:
${JSON.stringify(taskAnalysis, null, 2)}

Phase 1 Output (Decision Points and Strategic Analysis):
${JSON.stringify(phase1Data, null, 2)}

Phase 2 Output (Decision Analysis):
${JSON.stringify(phase2Data, null, 2)}

Past Decisions Referenced (contains full details including any schema/data models from past decisions):
${JSON.stringify(summarizedPastDecisions, null, 2)}${constraintsContext}

Project Constitution (Strategic Context):
${constitutionContext}${deploymentContextSection}

Strategic Milestone Guidance for ${companyStage.toUpperCase()} ${projectType.toUpperCase()}:
- Company Stage: ${currentStageGuidance}
- Project Type: ${currentProjectTypeGuidance}
- Top Priority: ${topPriority.replace(/-/g, ' ')}

Guidelines for the design document:
1.  **Goals and Non-Goals:** Based on the full context provided, define 2-4 primary goals and 1-3 specific non-goals.
2.  Keep all descriptions concise and focused on essential information
3.  Avoid repeating information across sections
4.  Use bullet points and lists where appropriate
5.  Focus on technical details and implementation specifics
6.  For each architectural choice:
   - Clearly state the decision and its rationale
   - Reference any relevant past decisions that influenced the choice, especially how it aligns or diverges from existing data models or patterns.
   - **LEVERAGE DEPLOYMENT CONTEXT:** When making infrastructure or technology choices, explicitly consider the existing deployment environment and justify any deviations
   - Keep justifications brief but complete
   - **Respect existing constraints** - if a constraint indicates a decision is already made, acknowledge this in the analysis
7.  In the overall system overview:
   - Focus on system components and their interactions
   - Avoid repeating details from other sections
   - Use clear, technical language
8.  For alternatives analysis:
   - Focus on key differentiators between options
   - **Consider deployment practicality** - factor in operational complexity of new vs. existing technologies
   - Keep pros/cons brief and specific
   - Clearly state why the chosen approach is better
   - **Respect existing constraints** - if a constraint indicates a decision is already made, acknowledge this in the analysis
9.  **Data Modeling Specifics (CRITICAL):**
   - When defining \`data_model_changes\`, **rigorously analyze the data models present in the 'Past Decisions Referenced'**.
   - **Prioritize extending existing data models/schemas** over creating new ones if the new data logically fits.
   - **Consider existing data stores** from the deployment context - prefer using established data stores over introducing new ones
   - If new tables/collections are necessary, ensure their structure, naming conventions, and data types are **compliant and consistent** with established patterns found in past decisions.
   - Explicitly state if a new data model is an extension of an existing one, or if it's new, why it cannot leverage existing structures.
   - The goal is to **avoid data redundancy and maintain architectural consistency** across the system's data landscape.
10. **Strategic Milestone Prioritization (CRITICAL):**
    - Use the Phase 1 strategic analysis and constitution context to structure milestones
    - **Identify core milestones** that deliver immediate user value and can be implemented with minimal dependencies
    - **Flag dependency risks** where new technologies or external services might introduce maintenance overhead
    - **Align with company stage** - prioritize speed for startups, scalability for growth, reliability for mature companies
    - **Consider project type** - align milestone priorities with the specific user needs and constraints of the project type
    - **Factor deployment complexity** - consider operational overhead of implementing in the existing deployment environment

The response should be a valid JSON object with this structure:

{
  "title": "${taskTitle}",
  "goals": [
    "string - Goal 1..."
  ],
  "non_goals": [
    "string - Non-goal 1..."
  ],
  "high_level_design": {
    "overall_system_overview": "string - Brief technical overview focusing on component interactions",
    "process_flow_diagram_mermaid": "string - Mermaid diagram showing key interactions",
    "core_architectural_choices": [
      {
        "title": "string - Title from Phase 1",
        "recommended_approach_description": "string - Brief, clear description of chosen solution or technology",
        "justification_and_context": "string - Concise explanation with references to Phase 2 analysis and how it aligns with past decisions/data models",
        "new_constraints_introduced": "string - Key technical constraints only"
      }
    ],
    "key_components_and_responsibilities": [
      { "name": "string", "responsibility": "string - One clear sentence" }
    ],
    "data_model_changes": "string - Essential schema changes. Detail how these changes relate to existing data models from \\'Past Decisions Referenced\\'. Specify if extending existing models or creating new ones, with justification for compliance and avoiding redundancy.",
    "security_considerations": "string - Key security measures",
    "error_handling_and_recovery": {
      "critical_error_scenarios": ["string - Key failure modes only"],
      "overall_strategy": "string - Essential error handling approach"
    }
  },
  "alternatives_analysis": {
    "overall_solution_alternatives": [
      {
        "approach_name": "string",
        "description": "string - Brief technical description",
        "pros": ["string - Key advantages only"],
        "cons": ["string - Key disadvantages only"],
        "alignment_with_context": "string - Brief explanation of fit"
      }
    ],
    "key_technical_decision_alternatives": [
      {
        "decision_point_title": "string",
        "alternative_considered": "string - Brief description",
        "reason_not_chosen": "string - Key technical reasons only"
      }
    ],
    "overall_recommendation_and_justification": "string - Brief summary of key decision factors"
  },
  "implementation_strategy": {
    "core_milestones": [
      {
        "milestone_id": "string - e.g., M1, M2",
        "title": "string - Clear milestone title",
        "description": "string - What this milestone delivers",
        "user_value_delivered": "string - Specific user value this milestone provides",
        "priority": "string - critical, high, medium, low",
        "estimated_complexity": "string - low, medium, high",
        "dependencies": ["string - What this milestone depends on"],
        "success_criteria": ["string - How to measure milestone completion"],
        "risks": ["string - Key risks and mitigation strategies"]
      }
    ],
    "strategic_recommendations": {
      "immediate_focus": ["string - What to prioritize based on constitution"],
      "defer_until_later": ["string - What to defer and why"],
      "complexity_concerns": ["string - Areas where new dependencies might introduce unnecessary complexity"],
      "constitution_alignment": "string - How this strategy aligns with company stage and project type"
    }
  },
  "success_metrics": ["string - Essential metrics only"],
  "referenced_decisions": [
    {
      "id": "string - e.g., ADR-007",
      "summary_of_relevance_in_this_design": "string - Brief impact on current design"
    }
  ]
}`;
}

// --- NEW PROMPT FOR DATA MODEL COMPLIANCE ---
/**
 * Generates a prompt for an LLM to validate and refine proposed data model changes
 * against existing data models from past architectural decisions.
 *
 * @param {string|object} draftDataModelChanges - The initial data_model_changes proposed (e.g., from Phase 3 initial draft). This could be a string or a structured object.
 * @param {Array<object>} fullPastDecisionsDetails - Array of full decision objects from the knowledge base, which may contain their own 'data_model_changes' or schema details.
 * @param {object} overallTaskContext - Context of the overall task.
 * @param {string} overallTaskContext.taskTitle - The title of the overall task.
 * @param {string} overallTaskContext.taskDescription - The description of the overall task.
 * @returns {string} - Prompt for the LLM.
 */
export function generateDataModelCompliancePrompt(draftDataModelChanges, fullPastDecisionsDetails, overallTaskContext) {
  const { taskTitle, taskDescription } = overallTaskContext;

  // Safely stringify inputs for inclusion in the prompt
  const draftChangesString = typeof draftDataModelChanges === 'string' ? draftDataModelChanges : JSON.stringify(draftDataModelChanges, null, 2);
  const pastDecisionsString = JSON.stringify(
    fullPastDecisionsDetails.map(pd => ({ // Selectively include relevant parts of past decisions to keep prompt focused
      id: pd.id,
      title: pd.metadata?.title || pd.title, // Handle title from direct object or metadata
      data_model_changes: pd.metadata?.data_model_changes || pd.data_model_changes // Handle data_model_changes
      // Potentially include other relevant metadata like 'description' or 'rationale' if helpful for context
    })),
    null, 2
  );

  return `You are an expert database architect and data modeler with a deep understanding of database normalization, performance tuning, and scalable system design. Your task is to critically review a DRAFT set of data model changes and refine them to ensure long-term architectural health, performance, and maintainability, avoiding unnecessary modifications to core tables.

**Overall Task Context:**
*   Task Title: ${taskTitle}
*   Task Description (brief): ${taskDescription.substring(0, 500)}${taskDescription.length > 500 ? '...' : ''}

**Draft Data Model Changes Proposed for the Task:**
---
${draftChangesString}
---

**Relevant Existing Data Models (extracted from Past Architectural Decisions in the Knowledge Base):**
---
${pastDecisionsString}
---

**Your Detailed Instructions & Core Principles:**

You must adhere to the following principles when refining the data model. Your primary goal is to prevent "migration havoc" and ensure the data architecture remains scalable and maintainable.

1.  **Favor Normalization & New Tables for New Features:**
    *   For new, distinct features, your default approach should be to create **new, well-normalized tables**. This encapsulates the feature's data, preventing bloat in core system tables.
    *   Analyze the relationship between new and existing data. If the new data represents a one-to-many or many-to-many relationship with an existing entity, it **must** be in a new table.

2.  **Justify Any Denormalization or Extension of Core Tables:**
    *   You may only extend an existing table (i.e., add columns) if there is a **compelling, explicitly stated performance reason** to do so (e.g., avoiding a join on a high-frequency, critical query path).
    *   This choice must be defended in the \`summary\` for that model. Simply stating "it's easier" is not an acceptable justification.

3.  **Minimize Migratory Friction on Core Tables:**
    *   Be extremely conservative about modifying existing, high-traffic core tables.
    *   Adding a single, simple, nullable column might be acceptable if fully justified.
    *   Proposing to add multiple columns, complex types (JSONB), or columns that require backfills on large tables should be strongly avoided in favor of creating a new, related table.

4.  **Ensure Consistency and Compliance:**
    *   When you do create new tables, their naming conventions (for tables, fields, foreign keys) and data types **must** be consistent with the patterns observed in the \`Relevant Existing Data Models\`.
    *   Flag and correct any inconsistencies from the original draft.

**Output Format:**

Provide your response as a single JSON object adhering strictly to this structure. Do NOT include any explanatory text outside the JSON.

\`\`\`json
{
  "refined_data_models": {
    "summary": "string - REQUIRED. Brief overview (2-3 sentences) of your overall strategy, reflecting the principles above. E.g., 'Created new normalized tables for the 'voice-memo' feature to avoid risky migrations on the core User table. Extended the Settings table with one minor field, as it directly relates to existing data and has minimal performance impact.'",
    "models": [
      {
        "model_name": "string - REQUIRED. Clear name for the data entity (e.g., UserProfileExtended, NewProductFeedback, OrderItemDetails)",
        "status": "extended | new | unchanged_from_draft | consolidated",
        "description": "string - REQUIRED. Brief description of this model/entity and its purpose in the context of the task.",
        "schema_definition": {
          // REQUIRED. Provide a clear schema definition.
          // Example:
          // "fields": {
          //   "user_id": {"type": "UUID", "primary_key": true, "notes": "Foreign key to Users table"},
          //   "feedback_text": {"type": "TEXT", "nullable": false},
          //   "rating": {"type": "INTEGER", "constraints": "1-5"},
          //   "created_at": {"type": "TIMESTAMP WITH TIME ZONE", "default": "NOW()"}
          // },
          // "relationships": [
          //  {"type": "one-to-many", "target_model": "User", "join_on": "user_id"}
          // ]
        }
      }
      // ... more model objects as needed
    ]
  }
}
\`\`\`
Focus on providing a robust, compliant, and non-redundant set of data model specifications that prioritize long-term database health. Be precise and ensure all required fields in the JSON output are populated.
`;
}

export function generateTaskAnalysisPrompt(taskDetails, userJourneys = [], projectConstitution = null) {
  console.log(`[generateTaskAnalysisPrompt] Generating with title: "${taskDetails.title}", initialIdeas: ${taskDetails.initialIdeas ? 'present' : 'absent'}, userJourneys: ${userJourneys.length}`);

  // Check if this is an experimental feature
  const isExperimental = taskDetails.isExperimental || false;
  console.log(`[generateTaskAnalysisPrompt] Experimental feature detected: ${isExperimental}`);

  const userJourneysString = userJourneys && userJourneys.length > 0
    ? userJourneys.map(journey => `
#### Journey: ${journey.title}
- **Actor:** ${journey.userRole}
- **Steps:**
  ${journey.steps.map(step => `- ${step}`).join('\n      ')}
`).join('\n')
    : "No user journeys were provided.";

  // Add experimental constraints
  const experimentalConstraints = isExperimental ? `

🧪 **EXPERIMENTAL FEATURE ANALYSIS CONSTRAINTS:**
- **Focus on Core Validation**: Extract only requirements needed to validate the experimental concept
- **Minimal Functionality**: Avoid complex requirements that aren't essential for initial validation
- **Quick Wins**: Prioritize functional requirements that can be implemented quickly
- **Simple NFRs**: Focus on basic quality requirements, avoid complex performance or scalability requirements unless critical
- **Defer Complex Features**: Identify complex requirements that can be marked as non-goals for initial validation
- **Easy to Disable**: Consider requirements that make the feature easy to turn off if needed
- **Limited Scope**: Focus on requirements that affect a small user segment initially

**EXPERIMENTAL MINDSET:** For each requirement, ask: "Is this necessary to validate the core concept, or is it complexity that can be deferred?"
` : '';

  // Add minimal project context if available
  const projectContext = projectConstitution?.projectType ? `
**PROJECT CONTEXT:**
- **Project Type:** ${projectConstitution.projectType}
` : '';

  // Adjust the guidance based on experimental status
  const analysisGuidance = isExperimental ? `
**EXPERIMENTAL FEATURE ANALYSIS APPROACH:**
Your task is to extract the minimal requirements needed to validate the experimental concept. Focus on:
1. **Core User Value**: What's the minimum functionality needed to prove user value?
2. **Validation Requirements**: What must work to test the hypothesis?
3. **Simplicity First**: Can complex requirements be simplified or deferred?
4. **Quick Implementation**: Prioritize requirements that enable fast iteration
5. **Reversible Design**: Consider requirements that make the feature easy to modify or remove
` : `
**STANDARD FEATURE ANALYSIS APPROACH:**
Your task is to extract comprehensive requirements to enable full feature implementation. Focus on:
1. **Complete User Value**: What functionality is needed for a complete user experience?
2. **Production Requirements**: What must work for production deployment?
3. **Quality Standards**: What quality requirements ensure long-term success?
4. **Scalability Needs**: What requirements support growth and scale?
5. **Maintainable Design**: Consider requirements that ensure long-term system health
`;

  // Adjust the requirements focus based on experimental status
  const requirementsGuidance = isExperimental ? `
**FUNCTIONAL REQUIREMENTS EXTRACTION (EXPERIMENTAL):**
- Extract ONLY the core functional requirements needed to validate the experimental concept
- Focus on the minimum viable functionality that proves user value
- Defer complex integrations, edge cases, or advanced features
- Prioritize requirements that can be implemented in days, not weeks
- Consider feature flag compatibility and easy removal
- **LIMIT TO MAX 12 TOTAL REQUIREMENTS**

**NFR EXTRACTION (EXPERIMENTAL):**
- Focus on basic quality requirements only (security, basic performance)
- Avoid complex scalability requirements unless critical for validation
- Defer advanced monitoring, analytics, or optimization requirements
- Consider requirements that make the feature easy to disable or modify
- **LIMIT TO MAX 6 TOTAL NFRS** - focus on the most critical only
` : `
**FUNCTIONAL REQUIREMENTS EXTRACTION (STANDARD):**
- Extract comprehensive functional requirements for full feature implementation
- Include all necessary integrations, edge cases, and advanced features
- Consider long-term maintenance and evolution needs
- Include requirements for monitoring, analytics, and optimization
- **LIMIT TO MAX 15 TOTAL REQUIREMENTS** - prioritize the most critical
- **Focus on core user value** - avoid edge cases and administrative requirements unless essential

**NFR EXTRACTION (STANDARD):**
- Include comprehensive quality requirements (performance, scalability, security)
- Consider production-grade monitoring and observability needs
- Include requirements for error handling, recovery, and maintenance
- Consider long-term architectural implications
- **LIMIT TO MAX 6 TOTAL NFRS** - focus on the most impactful quality requirements
`;

  // Add concise formatting guidelines
  const conciseFormattingGuidance = `
**CRITICAL: CONCISE REQUIREMENT FORMATTING**

For both functional and non-functional requirements, use CONCISE, SCANNABLE language:

**✅ GOOD Examples:**
- "Authenticate users via OAuth2"
- "Store user preferences persistently"
- "Validate input before processing"
- "Respond to API calls within 200ms"
- "Support 1000+ concurrent users"

**❌ BAD Examples (too verbose):**
- "The system must provide a comprehensive authentication mechanism that allows users to log in using their existing OAuth2 credentials from various providers"
- "The system must be able to store user preferences in a persistent storage solution that can be retrieved across sessions"
- "The system must provide sub-second response times for user queries to ensure a smooth user experience"

**FORMATTING RULES:**
1. **Action + Object + Condition**: Keep to this simple pattern
2. **Max 8 words per requirement**: Force clarity and specificity
3. **Active voice**: Use "Authenticate users" not "Users must be authenticated"
4. **Specific verbs**: Use "Store", "Validate", "Process" instead of "Provide", "Support", "Enable"
5. **Measurable when possible**: Include specific metrics (200ms, 1000 users, 99.9% uptime)

**NFR FORMATTING:**
- Lead with the quality attribute: "Respond within 200ms" not "System must respond within 200ms"
- Include specific metrics when possible
- Focus on user impact, not technical implementation

**PRIORITIZATION STRATEGY:**
1. **Core Value First**: Start with requirements that directly enable the main user value
2. **Breadth Over Depth**: Prefer broader requirements over specific edge cases
3. **User Impact Focus**: Prioritize requirements that users directly interact with
4. **Defer Complex Edge Cases**: Avoid requirements for rare scenarios or administrative tasks
5. **Consolidate Similar Requirements**: Combine related requirements into a single, broader statement

**QUALITY FILTERING - ONLY INCLUDE REQUIREMENTS THAT:**
- Are essential for the core user journey to work
- Cannot be easily implemented later without major architectural changes
- Have high user impact and are frequently used
- Are required for basic security, performance, or data integrity
`;

  // Construct the prompt, now with enhanced guidance for experimental features
  const prompt = `
You are an expert Principal Engineer responsible for analyzing a new feature request to identify its core requirements and architectural implications.

Your task is to break down the user's request into a set of functional requirements and a set of non-functional requirements (NFRs) that are *implied* by the request.

**TASK DETAILS:**
- **Title:** ${taskDetails.title}
- **Description:** ${taskDetails.description}
- **Experimental Feature:** ${isExperimental ? 'YES - Focus on minimal validation requirements' : 'NO - Standard comprehensive analysis'}

**USER JOURNEYS:**
${userJourneysString}

${projectContext}

**Initial Ideas/Notes from Requester:**
${taskDetails.initialIdeas || "No initial ideas were provided."}

${analysisGuidance}${experimentalConstraints}

**CRITICAL INSTRUCTIONS FOR INTERPRETING USER JOURNEYS:**

1. **Focus on User Value, Not Implementation:** The user journeys represent what actual users need to accomplish, not how the system will be built. Extract requirements that enable these user goals.

2. **Identify System Capabilities Needed:** For each journey step, ask "What must the system be capable of to support this user action?" This becomes a functional requirement.

3. **Infer Quality Requirements:** Look for implied quality needs:
   - Do users need "fast" responses? → Performance requirements
   - Do users handle "sensitive" data? → Security requirements  
   - Do users need "reliable" access? → Availability requirements
   - Do users need to "scale" their usage? → Scalability requirements

4. **Derive Non-Goals from Scope:** What user needs are explicitly NOT addressed by these journeys? What would be out of scope for this specific task?

5. **Example Interpretation:**
   - User Journey Step: "Developer reads library documentation to understand authentication flow"
   - Functional Requirement: "The system must provide comprehensive API documentation for authentication methods"
   - Implied NFR: "Documentation must be easily discoverable and searchable" (evidence: developer needs to "understand" the flow)
   - Non-Goal: "The system does not need to provide video tutorials" (not mentioned in any journey)

${requirementsGuidance}

${conciseFormattingGuidance}

**YOUR ANALYSIS TASK:**

Based on the user journeys and task details above, analyze what the system must DO to support these user needs (functional requirements) and what quality characteristics it must HAVE (non-functional requirements).

**REMEMBER**: Each requirement should be concise, scannable, and actionable. Follow the formatting guidelines above.

---

**CRITICAL REMINDER**: You must generate a **prioritized, concise list** of ${isExperimental ? '8-12 functional requirements total' : '12-15 functional requirements total'}. Focus on the most essential requirements that enable core user value.

Provide a structured analysis in the following JSON format. Do not include any explanatory text outside of the JSON object.

{
  "summary": "A brief, one-sentence summary of the core task from the user's perspective (what value users will get).",
  "functional_requirements": [
    "Concise, scannable functional requirements. ${isExperimental ? 'For experimental features, focus on minimal viable requirements needed for concept validation.' : 'Focus on WHAT the system must do to enable user actions.'} Use format: 'Authenticate users via OAuth2', 'Store user preferences persistently', 'Validate input before processing'."
  ],
  "implied_NFRs": [
    {
      "nfr": "The implied non-functional requirement derived from user journey analysis. ${isExperimental ? 'For experimental features, focus on basic quality requirements only.' : 'Use concise format: \'Respond within 200ms\', \'Support 1000+ concurrent users\', \'Achieve 99.9% uptime\''}",
      "evidence": "The specific phrase from the user journeys or task description that implies this NFR (e.g., 'User expects immediate search results when typing')"
    }
  ],
  "core_architectural_characteristics": [
    "A list of 1-3 critical architectural characteristics (e.g., 'Performance', 'Security', 'Usability') that are most important for supporting these user journeys effectively. ${isExperimental ? 'For experimental features, prioritize characteristics needed for quick validation.' : ''}"
  ],
  "non_goals": [
    "A list of 1-3 specific things that are explicitly out of scope based on what users DON'T need to do in these journeys. ${isExperimental ? 'For experimental features, include complex functionality that can be deferred for initial validation.' : 'Use concise format: \'No real-time collaboration\', \'No legacy system integration\', \'No offline mode support\'.'}"
  ]
}

**REQUIREMENT SELECTION PRIORITIES:**
1. **Must-Have for Core Journey**: Without this, the main user journey completely fails
2. **High User Impact**: Users directly interact with this capability frequently
3. **Architectural Foundation**: Required for other features to work properly
4. **Security/Data Integrity**: Essential for protecting user data and system integrity
5. **Performance Critical**: Directly impacts user experience if not implemented well

**AVOID THESE REQUIREMENTS:**
- Administrative or setup tasks that can be handled through documentation
- Edge cases that affect less than 5% of users
- Complex integrations that can be implemented in later phases
- Detailed validation rules that can be specified during implementation
- Monitoring, logging, or debugging features (unless user-facing)
`;
  return prompt;
}

export function generateKnowledgeGraphTriplesPrompt(decision) {
  console.log(`[generateKnowledgeGraphTriplesPrompt] Generating for decision: "${decision.metadata?.title || decision.id}"`);
  const decisionText = `
    Title: ${decision.metadata?.title || ''}
    Description: ${decision.metadata?.description || ''}
    Rationale: ${decision.metadata?.rationale || ''}
    Implications: ${decision.metadata?.implications || ''}
  `;

  return `
You are an AI knowledge graph expert. Your task is to analyze the text from a single architectural decision and extract the relationships between its concepts.
The goal is to build a knowledge graph that represents our architecture.

**Decision Text:**
---
${decisionText.trim()}
---

**Instructions:**
1.  Identify the core architectural concepts in the text. Concepts can be technologies (e.g., 'PostgreSQL', 'React'), patterns (e.g., 'Circuit Breaker', 'Event Sourcing'), or qualities (e.g., 'Scalability', 'Fault Tolerance').
2.  Determine the relationships between these concepts.
3.  Output a JSON object containing a list of these relationships as "semantic triples".

**Semantic Triple Schema:**
Each triple should be an object with three keys:
*   \`subject\`: The source concept.
*   \`predicate\`: The relationship verb (e.g., 'IMPLEMENTS', 'IS_A', 'USES', 'IMPROVES', 'ENABLES', 'ADDRESSES'). Use uppercase for the predicate.
*   \`object\`: The target concept.

**Example:**
For a decision about using Redis for caching, the output might be:
{
  "relationships": [
    { "subject": "Redis", "predicate": "IMPLEMENTS", "object": "Distributed Caching" },
    { "subject": "Distributed Caching", "predicate": "IMPROVES", "object": "Performance" },
    { "subject": "Distributed Caching", "predicate": "IS_A", "object": "Caching Pattern" }
  ]
}

**Rules:**
- Be precise. Only extract relationships explicitly stated or strongly implied in the text.
- Do not invent concepts not present in the decision text.
- If no meaningful relationships can be extracted, return an empty "relationships" array.
- Respond with ONLY the JSON object.

Begin JSON output now.
`;
}


export function generateDesignDocConceptExtractionPromptForTask(taskTitle, taskDescription) {
  if (!taskDescription || taskDescription.trim() === '') {
    console.warn('[GenTaskConceptExtractionPrompt] Task description is empty.');
    return '';
  }

  const contentSnippet = `Task Title: ${taskTitle}\\\\nTask Description: ${taskDescription}`;

  return `You are an expert system architect. Your task is to analyze the provided task information (title and description) and extract the most salient initial domain concepts. These concepts will primarily be used to understand the core subject matter of the task for the first phase of design document generation, which focuses on goals, non-goals, and identifying key technical decisions.

**Task Information:**
---\n${contentSnippet}\n---\n

**Instructions:**

1.  Read the Task Information carefully to understand its main themes and architectural focus.
2.  Identify and extract between **two (2) and five (5)** key initial domain concepts. These should be nouns or short noun phrases (2-4 words max) that represent the primary topics.
3.  These concepts are for initial understanding. More detailed concept extraction for RAG search will happen later if needed for specific technical decisions.

**Output Format:**

Provide your response as a JSON object with a single key "initial_domain_concepts". The value should be an array of strings.

Example:
{
  "initial_domain_concepts": [
    "user authentication module",
    "oauth2 integration",
    "database schema migration",
    "frontend state management"
  ]
}

Please provide ONLY the JSON object in your response.`;
}

/**
 * Generates a prompt to seed a Project Constitution from a raw text block.
 *
 * @param {string} rawText - The unstructured text containing architectural principles, goals, etc.
 * @returns {string} - The prompt for the LLM.
 */
export function generateConstitutionSeedingPrompt(rawText) {
  return `You are an expert software architect and product strategist tasked with analyzing a team's unstructured context to pre-fill a "Project Constitution". Your goal is to infer the team's stage, priorities, principles, and core user interactions from the provided text.

Here is the raw text provided by the user:
<raw_text>
${rawText}
</raw_text>

Analyze the text and respond with a JSON object that has the following structure:
{
  "companyStage": "...", // "startup", "growth", or "mature"
      "projectType": "...", // "b2c-saas", "b2b-saas", "library", "internal-tool", "data-platform", "other", or "not-set"
  "priorities": [...], // A ranked array of priorities
  "architecturalPrinciples": "...", // A summary of key architectural rules or philosophies
  "productAndBusinessContext": "...", // A summary of the product goals and business context
  "userPersonas": [...] // An array of short, descriptive user personas
}

**Instructions for filling the JSON:**

1.  **companyStage**: Infer the company's current stage.
    *   **startup**: Focus on speed, iteration, finding product-market fit.
    *   **growth**: Focus on scaling, capturing market share, adding features.
    *   **mature**: Focus on stability, efficiency, optimization, and reliability.
    Choose the best fit.

2.  **projectType**: Infer the type of project. Is it a 'b2c-saas' (business-to-consumer software as a service), 'b2b-saas' (business-to-business), a 'library', an 'internal-tool' for a company, a 'data-platform', or something else ('other')? If you cannot determine the type, use 'not-set'. This is a critical classification that will inform architectural choices.

3.  **priorities**: This is the most critical part. Based on the text, you must rank the following SEVEN architectural priorities from most important to least important.
    *   **Time to Market**: Prioritizes speed of delivery and simplicity of implementation.
    *   **Reliability**: Focuses on building a stable, fault-tolerant, and resilient system.
    *   **Scalability**: Ensures the system can handle growth in users, data, and traffic.
    *   **Maintainability**: Prioritizes ease of future updates, debugging, and understanding.
    *   **Cost of Ownership**: Considers long-term expenses including infrastructure, licenses, and developer time.
    *   **Developer Experience**: Focuses on how easy and efficient it is for developers to work on the system.
    *   **Strategic Alignment**: Aligns with long-term product vision, existing tech, and team skills.

    Your output for the "priorities" key MUST be a ranked array containing ALL SEVEN of these string values: 
    'time-to-market', 'reliability', 'scalability', 'maintainability', 'cost-of-ownership', 'developer-experience', 'strategic-alignment'.
    Example: ["reliability", "scalability", "maintainability", "time-to-market", "strategic-alignment", "developer-experience", "cost-of-ownership"]

4.  **architecturalPrinciples**: Extract any explicit or implicit rules, patterns, or philosophies about how the team builds software. Synthesize them into a few concise bullet points (using markdown). Examples: "Use microservices for new domains," "All endpoints must be authenticated," "Prefer managed services over self-hosting."

5.  **productAndBusinessContext**: Summarize the key business goals, target users, and product strategy mentioned in the text. This should provide context for *why* the technical decisions are being made.

6. **userPersonas**: Based on the project type and business context, extract 2-4 short, descriptive user personas. For libraries, this might be developer types (e.g., 'Frontend Developer on Vercel', 'Mobile Developer on React Native'). For applications, this might be user roles (e.g., 'Team Administrator', 'Content Creator', 'External Collaborator'). Keep them concise.


Provide ONLY the JSON object as your response. Do not include any other text, greetings, or explanations.
`;
}


// --- RELEVANCE ENGINE PROMPT ---
/**
 * Generates a prompt for the relevance engine to extract key principles from the constitution.
 *
 * @param {object} taskDetails - The details of the task being worked on.
 * @param {string} taskDetails.title - The title of the task.
 * @param {string} taskDetails.description - The description of the task.
 * @param {object} projectConstitution - The full project constitution object.
 * @returns {string} The prompt for the LLM.
 */
export function generateRelevanceEnginePrompt(taskDetails, projectConstitution) {
    const { taskTitle, taskDescription, initialIdeas } = taskDetails;
    const { companyStage, projectType, priorities, architecturalPrinciples, productAndBusinessContext, primaryLanguage } = projectConstitution;

    let constitutionContext = "No constitution provided.";
    if (projectConstitution && Object.keys(projectConstitution).length > 0) {
        const parts = [];
        if (companyStage && companyStage !== 'not-set') {
            parts.push(`- Company Stage: ${companyStage}`);
        }
        if (projectType && projectType !== 'not-set') {
            parts.push(`- Project Type: ${projectType}`);
        }
        if (priorities && priorities.length > 0) {
            const priorityList = priorities.map((p, i) => `  ${i + 1}. ${p.replace(/-/g, ' ')}`).join('\\n');
            parts.push(`- Ranked Priorities:\\n${priorityList}`);
        }
        if (primaryLanguage) {
            parts.push(`- Primary Language: ${primaryLanguage}`);
        }
        if (architecturalPrinciples) {
            parts.push(`- Architectural Principles:\\n${architecturalPrinciples}`);
        }
        if (productAndBusinessContext) {
            parts.push(`- Product & Business Context:\\n${productAndBusinessContext}`);
        }
        constitutionContext = parts.join('\\n\\n');
    }

    return `You are an AI Relevance Engine. Your task is to analyze a developer's task and a Project Constitution, and then extract ONLY the 3-5 most relevant principles from the constitution that should guide the architect for this specific task.

**Full Project Constitution:**
<constitution>
${constitutionContext}
</constitution>

**Developer's Task:**
<task>
**Title:** ${taskTitle}
**Description:** ${taskDescription}
${initialIdeas ? `**Initial Ideas:** ${initialIdeas}` : ''}
</task>

Based on the task, identify the 3-5 most critical principles from the full constitution above. These "golden snippets" will be the *only* context given to the architect AI. Focus on what is most likely to prevent mistakes or ensure alignment for *this specific task*.

Return a JSON object with a single key, "relevant_principles", containing a string with the extracted principles.

Example Response:
{
  "relevant_principles": "- Ranked Priorities: 1. Reliability, 2. Scalability\\n- Architectural Principles: All new services must use the company's standard logging library.\\n- Product & Business Context: This feature is for enterprise customers who have high data security requirements."
}
`;
}

/**
 * Generates a prompt to draft user journeys based on initial task details.
 * This is designed to reduce the user's authoring burden by providing an AI-generated starting point.
 *
 * @param {import('../types/design-doc-wizard').TaskDetails} taskDetails - The user-provided task details.
 * @returns {string} The prompt for the LLM.
 */
export function generateUserJourneysDraftPrompt(taskDetails, projectConstitution) {
  // Extract key constitution elements for sharp constraints
  const companyStage = projectConstitution?.companyStage || 'not-set';
  const projectType = projectConstitution?.projectType || 'not-set';
  const topPriority = projectConstitution?.priorities?.[0] || 'time-to-market';
  const businessContext = projectConstitution?.productAndBusinessContext || '';
  const architecturalPrinciples = projectConstitution?.architecturalPrinciples || '';
  
  // Check if this is an experimental feature
  const isExperimental = taskDetails.isExperimental || false;

  // Generate company stage constraints
  const stageConstraints = {
    'startup': 'STARTUP CONSTRAINT: Generate max 3 journeys focused on core value validation. Avoid complex or edge-case scenarios. Prefer documenting limitations over building complex systems.',
    'growth': 'GROWTH CONSTRAINT: Generate max 3 journeys focused on scalable user workflows. Avoid administrative or setup-heavy scenarios. Balance feature completeness with maintainability.',
    'mature': 'MATURE CONSTRAINT: Generate max 3 journeys focused on reliable, well-defined user workflows. Prioritize stability and avoid experimental features that increase debugging complexity.'
  };

  // Generate priority-based focus
  const priorityFocus = {
    'time-to-market': 'PRIORITY FOCUS: Emphasize journeys that deliver immediate user value with minimal complexity. Document limitations rather than building complex edge case handling.',
    'reliability': 'PRIORITY FOCUS: Emphasize journeys that showcase stable, predictable user workflows. Avoid complex scenarios that increase maintenance burden.',
    'scalability': 'PRIORITY FOCUS: Emphasize journeys that need to work for many users or large-scale scenarios, but prefer simple, maintainable solutions.',
    'maintainability': 'PRIORITY FOCUS: Emphasize journeys that are simple and clear for long-term support. Strongly avoid complex edge cases that are hard to debug.',
    'cost-of-ownership': 'PRIORITY FOCUS: Emphasize journeys that minimize operational overhead and complexity. Question whether edge cases justify their implementation cost.',
    'developer-experience': 'PRIORITY FOCUS: Emphasize journeys that are easy and pleasant for developers to execute. Avoid complex scenarios that make the system hard to understand.',
    'strategic-alignment': 'PRIORITY FOCUS: Emphasize journeys that align with long-term product vision and existing systems. Avoid solutions that increase architectural complexity unnecessarily.'
  };

  // Add experimental constraints that override other constraints
  const experimentalConstraints = isExperimental ? `

🧪 **EXPERIMENTAL FEATURE CONSTRAINTS (OVERRIDES ALL OTHER CONSTRAINTS):**
- **Maximum 2 journeys**: Focus on the bare minimum to validate the concept
- **Extreme simplicity**: Each journey should have maximum 3-4 steps
- **Quick wins only**: Prioritize journeys that can be implemented in days, not weeks
- **Easy to disable**: Consider how each journey can be easily turned off if needed
- **No architectural changes**: Avoid journeys that require database schema changes or new infrastructure
- **Feature flag friendly**: Design journeys that work well with feature flags and gradual rollouts
- **Minimal dependencies**: Avoid journeys that require integrating with multiple external systems
- **Low maintenance**: Focus on journeys that are unlikely to break with system updates
- **Limited scope**: Prefer journeys that affect a small user segment initially
- **Reversible**: Consider how each journey can be easily removed without impacting other features

**EXPERIMENTAL MINDSET:** Ask for each journey: "What's the simplest way to validate this user need without building complex systems?"
` : '';

  const currentStageConstraint = stageConstraints[companyStage] || 'Generate 1-3 focused user journeys, prioritizing simplicity over complexity.';
  const currentPriorityFocus = priorityFocus[topPriority] || 'Focus on high-value user scenarios that are simple to implement and maintain.';

  // Special guidance for libraries
  const libraryGuidance = projectType === 'library' ? `

**🔬 CRITICAL LIBRARY-SPECIFIC DECISION FRAMEWORK:**
- **Boundary Question**: For every decision, ask: "Should this be the library's responsibility or the application's?"
- **Maintenance Reality**: Every feature in a library affects ALL users. Complex features become technical debt for your entire user base.
- **Simplicity Principle**: Libraries should solve ONE thing exceptionally well. Avoid journeys that expand scope unnecessarily.
- **User Responsibility**: Often it's better to require library users to handle edge cases than to build complex internal logic.
- **Documentation vs Implementation**: Sometimes documenting limitations is far better than implementing complex solutions.
- **Priority Journeys**: Prioritize journey per platform category if user journeys are critically different across platforms supported and will involve making if then class of technical decisions.
` : '';

  // Use constitution context if available to frame the journeys better
  const constitutionContext = projectConstitution ? `
---
**PROJECT CONSTITUTION CONTEXT:**
- **Company Stage:** ${companyStage}
- **Project Type:** ${projectType}
- **Top Priority:** ${topPriority.replace(/-/g, ' ')}
- **Business Context:** ${businessContext}
- **Architectural Principles:** ${architecturalPrinciples}
- **Experimental Feature:** ${isExperimental ? 'YES - Focus on rapid validation with minimal complexity' : 'NO - Standard feature development'}

**CONSTRAINTS FROM YOUR PROJECT:**
${currentStageConstraint}
${currentPriorityFocus}${libraryGuidance}${experimentalConstraints}
---
` : '';

  // Generate project-type-specific instructions with sharper focus
  const projectTypeInstructions = getSharpProjectTypeInstructions(projectType, businessContext);

  // Adjust quantity limits and instructions based on experimental status
  const quantityLimit = isExperimental ? 
    '1. **QUANTITY LIMIT:** Generate ONLY 1-2 user journeys for experimental features. Focus on the absolute core user workflow needed to validate the concept.' :
    '1. **QUANTITY LIMIT:** Generate ONLY 1-3 user journeys. Focus on the absolute most important user workflows that justify their implementation and maintenance cost.';

  const priorityInstructions = isExperimental ?
    '2. **EXPERIMENTAL PRIORITIZATION:** For experimental features, all journeys should be "Critical" for concept validation. Avoid "Nice-to-have" features that add complexity without validation value.' :
    '2. **PRIORITIZATION REQUIRED:** For each journey, assign a priority level:\n   - **"Critical"**: Must be supported for the feature to be valuable. Core user workflows.\n   - **"Nice-to-have"**: Would improve user experience but feature works without it.\n   - **"Edge/Unlikely"**: Handles rare scenarios. Consider documenting limitations instead of implementing.';

  const complexityInstructions = isExperimental ?
    '3. **EXPERIMENTAL COMPLEXITY ANALYSIS:** For experimental features, heavily bias toward "Low complexity" journeys. If a journey requires significant complexity, consider if it\'s truly necessary for concept validation or if it can be simplified/deferred.' :
    '3. **COMPLEXITY ANALYSIS:** For each journey, consider:\n   - How hard will this be to implement correctly?\n   - How hard will this be to test and debug?\n   - What edge cases does this introduce?\n   - Could we document limitations instead?';

  const prompt = `
You are a Product Strategy expert creating user journeys for a new technical task. Your goal is to draft ONLY the most critical user journeys that represent core user value, while avoiding unnecessary complexity.

**CRITICAL PHILOSOPHY: SIMPLICITY OVER COMPLETENESS**
- **Question every journey**: Does this solve a real problem or create unnecessary complexity?
- **Prefer limitations**: It's better to document what you DON'T support than to build complex systems that are hard to maintain and debug.
- **Consider maintenance cost**: Every journey you include will need to be supported, tested, and debugged for years.

**TASK DETAILS:**
- **Title:** ${taskDetails.title}
- **Description:** ${taskDetails.description}
- **Initial Ideas/Notes from Requester:** ${taskDetails.initialIdeas || "None"}
- **Experimental Feature:** ${isExperimental ? 'YES - This is an experimental feature requiring minimal complexity' : 'NO - Standard feature development'}
${constitutionContext}

**PROJECT TYPE GUIDANCE:**
${projectTypeInstructions}

**CRITICAL INSTRUCTIONS - READ CAREFULLY:**

${quantityLimit}

${priorityInstructions}

${complexityInstructions}

4. **USER VALUE FOCUS:** Every step must represent something the user directly cares about achieving. No implementation details.

5. **GENERAL USER ROLES:** Use general role categories rather than overly specific personas (e.g., "Developer", "Team Lead", "End User", "Admin"). Avoid company-specific or industry-specific details unless truly necessary.

6. **CONCISE JOURNEYS:** Focus on essential steps only. Avoid story-telling or verbose descriptions. Each step should be a clear, actionable statement.

7. **BUSINESS-CONTEXT FILTERING:** Given the business context above, only create journeys that directly support the core value proposition.

8. **AVOID THESE PATTERNS:**
   - "Reviews documentation" → Instead: "Accomplishes [specific goal]"
   - "Configures system" → Instead: "Sets up [specific business outcome]"  
   - "Implements code" → Instead: "Achieves [specific user benefit]"
   - Generic troubleshooting or setup steps
   - Complex edge cases that would be hard to maintain
   - Story-telling language or verbose descriptions

9. **FOCUS ON OUTCOMES:** Each journey should end with the user achieving a meaningful business outcome, not completing a technical task.

**EXAMPLES OF GOOD vs. BAD:**

❌ BAD: "Sales Director at growing B2B startup rapidly onboards new sales team members during scaling period"
✅ GOOD: "Team lead onboards new team members"

❌ BAD: "Operations Manager at real estate firm discovers former employee may still have access to team invite link"
✅ GOOD: "Admin resets team access after employee turnover"

❌ BAD: "Developer handles all possible authentication edge cases"
✅ GOOD: "Developer secures user authentication flow"

**Your Task:**
Based on the task details, business context, and constraints above, draft ${isExperimental ? '1-2' : '1-3'} prioritized user journeys that represent the core value this feature will deliver. ${isExperimental ? 'For experimental features, focus on the minimal journeys needed to validate the concept quickly.' : 'Focus on what\'s truly critical vs. what\'s just nice to have.'}

**Output Format:**
Provide your response as a JSON object with a single key "user_journeys". Do not include any text outside of the JSON object.

**JSON Schema:**
{
  "user_journeys": [
    {
      "title": "A concise, outcome-focused title (e.g., 'Team lead onboards new members', 'User authenticates with OAuth')",
      "userRole": "A general user role category (e.g., 'Developer', 'Team Lead', 'End User', 'Admin')",
      "priority": "Critical | Nice-to-have | Edge/Unlikely",
      "complexity_assessment": "A brief assessment of implementation and maintenance complexity (e.g., 'Low complexity, standard CRUD operations' or 'High complexity, requires real-time synchronization')",
      "steps": [
        "User performs core action",
        "User achieves primary outcome", 
        "User completes essential workflow"
      ],
      "maintenance_considerations": "Brief note on what makes this easy or hard to maintain long-term"
    }
  ]
}

Begin the JSON output now.
`;
  return prompt;
}

/**
 * Helper function to generate project-type-specific instructions for user journey creation.
 * @param {string} projectType - The project type from the constitution
 * @returns {string} - Project-type-specific instructions
 */
function getProjectTypeInstructions(projectType) {
  switch (projectType) {
    case 'library':
      return `
This is a LIBRARY project. The primary users are:
- **Application Developers** who will integrate this library into their projects
- **Other Library Developers** who might contribute to or build upon this library

Focus on journeys like:
- "Developer integrates [library feature] into their application"
- "Developer troubleshoots [library feature] in their production environment"
- "Developer customizes [library feature] for their specific use case"

DO NOT create journeys for:
- Library maintainers implementing features
- Library contributors writing tests
- Library developers publishing releases`;

    case 'b2c-saas':
      return `
This is a B2C SAAS project. The primary users are:
- **End Users** (consumers) who will use the application
- **Guest Users** who are considering using the application

Focus on journeys like:
- "User signs up for the service"
- "User accomplishes their core goal using the product"
- "User manages their account and preferences"

DO NOT create journeys for:
- Internal developers building features
- Product managers defining requirements
- Customer support agents`;

    case 'b2b-saas':
      return `
This is a B2B SAAS project. The primary users are:
- **Business Users** (employees at client companies)
- **Admin Users** (IT/administrators at client companies)
- **Decision Makers** (buyers evaluating the solution)

Focus on journeys like:
- "Admin configures the system for their organization"
- "Business user accomplishes their work task using the tool"
- "Decision maker evaluates the solution for their company"

DO NOT create journeys for:
- Internal developers building features
- Sales teams demonstrating the product
- Customer success managers onboarding clients`;

    case 'internal-tool':
      return `
This is an INTERNAL TOOL project. The primary users are:
- **Internal Employees** who will use this tool for their work
- **Specific Teams** (e.g., ops, support, engineering) who need this functionality

Focus on journeys like:
- "Support agent uses tool to resolve customer issue"
- "Engineer uses tool to diagnose system problem"
- "Manager uses tool to generate team report"

DO NOT create journeys for:
- Internal developers building the tool
- IT team deploying the tool
- Product managers defining requirements`;

    case 'data-platform':
      return `
This is a DATA PLATFORM project. The primary users are:
- **Data Engineers** who will build data pipelines
- **Data Analysts** who will query and analyze data
- **Data Scientists** who will build models and run experiments

Focus on journeys like:
- "Data engineer creates a new data pipeline"
- "Data analyst builds a dashboard for business metrics"
- "Data scientist trains a model using platform data"

DO NOT create journeys for:
- Platform engineers building the infrastructure
- DevOps engineers deploying the platform
- Product managers defining platform requirements`;

    case 'not-set':
    case 'other':
    default:
      return `
Project type is not specified or is 'other'. Consider these common user types:
- **Primary Users** who will directly interact with the system
- **Secondary Users** who will be affected by the system
- **Administrative Users** who will manage the system

Focus on journeys that represent VALUE the system provides to its users, not how it's built or deployed.

Avoid implementation-focused journeys like:
- "Developer implements feature X"
- "Engineer deploys system Y"
- "Team writes documentation for Z"`;
  }
}

/**
 * NEW: Enhanced helper function to generate sharp, business-context-aware instructions for user journey creation.
 * @param {string} projectType - The project type from the constitution
 * @param {string} businessContext - The business context from the constitution
 * @returns {string} - Sharp, focused project-type-specific instructions
 */
function getSharpProjectTypeInstructions(projectType, businessContext) {
  const baseContext = businessContext ? `\n\nGiven your business context: "${businessContext}"` : '';
  
  switch (projectType) {
    case 'library':
      return `
This is a LIBRARY project. Focus on SPECIFIC developers who will integrate this library.${baseContext}

**🔬 CRITICAL LIBRARY BOUNDARY REQUIREMENTS:**
- **Library Responsibility**: Only include journeys where the library should clearly handle the complexity, not the application.
- **Maintenance Impact**: Every journey affects ALL library users - only include if it justifies the maintenance burden across the entire user base.
- **Simplicity Principle**: Libraries should solve ONE thing exceptionally well. Avoid journeys that expand scope unnecessarily.
- **Edge Case Handling**: It's often better to document limitations than to build complex edge case handling in the library.

**SHARP FOCUS REQUIREMENTS:**
- Use specific developer types: "React developer building e-commerce sites", "Node.js developer building APIs", etc.
- Focus on business outcomes: "Processes payments securely", "Authenticates enterprise users", etc.
- Avoid generic setup: No "reads documentation", "installs library", "configures settings"
- End with business value: "Launches secure checkout", "Enables SSO for customers", etc.
- **Boundary Test**: For each step, ask "Should this complexity live in the library or be the application's responsibility?"

**EXAMPLE SHARP JOURNEY:**
❌ AVOID: "Developer handles all possible edge cases with advanced configuration options"
✅ USE: "Fintech developer enables secure user login for banking app"
❌ AVOID: "Developer customizes complex error recovery scenarios"
✅ USE: "API developer validates user tokens with 99.9% uptime requirement"

**COMPLEXITY QUESTIONS TO ASK:**
- Does this journey require the library to handle edge cases that could be application-level decisions?
- Would implementing this make the library harder to understand or maintain?
- Could we document this limitation instead of building complex handling?`;

    case 'b2c-saas':
      return `
This is a B2C SAAS project. Focus on SPECIFIC end users accomplishing real goals.${baseContext}

**SHARP FOCUS REQUIREMENTS:**
- Use specific user types: "Freelance designer", "Small business owner", "Marketing manager", etc.
- Focus on business outcomes: "Increases conversion rates", "Saves time on reporting", etc.
- Avoid generic actions: No "logs in", "navigates to", "clicks button"
- End with clear value: "Publishes campaign", "Exports client report", "Completes sale", etc.

**EXAMPLE SHARP JOURNEY:**
❌ AVOID: "User manages their account settings"
✅ USE: "Small business owner tracks inventory levels across multiple locations"`;

    case 'b2b-saas':
      return `
This is a B2B SAAS project. Focus on SPECIFIC business users solving work problems.${baseContext}

**SHARP FOCUS REQUIREMENTS:**
- Use specific roles: "Sales manager at tech startup", "HR director at remote company", etc.
- Focus on work outcomes: "Closes more deals", "Reduces hiring time", "Improves team productivity"
- Avoid admin tasks: No "configures permissions", "sets up integrations", "manages users"
- End with business impact: "Generates quarterly report", "Onboards new hire", "Identifies top leads"

**EXAMPLE SHARP JOURNEY:**
❌ AVOID: "Admin configures the system for their organization"
✅ USE: "Sales director identifies highest-value prospects for Q4 outreach"`;

    case 'internal-tool':
      return `
This is an INTERNAL TOOL project. Focus on SPECIFIC employees solving work problems.${baseContext}

**SHARP FOCUS REQUIREMENTS:**
- Use specific roles: "Customer support agent", "DevOps engineer", "Finance analyst", etc.
- Focus on work efficiency: "Resolves tickets faster", "Deploys with confidence", "Processes expenses"
- Avoid tool setup: No "accesses tool", "learns interface", "configures settings"
- End with productivity: "Closes support ticket", "Deploys to production", "Approves expense report"

**EXAMPLE SHARP JOURNEY:**
❌ AVOID: "Support agent uses tool to resolve customer issue"
✅ USE: "Support agent resolves billing dispute in under 2 minutes"`;

    case 'data-platform':
      return `
This is a DATA PLATFORM project. Focus on SPECIFIC data professionals delivering insights.${baseContext}

**SHARP FOCUS REQUIREMENTS:**
- Use specific roles: "Marketing analyst", "ML engineer at fintech", "BI developer", etc.
- Focus on insights: "Identifies growth opportunities", "Improves model accuracy", "Automates reporting"
- Avoid data setup: No "ingests data", "configures pipelines", "sets up schemas"
- End with decisions: "Launches targeted campaign", "Deploys better model", "Presents to executives"

**EXAMPLE SHARP JOURNEY:**
❌ AVOID: "Data analyst builds a dashboard for business metrics"
✅ USE: "Marketing analyst identifies which campaigns drive highest customer lifetime value"`;

    case 'not-set':
    case 'other':
    default:
      return `
**SHARP FOCUS REQUIREMENTS (Generic):**${baseContext}
- Use SPECIFIC user types relevant to your business context above
- Focus on BUSINESS OUTCOMES and measurable value
- Avoid setup, configuration, or learning steps
- End with clear user success and business impact

**EXAMPLE SHARP JOURNEY:**
❌ AVOID: "User accomplishes task using the system"
✅ USE: "[Specific user type] achieves [specific business outcome] in [context]"`;
  }
}

/**
 * Generates a prompt to analyze the impact of new user journeys against existing ones.
 *
 * @param {import('../types/design-doc-wizard').UserJourney[]} newJourneys - The user-confirmed journeys for the new task.
 * @param {import('../types/design-doc-wizard').UserJourney[]} existingJourneys - Similar journeys retrieved from the knowledge base.
 * @returns {string} The prompt for the LLM.
 */
export function generateJourneyImpactAnalysisPrompt(newJourneys, existingJourneys) {
  const newJourneysString = newJourneys.map(j => `
#### Journey: ${j.title}
- **Actor:** ${j.userRole}
- **Steps:**
  ${j.steps.map(step => `- ${step}`).join('\n      ')}
`).join('\n');

  const existingJourneysString = existingJourneys.map(j => `
#### Journey: ${j.title}
- **Actor:** ${j.userRole}
- **Steps:**
  ${j.steps.map(step => `- ${step}`).join('\n      ')}
`).join('\n');

  return `
You are an expert Product Manager and Systems Analyst. Your goal is to analyze a set of PROPOSED user journeys for a new feature against a list of EXISTING user journeys from our product's knowledge base.

Your task is to identify potential conflicts, inconsistencies, gaps, and ambiguities that need to be clarified before development begins.

**PROPOSED Journeys for the New Feature:**
---
${newJourneysString}
---

**EXISTING Journeys Retrieved from Knowledge Base (for context):**
---
${existingJourneysString}
---

**Analysis Instructions:**

1.  **Compare Steps:** Carefully compare the steps of the PROPOSED journeys against the steps of EXISTING journeys. Look for logical inconsistencies (e.g., a required step from an existing journey is missing in a new one).
2.  **Identify Role Conflicts:** Check if the actions of a user role in a PROPOSED journey conflict with the established capabilities or flows of the same role in an EXISTING journey.
3.  **Find Gaps:** Identify what's *missing*. Does a PROPOSED journey leave the user in a state that isn't handled by any existing flow? (e.g., a new "guest cart" that has no path to checkout).
4.  **Surface Ambiguities:** If a PROPOSED journey is vague, formulate a precise question to force clarification. Reference a similar, more detailed EXISTING journey if possible.

**Output Format:**
You MUST respond with a single JSON object. The object should have a key "journey_impact_analysis" with the following structure:
{
  "summary": "A high-level summary of the potential impact on existing user journeys.",
  "clarification_points": [
    {
      "type": "conflict | inconsistency | gap | ambiguity",
      "question": "A specific, actionable question for the product team to answer.",
      "context": "A brief explanation of why this is a concern, referencing specific PROPOSED and EXISTING journeys by their titles."
    }
  ]
}

**Example Response:**
\`\`\`json
{
  "journey_impact_analysis": {
    "summary": "The proposed 'Guest Add to Cart' journey creates a new persistent cart state for unauthenticated users, which conflicts with the existing 'Checkout' journey that requires login. This needs to be resolved.",
    "clarification_points": [
      {
        "type": "conflict",
        "question": "The existing 'User Checkout' journey requires a user to be logged in. At what point in the flow will the user with a 'Guest Cart' be prompted to log in or create an account?",
        "context": "This conflicts with the 'Guest Adds Item to Cart' journey which doesn't specify a login step, and the existing 'User Checkout' journey which assumes an authenticated user from the start."
      },
      {
        "type": "inconsistency",
        "question": "The existing 'Update Profile' journey requires email verification for changes. The proposed 'Admin Invites Member' journey does not mention email verification for the new member. Should this be added for consistency?",
        "context": "The PROPOSED 'Admin Invites Member' journey is inconsistent with the security pattern established in the EXISTING 'Update Profile' journey."
      }
    ]
  }
}
\`\`\`

If you find no issues, return an empty "clarification_points" array.
`;
}

/**
 * Generates a prompt for Strategic Value Assessment - evaluating whether a feature should be built, 
 * documented, or deferred based on project type and constitution context.
 * This acts as a "smart product engineer" checkpoint before deep technical analysis.
 *
 * @param {object} taskAnalysis - The completed task analysis with requirements and journeys
 * @param {object} projectConstitution - The project constitution for strategic context
 * @param {string[]} [retrievedContext] - Optional context from similar past decisions
 * @param {boolean} [isMinimalScope=false] - Whether this is a minimal scope assessment
 * @returns {string} - Prompt for the LLM
 */
export function generateStrategicValueAssessmentPrompt(taskAnalysis, projectConstitution, retrievedContext = [], isMinimalScope = false, isExperimental = false) {
  const { functional_requirements, implied_NFRs, core_architectural_characteristics, non_goals, summary } = taskAnalysis;
  
  const flattenedFunctionalRequirements = functional_requirements || [];
  const { companyStage, projectType, priorities, architecturalPrinciples, productAndBusinessContext } = projectConstitution;

  const contextString = retrievedContext.length > 0 
    ? `\n**Past Decisions:** ${retrievedContext.join(' | ')}\n`
    : '';

  const top3Priorities = priorities.slice(0, 3);
  const prioritiesString = top3Priorities.map((p, i) => `${i + 1}. ${p.replace(/-/g, ' ')}`).join(', ');

  const scopeContext = isMinimalScope 
    ? `\n**MINIMAL SCOPE:** Reduced version to avoid complexity issues from original assessment.`
    : '';

  const experimentalContext = isExperimental 
    ? `\n**EXPERIMENTAL:** Designed for rapid validation, easy disable/remove.`
    : '';

  return `You are a Strategic Engineer evaluating whether this feature should be BUILT, DOCUMENTED, or DEFERRED.

**TASK:** ${summary}
**Requirements:** ${flattenedFunctionalRequirements.join(', ')}
**NFRs:** ${implied_NFRs.map(nfr => nfr.nfr).join(', ')}
**Stage:** ${companyStage} ${projectType}
**Priorities:** ${prioritiesString}${contextString}${scopeContext}${experimentalContext}

**EVALUATE:**
1. **Value:** Does this solve real user problems aligned with priorities?
2. **Maintenance:** What's the debugging/support burden long-term?
3. **Alignment:** How well does this fit project stage and constitution?
4. **Alternatives:** Can documentation/process changes solve this instead?
5. **Library Rot (for libraries):** If deferring, what pain would existing users face if the library becomes unmaintained?

**BUILD IF:** Medium+ user value, reasonable maintenance burden, strong alignment, OR library rot would cause significant user pain
**DOCUMENT IF:** Problem solved with guidance/examples, no code needed, AND library rot not a concern
**DEFER IF:** High maintenance burden vs user value, misaligned with priorities, AND library rot would not cause significant existing user pain

**OUTPUT FORMAT:**
- If recommendation is BUILD: omit "recommended_alternatives" and "override_justification_needed" fields
- If recommendation is DOCUMENT or DEFER: include all fields

{
  "strategic_assessment": {
    "recommendation": "BUILD | DOCUMENT | DEFER",
    "confidence_level": "high | medium | low",
    "strategic_rationale": "2-3 sentences max. Why this recommendation fits project priorities and stage.",
    "complexity_assessment": {
      "maintenance_burden": "low | medium | high",
      "user_value_delivered": "none | low | medium | high",
      "complexity_justification": "1-2 sentences. Why maintenance burden is/isn't justified by value."
    },
    "alignment_analysis": {
      "constitutional_alignment": "strong | moderate | weak",
      "priority_alignment": "Brief assessment vs top 3 priorities",
      "stage_appropriateness": "Fits ${companyStage} ${projectType}? Why/why not?",
      "strategic_concerns": ["Brief specific concerns, if any"]
    },
    "recommended_alternatives": [
      // ONLY include this field if recommendation is DOCUMENT or DEFER
      {
        "approach": "Alternative solution in few words",
        "effort_required": "low | medium | high",
        "value_delivered": "% value vs full feature",
        "implementation_notes": "How to implement - specific guidance"
      }
    ],
    "override_justification_needed": "What would justify overriding DOCUMENT/DEFER recommendation? (ONLY include this field if recommendation is DOCUMENT or DEFER)",
    "next_steps": "Specific actions based on recommendation"
  }
}
`;
}



/**
 * Generates a prompt for an LLM to classify a list of GitHub issues.
 *
 * @param {Array<object>} issues - An array of issue objects, each with title, body, labels.
 * @returns {string} The prompt for the LLM.
 */
export function generateIssueTriagePrompt(issues) {
  const issuesString = issues.map(issue => `
<issue>
  <id>${issue.id}</id>
  <title>${issue.title}</title>
  <body>${issue.body || 'No body provided.'}</body>
  <labels>${issue.labels.join(', ') || 'No labels'}</labels>
</issue>
`).join('');

  return `You are an expert engineering Program Manager. Your task is to triage a list of GitHub issues and classify them.

Here is a list of issues:
${issuesString}

For each issue, classify it into one of the following categories:
- "feature": A request for a new feature or functionality.
- "bug": A report of a bug or incorrect behavior.
- "chore": A maintenance task, refactoring, or documentation update.
- "question": A user asking a question.
- "other": Anything else.

Focus on the user's intent. A bug report might be phrased as a feature request (e.g., "I want the app to not crash"), so read the body carefully.

Provide your response as a single JSON object with a key "triage_results". The value should be an array of objects, each containing the issue "id" and its "classification".

Example:
{
  "triage_results": [
    { "id": 12345, "classification": "feature" },
    { "id": 67890, "classification": "bug" }
  ]
}

Please provide ONLY the JSON object in your response.
`;
}

export function generateFeatureRolloutStrategyPrompt(designDoc, taskDetails, projectConstitution, isExperimental, retrievedContext = []) {
  const contextString = retrievedContext.length > 0 
    ? `\n**Relevant Past Rollouts & Incidents (Knowledge Base):**\n${retrievedContext.join('\n\n')}\n`
    : '';

  const projectType = projectConstitution?.projectType || 'application'; // Default to 'application'
  const isLibrary = projectType === 'library';

  // Extract key technical details for risk assessment
  const hasDataModelChanges = designDoc.high_level_design?.data_model_changes && 
    designDoc.high_level_design.data_model_changes !== 'N/A' && 
    designDoc.high_level_design.data_model_changes.trim() !== '';
  
  const hasSecurityImpact = designDoc.high_level_design?.security_considerations && 
    designDoc.high_level_design.security_considerations !== 'N/A' && 
    designDoc.high_level_design.security_considerations.trim() !== '';
  
  const architecturalDecisions = designDoc.high_level_design?.core_architectural_choices || [];
  const systemOverview = designDoc.high_level_design?.overall_system_overview || '';
  const keyComponents = designDoc.high_level_design?.key_components_and_responsibilities || [];

  // Phase and Metrics customization
  const phaseTemplate = isLibrary 
    ? `{
        "phase_name": "Phase 1: Alpha Release",
        "duration": "2-4 weeks",
        "target_population": "Internal team & select early adopters",
        "goals": ["Validate API stability", "Gather feedback on developer experience"],
        "graduation_criteria": ["No breaking API changes for 1 week", "Successful integration in 2+ internal projects"]
      },
      {
        "phase_name": "Phase 2: Beta Release",
        "duration": "4-6 weeks",
        "target_population": "Public beta testers",
        "goals": ["Identify bugs in wider use cases", "Finalize API surface"],
        "graduation_criteria": ["Critical bug count below 5", "Test coverage > 80%"]
      },
      {
        "phase_name": "Phase 3: General Availability (GA)",
        "duration": "Ongoing",
        "target_population": "All users",
        "goals": ["Ensure long-term stability", "Promote adoption"],
        "graduation_criteria": ["99.9% uptime for any associated services", "Comprehensive documentation published"]
      }`
    : `{
        "phase_name": "Phase 1: Internal Validation",
        "duration": "48 hours",
        "target_population": "Internal team",
        "goals": ["Goal 1", "Goal 2"],
        "graduation_criteria": ["No critical bugs for 24h", "Metric X remains stable"]
      }`;

  const monitoringTemplate = isLibrary 
    ? `[
      {
        "metric_name": "Unit Test Coverage",
        "type": "Quality",
        "threshold": "> 85%",
        "alert_level": "Warning",
        "notes": "Enforced by CI pipeline on every commit."
      },
      {
        "metric_name": "Integration Test Pass Rate",
        "type": "Quality",
        "threshold": "100%",
        "alert_level": "P0 (Blocks Release)",
        "notes": "Runs against a suite of sample applications."
      },
      {
        "metric_name": "Bundle Size",
        "type": "Performance",
        "threshold": "< 100KB gzipped",
        "alert_level": "Warning",
        "notes": "Track bundle size to prevent bloat."
      },
      {
        "metric_name": "API Documentation Coverage",
        "type": "Quality",
        "threshold": "100% for public APIs",
        "alert_level": "Warning",
        "notes": "Ensure all public methods and types are documented."
      }
    ]`
    : `[
      {
        "metric_name": "API Error Rate",
        "type": "System",
        "threshold": "< 0.5%",
        "alert_level": "P1",
        "notes": "Monitor on main application dashboard"
      },
      {
        "metric_name": "Feature Adoption",
        "type": "Business",
        "threshold": "> 10% after 7 days",
        "alert_level": "Info",
        "notes": "Tracked via analytics events"
      }
    ]`;
  
  return `You are an expert Principal Site Reliability Engineer. Your task is to create a concise, scannable, and action-oriented rollout strategy. Focus on clarity and what needs to be done, not prose. Use checklists and tables.

**DESIGN DOCUMENT ANALYSIS:**
- **Title:** ${designDoc.title}
- **Goals:** ${(designDoc.goals || []).join(', ')}
- **Project Type:** ${projectType}
- **Experimental:** ${isExperimental}
- **System Overview:** ${systemOverview}
- **Data Model Changes:** ${hasDataModelChanges ? 'YES' : 'NO'}
- **Security Impact:** ${hasSecurityImpact ? 'YES' : 'NO'}
${contextString}

**CRITICAL REQUIREMENTS:**
1.  **BE CONCISE & ACTION-ORIENTED:** Use checklists, tables, and short bullet points. Avoid long paragraphs.
2.  **NO VAGUE GUIDANCE:** Every metric, threshold, and action must be specific and measurable.
3.  **PRIORITIZE ACTIONS:** Start with a summary of the most critical actions and checks.
4.  **CLEAR OWNERSHIP (when possible):** Suggest roles (e.g., On-call, Eng Lead, PM).

**OUTPUT FORMAT (JSON ONLY):**

{
  "rollout_strategy": {
    "executive_summary": {
      "risk_level": "LOW|MEDIUM|HIGH|CRITICAL",
      "decision": "Recommended course of action (e.g., 'Proceed with caution, contingent on pre-flight checks')",
      "summary": "One-sentence summary of the rollout approach.",
      "key_next_actions": [
        "Actionable task 1 (e.g., 'Implement feature flag 'X'')",
        "Actionable task 2 (e.g., 'Set up P0 alert on metric 'Y'')"
      ]
    },
    "pre_flight_checklist": [
      { "item": "Feature flag logic implemented and tested", "owner": "Engineering" },
      { "item": "Required monitoring dashboards created and validated", "owner": "On-call" },
      { "item": "Rollback procedure manually verified in staging", "owner": "Engineering" },
      { "item": "Customer support documentation drafted", "owner": "PM/Support" }
    ],
    "risk_assessment": {
      "key_risks": [
        { "risk": "Specific failure mode 1", "impact": "Potential user/system impact", "mitigation": "How to prevent or handle it" }
      ],
      "technical_constraints": ["e.g., 'Database migration must complete before feature flag activation'"]
    },
    "rollout_phases": [
      ${phaseTemplate}
    ],
    "monitoring": ${monitoringTemplate},
    "contingency_plan": {
      "rollback_complexity": "IMMEDIATE|MODERATE|COMPLEX",
      "rollback_steps": [
        "Step 1: Disable feature flag 'X' in launch-config.",
        "Step 2: Run cleanup script 'scripts/cleanup-tokens.js'.",
        "Step 3: Verify endpoint '/api/feature' returns 404."
      ],
      "communication_plan": "On-call notifies Eng Lead via Slack channel #eng-deploys. PM updates status page."
    }
  }
}
`;
}

// Helper function to format deployment constitution with only populated fields
function formatDeploymentConstitution(deploymentConstitution, includeGuidance = false) {
  if (!deploymentConstitution) {
    return '';
  }

  const { 
    provider, 
    compute, 
    data_stores, 
    containerization, 
    deploymentPipeline,
    hosting_services,
    deployment_tools,
    monitoring_tools,
    serverless_functions,
    storage_services,
    compute_services
  } = deploymentConstitution;

  const sections = [];
  const originalFieldCount = Object.keys(deploymentConstitution).length;

  // Only add fields that have meaningful values
  if (provider) {
    sections.push(`- **Hosting Provider:** ${provider}`);
  }

  if (compute?.length > 0) {
    sections.push(`- **Compute Environment:** ${compute.map(c => c.type).join(', ')}`);
  }

  if (data_stores?.length > 0) {
    sections.push(`- **Existing Data Stores:** ${data_stores.map(d => `${d.name} (${d.type})`).join(', ')}`);
  }

  if (containerization?.type) {
    const baseImageText = containerization.base_image ? ` (Base: ${containerization.base_image})` : '';
    sections.push(`- **Containerization:** ${containerization.type}${baseImageText}`);
  }

  if (deploymentPipeline?.deployed_to?.length > 0) {
    sections.push(`- **Deployment Pipeline:** ${deploymentPipeline.deployed_to.join(', ')}`);
  }

  if (deploymentPipeline?.has_tests !== undefined) {
    sections.push(`- **Testing Integration:** Automated tests are ${deploymentPipeline.has_tests ? 'configured' : 'not configured'}`);
  }

  if (serverless_functions?.length > 0) {
    const functions = serverless_functions.map(f => `${f.name} (${f.provider})`).join(', ');
    sections.push(`- **Serverless Functions:** ${functions}`);
  }

  if (storage_services?.length > 0) {
    const storage = storage_services.map(s => `${s.name} (${s.type})`).join(', ');
    sections.push(`- **Storage Services:** ${storage}`);
  }

  if (hosting_services?.length > 0) {
    sections.push(`- **Hosting Services:** ${hosting_services.join(', ')}`);
  }

  if (monitoring_tools?.length > 0) {
    sections.push(`- **Monitoring Tools:** ${monitoring_tools.join(', ')}`);
  }

  if (deployment_tools?.length > 0) {
    sections.push(`- **Deployment Tools:** ${deployment_tools.join(', ')}`);
  }

  if (compute_services?.length > 0) {
    const compute = compute_services.map(c => `${c.name} (${c.type})`).join(', ');
    sections.push(`- **Compute Services:** ${compute}`);
  }

  // Return empty string if no meaningful data
  if (sections.length === 0) {
    console.log('[Deployment Constitution] No populated fields found, skipping deployment context');
    return '';
  }

  // Log filtering information
  console.log(`[Deployment Constitution] Filtered deployment context: ${sections.length} populated fields out of ${originalFieldCount} total fields`);

  let result = sections.join('\n');

  // Add guidance section if requested
  if (includeGuidance) {
    result += '\n\n**🎯 DEPLOYMENT-AWARE DECISION CRITERIA:**\n';
    result += '- **Prefer Existing Stack:** Options that leverage existing infrastructure should be strongly favored\n';
    result += '- **Operational Complexity:** Consider deployment, monitoring, and maintenance overhead of new vs. existing technologies\n';
    result += '- **Data Store Alignment:** When data storage is involved, prefer extending existing data stores over introducing new ones\n';
    result += '- **Pipeline Compatibility:** Ensure new components integrate smoothly with existing deployment and testing pipelines\n';
    result += '- **Monitoring Integration:** Consider how new components will be monitored and observed in the current environment\n';
  }

  return result;
}
