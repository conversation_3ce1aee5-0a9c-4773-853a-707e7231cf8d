import { Anthropic } from '@anthropic-ai/sdk';
import { generatePrompt } from './prompt.js';
import { logger } from '../utils/logger.js';
import dotenv from 'dotenv';

let anthropic;

function initAnthropic() {
// Explicitly load environment variables
dotenv.config();

// Validate required environment variables
if (!process.env.ANTHROPIC_API_KEY) {
  logger.error("ANTHROPIC_API_KEY is not defined in environment variables");
  throw new Error("Missing required environment variable: ANTHROPIC_API_KEY");
}

// Initialize Anthropic client with error handling
try {
  logger.debug("Initializing Anthropic client...");
  anthropic = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
  });
  logger.debug("Anthropic client initialized successfully");
} catch (error) {
  logger.error("Failed to initialize Anthropic client:", error);
  throw new Error(`Failed to initialize Anthropic client: ${error.message}`);
}
}

/**
 * Extract architectural knowledge from PR
 * @param {Object} pr - PR details
 * @param {Array} files - PR files
 * @param {Array} comments - PR comments
 * @returns {Promise<any>} - The extracted knowledge object or null.
 */
export async function extractKnowledge(pr, files, comments) {
  try {
    logger.info(`Extracting knowledge from PR #${pr.number}: ${pr.title}`);
    
    // Validate input parameters
    if (!pr || !pr.number) {
      logger.error("Invalid PR object:", pr);
      throw new Error("Invalid PR object provided");
    }
    
    logger.debug(`PR object: ${JSON.stringify(pr, null, 2)}`);
    logger.debug(`Files count: ${files?.length || 0}`);
    logger.debug(`Comments count: ${comments?.length || 0}`);
    
    // Prepare PR context for prompt
    const prContext = {
      number: pr.number,
      title: pr.title,
      body: pr.body || '',
      author: pr.author,
      created_at: pr.created_at,
      html_url: pr.html_url,
      files: files?.map(file => ({
        filename: file.filename,
        status: file.status,
        additions: file.additions,
        deletions: file.deletions
      })) || []
    };
    
    // Extract code changes from the files
    const codeChanges = files
      .filter(file => file.key_changes && file.key_changes.length > 0)
      .map(file => ({
        filename: file.filename,
        changes: file.key_changes,
        patch: file.patch?.length > 1000 ? file.patch.substring(0, 1000) + '...' : file.patch
      }));
    
    logger.debug(`Extracted ${codeChanges.length} files with key changes`);
    
    // Extract relevant comments
    const sortedComments = comments
      .filter(comment => comment.body && comment.user && !comment.user.login.includes('[bot]')) // Filter out bot comments
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()); // Sort by date descending, using getTime()

    // Format comments
    const formattedComments = sortedComments.map(comment => (
      {
        author: comment.author,
        body: comment.body.length > 500 
          ? comment.body.substring(0, 500) + '...' // Truncate long comments
          : comment.body
      }
    ));
    
    logger.debug(`Extracted ${formattedComments.length} relevant comments`);
    
    // Generate prompt for Claude
    const prompt = generatePrompt(prContext, codeChanges, formattedComments);
    logger.debug(`Generated prompt (length: ${prompt.length} characters)`);
    
    // Validate Anthropic client
    if (!anthropic || !anthropic.messages || !anthropic.messages.create) {
      logger.error("Anthropic client not properly initialized:", anthropic);
      initAnthropic();
    }
    
    // Extract knowledge using Claude
    logger.debug("Calling Anthropic API...");
    
    // Log the model being used
    const model = process.env.ANTHROPIC_MODEL || "claude-sonnet-4-20250514";
    logger.debug(`Using model: ${model}`);
    
    // Make the API call with more detailed error handling
    let message;
    try {
      message = await anthropic.messages.create({
        model: model,
        max_tokens: 4000,
        messages: [{ role: "user", content: prompt }]
      });
      logger.debug("Received response from Anthropic API");
    } catch (error) {
      logger.error("Error calling Anthropic API:", error);
      throw new Error(`Error calling Anthropic API: ${error.message}`);
    }
    
    // Validate the response
    if (!message || !message.content || !message.content[0] || !message.content[0].text) {
      logger.error("Invalid response from Anthropic API:", message);
      throw new Error("Invalid response from Anthropic API");
    }
    
    // Parse the JSON response
    const responseText = message.content[0].text;
    logger.debug(`Response length: ${responseText.length} characters`);
    
    // Try to extract JSON from the response
    let knowledge;
    try {
      // Look for JSON block
      const jsonMatch = responseText.match(/```json\n([\s\S]*?)\n```/) || 
                         responseText.match(/```\n([\s\S]*?)\n```/) || 
                         [null, responseText];
      
      const knowledgeJson = jsonMatch[1].trim();
      logger.debug(`Extracted JSON (length: ${knowledgeJson.length} characters)`);
      
      knowledge = JSON.parse(knowledgeJson);
      logger.debug("Successfully parsed JSON response");
    } catch (error) {
      logger.warn(`Could not parse Claude response as JSON: ${error.message}`);
      logger.debug("Raw response:", responseText);
      
      // Create a minimal knowledge object if parsing fails
      knowledge = {
        architectural_decisions: [],
        raw_response: responseText
      };
    }
    
    // Add metadata
    knowledge.metadata = {
      pr_number: pr.number,
      pr_title: pr.title,
      pr_url: pr.html_url,
      author: pr.author,
      created_at: pr.created_at,
      extracted_at: new Date().toISOString(),
      files: prContext.files.map(f => f.filename)
    };
    
    return knowledge;
  } catch (error) {
    logger.error(`Error extracting knowledge from PR #${pr?.number}:`, error);
    throw new Error(`Failed to extract knowledge from PR #${pr?.number}: ${error.message}`);
  }
}

/**
 * Check if a comment is meaningful
 * @param {string} body - Comment body
 * @returns {boolean} - True if meaningful
 */
function isMeaningfulComment(body) {
  if (!body || body.trim().length < 10) {
    return false;
  }
  
  // Skip automated comments
  const automatedPatterns = [
    /branch has been rebased/i,
    /branch is up to date/i,
    /Merged pull request/i,
    /LGTM/i,
    /CI failed/i,
    /CI passed/i,
    /tests failed/i,
    /tests passed/i,
    /All checks have passed/i,
    /Deployment status/i
  ];
  
  if (automatedPatterns.some(pattern => pattern.test(body))) {
    return false;
  }
  
  return true;
}