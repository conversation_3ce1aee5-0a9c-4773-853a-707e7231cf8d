import { useCallback } from 'react';
import type { WizardState, GitHubIssue } from '../types/design-doc-wizard';
import { generateSessionId } from '../utils/design-doc-wizard';

interface UseWizardNavigationProps {
  wizardState: WizardState;
  setWizardState: React.Dispatch<React.SetStateAction<WizardState>>;
  analyzeAllDecisions: (decisions: any[]) => Promise<void>;
}

export function useWizardNavigation({ 
  wizardState, 
  setWizardState, 
  analyzeAllDecisions 
}: UseWizardNavigationProps) {

  // Navigation helpers
  const goToNextDecision = () => {
    if (wizardState.currentDecisionIndex < wizardState.decisionPoints.length - 1) {
      setWizardState(prev => ({
        ...prev,
        currentDecisionIndex: prev.currentDecisionIndex + 1
      }));
    }
  };

  const goToPreviousDecision = () => {
    if (wizardState.currentDecisionIndex > 0) {
      setWizardState(prev => ({
        ...prev,
        currentDecisionIndex: prev.currentDecisionIndex - 1
      }));
    }
  };

  const handleConfirmRequirements = useCallback(async () => {
    // Move to task verification step to allow user to verify the AI analysis
    setWizardState(prev => ({ ...prev, currentStep: 'task-verification' }));
  }, [setWizardState]);

  const handleImportFromIssue = useCallback(() => {
    // This would need fetchIssues to be passed in or handled differently
    setWizardState(prev => ({ ...prev, showIssueSelector: true }));
  }, [setWizardState]);

  const handleSelectIssue = useCallback((issue: GitHubIssue) => {
    setWizardState(prev => ({
      ...prev,
      taskDetails: {
        ...prev.taskDetails,
        title: issue.title,
        description: issue.body || '',
      },
      showIssueSelector: false
    }));
  }, [setWizardState]);

  const handleConstitutionSubmit = useCallback(() => {
    // Logic to save constitution if needed
    setWizardState(prev => ({ ...prev, currentStep: 'task-definition', isMinimalScope: false }));
  }, [setWizardState]);

  const resetWizardForNewDocument = useCallback(() => {
    // Reset wizard for new document
    setWizardState({
      sessionId: generateSessionId(),
      currentStep: 'task-definition',
      taskDetails: { title: '', description: '', initialIdeas: '' },
      decisionPoints: [],
      currentDecisionIndex: 0,
      isLoading: false,
      nonGoals: [],
      projectConstitution: wizardState.projectConstitution, // Persist constitution
      isMinimalScope: false // Clear minimal scope flag for new document
    });
  }, [wizardState.projectConstitution, setWizardState]);

  return {
    goToNextDecision,
    goToPreviousDecision,
    handleConfirmRequirements,
    handleImportFromIssue,
    handleSelectIssue,
    handleConstitutionSubmit,
    resetWizardForNewDocument,
  };
} 