import { useCallback } from 'react';
import type { 
  WizardState, 
  DecisionPoint, 
  UseDesignDocActionsProps,
  DecisionReviewPreferences,
  DecisionProcessingResult
} from '../types/design-doc-wizard';
import { DEFAULT_REVIEW_PREFERENCES } from '../types/design-doc-wizard';
import { DecisionClassifier } from '../utils/decisionClassification';

export function useDesignDocActions({
  wizardState,
  setWizardState,
  repositorySlug,
  installationId,
  isPublic,
  supabase,
  apiKey,
  githubHandle,
  setIssues,
  setIsLoadingIssues
}: UseDesignDocActionsProps) {

  // Get decision review preferences with fallback to defaults
  const getReviewPreferences = useCallback((): DecisionReviewPreferences => {
    return wizardState.projectConstitution?.decisionReviewPreferences || DEFAULT_REVIEW_PREFERENCES;
  }, [wizardState.projectConstitution]);

  // Analyze all decisions with intelligent classification and auto-processing
  const analyzeAllDecisions = useCallback(async (decisions: DecisionPoint[]) => {
    if (!decisions || decisions.length === 0) return;

    // Check if decisions have already been analyzed - if so, skip to decision-making step
    const hasAnalyzedDecisions = decisions.some(d => d.analysis || (d.options && d.options.length > 0));
    if (hasAnalyzedDecisions) {
      console.log('Decisions already analyzed, navigating to decision-making step');
      setWizardState((prev: WizardState) => ({ 
        ...prev, 
        currentStep: 'decision-making',
        showDecisionProcessingSummary: false // Skip summary if already processed
      }));
      return;
    }

    setWizardState((prev: WizardState) => ({ ...prev, isLoading: true }));

    try {
      console.log(`Starting intelligent analysis of ${decisions.length} decisions...`);
      
      // Track progress for UI updates
      let completedCount = 0;
      const totalCount = decisions.length;
      
      // Create all API calls in parallel using Promise.all
      const analysisPromises = decisions.map(async (decision, index) => {
        console.log(`Starting analysis for decision ${index + 1}/${decisions.length}: ${decision.title}`);

        try {
        const response = await fetch('/api/design-doc-wizard/decision-context', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            decisionPoint: decision,
            taskAnalysis: wizardState.taskAnalysis, // Pass the verified task analysis
            taskDetails: wizardState.taskDetails, // Pass taskDetails for experimental flag
            projectConstitution: wizardState.projectConstitution,
            repositorySlug,
            installationId: installationId || undefined,
            isPublic
          })
        });

        if (!response.ok) {
          console.error(`Failed to analyze decision ${decision.title}:`, response.statusText);
            completedCount++;
            console.log(`Progress: ${completedCount}/${totalCount} decisions analyzed (${Math.round(completedCount/totalCount*100)}%)`);
            
            // Return the decision without analysis rather than failing completely
            return {
              ...decision,
              analysis: undefined,
              options: []
            };
        }

        const data = await response.json();
        
          completedCount++;
          console.log(`[Frontend] Parallel analysis - Decision ${index + 1} (${decision.title}) completed (${completedCount}/${totalCount})`);
        
        // Update the decision with the enhanced data including options
        const analysisData = data.enhancedDecisionPoint || data;
        console.log(analysisData.recommendation_rationale);
        console.log(analysisData.recommended_option_id);
        
          return {
          ...decision,
          analysis: analysisData, // Assign the whole analysis object
          options: analysisData.options || [], // Use 'options' directly from the API response
          // Ensure we preserve the original ID and basic info
          id: decision.id,
          title: decision.title,
          description: decision.description,
          recommended_option_id: analysisData.recommended_option_id,
          recommendation_rationale: analysisData.recommendation_rationale
          };

        } catch (error: any) {
          console.error(`Error analyzing decision ${decision.title}:`, error);
          completedCount++;
          console.log(`Progress: ${completedCount}/${totalCount} decisions processed (${Math.round(completedCount/totalCount*100)}%) - with error`);
          
          return {
            ...decision,
            analysis: undefined,
            options: []
          };
        }
      });

      // Wait for all analyses to complete in parallel
      const updatedDecisions = await Promise.all(analysisPromises);
      
      // Count successful vs failed analyses
      const successCount = updatedDecisions.filter(d => d.analysis).length;
      const failureCount = updatedDecisions.filter(d => !d.analysis).length;
      
      console.log(`Parallel decision analysis complete: ${successCount} successful, ${failureCount} failed out of ${decisions.length} total`);
      
      if (failureCount > 0) {
        console.warn('Some decisions failed to analyze:', updatedDecisions.filter(d => !d.analysis).map(d => d.title));
      }

      // Now apply intelligent decision processing
      const reviewPreferences = getReviewPreferences();
      const processingResults = DecisionClassifier.processDecisions(updatedDecisions, reviewPreferences);
      
      // Separate decisions based on processing results
      const autoProcessedDecisions = updatedDecisions.filter((decision, index) => 
        processingResults[index]?.autoProcessed
      );
      
      const pendingReviewDecisions = updatedDecisions.filter((decision, index) => 
        processingResults[index]?.classification.requiresReview
      );

      // Apply auto-selections to decisions
      const decisionsWithAutoSelections = updatedDecisions.map((decision, index) => {
        const result = processingResults[index];
        if (result?.autoProcessed && result.selectedOption) {
          return {
            ...decision,
            selectedOption: result.selectedOption,
            rationale: result.rationale
          };
        }
        return decision;
      });

      const processingStats = DecisionClassifier.getProcessingSummary(processingResults);
      console.log('Decision processing summary:', processingStats);

      // Determine next step based on review preferences and results
      let nextStep: WizardState['currentStep'] = 'decision-making';
      
      if (reviewPreferences.reviewStepByStep || pendingReviewDecisions.length === 0) {
        // If user wants step-by-step or no decisions need review, go to decision-making
        nextStep = 'decision-making';
      } else {
        // If auto-advance is enabled and there are decisions that need review
        nextStep = 'decision-making';
      }

      setWizardState((prev: WizardState) => ({
        ...prev,
        decisionPoints: decisionsWithAutoSelections,
        decisionProcessingResults: processingResults,
        autoProcessedDecisions,
        pendingReviewDecisions,
        currentStep: nextStep,
        currentDecisionIndex: 0,
        isLoading: false,
        showDecisionProcessingSummary: processingStats.autoProcessed > 0
      }));

    } catch (error: any) {
      console.error('Error in parallel decision analysis:', error);
      setWizardState((prev: WizardState) => ({
        ...prev,
        error: error.message,
        isLoading: false,
        currentStep: 'decision-discovery' // Go back to previous step
      }));
    }
  }, [wizardState.taskDetails, wizardState.taskAnalysis, repositorySlug, installationId, isPublic, wizardState.projectConstitution, setWizardState, getReviewPreferences]);

  // Process a single decision with intelligent classification
  const processDecisionWithClassification = useCallback((decision: DecisionPoint): DecisionProcessingResult => {
    const reviewPreferences = getReviewPreferences();
    return DecisionClassifier.processDecisions([decision], reviewPreferences)[0];
  }, [getReviewPreferences]);

  // Override auto-processed decision
  const overrideAutoProcessedDecision = useCallback((decisionId: string, optionId: string, rationale: string) => {
    setWizardState((prev: WizardState) => ({
      ...prev,
      decisionPoints: prev.decisionPoints.map((dp: DecisionPoint) =>
        dp.id === decisionId
          ? { ...dp, selectedOption: optionId, rationale }
          : dp
      ),
      decisionProcessingResults: prev.decisionProcessingResults?.map((result: DecisionProcessingResult) =>
        result.decisionId === decisionId
          ? { ...result, selectedOption: optionId, rationale, userOverride: true, autoProcessed: false }
          : result
      )
    }));
  }, [setWizardState]);

  // Bulk approve auto-processed decisions
  const approveAutoProcessedDecisions = useCallback(() => {
    setWizardState((prev: WizardState) => {
      const autoProcessedResults = prev.decisionProcessingResults?.filter((r: DecisionProcessingResult) => r.autoProcessed) || [];
      console.log(`Approving ${autoProcessedResults.length} auto-processed decisions`);
      
      return {
        ...prev,
        showDecisionProcessingSummary: false
      };
    });
  }, [setWizardState]);

  // Phase 1: Discover decision points and immediately analyze them
  const discoverDecisionPoints = useCallback(async () => {
    if (!wizardState.taskDetails.title || !wizardState.taskDetails.description) return;

    setWizardState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await fetch('/api/design-doc-wizard/discover-decisions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskDetails: wizardState.taskDetails,
          taskAnalysis: wizardState.taskAnalysis,
          projectConstitution: wizardState.projectConstitution,
          repositorySlug,
          installationId: installationId || undefined,
          isPublic
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to discover decision points: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Update state with discovered decisions and strategic analysis
      setWizardState(prev => ({
        ...prev,
        decisionPoints: data.decisionPoints || [],
        nonGoals: data.nonGoals || [],
        taskAnalysis: data.taskAnalysis || prev.taskAnalysis,
        strategicAnalysis: data.strategicAnalysis || undefined, // Store strategic analysis for milestone prioritization
        currentStep: 'decision-discovery', // Go to decision discovery step
        isLoading: false
      }));

    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false
      }));
    }
  }, [wizardState.taskDetails, wizardState.taskAnalysis, wizardState.projectConstitution, repositorySlug, installationId, isPublic, setWizardState]);

  // Phase 2: Get context for a specific decision point
  const getDecisionContext = useCallback(async (decisionPoint: DecisionPoint) => {
    setWizardState(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await fetch('/api/design-doc-wizard/decision-context', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          decisionPoint,
          taskAnalysis: wizardState.taskAnalysis, // Pass the verified task analysis
          taskDetails: wizardState.taskDetails, // Pass taskDetails for experimental flag
          projectConstitution: wizardState.projectConstitution,
          repositorySlug,
          installationId: installationId || undefined,
          isPublic
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to get decision context: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Add comprehensive logging of the API response
      console.log('[Frontend] getDecisionContext - Full API response:', JSON.stringify(data, null, 2));
      console.log('[Frontend] getDecisionContext - data.success:', data.success);
      console.log('[Frontend] getDecisionContext - data.enhancedDecisionPoint:', data.enhancedDecisionPoint);
      console.log('[Frontend] getDecisionContext - data.enhancedDecisionPoint?.options:', data.enhancedDecisionPoint?.options);
      console.log('[Frontend] getDecisionContext - data.enhancedDecisionPoint?.options?.length:', data.enhancedDecisionPoint?.options?.length);
      
      // Update the specific decision point with context
      const analysisData = data.enhancedDecisionPoint || data;
      console.log('[Frontend] getDecisionContext - analysisData:', analysisData);
      console.log('[Frontend] getDecisionContext - analysisData.options:', analysisData.options);
      console.log('[Frontend] getDecisionContext - analysisData.potential_approaches:', analysisData.potential_approaches);
      
      // Fix: Use the correct field name from the API response
      const optionsFromAPI = analysisData.options || []; // Use 'options' instead of 'potential_approaches'
      console.log('[Frontend] getDecisionContext - optionsFromAPI:', optionsFromAPI);
      console.log('[Frontend] getDecisionContext - optionsFromAPI.length:', optionsFromAPI.length);
      
      setWizardState(prev => ({
        ...prev,
        decisionPoints: prev.decisionPoints.map(dp => 
          dp.id === decisionPoint.id 
            ? { 
                ...dp,
                ...analysisData, // Spread the analysis data here
                options: optionsFromAPI, // Explicitly set options
              }
            : dp
        ),
        isLoading: false
      }));
      
      console.log('[Frontend] getDecisionContext - State updated successfully');
    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false
      }));
    }
  }, [wizardState.taskDetails, wizardState.taskAnalysis, repositorySlug, installationId, isPublic, wizardState.projectConstitution, setWizardState]);

  // Handle decision selection
  const selectDecisionOption = useCallback((decisionPointId: string, optionId: string, rationale: string) => {
    setWizardState(prev => ({
      ...prev,
      decisionPoints: prev.decisionPoints.map(dp =>
        dp.id === decisionPointId
          ? { ...dp, selectedOption: optionId, rationale }
          : dp
      )
    }));
  }, [setWizardState]);

  // Generate final design document
  const generateDesignDoc = useCallback(async () => {
    // Check if design document already exists - if so, just navigate to review step
    if (wizardState.generatedDoc) {
      console.log('Design document already exists, navigating to review step');
      setWizardState(prev => ({ ...prev, currentStep: 'review-generation' }));
      return;
    }

    setWizardState(prev => ({ ...prev, isLoading: true, currentStep: 'review-generation' }));

    try {
      const response = await fetch('/api/design-doc-wizard/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskDetails: wizardState.taskDetails,
          taskAnalysis: wizardState.taskAnalysis,
          decisionPoints: wizardState.decisionPoints,
          projectConstraints: wizardState.projectConstraints,
          projectConstitution: wizardState.projectConstitution, // Pass project constitution for strategic guidance
          repositorySlug,
          installationId: installationId || undefined,
          isPublic
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate design document: ${response.statusText}`);
      }

      const data = await response.json();
      
      setWizardState(prev => ({
        ...prev,
        generatedDoc: data.designDoc,
        isLoading: false
      }));
    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false
      }));
    }
  }, [wizardState.taskDetails, wizardState.taskAnalysis, wizardState.decisionPoints, wizardState.projectConstraints, wizardState.projectConstitution, repositorySlug, installationId, isPublic, setWizardState]);

  // Fetch GitHub issues
  const fetchIssues = useCallback(async () => {
    if (!repositorySlug || !apiKey || !githubHandle) {
      console.warn('Cannot fetch issues: missing repositorySlug, apiKey, or githubHandle.');
      return;
    }
    
    setIsLoadingIssues(true);
    try {
      const response = await fetch('/api/github/issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repository_slug: repositorySlug,
          github_handle: githubHandle,
          api_key: apiKey,
        })
      });

      if (response.ok) {
        const data = await response.json();
        setIssues(data || []);
      } else {
        console.error('Failed to fetch issues:', response.statusText);
        setIssues([]);
      }
    } catch (error: any) {
      console.error('Error fetching issues:', error);
      setIssues([]);
    } finally {
      setIsLoadingIssues(false);
    }
  }, [repositorySlug, apiKey, githubHandle, setIssues, setIsLoadingIssues]);

  // Regenerate all decisions
  const regenerateAllDecisions = useCallback(async () => {
    if (wizardState.decisionPoints.length === 0) return;
    
    setWizardState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const response = await fetch('/api/design-doc-wizard/discover-decisions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskTitle: wizardState.taskDetails.title,
          taskDescription: wizardState.taskDetails.description + `\n\nCurrent decisions to analyze:\n${wizardState.decisionPoints
            .map((dp, idx) => `${idx + 1}. ${dp.title}: ${dp.description}`)
            .join('\n')}`,
          initialIdeas: wizardState.taskDetails.initialIdeas,
          projectConstitution: wizardState.projectConstitution,
          repositorySlug,
          installationId: installationId || undefined,
          isPublic
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.decisionPoints && data.decisionPoints.length > 0) {
          // Update existing decisions with new context
          const updatedDecisions = wizardState.decisionPoints.map((existingDecision, index) => {
            const newContextData = data.decisionPoints[index];
            if (newContextData) {
              return {
                ...existingDecision,
                relatedDecisions: newContextData.relatedDecisions || existingDecision.relatedDecisions,
                searchQueries: newContextData.searchQueries || existingDecision.searchQueries
              };
            }
            return existingDecision;
          });
          
          setWizardState(prev => ({
            ...prev,
            decisionPoints: updatedDecisions
          }));
        }
      }
    } catch (error: any) {
      console.error('Error regenerating decisions:', error);
      setWizardState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false
      }));
    }
  }, [wizardState.decisionPoints, wizardState.taskDetails, wizardState.projectConstitution, repositorySlug, installationId, isPublic, setWizardState]);

  // AI Seeding for Constitution
  const seedConstitution = useCallback(async (constitutionSeedText: string) => {
    if (!constitutionSeedText) return;
    setWizardState(prev => ({ ...prev, isSeedingConstitution: true, error: undefined }));
    try {
      const response = await fetch('/api/constitution/seed', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rawText: constitutionSeedText })
      });

      if (!response.ok) {
        throw new Error('Failed to seed constitution from text.');
      }

      const seededData = await response.json();

      setWizardState(prev => ({
        ...prev,
        projectConstitution: {
          ...prev.projectConstitution,
          ...seededData,
        },
        userJourneys: seededData.seed_user_journeys || prev.userJourneys,
      }));
      
    } catch (error: any) {
      setWizardState(prev => ({ ...prev, error: error.message }));
    } finally {
      setWizardState(prev => ({ ...prev, isSeedingConstitution: false }));
    }
  }, [setWizardState]);

  return {
    analyzeAllDecisions,
    processDecisionWithClassification,
    overrideAutoProcessedDecision,
    approveAutoProcessedDecisions,
    discoverDecisionPoints,
    getDecisionContext,
    selectDecisionOption,
    generateDesignDoc,
    fetchIssues,
    regenerateAllDecisions,
    seedConstitution
  };
} 