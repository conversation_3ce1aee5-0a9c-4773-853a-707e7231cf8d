import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { createBrowserClient } from '@supabase/ssr';
import type { Session } from '@supabase/supabase-js';
import type { 
  WizardState, 
  DecisionPoint, 
  TaskDetails, 
  TaskAnalysis, 
  ProjectConstitution,
  DecisionOption,
  GitHubIssue,
  PriorityId
} from '../types/design-doc-wizard';
import { TECHNICAL_PRIORITIES } from '../types/design-doc-wizard';
import { generateSessionId } from '../utils/design-doc-wizard';

/**
 * Design Doc Wizard State Management Hook
 * 
 * This hook provides comprehensive state management for the design document wizard,
 * including:
 * - Automatic state persistence across browser sessions
 * - Smart navigation that preserves generated content
 * - Navigation guards that validate prerequisites
 * - Prevention of unnecessary LLM regeneration
 * - Persistent session IDs for shareable links
 */
export function useDesignDocWizard() {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const [constitutionSeedText, setConstitutionSeedText] = useState('');
  const [showIssueSelector, setShowIssueSelector] = useState(false);
  const [issues, setIssues] = useState<GitHubIssue[]>([]);
  const [isLoadingIssues, setIsLoadingIssues] = useState(false);
  const [installationId, setInstallationId] = useState<string>('');
  const [isPublic, setIsPublic] = useState<boolean>(false);
  const [githubHandle, setGithubHandle] = useState<string>('');
  const [apiKey, setApiKey] = useState<string>('');
  const [dbSessionId, setDbSessionId] = useState<string | null>(null);
  const [isLoadingSessionData, setIsLoadingSessionData] = useState(false);

  // Wizard state with persistence
  const [wizardState, setWizardState] = useState<WizardState>({
    sessionId: searchParams?.get('sessionId') || generateSessionId(),
    currentStep: searchParams?.get('step') as any || 'initializing',
    taskDetails: { title: '', description: '', initialIdeas: '' },
    decisionPoints: [],
    currentDecisionIndex: 0,
    isLoading: false,
    projectConstitution: {
      companyStage: 'not-set',
      projectType: 'not-set',
      priorities: TECHNICAL_PRIORITIES.map(p => p.id),
      architecturalPrinciples: '',
      productAndBusinessContext: '',
      primaryLanguage: '',
    }
  });

  // Repository context from URL
  const [repositorySlug, setRepositorySlug] = useState<string>('');

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Create a new session in the database
  const createSession = useCallback(async (repo: string, owner: string, installationId: string) => {
    try {
      const response = await fetch('/api/design-doc-wizard/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ repo, owner, installationId })
      });
      
      if (response.ok) {
        const data = await response.json();
        setDbSessionId(data.sessionId);
        
        // Update the wizard state with the new session ID
        setWizardState(prev => ({ ...prev, sessionId: data.sessionId }));
        
        // Update URL with session ID
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('sessionId', data.sessionId);
        window.history.replaceState({}, '', currentUrl.toString());
        
        return data.sessionId;
      }
    } catch (error) {
      console.warn('Failed to create session:', error);
    }
    return null;
  }, []);

  // Persist state to server
  const persistState = useCallback(async (state: WizardState) => {
    if (!dbSessionId) return;
    
    try {
      // Extract title from task details or generated doc
      const title = state.generatedDoc?.title || state.taskDetails?.title || 'Untitled Design Document';
      
      // Extract GitHub issue URL if present in task description
      const githubIssueMatch = state.taskDetails?.description?.match(/https:\/\/github\.com\/[^\/]+\/[^\/]+\/issues\/\d+/);
      const githubIssueUrl = githubIssueMatch ? githubIssueMatch[0] : undefined;
      
      await fetch('/api/design-doc-wizard/session', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: dbSessionId,
          wizardState: state,
          title,
          githubIssueUrl
        })
      });
    } catch (error) {
      console.warn('Failed to persist state:', error);
    }
  }, [dbSessionId]);

  // Load state from server
  const loadPersistedState = useCallback(async (sessionId: string): Promise<WizardState | null> => {
    try {
      const response = await fetch(`/api/design-doc-wizard/session/${sessionId}`);
      if (response.ok) {
        const data = await response.json();
        const parsedState = data.wizard_state;
        // Validate the state structure
        if (parsedState && parsedState.sessionId && parsedState.currentStep && parsedState.taskDetails) {
          setDbSessionId(sessionId);
          return parsedState;
        }
      }
    } catch (error) {
      console.warn('Failed to load persisted state:', error);
    }
    return null;
  }, []);

  // Set repo context and load persisted state on mount
  useEffect(() => {
    // Extract repository context from URL search params
    const repoSlugFromUrl = searchParams?.get('repositorySlug');
    const installationIdFromUrl = searchParams?.get('installationId');
    const isPublicFromUrl = searchParams?.get('isPublic') === 'true';

    if (repoSlugFromUrl) {
      setRepositorySlug(repoSlugFromUrl);
      console.log('Set repository context from URL:', repoSlugFromUrl);
    }
    if (installationIdFromUrl) {
      setInstallationId(installationIdFromUrl);
    }
    setIsPublic(isPublicFromUrl);

    // Load persisted state for the session
    const sessionIdFromUrl = searchParams?.get('sessionId');
    if (sessionIdFromUrl) {
      const loadAsync = async () => {
        const persistedState = await loadPersistedState(sessionIdFromUrl);
        if (persistedState) {
          setWizardState(persistedState);
          console.log('Loaded persisted state for session:', sessionIdFromUrl);
        }
      };
      loadAsync();
    }
  }, [searchParams, loadPersistedState]);

  // Initialize session and auth
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, newSession) => {
      setSession(newSession);
      setIsLoadingSession(false);
      
      // Extract GitHub handle from session
      if (newSession?.user?.user_metadata?.user_name) {
        setGithubHandle(newSession.user.user_metadata.user_name);
      }
    });

    supabase.auth.getSession().then(({ data: { session: initialSession } }) => {
      setSession(initialSession);
      setIsLoadingSession(false);
      
      // Extract GitHub handle from initial session
      if (initialSession?.user?.user_metadata?.user_name) {
        setGithubHandle(initialSession.user.user_metadata.user_name);
      }
    });

    return () => subscription.unsubscribe();
  }, [supabase.auth]);

  // Create database session when we have repository context and user is authorized
  useEffect(() => {
    const shouldCreateSession = 
      isAuthorized && 
      repositorySlug && 
      installationId && 
      !dbSessionId && 
      wizardState.currentStep !== 'initializing';

    if (shouldCreateSession) {
      const [owner, repo] = repositorySlug.split('/');
      if (owner && repo) {
        createSession(repo, owner, installationId);
      }
    }
  }, [isAuthorized, repositorySlug, installationId, dbSessionId, wizardState.currentStep, createSession]);

  // Persist state whenever it changes
  useEffect(() => {
    // Persist state for all steps except initializing, and always persist if there's generated content
    const hasGeneratedContent = 
      (wizardState.userJourneys && wizardState.userJourneys.length > 0) ||
      wizardState.taskAnalysis ||
      wizardState.strategicAssessment ||
      (wizardState.decisionPoints && wizardState.decisionPoints.length > 0) ||
      wizardState.generatedDoc ||
      (wizardState.decisionProcessingResults && wizardState.decisionProcessingResults.length > 0);

    const shouldPersist = dbSessionId && 
      (wizardState.currentStep !== 'initializing' || hasGeneratedContent) &&
      (wizardState.taskDetails.title || wizardState.taskDetails.description || hasGeneratedContent);

    if (shouldPersist) {
      persistState(wizardState);
    }
  }, [wizardState, persistState, dbSessionId]);

  // Access control
  useEffect(() => {
    if (session?.user) {
      const allowedEmails = process.env.NEXT_PUBLIC_ALLOWED_EMAILS?.split(',').map(email => email.trim().toLowerCase()) || [];
      const allowedGithubIds = process.env.NEXT_PUBLIC_ALLOWED_GITHUB_IDS?.split(',').map(id => id.trim()) || [];
      const userEmail = session.user.email?.toLowerCase() || '';
      const githubId = session.user.user_metadata?.user_name || '';
      
      const isEmailAllowed = allowedEmails.length === 0 || allowedEmails.includes(userEmail);
      const isGithubIdAllowed = allowedGithubIds.length === 0 || allowedGithubIds.includes(githubId);
      
      setIsAuthorized(isEmailAllowed || isGithubIdAllowed);
    } else {
      setIsAuthorized(false);
    }
  }, [session]);

  const fetchConstitution = useCallback(async (repo: string) => {
    try {
      const response = await fetch(`/api/constitution?repositorySlug=${repo}`);
      if (response.ok) {
        const data = await response.json();
        const constitutionData = data.constitution;
        const wasInitializing = wizardState.currentStep === 'initializing';
        
        if (wasInitializing) {
          // Check deployment constitution status first, then set the appropriate step
          setWizardState(prev => ({ ...prev, isCheckingDeploymentConstitution: true, deploymentConstitutionError: undefined }));
          
          try {
            const effectiveInstallationId = isPublic ? '0' : (installationId || '0');
            const deploymentResponse = await fetch(`/api/repository-status?repositorySlug=${encodeURIComponent(repo)}&installationId=${effectiveInstallationId}`);
            
            if (deploymentResponse.ok) {
              const deploymentData = await deploymentResponse.json();
              setWizardState(prev => ({
                ...prev,
                projectConstitution: constitutionData,
                hasDeploymentConstitution: deploymentData.hasDeploymentConstitution,
                isCheckingDeploymentConstitution: false,
                // If deployment constitution exists, skip directly to task-definition
                // Only show deployment-constitution-check step if it doesn't exist
                currentStep: deploymentData.hasDeploymentConstitution ? 'task-definition' : 'deployment-constitution-check'
              }));
            } else {
              throw new Error('Failed to check deployment constitution status');
            }
          } catch (deploymentError: any) {
            setWizardState(prev => ({
              ...prev,
              projectConstitution: constitutionData,
              deploymentConstitutionError: deploymentError.message,
              isCheckingDeploymentConstitution: false,
              hasDeploymentConstitution: false,
              currentStep: 'deployment-constitution-check'
            }));
          }
        } else {
          // Not initializing, just update the constitution
          setWizardState(prev => ({
            ...prev,
            projectConstitution: constitutionData
          }));
        }
      } else if (response.status === 404) {
        // No constitution exists, stay on the constitution step
        setWizardState(prev => ({ ...prev, currentStep: prev.currentStep === 'initializing' ? 'constitution' : prev.currentStep }));
      } else {
        throw new Error('Failed to fetch project constitution');
      }
    } catch (error: any) {
      setWizardState(prev => ({ ...prev, error: error.message }));
    }
  }, [setWizardState, wizardState.currentStep, installationId, isPublic]);

  const checkDeploymentConstitution = useCallback(async (repo: string) => {
    setWizardState(prev => ({ ...prev, isCheckingDeploymentConstitution: true, deploymentConstitutionError: undefined }));
    
    try {
      // Ensure installationId is always a valid value
      const effectiveInstallationId = isPublic ? '0' : (installationId || '0');
      const response = await fetch(`/api/repository-status?repositorySlug=${encodeURIComponent(repo)}&installationId=${effectiveInstallationId}`);
      if (response.ok) {
        const data = await response.json();
        setWizardState(prev => ({
          ...prev,
          hasDeploymentConstitution: data.hasDeploymentConstitution,
          isCheckingDeploymentConstitution: false,
          // If deployment constitution exists, skip directly to task-definition
          // Only show deployment-constitution-check step if it doesn't exist
          currentStep: data.hasDeploymentConstitution ? 'task-definition' : 'deployment-constitution-check'
        }));
      } else {
        throw new Error('Failed to check deployment constitution status');
      }
    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        deploymentConstitutionError: error.message,
        isCheckingDeploymentConstitution: false,
        hasDeploymentConstitution: false
      }));
    }
  }, [installationId, isPublic, setWizardState]);

  const generateDeploymentConstitution = useCallback(async () => {
    if (!repositorySlug) return;
    
    setWizardState(prev => ({ ...prev, isGeneratingDeploymentConstitution: true, deploymentConstitutionError: undefined }));
    
    try {
      // Ensure installationId is always a valid value
      const effectiveInstallationId = isPublic ? '0' : (installationId || '0');
      const response = await fetch('/api/repository/generate-deployment-constitution', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repositorySlug,
          installationId: effectiveInstallationId,
          isPublic
        })
      });

      if (response.ok) {
        setWizardState(prev => ({
          ...prev,
          hasDeploymentConstitution: true,
          isGeneratingDeploymentConstitution: false,
          currentStep: 'task-definition'
        }));
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate deployment constitution');
      }
    } catch (error: any) {
      setWizardState(prev => ({
        ...prev,
        deploymentConstitutionError: error.message,
        isGeneratingDeploymentConstitution: false
      }));
    }
  }, [repositorySlug, installationId, isPublic, setWizardState]);

  const skipDeploymentConstitution = useCallback(() => {
    setWizardState(prev => ({ ...prev, currentStep: 'task-definition' }));
  }, [setWizardState]);

  const continueFromDeploymentConstitution = useCallback(() => {
    setWizardState(prev => ({ ...prev, currentStep: 'task-definition' }));
  }, [setWizardState]);

  // Navigation guard functions to validate prerequisites
  const canNavigateToUserJourneyDefinition = () => {
    return wizardState.taskDetails.title && wizardState.taskDetails.description;
  };

  const canNavigateToTaskVerification = () => {
    return wizardState.userJourneys && wizardState.userJourneys.length > 0;
  };

  const canNavigateToStrategicAssessment = () => {
    return wizardState.taskAnalysis !== undefined;
  };

  const canNavigateToDecisionDiscovery = () => {
    return wizardState.strategicAssessment !== undefined;
  };

  const canNavigateToDecisionMaking = () => {
    return wizardState.decisionPoints && wizardState.decisionPoints.length > 0;
  };

  const canNavigateToReviewGeneration = () => {
    return wizardState.decisionPoints && 
           wizardState.decisionPoints.length > 0 &&
           wizardState.decisionPoints.some(d => d.selectedOption);
  };

  // Enhanced navigation functions that respect prerequisites
  const navigateToStepIfValid = (targetStep: string) => {
    switch (targetStep) {
      case 'user-journey-definition':
        if (canNavigateToUserJourneyDefinition()) {
          setWizardState(prev => ({ ...prev, currentStep: 'user-journey-definition' }));
        } else {
          console.warn('Cannot navigate to user journey definition: missing task details');
        }
        break;
      case 'task-verification':
        if (canNavigateToTaskVerification()) {
          setWizardState(prev => ({ ...prev, currentStep: 'task-verification' }));
        } else {
          console.warn('Cannot navigate to task verification: missing user journeys');
        }
        break;
      case 'strategic-assessment':
        if (canNavigateToStrategicAssessment()) {
          setWizardState(prev => ({ ...prev, currentStep: 'strategic-assessment' }));
        } else {
          console.warn('Cannot navigate to strategic assessment: missing task analysis');
        }
        break;
      case 'decision-discovery':
        if (canNavigateToDecisionDiscovery()) {
          setWizardState(prev => ({ ...prev, currentStep: 'decision-discovery' }));
        } else {
          console.warn('Cannot navigate to decision discovery: missing strategic assessment');
        }
        break;
      case 'decision-making':
        if (canNavigateToDecisionMaking()) {
          setWizardState(prev => ({ ...prev, currentStep: 'decision-making' }));
        } else {
          console.warn('Cannot navigate to decision making: missing decision points');
        }
        break;
      case 'review-generation':
        if (canNavigateToReviewGeneration()) {
          setWizardState(prev => ({ ...prev, currentStep: 'review-generation' }));
        } else {
          console.warn('Cannot navigate to review generation: missing selected decisions');
        }
        break;
      default:
        // For basic steps, just navigate
        setWizardState(prev => ({ ...prev, currentStep: targetStep as any }));
    }
  };

  const saveConstitution = useCallback(async () => {
    if (!repositorySlug || !wizardState.projectConstitution) return;
    setWizardState(prev => ({ ...prev, isSavingConstitution: true }));
    try {
      // Ensure the latest language is included, in case state update is lagging
      const currentLanguage = wizardState.projectConstitution?.primaryLanguage;
      const constitutionToSend = {
        ...wizardState.projectConstitution,
        primaryLanguage: currentLanguage,
      };

      const response = await fetch('/api/constitution', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          repositorySlug,
          constitutionData: constitutionToSend
        })
      });
      if (!response.ok) {
        throw new Error('Failed to save constitution');
      }
      setWizardState(prev => ({ ...prev, currentStep: 'task-definition' }));
    } catch (error: any) {
      setWizardState(prev => ({ ...prev, error: error.message }));
    } finally {
      setWizardState(prev => ({ ...prev, isSavingConstitution: false }));
    }
  }, [repositorySlug, wizardState.projectConstitution]);

  // Handle priority ranking change
  const handlePriorityChange = (index: number, direction: 'up' | 'down') => {
    if (!wizardState.projectConstitution?.priorities) return;

    const priorities = [...wizardState.projectConstitution.priorities];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    if (newIndex < 0 || newIndex >= priorities.length) return;

    // Swap elements
    [priorities[index], priorities[newIndex]] = [priorities[newIndex], priorities[index]];

    setWizardState(prev => ({
        ...prev,
        projectConstitution: {
            ...prev.projectConstitution!,
            priorities,
        }
    }));
  };

  // Extract repository context from URL and fetch constitution
  useEffect(() => {
    if (searchParams) {
      const repo = searchParams.get('repositorySlug') || '';
      const instId = searchParams.get('installationId') || '';
      const publicFlag = searchParams.get('isPublic') === 'true';
      
      setRepositorySlug(repo);
      setInstallationId(instId);
      setIsPublic(publicFlag);

      if (repo) {
        fetchConstitution(repo);
      }
    }
  }, [searchParams, fetchConstitution]);

  // Fetch primary language
  useEffect(() => {
    if (repositorySlug && installationId) {
      const fetchLanguage = async () => {
        try {
          const response = await fetch(`/api/github/languages?repositorySlug=${repositorySlug}&installationId=${installationId}`);
          if (response.ok) {
            const data = await response.json();
            setWizardState(prev => ({
              ...prev,
              projectConstitution: {
                ...prev.projectConstitution!,
                primaryLanguage: data.primaryLanguage,
              }
            }));
          }
        } catch (error) {
          console.warn('Failed to fetch primary language:', error);
        }
      };
      fetchLanguage();
    }
  }, [repositorySlug, installationId]);

  // Fetch or generate API key
  useEffect(() => {
    const manageApiKey = async () => {
      let key = localStorage.getItem('archknow-api-key');
      if (key) {
        setApiKey(key);
        return;
      }

      try {
        const response = await fetch('/api/user/api-key', { method: 'POST' });
        const data = await response.json();
        if (response.ok) {
          const newKey = data.apiKey;
          if (newKey) {
            setApiKey(newKey);
            localStorage.setItem('archknow-api-key', newKey);
          }
        } else {
          throw new Error(data.error || 'Failed to generate API key');
        }
      } catch (error) {
        console.error("Error managing API key:", error);
        setWizardState(prev => ({ ...prev, error: error instanceof Error ? error.message : 'Failed to get API key.' }));
      }
    };

    if (isAuthorized) {
        manageApiKey();
    }
  }, [isAuthorized]);

  // Load existing session if sessionId is provided in URL
  useEffect(() => {
    const sessionIdParam = searchParams?.get('sessionId');
    if (sessionIdParam && sessionIdParam !== wizardState.sessionId) {
      const loadSession = async () => {
        setIsLoadingSessionData(true);
        try {
          const response = await fetch(`/api/design-doc-wizard/session/${sessionIdParam}`);
          if (response.ok) {
            const sessionData = await response.json();
            if (sessionData.wizard_state) {
              // Merge the loaded state but preserve the step from URL if provided
              const stepParam = searchParams?.get('step');
              const loadedState = sessionData.wizard_state;
              
              setWizardState(prev => ({
                ...loadedState,
                sessionId: sessionIdParam,
                currentStep: stepParam || loadedState.currentStep || 'initializing'
              }));
              
              // Set repository context
              if (sessionData.repositories) {
                setRepositorySlug(`${sessionData.repositories.owner}/${sessionData.repositories.name}`);
              }
              
              // Set installation ID from session data
              if (sessionData.installation_id) {
                setInstallationId(String(sessionData.installation_id));
              }
              
              setDbSessionId(sessionIdParam);
            }
          } else {
            console.error('Failed to load session:', response.statusText);
          }
        } catch (error) {
          console.error('Error loading session:', error);
        } finally {
          setIsLoadingSessionData(false);
        }
      };
      
      loadSession();
    }
  }, [searchParams, wizardState.sessionId]);

  return {
    // State
    session,
    isLoadingSession,
    isLoadingSessionData,
    isAuthorized,
    wizardState,
    setWizardState,
    repositorySlug,
    installationId,
    isPublic,
    constitutionSeedText,
    setConstitutionSeedText,
    showIssueSelector,
    setShowIssueSelector,
    issues,
    setIssues,
    isLoadingIssues,
    setIsLoadingIssues,
    githubHandle,
    setGithubHandle,
    apiKey,
    dbSessionId, // Expose the database session ID
    
    // Actions
    router,
    fetchConstitution,
    saveConstitution,
    handlePriorityChange,
    navigateToStepIfValid,
    
    // Deployment Constitution Actions
    checkDeploymentConstitution,
    generateDeploymentConstitution,
    skipDeploymentConstitution,
    continueFromDeploymentConstitution,
    
    // Navigation guards
    canNavigateToUserJourneyDefinition,
    canNavigateToTaskVerification,
    canNavigateToStrategicAssessment,
    canNavigateToDecisionDiscovery,
    canNavigateToDecisionMaking,
    canNavigateToReviewGeneration,
    
    // Supabase instance
    supabase
  };
} 