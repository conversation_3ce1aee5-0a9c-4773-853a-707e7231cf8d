import { useCallback } from 'react';
import type { 
  DecisionPoint, 
  TaskDetails, 
  TaskAnalysis, 
  DecisionOption,
  WizardState,
  DecisionRemovalReason,
  RemovedDecisionContext,
  ProjectConstraint
} from '../types/design-doc-wizard';

interface UseWizardEditingProps {
  wizardState: WizardState;
  setWizardState: React.Dispatch<React.SetStateAction<WizardState>>;
}

export function useWizardEditing({ wizardState, setWizardState }: UseWizardEditingProps) {

  // Decision editing functions
  const addNewDecision = useCallback(() => {
    const newDecision: DecisionPoint = {
      id: `custom_${Date.now()}`,
      title: 'New Technical Decision',
      description: 'Describe the technical decision that needs to be made...',
      options: [],
      relatedDecisions: [],
      searchQueries: []
    };
    
    setWizardState(prev => ({
      ...prev,
      decisionPoints: [...prev.decisionPoints, newDecision]
    }));
  }, [setWizardState]);

  const updateDecision = useCallback((decisionId: string, updates: Partial<DecisionPoint>) => {
    setWizardState(prev => ({
      ...prev,
      decisionPoints: prev.decisionPoints.map(dp =>
        dp.id === decisionId ? { ...dp, ...updates } : dp
      )
    }));
  }, [setWizardState]);

  const removeDecision = useCallback((decisionId: string) => {
    setWizardState(prev => ({
      ...prev,
      decisionPoints: prev.decisionPoints.filter(dp => dp.id !== decisionId)
    }));
  }, [setWizardState]);

  // Enhanced decision removal functions
  const showRemovalModal = useCallback((decisionId: string) => {
    setWizardState(prev => ({
      ...prev,
      showRemovalModal: true,
      removalModalDecisionId: decisionId
    }));
  }, [setWizardState]);

  const hideRemovalModal = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      showRemovalModal: false,
      removalModalDecisionId: undefined
    }));
  }, [setWizardState]);

  const removeDecisionWithContext = useCallback((
    decisionId: string, 
    reason: DecisionRemovalReason, 
    contextNote: string
  ) => {
    const decision = wizardState.decisionPoints.find(dp => dp.id === decisionId);
    if (!decision) return;

    // Create removal context record
    const removalContext: RemovedDecisionContext = {
      decisionId,
      decisionTitle: decision.title,
      decisionDescription: decision.description,
      removalReason: reason,
      contextNote,
      capturedAs: 'note', // Default, will be updated based on reason
      removedAt: new Date().toISOString()
    };

    setWizardState(prev => {
      let updatedState = { ...prev };

      // Route the context based on the removal reason
      switch (reason) {
        case 'already-decided':
          // Add as a project constraint
          const constraint: ProjectConstraint = {
            id: `constraint_${Date.now()}`,
            title: `Decision Made: ${decision.title}`,
            description: contextNote || `We have already decided on ${decision.title.toLowerCase()}: ${decision.description}`,
            source: 'removed-decision',
            createdAt: new Date().toISOString()
          };
          updatedState.projectConstraints = [...(prev.projectConstraints || []), constraint];
          removalContext.capturedAs = 'constraint';
          break;

        case 'out-of-scope':
          // Add as a non-goal
          const nonGoalText = contextNote || `${decision.title} is out of scope for this task`;
          updatedState.nonGoals = [...(prev.nonGoals || []), nonGoalText];
          removalContext.capturedAs = 'non-goal';
          break;

        case 'duplicate':
          // Add as an assumption/note that this is handled elsewhere
          const assumptionConstraint: ProjectConstraint = {
            id: `assumption_${Date.now()}`,
            title: `Duplicate Decision: ${decision.title}`,
            description: contextNote || `${decision.title} is already covered by another decision point`,
            source: 'removed-decision',
            createdAt: new Date().toISOString()
          };
          updatedState.projectConstraints = [...(prev.projectConstraints || []), assumptionConstraint];
          removalContext.capturedAs = 'assumption';
          break;

        case 'not-relevant':
        case 'custom':
        default:
          // Keep as a note in removal context only
          removalContext.capturedAs = 'note';
          break;
      }

      // Remove the decision from the list
      updatedState.decisionPoints = prev.decisionPoints.filter(dp => dp.id !== decisionId);
      
      // Add to removed decisions history
      updatedState.removedDecisions = [...(prev.removedDecisions || []), removalContext];
      
      // Hide the modal
      updatedState.showRemovalModal = false;
      updatedState.removalModalDecisionId = undefined;

      return updatedState;
    });
  }, [wizardState.decisionPoints, setWizardState]);

  const restoreRemovedDecision = useCallback((removalContextId: string) => {
    const removalContext = wizardState.removedDecisions?.find(
      rc => rc.decisionId === removalContextId
    );
    if (!removalContext) return;

    // Recreate the decision point
    const restoredDecision: DecisionPoint = {
      id: removalContext.decisionId,
      title: removalContext.decisionTitle,
      description: removalContext.decisionDescription,
      options: [],
      relatedDecisions: [],
      searchQueries: []
    };

    setWizardState(prev => ({
      ...prev,
      decisionPoints: [...prev.decisionPoints, restoredDecision],
      removedDecisions: (prev.removedDecisions || []).filter(
        rc => rc.decisionId !== removalContextId
      )
      // Note: We don't automatically remove from constraints/non-goals 
      // as user might want to keep those separate
    }));
  }, [wizardState.removedDecisions, setWizardState]);

  // Project constraints management
  const addProjectConstraint = useCallback((title: string, description: string) => {
    const constraint: ProjectConstraint = {
      id: `constraint_${Date.now()}`,
      title,
      description,
      source: 'user',
      createdAt: new Date().toISOString()
    };
    
    setWizardState(prev => ({
      ...prev,
      projectConstraints: [...(prev.projectConstraints || []), constraint]
    }));
  }, [setWizardState]);

  const removeProjectConstraint = useCallback((constraintId: string) => {
    setWizardState(prev => ({
      ...prev,
      projectConstraints: (prev.projectConstraints || []).filter(c => c.id !== constraintId)
    }));
  }, [setWizardState]);

  const updateProjectConstraint = useCallback((constraintId: string, updates: Partial<ProjectConstraint>) => {
    setWizardState(prev => ({
      ...prev,
      projectConstraints: (prev.projectConstraints || []).map(c =>
        c.id === constraintId ? { ...c, ...updates } : c
      )
    }));
  }, [setWizardState]);

  const toggleEditMode = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      isEditingDecisions: !prev.isEditingDecisions
    }));
  }, [setWizardState]);

  // Goals and Non-Goals editing functions
  const toggleNonGoalsEditMode = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      isEditingNonGoals: !prev.isEditingNonGoals
    }));
  }, [setWizardState]);

  const updateNonGoals = useCallback((newNonGoals: string[]) => {
    setWizardState(prev => ({
      ...prev,
      nonGoals: newNonGoals
    }));
  }, [setWizardState]);

  const addNonGoal = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      nonGoals: [...(prev.nonGoals || []), 'New non-goal...']
    }));
  }, [setWizardState]);

  const removeNonGoal = useCallback((index: number) => {
    setWizardState(prev => ({
      ...prev,
      nonGoals: (prev.nonGoals || []).filter((_, i) => i !== index)
    }));
  }, [setWizardState]);

  const updateNonGoalAtIndex = useCallback((index: number, newValue: string) => {
    setWizardState(prev => ({
      ...prev,
      nonGoals: (prev.nonGoals || []).map((nonGoal, i) => i === index ? newValue : nonGoal)
    }));
  }, [setWizardState]);

  // Editing functions for the new requirements review step
  const toggleRequirementsEditMode = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      isEditingRequirements: !prev.isEditingRequirements
    }));
  }, [setWizardState]);

  // Goals functions (for backward compatibility with old UI sections)
  const addGoal = useCallback(() => {
    // This function is referenced by old UI but goals are no longer used
    console.warn('addGoal called but goals are deprecated. Use functional requirements instead.');
  }, []);

  const removeGoal = useCallback((index: number) => {
    // This function is referenced by old UI but goals are no longer used
    console.warn('removeGoal called but goals are deprecated. Use functional requirements instead.');
  }, []);

  const updateGoalAtIndex = useCallback((index: number, newValue: string) => {
    // This function is referenced by old UI but goals are no longer used
    console.warn('updateGoalAtIndex called but goals are deprecated. Use functional requirements instead.');
  }, []);

  const updateGoals = useCallback((newGoals: string[]) => {
    // This function is referenced by old UI but goals are no longer used
    console.warn('updateGoals called but goals are deprecated. Use functional requirements instead.');
  }, []);

  // Task Details editing functions
  const toggleTaskEditMode = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      isEditingTaskDetails: !prev.isEditingTaskDetails
    }));
  }, [setWizardState]);

  const updateTaskDetails = useCallback((updates: Partial<TaskDetails>) => {
    setWizardState(prev => ({
      ...prev,
      taskDetails: { ...prev.taskDetails, ...updates }
    }));
  }, [setWizardState]);

  // Decision Options editing functions
  const toggleOptionsEditMode = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      isEditingOptions: !prev.isEditingOptions
    }));
  }, [setWizardState]);

  const addDecisionOption = useCallback((decisionId: string) => {
    const newOption: DecisionOption = {
      id: `option_${Date.now()}`,
      name: 'New Option',
      description: 'Describe this implementation approach...',
      pros: ['Add advantages here...'],
      cons: ['Add disadvantages here...'],
      riskLevel: 'medium',
      complexity: 'medium',
      maintenanceBurden: 'medium',
      debuggingComplexity: 'Standard debugging approaches apply',
      architecturalAlignment: 'medium',
      alignmentJustification: 'Alignment with existing patterns needs evaluation',
      changeScope: 'moderate'
    };
    
    setWizardState(prev => ({
      ...prev,
      decisionPoints: prev.decisionPoints.map(dp =>
        dp.id === decisionId 
          ? { ...dp, options: [...(dp.options || []), newOption] }
          : dp
      )
    }));
  }, [setWizardState]);

  const updateDecisionOption = useCallback((decisionId: string, optionId: string, updates: Partial<DecisionOption>) => {
    setWizardState(prev => ({
      ...prev,
      decisionPoints: prev.decisionPoints.map(dp =>
        dp.id === decisionId 
          ? {
              ...dp,
              options: (dp.options || []).map(opt =>
                opt.id === optionId ? { ...opt, ...updates } : opt
              )
            }
          : dp
      )
    }));
  }, [setWizardState]);

  const removeDecisionOption = useCallback((decisionId: string, optionId: string) => {
    setWizardState(prev => ({
      ...prev,
      decisionPoints: prev.decisionPoints.map(dp =>
        dp.id === decisionId 
          ? {
              ...dp,
              options: (dp.options || []).filter(opt => opt.id !== optionId),
              selectedOption: dp.selectedOption === optionId ? undefined : dp.selectedOption
            }
          : dp
      )
    }));
  }, [setWizardState]);

  const addProToCon = useCallback((decisionId: string, optionId: string, type: 'pros' | 'cons') => {
    setWizardState(prev => ({
      ...prev,
      decisionPoints: prev.decisionPoints.map(dp =>
        dp.id === decisionId 
          ? {
              ...dp,
              options: (dp.options || []).map(opt =>
                opt.id === optionId 
                  ? { ...opt, [type]: [...opt[type], `New ${type.slice(0, -1)}...`] }
                  : opt
              )
            }
          : dp
      )
    }));
  }, [setWizardState]);

  const updateProCon = useCallback((decisionId: string, optionId: string, type: 'pros' | 'cons', index: number, value: string) => {
        setWizardState(prev => ({
          ...prev,
      decisionPoints: prev.decisionPoints.map(dp =>
        dp.id === decisionId 
          ? {
              ...dp,
              options: (dp.options || []).map(opt =>
                opt.id === optionId 
                  ? { 
                      ...opt, 
                      [type]: opt[type].map((item, i) => i === index ? value : item)
                    }
                  : opt
              )
            }
          : dp
      )
    }));
  }, [setWizardState]);

  const removeProCon = useCallback((decisionId: string, optionId: string, type: 'pros' | 'cons', index: number) => {
    setWizardState(prev => ({
      ...prev,
      decisionPoints: prev.decisionPoints.map(dp =>
        dp.id === decisionId 
          ? {
              ...dp,
              options: (dp.options || []).map(opt =>
                opt.id === optionId 
                  ? { 
                      ...opt, 
                      [type]: opt[type].filter((_, i) => i !== index)
                    }
                  : opt
              )
            }
          : dp
      )
    }));
  }, [setWizardState]);

  // Task Analysis editing functions
  const updateTaskAnalysis = useCallback((updates: Partial<TaskAnalysis>) => {
      setWizardState(prev => ({
        ...prev,
      taskAnalysis: prev.taskAnalysis ? { ...prev.taskAnalysis, ...updates } : updates as TaskAnalysis
      }));
  }, [setWizardState]);

  const addFunctionalRequirement = useCallback(() => {
      setWizardState(prev => ({
        ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        functional_requirements: [...prev.taskAnalysis.functional_requirements, 'New functional requirement...']
      } : undefined
    }));
  }, [setWizardState]);

  const updateFunctionalRequirement = useCallback((index: number, value: string) => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        functional_requirements: prev.taskAnalysis.functional_requirements.map((req, i) => i === index ? value : req)
      } : undefined
    }));
  }, [setWizardState]);

  const removeFunctionalRequirement = useCallback((index: number) => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        functional_requirements: prev.taskAnalysis.functional_requirements.filter((_, i) => i !== index)
      } : undefined
    }));
  }, [setWizardState]);

  const updateFunctionalRequirementsDiagram = useCallback((value: string) => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        functional_requirements_diagram: value
      } : undefined
    }));
  }, [setWizardState]);

  const addNFR = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        implied_NFRs: [...prev.taskAnalysis.implied_NFRs, { nfr: 'New NFR...', evidence: 'Evidence...' }]
      } : undefined
    }));
  }, [setWizardState]);

  const updateNFR = useCallback((index: number, field: 'nfr' | 'evidence', value: string) => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        implied_NFRs: prev.taskAnalysis.implied_NFRs.map((nfr, i) => 
          i === index ? { ...nfr, [field]: value } : nfr
        )
      } : undefined
    }));
  }, [setWizardState]);

  const removeNFR = useCallback((index: number) => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        implied_NFRs: prev.taskAnalysis.implied_NFRs.filter((_, i) => i !== index)
      } : undefined
    }));
  }, [setWizardState]);

  const addArchCharacteristic = useCallback(() => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        core_architectural_characteristics: [...prev.taskAnalysis.core_architectural_characteristics, 'New architectural characteristic...']
      } : undefined
    }));
  }, [setWizardState]);

  const updateArchCharacteristic = useCallback((index: number, value: string) => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        core_architectural_characteristics: prev.taskAnalysis.core_architectural_characteristics.map((char, i) => i === index ? value : char)
      } : undefined
    }));
  }, [setWizardState]);

  const removeArchCharacteristic = useCallback((index: number) => {
    setWizardState(prev => ({
      ...prev,
      taskAnalysis: prev.taskAnalysis ? {
        ...prev.taskAnalysis,
        core_architectural_characteristics: prev.taskAnalysis.core_architectural_characteristics.filter((_, i) => i !== index)
      } : undefined
    }));
  }, [setWizardState]);

  return {
    // Decision editing
    addNewDecision,
    updateDecision,
    removeDecision,
    toggleEditMode,
    
    // Enhanced decision removal
    showRemovalModal,
    hideRemovalModal,
    removeDecisionWithContext,
    restoreRemovedDecision,
    
    // Project constraints
    addProjectConstraint,
    removeProjectConstraint,
    updateProjectConstraint,
    
    // Non-goals editing
    toggleNonGoalsEditMode,
    updateNonGoals,
    addNonGoal,
    removeNonGoal,
    updateNonGoalAtIndex,
    
    // Requirements editing
    toggleRequirementsEditMode,
    
    // Legacy goals functions
    addGoal,
    removeGoal,
    updateGoalAtIndex,
    updateGoals,
    
    // Task editing
    toggleTaskEditMode,
    updateTaskDetails,
    
    // Options editing
    toggleOptionsEditMode,
    addDecisionOption,
    updateDecisionOption,
    removeDecisionOption,
    addProToCon,
    updateProCon,
    removeProCon,
    
    // Task analysis editing
    updateTaskAnalysis,
    addFunctionalRequirement,
    updateFunctionalRequirement,
    removeFunctionalRequirement,
    updateFunctionalRequirementsDiagram,
    addNFR,
    updateNFR,
    removeNFR,
    addArchCharacteristic,
    updateArchCharacteristic,
    removeArchCharacteristic,
  };
} 