import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { createBrowserClient } from '@supabase/ssr';
import type { Session } from '@supabase/supabase-js';
import type { RCAWizardState } from '../types/design-doc-wizard';

export function useRCAWizard() {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const [githubHandle, setGithubHandle] = useState<string>('');
  const [apiKey, setApiKey] = useState<string>('');
  const [dbSessionId, setDbSessionId] = useState<string | null>(null);

  const [wizardState, setWizardState] = useState<RCAWizardState>({
    sessionId: searchParams?.get('sessionId') || '',
    currentStep: 'symptom-analysis',
    symptoms: {
      errorType: '',
      affectedComponents: [],
      userImpact: '',
      timePattern: '',
      environment: ''
    },
    isLoading: false,
    repositorySlug: searchParams?.get('repositorySlug') || '',
    installationId: searchParams?.get('installationId') || '',
    isPublic: searchParams?.get('isPublic') === 'true',
    issues: [],
    selectedIssue: null
  });

  const [repositorySlug, setRepositorySlug] = useState<string>('');

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const createSession = useCallback(async (repo: string, owner: string, installationId: string) => {
    try {
      const response = await fetch('/api/rca-wizard/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repository_slug: `${owner}/${repo}`,
          installation_id: installationId,
        }),
      });
      if (response.ok) {
        const data = await response.json();
        setDbSessionId(data.sessionId);
        router.push(`/rca-wizard?sessionId=${data.sessionId}&repo=${repo}&owner=${owner}&installationId=${installationId}`);
        return data.sessionId;
      }
    } catch (error) {
      console.error('Failed to create RCA session:', error);
    }
  }, [router]);

  const persistState = useCallback(async (state: RCAWizardState) => {
    if (!dbSessionId) return;
    try {
      await fetch(`/api/rca-wizard/session/${dbSessionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ wizardState: state }),
      });
    } catch (error) {
      console.warn('Failed to persist RCA state:', error);
    }
  }, [dbSessionId]);

  const loadSession = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/rca-wizard/session/${sessionId}`);
      if (response.ok) {
        const sessionData = await response.json();
        setWizardState(sessionData.wizard_state);
        setRepositorySlug(sessionData.repository_slug);
        setDbSessionId(sessionData.id);
        return sessionData;
      }
    } catch (error) {
      console.warn('Failed to load RCA session:', error);
    }
    return null;
  }, []);

  useEffect(() => {
    async function initializeSession() {
      setIsLoadingSession(true);
      
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setSession(session);

        // Extract repository context from URL search params - same as Feature Wizard
        const repoSlugFromUrl = searchParams?.get('repositorySlug');
        const installationIdParam = searchParams?.get('installationId');
        const isPublicParam = searchParams?.get('isPublic') === 'true';
        const sessionId = searchParams?.get('sessionId');

        if (repoSlugFromUrl) {
          setRepositorySlug(repoSlugFromUrl);
          setWizardState(prev => ({ 
            ...prev, 
            repositorySlug: repoSlugFromUrl, 
            installationId: installationIdParam || undefined, 
            isPublic: isPublicParam 
          }));

          if (sessionId) {
            await loadSession(sessionId);
          } else if (session) {
            // Split repositorySlug to get owner and repo for createSession
            const [owner, repo] = repoSlugFromUrl.split('/');
            if (owner && repo) {
              await createSession(repo, owner, installationIdParam || '');
            }
          }
        }
      } catch (error) {
        console.error('Failed to initialize RCA session:', error);
      } finally {
        setIsLoadingSession(false);
      }
    }

    initializeSession();
  }, [searchParams, supabase.auth, createSession, loadSession]);

  useEffect(() => {
    if (dbSessionId && wizardState.sessionId) {
      persistState(wizardState);
    }
  }, [wizardState, persistState, dbSessionId]);

  useEffect(() => {
    if (session) {
      const userGithubHandle = session.user.user_metadata?.user_name;
      if (userGithubHandle === 'jabubaker') {
        setIsAuthorized(true);
      }
      setGithubHandle(userGithubHandle);
    }
  }, [session]);

  // Fetch or generate API key - same as Feature Wizard
  useEffect(() => {
    const manageApiKey = async () => {
      let key = localStorage.getItem('archknow-api-key');
      if (key) {
        setApiKey(key);
        return;
      }

      try {
        const response = await fetch('/api/user/api-key', { method: 'POST' });
        const data = await response.json();
        if (response.ok) {
          const newKey = data.apiKey;
          if (newKey) {
            setApiKey(newKey);
            localStorage.setItem('archknow-api-key', newKey);
          }
        } else {
          throw new Error(data.error || 'Failed to generate API key');
        }
      } catch (error) {
        console.error("Error managing API key:", error);
        setWizardState(prev => ({ ...prev, error: error instanceof Error ? error.message : 'Failed to get API key.' }));
      }
    };

    if (isAuthorized) {
      manageApiKey();
    }
  }, [isAuthorized]);

  return {
    session,
    isLoadingSession,
    isAuthorized,
    wizardState,
    setWizardState,
    repositorySlug,
    githubHandle,
    apiKey,
    supabase
  };
} 