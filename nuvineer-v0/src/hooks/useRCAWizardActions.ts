import { useState, useCallback } from 'react';
import type { RCAWizardState } from '../types/design-doc-wizard';

interface UseRCAWizardActionsProps {
  wizardState: RCAWizardState;
  setWizardState: React.Dispatch<React.SetStateAction<RCAWizardState>>;
  repositorySlug: string;
  githubHandle: string;
  apiKey: string;
  installationId?: string;
  isPublic?: boolean;
}

export function useRCAWizardActions({
  wizardState,
  setWizardState,
  repositorySlug,
  githubHandle,
  apiKey,
  installationId,
  isPublic,
}: UseRCAWizardActionsProps) {
  const [issues, setIssues] = useState<any[]>([]);
  const [isLoadingIssues, setIsLoadingIssues] = useState(false);
  const [showIssueSelector, setShowIssueSelector] = useState(false);

  // Fetch GitHub issues - same as Feature Wizard
  const fetchIssues = useCallback(async () => {
    if (!repositorySlug || !apiKey || !githubHandle) {
      console.warn('Cannot fetch issues: missing repositorySlug, apiKey, or githubHandle.');
      return;
    }
    
    setIsLoadingIssues(true);
    try {
      const response = await fetch('/api/github/issues', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          repository_slug: repositorySlug,
          github_handle: githubHandle,
          api_key: apiKey,
        }),
      });
      if (response.ok) {
        const data = await response.json();
        setIssues(data || []);
      } else {
        console.error('Failed to fetch issues:', response.statusText);
        setIssues([]);
      }
    } catch (error) {
      console.error('Error fetching issues:', error);
      setIssues([]);
    } finally {
      setIsLoadingIssues(false);
    }
  }, [repositorySlug, githubHandle, apiKey]);

  // Handle import from issue - calls fetchIssues like in Feature Wizard
  const handleImportFromIssue = useCallback(async () => {
    await fetchIssues();
    setShowIssueSelector(true);
  }, [fetchIssues]);

  const handleSelectIssue = useCallback(async (issue: any) => {
    console.log('[RCA] Selected issue:', issue);
    setShowIssueSelector(false);
    setWizardState(prevState => ({ ...prevState, selectedIssue: issue }));

    try {
      console.log('[RCA] Calling import-issue API...');
      const response = await fetch('/api/rca-wizard/import-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          issueUrl: issue.html_url,
          apiKey: apiKey,
          repositorySlug: repositorySlug,
          githubHandle: githubHandle
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[RCA] Import-issue API response:', data);
        console.log('[RCA] Type of data:', typeof data);
        console.log('[RCA] Data keys:', Object.keys(data));
        console.log('[RCA] data.symptoms:', data.symptoms);
        console.log('[RCA] Type of data.symptoms:', typeof data.symptoms);
        
        setWizardState(prevState => {
          const symptoms = data.symptoms || {};
          console.log('[RCA] Extracted symptoms object:', symptoms);
          
          const newState = {
            ...prevState,
            symptoms: {
              errorType: symptoms.errorType || prevState.symptoms.errorType,
              affectedComponents: symptoms.affectedComponents || prevState.symptoms.affectedComponents,
              userImpact: symptoms.userImpact || prevState.symptoms.userImpact,
              timePattern: symptoms.timePattern || prevState.symptoms.timePattern,
              environment: symptoms.environment || prevState.symptoms.environment,
            }
          };
          console.log('[RCA] Previous state symptoms:', prevState.symptoms);
          console.log('[RCA] New state symptoms:', newState.symptoms);
          console.log('[RCA] Individual field mapping:');
          console.log('  errorType:', symptoms.errorType, '->', newState.symptoms.errorType);
          console.log('  affectedComponents:', symptoms.affectedComponents, '->', newState.symptoms.affectedComponents);
          console.log('  userImpact:', symptoms.userImpact, '->', newState.symptoms.userImpact);
          console.log('  timePattern:', symptoms.timePattern, '->', newState.symptoms.timePattern);
          console.log('  environment:', symptoms.environment, '->', newState.symptoms.environment);
          return newState;
        });
        console.log('[RCA] Symptoms updated successfully');
      } else {
        console.error("Failed to process issue for RCA:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error processing issue:", error);
    }
  }, [apiKey, repositorySlug, githubHandle, setWizardState]);

  return {
    issues,
    isLoadingIssues,
    showIssueSelector,
    setShowIssueSelector,
    handleImportFromIssue,
    handleSelectIssue,
    fetchIssues,
    installationId,
    isPublic,
  };
} 