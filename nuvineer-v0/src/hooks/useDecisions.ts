import { useState, useEffect, useMemo } from 'react';
import { DecisionReference } from '../utils/decisionUtils';
import { fetchDecisionsByIds, extractDecisionIdsFromTexts } from '../services/decisionService';

interface UseDecisionsParams {
  repositorySlug?: string;
  installationId?: number;
  texts?: (string | string[])[];
}

interface UseDecisionsReturn {
  decisions: Record<string, DecisionReference>;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook to fetch decisions based on decision ID references found in text
 */
export function useDecisions({
  repositorySlug,
  installationId,
  texts = []
}: UseDecisionsParams): UseDecisionsReturn {
  const [decisions, setDecisions] = useState<Record<string, DecisionReference>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Extract decision IDs from all provided texts
  const decisionIds = useMemo(() => {
    return extractDecisionIdsFromTexts(texts);
  }, [texts]);

  const fetchDecisions = async () => {
    // Don't fetch if we don't have the required parameters or no decision IDs
    if (!repositorySlug || installationId === undefined || decisionIds.length === 0) {
      setDecisions({});
      setIsLoading(false);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const fetchedDecisions = await fetchDecisionsByIds({
        ids: decisionIds,
        repositorySlug,
        installationId
      });

      setDecisions(fetchedDecisions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch decisions';
      setError(errorMessage);
      console.error('Error fetching decisions:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDecisions();
  }, [repositorySlug, installationId, decisionIds.join(',')]);

  return {
    decisions,
    isLoading,
    error,
    refetch: fetchDecisions,
  };
}

/**
 * Hook to get project context with debugging
 */
export function useProjectContextWithDebug(repositorySlug?: string, installationId?: string | number) {
  console.log('[useProjectContextWithDebug] Received params:', { repositorySlug, installationId });
  
  const finalRepositorySlug = repositorySlug || process.env.NEXT_PUBLIC_DEFAULT_REPO_SLUG;
  const finalInstallationId = typeof installationId === 'string' 
    ? parseInt(installationId, 10) 
    : installationId || parseInt(process.env.NEXT_PUBLIC_DEFAULT_INSTALLATION_ID || '0', 10);

  console.log('[useProjectContextWithDebug] Final params:', { 
    finalRepositorySlug, 
    finalInstallationId,
    type: typeof finalInstallationId 
  });

  return {
    repositorySlug: finalRepositorySlug,
    installationId: finalInstallationId
  };
} 