import { Octokit } from '@octokit/rest';
import { getOctokit } from '@/lib/github-auth';
import { createSupabaseAdminClient } from '@/lib/supabase-server'; // Use correct path alias

// Define the return structure for clarity
export interface RepositoryAccessDetails {
    type: 'public' | 'private' | 'forbidden' | 'not_found' | 'error';
    namespace?: string; // e.g., '0-owner--repo' or '12345-owner--repo'
    installationId?: number;
    message?: string; // For errors
}

// Helper function to get Octokit instance for checking public repo status
// Uses GITHUB_TOKEN for potentially higher rate limits than unauthenticated
function getPublicCheckOctokit(): Octokit {
    // Consider caching this instance if called frequently
    return new Octokit({ auth: process.env.GITHUB_TOKEN });
}

/**
 * Determines if a user has access to a repository (publicly or via a private installation)
 * and returns the appropriate Pinecone namespace.
 * 
 * @param userId The ID of the user making the request (from API key validation).
 * @param repositorySlug The repository identifier, e.g., "owner/repo".
 * @returns RepositoryAccessDetails object indicating access type and namespace.
 */
export async function getRepositoryAccessDetails(userId: string, repositorySlug: string): Promise<RepositoryAccessDetails> {
    const [owner, repo] = repositorySlug.split('/');
    if (!owner || !repo) {
        return { type: 'error', message: 'Invalid repository slug format. Expected \'owner/repo\'.' };
    }

    const pineconeSlug = repositorySlug.replace('/', '--');
    const supabase = createSupabaseAdminClient(); // Use admin client for direct table access

    // 1. Check if the repository is Public
    try {
        const publicOctokit = getPublicCheckOctokit();
        console.log(`[RepoAccess] Checking public status for ${repositorySlug}...`);
        const { data: repoData } = await publicOctokit.rest.repos.get({ owner, repo });
        
        if (!repoData.private) {
            console.log(`[RepoAccess] Repository ${repositorySlug} confirmed public.`);
            return { type: 'public', namespace: `0-${pineconeSlug}` };
        } else {
            // Repository exists but is private, proceed to installation check
            console.log(`[RepoAccess] Repository ${repositorySlug} is private. Checking user installations...`);
        }
    } catch (error: any) {
        if (error.status === 404) {
            // Not found publicly, could be private or non-existent. Proceed to installation check.
            console.log(`[RepoAccess] Repository ${repositorySlug} not found publicly. Checking user installations...`);
        } else {
            // Other errors during public check (rate limit, auth, etc.)
            console.error(`[RepoAccess] Error checking public status for ${repositorySlug}: ${error.status}`, error.message);
            return { type: 'error', message: `GitHub API error checking public status: ${error.message}` };
        }
    }

    // 2. Check User Installations and Access (if not public or public check failed with 404)
    let userInstallations: { installation_id: number }[] = [];
    try {
        const { data, error: dbError } = await supabase
            .from('user_installations') // Your table name
            .select('installation_id')
            .eq('user_id', userId); // The user ID from the validated API key

        if (dbError) {
            console.error(`[RepoAccess] Supabase error fetching installations for user ${userId}:`, dbError);
            throw dbError; // Let the outer catch handle it
        }
        if (!data || data.length === 0) {
            console.log(`[RepoAccess] No installations found for user ${userId}.`);
            // If we got here after a 404 on public check, repo likely doesn't exist or user truly has no access
            // If we got here after confirming private, user lacks app installation
            // Let's return NotFound if public check was 404, otherwise Forbidden.
            // Re-checking the error from the public check isn't straightforward here.
            // Simplification: Return Forbidden if no installations found for the user.
             return { type: 'forbidden' }; 
        }
        // Ensure installation_id is treated as a number
        userInstallations = data.map(item => ({
            installation_id: Number(item.installation_id) 
        })).filter(item => !isNaN(item.installation_id)); // Filter out any NaN results if conversion fails

        if (userInstallations.length === 0 && data.length > 0) {
            // This case means all fetched installation_ids were invalid numbers
            console.warn(`[RepoAccess] Found installation records for user ${userId}, but all had invalid installation_ids.`);
            return { type: 'error', message: 'Invalid installation data found.' };
        }
        
        console.log(`[RepoAccess] Found ${userInstallations.length} valid installations for user ${userId}. Checking access...`);

    } catch (error: any) {
        console.error(`[RepoAccess] Failed to query user installations for user ${userId}:`, error);
        return { type: 'error', message: `Database error fetching user installations: ${error.message}` };
    }

    // 3. Verify Access via Installations
    for (const { installation_id } of userInstallations) {
        try {
            console.log(`[RepoAccess] Checking access for repo ${repositorySlug} via installation ${installation_id}...`);
            const installationOctokit = await getOctokit(String(installation_id));
            // Try to get the repo using the installation token
            await installationOctokit.rest.repos.get({ owner, repo }); 
            // Success! This installation has access.
            console.log(`[RepoAccess] Access confirmed for repo ${repositorySlug} via installation ${installation_id}.`);
            return {
                type: 'private',
                namespace: `${installation_id}-${pineconeSlug}`,
                installationId: installation_id
            };
        } catch (error: any) {
            if (error.status === 404 || error.status === 403) {
                // This installation does not have access to this specific repository.
                console.log(`[RepoAccess] Installation ${installation_id} does not grant access to ${repositorySlug}. Status: ${error.status}. Checking next...`);
                continue; // Try the next installation
            } else {
                // Other GitHub API error for this installation
                console.error(`[RepoAccess] Error checking access for repo ${repositorySlug} via installation ${installation_id}: ${error.status}`, error.message);
                // Don't fail the whole process, maybe another installation works.
                // Consider if we should return error immediately? For now, log and continue.
                // return { type: 'error', message: `GitHub API error checking installation ${installation_id} access: ${error.message}` };
            }
        }
    }

    // 4. If loop completes without finding access
    console.log(`[RepoAccess] No installation associated with user ${userId} grants access to ${repositorySlug}.`);
    // If we got here, the repo is either private and the user lacks access via any installation,
    // or it was not found publicly and none of their installations could see it either (effectively not found for this user).
    // Returning NotFound seems more appropriate than Forbidden if the public check was also a 404. 
    // However, distinguishing that state cleanly is complex. Let's stick to Forbidden if private, not found otherwise.
    // Simplified: Since we tried installations, and none worked, access is forbidden.
    return { type: 'forbidden' };
} 