import type { Session } from '@supabase/supabase-js';

export type JobStatus = 'queued' | 'in_progress' | 'completed' | 'failed' | 'stopped';
export type AnalysisPhase = 'test-pr' | 'full-analysis' | 'relationship_analysis' | 'relationship_analysis_finalization' | 'populating_repository';

export interface AnalysisJobParams {
  repositorySlug: string;
  isPublic: boolean;
  installationId?: string;
  batchSize: number;
  sinceDate?: string | null;
  maxHistoricalPRs: number;
  testMode: boolean;
}

export interface Job {
  id: string;
  params: AnalysisJobParams;
  status: JobStatus;
  phase: AnalysisPhase;
  progress: number;
  message: string;
  details: Array<{ pr_number?: number; status: string; decisions?: number; reason?: string; error?: string }>;
  error?: string | null;
  createdAt: number;
  updatedAt: number;
  processedCount?: number;
  decisionsCreatedCount?: number;
  noDecisionsCount?: number;
  failedCount?: number;
  processed_nodes_for_relationships_count?: number;
  hasMore?: boolean; // Simulates if more data is expected from backend processing
}

// In-memory store for jobs. In production, use a persistent database.
const jobs: Map<string, Job> = new Map();

// --- Core Analysis Simulation ---
// This is where your actual repository analysis logic would be triggered.
// For now, it just simulates progress.
async function simulateAnalysisProcessing(jobId: string) {
  const job = jobs.get(jobId);
  if (!job || job.status !== 'in_progress') {
    return;
  }

  console.log(`[JobManager] Simulating work for job ${jobId} (${job.params.repositorySlug}) - Phase: ${job.phase}`);

  // Simulate work being done in chunks
  let currentProgress = job.progress;
  const targetProgress = 100;
  const increment = job.params.testMode ? 25 : 10; // Test mode progresses faster

  while (currentProgress < targetProgress && job.status === 'in_progress') {
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000)); // Simulate variable work time

    const currentJobState = jobs.get(jobId); // Re-fetch job to check if it was stopped
    if (!currentJobState || currentJobState.status !== 'in_progress') {
      console.log(`[JobManager] Job ${jobId} processing halted (status: ${currentJobState?.status}).`);
      return;
    }

    currentProgress = Math.min(currentProgress + increment, targetProgress);
    const newDetails = [
        ...(job.details || []),
        { pr_number: Math.floor(Math.random() * 1000), status: 'success', decisions: Math.floor(Math.random() * 3) }
    ];
    
    job.progress = currentProgress;
    job.message = `Analysis is ${currentProgress}% complete.`;
    job.details = newDetails.slice(-5); // Keep last 5 details for brevity
    job.updatedAt = Date.now();
    job.processedCount = (job.processedCount || 0) + 1;
    if (job.params.testMode && currentProgress >= 50 && !job.decisionsCreatedCount) {
        job.decisionsCreatedCount = Math.floor(Math.random()*2)+1;
    } else if (!job.params.testMode) {
        job.decisionsCreatedCount = (job.decisionsCreatedCount || 0) + (Math.random() > 0.7 ? 1: 0);
    }


    console.log(`[JobManager] Job ${jobId} progress: ${currentProgress}%`);
  }

  if (job.status === 'in_progress') { // Ensure it wasn't stopped
    job.status = 'completed';
    job.message = '✅ Analysis complete! Your architectural knowledge base is ready to use.';
    job.progress = 100;
    job.hasMore = false;
    job.updatedAt = Date.now();
    console.log(`[JobManager] Job ${jobId} completed.`);
  }
}

// --- Public API for Job Manager ---

export function createJob(params: AnalysisJobParams): Job {
  const jobId = `job_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  const phase = params.testMode ? 'test-pr' : 'full-analysis';
  const newJob: Job = {
    id: jobId,
    params,
    status: 'queued',
    phase,
    progress: 0,
    message: `Analysis job for ${params.repositorySlug} is queued.`,
    details: [],
    createdAt: Date.now(),
    updatedAt: Date.now(),
    hasMore: true,
  };
  jobs.set(jobId, newJob);
  console.log(`[JobManager] Job created: ${jobId} for ${params.repositorySlug}, Phase: ${phase}`);

  // Simulate dequeuing and starting the job almost immediately
  setTimeout(() => {
    const job = jobs.get(jobId);
    if (job && job.status === 'queued') {
      job.status = 'in_progress';
      job.message = 'Analysis started...';
      job.updatedAt = Date.now();
      jobs.set(jobId, job);
      simulateAnalysisProcessing(jobId); // Start the simulation
    }
  }, 100);

  return newJob;
}

export function getJobStatus(jobId: string): Job | undefined {
  const job = jobs.get(jobId);
  if (job) {
    // For "completed" test PRs, shape the response as expected by the frontend
    if (job.phase === 'test-pr' && job.status === 'completed') {
        return {
            ...job,
            processedCount: job.processedCount || 1, // ensure these fields exist for test-pr completion
            decisionsCreatedCount: job.decisionsCreatedCount || 0,
            noDecisionsCount: (job.processedCount || 1) - (job.decisionsCreatedCount || 0),
            failedCount: job.failedCount || 0,
        };
    }
    return { ...job }; // Return a copy to prevent direct modification
  }
  return undefined;
}

export function requestStopJob(jobId: string): { success: boolean; message: string } {
  const job = jobs.get(jobId);
  if (!job) {
    return { success: false, message: 'Job not found.' };
  }
  if (job.status === 'completed' || job.status === 'failed' || job.status === 'stopped') {
    return { success: true, message: `Job already ${job.status}.` };
  }

  job.status = 'stopped';
  job.message = 'Analysis stop requested by user.';
  job.progress = job.progress > 0 ? job.progress : 0; // Keep current progress or set to 0
  job.hasMore = false;
  job.updatedAt = Date.now();
  jobs.set(jobId, job); // Update the job in the map
  console.log(`[JobManager] Job ${jobId} stop requested.`);
  return { success: true, message: 'Analysis job stop requested.' };
}

// Periodically clean up old jobs (e.g., completed or failed jobs older than 24 hours)
// This is a very basic cleanup. A production system would have more robust lifecycle management.
setInterval(() => {
  const now = Date.now();
  const twentyFourHours = 24 * 60 * 60 * 1000;
  for (const [jobId, job] of jobs.entries()) {
    if ((job.status === 'completed' || job.status === 'failed' || job.status === 'stopped') && (now - job.updatedAt > twentyFourHours)) {
      jobs.delete(jobId);
      console.log(`[JobManager] Cleaned up old job: ${jobId}`);
    }
  }
}, 60 * 60 * 1000); // Run cleanup every hour

console.log('[JobManager] In-memory job manager initialized.'); 