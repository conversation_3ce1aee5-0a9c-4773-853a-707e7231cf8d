import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Pinecone } from '@pinecone-database/pinecone';
import {
  generatePhase1GoalsAndDecisionsPrompt,
  generatePhase2DecisionContextPrompt,
  generatePhase3FullDesignDocPrompt,
  generateDesignDocConceptExtractionPromptForTask,
  generateSearchQueriesForDecisionPointPrompt,
  generateDataModelCompliancePrompt,
  generateTaskAnalysisPrompt,
} from '../analyzer/prompt'; // Assuming prompts are in analyzer
import { callClaudeLlmForJson } from './llmUtils'; // Assuming a JSON-specific LLM utility
import { queryPinecone, getVectorById, getVectorsByIds } from './pineconeUtils'; // Pinecone utilities
import { getRepositoryNamespace } from './pinecone-utils';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const pineconeApiKey = process.env.PINECONE_API_KEY;
const pineconeIndexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';

// Initialize clients lazily to avoid build-time errors
let supabaseAdmin: SupabaseClient | null = null;
let pinecone: Pinecone | null = null;

function initializeClients() {
  if (!supabaseUrl || !supabaseServiceRoleKey || !pineconeApiKey) {
    throw new Error(
      'Supabase URL, Service Role Key, or Pinecone API Key not configured. Design Doc Processor cannot start.'
    );
  }

  if (!supabaseAdmin) {
    supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);
  }

  if (!pinecone) {
    pinecone = new Pinecone({ apiKey: pineconeApiKey });
  }
}

// TODO: Move this to a shared types definition file

// Interface for the output of the data model compliance phase (Phase 3.5)
interface RefinedDataModel {
  model_name: string;
  status: "extended" | "new" | "unchanged_from_draft" | "consolidated";
  description: string;
  schema_definition: any; // Can be a structured object or a string (e.g., SQL DDL, JSON schema)
  justification_and_compliance_notes: string;
  original_draft_issues_addressed?: string[];
}

interface RefinedDataModels {
  summary_of_compliance_approach: string;
  models: RefinedDataModel[];
  unresolved_compliance_concerns?: string[];
}

interface RefinedDataModelsOutput {
  refined_data_models: RefinedDataModels;
}

export interface DesignDocJob {
  id: string;
  repository_slug: string;
  task_title: string;
  task_description: string;
  initial_ideas?: string;
  current_phase: string; // e.g., 'phase0_pending', 'phase1_pending', etc.
  status: string; // 'processing', 'completed', 'error'
  created_at: string;
  updated_at: string;
  error_message?: string;
  phase0_output?: { // Output from initial concept extraction
    initialTaskConcepts: string[];
  };
  phase1_output?: { // Define structure for phase1_output
    goals: string[];
    non_goals: string[];
    critical_technical_decision_points: any[];
    initialTaskConcepts?: string[]; 
    taskAnalysis: any;
    core_vs_deferrable_analysis?: any;
    strategic_recommendations?: any;
  };
  phase2_output?: { // Added more specific structure
    analyzed_decision_points: any[];
    unique_past_decision_ids_for_phase3: string[];
  };
  phase3_output?: any; // The final design document object
  phase3_5_refined_data_models_output?: RefinedDataModelsOutput; // Output from data model refinement
  referenced_decisions?: any[]; // Array of full past decision objects
  installation_id?: number;
  output_file_path?: string;
  implementation_plan_status?: 'pending' | 'generating' | 'completed' | 'error'; // New field
  phase4_output?: any; // New field for implementation plan
  finalized_design_doc_markdown?: string; // New field for storing the finalized markdown
  ai_agent_protocol?: string; // New field for AI Agent Protocol content
  document_title?: string; // New field for document title override
}

async function updateJob(jobId: string, updates: Partial<DesignDocJob>) {
  console.log(`[Job ${jobId}] Updating job with:`, updates);
  const { error } = await supabaseAdmin!
    .from('design_doc_jobs')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', jobId);

  if (error) {
    console.error(`[Job ${jobId}] Error updating job:`, error);
    throw new Error(`Failed to update job status for ${jobId}: ${error.message}`);
  }
}

async function processPhase1(job: DesignDocJob): Promise<any> {
  console.log(`[Job ${job.id}] Starting Phase 1: Goals & Decisions Identification`);
  await updateJob(job.id, { current_phase: 'phase1_goals_decisions_inprogress' });

  // --- Step 1: Perform Task Analysis ---
  console.log(`[Job ${job.id}] Phase 1: Performing Task Analysis...`);
  const taskAnalysisPrompt = generateTaskAnalysisPrompt(job.task_title, []);
  const taskAnalysisResult = await callClaudeLlmForJson(taskAnalysisPrompt);
  console.log(`[Job ${job.id}] Phase 1: Task Analysis complete.`);

  // --- Step 2: Fetch Project Constitution and handle it ---
  let projectConstitution = null;
  
  try {
    console.log(`[Job ${job.id}] Phase 1: Fetching project constitution for repo: ${job.repository_slug}`);
    const { data: constitutionData, error: constitutionError } = await supabaseAdmin
      .from('project_constitutions')
      .select('constitution_data')
      .eq('repository_slug', job.repository_slug)
      .single();

    if (constitutionError) {
      console.log(`[Job ${job.id}] Phase 1: Could not fetch project constitution. It may not exist. Proceeding without it.`);
    } else if (constitutionData) {
      console.log(`[Job ${job.id}] Phase 1: Found project constitution. Using complete constitution for analysis.`);
      projectConstitution = constitutionData.constitution_data;
      console.log(`[Job ${job.id}] Phase 1: Using complete constitution with company stage, project type, priorities, and architectural principles.`);
    }
  } catch (e: any) {
    console.error(`[Job ${job.id}] Phase 1: Error during constitution processing: ${e.message}. Proceeding without it.`);
  }

  const taskDetails = {
    taskTitle: job.task_title,
    taskDescription: job.task_description,
    initialIdeas: job.initial_ideas,
  };
  const phase1PromptContent = generatePhase1GoalsAndDecisionsPrompt(taskDetails, taskAnalysisResult, projectConstitution);

  const llmPhase1Result = await callClaudeLlmForJson(phase1PromptContent);

  if (!llmPhase1Result.critical_technical_decision_points) {
    throw new Error('Phase 1 LLM output is missing required field: critical_technical_decision_points.');
  }

  const critical_technical_decision_points = llmPhase1Result.critical_technical_decision_points.map((dp:any, index:number) => ({
    ...dp,
    id: dp.id || `tdp_${String(index + 1).padStart(3, '0')}`
  }));

  const phase1Output = {
    goals: llmPhase1Result.goals || [], // Goals might be generated later if not present
    non_goals: llmPhase1Result.non_goals || [], // Non-goals might be generated later if not present
    critical_technical_decision_points: critical_technical_decision_points,
    initialTaskConcepts: taskAnalysisResult.initial_domain_concepts || [],
    taskAnalysis: taskAnalysisResult,
    // Store strategic analysis fields for later use
    core_vs_deferrable_analysis: llmPhase1Result.core_vs_deferrable_analysis || null,
    strategic_recommendations: llmPhase1Result.strategic_recommendations || null,
  };

  await updateJob(job.id, { phase1_output: phase1Output, current_phase: 'phase1_goals_decisions_complete' });
  console.log(`[Job ${job.id}] Completed Phase 1 with strategic analysis.`);
  return phase1Output;
}

async function processPhase2(job: DesignDocJob, phase1Output: DesignDocJob['phase1_output']): Promise<any> {
  if (!phase1Output || !phase1Output.taskAnalysis) {
    throw new Error('Phase 1 output or its taskAnalysis is undefined for Phase 2');
  }
  console.log(`[Job ${job.id}] Starting Phase 2: Technical Decision Point Analysis`);
  await updateJob(job.id, { current_phase: 'phase2_decision_context_inprogress' });

  // Fetch Project Constitution for Phase 2
  let projectConstitution = null;
  try {
    console.log(`[Job ${job.id}] Phase 2: Fetching project constitution for repo: ${job.repository_slug}`);
    const { data: constitutionData, error: constitutionError } = await supabaseAdmin
      .from('project_constitutions')
      .select('constitution_data')
      .eq('repository_slug', job.repository_slug)
      .single();

    if (constitutionError) {
      console.log(`[Job ${job.id}] Phase 2: Could not fetch project constitution. It may not exist. Proceeding without it.`);
    } else if (constitutionData) {
      console.log(`[Job ${job.id}] Phase 2: Found project constitution. Using complete constitution for decision analysis.`);
      projectConstitution = constitutionData.constitution_data;
    }
  } catch (e: any) {
    console.error(`[Job ${job.id}] Phase 2: Error during constitution processing: ${e.message}. Proceeding without it.`);
  }

  const analyzedDecisionPoints: any[] = []; 
  const allUniquePastDecisionIdsForPhase3 = new Set<string>();
  const overallTaskContext = { taskTitle: job.task_title, taskDescription: job.task_description };
  const jobNamespace = getRepositoryNamespace(job.installation_id || 0, job.repository_slug);

  const globallyRelevantPastDecisions: any[] = [];
  const uniqueGlobalResultIds = new Set<string>(); 
  const initialTaskConceptsFromPhase1 = phase1Output.initialTaskConcepts || [];

  if (initialTaskConceptsFromPhase1.length > 0) {
    console.log(`[Job ${job.id}] Phase 2: Fetching globally relevant decisions by querying for ${initialTaskConceptsFromPhase1.length} initial task concepts individually.`);

    for (const concept of initialTaskConceptsFromPhase1) {
      if (!concept || concept.trim() === "") {
        console.log(`[Job ${job.id}] Phase 2: Skipping empty initial task concept for global context query.`);
        continue;
      }
      console.log(`[Job ${job.id}] Phase 2: Global context query with individual concept: "${concept.substring(0,100)}..."`);
      try {
        const pineconeResults = await queryPinecone(
          concept.trim(), 
          jobNamespace,
          3, 
          0.4 
        );
        pineconeResults.forEach(result => {
          if (result.id && !uniqueGlobalResultIds.has(result.id)) {
            globallyRelevantPastDecisions.push({ 
              id: result.id,
              score: result.score,
              ...result.metadata 
            });
            uniqueGlobalResultIds.add(result.id);
            allUniquePastDecisionIdsForPhase3.add(result.id); 
          }
        });
      } catch (e:any) {
          console.warn(`[Job ${job.id}] Phase 2: Error querying Pinecone for concept "${concept}":`, e.message);
      }
    }
    console.log(`[Job ${job.id}] Phase 2: Fetched ${globallyRelevantPastDecisions.length} unique globally relevant past decisions after querying for all initial task concepts.`);
  } else {
    console.log(`[Job ${job.id}] Phase 2: No initial task concepts from Phase 1 to use for global context fetch.`);
  }

  const decisionPointAnalysisPromises = phase1Output.critical_technical_decision_points.map(async (decisionPoint) => {
    console.log(`[Job ${job.id}] Phase 2: Analyzing TDP "${decisionPoint.decision_point_title}" (ID: ${decisionPoint.id})`);

    const decisionPointDetailsForQueryGen = {
        title: decisionPoint.decision_point_title,
        description: decisionPoint.decision_point_description,
        potential_impact_areas: decisionPoint.potential_impact_areas,
    };
    const searchQueryGenPrompt = generateSearchQueriesForDecisionPointPrompt(decisionPointDetailsForQueryGen, overallTaskContext);
    const searchQueryGenResult = await callClaudeLlmForJson(searchQueryGenPrompt);
    const specificSearchQueries: string[] = searchQueryGenResult.search_queries || [];
    console.log(`[Job ${job.id}] Phase 2: Generated ${specificSearchQueries.length} specific search queries for TDP "${decisionPoint.id}":`, specificSearchQueries);

    const tdpSpecificPastDecisions: any[] = [];
    const uniqueTdpResultIds = new Set<string>(); 

    if (specificSearchQueries.length > 0) {
      for (const sQuery of specificSearchQueries) {
        if (!sQuery || sQuery.trim() === "") continue;
        console.log(`[Job ${job.id}] Phase 2: TDP-specific query for "${decisionPoint.id}" with: "${sQuery.substring(0,100)}..."`);
        const pineconeResults = await queryPinecone(sQuery, jobNamespace, 3, 0.45);
        pineconeResults.forEach(result => {
          if (result.id && !uniqueTdpResultIds.has(result.id) && !uniqueGlobalResultIds.has(result.id)) {
            tdpSpecificPastDecisions.push({
                id: result.id,
                score: result.score,
                ...result.metadata
            });
            uniqueTdpResultIds.add(result.id);
            allUniquePastDecisionIdsForPhase3.add(result.id); 
          }
        });
      }
      console.log(`[Job ${job.id}] Phase 2: Retrieved ${tdpSpecificPastDecisions.length} unique TDP-specific past decisions for TDP "${decisionPoint.id}".`);
    } else {
      console.log(`[Job ${job.id}] Phase 2: No specific search queries for TDP "${decisionPoint.id}".`);
    }

    const combinedContextForPrompt = [
      ...globallyRelevantPastDecisions.map(d => ({
        id: d.id,
        title: d.title || 'Untitled Decision',
        description: d.description || d.summary || 'No description available',
        score: d.score,
        source: 'global',
        dev_prompt: d.dev_prompt,
        follows_standard_practice_reason: d.follows_standard_practice_reason,
        domain_concepts: d.domain_concepts,
        related_files: d.related_files
      })),
      ...tdpSpecificPastDecisions.map(d => ({
        id: d.id,
        title: d.title || 'Untitled Decision',
        description: d.description || d.summary || 'No description available',
        score: d.score,
        source: 'tdp-specific',
        dev_prompt: d.dev_prompt,
        follows_standard_practice_reason: d.follows_standard_practice_reason,
        domain_concepts: d.domain_concepts,
        related_files: d.related_files
      }))
    ];
    const finalPromptContextMap = new Map<string, any>();
    combinedContextForPrompt.forEach(item => {
        if (!finalPromptContextMap.has(item.id)) {
            finalPromptContextMap.set(item.id, item);
        }
    });
    const finalPromptContext = Array.from(finalPromptContextMap.values());

    console.log(`[Job ${job.id}] Phase 2: Using ${finalPromptContext.length} combined unique summaries for TDP "${decisionPoint.id}" analysis.`);

    const phase2Prompt = generatePhase2DecisionContextPrompt(
      decisionPoint,
      finalPromptContext,
      phase1Output.taskAnalysis,
      projectConstitution // Pass projectConstitution here
    );

    const decisionAnalysis = await callClaudeLlmForJson(phase2Prompt);

    if (!decisionAnalysis.decision_point_id) {
        console.warn(`[Job ${job.id}] Phase 2 LLM output for TDP "${decisionPoint.decision_point_title}" missing decision_point_id. Using original.`);
        decisionAnalysis.decision_point_id = decisionPoint.id;
    }
    if (decisionAnalysis.decision_point_id !== decisionPoint.id) {
        console.warn(`[Job ${job.id}] Phase 2 LLM output TDP ID "${decisionAnalysis.decision_point_id}" mismatch with original "${decisionPoint.id}". Using original.`);
        decisionAnalysis.decision_point_id = decisionPoint.id;
    }
    return decisionAnalysis; 
  });

  const resolvedAnalyzedDecisionPoints = await Promise.all(decisionPointAnalysisPromises);
  analyzedDecisionPoints.push(...resolvedAnalyzedDecisionPoints);

  console.log(`[Job ${job.id}] Phase 2: Completed analysis for all ${analyzedDecisionPoints.length} TDPs.`);

  const phase2Output = {
    analyzed_decision_points: analyzedDecisionPoints,
    unique_past_decision_ids_for_phase3: Array.from(allUniquePastDecisionIdsForPhase3)
  };

  await updateJob(job.id, { phase2_output: phase2Output, current_phase: 'phase2_decision_context_complete' });
  console.log(`[Job ${job.id}] Completed Phase 2.`);
  return phase2Output;
}

async function processPhase3(job: DesignDocJob, phase1Output: DesignDocJob['phase1_output'], phase2Output: any): Promise<any> {
  if (!phase1Output) throw new Error('Phase 1 output is undefined for Phase 3');
  console.log(`[Job ${job.id}] Starting Phase 3: Full Design Document Generation`);
  await updateJob(job.id, { current_phase: 'phase3_doc_generation_inprogress' });

  // Fetch Project Constitution for Phase 3
  let projectConstitution = null;
  try {
    console.log(`[Job ${job.id}] Phase 3: Fetching project constitution for repo: ${job.repository_slug}`);
    const { data: constitutionData, error: constitutionError } = await supabaseAdmin
      .from('project_constitutions')
      .select('constitution_data')
      .eq('repository_slug', job.repository_slug)
      .single();

    if (constitutionError) {
      console.log(`[Job ${job.id}] Phase 3: Could not fetch project constitution. It may not exist. Proceeding without it.`);
    } else if (constitutionData) {
      console.log(`[Job ${job.id}] Phase 3: Found project constitution. Using complete constitution for strategic milestone prioritization.`);
      projectConstitution = constitutionData.constitution_data;
    }
  } catch (e: any) {
    console.error(`[Job ${job.id}] Phase 3: Error during constitution processing: ${e.message}. Proceeding without it.`);
  }

  let fullPastDecisionsDetails: any[] = [];
  const idsToFetch = phase2Output.unique_past_decision_ids_for_phase3;

  if (idsToFetch && idsToFetch.length > 0) {
    console.log(`[Job ${job.id}] Phase 3: Fetching full details for ${idsToFetch.length} past decisions:`, idsToFetch);
    fullPastDecisionsDetails = await getVectorsByIds(idsToFetch, getRepositoryNamespace(job.installation_id || 0, job.repository_slug));
    console.log(`[Job ${job.id}] Phase 3: Fetched ${fullPastDecisionsDetails.length} full past decision details.`);
  }
  await updateJob(job.id, { referenced_decisions: fullPastDecisionsDetails });
  job.referenced_decisions = fullPastDecisionsDetails;

  const phase3Prompt = generatePhase3FullDesignDocPrompt(
    {
      taskTitle: job.task_title,
      taskDescription: job.task_description,
      initialApproachIdeas: job.initial_ideas,
    },
    phase1Output.taskAnalysis,
    phase1Output,
    phase2Output.analyzed_decision_points,
    fullPastDecisionsDetails,
    [], // project constraints - empty for now
    projectConstitution // Pass the project constitution for strategic guidance
  );

  const llmPhase3Result = await callClaudeLlmForJson(phase3Prompt);

  if (!llmPhase3Result.title || !llmPhase3Result.goals || !llmPhase3Result.high_level_design) {
      throw new Error('Phase 3 LLM output is missing required fields (title, goals, high_level_design).');
  }

  console.log(`[Job ${job.id}] Completed Phase 3 initial document generation.`);
  return llmPhase3Result; 
}

async function processPhase3_5_DataModelRefinement(job: DesignDocJob): Promise<RefinedDataModelsOutput> {
  console.log(`[Job ${job.id}] Starting Phase 3.5: Data Model Refinement & Compliance`);
  await updateJob(job.id, { current_phase: 'phase3_5_datamodel_refinement_inprogress' });

  if (!job.phase3_output || !job.phase3_output.high_level_design || typeof job.phase3_output.high_level_design.data_model_changes !== 'string') {
    throw new Error('Phase 3 output or its data_model_changes (string) are missing or not in the expected format for Phase 3.5.');
  }

  const draftDataModelChanges = job.phase3_output.high_level_design.data_model_changes;
  const jobNamespace = getRepositoryNamespace(job.installation_id || 0, job.repository_slug);
  
  const genericQueryString = "any architectural decision"; // A very generic query.
  console.log(`[Job ${job.id}] Phase 3.5: Querying Pinecone broadly in namespace ${jobNamespace}. Will filter for active decisions with data_model_changes in code.`);
  
  const pineconeQueryResults = await queryPinecone(
    genericQueryString, // Generic query string
    jobNamespace,       // Repository-specific namespace
    1000,               // High topK to fetch many candidates
    0.01                // Very low threshold, filtering is done in JS
    // No server-side filter object here, as queryPinecone expects 4 args.
  );
  console.log(`[Job ${job.id}] Phase 3.5: Retrieved ${pineconeQueryResults.length} candidate decisions from Pinecone for client-side filtering.`);

  const filteredPastDecisionDataModels: any[] = [];
  if (pineconeQueryResults) {
    for (const result of pineconeQueryResults) {
      // CLIENT-SIDE FILTER for is_superseded
      if (result.metadata && result.metadata.is_superseded === false) {
        // CLIENT-SIDE FILTER for presence and non-emptiness of data_model_changes
        if (result.metadata.data_model_changes && 
            ( (Array.isArray(result.metadata.data_model_changes) && result.metadata.data_model_changes.length > 0) || 
              (typeof result.metadata.data_model_changes === 'object' && Object.keys(result.metadata.data_model_changes).length > 0) ||
              (typeof result.metadata.data_model_changes === 'string' && result.metadata.data_model_changes.trim() !== '') ) 
           ) {
          filteredPastDecisionDataModels.push({
            id: result.id,
            title: result.metadata.title || 'Untitled Past Decision', 
            data_model_changes: result.metadata.data_model_changes 
          });
        }
      }
    }
  }
  console.log(`[Job ${job.id}] Phase 3.5: Filtered down to ${filteredPastDecisionDataModels.length} active decisions with populated data_model_changes after client-side processing.`);

  const overallTaskContext = {
    taskTitle: job.task_title,
    taskDescription: job.task_description,
  };

  const compliancePrompt = generateDataModelCompliancePrompt(
    draftDataModelChanges,
    filteredPastDecisionDataModels, 
    overallTaskContext
  );

  console.log(`[Job ${job.id}] Phase 3.5: Compliance Prompt being sent to LLM:\n`, compliancePrompt);

  const llmComplianceResult = await callClaudeLlmForJson(compliancePrompt) as RefinedDataModelsOutput;

  if (!llmComplianceResult.refined_data_models || !llmComplianceResult.refined_data_models.models) {
    throw new Error('Phase 3.5 LLM output is missing required fields (refined_data_models.models).');
  }

  console.log(`[Job ${job.id}] Completed Phase 3.5 Data Model Refinement & Compliance.`);
  return llmComplianceResult;
}

export async function processPhase4(job: DesignDocJob): Promise<any> {
  initializeClients();
  console.log(`[Job ${job.id}] Starting Phase 4: Actionable Implementation Plan Generation with Quality Control`);

  try {
    // Check if we have finalized markdown content
    if (!job.finalized_design_doc_markdown) {
      console.warn(`[Job ${job.id}] No finalized_design_doc_markdown found. Falling back to structured data from previous phases.`);
      
      if (!job.phase3_output || !job.phase1_output || !job.phase2_output) {
        throw new Error('Missing finalized markdown and required outputs from previous phases (Phase1, Phase2, or Phase3) to generate implementation plan.');
      }
      
      // Fallback to old behavior if no finalized markdown
      const { task_title, task_description, installation_id, repository_slug } = job;
      const goals = job.phase1_output?.goals || [];
      const non_goals = job.phase1_output?.non_goals || [];
      const core_architectural_choices = job.phase3_output?.core_architectural_choices || [];
      const milestones_sketch = job.phase3_output?.milestones_sketch || '';
      const success_metrics = job.phase3_output?.success_metrics || [];
      
      return await generateImplementationPlanFromStructuredData(job, goals, non_goals, core_architectural_choices, milestones_sketch, success_metrics);
    }

    console.log(`[Job ${job.id}] Using entire finalized markdown content for implementation plan generation.`);
    
    const { task_title, task_description, installation_id, repository_slug } = job;
    const entireMarkdownContent = job.finalized_design_doc_markdown;
    
    const implementationPlanTitle = `Implementation Plan for: ${task_title}`;
    const designDocumentReferenceTitle = task_title;
    const jobNamespace = getRepositoryNamespace(installation_id || 0, repository_slug);

    // Generate a cleaner design document identifier for branch naming
    const designDocIdentifier = task_title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 50); // Limit length for practical Git usage

    // Generate implementation plan directly from the entire markdown content using LLM
    const implementationPlanPrompt = `
      You are an expert software architect tasked with creating a detailed, actionable implementation plan from a finalized design document with robust quality control, scope validation, and risk assessment built into every milestone.
      
      Task: ${task_title}
      Description: ${task_description}
      
      Design Document (Complete Markdown):
      ${entireMarkdownContent}
      
      ${job.ai_agent_protocol ? `
      AI AGENT IMPLEMENTATION PROTOCOL:
      The implementation plan must follow the AI Agent Implementation Protocol provided below. This protocol defines the specific workflow, branch naming conventions, milestone structure, and quality requirements that must be followed:
      
      ${job.ai_agent_protocol}
      
      CRITICAL PROTOCOL REQUIREMENTS: Your implementation plan must strictly adhere to the above protocol, particularly:
      - Branch naming conventions (feature/<design-doc-identifier> for main, feature/<design-doc-identifier>/milestone-N-<description> for milestones)
      - Milestone-driven approach with independent, testable milestones that include scope validation
      ` : ''}
      
      Your task is to analyze the ENTIRE design document above and create a comprehensive implementation plan that breaks down the project into actionable milestones with built-in quality control.
      
      IMPORTANT BRANCH NAMING REQUIREMENTS:
      - Main feature branch: feature/${designDocIdentifier}
      - Milestone branches: feature/${designDocIdentifier}/milestone-N-<short-description>
      - Branch names must be Git-friendly (lowercase, hyphens, no spaces)
      
      QUALITY CONTROL REQUIREMENTS:
      1. Each milestone must include explicit scope validation criteria
      2. **CRITICAL**: Each milestone must minimize introduction of new security or ux risks
      4. Each milestone must be independently testable and deployable
      5. Include specific validation methods for each deliverable
      
      Requirements:
      1. Analyze the full document content - don't miss any important details
      2. Create logical incremental milestones atmost 5 as dictated in the design document that build upon each other with clear validation points
      3. Each milestone should have specific, actionable deliverables with measurable success criteria
      5. Identify key architectural decisions and their implementation requirements with validation steps
      6. Extract any success metrics or verification criteria mentioned in the design document
      7. Consider all sections of the document comprehensively
      8. **CRITICAL**: Provide concrete, specific branch naming conventions for each milestone
      9. **CRITICAL**: Ensure each milestone can be independently integrated with proper scope validation
      
      Output Format (JSON only):
      {
        "git_must_use_main_feature_branch_name": "feature/${designDocIdentifier}",
        "milestones": [
          {
            "milestone_id": "M1.0",
            "title": "string - Clear milestone title",
            "description": "string - Detailed description with scope boundaries",
            "priority": "P0-Critical|P1-High|P2-Medium",
            "key_tasks_and_deliverables": [
              {
                "task": "string - Specific deliverable",
                "validation_criteria": "string - How to verify completion"
              }
            ],
            "scope_validation": {
              "planned_deliverables": ["specific items that must be delivered"]
            },
            "verification_criteria": ["criteria1", "criteria2"]
          }
        ],
        "data_model_summary": "string - Summary of data model changes with validation requirements",
        "referenced_decisions": ["summary of key referenced decisions"]
      }
    `;

    console.log(`[Job ${job.id}] Generating implementation plan from entire markdown content using LLM.`);
    const implementationPlanResult = await callClaudeLlmForJson(implementationPlanPrompt);

    // Enhance each milestone with relevant guidance from past decisions
    const enhancedMilestones = [];
    if (implementationPlanResult.milestones && Array.isArray(implementationPlanResult.milestones)) {
      for (let i = 0; i < implementationPlanResult.milestones.length; i++) {
        const milestone = implementationPlanResult.milestones[i];
        
        // Ensure branch naming is consistent and follows conventions
        const milestoneNumber = i + 1;
        const milestoneTitle = milestone.title || milestone.description || `Milestone ${milestoneNumber}`;
        const shortDescription = milestoneTitle
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '')
          .split('-')
          .slice(0, 3) // Take first 3 words
          .join('-');
        
        const correctBranchName = `feature/${designDocIdentifier}/milestone-${milestoneNumber}-${shortDescription}`;
        
        // Generate search queries for this milestone
        const searchQueriesPrompt = `
          For the milestone: "${milestoneTitle}"
          Description: "${milestone.description || 'No description provided'}"
          Key Tasks: ${milestone.key_tasks_and_deliverables?.map((task: any) => 
            typeof task === 'string' ? task : task.task || 'Task details not specified'
          ).join(', ') || 'No specific tasks listed'}
          
          Generate 3-5 specific search queries to find relevant architectural decisions and best practices.
          Focus on the core technical challenges and implementation patterns needed.
          
          Output Format (JSON only):
          {
            "search_queries": ["query1", "query2", "query3"]
          }
        `;
        
        const searchQueriesResult = await callClaudeLlmForJson(searchQueriesPrompt);
        const searchQueries = searchQueriesResult.search_queries || [];

        // Search for relevant guidance
        const applicableDeveloperGuidanceAndBestPractices = [];
        const uniqueRetrievedDecisionIds = new Set<string>();

        for (const query of searchQueries) {
          if (!query || typeof query !== 'string') continue;
          
          console.log(`[Job ${job.id}] Phase 4 - Milestone ${milestone.milestone_id}: Searching with "${query}" in namespace ${jobNamespace}`);
          try {
            const pineconeResults = await queryPinecone(query, jobNamespace, 3, 0.4);
            
            for (const match of pineconeResults) {
              if (match.id && !uniqueRetrievedDecisionIds.has(match.id)) {
                uniqueRetrievedDecisionIds.add(match.id);
                applicableDeveloperGuidanceAndBestPractices.push({
                  retrieved_decision_id: match.id,
                  retrieved_decision_title: match.metadata?.title || '',
                  guidance_summary: match.metadata?.dev_prompt || match.metadata?.follows_standard_practice_reason || '',
                 // relevance_to_milestone_tasks: `Retrieved with query: "${query}" for milestone: ${milestone.title}`,
                  related_files: match.metadata?.related_files || [],
                //  retrieval_confidence_score: match.score,
                });
              }
            }
          } catch (error) {
            console.warn(`[Job ${job.id}] Error querying Pinecone for milestone ${milestone.milestone_id}:`, error);
          }
        }

         // Enhance the milestone with guidance and search queries
         enhancedMilestones.push({
           ...milestone,
           git_must_use_milestone_branch_name: correctBranchName,
           // Ensure title is available for display
           title: milestoneTitle,
           // Convert task objects to readable strings for display
           key_tasks_and_deliverables: milestone.key_tasks_and_deliverables?.map((task: any) => {
             if (typeof task === 'string') {
               return task;
             } else if (task && typeof task === 'object') {
               const taskText = task.task || 'Task not specified';
               const validationText = task.validation_criteria;
               return validationText ? `${taskText} (Validation: ${validationText})` : taskText;
             }
             return 'Task details not available';
           }) || [],
           // Ensure scope_validation has proper structure
           scope_validation: milestone.scope_validation || {
             planned_deliverables: ['Implementation scope to be defined']
           },
        //   branch_naming_rationale: `Follows convention: feature/${designDocIdentifier}/milestone-${milestoneNumber}-${shortDescription}. Descriptive yet concise for Git workflow.`,
        //   derived_domain_concepts_for_guidance_search: searchQueries,
          applicable_developer_guidance_and_best_practices: applicableDeveloperGuidanceAndBestPractices,
        //   relevant_arch_choices_titles: implementationPlanResult.key_architectural_choices_summary?.map((choice: any) => choice.decision_title) || [],
        //   git_integration_notes: `Create PR from ${correctBranchName} to feature/${designDocIdentifier}, then integrate milestone to main branch upon completion.`,
        //   // Include AI agent compliance if provided by LLM, otherwise provide defaults
        //   ai_agent_compliance: milestone.ai_agent_compliance || {
        //     follows_protocol: true,
        //     independent_integration: `Milestone ${milestoneNumber} can be independently tested and integrated without dependencies on future milestones`,
        //     atomic_commit_strategy: "Break implementation into logical, atomic commits following conventional commit format",
        //     testing_approach: "Comprehensive unit tests and integration tests specific to this milestone's deliverables"
        //   }
         });
      }
    }

    implementationPlanResult.milestones = enhancedMilestones;
    console.log(`[Job ${job.id}] Phase 4: Enhanced milestones:`, implementationPlanResult.milestones);
    return implementationPlanResult;
      // implementation_plan_title: implementationPlanTitle,
      // version: "1.0",
      // design_document_reference_title: designDocumentReferenceTitle,
      // overall_project_goals: implementationPlanResult.overall_project_goals || [],
      // overall_project_non_goals: implementationPlanResult.overall_project_non_goals || [],
      // key_architectural_choices_summary: implementationPlanResult.key_architectural_choices_summary || [],
      // main_feature_branch_name: `feature/${designDocIdentifier}`,
      // git_workflow_guidance: {
      //   primary_branch: "main",
      //   main_feature_branch: `feature/${designDocIdentifier}`,
      //   integration_strategy: implementationPlanResult.git_workflow_guidance?.integration_strategy || "Each milestone is developed on its own branch, merged to main feature branch, then integrated to main upon completion",
      //   pr_naming_convention: implementationPlanResult.git_workflow_guidance?.pr_naming_convention || "Feat(Milestone N): <Milestone Title>",
      //   commit_message_format: implementationPlanResult.git_workflow_guidance?.commit_message_format || "feat/fix/test: <clear description> (follows Conventional Commits)",
      //   branch_cleanup_strategy: "Delete milestone branches after merging, delete main feature branch after final integration",
      //   milestone_integration_workflow: implementationPlanResult.git_workflow_guidance?.milestone_integration_workflow || "Standard PR review -> merge to feature branch -> integration PR to main",
      //   quality_gate_enforcement: implementationPlanResult.git_workflow_guidance?.quality_gate_enforcement || "All quality gates must pass before milestone integration: testing, security scans, performance validation, and code review approval"
      // },
      // global_guidance_from_initial_design_context: implementationPlanResult.referenced_decisions_summary?.map((summary: any) => ({
      //   guidance_title: typeof summary === 'string' ? "Referenced Decision" : summary.decision || "Referenced Decision",
      //   source_decision_ids: [],
      //   summary_of_practice: typeof summary === 'string' ? summary : summary.implementation_guidance || "Referenced in design document"
      // })) || [],
      // milestones: enhancedMilestones,
      // final_success_metrics_recap: implementationPlanResult.final_success_metrics_recap || [],
      // refined_data_model_summary: implementationPlanResult.data_model_summary || "Data model details analyzed from complete design document.",
      // finalized_design_doc_content_used: true, // Flag to indicate we used the entire finalized markdown
      // full_design_doc_analyzed: true, // Additional flag to indicate complete document analysis
      // ai_agent_protocol_integration: {
      //   protocol_version: "1.0",
      //   follows_milestone_driven_approach: true,
      //   branch_naming_enforced: true,
      //   atomic_commits_required: true,
      //   independent_milestone_integration: true,
      //   protocol_provided: !!job.ai_agent_protocol,
      //   quality_control_integrated: true, // New flag to indicate quality control features
      //   scope_validation_required: true, // New flag to indicate scope validation is built-in
      //   ai_agent_protocol_compliance: implementationPlanResult.ai_agent_protocol_compliance || {
      //     protocol_followed: true,
      //     phase_0_guidance: "Analyze design document and confirm/define milestones with scope boundaries",
      //     phase_1_guidance: "Create main feature branch following naming convention and establish quality baselines",
      //     phase_2_guidance: "Implement each milestone on dedicated branch with atomic commits and continuous validation",
      //     phase_3_guidance: "Review, validate scope compliance, and integrate milestones via PR workflow with quality gates"
      //   }
    //   }
    // };

    // console.log(`[Job ${job.id}] Successfully generated implementation plan with quality control from entire finalized markdown content in processPhase4.`);
    // return phase4Output;

  } catch (error: any) {
    console.error(`[Job ${job.id}] Error in processPhase4:`, error);
    throw error; 
  }
}

// Helper function to parse design document markdown
function parseDesignDocMarkdown(markdownContent: string): any {
  const parsed = {
    goals: [] as string[],
    non_goals: [] as string[],
    architectural_choices: [] as Array<{decision_title: string; recommended_approach: string}>,
    success_metrics: [] as string[],
    referenced_decisions: [] as Array<{guidance_title: string; source_decision_ids: string[]; summary_of_practice: string}>,
    data_model_summary: "",
    milestones_content: ""
  };

  try {
    // Parse goals section
    const goalsMatch = markdownContent.match(/## Goals\s*\n([\s\S]*?)(?=\n## |$)/i);
    if (goalsMatch) {
      parsed.goals = goalsMatch[1]
        .split('\n')
        .filter(line => line.trim().startsWith('- '))
        .map(line => line.replace(/^- /, '').trim())
        .filter(goal => goal.length > 0);
    }

    // Parse non-goals section
    const nonGoalsMatch = markdownContent.match(/## Non-Goals\s*\n([\s\S]*?)(?=\n## |$)/i);
    if (nonGoalsMatch) {
      parsed.non_goals = nonGoalsMatch[1]
        .split('\n')
        .filter(line => line.trim().startsWith('- '))
        .map(line => line.replace(/^- /, '').trim())
        .filter(goal => goal.length > 0);
    }

    // Parse architectural choices (look for core architectural choices or high level design sections)
    const archChoicesMatch = markdownContent.match(/### Core Architectural Choices\s*\n([\s\S]*?)(?=\n### |$)/i) ||
                            markdownContent.match(/## High Level Design\s*\n([\s\S]*?)(?=\n## |$)/i);
    if (archChoicesMatch) {
      // Extract key decisions from the content
      const content = archChoicesMatch[1];
      const listItems = content.split('\n')
        .filter(line => line.trim().startsWith('- ') || line.trim().startsWith('* '))
        .map(line => line.replace(/^[*-] /, '').trim())
        .filter(item => item.length > 0);
      
      parsed.architectural_choices = listItems.map((item, index) => ({
        decision_title: `Architectural Choice ${index + 1}`,
        recommended_approach: item
      }));
    }

    // Parse success metrics
    const metricsMatch = markdownContent.match(/## Success Metrics\s*\n([\s\S]*?)(?=\n## |$)/i);
    if (metricsMatch) {
      parsed.success_metrics = metricsMatch[1]
        .split('\n')
        .filter(line => line.trim().startsWith('- '))
        .map(line => line.replace(/^- /, '').trim())
        .filter(metric => metric.length > 0);
    }

    // Parse referenced decisions
    const referencedMatch = markdownContent.match(/## Referenced Architectural Decisions\s*\n([\s\S]*?)(?=\n## |$)/i);
    if (referencedMatch) {
      const content = referencedMatch[1];
      // Look for decision references with cursor:// links
      const decisionLinks = content.match(/\[([^\]]+)\]\(cursor:\/\/archknow\.archknow\/viewDecision\?id=([^)]+)\)/g);
      if (decisionLinks) {
        parsed.referenced_decisions = decisionLinks.map(link => {
          const match = link.match(/\[([^\]]+)\]\(cursor:\/\/archknow\.archknow\/viewDecision\?id=([^)]+)\)/);
          if (match) {
            return {
              guidance_title: match[1],
              source_decision_ids: [match[2]],
              summary_of_practice: "Referenced in finalized design document"
            };
          }
          return null;
        }).filter((item): item is {guidance_title: string; source_decision_ids: string[]; summary_of_practice: string} => item !== null);
      }
    }

    // Parse data model information
    const dataModelMatch = markdownContent.match(/### Data Model Changes\s*\n([\s\S]*?)(?=\n### |$)/i) ||
                          markdownContent.match(/## Refined Data Models\s*\n([\s\S]*?)(?=\n## |$)/i);
    if (dataModelMatch) {
      parsed.data_model_summary = dataModelMatch[1].trim().substring(0, 500);
    }

    // Extract content that might contain milestones or implementation phases
    const milestonesMatch = markdownContent.match(/## (Milestones|Implementation|Phases)\s*\n([\s\S]*?)(?=\n## |$)/i);
    if (milestonesMatch) {
      parsed.milestones_content = milestonesMatch[2].trim();
    } else {
      // Fallback: use the entire content for milestone analysis
      parsed.milestones_content = markdownContent;
    }

  } catch (error) {
    console.error('Error parsing design document markdown:', error);
  }

  return parsed;
}

// Helper function to generate milestones from parsed content
async function generateMilestonesFromParsedContent(job: DesignDocJob, parsedContent: any, jobNamespace: string): Promise<any[]> {
  const detailedMilestones = [];
  
  // Generate a cleaner design document identifier for branch naming
  const designDocIdentifier = job.task_title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 50); // Limit length for practical Git usage

  try {
    const milestonesPrompt = `
      Based on the design document content below, generate 3-5 logical milestones for implementation.
      Each milestone should be independently implementable and testable.

      Design Document Content:
      ${JSON.stringify(parsedContent, null, 2)}

      Output Format (JSON only):
      {
        "milestones": [
          {
            "title": "string - Clear milestone title",
            "description": "string - Detailed description",
            "technical_focus": "string - Main technical area",
            "key_deliverables": ["deliverable1", "deliverable2"]
          }
        ]
      }
    `;

    const milestonesResult = await callClaudeLlmForJson(milestonesPrompt);
    const generatedMilestones = milestonesResult.milestones || [];

    for (let i = 0; i < generatedMilestones.length; i++) {
      const milestone = generatedMilestones[i];
      const milestoneId = `M${i + 1}.0`;
      const milestoneNumber = i + 1;
      
      // Generate proper branch name
      const shortDescription = milestone.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
        .split('-')
        .slice(0, 3) // Take first 3 words
        .join('-');
      
      const milestoneBranchName = `feature/${designDocIdentifier}/milestone-${milestoneNumber}-${shortDescription}`;

      const searchQueriesPrompt = `
        For the milestone: "${milestone.title}"
        Description: "${milestone.description}"
        Technical Focus: "${milestone.technical_focus}"
        Key Deliverables: ${milestone.key_deliverables?.join(', ') || 'None specified'}

        Generate 3-5 specific search queries to find relevant architectural decisions and patterns.
        Output Format (JSON only):
        {
          "search_queries": ["query1", "query2", "query3"]
        }
      `;

      const searchQueriesResult = await callClaudeLlmForJson(searchQueriesPrompt);
      const searchQueries = searchQueriesResult.search_queries || [];
      const applicableDeveloperGuidanceAndBestPractices = [];
      const uniqueRetrievedDecisionIds = new Set<string>();

      for (const query of searchQueries) {
        if (!query || typeof query !== 'string') continue;
        
        console.log(`[Job ${job.id}] Phase 4 - Milestone ${milestoneId}: Searching with "${query}" in namespace ${jobNamespace}`);
        const pineconeResults = await queryPinecone(query, jobNamespace, 3, 0.4);
        
        for (const match of pineconeResults) {
          if (match.id && !uniqueRetrievedDecisionIds.has(match.id)) {
            uniqueRetrievedDecisionIds.add(match.id);
            applicableDeveloperGuidanceAndBestPractices.push({
              retrieved_decision_id: match.id,
              retrieved_decision_title: match.metadata?.title || 'Untitled Decision',
              guidance_summary: match.metadata?.dev_prompt || match.metadata?.follows_standard_practice_reason || match.metadata?.description?.substring(0,200) || 'No specific guidance summary.',
              relevance_to_milestone_tasks: `Retrieved with query: "${query}" for milestone: ${milestone.title}`,
              related_files: match.metadata?.related_files || [],
              retrieval_confidence_score: match.score,
            });
          }
        }
      }

      detailedMilestones.push({
        milestone_id: milestoneId,
        title: milestone.title,
        description: milestone.description,
        priority: i === 0 ? 'P0-Critical' : 'P1-High',
        technical_complexity: 'Medium',
        key_tasks_and_deliverables: milestone.key_deliverables || [`Implement: ${milestone.title}`],
        verification_criteria: [`Completion of all tasks in ${milestone.title}`, 'Code review passed', 'All tests green'],
        depends_on_milestones: i === 0 ? ['None'] : [`M${i}.0`],
        git_must_use_milestone_branch_name: milestoneBranchName,
        branch_naming_rationale: `Follows AI Agent Protocol convention: feature/${designDocIdentifier}/milestone-${milestoneNumber}-${shortDescription}. Descriptive yet Git-friendly.`,
        relevant_arch_choices_titles: [milestone.technical_focus],
        derived_domain_concepts_for_guidance_search: searchQueries,
        applicable_developer_guidance_and_best_practices: applicableDeveloperGuidanceAndBestPractices,
        notes_for_branch_integration: `Feature flag: ${job.task_title.toLowerCase().replace(/\s+/g, '_')}_milestone_${i+1}. Consider API versioning if external contracts change.`,
        git_integration_notes: `Create PR from ${milestoneBranchName} to feature/${designDocIdentifier}, then integrate milestone to main branch upon completion.`
      });
    }
  } catch (error) {
    console.error(`[Job ${job.id}] Error generating milestones from parsed content:`, error);
    
    // Fallback: create a single milestone with proper branch naming
    const designDocIdentifier = job.task_title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 50);
    
    const fallbackBranchName = `feature/${designDocIdentifier}/milestone-1-implement`;
    
    detailedMilestones.push({
      milestone_id: 'M1.0',
      title: `Implement ${job.task_title}`,
      description: `Complete implementation of ${job.task_title} as described in the design document`,
      priority: 'P0-Critical',
      technical_complexity: 'Medium',
      key_tasks_and_deliverables: [`Implement core functionality for ${job.task_title}`],
      verification_criteria: ['All requirements implemented', 'Code review passed', 'All tests green'],
      depends_on_milestones: ['None'],
      milestone_branch_name: fallbackBranchName,
      branch_naming_rationale: `Fallback branch naming following AI Agent Protocol convention.`,
      relevant_arch_choices_titles: [],
      derived_domain_concepts_for_guidance_search: [],
      applicable_developer_guidance_and_best_practices: [],
      notes_for_branch_integration: `Feature flag: ${job.task_title.toLowerCase().replace(/\s+/g, '_')}_milestone_1.`,
      git_integration_notes: `Create PR from ${fallbackBranchName} to feature/${designDocIdentifier}, then integrate milestone to main branch upon completion.`
    });
  }

  return detailedMilestones;
}

// Legacy function for backward compatibility
async function generateImplementationPlanFromStructuredData(job: DesignDocJob, goals: any[], non_goals: any[], core_architectural_choices: any[], milestones_sketch: string, success_metrics: any[]): Promise<any> {
  const { task_title, installation_id, repository_slug } = job;
  
  // Generate a cleaner design document identifier for branch naming
  const designDocIdentifier = task_title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 50); // Limit length for practical Git usage
  
  const dataModelContextForP4 = job.phase3_5_refined_data_models_output 
    ? JSON.stringify(job.phase3_5_refined_data_models_output.refined_data_models.models.map(m => ({name: m.model_name, status: m.status, desc: m.description})), null, 2)
    : job.phase3_output?.high_level_design?.data_model_changes || "Data model details not available post-refinement.";

  const phase3ReferencedDecisionsSummary = job.phase3_output?.referenced_decisions || [];
  const implementationPlanTitle = `Implementation Plan for: ${task_title}`;
  const designDocumentReferenceTitle = task_title;

  const keyArchitecturalChoicesSummary = core_architectural_choices.map((choice: { title: string; recommended_approach_description: string }) => ({
    decision_title: choice.title,
    recommended_approach: choice.recommended_approach_description,
  }));

  const globalGuidanceFromInitialDesignContext = phase3ReferencedDecisionsSummary.map((dec: any) => ({
    guidance_title: dec.title || 'Untitled Decision', 
    source_decision_ids: [dec.id], 
    summary_of_practice: dec.summary_of_relevance_in_this_design || 'No specific guidance summary available from design doc.',
    dev_prompt: dec.dev_prompt,
    follows_standard_practice_reason: dec.follows_standard_practice_reason,
    related_files: dec.related_files
  }));
  
  const detailedMilestones = [];
  const sketchEntries = milestones_sketch?.split(/Phase \d+:|Milestone \d+:|Step \d+:|\n- /i).filter((s:string) => s.trim() !== '') || [`Default Milestone: Implement ${task_title}`];
  const jobNamespace = getRepositoryNamespace(installation_id || 0, repository_slug);

  for (let i = 0; i < sketchEntries.length; i++) {
    const entryDescription = sketchEntries[i].trim();
    const milestoneId = `M${i + 1}.0`;
    const milestoneNumber = i + 1;
    const milestoneTitle = `Milestone ${milestoneNumber}: ${entryDescription.substring(0, 50)}${entryDescription.length > 50 ? '...' : ''}`;
    
    // Generate proper branch name
    const shortDescription = entryDescription
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .split('-')
      .slice(0, 3) // Take first 3 words
      .join('-');
    
    const milestoneBranchName = `feature/${designDocIdentifier}/milestone-${milestoneNumber}-${shortDescription}`;
    
    const keyTasksAndDeliverables = [`Implement core aspects of: ${entryDescription}`];
    const relevantArchChoicesTitles = Array.isArray(core_architectural_choices) ? core_architectural_choices.slice(0,2).map((c:any) => c.title) : [];
    const dataModelContextSnippet = dataModelContextForP4.substring(0, 300) + (dataModelContextForP4.length > 300 ? '...' : '');

    const llmPromptForSearchQueries = `
      You are an expert software architect assisting in breaking down a project into actionable milestones. 
      For the upcoming milestone detailed below, your task is to identify the core technical challenges 
      and the types of solutions or patterns that will likely be needed. Then, generate specific search 
      query phrases to find relevant existing knowledge (architectural decisions, best practices, code examples, UI patterns, data model patterns, etc.) 
      in our Pinecone knowledge base.

      Milestone Details:
      - Title: "${milestoneTitle}"
      - Description: "${entryDescription}"
      - Key Tasks & Deliverables:
          ${keyTasksAndDeliverables.map(task => `- ${task}`).join('\n')}
      - Relevant Core Architectural Choices Already Made:
          ${relevantArchChoicesTitles.map(choice => `- ${choice}`).join('\n')}
      - Relevant Data Model Context (summary): ${dataModelContextSnippet}

      Instructions for LLM:
      1. Deeply analyze the tasks, architectural context, and data model context.
      2. Identify specific areas where established patterns or guidance would be beneficial. For example:
          *   If UI work is mentioned, are there needs for specific UI patterns (e.g., 'UI pattern breadcrumbs', 'UI pattern complex navigation tabs')?
          *   If backend work is mentioned, are there needs for data processing patterns (e.g., 'Node.js background processing large file import')?
          *   If data modeling tasks are implied, queries for 'data model user profile extension' or 'schema migration strategy'.
      3. Generate 3-5 targeted search query phrases (typically 2-6 words each). These should be precise enough to retrieve specific, actionable guidance.

      Output Format (JSON only):
      {
        "milestone_specific_guidance_search_queries": [
          "string - Query 1",
          "string - Query 2"
        ]
      }
    `;
    
    const searchQueriesResult = await callClaudeLlmForJson(llmPromptForSearchQueries);
    const milestoneSpecificGuidanceSearchQueries = searchQueriesResult.milestone_specific_guidance_search_queries || [];

    const applicableDeveloperGuidanceAndBestPractices = [];
    const uniqueRetrievedDecisionIds = new Set<string>();

    if (milestoneSpecificGuidanceSearchQueries.length > 0) {
      for (const sQuery of milestoneSpecificGuidanceSearchQueries) {
        if (!sQuery || String(sQuery).trim() === "") continue;
        console.log(`[Job ${job.id}] Phase 4 - Milestone ${milestoneId}: Querying Pinecone with "${String(sQuery).substring(0,100)}..." in namespace ${jobNamespace}`);
        const pineconeResults = await queryPinecone(String(sQuery), jobNamespace, 3, 0.4); 
        
        for (const match of pineconeResults) {
          if (match.id && !uniqueRetrievedDecisionIds.has(match.id)) {
            uniqueRetrievedDecisionIds.add(match.id);
            applicableDeveloperGuidanceAndBestPractices.push({
              retrieved_decision_id: match.id,
              retrieved_decision_title: match.metadata?.title || 'Untitled Decision',
              guidance_summary: match.metadata?.dev_prompt || match.metadata?.follows_standard_practice_reason || match.metadata?.description?.substring(0,200) || 'No specific guidance summary.',
              relevance_to_milestone_tasks: `Retrieved with query: "${sQuery}" for tasks related to ${entryDescription.substring(0,30)}...`,
              related_files: match.metadata?.related_files || [],
              retrieval_confidence_score: match.score,
            });
          }
        }
      }
    }

    detailedMilestones.push({
      milestone_id: milestoneId,
      title: milestoneTitle,
      description: entryDescription,
      priority: i === 0 ? 'P0-Critical' : 'P1-High', 
      technical_complexity: 'Medium', 
      key_tasks_and_deliverables: keyTasksAndDeliverables,
      verification_criteria: [`Completion of all tasks in ${milestoneTitle}`, 'Code review passed', 'All tests green'],
      depends_on_milestones: i === 0 ? ['None'] : [`M${i}.0`],
      git_must_use_milestone_branch_name: milestoneBranchName,
      branch_naming_rationale: `Follows AI Agent Protocol convention: feature/${designDocIdentifier}/milestone-${milestoneNumber}-${shortDescription}. Descriptive yet Git-friendly.`,
      relevant_arch_choices_titles: relevantArchChoicesTitles,
      derived_domain_concepts_for_guidance_search: milestoneSpecificGuidanceSearchQueries, 
      applicable_developer_guidance_and_best_practices: applicableDeveloperGuidanceAndBestPractices,
      notes_for_branch_integration: `Feature flag: ${task_title.toLowerCase().replace(/\s+/g, '_')}_milestone_${i+1}. Consider API versioning if external contracts change.`,
      git_integration_notes: `Create PR from ${milestoneBranchName} to feature/${designDocIdentifier}, then integrate milestone to main branch upon completion.`
    });
  }

  return {
    implementation_plan_title: implementationPlanTitle,
    version: "1.0",
    design_document_reference_title: designDocumentReferenceTitle,
    overall_project_goals: goals,
    overall_project_non_goals: non_goals,
    key_architectural_choices_summary: keyArchitecturalChoicesSummary,
    main_feature_branch_name: `feature/${designDocIdentifier}`,
    git_workflow_guidance: {
      primary_branch: "main",
      main_feature_branch: `feature/${designDocIdentifier}`,
      integration_strategy: "Each milestone is developed on its own branch, merged to main feature branch, then integrated to main upon completion",
      pr_naming_convention: "Feat(Milestone N): <Milestone Title>",
      commit_message_format: "feat/fix/test: <clear description> (follows Conventional Commits)",
      branch_cleanup_strategy: "Delete milestone branches after merging, delete main feature branch after final integration"
    },
    global_guidance_from_initial_design_context: globalGuidanceFromInitialDesignContext,
    milestones: detailedMilestones,
    final_success_metrics_recap: success_metrics,
    refined_data_model_summary: job.phase3_5_refined_data_models_output 
        ? job.phase3_5_refined_data_models_output.refined_data_models.summary_of_compliance_approach
        : "Data model refinement phase was not run or output not available.",
    finalized_design_doc_content_used: false, // Flag to indicate we used legacy structured data
    ai_agent_protocol_integration: {
      protocol_version: "1.0",
      follows_milestone_driven_approach: true,
      branch_naming_enforced: true,
      atomic_commits_required: true,
      independent_milestone_integration: true
    }
  };
}

export async function processDesignDocJob(jobId: string): Promise<void> {
  initializeClients();
  let job: DesignDocJob | null = null;
  try {
    console.log(`[Job ${jobId}] Starting Design Doc Job Processor`);
    const { data, error: fetchError } = await supabaseAdmin!
      .from('design_doc_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (fetchError || !data) {
      console.error(`[Job ${jobId}] Critical error fetching job:`, fetchError);
      throw new Error(`Failed to fetch job ${jobId}: ${fetchError?.message || 'Job not found'}`);
    }
    job = data as DesignDocJob;
    console.log(`[Job ${jobId}] Fetched job successfully. Current status: ${job.status}, phase: ${job.current_phase}`);

    if (job.status === 'completed' || job.status === 'error') {
      console.log(`[Job ${jobId}] Job already completed or in error state. Skipping processing.`);
      return;
    }

    if (job.status === 'pending') { 
        await updateJob(job.id, { status: 'processing' }); 
        job.status = 'processing'; 
    }

    // Chain of responsibility for processing phases
    if (job.current_phase.startsWith('phase0') || job.current_phase === 'pending' || job.current_phase.startsWith('phase1')) {
      const phase1Result = await processPhase1(job);
      await updateJob(job.id, {
        phase1_output: phase1Result,
        current_phase: 'phase1_goals_decisions_complete', 
        status: 'pending'
      });
      console.log(`[Job ${jobId}] Phase 1 complete. Job set to pending for Phase 2.`);
      job = { ...job, phase1_output: phase1Result, current_phase: 'phase1_goals_decisions_complete', status: 'pending' };
    }
    
    if (job.current_phase === 'phase1_goals_decisions_complete' || 
             job.current_phase === 'phase2_pending' || 
             job.current_phase === 'phase2_decision_context_inprogress') {
      if (!job.phase1_output) {
        console.error(`[Job ${jobId}] Critical: phase1_output is missing for Phase 2. Refetching job.`);
        const { data: refetchedJobData, error: refetchError } = await supabaseAdmin!.from('design_doc_jobs').select('*').eq('id', jobId).single();
        if (refetchError || !refetchedJobData) throw new Error(`Failed to refetch job ${jobId} for Phase 2.`);
        job = refetchedJobData as DesignDocJob;
        if (!job.phase1_output) throw new Error(`Refetched job ${jobId} still missing phase1_output for Phase 2.`);
      }
      const phase2Result = await processPhase2(job, job.phase1_output as DesignDocJob['phase1_output']);
      await updateJob(job.id, {
        phase2_output: phase2Result,
        current_phase: 'phase2_decision_context_complete', 
        status: 'pending'
      });
      console.log(`[Job ${jobId}] Phase 2 complete. Job set to pending for Phase 3.`);
      job = { ...job, phase2_output: phase2Result, current_phase: 'phase2_decision_context_complete', status: 'pending' };
    }
    
    if (job.current_phase === 'phase2_decision_context_complete' || 
             job.current_phase === 'phase3_pending' || 
             job.current_phase === 'phase3_doc_generation_inprogress') {
      if (!job.phase1_output || !job.phase2_output) {
        console.error(`[Job ${jobId}] Critical: phase1_output or phase2_output is missing for Phase 3. Refetching job.`);
        const { data: refetchedJobData, error: refetchError } = await supabaseAdmin.from('design_doc_jobs').select('*').eq('id', jobId).single();
        if (refetchError || !refetchedJobData) throw new Error(`Failed to refetch job ${jobId} for Phase 3.`);
        job = refetchedJobData as DesignDocJob;
        if (!job.phase1_output || !job.phase2_output) throw new Error(`Refetched job ${jobId} still missing phase outputs for Phase 3.`);
      }
      const phase3Result = await processPhase3(job, job.phase1_output as DesignDocJob['phase1_output'], job.phase2_output);
      await updateJob(job.id, {
        phase3_output: phase3Result,
        current_phase: 'phase3_doc_generation_complete',
        status: 'pending'
      });
      console.log(`[Job ${jobId}] Phase 3 (Initial Draft) complete. Job set to pending for Phase 3.5 (Data Model Refinement).`);
      job = { ...job, phase3_output: phase3Result, current_phase: 'phase3_doc_generation_complete', status: 'pending' };
    }
    
    if (job.current_phase === 'phase3_doc_generation_complete' ||
             job.current_phase === 'phase3_5_datamodel_refinement_pending' ||
             job.current_phase === 'phase3_5_datamodel_refinement_inprogress') {
        if (!job.phase3_output || !job.referenced_decisions) {
            console.error(`[Job ${jobId}] Critical: phase3_output or referenced_decisions missing for Phase 3.5. Refetching job.`);
            const { data: refetchedJobData, error: refetchError } = await supabaseAdmin!.from('design_doc_jobs').select('*').eq('id', jobId).single();
            if (refetchError || !refetchedJobData) throw new Error(`Failed to refetch job ${jobId} for Phase 3.5 after missing phase3_output/referenced_decisions.`);
            job = refetchedJobData as DesignDocJob;
            if (!job.phase3_output || !job.referenced_decisions) throw new Error(`Refetched job ${jobId} still missing phase3_output/referenced_decisions for Phase 3.5.`);
        }
     const phase3_5_Result = await processPhase3_5_DataModelRefinement(job);
     
     await updateJob(job.id, {
       phase3_5_refined_data_models_output: phase3_5_Result,
       current_phase: 'phase3_5_datamodel_refinement_complete',
       status: 'completed' 
     });
     console.log(`[Job ${jobId}] Phase 3.5 (Data Model Refinement) complete. Job marked as completed (ready for output generation or P4).`);
     job = { ...job, phase3_5_refined_data_models_output: phase3_5_Result, current_phase: 'phase3_5_datamodel_refinement_complete', status: 'completed' };
    }
    
    if (job.current_phase === 'phase3_5_datamodel_refinement_complete' || 
        job.current_phase === 'phase4_pending' || 
        job.current_phase === 'phase4_inprogress') {
        if (!job.phase1_output || !job.phase2_output || !job.phase3_output || !job.phase3_5_refined_data_models_output) {
            console.error(`[Job ${jobId}] Critical: Missing previous phase outputs for Phase 4. Refetching job.`);
            const { data: refetchedJobData, error: refetchError } = await supabaseAdmin.from('design_doc_jobs').select('*').eq('id', jobId).single();
            if (refetchError || !refetchedJobData) throw new Error(`Failed to refetch job ${jobId} for Phase 4.`);
            job = refetchedJobData as DesignDocJob;
            if (!job.phase1_output || !job.phase2_output || !job.phase3_output || !job.phase3_5_refined_data_models_output) {
                throw new Error(`Refetched job ${jobId} still missing required phase outputs for Phase 4.`);
            }
        }
      const phase4Output = await processPhase4(job);
      const outputFilePath = `design-docs/${job.repository_slug}/${job.id}.md`;
      
      await updateJob(job.id, {
        phase4_output: phase4Output,
        current_phase: 'phase4_complete',
        status: 'completed',
        output_file_path: outputFilePath
      });
      console.log(`[Job ${jobId}] Phase 4 complete. Job marked as completed (ready for output generation).`);
      job = { ...job, phase4_output: phase4Output, current_phase: 'phase4_complete', status: 'completed' };
    }
    
    else {
      console.log(`[Job ${jobId}] Job is in an unknown or unexpected phase: ${job.current_phase}. Status: ${job.status}. Skipping processing for this cycle.`);
    }

  } catch (error: any) {
    console.error(`[Job ${jobId}] Unhandled error in job processor:`, error);
    if (job) {
      await updateJob(job.id, {
        status: 'error',
        error_message: error.message,
      }).catch(updateErr => {
        console.error(`[Job ${jobId}] FATAL: Could not even update job status to error:`, updateErr);
      });
    }
  }
} 