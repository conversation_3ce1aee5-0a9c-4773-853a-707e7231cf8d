import { Pinecone, Index } from '@pinecone-database/pinecone';
import OpenAI from 'openai';

const pineconeApiKey = process.env.PINECONE_API_KEY;
const pineconeIndexName = process.env.PINECONE_INDEX_NAME || 'architecture-decisions';
const openaiApiKey = process.env.OPENAI_API_KEY;

if (!pineconeApiKey || !openaiApiKey) {
  throw new Error('Pinecone API Key or OpenAI API Key not configured for pineconeUtils');
}

const pinecone = new Pinecone({ apiKey: pineconeApiKey });
const openai = new OpenAI({ apiKey: openaiApiKey });
const pcIndexStore: Record<string, Index> = {}; // Cache for index handles

async function getPcIndex(namespace: string): Promise<Index> {
  if (!pcIndexStore[namespace]) {
    const index = pinecone.Index(pineconeIndexName);
    // Note: Pinecone client handles namespace per operation, not at Index level globally for v2+ client
    // So, we store the generic index handle and apply namespace in each call.
    // However, to keep calls simpler, we can wrap it if we decide a specific index object per namespace is cleaner.
    // For now, this structure assumes namespace is passed to query/fetch.
    pcIndexStore[namespace] = index; // This might just be `pinecone.Index(pineconeIndexName)` if namespace is always dynamic
    console.log(`[PineconeUtils] Pinecone index handle for generic index '${pineconeIndexName}' cached (namespace to be applied per call).`);
  }
  return pcIndexStore[namespace];
}

async function generateEmbedding(text: string, model = "text-embedding-3-small"): Promise<number[]> {
  try {
    const response = await openai.embeddings.create({
      model: model,
      input: text.replace(/\n/g, ' '),
      encoding_format: "float",
    });
    return response.data[0].embedding;
  } catch (error) {
    console.error('[PineconeUtils] Error generating OpenAI embedding:', error);
    throw error;
  }
}

/**
 * Queries a Pinecone index namespace.
 * @param queryText Text to generate embedding for the query.
 * @param namespace The namespace to query.
 * @param topK Number of results to return.
 * @param minScore Minimum similarity score.
 * @param filter Optional filter object for metadata filtering
 * @returns Array of matching results { id, score, metadata }.
 */
export async function queryPinecone(
  queryText: string,
  namespace: string,
  topK = 5,
  minScore = 0.7,
  filter?: Record<string, any>
): Promise<Array<{ id: string; score: number; metadata: any }>> {
  if (!queryText.trim()) {
    console.warn('[PineconeUtils] Empty queryText for Pinecone query. Returning empty array.');
    return [];
  }
  console.log(`[PineconeUtils] Querying namespace '${namespace}' for "${queryText.substring(0,50)}..." (topK: ${topK}, minScore: ${minScore})`);
  try {
    const index = await getPcIndex(namespace); // Gets the generic index, namespace applied below
    const queryEmbedding = await generateEmbedding(queryText);

    const queryOptions: any = {
      vector: queryEmbedding,
      topK: topK,
      includeMetadata: true,
      includeValues: false,
    };

    if (filter !== undefined) {
      queryOptions.filter = filter;
      console.log(`[PineconeUtils] Applying provided server-side metadata filter:`, filter);
    } else {
      queryOptions.filter = { 'is_superseded': false };
      console.log(`[PineconeUtils] Applying default server-side metadata filter: { 'is_superseded': false }`);
    }

    const queryResponse = await index.namespace(namespace).query(queryOptions);

    const matches = queryResponse.matches || [];
    const filteredMatches = matches.filter(match => match.score && match.score >= minScore);
    
    console.log(`[PineconeUtils] Found ${matches.length} raw matches, ${filteredMatches.length} after score filtering for namespace '${namespace}'.`);
    return filteredMatches.map(match => ({
      id: match.id,
      score: match.score || 0,
      metadata: match.metadata || {},
    }));
  } catch (error) {
    console.error(`[PineconeUtils] Error querying Pinecone namespace '${namespace}':`, error);
    return []; // Return empty on error to avoid breaking flows
  }
}

/**
 * Fetches a single vector (with metadata) by ID from a Pinecone namespace.
 * @param id The ID of the vector to fetch.
 * @param namespace The namespace to fetch from.
 * @returns The vector object { id, metadata, values (optional) } or null if not found.
 */
export async function getVectorById(id: string, namespace: string): Promise<any | null> {
  console.log(`[PineconeUtils] Fetching vector ID '${id}' from namespace '${namespace}'.`);
  try {
    const index = await getPcIndex(namespace);
    const fetchResponse = await index.namespace(namespace).fetch([id]);
    if (fetchResponse.records && fetchResponse.records[id]) {
      return fetchResponse.records[id];
    }
    return null;
  } catch (error) {
    console.error(`[PineconeUtils] Error fetching vector ID '${id}' from namespace '${namespace}':`, error);
    return null;
  }
}

/**
 * Fetches multiple vectors (with metadata) by their IDs from a Pinecone namespace.
 * @param ids Array of vector IDs to fetch.
 * @param namespace The namespace to fetch from.
 * @returns Array of vector objects. Returns empty array if errors or no vectors found.
 */
export async function getVectorsByIds(ids: string[], namespace: string): Promise<any[]> {
  if (!ids || ids.length === 0) {
    return [];
  }
  console.log(`[PineconeUtils] Fetching ${ids.length} vector IDs from namespace '${namespace}'. IDs: ${ids.join(', ')}`);
  try {
    const index = await getPcIndex(namespace);
    const fetchResponse = await index.namespace(namespace).fetch(ids);
    // Convert records object into an array
    return ids.map(id => fetchResponse.records?.[id]).filter(record => record !== undefined);
  } catch (error) {
    console.error(`[PineconeUtils] Error fetching vectors by IDs from namespace '${namespace}':`, error);
    return [];
  }
} 