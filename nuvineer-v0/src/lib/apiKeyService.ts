import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// Initialize Supabase client
// Ensure environment variables are set in your .env.local or environment
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Use Service Role Key for backend operations

if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Supabase URL or Service Role Key is not defined in environment variables.');
}

// Create a separate client instance for server-side operations
// IMPORTANT: Never expose the service_role key client-side
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
        // It's generally recommended to disable auto-refresh and persistence for server-side clients
        autoRefreshToken: false,
        persistSession: false
    }
});

const SALT_ROUNDS = 10;
const API_KEY_PREFIX = 'ak_'; // Prefix for generated keys

// Interface for the structure in the 'api_keys' table (adjust as needed)
interface ApiKeyRecord {
  id: string; // UUID
  user_id: string; // UUID linking to auth.users
  hashed_key: string;
  key_prefix: string; // e.g., "ak_abc123"
  created_at: string;
  last_used_at?: string | null;
  name?: string | null; // Optional user-defined name
}

// Interface for the data returned when listing keys (non-sensitive)
export interface ApiKeyInfo {
  id: string;
  prefix: string;
  createdAt: string;
  lastUsedAt?: string | null;
  name?: string | null;
}

// Interface for the data returned upon key generation (includes the raw key)
export interface NewApiKey extends ApiKeyInfo {
  key: string; // The full, unhashed key (only returned once)
}


/**
 * Generates a new API key for a user, hashes it, stores it, and returns the original key once.
 * @param userId The ID of the user creating the key.
 * @param keyName Optional name for the key.
 * @returns The newly generated key details including the unhashed key.
 */
export async function generateApiKey(userId: string, keyName?: string): Promise<NewApiKey> {
  if (!userId) {
    throw new Error('User ID is required to generate an API key.');
  }

  const rawKey = `${API_KEY_PREFIX}${uuidv4().replace(/-/g, '')}`; // Generate a unique key string
  const keyPrefix = rawKey.substring(0, API_KEY_PREFIX.length + 6); // e.g., ak_ seguido por 6 caracteres
  const hashedKey = await bcrypt.hash(rawKey, SALT_ROUNDS);
  const keyId = uuidv4(); // Generate a UUID for the database record ID

  const { data, error } = await supabaseAdmin
    .from('api_keys') // Ensure this table exists in your Supabase schema
    .insert({
      id: keyId,
      user_id: userId,
      hashed_key: hashedKey,
      key_prefix: keyPrefix,
      name: keyName,
      // created_at is handled by Supabase default value
    })
    .select('id, key_prefix, created_at, name') // Select the data to return
    .single();

  if (error) {
    console.error('Supabase error generating API key:', error);
    throw new Error('Failed to store API key.');
  }

  if (!data) {
      throw new Error('Failed to retrieve generated API key data after insert.');
  }


  return {
    id: data.id,
    prefix: data.key_prefix,
    createdAt: data.created_at,
    name: data.name,
    key: rawKey, // Return the unhashed key
  };
}

/**
 * Lists non-sensitive information about a user's API keys.
 * @param userId The ID of the user whose keys to list.
 * @returns An array of API key information.
 */
export async function listApiKeys(userId: string): Promise<ApiKeyInfo[]> {
   if (!userId) {
    throw new Error('User ID is required to list API keys.');
  }

  const { data, error } = await supabaseAdmin
    .from('api_keys')
    .select('id, key_prefix, created_at, last_used_at, name')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Supabase error listing API keys:', error);
    throw new Error('Failed to retrieve API keys.');
  }

  return data.map(key => ({
      id: key.id,
      prefix: key.key_prefix,
      createdAt: key.created_at,
      lastUsedAt: key.last_used_at,
      name: key.name,
  }));
}

/**
 * Revokes (deletes) an API key, ensuring the user owns it.
 * @param userId The ID of the user attempting to revoke the key.
 * @param keyId The ID of the API key record to revoke.
 */
export async function revokeApiKey(userId: string, keyId: string): Promise<void> {
   if (!userId || !keyId) {
    throw new Error('User ID and Key ID are required to revoke an API key.');
  }

  const { error } = await supabaseAdmin
    .from('api_keys')
    .delete()
    .match({ id: keyId, user_id: userId }); // Match both ID and user ID for security

  if (error) {
    console.error('Supabase error revoking API key:', error);
    // Don't necessarily throw if the key wasn't found, could be already deleted
    if (error.code !== 'PGRST116') { // PGRST116 = Row not found, adjust if needed
         throw new Error('Failed to revoke API key.');
    } else {
        console.log(`API Key ${keyId} not found for user ${userId} during revoke, possibly already deleted.`);
    }
  }
  // No return value needed on success
}


/**
 * Validates a provided raw API key against stored hashes.
 * Updates the last_used_at timestamp on successful validation.
 * @param rawKey The full, unhashed API key provided by the client.
 * @returns The user_id associated with the valid key, or null if invalid.
 */
export async function validateApiKeyAndGetUser(rawKey: string): Promise<string | null> {
  if (!rawKey || !rawKey.startsWith(API_KEY_PREFIX)) {
    return null; // Invalid format
  }

  const keyPrefix = rawKey.substring(0, API_KEY_PREFIX.length + 6);

  // Find potential matching keys based on the prefix
  const { data: potentialKeys, error: fetchError } = await supabaseAdmin
    .from('api_keys')
    .select('id, user_id, hashed_key')
    .eq('key_prefix', keyPrefix);

  if (fetchError) {
    console.error('Supabase error fetching keys for validation:', fetchError);
    return null; // Or throw an error depending on desired behavior
  }

  if (!potentialKeys || potentialKeys.length === 0) {
    return null; // No key found with this prefix
  }

  // Iterate through potential matches and compare hashes
  for (const keyRecord of potentialKeys) {
    const isValid = await bcrypt.compare(rawKey, keyRecord.hashed_key);
    if (isValid) {
      // Key is valid, update last_used_at (fire-and-forget is okay here)
      supabaseAdmin
        .from('api_keys')
        .update({ last_used_at: new Date().toISOString() })
        .eq('id', keyRecord.id)
        .then(({ error: updateError }) => {
          if (updateError) {
            console.error('Supabase error updating last_used_at:', updateError);
          }
        });

      return keyRecord.user_id; // Return the user ID associated with the valid key
    }
  }

  return null; // No matching hash found
}

// You will need to create the 'api_keys' table in your Supabase dashboard
// with columns like: id (uuid, pk), user_id (uuid, fk to auth.users),
// hashed_key (text), key_prefix (text, index recommended),
// created_at (timestamptz, default now()), last_used_at (timestamptz, nullable),
// name (text, nullable).
// Ensure appropriate row-level security (RLS) policies if necessary,
// though using the service role key bypasses RLS by default. 