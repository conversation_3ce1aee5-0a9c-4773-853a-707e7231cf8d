/**
 * Generates a Pinecone-safe namespace string from installation ID and repository slug.
 * Format: installation-<id>-repo-<owner>-<repo>
 * 
 * @param installationId - The GitHub App installation ID.
 * @param repositorySlug - The GitHub repository slug (e.g., 'owner/repo').
 * @returns A Pinecone-compatible namespace string (e.g., 'installation-12345-repo-owner-repo').
 * @throws If installationId or repositorySlug are invalid.
 */
export function getRepositoryNamespace(installationId: number | string, repositorySlug: string): string {
    const numericInstallationId = typeof installationId === 'string' ? parseInt(installationId, 10) : installationId;

    // Allow 0 for public repos, but not negative or NaN
    if (isNaN(numericInstallationId) || numericInstallationId < 0) {
        throw new Error(`Invalid installation ID provided: ${installationId}`);
    }
    if (!repositorySlug || typeof repositorySlug !== 'string' || !repositorySlug.includes('/')) {
        throw new Error(`Invalid repository slug provided: ${repositorySlug}`);
    }

    const [owner, repoName] = repositorySlug.split('/');
    if (!owner || !repoName) {
        throw new Error(`Invalid repository slug format: ${repositorySlug}. Expected 'owner/repoName'.`);
    }

    // Construct the namespace using the new format
    const namespace = `${numericInstallationId}-${owner.toLowerCase()}--${repoName.toLowerCase()}`;

    // Basic validation (Pinecone allows alphanumeric and hyphen)
    // Pinecone namespaces must be between 3 and 63 characters long,
    // and can only contain lowercase alphanumeric characters and hyphens.
    // Namespaces cannot start or end with a hyphen.
    const pineconeNamespaceRegex = /^[a-z0-9][a-z0-9-]{1,61}[a-z0-9]$/;
    if (!pineconeNamespaceRegex.test(namespace)) {
        console.warn(`Generated namespace "${namespace}" might contain characters disallowed by Pinecone or does not meet length/format requirements. Review sanitization if errors occur.`);
        // Consider adding more robust sanitization or throwing an error if strict compliance is needed.
        // For example, replacing invalid characters or truncating if too long, ensuring it doesn't start/end with hyphen.
    }
    
    // Example of more robust handling (optional, depending on strictness needed):
    // let saneNamespace = namespace.toLowerCase().replace(/[^a-z0-9-]/g, '-').substring(0, 63);
    // // Ensure it doesn't start or end with a hyphen after sanitization
    // saneNamespace = saneNamespace.replace(/^-+|-+$/g, ''); 
    // // If it became too short or empty after sanitization, handle appropriately
    // if (saneNamespace.length < 3) { /* throw error or use default */ }


    // Optional: Check length limit (Pinecone namespaces are typically 3-63 chars)
    if (namespace.length > 63 || namespace.length < 3) {
       console.warn(`Generated namespace "${namespace}" (length: ${namespace.length}) is outside Pinecone's recommended length (3-63 chars).`);
    }


    return namespace;
} 