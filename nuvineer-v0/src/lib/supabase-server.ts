import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';

// --- Server Component Client (for user session context) ---
export function createSupabaseServerClient() {
  const cookieStore = cookies();
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
             cookieStore.set({ name, value, ...options });
           } catch (error) {
              // Handle potential errors if running outside request context
              console.warn(`[SupabaseServer] Error setting cookie ${name}:`, error);
           }
        },
        remove(name: string, options: CookieOptions) {
           try {
             cookieStore.delete({ name, ...options });
           } catch (error) {
              console.warn(`[SupabaseServer] Error deleting cookie ${name}:`, error);
           }
        },
      },
    }
  );
}

// --- Admin Client (for elevated privileges using Service Role Key) ---

// Cache the admin client instance
let supabaseAdminClient: ReturnType<typeof createClient> | null = null;

export function createSupabaseAdminClient() {
  if (supabaseAdminClient) {
    return supabaseAdminClient;
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    throw new Error('Missing environment variable: NEXT_PUBLIC_SUPABASE_URL');
  }
  if (!supabaseServiceRoleKey) {
    throw new Error('Missing environment variable: SUPABASE_SERVICE_ROLE_KEY');
  }

  // Create a new client instance specifically for admin tasks
  supabaseAdminClient = createClient(supabaseUrl, supabaseServiceRoleKey, {
     auth: {
       // Prevent client from trying to use browser storage or cookies
       persistSession: false,
       autoRefreshToken: false,
       detectSessionInUrl: false
     }
  });
  
  console.log("[SupabaseServer] Supabase Admin Client initialized.");
  return supabaseAdminClient;
} 