import Anthropic from '@anthropic-ai/sdk';

const getEnvVar = (name: string, defaultValue: string | null = null): string | null => {
    return process.env[name] || defaultValue;
};

const CLAUDE_API_KEY = getEnvVar('ANTHROPIC_API_KEY');
const defaultClaudeModel = 'claude-sonnet-4-20250514';

/**
 * A shared function to call the Claude LLM.
 * Can be used from any Next.js API route.
 * @param {string} prompt - The prompt to send to the model.
 * @param {string} [model=defaultClaudeModel] - The model to use.
 * @param {boolean} [returnRawText=false] - Whether to return raw text or a parsed JSON object.
 * @returns {Promise<any>} - The response from the LLM.
 */
export async function callLLM(prompt: string, model: string = defaultClaudeModel, returnRawText: boolean = false): Promise<any> {
  if (!CLAUDE_API_KEY) {
    console.error("ANTHROPIC_API_KEY environment variable not set.");
    throw new Error("ANTHROPIC_API_KEY is not configured.");
  }

  const anthropic = new Anthropic({
    apiKey: CLAUDE_API_KEY,
  });

  try {
    const response = await anthropic.messages.create({
      model: model,
      max_tokens: 4096,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1,
    });

    if (response.content[0].type !== 'text') {
        throw new Error("Expected a text block in the LLM response.");
    }

    const llmResponse = response.content[0].text;

    if (returnRawText) {
      return llmResponse;
    }

    // Find the start and end of the JSON object
    const jsonStart = llmResponse.indexOf('{');
    const jsonEnd = llmResponse.lastIndexOf('}');

    if (jsonStart === -1 || jsonEnd === -1) {
      console.error("LLM response did not contain a valid JSON object.", llmResponse);
      throw new Error("LLM response is not valid JSON.");
    }

    const jsonString = llmResponse.substring(jsonStart, jsonEnd + 1);
    return JSON.parse(jsonString);

  } catch (error) {
    console.error("Error calling LLM:", error);
    throw error;
  }
} 