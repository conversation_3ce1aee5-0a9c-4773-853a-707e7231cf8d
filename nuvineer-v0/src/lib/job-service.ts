import { SupabaseClient, createClient } from '@supabase/supabase-js';

// Define these in your environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!; // Use service role for backend operations

// Initialize Supabase client. 
// Ensure you have a single instance or a way to get an instance where needed.
// For API routes, you might initialize it per request or use a shared instance.
let supabase: SupabaseClient;
if (supabaseUrl && supabaseServiceRoleKey) {
  supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      // In a service role context, you might not need autoRefreshToken and persistSession,
      // but it doesn't hurt for a general client setup.
      autoRefreshToken: true,
      persistSession: false,
      detectSessionInUrl: false
    }
  });
} else {
  console.warn("Supabase URL or Service Role Key is not defined. Job service will not connect to Supabase.");
  // Fallback or error handling if Supabase isn't configured
}

export type JobStatus = 'queued' | 'in_progress' | 'completed' | 'failed' | 'stopped';
export type AnalysisPhase = 'test-pr' | 'full-analysis' | 'relationship_analysis' | 'relationship_analysis_finalization' | 'populating_repository';

export interface AnalysisJobParams {
  repositorySlug: string;
  isPublic: boolean;
  installationId?: string;
  batchSize: number;
  sinceDate?: string | null;
  maxHistoricalPRs: number;
  testMode: boolean;
  // You might want to add userId if jobs are user-specific
  // userId?: string;
}

// Extending AnalysisJobParams for creation to optionally allow initial phase, status, message override
export interface CreateJobOptions extends AnalysisJobParams {
  initialPhase?: AnalysisPhase;
  initialStatus?: JobStatus;
  initialMessage?: string;
}

export interface Job {
  id: string; // UUID from the database
  params: AnalysisJobParams;
  status: JobStatus;
  phase: AnalysisPhase;
  progress: number;
  message: string;
  details: Array<{ pr_number?: number; status: string; decisions?: number; reason?: string; error?: string }>;
  error?: string | null;
  processed_count?: number;
  decisions_created_count?: number;
  no_decisions_count?: number;
  failed_count?: number;
  processed_nodes_for_relationships_count?: number;
  has_more?: boolean;
  created_at: string; // ISO timestamp string
  updated_at: string; // ISO timestamp string
}

// --- Public API for Job Service ---

export async function createSupabaseJob(params: CreateJobOptions): Promise<Job> {
  if (!supabase) throw new Error('Supabase client not initialized.');

  const phase = params.initialPhase || (params.testMode ? 'test-pr' : 'full-analysis');
  const status = params.initialStatus || 'queued';
  const message = params.initialMessage || `Analysis job for ${params.repositorySlug} is ${status}.`;

  // Destructure to separate CreateJobOptions from AnalysisJobParams for insertion
  const { initialPhase, initialStatus, initialMessage, ...jobParamsForInsert } = params;

  const { data, error } = await supabase
    .from('analysis_jobs')
    .insert({
      params: jobParamsForInsert as any, // Cast to any if Supabase struggles with complex JSONB types initially
      status: status,
      phase: phase,
      progress: 0,
      message: message,
      details: [],
      has_more: status !== 'failed' && status !== 'completed', // Default based on status
      // user_id: params.userId // if you add user_id
    })
    .select()
    .single();

  if (error) {
    console.error('[JobService] Error creating job in Supabase:', error);
    throw new Error(`Supabase error creating job: ${error.message}`);
  }
  if (!data) {
    throw new Error('Failed to create job in Supabase, no data returned.');
  }

  console.log(`[JobService] Job created in Supabase: ${data.id} for ${params.repositorySlug}, Phase: ${phase}, Status: ${status}`);
  return data as Job; // Cast to Job type
}

export async function getSupabaseJobStatus(jobId: string): Promise<Job | null> {
  if (!supabase) throw new Error('Supabase client not initialized.');

  const { data, error } = await supabase
    .from('analysis_jobs')
    .select('*')
    .eq('id', jobId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') { // PostgREST error for "Fetched N rows, expected 1"
        console.log(`[JobService] Job not found in Supabase: ${jobId}`);
        return null; // Job not found is not a throw-worthy error here
    }
    console.error(`[JobService] Error fetching job ${jobId} from Supabase:`, error);
    throw new Error(`Supabase error fetching job: ${error.message}`);
  }
  
  return data as Job | null;
}

export async function requestStopSupabaseJob(jobId: string): Promise<{ success: boolean; message: string; job?: Job | null }> {
  if (!supabase) throw new Error('Supabase client not initialized.');

  const currentJob = await getSupabaseJobStatus(jobId);
  if (!currentJob) {
    return { success: false, message: 'Job not found.' };
  }

  if (['completed', 'failed', 'stopped'].includes(currentJob.status)) {
    return { success: true, message: `Job already ${currentJob.status}.`, job: currentJob };
  }

  const { data, error } = await supabase
    .from('analysis_jobs')
    .update({
      status: 'stopped',
      message: 'Analysis stop requested by user.',
      has_more: false,
      updated_at: new Date().toISOString(),
    })
    .eq('id', jobId)
    .select()
    .single();

  if (error) {
    console.error(`[JobService] Error updating job ${jobId} to stopped in Supabase:`, error);
    throw new Error(`Supabase error stopping job: ${error.message}`);
  }

  console.log(`[JobService] Job ${jobId} stop requested in Supabase.`);
  return { success: true, message: 'Analysis job stop requested.', job: data as Job | null };
}

/**
 * Fetches the next available job for processing.
 * This would be called by the cron job handler.
 */
export async function getNextJobToProcess(): Promise<Job | null> {
  if (!supabase) throw new Error('Supabase client not initialized.');

  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
  let job: Job | null = null;
  let error;

  // 1. Try to get a 'queued' job first
  console.log("[JobService] Looking for 'queued' jobs...");
  ({ data: job, error } = await supabase
    .from('analysis_jobs')
    .select('*')
    .eq('status', 'queued')
    .order('created_at', { ascending: true })
    .limit(1)
    .single());

  if (error && error.code !== 'PGRST116') {
    console.error('[JobService] Error fetching queued job:', error);
  }

  if (job) {
    console.log(`[JobService] Found queued job ${job.id}. Attempting to claim...`);
    const { data: updatedJob, error: updateError } = await supabase
      .from('analysis_jobs')
      .update({ status: 'in_progress', updated_at: new Date().toISOString(), message: 'Processing started by worker.' })
      .eq('id', job.id)
      .eq('status', 'queued') // Optimistic lock
      .select()
      .single();
    
    if (updateError) {
        console.error(`[JobService] Error updating job ${job.id} to in_progress:`, updateError);
        return null; 
    }
    if (!updatedJob) {
        console.log(`[JobService] Queued job ${job.id} was likely picked up by another worker.`);
        return null;
    }
    console.log(`[JobService] Successfully claimed queued job ${updatedJob.id}.`);
    return updatedJob as Job;
  }

  // 2. If no 'queued' job, try to get an 'in_progress' job that explicitly has more work
  console.log("[JobService] No 'queued' jobs. Looking for 'in_progress' jobs with has_more=true...");
  ({ data: job, error } = await supabase
    .from('analysis_jobs')
    .select('*')
    .eq('status', 'in_progress')
    .eq('has_more', true)
    .order('updated_at', { ascending: true }) // Pick the one least recently updated among these active ones
    .limit(1)
    .single());

  if (error && error.code !== 'PGRST116') {
    console.error('[JobService] Error fetching in_progress (has_more=true) job:', error);
  }

  if (job) {
    console.log(`[JobService] Found active 'in_progress' job ${job.id} with has_more=true. Refreshing timestamp...`);
    // Just update its timestamp to mark it as actively picked up again for this chunk
    const { data: refreshedJob, error: refreshError } = await supabase
        .from('analysis_jobs')
        .update({ updated_at: new Date().toISOString(), message: 'Continuing processing.' })
        .eq('id', job.id)
        .select()
        .single();
    if (refreshError) {
        console.error(`[JobService] Error refreshing active in_progress job ${job.id}:`, refreshError);
        return null;
    }
    console.log(`[JobService] Successfully refreshed active in_progress job ${refreshedJob!.id}.`);
    return refreshedJob as Job;
  }

  // 3. If no such job, try to pick up a potentially STALLED 'in_progress' job (old updated_at)
  console.log("[JobService] No active 'in_progress' with has_more=true. Looking for STALLED 'in_progress' jobs...");
  ({ data: job, error } = await supabase
    .from('analysis_jobs')
    .select('*')
    .eq('status', 'in_progress')
    .lt('updated_at', fiveMinutesAgo) 
    .order('updated_at', { ascending: true })
    .limit(1)
    .single());

  if (error && error.code !== 'PGRST116') {
    console.error('[JobService] Error fetching stalled in_progress job:', error);
  }
  
  if (job) {
    console.log(`[JobService] Found STALLED 'in_progress' job ${job.id}. Attempting to resume...`);
    const { data: refreshedJob, error: refreshError } = await supabase
        .from('analysis_jobs')
        .update({ updated_at: new Date().toISOString(), message: 'Processing resumed for stalled job.' })
        .eq('id', job.id)
        .select()
        .single();
    if (refreshError) {
        console.error(`[JobService] Error refreshing stalled job ${job.id}:`, refreshError);
        return null;
    }
    console.log(`[JobService] Successfully resumed stalled in_progress job ${refreshedJob!.id}.`);
    return refreshedJob as Job;
  }

  console.log(`[JobService] No suitable job found to process in any category.`);
  return null; 
}

/**
 * Updates a job in Supabase.
 * This would be called by the processing logic within the cron job handler.
 */
export async function updateSupabaseJob(jobId: string, updates: Partial<Omit<Job, 'id' | 'params' | 'created_at' | 'phase'> & { phase?: AnalysisPhase }>): Promise<Job | null> {
  if (!supabase) throw new Error('Supabase client not initialized.');
  
  const updatePayload = {
      ...updates,
      updated_at: new Date().toISOString(),
  };

  const { data, error } = await supabase
    .from('analysis_jobs')
    .update(updatePayload)
    .eq('id', jobId)
    .select()
    .single();

  if (error) {
    console.error(`[JobService] Error updating job ${jobId} in Supabase:`, error);
    throw new Error(`Supabase error updating job: ${error.message}`);
  }
  if (!data) {
      // This might happen if the job was deleted or ID is wrong
      console.warn(`[JobService] Did not find job ${jobId} to update.`);
      return null;
  }

  // console.log(`[JobService] Job updated in Supabase: ${jobId}`);
  return data as Job;
}

console.log('[JobService] Supabase-backed job service initialized (or attempted).'); 