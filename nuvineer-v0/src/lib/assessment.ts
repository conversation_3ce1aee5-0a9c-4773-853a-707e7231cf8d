import Anthropic from '@anthropic-ai/sdk';
import { generateStrategicValueAssessmentPrompt } from '../analyzer/prompt';

export async function performStrategicAssessment(
  taskAnalysis: any, 
  projectConstitution: any, 
  taskDetails: any
) {
  // Validate required fields
  if (!taskAnalysis || !projectConstitution) {
    throw new Error('Task analysis and project constitution are required for assessment.');
  }

  // Check if this is an experimental feature
  const isExperimental = taskDetails?.isExperimental || false;
  
  // Log experimental feature assessment
  if (isExperimental) {
    console.log(`[Strategic Assessment] Performing experimental feature assessment for: "${taskDetails?.title}"`);
  }

  // TODO: Retrieve relevant context from past decisions if needed
  const retrievedContext: string[] = [];

  // Generate the prompt
  const prompt = generateStrategicValueAssessmentPrompt(
    taskAnalysis, 
    projectConstitution, 
    retrievedContext, 
    false, // isMinimalScope is not used in this flow directly
    isExperimental
  );

  // Create Anthropic client
  const anthropic = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
  });

  // Call Claude API
  const message = await anthropic.messages.create({
    model: 'claude-sonnet-4-20250514',
    max_tokens: 4000,
    temperature: 0.1,
    messages: [{ role: 'user', content: prompt }],
  });

  // Parse the response
  const responseText = message.content[0].type === 'text' ? message.content[0].text : '';
  
  let strategicAssessment;
  try {
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[0]);
      strategicAssessment = parsed.strategic_assessment;
    }
  } catch (parseError) {
    console.error('Error parsing Claude response:', parseError);
    throw new Error('Failed to parse AI response');
  }

  if (!strategicAssessment) {
    throw new Error('No strategic assessment found in response');
  }

  return strategicAssessment;
} 