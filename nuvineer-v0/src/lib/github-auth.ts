import { Octokit } from '@octokit/rest';
import jwt from 'jsonwebtoken';

// Token cache to avoid generating new tokens for each request
interface TokenCacheEntry {
  token: string;
  expiresAt: number; // Store expiration time (timestamp in seconds)
}

// Cache for installation tokens
const tokenCache: Record<string, TokenCacheEntry> = {};

/**
 * Format the private key correctly for JWT signing
 * GitHub provides the private key with literal '\n' characters that need to be replaced
 */
export function formatPrivateKey(key?: string): string {
  if (!key) return '';
  
  // Replace literal '\n' with actual newlines if needed
  let formattedKey = key.includes('\\n') ? key.replace(/\\n/g, '\n') : key;
  
  // Remove surrounding quotes if present (sometimes happens when copying from environment files)
  if (formattedKey.startsWith('"') && formattedKey.endsWith('"')) {
    formattedKey = formattedKey.slice(1, -1);
  }
  
  return formattedKey;
}

/**
 * Get the GitHub App private key from environment variables
 */
export function getGitHubAppPrivateKey(): string {
  const privateKey = process.env.GITHUB_APP_PRIVATE_KEY;
  
  if (!privateKey) {
    console.warn('[GitHub App] Private key not found in environment variables');
    return '';
  }
  
  const formattedKey = formatPrivateKey(privateKey);
  
  // Debug info for key format (without revealing the key contents)
  const startsWith = formattedKey.startsWith('-----BEGIN RSA PRIVATE KEY-----');
  const endsWith = formattedKey.endsWith('-----END RSA PRIVATE KEY-----');
  const keyLength = formattedKey.length;
  
  console.log(`[GitHub App] Private key format check: starts correctly: ${startsWith}, ends correctly: ${endsWith}, length: ${keyLength}`);
  
  if (!startsWith || !endsWith) {
    console.warn('[GitHub App] Private key appears to be malformed. Check for proper PEM format.');
  }
  
  return formattedKey;
}

/**
 * Generate a JWT for GitHub App authentication
 */
export function generateAppJWT(): string {
  const appId = process.env.GITHUB_APP_ID;
  if (!appId) {
    throw new Error('GitHub App ID not found in environment variables');
  }
  
  const privateKey = getGitHubAppPrivateKey();
  if (!privateKey) {
    throw new Error('GitHub App private key not found or invalid');
  }
  
  // JWT expiration: 10 minutes (GitHub maximum is 10 min)
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    iat: now,
    exp: now + 10 * 60, // 10 minutes
    iss: appId
  };
  
  try {
    const token = jwt.sign(payload, privateKey, { algorithm: 'RS256' });
    return token;
  } catch (error) {
    console.error('[GitHub App] Error generating JWT:', error);
    throw new Error(`Failed to generate JWT: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Get an installation access token for a specific GitHub App installation
 */
export async function getInstallationAccessToken(installationId: string): Promise<string> {
  if (!installationId || installationId === '0') {
    throw new Error('Invalid installation ID');
  }
  
  const cacheKey = `installation-${installationId}`;
  const now = Math.floor(Date.now() / 1000);
  
  // Check cache first (with 5-minute buffer before expiration)
  if (tokenCache[cacheKey] && tokenCache[cacheKey].expiresAt > now + 300) {
    console.log(`[GitHub App] Using cached token for installation ${installationId}`);
    return tokenCache[cacheKey].token;
  }
  
  try {
    // Generate app JWT for authenticating as the GitHub App
    const appJwt = generateAppJWT();
    
    // Create an Octokit instance authenticated as the GitHub App
    const appOctokit = new Octokit({
      auth: appJwt
    });
    
    // Request an installation token
    const response = await appOctokit.apps.createInstallationAccessToken({
      installation_id: parseInt(installationId, 10)
    });
    
    // Cache the token
    const token = response.data.token;
    const expiresAt = new Date(response.data.expires_at).getTime() / 1000;
    
    tokenCache[cacheKey] = { token, expiresAt };
    
    console.log(`[GitHub App] Generated new token for installation ${installationId}, expires at ${new Date(expiresAt * 1000).toISOString()}`);
    return token;
    
  } catch (error) {
    console.error(`[GitHub App] Error getting installation token for installation ${installationId}:`, error);
    throw new Error(`Failed to get installation token: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Get headers for direct GitHub API requests including proper authentication
 */
export function getHeaders(authToken?: string): Record<string, string> {
  const headers: Record<string, string> = { 
    'Accept': 'application/vnd.github.v3+json'
  };
  
  if (authToken) {
    headers['Authorization'] = `token ${authToken}`;
  } else if (process.env.GITHUB_TOKEN) {
    // Use GITHUB_TOKEN for public repositories
    headers['Authorization'] = `token ${process.env.GITHUB_TOKEN}`;
  }
  
  return headers;
}

/**
 * Get the appropriate Octokit instance based on repository access context
 * Handles both private (installation-based) and public (PAT-based) access
 */
export async function getOctokit(installationId?: string): Promise<Octokit> {
  console.log(`[getOctokit] Called with installationId: ${installationId || 'none'}`);

  // For public repos (or when no installation context is available), use a personal access token if available.
  if (!installationId || installationId === '0') {
    const pat = process.env.GITHUB_TOKEN;
    if (pat) {
      console.log('[getOctokit] Creating Octokit instance with GITHUB_TOKEN for public access.');
      return new Octokit({ auth: pat });
    } else {
      console.log('[getOctokit] Creating unauthenticated Octokit client for public access.');
      return new Octokit();
    }
  }

  // For private repos, get an installation-specific token.
  try {
    const installationToken = await getInstallationAccessToken(installationId);
    console.log(`[getOctokit] Creating Octokit instance with installation token for installation ${installationId}.`);
    return new Octokit({ auth: installationToken });
  } catch (error) {
    console.error(`[getOctokit] Failed to get installation token for ${installationId}. Falling back to unauthenticated.`, error);
    return new Octokit();
  }
}

/**
 * Unified function to get an authentication token for GitHub API calls
 * Works for both private repos (with installationId) and public repos
 */
export async function getAuthToken(installationId?: string): Promise<string | null> {
  if (installationId && installationId !== '0') {
    try {
      // Private repository: Use App installation token
      return await getInstallationAccessToken(installationId);
    } catch (error) {
      console.error(`[getAuthToken] Error getting installation token for ${installationId}:`, error);
      // Fall back to GITHUB_TOKEN if available
    }
  }
  
  // Public repository: Use GITHUB_TOKEN or null (unauthenticated)
  return process.env.GITHUB_TOKEN || null;
} 

/**
 * Checks the GitHub API rate limit status.
 * @param octokit An Octokit instance.
 * @param threshold The minimum number of remaining requests before triggering a pause. Defaults to 100.
 * @returns An object indicating if the rate limit is low.
 */
export async function checkRateLimit(octokit: Octokit, threshold = 100): Promise<{ isLimited: boolean; resetTime: Date; remaining: number }> {
    try {
        const { data: rateLimit } = await octokit.rateLimit.get();
        // We are primarily concerned with the 'core' limit, which is used for most REST API requests.
        const coreLimit = rateLimit.resources.core;
        const remaining = coreLimit.remaining;
        const resetTime = new Date(coreLimit.reset * 1000);

        console.log(`[GitHub Rate Limit] Core requests remaining: ${remaining}/${coreLimit.limit}. Resets at ${resetTime.toLocaleTimeString()}.`);

        if (remaining < threshold) {
            console.warn(`[GitHub Rate Limit] WARNING: Low rate limit detected.`);
            return {
                isLimited: true,
                resetTime: resetTime,
                remaining: remaining
            };
        }

        return { isLimited: false, remaining, resetTime };
    } catch (error) {
        console.error("[GitHub Rate Limit] Could not check rate limit:", error);
        // Fail open: if the rate limit check fails, we assume it's okay to proceed to not halt the worker.
        return { isLimited: false, remaining: -1, resetTime: new Date() };
    }
} 