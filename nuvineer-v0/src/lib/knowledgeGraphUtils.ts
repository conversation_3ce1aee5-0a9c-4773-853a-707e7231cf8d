import { createClient } from '@supabase/supabase-js';
import { getVectorsByIds, queryPinecone } from './pineconeUtils';
import { getRepositoryNamespace } from './pinecone-utils';

// --- Interfaces ---

export interface KnowledgeGraphEdge {
  id: number;
  created_at: string;
  source_decision_id: string;
  repository_slug: string;
  subject: string;
  predicate: string;
  object: string;
}

export interface EnrichedDecision {
  id: string;
  title: string;
  summary: string;
  rationale?: string;
  implications?: string;
  dev_prompt?: string;
  related_files?: string[];
  relevanceScore: number; // Add relevance scoring
  relationshipContext: string[]; // Track how this decision relates to the query
}

export interface DecisionCluster {
  coreDecisions: EnrichedDecision[];
  supportingDecisions: EnrichedDecision[];
  constraintDecisions: EnrichedDecision[];
  impactedDecisions: EnrichedDecision[];
  patternDecisions: EnrichedDecision[];
}

export interface GraphContext {
  clusters: DecisionCluster;
  edges: KnowledgeGraphEdge[];
  seedDecisionIds: string[];
  analysisNarrative: string; // AI-generated narrative explaining the relationships
}

// --- Predicate Categories for Intelligent Traversal ---

const PREDICATE_WEIGHTS: { [key: string]: number } = {
  // Core architectural relationships (highest priority)
  'IMPLEMENTS': 1.0,
  'EXTENDS': 0.9,
  'USES': 0.8,
  'REPLACES': 0.9,
  'MODIFIES': 0.8,

  // Supporting relationships
  'ENABLES': 0.7,
  'SUPPORTS': 0.7,
  'PROVIDES': 0.6,
  'ENHANCES': 0.6,
  'IMPROVES': 0.6,

  // Constraint relationships
  'REQUIRES': 0.9,
  'DEPENDS_ON': 0.9,
  'ENFORCES': 0.7,
  'PREVENTS': 0.7,
  'ADDRESSES': 0.7,

  // Impact relationships
  'IMPACTS': 0.8,
  'AFFECTS': 0.7,
  'CREATES': 0.6,
  'INTRODUCES': 0.6,

  // Pattern relationships
  'FOLLOWS': 0.7,
  'IS_A': 0.6,
  'IS_PART_OF': 0.6,
  'ESTABLISHES': 0.6,

  // Default for unknown predicates
  'DEFAULT': 0.3
};

const DECISION_POINT_CATEGORIES: { [key: string]: string[] } = {
  'architecture': ['IMPLEMENTS', 'EXTENDS', 'USES', 'REPLACES', 'MODIFIES', 'IS_A', 'FOLLOWS'],
  'technology': ['USES', 'REPLACES', 'IMPLEMENTS', 'REQUIRES', 'SUPPORTS'],
  'data': ['MODIFIES', 'USES', 'CREATES', 'PROCESSES', 'STORES'],
  'integration': ['ENABLES', 'CONNECTS', 'INTERFACES', 'SUPPORTS', 'USES'],
  'security': ['PREVENTS', 'PROTECTS', 'ENFORCES', 'ADDRESSES', 'ENCRYPTS'],
  'performance': ['IMPROVES', 'ENHANCES', 'REDUCES', 'OPTIMIZES', 'ENABLES']
};

// --- Supabase Client Initialization ---

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.warn("Supabase credentials are not fully set up. Knowledge Graph service will be limited.");
}

const supabaseAdmin = createClient(supabaseUrl!, supabaseServiceRoleKey!, {
  auth: { persistSession: false },
});

/**
 * Intelligently traverses the knowledge graph using semantic relationships
 * to find the most relevant architectural context for technical decision points.
 */
export async function getGraphContextForDecisions(
  seedDecisionIds: string[],
  repositorySlug: string,
  installationId: number,
  isPublic: boolean,
  decisionPointCategory?: string
): Promise<GraphContext> {
  const logPrefix = `[IntelligentKnowledgeGraph] [${repositorySlug}]`;

  if (seedDecisionIds.length === 0) {
    console.log(`${logPrefix} No seed decisions provided. Returning empty graph context.`);
    return { 
      clusters: { coreDecisions: [], supportingDecisions: [], constraintDecisions: [], impactedDecisions: [], patternDecisions: [] },
      edges: [], 
      seedDecisionIds: [],
      analysisNarrative: "No architectural decisions found for this context."
    };
  }
  
  console.log(`${logPrefix} Starting intelligent graph traversal for ${seedDecisionIds.length} seed decision(s) with category: ${decisionPointCategory}`);

  // Step 1: Multi-hop traversal with predicate-aware scoring
  const allRelevantEdges = await performIntelligentTraversal(seedDecisionIds, repositorySlug, decisionPointCategory);
  console.log(`${logPrefix} Found ${allRelevantEdges.length} semantically relevant edges`);

  // Step 2: Collect and score all unique decision IDs
  const decisionScores = calculateDecisionRelevanceScores(seedDecisionIds, allRelevantEdges, decisionPointCategory);
  const allDecisionIds = Object.keys(decisionScores);
  console.log(`${logPrefix} Scoring ${allDecisionIds.length} unique decisions`);

  // Step 3: Batch fetch full decision metadata from Pinecone
  const namespace = getRepositoryNamespace(installationId, repositorySlug);
  const pineconeResults = await getVectorsByIds(allDecisionIds, namespace);

  if (!pineconeResults || pineconeResults.length === 0) {
    console.warn(`${logPrefix} Could not retrieve metadata for any decisions from Pinecone.`);
    return { 
      clusters: { coreDecisions: [], supportingDecisions: [], constraintDecisions: [], impactedDecisions: [], patternDecisions: [] },
      edges: [], 
      seedDecisionIds,
      analysisNarrative: "Unable to retrieve architectural decision metadata."
    };
  }

  // Step 4: Enrich decisions with relationship context
  const enrichedDecisions = enrichDecisionsWithContext(pineconeResults, decisionScores, allRelevantEdges);

  // Step 5: Cluster decisions by their architectural role
  const clusters = clusterDecisionsByRole(enrichedDecisions, allRelevantEdges, seedDecisionIds);

  // Step 6: Generate AI narrative explaining the relationships
  const analysisNarrative = await generateArchitecturalNarrative(clusters, allRelevantEdges, decisionPointCategory);

  console.log(`${logPrefix} Successfully processed ${enrichedDecisions.length} enriched decisions into clusters`);

  return {
    clusters,
    edges: allRelevantEdges,
    seedDecisionIds,
    analysisNarrative
  };
}

/**
 * Performs intelligent multi-hop graph traversal prioritizing semantically relevant relationships
 */
async function performIntelligentTraversal(
  seedDecisionIds: string[], 
  repositorySlug: string, 
  category?: string
): Promise<KnowledgeGraphEdge[]> {
  const relevantPredicates = category && DECISION_POINT_CATEGORIES[category] 
    ? DECISION_POINT_CATEGORIES[category] 
    : Object.keys(PREDICATE_WEIGHTS);
  const visitedDecisions = new Set<string>();
  const allEdges: KnowledgeGraphEdge[] = [];
  let currentHopDecisions = new Set(seedDecisionIds);

  // Perform up to 3 hops of intelligent traversal
  for (let hop = 0; hop < 3 && currentHopDecisions.size > 0; hop++) {
    console.log(`[IntelligentTraversal] Hop ${hop + 1}: Exploring ${currentHopDecisions.size} decisions`);
    
    const currentIds = Array.from(currentHopDecisions);
    const { data: edges, error } = await supabaseAdmin
      .from('knowledge_graph_edges')
      .select('*')
      .eq('repository_slug', repositorySlug)
      .or(`source_decision_id.in.(${currentIds.join(',')}),object.in.(${currentIds.join(',')})`)
      .in('predicate', relevantPredicates);

    if (error) {
      console.error(`[IntelligentTraversal] Error in hop ${hop + 1}:`, error);
      break;
    }

    if (!edges || edges.length === 0) {
      console.log(`[IntelligentTraversal] No more relevant edges found at hop ${hop + 1}`);
      break;
    }

    // Filter edges by relevance and add to collection
    const relevantEdges = edges.filter(edge => 
      (PREDICATE_WEIGHTS[edge.predicate] || PREDICATE_WEIGHTS.DEFAULT) >= 0.5
    );

    allEdges.push(...relevantEdges);
    
    // Prepare next hop decisions (avoid revisiting)
    const nextHopDecisions = new Set<string>();
    relevantEdges.forEach(edge => {
      visitedDecisions.add(edge.source_decision_id);
      if (isDecisionId(edge.object) && !visitedDecisions.has(edge.object)) {
        nextHopDecisions.add(edge.object);
      }
    });

    currentHopDecisions = nextHopDecisions;
    console.log(`[IntelligentTraversal] Hop ${hop + 1}: Found ${relevantEdges.length} relevant edges, ${nextHopDecisions.size} new decisions for next hop`);
  }

  return allEdges;
}

/**
 * Calculates relevance scores for decisions based on their relationships and distance from seeds
 */
function calculateDecisionRelevanceScores(
  seedDecisionIds: string[], 
  edges: KnowledgeGraphEdge[], 
  category?: string
): { [decisionId: string]: number } {
  const scores: { [decisionId: string]: number } = {};
  
  // Initialize seed decisions with maximum score
  seedDecisionIds.forEach(id => {
    scores[id] = 1.0;
  });

  // Score decisions based on their relationships
  edges.forEach(edge => {
    const predicateWeight = PREDICATE_WEIGHTS[edge.predicate] || PREDICATE_WEIGHTS.DEFAULT;
    const categoryBonus = category && DECISION_POINT_CATEGORIES[category]?.includes(edge.predicate) ? 0.2 : 0;
    const relationshipScore = predicateWeight + categoryBonus;

    // Score the target decision based on relationship strength
    if (isDecisionId(edge.object)) {
      const currentScore = scores[edge.object] || 0;
      const sourceScore = scores[edge.source_decision_id] || 0.5;
      scores[edge.object] = Math.max(currentScore, sourceScore * relationshipScore * 0.8); // Decay with distance
    }

    // Ensure source decision is scored
    if (!scores[edge.source_decision_id]) {
      scores[edge.source_decision_id] = 0.5;
    }
  });

  return scores;
}

/**
 * Enriches decisions with relationship context and relevance scores
 */
function enrichDecisionsWithContext(
  pineconeResults: any[], 
  decisionScores: { [id: string]: number }, 
  edges: KnowledgeGraphEdge[]
): EnrichedDecision[] {
  return pineconeResults
    .filter(record => record && decisionScores[record.id])
    .map(record => {
      const relationshipContext = edges
        .filter(edge => edge.source_decision_id === record.id || edge.object === record.id)
        .map(edge => `${edge.predicate}: ${edge.subject} → ${edge.object}`)
        .slice(0, 5); // Limit context size

      return {
        id: record.id,
        title: record.metadata?.title || 'Untitled Decision',
        summary: record.metadata?.description || record.metadata?.rationale || 'No summary available',
        rationale: record.metadata?.rationale,
        implications: record.metadata?.implications,
        dev_prompt: record.metadata?.dev_prompt,
        related_files: record.metadata?.related_files,
        relevanceScore: decisionScores[record.id],
        relationshipContext
      };
    })
    .sort((a, b) => b.relevanceScore - a.relevanceScore);
}

/**
 * Clusters decisions by their architectural role based on relationship patterns
 */
function clusterDecisionsByRole(
  decisions: EnrichedDecision[], 
  edges: KnowledgeGraphEdge[], 
  seedIds: string[]
): DecisionCluster {
  const clusters: DecisionCluster = {
    coreDecisions: [],
    supportingDecisions: [],
    constraintDecisions: [],
    impactedDecisions: [],
    patternDecisions: []
  };

  decisions.forEach(decision => {
    const relatedEdges = edges.filter(edge => 
      edge.source_decision_id === decision.id || edge.object === decision.id
    );

    const predicates = relatedEdges.map(edge => edge.predicate);
    const isSeed = seedIds.includes(decision.id);

    if (isSeed || decision.relevanceScore >= 0.8) {
      clusters.coreDecisions.push(decision);
    } else if (predicates.some(p => ['REQUIRES', 'DEPENDS_ON', 'ENFORCES', 'PREVENTS'].includes(p))) {
      clusters.constraintDecisions.push(decision);
    } else if (predicates.some(p => ['IMPACTS', 'AFFECTS', 'CREATES', 'INTRODUCES'].includes(p))) {
      clusters.impactedDecisions.push(decision);
    } else if (predicates.some(p => ['IS_A', 'FOLLOWS', 'ESTABLISHES', 'IS_PART_OF'].includes(p))) {
      clusters.patternDecisions.push(decision);
    } else {
      clusters.supportingDecisions.push(decision);
    }
  });

  // Limit cluster sizes for performance
  const clusterKeys: (keyof DecisionCluster)[] = ['coreDecisions', 'supportingDecisions', 'constraintDecisions', 'impactedDecisions', 'patternDecisions'];
  clusterKeys.forEach(key => {
    clusters[key] = clusters[key].slice(0, 10);
  });

  return clusters;
}

/**
 * Generates an AI narrative explaining the architectural relationships
 */
async function generateArchitecturalNarrative(
  clusters: DecisionCluster, 
  edges: KnowledgeGraphEdge[], 
  category?: string
): Promise<string> {
  const totalDecisions = Object.values(clusters).flat().length;
  
  if (totalDecisions === 0) {
    return "No architectural context found for this technical decision point.";
  }

  // Build a concise narrative based on cluster analysis
  const narrativeParts: string[] = [];
  
  if (clusters.coreDecisions.length > 0) {
    narrativeParts.push(`Found ${clusters.coreDecisions.length} core architectural decision(s) directly relevant to this ${category || 'technical'} decision point.`);
  }

  if (clusters.constraintDecisions.length > 0) {
    const constraintTypes = edges
      .filter(edge => ['REQUIRES', 'DEPENDS_ON', 'ENFORCES', 'PREVENTS'].includes(edge.predicate))
      .map(edge => edge.predicate.toLowerCase())
      .slice(0, 3);
    narrativeParts.push(`This decision ${constraintTypes.join(', ')} ${clusters.constraintDecisions.length} architectural constraint(s).`);
  }

  if (clusters.impactedDecisions.length > 0) {
    narrativeParts.push(`Implementation will impact ${clusters.impactedDecisions.length} related system(s) or component(s).`);
  }

  if (clusters.patternDecisions.length > 0) {
    narrativeParts.push(`Aligns with ${clusters.patternDecisions.length} established architectural pattern(s) in the codebase.`);
  }

  return narrativeParts.join(' ') || "Architectural context analysis completed.";
}

/**
 * Heuristic to determine if a string represents a decision ID
 */
function isDecisionId(value: string): boolean {
  return typeof value === 'string' && 
         (value.includes('decision_') || 
          (value.includes('-') && !value.includes(' ') && value.length > 10));
}

/**
 * Intelligently selects seed decisions by analyzing graph structure and architectural significance
 * This replaces the basic semantic similarity approach with graph-aware seed selection
 */
export async function getIntelligentSeedDecisions(
  searchQueries: string[],
  repositorySlug: string,
  installationId: number,
  isPublic: boolean,
  decisionPointCategory?: string,
  maxSeeds: number = 8
): Promise<string[]> {
  const logPrefix = `[IntelligentSeedSelection] [${repositorySlug}]`;
  const namespace = getRepositoryNamespace(installationId, repositorySlug);
  
  console.log(`${logPrefix} Starting intelligent seed selection for category: ${decisionPointCategory}`);

  // Step 1: Get initial candidate decisions from semantic search
  const candidateDecisions = new Map<string, { score: number, metadata: any }>();
  
  for (const query of searchQueries.slice(0, 4)) { // More queries for better coverage
    try {
      const searchResults = await queryPinecone(query, namespace, 12, 0.3); // Lower threshold, more results
      searchResults.forEach((result: any) => {
        if (!candidateDecisions.has(result.id) || candidateDecisions.get(result.id)!.score < result.score) {
          candidateDecisions.set(result.id, { score: result.score, metadata: result.metadata });
        }
      });
    } catch (error) {
      console.warn(`${logPrefix} Search failed for query "${query}":`, error);
    }
  }

  const candidateIds = Array.from(candidateDecisions.keys());
  console.log(`${logPrefix} Found ${candidateIds.length} candidate decisions from semantic search`);

  if (candidateIds.length === 0) {
    return [];
  }

  // Step 2: Analyze graph structure to identify architectural significance
  const { data: allEdges, error } = await supabaseAdmin
    .from('knowledge_graph_edges')
    .select('*')
    .eq('repository_slug', repositorySlug)
    .or(`source_decision_id.in.(${candidateIds.join(',')}),object.in.(${candidateIds.join(',')})`);

  if (error) {
    console.error(`${logPrefix} Error fetching graph edges:`, error);
    // Fallback to basic semantic similarity
    return candidateIds
      .sort((a, b) => candidateDecisions.get(b)!.score - candidateDecisions.get(a)!.score)
      .slice(0, maxSeeds);
  }

  // Step 3: Calculate architectural significance scores
  const architecturalScores = calculateArchitecturalSignificance(
    candidateIds, 
    allEdges || [], 
    candidateDecisions, 
    decisionPointCategory
  );

  // Step 4: Select seeds using multi-criteria optimization
  const selectedSeeds = selectOptimalSeeds(
    candidateIds,
    architecturalScores,
    candidateDecisions,
    allEdges || [],
    maxSeeds
  );

  console.log(`${logPrefix} Selected ${selectedSeeds.length} intelligent seed decisions`);
  return selectedSeeds;
}

/**
 * Calculates architectural significance based on graph topology and relationship patterns
 */
function calculateArchitecturalSignificance(
  candidateIds: string[],
  edges: KnowledgeGraphEdge[],
  candidateDecisions: Map<string, { score: number, metadata: any }>,
  category?: string
): Map<string, number> {
  const scores = new Map<string, number>();
  
  // Initialize with semantic similarity scores
  candidateIds.forEach(id => {
    scores.set(id, candidateDecisions.get(id)!.score * 0.3); // Base semantic score (30% weight)
  });

  // Calculate graph-based metrics
  const inDegree = new Map<string, number>();
  const outDegree = new Map<string, number>();
  const predicateWeights = new Map<string, number>();
  const foundationalConnections = new Map<string, number>();

  // Analyze edge patterns
  edges.forEach(edge => {
    const sourceId = edge.source_decision_id;
    const targetId = edge.object;
    const predicate = edge.predicate;
    
    if (!isDecisionId(targetId)) return;

    // Count degrees
    outDegree.set(sourceId, (outDegree.get(sourceId) || 0) + 1);
    inDegree.set(targetId, (inDegree.get(targetId) || 0) + 1);

    // Weight by predicate importance
    const predicateWeight = PREDICATE_WEIGHTS[predicate] || PREDICATE_WEIGHTS.DEFAULT;
    predicateWeights.set(sourceId, (predicateWeights.get(sourceId) || 0) + predicateWeight);
    predicateWeights.set(targetId, (predicateWeights.get(targetId) || 0) + predicateWeight * 0.7);

    // Identify foundational patterns (decisions that others DEPEND_ON, REQUIRES, etc.)
    if (['DEPENDS_ON', 'REQUIRES', 'EXTENDS', 'IMPLEMENTS'].includes(predicate)) {
      foundationalConnections.set(targetId, (foundationalConnections.get(targetId) || 0) + 1.5);
    }
    
    // Category-specific boosting
    if (category && DECISION_POINT_CATEGORIES[category]?.includes(predicate)) {
      const categoryBoost = 0.3;
      scores.set(sourceId, (scores.get(sourceId) || 0) + categoryBoost);
      scores.set(targetId, (scores.get(targetId) || 0) + categoryBoost * 0.8);
    }
  });

  // Apply graph topology scoring
  candidateIds.forEach(id => {
    let currentScore = scores.get(id) || 0;
    
    // Hub score (high out-degree indicates influential decisions)
    const hubScore = Math.min((outDegree.get(id) || 0) * 0.1, 0.4);
    
    // Authority score (high in-degree indicates foundational decisions)
    const authorityScore = Math.min((inDegree.get(id) || 0) * 0.08, 0.3);
    
    // Predicate importance score
    const predicateScore = Math.min((predicateWeights.get(id) || 0) * 0.1, 0.5);
    
    // Foundational bonus (decisions others depend on)
    const foundationalScore = Math.min((foundationalConnections.get(id) || 0) * 0.15, 0.4);
    
    // Metadata quality bonus
    const metadata = candidateDecisions.get(id)?.metadata;
    let qualityBonus = 0;
    if (metadata?.implications) qualityBonus += 0.1;
    if (metadata?.dev_prompt) qualityBonus += 0.1;
    if (metadata?.related_files?.length > 0) qualityBonus += 0.1;
    
    const finalScore = currentScore + hubScore + authorityScore + predicateScore + foundationalScore + qualityBonus;
    scores.set(id, finalScore);
    
    console.log(`[ArchSignificance] ${id}: semantic=${currentScore.toFixed(2)}, hub=${hubScore.toFixed(2)}, authority=${authorityScore.toFixed(2)}, predicate=${predicateScore.toFixed(2)}, foundational=${foundationalScore.toFixed(2)}, quality=${qualityBonus.toFixed(2)} → ${finalScore.toFixed(2)}`);
  });

  return scores;
}

/**
 * Selects optimal seeds using diversity and coverage optimization
 */
function selectOptimalSeeds(
  candidateIds: string[],
  architecturalScores: Map<string, number>,
  candidateDecisions: Map<string, { score: number, metadata: any }>,
  edges: KnowledgeGraphEdge[],
  maxSeeds: number
): string[] {
  const selected: string[] = [];
  const remaining = [...candidateIds];
  
  // Always start with the highest-scoring decision
  remaining.sort((a, b) => (architecturalScores.get(b) || 0) - (architecturalScores.get(a) || 0));
  selected.push(remaining.shift()!);
  
  // Select remaining seeds to maximize coverage and minimize redundancy
  while (selected.length < maxSeeds && remaining.length > 0) {
    let bestCandidate = remaining[0];
    let bestScore = -1;
    
    for (const candidate of remaining) {
      // Base architectural significance
      let score = architecturalScores.get(candidate) || 0;
      
      // Diversity bonus: prefer decisions that connect to different areas
      const candidateConnections = new Set<string>();
      edges.forEach(edge => {
        if (edge.source_decision_id === candidate && isDecisionId(edge.object)) {
          candidateConnections.add(edge.object);
        }
        if (edge.object === candidate) {
          candidateConnections.add(edge.source_decision_id);
        }
      });
      
      // Check overlap with already selected seeds
      const selectedConnections = new Set<string>();
      selected.forEach(selectedId => {
        edges.forEach(edge => {
          if (edge.source_decision_id === selectedId && isDecisionId(edge.object)) {
            selectedConnections.add(edge.object);
          }
          if (edge.object === selectedId) {
            selectedConnections.add(edge.source_decision_id);
          }
        });
      });
      
      // Calculate diversity bonus
      const overlap = Array.from(candidateConnections).filter(conn => selectedConnections.has(conn)).length;
      const uniqueConnections = candidateConnections.size - overlap;
      const diversityBonus = Math.min(uniqueConnections * 0.05, 0.3);
      
      score += diversityBonus;
      
      if (score > bestScore) {
        bestScore = score;
        bestCandidate = candidate;
      }
    }
    
    selected.push(bestCandidate);
    remaining.splice(remaining.indexOf(bestCandidate), 1);
  }
  
  console.log(`[SeedSelection] Selected seeds with scores: ${selected.map(id => `${id}:${(architecturalScores.get(id) || 0).toFixed(2)}`).join(', ')}`);
  return selected;
} 