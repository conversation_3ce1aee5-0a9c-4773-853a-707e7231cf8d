import { Octokit } from '@octokit/rest';
// Import the centralized auth functions instead of duplicating them
import { getOctokit } from '@/lib/github-auth';

// Interfaces matching those in orchestrator/API routes
interface PrFile {
  filename: string;
  additions: number;
  deletions: number;
  patch: string;
}

export interface PrContext {
  title: string;
  body: string;
  html_url: string;
  number: number;
  merged_at: string | null;
  created_at: string | null;
  user: {
    login: string;
  };
  files: PrFile[];
}

export interface CodeChange {
  filename: string;
  patch: string;
}

export interface FormattedComment {
  user: {
    login: string | null;
  };
  body: string | null;
}

// --- Environment Variables ---
function getRequiredEnvVar(name: string, defaultValue?: string): string {
    const value = process.env[name];
    if (!value && defaultValue === undefined) {
        throw new Error(`Missing required environment variable: ${name}`);
    }
    return value || defaultValue || '';
}

const GITHUB_APP_ID = process.env.GITHUB_APP_ID;
const GITHUB_APP_PRIVATE_KEY = process.env.GITHUB_APP_PRIVATE_KEY;

function validateGitHubConfig() {
  if (!GITHUB_APP_ID || !GITHUB_APP_PRIVATE_KEY) {
    throw new Error('GitHub App ID and Private Key must be set');
  }
}

// Use the centralized getOctokit function instead of duplicating getInstallationOctokit
export async function getInstallationOctokit(installationId: number): Promise<Octokit> {
    validateGitHubConfig();
    return getOctokit(installationId.toString());
}

/**
 * Fetches PR details, files, comments, and diff from GitHub.
 * @param octokit - An authenticated Octokit instance.
 * @param owner - Repository owner.
 * @param repo - Repository name.
 * @param prNumber - Pull request number.
 * @returns {Promise<{formattedData: {prContext: PrContext, formattedComments: FormattedComment[]}, diff: string, codeChanges: CodeChange[], error?: any}>}
 */
export async function fetchAndFormatPRData(
  octokit: Octokit,
  owner: string,
  repo: string,
  prNumber: number
): Promise<{
  formattedData: { // Encapsulate context and comments
    prContext: PrContext,
    formattedComments: FormattedComment[]
  },
  diff: string, // Add diff
  codeChanges: CodeChange[] // Keep codeChanges separate as API route seems to expect
}> {
  console.log(`[GitHub Lib] Fetching data for ${owner}/${repo}#${prNumber}`);

  try {
    // Fetch PR details
    const { data: pr } = await octokit.pulls.get({
      owner,
      repo,
      pull_number: prNumber,
    });

    // ---> ADDED: Check if PR data was fetched <---
    if (!pr) {
      console.error(`[GitHub Lib] Failed to fetch PR details for ${owner}/${repo}#${prNumber}. API returned null.`);
      throw new Error(`Failed to fetch PR details for ${owner}/${repo}#${prNumber}: API returned null data.`);
    }
    // ---> END ADDED CHECK <---

    // Fetch PR files (changes)
    // Using listFiles provides structured file data including patch
    const { data: files } = await octokit.pulls.listFiles({
      owner,
      repo,
      pull_number: prNumber,
      // per_page: 100 // Consider pagination if PRs have > 100 files, though listFiles handles up to 300
    });

    // Fetch PR comments
    const { data: comments } = await octokit.issues.listComments({
      owner,
      repo,
      issue_number: prNumber,
    });

    // --- ADDED: Fetch PR diff --- 
    console.log(`[GitHub Lib] Fetching diff for ${owner}/${repo}#${prNumber}...`);
    const diffResponse = await octokit.pulls.get({
        owner,
        repo,
        pull_number: prNumber,
        mediaType: { format: "diff" },
    });
    // The diff content is directly in diffResponse.data
    const diff = diffResponse.data as unknown as string; 
    if (typeof diff !== 'string') {
        // This case should ideally not happen if the API call succeeds, but good to check.
        console.warn(`[GitHub Lib] Diff fetched for ${owner}/${repo}#${prNumber}, but the type was not string (type: ${typeof diff}).`);
        throw new Error('Failed to fetch PR diff content correctly.');
    }
    console.log(`[GitHub Lib] Diff fetched successfully (${diff.length} characters).`);
    // --- END ADDED --- 

    // Format PR context (using data from pulls.get and pulls.listFiles)
    const prContext: PrContext = {
      title: pr.title ?? 'No Title',
      body: pr.body || '',
      html_url: pr.html_url,
      number: pr.number,
      merged_at: pr.merged_at,
      created_at: pr.created_at,
      user: { login: pr.user?.login ?? 'unknown' },
      // Map file details from listFiles result
      files: files.map(file => ({
        filename: file.filename,
        additions: file.additions,
        deletions: file.deletions,
        patch: file.patch || '', // Use patch from listFiles
      })),
    };

    // Format code changes (already done by listFiles)
    const codeChanges: CodeChange[] = files.map(file => ({
      filename: file.filename,
      patch: file.patch || '',
    }));

    // Format comments
    const formattedComments: FormattedComment[] = comments
      .filter(comment => comment.user?.login !== 'vercel[bot]')
      .map(comment => ({
        user: {
          login: comment.user?.login ?? null,
        },
        body: comment.body ?? null,
      }));

    console.log(`[GitHub Lib] Successfully fetched and formatted PR data for ${owner}/${repo}#${prNumber} with ${files.length} files, ${comments.length} total comments (${formattedComments.length} after filtering Vercel bot), and diff.`);
    
    // --- CORRECTED RETURN STRUCTURE ---
    return {
        formattedData: { // Key expected by API route
            prContext: prContext,
            formattedComments: formattedComments
        },
        diff: diff, // Key expected by API route
        codeChanges: codeChanges // Key expected by API route
    };
    // --- END CORRECTION ---

  } catch (error: any) {
    console.error(`[GitHub Lib] Error fetching or formatting PR data for ${owner}/${repo}#${prNumber}:`, error);
    // Re-throw the error to be handled by the caller
    throw error; 
  }
} 