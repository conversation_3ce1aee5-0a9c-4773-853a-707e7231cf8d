-- Create commit_loading_jobs table for tracking progressive commit loading
CREATE TABLE IF NOT EXISTS commit_loading_jobs (
    id SERIAL PRIMARY KEY,
    repository_slug VARCHAR(255) NOT NULL,
    installation_id INTEGER NOT NULL DEFAULT 0,
    current_page INTEGER NOT NULL DEFAULT 1,
    total_commits_loaded INTEGER NOT NULL DEFAULT 0,
    is_completed BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_processed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    
    -- Ensure only one job per repository/installation combination
    UNIQUE(repository_slug, installation_id)
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_commit_loading_jobs_status_completed 
ON commit_loading_jobs (status, is_completed) 
WHERE status = 'pending' AND is_completed = false;

CREATE INDEX IF NOT EXISTS idx_commit_loading_jobs_repo_installation 
ON commit_loading_jobs (repository_slug, installation_id);

CREATE INDEX IF NOT EXISTS idx_commit_loading_jobs_created_at 
ON commit_loading_jobs (created_at);

-- Add comments for documentation
COMMENT ON TABLE commit_loading_jobs IS 'Tracks progressive commit loading jobs to avoid loading all commits at once';
COMMENT ON COLUMN commit_loading_jobs.repository_slug IS 'Repository identifier in owner/repo format';
COMMENT ON COLUMN commit_loading_jobs.installation_id IS 'GitHub App installation ID, 0 for public repos';
COMMENT ON COLUMN commit_loading_jobs.current_page IS 'Current page of commits being processed (100 commits per page)';
COMMENT ON COLUMN commit_loading_jobs.total_commits_loaded IS 'Total number of commits loaded so far';
COMMENT ON COLUMN commit_loading_jobs.is_completed IS 'Whether all commits have been loaded for this repository';
COMMENT ON COLUMN commit_loading_jobs.status IS 'Current status of the loading job';
COMMENT ON COLUMN commit_loading_jobs.last_processed_at IS 'When the job was last processed by the background worker'; 