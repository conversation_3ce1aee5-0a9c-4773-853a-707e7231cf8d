CREATE TABLE design_doc_sessions (
    -- Use a UUID for a globally unique, non-sequential session identifier.
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Repository identification using the existing pattern in the system
    repository_slug VARCHAR(255) NOT NULL, -- Format: "owner/repo"
    installation_id INTEGER NOT NULL DEFAULT 0, -- GitHub App installation ID, 0 for public repos

    -- Link to the user who created the session.
    user_id UUID REFERENCES auth.users(id),

    -- The title of the design document, for easy identification.
    title TEXT,

    -- The URL of the GitHub issue associated with this design document, if any.
    github_issue_url TEXT,

    -- The complete state of the wizard, including all steps and decisions.
    wizard_state JSONB,

    -- Timestamps for tracking creation and updates.
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_design_doc_sessions_repository 
ON design_doc_sessions (repository_slug, installation_id);

CREATE INDEX IF NOT EXISTS idx_design_doc_sessions_user 
ON design_doc_sessions (user_id);

CREATE INDEX IF NOT EXISTS idx_design_doc_sessions_created_at 
ON design_doc_sessions (created_at DESC);

-- Enable RLS
ALTER TABLE design_doc_sessions ENABLE ROW LEVEL SECURITY;

-- Create policy for users to select their own sessions
CREATE POLICY "Allow users to select their own sessions"
ON design_doc_sessions FOR SELECT
USING (auth.uid() = user_id);

-- Create policy for users to insert their own sessions
CREATE POLICY "Allow users to insert their own sessions"
ON design_doc_sessions FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy for users to update their own sessions
CREATE POLICY "Allow users to update their own sessions"
ON design_doc_sessions FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- A trigger to automatically update the 'updated_at' timestamp.
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_design_doc_sessions_updated_at
BEFORE UPDATE ON design_doc_sessions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE design_doc_sessions IS 'Stores design document wizard sessions with complete state and metadata';
COMMENT ON COLUMN design_doc_sessions.repository_slug IS 'Repository identifier in owner/repo format';
COMMENT ON COLUMN design_doc_sessions.installation_id IS 'GitHub App installation ID, 0 for public repos';
COMMENT ON COLUMN design_doc_sessions.user_id IS 'ID of the user who created this session';
COMMENT ON COLUMN design_doc_sessions.title IS 'Title of the design document for easy identification';
COMMENT ON COLUMN design_doc_sessions.github_issue_url IS 'URL of the associated GitHub issue, if any';
COMMENT ON COLUMN design_doc_sessions.wizard_state IS 'Complete wizard state including all steps and decisions';
