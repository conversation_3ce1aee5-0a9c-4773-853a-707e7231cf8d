-- Create RCA Analysis Sessions table
CREATE TABLE rca_analysis_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repository_slug TEXT NOT NULL,
    installation_id INTEGER,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    wizard_state JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_rca_analysis_sessions_user_id ON rca_analysis_sessions(user_id);
CREATE INDEX idx_rca_analysis_sessions_repository_slug ON rca_analysis_sessions(repository_slug);
CREATE INDEX idx_rca_analysis_sessions_created_at ON rca_analysis_sessions(created_at);

-- Enable RLS (Row Level Security)
ALTER TABLE rca_analysis_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policy to ensure users can only access their own sessions
CREATE POLICY "Users can only access their own RCA sessions" ON rca_analysis_sessions
    FOR ALL USING (auth.uid() = user_id);

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_rca_analysis_sessions_updated_at 
    BEFORE UPDATE ON rca_analysis_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 