CREATE TABLE IF NOT EXISTS deployment_constitutions (
    -- A unique identifier for each saved constitution.
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Repository identification using the established system pattern.
    repository_slug VARCHAR(255) NOT NULL, -- Format: "owner/repo"
    installation_id INTEGER NOT NULL,     -- GitHub App installation ID.

    -- Link to the user who initiated the analysis or approved the constitution.
    user_id UUID REFERENCES auth.users(id),

    -- The structured data representing the deployment environment.
    constitution_data JSONB NOT NULL,

    -- Information about the source of the constitution data.
    -- This is crucial for knowing if the IaC lives in a separate repository.
    source_repo_url TEXT,
    source_commit_hash VARCHAR(40),

    -- Timestamps for tracking creation and updates.
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- A unique constraint to ensure only one active constitution per repository.
    UNIQUE(repository_slug, installation_id)
);

-- Create indexes for efficient querying.
CREATE INDEX IF NOT EXISTS idx_deployment_constitutions_repository
ON deployment_constitutions (repository_slug, installation_id);

CREATE INDEX IF NOT EXISTS idx_deployment_constitutions_user
ON deployment_constitutions (user_id);

-- Enable Row Level Security (RLS) to control access.
ALTER TABLE deployment_constitutions ENABLE ROW LEVEL SECURITY;

-- Policies to allow users to manage their own repository constitutions.
-- This assumes that the user who creates/updates has appropriate permissions.

-- Allow users to view constitutions for repositories they have access to.
-- (This is a placeholder and may need to be adjusted based on your app's permissions model)
CREATE POLICY "Allow users to select repository constitutions"
ON deployment_constitutions FOR SELECT
USING (true); -- A more specific check, like team membership, would go here.

-- Allow users to insert a constitution for a repository.
CREATE POLICY "Allow users to insert repository constitutions"
ON deployment_constitutions FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Allow users to update their own repository's constitution.
CREATE POLICY "Allow users to update their repository constitutions"
ON deployment_constitutions FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Use the existing trigger function to automatically update the 'updated_at' timestamp.
CREATE TRIGGER update_deployment_constitutions_updated_at
BEFORE UPDATE ON deployment_constitutions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation.
COMMENT ON TABLE deployment_constitutions IS 'Stores the structured deployment context for a repository, generated from its infrastructure-as-code files.';
COMMENT ON COLUMN deployment_constitutions.repository_slug IS 'Repository identifier in owner/repo format.';
COMMENT ON COLUMN deployment_constitutions.installation_id IS 'GitHub App installation ID.';
COMMENT ON COLUMN deployment_constitutions.user_id IS 'ID of the user who initiated or approved the constitution.';
COMMENT ON COLUMN deployment_constitutions.constitution_data IS 'The structured (JSONB) data of the deployment environment.';
COMMENT ON COLUMN deployment_constitutions.source_repo_url IS 'The URL of the repository where the IaC source was found.';
COMMENT ON COLUMN deployment_constitutions.source_commit_hash IS 'The commit hash of the IaC source used for generation.';
