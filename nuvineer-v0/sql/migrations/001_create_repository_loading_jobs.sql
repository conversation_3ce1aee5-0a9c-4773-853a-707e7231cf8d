-- Drop the old table if it exists to ensure a clean slate.
DROP TABLE IF EXISTS commit_loading_jobs;

-- Create the new, unified table for tracking repository loading progress for both PRs and commits.
CREATE TABLE IF NOT EXISTS repository_loading_jobs (
    id SERIAL PRIMARY KEY,
    repository_slug VARCHAR(255) NOT NULL,
    installation_id INTEGER NOT NULL,
    
    -- Tracks the current phase of the loading process.
    current_phase VARCHAR(50) NOT NULL DEFAULT 'prs' CHECK (current_phase IN ('prs', 'commits', 'completed')),
    
    -- The next page number to fetch from the GitHub API for the current phase.
    next_page_to_process INTEGER NOT NULL DEFAULT 1,
    
    -- A flag to indicate if the entire loading process (all phases) is complete.
    is_completed BOOLEAN NOT NULL DEFAULT false,
    
    -- The status of the current job run. 'pending' means it's waiting for a worker.
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    
    -- Timestamps for tracking and debugging.
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    last_processed_at TIMESTAMP WITH TIME ZONE,
    
    -- For storing any error messages that occur during processing.
    error_message TEXT,
    
    -- Ensure only one loading job can exist per repository/installation combination.
    UNIQUE(repository_slug, installation_id)
);

-- Add an index for efficiently finding pending jobs.
CREATE INDEX IF NOT EXISTS idx_pending_repository_loading_jobs ON repository_loading_jobs (is_completed, status); 