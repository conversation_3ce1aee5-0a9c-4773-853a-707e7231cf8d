-- Create table to track all commit SHAs that belong to pull requests
-- This ensures we don't duplicate processing of commits that are already part of PRs
CREATE TABLE IF NOT EXISTS repository_pr_commit_shas (
    id SERIAL PRIMARY KEY,
    repository_slug VARCHAR(255) NOT NULL,
    installation_id INTEGER NOT NULL,
    commit_sha VARCHAR(40) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure no duplicate commit SHAs per repository/installation
    UNIQUE(repository_slug, installation_id, commit_sha)
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_repository_pr_commit_shas_lookup 
ON repository_pr_commit_shas (repository_slug, installation_id);

CREATE INDEX IF NOT EXISTS idx_repository_pr_commit_shas_commit_sha 
ON repository_pr_commit_shas (commit_sha);

-- Add comments for documentation
COMMENT ON TABLE repository_pr_commit_shas IS 'Tracks all commit SHAs that belong to pull requests to prevent duplicate processing during commit loading';
COMMENT ON COLUMN repository_pr_commit_shas.repository_slug IS 'Repository identifier in owner/repo format';
COMMENT ON COLUMN repository_pr_commit_shas.installation_id IS 'GitHub App installation ID, 0 for public repos';
COMMENT ON COLUMN repository_pr_commit_shas.commit_sha IS 'SHA of a commit that belongs to a pull request';
COMMENT ON COLUMN repository_pr_commit_shas.created_at IS 'When this commit SHA was first recorded as belonging to a PR'; 