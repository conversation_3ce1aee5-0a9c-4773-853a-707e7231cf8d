-- <PERSON>reate functions to efficiently get unique repository+installation_id combinations
-- This ensures we don't miss any repositories when scanning for relationship analysis needs

-- Function to get all unique repositories from repository_pr_analysis_status table
CREATE OR REPLACE FUNCTION get_unique_repositories()
RETURNS TABLE (
    repository_slug VARCHAR(255),
    installation_id INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        r.repository_slug::VARCHAR(255),
        (COALESCE(r.installation_id, 0))::INTEGER
    FROM repository_pr_analysis_status r
    ORDER BY r.repository_slug::VARCHAR(255), (COALESCE(r.installation_id, 0))::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- Function to get unique repositories that have processed PRs (any final state)
CREATE OR REPLACE FUNCTION get_unique_processed_repositories()
RETURNS TABLE (
    repository_slug VARCHAR(255),
    installation_id INTEGER,
    last_analyzed_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        r.repository_slug::VARCHAR(255),
        (COALESCE(r.installation_id, 0))::INTEGER,
        MAX(r.last_analyzed_at)
    FROM repository_pr_analysis_status r
    WHERE r.status IN ('completed', 'no_decisions', 'skipped_not_significant', 'failed')
    GROUP BY r.repository_slug, COALESCE(r.installation_id, 0)
    ORDER BY MAX(r.last_analyzed_at) ASC NULLS LAST;
END;
$$ LANGUAGE plpgsql;

-- Add helpful comments
COMMENT ON FUNCTION get_unique_repositories() IS 'Returns all unique repository+installation_id combinations from repository_pr_analysis_status table';
COMMENT ON FUNCTION get_unique_processed_repositories() IS 'Returns unique repository+installation_id combinations that have processed PRs (completed, no_decisions, skipped_not_significant, or failed)';

-- Create indexes to support these functions efficiently
CREATE INDEX IF NOT EXISTS idx_repository_pr_analysis_status_repo_install 
ON repository_pr_analysis_status (repository_slug, installation_id);

CREATE INDEX IF NOT EXISTS idx_repository_pr_analysis_status_status_last_analyzed 
ON repository_pr_analysis_status (status, last_analyzed_at) 
WHERE status IN ('completed', 'no_decisions', 'skipped_not_significant', 'failed');

-- Grant execute permissions to the application user if needed
-- GRANT EXECUTE ON FUNCTION get_unique_repositories() TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_unique_processed_repositories() TO your_app_user; 